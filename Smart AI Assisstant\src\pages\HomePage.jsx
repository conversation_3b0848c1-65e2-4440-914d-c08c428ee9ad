import React, { useState } from 'react';
import {
  Container,
  Grid,
  Box,
  Typography,
  Card,
  CardContent,
  Tabs,
  Tab,
  Alert,
  <PERSON>nackbar,
  Fade,
  Button,
  Chip,
} from '@mui/material';
import {
  Security as SecurityIcon,
  Link as LinkIcon,
  InsertDriveFile as FileIcon,
  Assessment as AssessmentIcon,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { useScan } from '../contexts/ScanContext';
import LinkScanner from '../components/LinkScanner';
import FileUploader from '../components/FileUploader';
import ResultView from '../components/ResultView';
import SmartDashboard from '../components/SmartDashboard';
import ThreatPrediction from '../components/ThreatPrediction';
import SmartNotifications from '../components/SmartNotifications';

const HomePage = React.memo(() => {
  const [activeTab, setActiveTab] = useState(0);
  const [notification, setNotification] = useState(null);
  const navigate = useNavigate();

  const {
    currentScan,
    isScanning,
    threatLevel,
    notifications,
    removeNotification
  } = useScan();

  // Function to scroll to scanner section and set active tab
  const handleStartScan = () => {
    setActiveTab(0);
    // Smooth scroll to scanner section
    setTimeout(() => {
      const scannerElement = document.getElementById('scanner-section');
      if (scannerElement) {
        scannerElement.scrollIntoView({ behavior: 'smooth', block: 'start' });
      }
    }, 100);
  };

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  const handleCloseNotification = (notificationId) => {
    removeNotification(notificationId);
  };

  const handleDownloadReport = (scanResult) => {
    // This would integrate with a PDF generation service
    console.log('Downloading report for:', scanResult);
    setNotification({
      type: 'info',
      message: 'Report download feature will be available soon.',
    });
  };

  const handleShareResult = (scanResult) => {
    // This would integrate with sharing functionality
    console.log('Sharing result:', scanResult);
    setNotification({
      type: 'info',
      message: 'Result sharing feature will be available soon.',
    });
  };

  return (
    <Box
      className="cyber-defense-bg-enhanced"
      sx={{
        width: '100%',
        minHeight: '100vh',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center',
        py: 4,
        position: 'relative',
        // Optimized text contrast overlay
        '&::before': {
          content: '""',
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: 'radial-gradient(ellipse at center, transparent 0%, rgba(15, 15, 15, 0.2) 60%, rgba(15, 15, 15, 0.4) 100%)',
          zIndex: 1,
          pointerEvents: 'none',
          willChange: 'transform',
        },
        '@keyframes particleFloat': {
          '0%': { transform: 'translateY(0px) translateX(0px) rotate(0deg)' },
          '25%': { transform: 'translateY(-15px) translateX(10px) rotate(90deg)' },
          '50%': { transform: 'translateY(0px) translateX(-5px) rotate(180deg)' },
          '75%': { transform: 'translateY(10px) translateX(-10px) rotate(270deg)' },
          '100%': { transform: 'translateY(0px) translateX(0px) rotate(360deg)' },
        },
      }}
    >
      <Container maxWidth="xl" sx={{ position: 'relative', zIndex: 2 }}>
        {/* Professional Hero Section - Quantum Shield */}
        <Box
          textAlign="center"
          mb={8}
          sx={{
            position: 'relative',
            py: 8,
            px: 4,
            borderRadius: 4,
            overflow: 'hidden',
            '&::before': {
              content: '""',
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              background: 'linear-gradient(45deg, rgba(102, 126, 234, 0.08) 0%, rgba(118, 75, 162, 0.08) 25%, rgba(240, 147, 251, 0.08) 50%, rgba(79, 172, 254, 0.08) 75%, rgba(102, 126, 234, 0.08) 100%)',
              backgroundSize: '400% 400%',
              animation: 'cyberWaves 15s ease infinite',
              zIndex: -2,
            },
            '&::after': {
              content: '""',
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              background: (theme) => theme.palette.mode === 'dark'
                ? 'linear-gradient(135deg, rgba(12, 12, 12, 0.4) 0%, rgba(26, 26, 26, 0.4) 50%, rgba(45, 45, 45, 0.4) 100%)'
                : 'linear-gradient(135deg, rgba(248, 250, 252, 0.6) 0%, rgba(241, 245, 249, 0.6) 50%, rgba(226, 232, 240, 0.6) 100%)',
              backdropFilter: 'blur(20px) saturate(180%)',
              zIndex: -1,
            },
            '@keyframes cyberWaves': {
              '0%': { backgroundPosition: '0% 50%' },
              '50%': { backgroundPosition: '100% 50%' },
              '100%': { backgroundPosition: '0% 50%' },
            },
            '@keyframes neuralPulse': {
              '0%': { backgroundPosition: '0% 50%' },
              '50%': { backgroundPosition: '100% 50%' },
              '100%': { backgroundPosition: '0% 50%' },
            },
            '@keyframes quantumGlow': {
              '0%': { transform: 'scale(1)', opacity: 0.3 },
              '50%': { transform: 'scale(1.05)', opacity: 0.1 },
              '100%': { transform: 'scale(1)', opacity: 0.3 },
            },
          }}
        >
          {/* Quantum Shield Icon */}
          <Box
            sx={{
              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
              borderRadius: '50%',
              width: 140,
              height: 140,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              mx: 'auto',
              mb: 4,
              boxShadow: '0 20px 60px rgba(102, 126, 234, 0.4)',
              position: 'relative',
              '&::before': {
                content: '""',
                position: 'absolute',
                top: -8,
                left: -8,
                right: -8,
                bottom: -8,
                borderRadius: '50%',
                background: 'linear-gradient(45deg, #667eea, #764ba2, #f093fb, #f5576c)',
                backgroundSize: '400% 400%',
                animation: 'neuralPulse 3s ease infinite',
                opacity: 0.7,
                zIndex: -1,
              },
              '&::after': {
                content: '""',
                position: 'absolute',
                top: -15,
                left: -15,
                right: -15,
                bottom: -15,
                borderRadius: '50%',
                border: '3px solid rgba(102, 126, 234, 0.3)',
                animation: 'quantumGlow 2s infinite',
              },
            }}
          >
            <SecurityIcon sx={{ fontSize: 70, color: 'white' }} />
          </Box>

          {/* Neural Pulse Title */}
          <Typography
            variant="h1"
            component="h1"
            gutterBottom
            fontWeight="bold"
            sx={{
              background: 'linear-gradient(45deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%)',
              backgroundSize: '400% 400%',
              backgroundClip: 'text',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              animation: 'neuralPulse 8s ease infinite',
              mb: 3,
              fontSize: { xs: '2.5rem', md: '4rem' },
              textShadow: '0 4px 20px rgba(102, 126, 234, 0.8), 0 2px 10px rgba(118, 75, 162, 0.6), 0 1px 5px rgba(240, 147, 251, 0.4)',
              filter: 'drop-shadow(0 6px 12px rgba(102, 126, 234, 0.7)) drop-shadow(0 3px 6px rgba(255, 255, 255, 0.3))',
              position: 'relative',
              '&::before': {
                content: '"AI Security Guard"',
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                background: 'linear-gradient(45deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.9) 25%, rgba(255, 255, 255, 0.95) 50%, rgba(255, 255, 255, 0.9) 75%, rgba(255, 255, 255, 0.95) 100%)',
                backgroundSize: '400% 400%',
                backgroundClip: 'text',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                animation: 'neuralPulse 8s ease infinite',
                zIndex: -1,
                filter: 'blur(0.5px)',
                opacity: 0.7,
              },
              '&::after': {
                content: '"AI Security Guard"',
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                background: 'linear-gradient(45deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%)',
                backgroundSize: '400% 400%',
                backgroundClip: 'text',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                animation: 'neuralPulse 8s ease infinite',
                zIndex: 1,
                opacity: 0.9,
              },
            }}
          >
            AI Security Guard
          </Typography>

          <Typography
            variant="h4"
            color="text.secondary"
            maxWidth="900px"
            mx="auto"
            sx={{
              mb: 6,
              lineHeight: 1.6,
              fontWeight: 400,
              fontSize: { xs: '1.2rem', md: '1.5rem' },
              textShadow: '0 4px 12px rgba(0, 0, 0, 0.8), 0 2px 6px rgba(0, 0, 0, 0.6)',
              color: 'rgba(255, 255, 255, 0.95)',
            }}
          >
            Next-generation cybersecurity powered by advanced artificial intelligence.
            Protect your digital assets with real-time threat detection and predictive analytics.
          </Typography>
          {/* Professional Feature Chips */}
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'center',
              gap: 3,
              flexWrap: 'wrap',
              mb: 6,
            }}
          >
            {[
              { label: 'Real-time Analytics', gradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' },
              { label: 'AI Threat Prediction', gradient: 'linear-gradient(135deg, #9c27b0 0%, #e91e63 100%)' },
              { label: 'Automated Response', gradient: 'linear-gradient(135deg, #4caf50 0%, #8bc34a 100%)' },
              { label: 'Smart Notifications', gradient: 'linear-gradient(135deg, #2196f3 0%, #9c27b0 100%)' },
            ].map((feature, index) => (
              <Chip
                key={index}
                label={feature.label}
                variant="outlined"
                sx={{
                  borderColor: 'transparent',
                  background: feature.gradient,
                  color: 'white',
                  fontWeight: 600,
                  fontSize: '1rem',
                  px: 3,
                  py: 2,
                  height: 'auto',
                  borderRadius: 3,
                  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                  boxShadow: '0 8px 25px rgba(0,0,0,0.4), 0 4px 12px rgba(102, 126, 234, 0.2)',
                  backdropFilter: 'blur(10px)',
                  border: '1px solid rgba(255, 255, 255, 0.1)',
                  textShadow: '0 2px 8px rgba(0, 0, 0, 0.8)',
                  '&:hover': {
                    transform: 'translateY(-6px) scale(1.08)',
                    boxShadow: '0 16px 40px rgba(0,0,0,0.5), 0 8px 20px rgba(102, 126, 234, 0.4)',
                    filter: 'brightness(1.1)',
                  },
                }}
              />
            ))}
          </Box>

          {/* CTA Buttons */}
          <Box display="flex" justifyContent="center" gap={3} flexWrap="wrap">
            <Button
              variant="contained"
              size="large"
              onClick={handleStartScan} // Navigate to URL Security Scanner tab
              sx={{
                px: 6,
                py: 2,
                borderRadius: 3,
                fontSize: '1.1rem',
                fontWeight: 600,
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                boxShadow: '0 12px 35px rgba(102, 126, 234, 0.5), 0 6px 15px rgba(0, 0, 0, 0.4)',
                backdropFilter: 'blur(10px)',
                border: '1px solid rgba(255, 255, 255, 0.1)',
                textShadow: '0 2px 8px rgba(0, 0, 0, 0.6)',
                '&:hover': {
                  background: 'linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)',
                  transform: 'translateY(-4px) scale(1.05)',
                  boxShadow: '0 20px 50px rgba(102, 126, 234, 0.6), 0 10px 25px rgba(0, 0, 0, 0.5)',
                  filter: 'brightness(1.1)',
                },
              }}
            >
              Start Security Scan
            </Button>
            <Button
              variant="outlined"
              size="large"
              onClick={() => navigate('/smart-features')}
              sx={{
                px: 6,
                py: 2,
                borderRadius: 3,
                fontSize: '1.1rem',
                fontWeight: 600,
                borderColor: 'rgba(255, 255, 255, 0.6)',
                color: 'white',
                borderWidth: 2,
                backdropFilter: 'blur(10px)',
                background: 'rgba(255, 255, 255, 0.05)',
                textShadow: '0 2px 8px rgba(0, 0, 0, 0.6)',
                '&:hover': {
                  borderWidth: 2,
                  borderColor: '#667eea',
                  background: 'linear-gradient(135deg, rgba(102, 126, 234, 0.2) 0%, rgba(118, 75, 162, 0.2) 100%)',
                  transform: 'translateY(-4px) scale(1.05)',
                  boxShadow: '0 16px 40px rgba(102, 126, 234, 0.3), 0 8px 20px rgba(0, 0, 0, 0.4)',
                  filter: 'brightness(1.2)',
                },
              }}
            >
              Explore AI Features
            </Button>
          </Box>
        </Box>

        {/* Full Width Professional Scanner Section */}
        <Box width="100%" maxWidth="1200px" mx="auto" id="scanner-section">
          {/* Main Scanner Card - Full Width */}
          <Box>
            <Card
              sx={{
                mb: 6,
                background: 'linear-gradient(135deg, rgba(12, 12, 12, 0.95) 0%, rgba(26, 26, 26, 0.95) 50%, rgba(45, 45, 45, 0.95) 100%)',
                backdropFilter: 'blur(25px) saturate(180%)',
                border: '1px solid rgba(255, 255, 255, 0.1)',
                borderRadius: 4,
                overflow: 'hidden',
                position: 'relative',
                boxShadow: '0 20px 60px rgba(102, 126, 234, 0.2)',
                transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                '&:hover': {
                  transform: 'translateY(-4px)',
                  boxShadow: '0 30px 80px rgba(102, 126, 234, 0.3)',
                },
                '&::before': {
                  content: '""',
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  right: 0,
                  height: '4px',
                  background: 'linear-gradient(90deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%)',
                  backgroundSize: '200% 200%',
                  animation: 'gradientShift 4s ease infinite',
                },
                '@keyframes gradientShift': {
                  '0%': { backgroundPosition: '0% 50%' },
                  '50%': { backgroundPosition: '100% 50%' },
                  '100%': { backgroundPosition: '0% 50%' },
                },
              }}
            >
              <CardContent sx={{ p: 6 }}>
                  {/* Tabs for different scan types */}
                  <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 4 }}>
                    <Tabs
                      value={activeTab}
                      onChange={handleTabChange}
                      variant="fullWidth"
                      textColor="primary"
                      indicatorColor="primary"
                      sx={{
                        '& .MuiTab-root': {
                          minHeight: 72,
                          fontSize: '1rem',
                          fontWeight: 600,
                          textTransform: 'none',
                        },
                      }}
                    >
                      <Tab
                        icon={<LinkIcon sx={{ fontSize: 28 }} />}
                        label="URL Security Scanner"
                        iconPosition="start"
                      />
                      <Tab
                        icon={<FileIcon sx={{ fontSize: 28 }} />}
                        label="File Security Scanner"
                        iconPosition="start"
                      />
                      <Tab
                        icon={<SecurityIcon sx={{ fontSize: 28 }} />}
                        label="Smart Dashboard"
                        iconPosition="start"
                      />
                      <Tab
                        icon={<AssessmentIcon sx={{ fontSize: 28 }} />}
                        label="Threat Prediction"
                        iconPosition="start"
                      />
                    </Tabs>
                  </Box>

                  {/* Tab Panels */}
                  <Box>
                    {activeTab === 0 && (
                      <Fade in={activeTab === 0} timeout={300}>
                        <Box>
                          <LinkScanner />
                        </Box>
                      </Fade>
                    )}

                    {activeTab === 1 && (
                      <Fade in={activeTab === 1} timeout={300}>
                        <Box>
                          <FileUploader />
                        </Box>
                      </Fade>
                    )}

                    {activeTab === 2 && (
                      <Fade in={activeTab === 2} timeout={300}>
                        <Box>
                          <SmartDashboard />
                        </Box>
                      </Fade>
                    )}

                    {activeTab === 3 && (
                      <Fade in={activeTab === 3} timeout={300}>
                        <Box>
                          <ThreatPrediction />
                        </Box>
                      </Fade>
                    )}
                  </Box>
                </CardContent>
              </Card>

              {/* Results Section */}
              {(currentScan || isScanning) && (
                <Fade in={true} timeout={500}>
                  <Box>
                    <ResultView
                      scanResult={currentScan}
                      onDownloadReport={handleDownloadReport}
                      onShareResult={handleShareResult}
                    />
                  </Box>
                </Fade>
              )}






            </Box>
        </Box>
      </Container>

      {/* Notifications */}
      {notifications.map((notification) => (
        <Snackbar
          key={notification.id}
          open={true}
          autoHideDuration={6000}
          onClose={() => handleCloseNotification(notification.id)}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
        >
          <Alert
            onClose={() => handleCloseNotification(notification.id)}
            severity={notification.type}
            variant="filled"
            sx={{
              width: '100%',
              borderRadius: 2,
              boxShadow: '0 8px 32px rgba(0,0,0,0.12)',
            }}
          >
            <Typography variant="subtitle2" fontWeight="bold">
              {notification.title}
            </Typography>
            {notification.message && (
              <Typography variant="body2">
                {notification.message}
              </Typography>
            )}
          </Alert>
        </Snackbar>
      ))}

      {/* Custom notification */}
      {notification && (
        <Snackbar
          open={true}
          autoHideDuration={4000}
          onClose={() => setNotification(null)}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
        >
          <Alert
            onClose={() => setNotification(null)}
            severity={notification.type}
            variant="filled"
            sx={{
              borderRadius: 2,
              boxShadow: '0 8px 32px rgba(0,0,0,0.12)',
            }}
          >
            {notification.message}
          </Alert>
        </Snackbar>
      )}
    </Box>
  );
});

HomePage.displayName = 'HomePage';

export default HomePage;
