[{"E:\\PythonProjects\\React.js\\Smart AI Assisstant\\src\\index.js": "1", "E:\\PythonProjects\\React.js\\Smart AI Assisstant\\src\\App.js": "2", "E:\\PythonProjects\\React.js\\Smart AI Assisstant\\src\\contexts\\ScanContext.jsx": "3", "E:\\PythonProjects\\React.js\\Smart AI Assisstant\\src\\pages\\HistoryPage.jsx": "4", "E:\\PythonProjects\\React.js\\Smart AI Assisstant\\src\\pages\\HomePage.jsx": "5", "E:\\PythonProjects\\React.js\\Smart AI Assisstant\\src\\components\\ErrorBoundary.jsx": "6", "E:\\PythonProjects\\React.js\\Smart AI Assisstant\\src\\components\\Header.jsx": "7", "E:\\PythonProjects\\React.js\\Smart AI Assisstant\\src\\components\\HistoryLog.jsx": "8", "E:\\PythonProjects\\React.js\\Smart AI Assisstant\\src\\components\\FileUploader.jsx": "9", "E:\\PythonProjects\\React.js\\Smart AI Assisstant\\src\\components\\ThreatMeter.jsx": "10", "E:\\PythonProjects\\React.js\\Smart AI Assisstant\\src\\components\\LinkScanner.jsx": "11", "E:\\PythonProjects\\React.js\\Smart AI Assisstant\\src\\components\\ResultView.jsx": "12", "E:\\PythonProjects\\React.js\\Smart AI Assisstant\\src\\services\\security.js": "13", "E:\\PythonProjects\\React.js\\Smart AI Assisstant\\src\\services\\api.js": "14", "E:\\PythonProjects\\React.js\\Smart AI Assisstant\\src\\components\\Footer.jsx": "15", "E:\\PythonProjects\\React.js\\Smart AI Assisstant\\src\\pages\\AboutPage.jsx": "16", "E:\\PythonProjects\\React.js\\Smart AI Assisstant\\src\\services\\reportService.js": "17", "E:\\PythonProjects\\React.js\\Smart AI Assisstant\\src\\components\\SettingsDialog.jsx": "18", "E:\\PythonProjects\\React.js\\Smart AI Assisstant\\src\\components\\SmartFAB.jsx": "19", "E:\\PythonProjects\\React.js\\Smart AI Assisstant\\src\\pages\\SmartFeaturesPage.jsx": "20", "E:\\PythonProjects\\React.js\\Smart AI Assisstant\\src\\components\\SmartNotifications.jsx": "21", "E:\\PythonProjects\\React.js\\Smart AI Assisstant\\src\\components\\ThreatPrediction.jsx": "22", "E:\\PythonProjects\\React.js\\Smart AI Assisstant\\src\\components\\SmartDashboard.jsx": "23", "E:\\PythonProjects\\React.js\\Smart AI Assisstant\\src\\components\\AutoScanSystem.jsx": "24", "E:\\PythonProjects\\React.js\\Smart AI Assisstant\\src\\components\\FloatingActionMenu.jsx": "25", "E:\\PythonProjects\\React.js\\Smart AI Assisstant\\src\\pages\\PlansPage.jsx": "26", "E:\\PythonProjects\\React.js\\Smart AI Assisstant\\src\\components\\SmartFeatures.jsx": "27", "E:\\PythonProjects\\React.js\\Smart AI Assisstant\\src\\contexts\\AuthContext.jsx": "28", "E:\\PythonProjects\\React.js\\Smart AI Assisstant\\src\\services\\auth.js": "29", "E:\\PythonProjects\\React.js\\Smart AI Assisstant\\src\\components\\EnhancedSettingsDialog.jsx": "30", "E:\\PythonProjects\\React.js\\Smart AI Assisstant\\src\\components\\UserProfile.jsx": "31", "E:\\PythonProjects\\React.js\\Smart AI Assisstant\\src\\components\\SecuritySettings.jsx": "32", "E:\\PythonProjects\\React.js\\Smart AI Assisstant\\src\\components\\AppearanceSettings.jsx": "33", "E:\\PythonProjects\\React.js\\Smart AI Assisstant\\src\\components\\AboutSettings.jsx": "34", "E:\\PythonProjects\\React.js\\Smart AI Assisstant\\src\\components\\NotificationSettings.jsx": "35", "E:\\PythonProjects\\React.js\\Smart AI Assisstant\\src\\components\\AuthenticationForms.jsx": "36", "E:\\PythonProjects\\React.js\\Smart AI Assisstant\\src\\components\\NotificationDetailDialog.jsx": "37"}, {"size": 254, "mtime": 1754419717108, "results": "38", "hashOfConfig": "39"}, {"size": 6184, "mtime": 1754581406410, "results": "40", "hashOfConfig": "39"}, {"size": 4858, "mtime": 1754498686494, "results": "41", "hashOfConfig": "39"}, {"size": 21882, "mtime": 1754509722771, "results": "42", "hashOfConfig": "39"}, {"size": 21162, "mtime": 1754589410973, "results": "43", "hashOfConfig": "39"}, {"size": 3870, "mtime": 1754419798962, "results": "44", "hashOfConfig": "39"}, {"size": 18937, "mtime": 1754589377798, "results": "45", "hashOfConfig": "39"}, {"size": 12978, "mtime": 1754420018117, "results": "46", "hashOfConfig": "39"}, {"size": 15013, "mtime": 1754589684153, "results": "47", "hashOfConfig": "39"}, {"size": 7064, "mtime": 1754419834146, "results": "48", "hashOfConfig": "39"}, {"size": 14279, "mtime": 1754589655700, "results": "49", "hashOfConfig": "39"}, {"size": 20767, "mtime": 1754423096323, "results": "50", "hashOfConfig": "39"}, {"size": 11940, "mtime": 1754420108847, "results": "51", "hashOfConfig": "39"}, {"size": 7534, "mtime": 1754420052788, "results": "52", "hashOfConfig": "39"}, {"size": 13808, "mtime": 1754509669665, "results": "53", "hashOfConfig": "39"}, {"size": 18841, "mtime": 1754585277719, "results": "54", "hashOfConfig": "39"}, {"size": 10115, "mtime": 1754422845196, "results": "55", "hashOfConfig": "39"}, {"size": 33722, "mtime": 1754581380692, "results": "56", "hashOfConfig": "39"}, {"size": 10287, "mtime": 1754433700162, "results": "57", "hashOfConfig": "39"}, {"size": 13425, "mtime": 1754509463865, "results": "58", "hashOfConfig": "39"}, {"size": 18088, "mtime": 1754433642016, "results": "59", "hashOfConfig": "39"}, {"size": 16671, "mtime": 1754433614187, "results": "60", "hashOfConfig": "39"}, {"size": 18492, "mtime": 1754589905425, "results": "61", "hashOfConfig": "39"}, {"size": 19758, "mtime": 1754589701700, "results": "62", "hashOfConfig": "39"}, {"size": 4122, "mtime": 1754589716142, "results": "63", "hashOfConfig": "39"}, {"size": 13725, "mtime": 1754586595049, "results": "64", "hashOfConfig": "39"}, {"size": 6749, "mtime": 1754500908797, "results": "65", "hashOfConfig": "39"}, {"size": 5698, "mtime": 1754583470513, "results": "66", "hashOfConfig": "39"}, {"size": 6163, "mtime": 1754583505605, "results": "67", "hashOfConfig": "39"}, {"size": 9625, "mtime": 1754583612311, "results": "68", "hashOfConfig": "39"}, {"size": 17283, "mtime": 1754589829633, "results": "69", "hashOfConfig": "39"}, {"size": 13746, "mtime": 1754583706378, "results": "70", "hashOfConfig": "39"}, {"size": 12144, "mtime": 1754583786985, "results": "71", "hashOfConfig": "39"}, {"size": 9926, "mtime": 1754584397203, "results": "72", "hashOfConfig": "39"}, {"size": 11402, "mtime": 1754584363131, "results": "73", "hashOfConfig": "39"}, {"size": 15772, "mtime": 1754583661289, "results": "74", "hashOfConfig": "39"}, {"size": 9237, "mtime": 1754585041360, "results": "75", "hashOfConfig": "39"}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1ow2vt1", {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "E:\\PythonProjects\\React.js\\Smart AI Assisstant\\src\\index.js", [], [], "E:\\PythonProjects\\React.js\\Smart AI Assisstant\\src\\App.js", [], [], "E:\\PythonProjects\\React.js\\Smart AI Assisstant\\src\\contexts\\ScanContext.jsx", [], [], "E:\\PythonProjects\\React.js\\Smart AI Assisstant\\src\\pages\\HistoryPage.jsx", [], [], "E:\\PythonProjects\\React.js\\Smart AI Assisstant\\src\\pages\\HomePage.jsx", [], [], "E:\\PythonProjects\\React.js\\Smart AI Assisstant\\src\\components\\ErrorBoundary.jsx", [], [], "E:\\PythonProjects\\React.js\\Smart AI Assisstant\\src\\components\\Header.jsx", [], [], "E:\\PythonProjects\\React.js\\Smart AI Assisstant\\src\\components\\HistoryLog.jsx", [], [], "E:\\PythonProjects\\React.js\\Smart AI Assisstant\\src\\components\\FileUploader.jsx", [], [], "E:\\PythonProjects\\React.js\\Smart AI Assisstant\\src\\components\\ThreatMeter.jsx", [], [], "E:\\PythonProjects\\React.js\\Smart AI Assisstant\\src\\components\\LinkScanner.jsx", [], [], "E:\\PythonProjects\\React.js\\Smart AI Assisstant\\src\\components\\ResultView.jsx", [], [], "E:\\PythonProjects\\React.js\\Smart AI Assisstant\\src\\services\\security.js", [], [], "E:\\PythonProjects\\React.js\\Smart AI Assisstant\\src\\services\\api.js", [], [], "E:\\PythonProjects\\React.js\\Smart AI Assisstant\\src\\components\\Footer.jsx", [], [], "E:\\PythonProjects\\React.js\\Smart AI Assisstant\\src\\pages\\AboutPage.jsx", [], [], "E:\\PythonProjects\\React.js\\Smart AI Assisstant\\src\\services\\reportService.js", [], [], "E:\\PythonProjects\\React.js\\Smart AI Assisstant\\src\\components\\SettingsDialog.jsx", [], [], "E:\\PythonProjects\\React.js\\Smart AI Assisstant\\src\\components\\SmartFAB.jsx", [], [], "E:\\PythonProjects\\React.js\\Smart AI Assisstant\\src\\pages\\SmartFeaturesPage.jsx", [], [], "E:\\PythonProjects\\React.js\\Smart AI Assisstant\\src\\components\\SmartNotifications.jsx", [], [], "E:\\PythonProjects\\React.js\\Smart AI Assisstant\\src\\components\\ThreatPrediction.jsx", [], [], "E:\\PythonProjects\\React.js\\Smart AI Assisstant\\src\\components\\SmartDashboard.jsx", [], [], "E:\\PythonProjects\\React.js\\Smart AI Assisstant\\src\\components\\AutoScanSystem.jsx", [], [], "E:\\PythonProjects\\React.js\\Smart AI Assisstant\\src\\components\\FloatingActionMenu.jsx", [], [], "E:\\PythonProjects\\React.js\\Smart AI Assisstant\\src\\pages\\PlansPage.jsx", [], [], "E:\\PythonProjects\\React.js\\Smart AI Assisstant\\src\\components\\SmartFeatures.jsx", [], [], "E:\\PythonProjects\\React.js\\Smart AI Assisstant\\src\\contexts\\AuthContext.jsx", [], [], "E:\\PythonProjects\\React.js\\Smart AI Assisstant\\src\\services\\auth.js", [], [], "E:\\PythonProjects\\React.js\\Smart AI Assisstant\\src\\components\\EnhancedSettingsDialog.jsx", [], [], "E:\\PythonProjects\\React.js\\Smart AI Assisstant\\src\\components\\UserProfile.jsx", [], [], "E:\\PythonProjects\\React.js\\Smart AI Assisstant\\src\\components\\SecuritySettings.jsx", [], [], "E:\\PythonProjects\\React.js\\Smart AI Assisstant\\src\\components\\AppearanceSettings.jsx", [], [], "E:\\PythonProjects\\React.js\\Smart AI Assisstant\\src\\components\\AboutSettings.jsx", [], [], "E:\\PythonProjects\\React.js\\Smart AI Assisstant\\src\\components\\NotificationSettings.jsx", [], [], "E:\\PythonProjects\\React.js\\Smart AI Assisstant\\src\\components\\AuthenticationForms.jsx", [], [], "E:\\PythonProjects\\React.js\\Smart AI Assisstant\\src\\components\\NotificationDetailDialog.jsx", [], []]