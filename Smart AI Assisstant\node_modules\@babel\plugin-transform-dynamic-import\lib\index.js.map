{"version": 3, "names": ["_helper<PERSON>lugin<PERSON><PERSON>s", "require", "SUPPORTED_MODULES", "Set", "MODULES_NOT_FOUND", "_default", "exports", "default", "declare", "api", "assertVersion", "name", "manipulateOptions", "_", "parser", "plugins", "push", "pre", "file", "set", "visitor", "Program", "modules", "get", "has", "Error"], "sources": ["../src/index.ts"], "sourcesContent": ["import { declare } from \"@babel/helper-plugin-utils\";\n\nconst SUPPORTED_MODULES = new Set([\"commonjs\", \"amd\", \"systemjs\"]);\n\nconst MODULES_NOT_FOUND = `\\\n@babel/plugin-transform-dynamic-import depends on a modules\ntransform plugin. Supported plugins are:\n - @babel/plugin-transform-modules-commonjs ^7.4.0\n - @babel/plugin-transform-modules-amd ^7.4.0\n - @babel/plugin-transform-modules-systemjs ^7.4.0\n\nIf you are using Webpack or Rollup and thus don't want\nBabel to transpile your imports and exports, you can use\nthe @babel/plugin-syntax-dynamic-import plugin and let your\nbundler handle dynamic imports.\n`;\n\nexport default declare(api => {\n  api.assertVersion(REQUIRED_VERSION(7));\n\n  return {\n    name: \"transform-dynamic-import\",\n    manipulateOptions: process.env.BABEL_8_BREAKING\n      ? undefined\n      : (_, parser) => parser.plugins.push(\"dynamicImport\"),\n\n    pre() {\n      // We keep using the old name, for compatibility with older\n      // version of the CommonJS transform.\n      this.file.set(\n        \"@babel/plugin-proposal-dynamic-import\",\n        PACKAGE_JSON.version,\n      );\n    },\n\n    visitor: {\n      Program() {\n        const modules = this.file.get(\"@babel/plugin-transform-modules-*\");\n\n        if (!SUPPORTED_MODULES.has(modules)) {\n          throw new Error(MODULES_NOT_FOUND);\n        }\n      },\n    },\n  };\n});\n"], "mappings": ";;;;;;AAAA,IAAAA,kBAAA,GAAAC,OAAA;AAEA,MAAMC,iBAAiB,GAAG,IAAIC,GAAG,CAAC,CAAC,UAAU,EAAE,KAAK,EAAE,UAAU,CAAC,CAAC;AAElE,MAAMC,iBAAiB,GAAG;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAAC,IAAAC,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEa,IAAAC,0BAAO,EAACC,GAAG,IAAI;EAC5BA,GAAG,CAACC,aAAa,CAAkB,CAAE,CAAC;EAEtC,OAAO;IACLC,IAAI,EAAE,0BAA0B;IAChCC,iBAAiB,EAEbA,CAACC,CAAC,EAAEC,MAAM,KAAKA,MAAM,CAACC,OAAO,CAACC,IAAI,CAAC,eAAe,CAAC;IAEvDC,GAAGA,CAAA,EAAG;MAGJ,IAAI,CAACC,IAAI,CAACC,GAAG,CACX,uCAAuC,UAEzC,CAAC;IACH,CAAC;IAEDC,OAAO,EAAE;MACPC,OAAOA,CAAA,EAAG;QACR,MAAMC,OAAO,GAAG,IAAI,CAACJ,IAAI,CAACK,GAAG,CAAC,mCAAmC,CAAC;QAElE,IAAI,CAACrB,iBAAiB,CAACsB,GAAG,CAACF,OAAO,CAAC,EAAE;UACnC,MAAM,IAAIG,KAAK,CAACrB,iBAAiB,CAAC;QACpC;MACF;IACF;EACF,CAAC;AACH,CAAC,CAAC", "ignoreList": []}