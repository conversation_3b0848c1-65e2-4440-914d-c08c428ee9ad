{"ast": null, "code": "var _jsxFileName = \"E:\\\\PythonProjects\\\\React.js\\\\Smart AI Assisstant\\\\src\\\\components\\\\Header.jsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { AppBar, Toolbar, Typography, IconButton, Switch, FormControlLabel, Box, Button, Menu, MenuItem, Container, Tooltip, Avatar, Badge } from '@mui/material';\nimport { Security as SecurityIcon, DarkMode as DarkModeIcon, LightMode as LightModeIcon, Language as LanguageIcon, History as HistoryIcon, Home as HomeIcon, Info as InfoIcon, Notifications as NotificationsIcon, Settings as SettingsIcon } from '@mui/icons-material';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport { useScan } from '../contexts/ScanContext';\nimport SettingsDialog from './SettingsDialog';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Header = /*#__PURE__*/_s(/*#__PURE__*/React.memo(_c = _s(() => {\n  _s();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const {\n    darkMode,\n    language,\n    toggleDarkMode,\n    setLanguage,\n    notifications\n  } = useScan();\n  const [languageAnchor, setLanguageAnchor] = React.useState(null);\n  const [settingsAnchor, setSettingsAnchor] = React.useState(null);\n  const [settingsDialogOpen, setSettingsDialogOpen] = React.useState(false);\n  const handleLanguageClick = event => {\n    setLanguageAnchor(event.currentTarget);\n  };\n  const handleLanguageClose = () => {\n    setLanguageAnchor(null);\n  };\n  const handleLanguageSelect = lang => {\n    setLanguage(lang);\n    handleLanguageClose();\n  };\n  const handleSettingsClick = event => {\n    setSettingsAnchor(event.currentTarget);\n  };\n  const handleSettingsClose = () => {\n    setSettingsAnchor(null);\n  };\n  const handleOpenSettingsDialog = () => {\n    setSettingsDialogOpen(true);\n    handleSettingsClose();\n  };\n  const isHomePage = location.pathname === '/';\n  const isHistoryPage = location.pathname === '/history';\n  const isAboutPage = location.pathname === '/about';\n  const navigationItems = [{\n    label: 'Home',\n    path: '/',\n    icon: /*#__PURE__*/_jsxDEV(HomeIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 72,\n      columnNumber: 39\n    }, this),\n    active: isHomePage\n  }, {\n    label: 'History',\n    path: '/history',\n    icon: /*#__PURE__*/_jsxDEV(HistoryIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 73,\n      columnNumber: 49\n    }, this),\n    active: isHistoryPage\n  }, {\n    label: 'About',\n    path: '/about',\n    icon: /*#__PURE__*/_jsxDEV(InfoIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 74,\n      columnNumber: 45\n    }, this),\n    active: isAboutPage\n  }];\n  return /*#__PURE__*/_jsxDEV(AppBar, {\n    position: \"sticky\",\n    elevation: 0,\n    sx: {\n      background: theme => theme.palette.mode === 'dark' ? 'linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%)' : 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n      backdropFilter: 'blur(10px)',\n      borderBottom: theme => `1px solid ${theme.palette.mode === 'dark' ? '#333' : 'rgba(255,255,255,0.1)'}`\n    },\n    children: /*#__PURE__*/_jsxDEV(Container, {\n      maxWidth: \"xl\",\n      children: /*#__PURE__*/_jsxDEV(Toolbar, {\n        sx: {\n          justifyContent: 'space-between',\n          py: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          display: \"flex\",\n          alignItems: \"center\",\n          gap: 2,\n          children: [/*#__PURE__*/_jsxDEV(IconButton, {\n            edge: \"start\",\n            color: \"inherit\",\n            \"aria-label\": \"security logo\",\n            onClick: () => navigate('/'),\n            sx: {\n              background: 'rgba(255,255,255,0.1)',\n              backdropFilter: 'blur(10px)',\n              '&:hover': {\n                background: 'rgba(255,255,255,0.2)',\n                transform: 'scale(1.05)'\n              },\n              transition: 'all 0.2s ease'\n            },\n            children: /*#__PURE__*/_jsxDEV(SecurityIcon, {\n              fontSize: \"large\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 108,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 93,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h4\",\n              component: \"h1\",\n              sx: {\n                fontWeight: 800,\n                cursor: 'pointer',\n                display: {\n                  xs: 'none',\n                  md: 'block'\n                },\n                background: 'linear-gradient(45deg, #ffffff, #e3f2fd)',\n                backgroundClip: 'text',\n                WebkitBackgroundClip: 'text',\n                WebkitTextFillColor: 'transparent',\n                textShadow: '0 2px 4px rgba(0,0,0,0.1)'\n              },\n              onClick: () => navigate('/'),\n              children: \"AI Security Guard\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 111,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              component: \"h1\",\n              sx: {\n                fontWeight: 700,\n                cursor: 'pointer',\n                display: {\n                  xs: 'block',\n                  md: 'none'\n                },\n                color: 'white'\n              },\n              onClick: () => navigate('/'),\n              children: \"AI Security\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 128,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              sx: {\n                display: {\n                  xs: 'none',\n                  md: 'block'\n                },\n                color: 'rgba(255,255,255,0.8)',\n                fontWeight: 500\n              },\n              children: \"Advanced Threat Detection Platform\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 141,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 110,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 92,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          display: \"flex\",\n          alignItems: \"center\",\n          gap: 2,\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            display: {\n              xs: 'none',\n              md: 'flex'\n            },\n            gap: 1,\n            children: navigationItems.map(item => /*#__PURE__*/_jsxDEV(Button, {\n              color: \"inherit\",\n              startIcon: item.icon,\n              onClick: () => navigate(item.path),\n              variant: item.active ? 'contained' : 'text',\n              sx: {\n                borderRadius: 3,\n                px: 3,\n                py: 1,\n                fontWeight: 600,\n                textTransform: 'none',\n                background: item.active ? 'rgba(255,255,255,0.2)' : 'transparent',\n                backdropFilter: item.active ? 'blur(10px)' : 'none',\n                border: item.active ? '1px solid rgba(255,255,255,0.3)' : 'none',\n                '&:hover': {\n                  background: 'rgba(255,255,255,0.15)',\n                  transform: 'translateY(-1px)'\n                },\n                transition: 'all 0.2s ease'\n              },\n              children: item.label\n            }, item.path, false, {\n              fileName: _jsxFileName,\n              lineNumber: 159,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 157,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            display: {\n              xs: 'flex',\n              md: 'none'\n            },\n            gap: 0.5,\n            children: navigationItems.map(item => /*#__PURE__*/_jsxDEV(Tooltip, {\n              title: item.label,\n              children: /*#__PURE__*/_jsxDEV(IconButton, {\n                color: \"inherit\",\n                onClick: () => navigate(item.path),\n                sx: {\n                  background: item.active ? 'rgba(255,255,255,0.2)' : 'transparent',\n                  backdropFilter: item.active ? 'blur(10px)' : 'none',\n                  border: item.active ? '1px solid rgba(255,255,255,0.3)' : 'none',\n                  '&:hover': {\n                    background: 'rgba(255,255,255,0.15)'\n                  }\n                },\n                children: item.icon\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 192,\n                columnNumber: 19\n              }, this)\n            }, item.path, false, {\n              fileName: _jsxFileName,\n              lineNumber: 191,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 189,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n            title: \"Notifications\",\n            children: /*#__PURE__*/_jsxDEV(IconButton, {\n              color: \"inherit\",\n              children: /*#__PURE__*/_jsxDEV(Badge, {\n                badgeContent: notifications.length,\n                color: \"error\",\n                children: /*#__PURE__*/_jsxDEV(NotificationsIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 216,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 215,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 214,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n            title: \"Settings\",\n            children: /*#__PURE__*/_jsxDEV(IconButton, {\n              color: \"inherit\",\n              onClick: handleSettingsClick,\n              sx: {\n                background: 'rgba(255,255,255,0.1)',\n                '&:hover': {\n                  background: 'rgba(255,255,255,0.2)'\n                }\n              },\n              children: /*#__PURE__*/_jsxDEV(SettingsIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 233,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 223,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 222,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Menu, {\n            anchorEl: settingsAnchor,\n            open: Boolean(settingsAnchor),\n            onClose: handleSettingsClose,\n            anchorOrigin: {\n              vertical: 'bottom',\n              horizontal: 'right'\n            },\n            transformOrigin: {\n              vertical: 'top',\n              horizontal: 'right'\n            },\n            PaperProps: {\n              sx: {\n                mt: 1,\n                borderRadius: 2,\n                minWidth: 200\n              }\n            },\n            children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: handleOpenSettingsDialog,\n              sx: {\n                gap: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(SettingsIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 259,\n                columnNumber: 17\n              }, this), \"Account & Settings\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 258,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: handleLanguageClick,\n              sx: {\n                gap: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(LanguageIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 265,\n                columnNumber: 17\n              }, this), \"Language\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 264,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              sx: {\n                gap: 2\n              },\n              children: [darkMode ? /*#__PURE__*/_jsxDEV(DarkModeIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 271,\n                columnNumber: 29\n              }, this) : /*#__PURE__*/_jsxDEV(LightModeIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 271,\n                columnNumber: 48\n              }, this), /*#__PURE__*/_jsxDEV(FormControlLabel, {\n                control: /*#__PURE__*/_jsxDEV(Switch, {\n                  checked: darkMode,\n                  onChange: toggleDarkMode,\n                  size: \"small\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 274,\n                  columnNumber: 21\n                }, this),\n                label: \"Dark Mode\",\n                sx: {\n                  margin: 0,\n                  flex: 1\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 272,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 270,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 237,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Menu, {\n            anchorEl: languageAnchor,\n            open: Boolean(languageAnchor),\n            onClose: handleLanguageClose,\n            anchorOrigin: {\n              vertical: 'bottom',\n              horizontal: 'right'\n            },\n            transformOrigin: {\n              vertical: 'top',\n              horizontal: 'right'\n            },\n            PaperProps: {\n              sx: {\n                mt: 1,\n                borderRadius: 2\n              }\n            },\n            children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => handleLanguageSelect('en'),\n              selected: language === 'en',\n              children: \"\\uD83C\\uDDFA\\uD83C\\uDDF8 English\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 306,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => handleLanguageSelect('ar'),\n              selected: language === 'ar',\n              children: \"\\uD83C\\uDDF8\\uD83C\\uDDE6 \\u0627\\u0644\\u0639\\u0631\\u0628\\u064A\\u0629\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 312,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 287,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Avatar, {\n            sx: {\n              width: 40,\n              height: 40,\n              bgcolor: 'rgba(255,255,255,0.2)',\n              border: '2px solid rgba(255,255,255,0.3)',\n              cursor: 'pointer',\n              '&:hover': {\n                transform: 'scale(1.05)'\n              },\n              transition: 'transform 0.2s ease'\n            },\n            children: /*#__PURE__*/_jsxDEV(SecurityIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 334,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 321,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 155,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 90,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 89,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 78,\n    columnNumber: 5\n  }, this);\n}, \"pGiW9ryULk/H4OaKiDnTYt3V0Yk=\", false, function () {\n  return [useNavigate, useLocation, useScan];\n})), \"pGiW9ryULk/H4OaKiDnTYt3V0Yk=\", false, function () {\n  return [useNavigate, useLocation, useScan];\n});\n_c2 = Header;\nHeader.displayName = 'Header';\nexport default Header;\nvar _c, _c2;\n$RefreshReg$(_c, \"Header$React.memo\");\n$RefreshReg$(_c2, \"Header\");", "map": {"version": 3, "names": ["React", "AppBar", "<PERSON><PERSON><PERSON>", "Typography", "IconButton", "Switch", "FormControlLabel", "Box", "<PERSON><PERSON>", "<PERSON><PERSON>", "MenuItem", "Container", "<PERSON><PERSON><PERSON>", "Avatar", "Badge", "Security", "SecurityIcon", "DarkMode", "DarkModeIcon", "LightMode", "LightModeIcon", "Language", "LanguageIcon", "History", "HistoryIcon", "Home", "HomeIcon", "Info", "InfoIcon", "Notifications", "NotificationsIcon", "Settings", "SettingsIcon", "useNavigate", "useLocation", "useScan", "SettingsDialog", "jsxDEV", "_jsxDEV", "Header", "_s", "memo", "_c", "navigate", "location", "darkMode", "language", "toggleDarkMode", "setLanguage", "notifications", "languageAnchor", "setLanguageAnchor", "useState", "settingsAnchor", "setSettingsAnchor", "settingsDialogOpen", "setSettingsDialogOpen", "handleLanguageClick", "event", "currentTarget", "handleLanguageClose", "handleLanguageSelect", "lang", "handleSettingsClick", "handleSettingsClose", "handleOpenSettingsDialog", "isHomePage", "pathname", "isHistoryPage", "isAboutPage", "navigationItems", "label", "path", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "active", "position", "elevation", "sx", "background", "theme", "palette", "mode", "<PERSON><PERSON>ilter", "borderBottom", "children", "max<PERSON><PERSON><PERSON>", "justifyContent", "py", "display", "alignItems", "gap", "edge", "color", "onClick", "transform", "transition", "fontSize", "variant", "component", "fontWeight", "cursor", "xs", "md", "backgroundClip", "WebkitBackgroundClip", "WebkitTextFillColor", "textShadow", "map", "item", "startIcon", "borderRadius", "px", "textTransform", "border", "title", "badgeContent", "length", "anchorEl", "open", "Boolean", "onClose", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "transform<PERSON><PERSON>in", "PaperProps", "mt", "min<PERSON><PERSON><PERSON>", "control", "checked", "onChange", "size", "margin", "flex", "selected", "width", "height", "bgcolor", "_c2", "displayName", "$RefreshReg$"], "sources": ["E:/PythonProjects/React.js/Smart AI Assisstant/src/components/Header.jsx"], "sourcesContent": ["import React from 'react';\nimport {\n  A<PERSON><PERSON><PERSON>,\n  <PERSON><PERSON><PERSON>,\n  <PERSON><PERSON><PERSON>,\n  IconButton,\n  Switch,\n  FormControlLabel,\n  Box,\n  Button,\n  Menu,\n  MenuItem,\n  Container,\n  Tooltip,\n  Avatar,\n  Badge,\n} from '@mui/material';\nimport {\n  Security as SecurityIcon,\n  DarkMode as DarkModeIcon,\n  LightMode as LightModeIcon,\n  Language as LanguageIcon,\n  History as HistoryIcon,\n  Home as HomeIcon,\n  Info as InfoIcon,\n  Notifications as NotificationsIcon,\n  Settings as SettingsIcon,\n} from '@mui/icons-material';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport { useScan } from '../contexts/ScanContext';\nimport SettingsDialog from './SettingsDialog';\n\nconst Header = React.memo(() => {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const { darkMode, language, toggleDarkMode, setLanguage, notifications } = useScan();\n  const [languageAnchor, setLanguageAnchor] = React.useState(null);\n  const [settingsAnchor, setSettingsAnchor] = React.useState(null);\n  const [settingsDialogOpen, setSettingsDialogOpen] = React.useState(false);\n\n  const handleLanguageClick = (event) => {\n    setLanguageAnchor(event.currentTarget);\n  };\n\n  const handleLanguageClose = () => {\n    setLanguageAnchor(null);\n  };\n\n  const handleLanguageSelect = (lang) => {\n    setLanguage(lang);\n    handleLanguageClose();\n  };\n\n  const handleSettingsClick = (event) => {\n    setSettingsAnchor(event.currentTarget);\n  };\n\n  const handleSettingsClose = () => {\n    setSettingsAnchor(null);\n  };\n\n  const handleOpenSettingsDialog = () => {\n    setSettingsDialogOpen(true);\n    handleSettingsClose();\n  };\n\n  const isHomePage = location.pathname === '/';\n  const isHistoryPage = location.pathname === '/history';\n  const isAboutPage = location.pathname === '/about';\n\n  const navigationItems = [\n    { label: 'Home', path: '/', icon: <HomeIcon />, active: isHomePage },\n    { label: 'History', path: '/history', icon: <HistoryIcon />, active: isHistoryPage },\n    { label: 'About', path: '/about', icon: <InfoIcon />, active: isAboutPage },\n  ];\n\n  return (\n    <AppBar\n      position=\"sticky\"\n      elevation={0}\n      sx={{\n        background: (theme) => theme.palette.mode === 'dark'\n          ? 'linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%)'\n          : 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n        backdropFilter: 'blur(10px)',\n        borderBottom: (theme) => `1px solid ${theme.palette.mode === 'dark' ? '#333' : 'rgba(255,255,255,0.1)'}`,\n      }}\n    >\n      <Container maxWidth=\"xl\">\n        <Toolbar sx={{ justifyContent: 'space-between', py: 1 }}>\n          {/* Logo and Title */}\n          <Box display=\"flex\" alignItems=\"center\" gap={2}>\n            <IconButton\n              edge=\"start\"\n              color=\"inherit\"\n              aria-label=\"security logo\"\n              onClick={() => navigate('/')}\n              sx={{\n                background: 'rgba(255,255,255,0.1)',\n                backdropFilter: 'blur(10px)',\n                '&:hover': {\n                  background: 'rgba(255,255,255,0.2)',\n                  transform: 'scale(1.05)',\n                },\n                transition: 'all 0.2s ease',\n              }}\n            >\n              <SecurityIcon fontSize=\"large\" />\n            </IconButton>\n            <Box>\n              <Typography\n                variant=\"h4\"\n                component=\"h1\"\n                sx={{\n                  fontWeight: 800,\n                  cursor: 'pointer',\n                  display: { xs: 'none', md: 'block' },\n                  background: 'linear-gradient(45deg, #ffffff, #e3f2fd)',\n                  backgroundClip: 'text',\n                  WebkitBackgroundClip: 'text',\n                  WebkitTextFillColor: 'transparent',\n                  textShadow: '0 2px 4px rgba(0,0,0,0.1)',\n                }}\n                onClick={() => navigate('/')}\n              >\n                AI Security Guard\n              </Typography>\n              <Typography\n                variant=\"h6\"\n                component=\"h1\"\n                sx={{\n                  fontWeight: 700,\n                  cursor: 'pointer',\n                  display: { xs: 'block', md: 'none' },\n                  color: 'white',\n                }}\n                onClick={() => navigate('/')}\n              >\n                AI Security\n              </Typography>\n              <Typography\n                variant=\"caption\"\n                sx={{\n                  display: { xs: 'none', md: 'block' },\n                  color: 'rgba(255,255,255,0.8)',\n                  fontWeight: 500,\n                }}\n              >\n                Advanced Threat Detection Platform\n              </Typography>\n            </Box>\n          </Box>\n\n          {/* Navigation and Controls */}\n          <Box display=\"flex\" alignItems=\"center\" gap={2}>\n            {/* Desktop Navigation */}\n            <Box display={{ xs: 'none', md: 'flex' }} gap={1}>\n              {navigationItems.map((item) => (\n                <Button\n                  key={item.path}\n                  color=\"inherit\"\n                  startIcon={item.icon}\n                  onClick={() => navigate(item.path)}\n                  variant={item.active ? 'contained' : 'text'}\n                  sx={{\n                    borderRadius: 3,\n                    px: 3,\n                    py: 1,\n                    fontWeight: 600,\n                    textTransform: 'none',\n                    background: item.active\n                      ? 'rgba(255,255,255,0.2)'\n                      : 'transparent',\n                    backdropFilter: item.active ? 'blur(10px)' : 'none',\n                    border: item.active ? '1px solid rgba(255,255,255,0.3)' : 'none',\n                    '&:hover': {\n                      background: 'rgba(255,255,255,0.15)',\n                      transform: 'translateY(-1px)',\n                    },\n                    transition: 'all 0.2s ease',\n                  }}\n                >\n                  {item.label}\n                </Button>\n              ))}\n            </Box>\n\n            {/* Mobile Navigation */}\n            <Box display={{ xs: 'flex', md: 'none' }} gap={0.5}>\n              {navigationItems.map((item) => (\n                <Tooltip key={item.path} title={item.label}>\n                  <IconButton\n                    color=\"inherit\"\n                    onClick={() => navigate(item.path)}\n                    sx={{\n                      background: item.active\n                        ? 'rgba(255,255,255,0.2)'\n                        : 'transparent',\n                      backdropFilter: item.active ? 'blur(10px)' : 'none',\n                      border: item.active ? '1px solid rgba(255,255,255,0.3)' : 'none',\n                      '&:hover': {\n                        background: 'rgba(255,255,255,0.15)',\n                      },\n                    }}\n                  >\n                    {item.icon}\n                  </IconButton>\n                </Tooltip>\n              ))}\n            </Box>\n\n            {/* Notifications */}\n            <Tooltip title=\"Notifications\">\n              <IconButton color=\"inherit\">\n                <Badge badgeContent={notifications.length} color=\"error\">\n                  <NotificationsIcon />\n                </Badge>\n              </IconButton>\n            </Tooltip>\n\n            {/* Settings Menu */}\n            <Tooltip title=\"Settings\">\n              <IconButton\n                color=\"inherit\"\n                onClick={handleSettingsClick}\n                sx={{\n                  background: 'rgba(255,255,255,0.1)',\n                  '&:hover': {\n                    background: 'rgba(255,255,255,0.2)',\n                  },\n                }}\n              >\n                <SettingsIcon />\n              </IconButton>\n            </Tooltip>\n\n            <Menu\n              anchorEl={settingsAnchor}\n              open={Boolean(settingsAnchor)}\n              onClose={handleSettingsClose}\n              anchorOrigin={{\n                vertical: 'bottom',\n                horizontal: 'right',\n              }}\n              transformOrigin={{\n                vertical: 'top',\n                horizontal: 'right',\n              }}\n              PaperProps={{\n                sx: {\n                  mt: 1,\n                  borderRadius: 2,\n                  minWidth: 200,\n                },\n              }}\n            >\n              {/* Settings Options */}\n              <MenuItem onClick={handleOpenSettingsDialog} sx={{ gap: 2 }}>\n                <SettingsIcon />\n                Account & Settings\n              </MenuItem>\n\n              {/* Language Selector */}\n              <MenuItem onClick={handleLanguageClick} sx={{ gap: 2 }}>\n                <LanguageIcon />\n                Language\n              </MenuItem>\n\n              {/* Dark Mode Toggle */}\n              <MenuItem sx={{ gap: 2 }}>\n                {darkMode ? <DarkModeIcon /> : <LightModeIcon />}\n                <FormControlLabel\n                  control={\n                    <Switch\n                      checked={darkMode}\n                      onChange={toggleDarkMode}\n                      size=\"small\"\n                    />\n                  }\n                  label=\"Dark Mode\"\n                  sx={{ margin: 0, flex: 1 }}\n                />\n              </MenuItem>\n            </Menu>\n\n            {/* Language Menu */}\n            <Menu\n              anchorEl={languageAnchor}\n              open={Boolean(languageAnchor)}\n              onClose={handleLanguageClose}\n              anchorOrigin={{\n                vertical: 'bottom',\n                horizontal: 'right',\n              }}\n              transformOrigin={{\n                vertical: 'top',\n                horizontal: 'right',\n              }}\n              PaperProps={{\n                sx: {\n                  mt: 1,\n                  borderRadius: 2,\n                },\n              }}\n            >\n              <MenuItem\n                onClick={() => handleLanguageSelect('en')}\n                selected={language === 'en'}\n              >\n                🇺🇸 English\n              </MenuItem>\n              <MenuItem\n                onClick={() => handleLanguageSelect('ar')}\n                selected={language === 'ar'}\n              >\n                🇸🇦 العربية\n              </MenuItem>\n            </Menu>\n\n            {/* User Avatar */}\n            <Avatar\n              sx={{\n                width: 40,\n                height: 40,\n                bgcolor: 'rgba(255,255,255,0.2)',\n                border: '2px solid rgba(255,255,255,0.3)',\n                cursor: 'pointer',\n                '&:hover': {\n                  transform: 'scale(1.05)',\n                },\n                transition: 'transform 0.2s ease',\n              }}\n            >\n              <SecurityIcon />\n            </Avatar>\n          </Box>\n        </Toolbar>\n      </Container>\n    </AppBar>\n  );\n});\n\nHeader.displayName = 'Header';\n\nexport default Header;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SACEC,MAAM,EACNC,OAAO,EACPC,UAAU,EACVC,UAAU,EACVC,MAAM,EACNC,gBAAgB,EAChBC,GAAG,EACHC,MAAM,EACNC,IAAI,EACJC,QAAQ,EACRC,SAAS,EACTC,OAAO,EACPC,MAAM,EACNC,KAAK,QACA,eAAe;AACtB,SACEC,QAAQ,IAAIC,YAAY,EACxBC,QAAQ,IAAIC,YAAY,EACxBC,SAAS,IAAIC,aAAa,EAC1BC,QAAQ,IAAIC,YAAY,EACxBC,OAAO,IAAIC,WAAW,EACtBC,IAAI,IAAIC,QAAQ,EAChBC,IAAI,IAAIC,QAAQ,EAChBC,aAAa,IAAIC,iBAAiB,EAClCC,QAAQ,IAAIC,YAAY,QACnB,qBAAqB;AAC5B,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,SAASC,OAAO,QAAQ,yBAAyB;AACjD,OAAOC,cAAc,MAAM,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9C,MAAMC,MAAM,gBAAAC,EAAA,cAAGxC,KAAK,CAACyC,IAAI,CAAAC,EAAA,GAAAF,EAAA,CAAC,MAAM;EAAAA,EAAA;EAC9B,MAAMG,QAAQ,GAAGV,WAAW,CAAC,CAAC;EAC9B,MAAMW,QAAQ,GAAGV,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEW,QAAQ;IAAEC,QAAQ;IAAEC,cAAc;IAAEC,WAAW;IAAEC;EAAc,CAAC,GAAGd,OAAO,CAAC,CAAC;EACpF,MAAM,CAACe,cAAc,EAAEC,iBAAiB,CAAC,GAAGnD,KAAK,CAACoD,QAAQ,CAAC,IAAI,CAAC;EAChE,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGtD,KAAK,CAACoD,QAAQ,CAAC,IAAI,CAAC;EAChE,MAAM,CAACG,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGxD,KAAK,CAACoD,QAAQ,CAAC,KAAK,CAAC;EAEzE,MAAMK,mBAAmB,GAAIC,KAAK,IAAK;IACrCP,iBAAiB,CAACO,KAAK,CAACC,aAAa,CAAC;EACxC,CAAC;EAED,MAAMC,mBAAmB,GAAGA,CAAA,KAAM;IAChCT,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;EAED,MAAMU,oBAAoB,GAAIC,IAAI,IAAK;IACrCd,WAAW,CAACc,IAAI,CAAC;IACjBF,mBAAmB,CAAC,CAAC;EACvB,CAAC;EAED,MAAMG,mBAAmB,GAAIL,KAAK,IAAK;IACrCJ,iBAAiB,CAACI,KAAK,CAACC,aAAa,CAAC;EACxC,CAAC;EAED,MAAMK,mBAAmB,GAAGA,CAAA,KAAM;IAChCV,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;EAED,MAAMW,wBAAwB,GAAGA,CAAA,KAAM;IACrCT,qBAAqB,CAAC,IAAI,CAAC;IAC3BQ,mBAAmB,CAAC,CAAC;EACvB,CAAC;EAED,MAAME,UAAU,GAAGtB,QAAQ,CAACuB,QAAQ,KAAK,GAAG;EAC5C,MAAMC,aAAa,GAAGxB,QAAQ,CAACuB,QAAQ,KAAK,UAAU;EACtD,MAAME,WAAW,GAAGzB,QAAQ,CAACuB,QAAQ,KAAK,QAAQ;EAElD,MAAMG,eAAe,GAAG,CACtB;IAAEC,KAAK,EAAE,MAAM;IAAEC,IAAI,EAAE,GAAG;IAAEC,IAAI,eAAEnC,OAAA,CAACZ,QAAQ;MAAAgD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,MAAM,EAAEZ;EAAW,CAAC,EACpE;IAAEK,KAAK,EAAE,SAAS;IAAEC,IAAI,EAAE,UAAU;IAAEC,IAAI,eAAEnC,OAAA,CAACd,WAAW;MAAAkD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,MAAM,EAAEV;EAAc,CAAC,EACpF;IAAEG,KAAK,EAAE,OAAO;IAAEC,IAAI,EAAE,QAAQ;IAAEC,IAAI,eAAEnC,OAAA,CAACV,QAAQ;MAAA8C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,MAAM,EAAET;EAAY,CAAC,CAC5E;EAED,oBACE/B,OAAA,CAACrC,MAAM;IACL8E,QAAQ,EAAC,QAAQ;IACjBC,SAAS,EAAE,CAAE;IACbC,EAAE,EAAE;MACFC,UAAU,EAAGC,KAAK,IAAKA,KAAK,CAACC,OAAO,CAACC,IAAI,KAAK,MAAM,GAChD,mDAAmD,GACnD,mDAAmD;MACvDC,cAAc,EAAE,YAAY;MAC5BC,YAAY,EAAGJ,KAAK,IAAK,aAAaA,KAAK,CAACC,OAAO,CAACC,IAAI,KAAK,MAAM,GAAG,MAAM,GAAG,uBAAuB;IACxG,CAAE;IAAAG,QAAA,eAEFlD,OAAA,CAAC3B,SAAS;MAAC8E,QAAQ,EAAC,IAAI;MAAAD,QAAA,eACtBlD,OAAA,CAACpC,OAAO;QAAC+E,EAAE,EAAE;UAAES,cAAc,EAAE,eAAe;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAH,QAAA,gBAEtDlD,OAAA,CAAC/B,GAAG;UAACqF,OAAO,EAAC,MAAM;UAACC,UAAU,EAAC,QAAQ;UAACC,GAAG,EAAE,CAAE;UAAAN,QAAA,gBAC7ClD,OAAA,CAAClC,UAAU;YACT2F,IAAI,EAAC,OAAO;YACZC,KAAK,EAAC,SAAS;YACf,cAAW,eAAe;YAC1BC,OAAO,EAAEA,CAAA,KAAMtD,QAAQ,CAAC,GAAG,CAAE;YAC7BsC,EAAE,EAAE;cACFC,UAAU,EAAE,uBAAuB;cACnCI,cAAc,EAAE,YAAY;cAC5B,SAAS,EAAE;gBACTJ,UAAU,EAAE,uBAAuB;gBACnCgB,SAAS,EAAE;cACb,CAAC;cACDC,UAAU,EAAE;YACd,CAAE;YAAAX,QAAA,eAEFlD,OAAA,CAACtB,YAAY;cAACoF,QAAQ,EAAC;YAAO;cAAA1B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvB,CAAC,eACbvC,OAAA,CAAC/B,GAAG;YAAAiF,QAAA,gBACFlD,OAAA,CAACnC,UAAU;cACTkG,OAAO,EAAC,IAAI;cACZC,SAAS,EAAC,IAAI;cACdrB,EAAE,EAAE;gBACFsB,UAAU,EAAE,GAAG;gBACfC,MAAM,EAAE,SAAS;gBACjBZ,OAAO,EAAE;kBAAEa,EAAE,EAAE,MAAM;kBAAEC,EAAE,EAAE;gBAAQ,CAAC;gBACpCxB,UAAU,EAAE,0CAA0C;gBACtDyB,cAAc,EAAE,MAAM;gBACtBC,oBAAoB,EAAE,MAAM;gBAC5BC,mBAAmB,EAAE,aAAa;gBAClCC,UAAU,EAAE;cACd,CAAE;cACFb,OAAO,EAAEA,CAAA,KAAMtD,QAAQ,CAAC,GAAG,CAAE;cAAA6C,QAAA,EAC9B;YAED;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbvC,OAAA,CAACnC,UAAU;cACTkG,OAAO,EAAC,IAAI;cACZC,SAAS,EAAC,IAAI;cACdrB,EAAE,EAAE;gBACFsB,UAAU,EAAE,GAAG;gBACfC,MAAM,EAAE,SAAS;gBACjBZ,OAAO,EAAE;kBAAEa,EAAE,EAAE,OAAO;kBAAEC,EAAE,EAAE;gBAAO,CAAC;gBACpCV,KAAK,EAAE;cACT,CAAE;cACFC,OAAO,EAAEA,CAAA,KAAMtD,QAAQ,CAAC,GAAG,CAAE;cAAA6C,QAAA,EAC9B;YAED;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbvC,OAAA,CAACnC,UAAU;cACTkG,OAAO,EAAC,SAAS;cACjBpB,EAAE,EAAE;gBACFW,OAAO,EAAE;kBAAEa,EAAE,EAAE,MAAM;kBAAEC,EAAE,EAAE;gBAAQ,CAAC;gBACpCV,KAAK,EAAE,uBAAuB;gBAC9BO,UAAU,EAAE;cACd,CAAE;cAAAf,QAAA,EACH;YAED;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNvC,OAAA,CAAC/B,GAAG;UAACqF,OAAO,EAAC,MAAM;UAACC,UAAU,EAAC,QAAQ;UAACC,GAAG,EAAE,CAAE;UAAAN,QAAA,gBAE7ClD,OAAA,CAAC/B,GAAG;YAACqF,OAAO,EAAE;cAAEa,EAAE,EAAE,MAAM;cAAEC,EAAE,EAAE;YAAO,CAAE;YAACZ,GAAG,EAAE,CAAE;YAAAN,QAAA,EAC9ClB,eAAe,CAACyC,GAAG,CAAEC,IAAI,iBACxB1E,OAAA,CAAC9B,MAAM;cAELwF,KAAK,EAAC,SAAS;cACfiB,SAAS,EAAED,IAAI,CAACvC,IAAK;cACrBwB,OAAO,EAAEA,CAAA,KAAMtD,QAAQ,CAACqE,IAAI,CAACxC,IAAI,CAAE;cACnC6B,OAAO,EAAEW,IAAI,CAAClC,MAAM,GAAG,WAAW,GAAG,MAAO;cAC5CG,EAAE,EAAE;gBACFiC,YAAY,EAAE,CAAC;gBACfC,EAAE,EAAE,CAAC;gBACLxB,EAAE,EAAE,CAAC;gBACLY,UAAU,EAAE,GAAG;gBACfa,aAAa,EAAE,MAAM;gBACrBlC,UAAU,EAAE8B,IAAI,CAAClC,MAAM,GACnB,uBAAuB,GACvB,aAAa;gBACjBQ,cAAc,EAAE0B,IAAI,CAAClC,MAAM,GAAG,YAAY,GAAG,MAAM;gBACnDuC,MAAM,EAAEL,IAAI,CAAClC,MAAM,GAAG,iCAAiC,GAAG,MAAM;gBAChE,SAAS,EAAE;kBACTI,UAAU,EAAE,wBAAwB;kBACpCgB,SAAS,EAAE;gBACb,CAAC;gBACDC,UAAU,EAAE;cACd,CAAE;cAAAX,QAAA,EAEDwB,IAAI,CAACzC;YAAK,GAvBNyC,IAAI,CAACxC,IAAI;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAwBR,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGNvC,OAAA,CAAC/B,GAAG;YAACqF,OAAO,EAAE;cAAEa,EAAE,EAAE,MAAM;cAAEC,EAAE,EAAE;YAAO,CAAE;YAACZ,GAAG,EAAE,GAAI;YAAAN,QAAA,EAChDlB,eAAe,CAACyC,GAAG,CAAEC,IAAI,iBACxB1E,OAAA,CAAC1B,OAAO;cAAiB0G,KAAK,EAAEN,IAAI,CAACzC,KAAM;cAAAiB,QAAA,eACzClD,OAAA,CAAClC,UAAU;gBACT4F,KAAK,EAAC,SAAS;gBACfC,OAAO,EAAEA,CAAA,KAAMtD,QAAQ,CAACqE,IAAI,CAACxC,IAAI,CAAE;gBACnCS,EAAE,EAAE;kBACFC,UAAU,EAAE8B,IAAI,CAAClC,MAAM,GACnB,uBAAuB,GACvB,aAAa;kBACjBQ,cAAc,EAAE0B,IAAI,CAAClC,MAAM,GAAG,YAAY,GAAG,MAAM;kBACnDuC,MAAM,EAAEL,IAAI,CAAClC,MAAM,GAAG,iCAAiC,GAAG,MAAM;kBAChE,SAAS,EAAE;oBACTI,UAAU,EAAE;kBACd;gBACF,CAAE;gBAAAM,QAAA,EAEDwB,IAAI,CAACvC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA;YAAC,GAhBDmC,IAAI,CAACxC,IAAI;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAiBd,CACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGNvC,OAAA,CAAC1B,OAAO;YAAC0G,KAAK,EAAC,eAAe;YAAA9B,QAAA,eAC5BlD,OAAA,CAAClC,UAAU;cAAC4F,KAAK,EAAC,SAAS;cAAAR,QAAA,eACzBlD,OAAA,CAACxB,KAAK;gBAACyG,YAAY,EAAEtE,aAAa,CAACuE,MAAO;gBAACxB,KAAK,EAAC,OAAO;gBAAAR,QAAA,eACtDlD,OAAA,CAACR,iBAAiB;kBAAA4C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAGVvC,OAAA,CAAC1B,OAAO;YAAC0G,KAAK,EAAC,UAAU;YAAA9B,QAAA,eACvBlD,OAAA,CAAClC,UAAU;cACT4F,KAAK,EAAC,SAAS;cACfC,OAAO,EAAElC,mBAAoB;cAC7BkB,EAAE,EAAE;gBACFC,UAAU,EAAE,uBAAuB;gBACnC,SAAS,EAAE;kBACTA,UAAU,EAAE;gBACd;cACF,CAAE;cAAAM,QAAA,eAEFlD,OAAA,CAACN,YAAY;gBAAA0C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAEVvC,OAAA,CAAC7B,IAAI;YACHgH,QAAQ,EAAEpE,cAAe;YACzBqE,IAAI,EAAEC,OAAO,CAACtE,cAAc,CAAE;YAC9BuE,OAAO,EAAE5D,mBAAoB;YAC7B6D,YAAY,EAAE;cACZC,QAAQ,EAAE,QAAQ;cAClBC,UAAU,EAAE;YACd,CAAE;YACFC,eAAe,EAAE;cACfF,QAAQ,EAAE,KAAK;cACfC,UAAU,EAAE;YACd,CAAE;YACFE,UAAU,EAAE;cACVhD,EAAE,EAAE;gBACFiD,EAAE,EAAE,CAAC;gBACLhB,YAAY,EAAE,CAAC;gBACfiB,QAAQ,EAAE;cACZ;YACF,CAAE;YAAA3C,QAAA,gBAGFlD,OAAA,CAAC5B,QAAQ;cAACuF,OAAO,EAAEhC,wBAAyB;cAACgB,EAAE,EAAE;gBAAEa,GAAG,EAAE;cAAE,CAAE;cAAAN,QAAA,gBAC1DlD,OAAA,CAACN,YAAY;gBAAA0C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,sBAElB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eAGXvC,OAAA,CAAC5B,QAAQ;cAACuF,OAAO,EAAExC,mBAAoB;cAACwB,EAAE,EAAE;gBAAEa,GAAG,EAAE;cAAE,CAAE;cAAAN,QAAA,gBACrDlD,OAAA,CAAChB,YAAY;gBAAAoD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,YAElB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eAGXvC,OAAA,CAAC5B,QAAQ;cAACuE,EAAE,EAAE;gBAAEa,GAAG,EAAE;cAAE,CAAE;cAAAN,QAAA,GACtB3C,QAAQ,gBAAGP,OAAA,CAACpB,YAAY;gBAAAwD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAAGvC,OAAA,CAAClB,aAAa;gBAAAsD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAChDvC,OAAA,CAAChC,gBAAgB;gBACf8H,OAAO,eACL9F,OAAA,CAACjC,MAAM;kBACLgI,OAAO,EAAExF,QAAS;kBAClByF,QAAQ,EAAEvF,cAAe;kBACzBwF,IAAI,EAAC;gBAAO;kBAAA7D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb,CACF;gBACDN,KAAK,EAAC,WAAW;gBACjBU,EAAE,EAAE;kBAAEuD,MAAM,EAAE,CAAC;kBAAEC,IAAI,EAAE;gBAAE;cAAE;gBAAA/D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP,CAAC,eAGPvC,OAAA,CAAC7B,IAAI;YACHgH,QAAQ,EAAEvE,cAAe;YACzBwE,IAAI,EAAEC,OAAO,CAACzE,cAAc,CAAE;YAC9B0E,OAAO,EAAEhE,mBAAoB;YAC7BiE,YAAY,EAAE;cACZC,QAAQ,EAAE,QAAQ;cAClBC,UAAU,EAAE;YACd,CAAE;YACFC,eAAe,EAAE;cACfF,QAAQ,EAAE,KAAK;cACfC,UAAU,EAAE;YACd,CAAE;YACFE,UAAU,EAAE;cACVhD,EAAE,EAAE;gBACFiD,EAAE,EAAE,CAAC;gBACLhB,YAAY,EAAE;cAChB;YACF,CAAE;YAAA1B,QAAA,gBAEFlD,OAAA,CAAC5B,QAAQ;cACPuF,OAAO,EAAEA,CAAA,KAAMpC,oBAAoB,CAAC,IAAI,CAAE;cAC1C6E,QAAQ,EAAE5F,QAAQ,KAAK,IAAK;cAAA0C,QAAA,EAC7B;YAED;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACXvC,OAAA,CAAC5B,QAAQ;cACPuF,OAAO,EAAEA,CAAA,KAAMpC,oBAAoB,CAAC,IAAI,CAAE;cAC1C6E,QAAQ,EAAE5F,QAAQ,KAAK,IAAK;cAAA0C,QAAA,EAC7B;YAED;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP,CAAC,eAGPvC,OAAA,CAACzB,MAAM;YACLoE,EAAE,EAAE;cACF0D,KAAK,EAAE,EAAE;cACTC,MAAM,EAAE,EAAE;cACVC,OAAO,EAAE,uBAAuB;cAChCxB,MAAM,EAAE,iCAAiC;cACzCb,MAAM,EAAE,SAAS;cACjB,SAAS,EAAE;gBACTN,SAAS,EAAE;cACb,CAAC;cACDC,UAAU,EAAE;YACd,CAAE;YAAAX,QAAA,eAEFlD,OAAA,CAACtB,YAAY;cAAA0D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEb,CAAC;EAAA,QAnTkB5C,WAAW,EACXC,WAAW,EAC+CC,OAAO;AAAA,EAiTnF,CAAC;EAAA,QAnTiBF,WAAW,EACXC,WAAW,EAC+CC,OAAO;AAAA,EAiTlF;AAAC2G,GAAA,GApTGvG,MAAM;AAsTZA,MAAM,CAACwG,WAAW,GAAG,QAAQ;AAE7B,eAAexG,MAAM;AAAC,IAAAG,EAAA,EAAAoG,GAAA;AAAAE,YAAA,CAAAtG,EAAA;AAAAsG,YAAA,CAAAF,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}