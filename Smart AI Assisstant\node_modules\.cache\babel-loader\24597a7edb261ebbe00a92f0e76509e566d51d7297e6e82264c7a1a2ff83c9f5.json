{"ast": null, "code": "var _jsxFileName = \"E:\\\\PythonProjects\\\\React.js\\\\Smart AI Assisstant\\\\src\\\\components\\\\LinkScanner.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useCallback } from 'react';\nimport { Card, CardContent, Typography, TextField, Button, Box, CircularProgress, Alert, Chip, List, ListItem, ListItemIcon, ListItemText, Divider } from '@mui/material';\nimport { Link as LinkIcon, Security as SecurityIcon, Warning as WarningIcon, CheckCircle as CheckCircleIcon, Error as ErrorIcon, Search as SearchIcon } from '@mui/icons-material';\nimport { useScan } from '../contexts/ScanContext';\nimport { scanUrl } from '../services/security';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst LinkScanner = /*#__PURE__*/_s(/*#__PURE__*/React.memo(_c = _s(() => {\n  _s();\n  const [url, setUrl] = useState('');\n  const [isScanning, setIsScanning] = useState(false);\n  const [scanResult, setScanResult] = useState(null);\n  const [error, setError] = useState('');\n  const {\n    startScan,\n    completeScan,\n    addToHistory,\n    addNotification\n  } = useScan();\n\n  // URL validation\n  const isValidUrl = useCallback(string => {\n    try {\n      const urlObj = new URL(string);\n      return urlObj.protocol === 'http:' || urlObj.protocol === 'https:';\n    } catch (_) {\n      return false;\n    }\n  }, []);\n\n  // Optimized URL input change handler\n  const handleUrlChange = useCallback(event => {\n    const value = event.target.value;\n    setUrl(value);\n    // Debounce error clearing and result clearing for better performance\n    if (error) setError('');\n    if (scanResult) setScanResult(null);\n  }, [error, scanResult]);\n\n  // Handle scan submission\n  const handleScan = useCallback(async () => {\n    if (!url.trim()) {\n      setError('Please enter a URL to scan');\n      return;\n    }\n    if (!isValidUrl(url)) {\n      setError('Please enter a valid URL (must start with http:// or https://)');\n      return;\n    }\n    setIsScanning(true);\n    setError('');\n    setScanResult(null);\n    try {\n      // Start scan in context\n      startScan('url', url);\n\n      // Perform the actual scan\n      const result = await scanUrl(url);\n\n      // Complete scan in context\n      completeScan(result);\n\n      // Set local result\n      setScanResult(result);\n\n      // Add to history\n      const historyEntry = {\n        id: Date.now(),\n        type: 'url',\n        target: url,\n        result,\n        timestamp: new Date().toISOString()\n      };\n      addToHistory(historyEntry);\n\n      // Add notification\n      addNotification({\n        type: result.isSafe ? 'success' : 'warning',\n        title: 'URL Scan Complete',\n        message: `${url} has been scanned successfully`\n      });\n    } catch (err) {\n      const errorMessage = err.message || 'Failed to scan URL';\n      setError(errorMessage);\n      addNotification({\n        type: 'error',\n        title: 'Scan Failed',\n        message: errorMessage\n      });\n    } finally {\n      setIsScanning(false);\n    }\n  }, [url, isValidUrl, startScan, completeScan, addToHistory, addNotification]);\n\n  // Handle Enter key press\n  const handleKeyPress = useCallback(event => {\n    if (event.key === 'Enter' && !isScanning) {\n      handleScan();\n    }\n  }, [handleScan, isScanning]);\n  const getSafetyIcon = (isSafe, threatLevel) => {\n    if (isSafe) return /*#__PURE__*/_jsxDEV(CheckCircleIcon, {\n      color: \"success\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 124,\n      columnNumber: 24\n    }, this);\n    if (threatLevel === 'high' || threatLevel === 'critical') return /*#__PURE__*/_jsxDEV(ErrorIcon, {\n      color: \"error\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 125,\n      columnNumber: 70\n    }, this);\n    return /*#__PURE__*/_jsxDEV(WarningIcon, {\n      color: \"warning\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 126,\n      columnNumber: 12\n    }, this);\n  };\n  const getSafetyColor = (isSafe, threatLevel) => {\n    if (isSafe) return 'success';\n    if (threatLevel === 'high' || threatLevel === 'critical') return 'error';\n    return 'warning';\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    \"data-testid\": \"url-scanner\",\n    sx: {\n      background: theme => theme.palette.mode === 'dark' ? 'linear-gradient(135deg, rgba(30, 30, 30, 0.95) 0%, rgba(50, 50, 50, 0.95) 100%)' : 'linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%)',\n      backdropFilter: 'blur(20px)',\n      borderRadius: 4,\n      border: theme => `1px solid ${theme.palette.divider}`,\n      p: 4,\n      position: 'relative',\n      overflow: 'hidden',\n      '&::before': {\n        content: '\"\"',\n        position: 'absolute',\n        top: 0,\n        left: 0,\n        right: 0,\n        height: '4px',\n        background: 'linear-gradient(90deg, #667eea 0%, #764ba2 100%)'\n      }\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      display: \"flex\",\n      alignItems: \"center\",\n      justifyContent: \"center\",\n      mb: 4,\n      sx: {\n        textAlign: 'center'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n          borderRadius: '50%',\n          width: 60,\n          height: 60,\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          mr: 3,\n          boxShadow: '0 8px 25px rgba(102, 126, 234, 0.3)'\n        },\n        children: /*#__PURE__*/_jsxDEV(LinkIcon, {\n          sx: {\n            color: 'white',\n            fontSize: 28\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 182,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 169,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h4\",\n          component: \"h2\",\n          fontWeight: \"bold\",\n          gutterBottom: true,\n          children: \"URL Security Scanner\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 185,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body1\",\n          color: \"text.secondary\",\n          children: \"Advanced threat detection for web links and URLs\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 188,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 184,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 160,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      mb: 4,\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        fontWeight: \"600\",\n        sx: {\n          mb: 2\n        },\n        children: \"Enter URL for Analysis\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 196,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(TextField, {\n        fullWidth: true,\n        label: \"Website URL\",\n        placeholder: \"https://example.com\",\n        value: url,\n        onChange: handleUrlChange,\n        onKeyPress: handleKeyPress,\n        error: !!error,\n        helperText: error || 'Enter a complete URL including http:// or https://',\n        disabled: isScanning,\n        variant: \"outlined\",\n        autoComplete: \"url\",\n        spellCheck: false,\n        sx: {\n          '& .MuiOutlinedInput-root': {\n            borderRadius: 3,\n            fontSize: '1.1rem',\n            '&:hover': {\n              boxShadow: '0 4px 12px rgba(0,0,0,0.1)'\n            },\n            '&.Mui-focused': {\n              boxShadow: '0 4px 20px rgba(102, 126, 234, 0.2)'\n            }\n          },\n          '& .MuiInputLabel-root': {\n            fontSize: '1rem',\n            fontWeight: 500\n          }\n        },\n        InputProps: {\n          startAdornment: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              background: 'linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%)',\n              borderRadius: '50%',\n              width: 40,\n              height: 40,\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              mr: 2\n            },\n            children: /*#__PURE__*/_jsxDEV(LinkIcon, {\n              sx: {\n                color: 'primary.main',\n                fontSize: 20\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 242,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 230,\n            columnNumber: 15\n          }, this)\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 199,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 195,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      display: \"flex\",\n      justifyContent: \"center\",\n      mb: 4,\n      children: /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        onClick: handleScan,\n        disabled: isScanning || !url.trim(),\n        startIcon: isScanning ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n          size: 20,\n          color: \"inherit\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 255,\n          columnNumber: 35\n        }, this) : /*#__PURE__*/_jsxDEV(SearchIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 255,\n          columnNumber: 84\n        }, this),\n        size: \"large\",\n        sx: {\n          px: 6,\n          py: 2,\n          borderRadius: 3,\n          fontSize: '1.1rem',\n          fontWeight: 600,\n          minWidth: 200,\n          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n          boxShadow: '0 8px 25px rgba(102, 126, 234, 0.3)',\n          '&:hover': {\n            background: 'linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)',\n            transform: 'translateY(-2px)',\n            boxShadow: '0 12px 35px rgba(102, 126, 234, 0.4)'\n          },\n          '&:disabled': {\n            background: 'rgba(0,0,0,0.12)',\n            transform: 'none',\n            boxShadow: 'none'\n          }\n        },\n        children: isScanning ? 'Analyzing URL...' : 'Start Security Scan'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 251,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 250,\n      columnNumber: 7\n    }, this), scanResult && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        background: theme => theme.palette.mode === 'dark' ? 'rgba(30, 30, 30, 0.5)' : 'rgba(255, 255, 255, 0.7)',\n        backdropFilter: 'blur(10px)',\n        borderRadius: 3,\n        p: 4,\n        border: theme => `1px solid ${theme.palette.divider}`\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h5\",\n        gutterBottom: true,\n        fontWeight: \"bold\",\n        textAlign: \"center\",\n        mb: 3,\n        children: \"Scan Results\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 295,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Alert, {\n        severity: getSafetyColor(scanResult.isSafe, scanResult.threatLevel),\n        icon: getSafetyIcon(scanResult.isSafe, scanResult.threatLevel),\n        sx: {\n          mb: 3,\n          borderRadius: 2,\n          '& .MuiAlert-message': {\n            width: '100%'\n          }\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          fontWeight: \"bold\",\n          gutterBottom: true,\n          children: scanResult.isSafe ? 'URL appears safe' : 'Potential security risks detected'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 310,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body1\",\n          children: scanResult.summary || 'Scan completed successfully'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 313,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 299,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        display: \"flex\",\n        gap: 2,\n        mb: 4,\n        flexWrap: \"wrap\",\n        justifyContent: \"center\",\n        children: [/*#__PURE__*/_jsxDEV(Chip, {\n          label: `Reputation Score: ${scanResult.reputationScore || 'N/A'}`,\n          variant: \"outlined\",\n          size: \"medium\",\n          sx: {\n            fontWeight: 600,\n            fontSize: '0.9rem',\n            px: 2,\n            py: 1\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 319,\n          columnNumber: 13\n        }, this), scanResult.category && /*#__PURE__*/_jsxDEV(Chip, {\n          label: `Category: ${scanResult.category}`,\n          variant: \"outlined\",\n          size: \"medium\",\n          sx: {\n            fontWeight: 600,\n            fontSize: '0.9rem',\n            px: 2,\n            py: 1\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 331,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 318,\n        columnNumber: 11\n      }, this), scanResult.details && scanResult.details.length > 0 && /*#__PURE__*/_jsxDEV(Box, {\n        mb: 3,\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          fontWeight: \"600\",\n          sx: {\n            mb: 2\n          },\n          children: \"Detailed Analysis\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 347,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(List, {\n          sx: {\n            background: theme => theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.02)',\n            borderRadius: 2,\n            p: 1\n          },\n          children: scanResult.details.map((detail, index) => /*#__PURE__*/_jsxDEV(React.Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(ListItem, {\n              sx: {\n                py: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    background: 'linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%)',\n                    borderRadius: '50%',\n                    width: 40,\n                    height: 40,\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center'\n                  },\n                  children: /*#__PURE__*/_jsxDEV(SecurityIcon, {\n                    fontSize: \"small\",\n                    color: \"primary\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 374,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 363,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 362,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                primary: /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"subtitle1\",\n                  fontWeight: \"600\",\n                  children: detail.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 379,\n                  columnNumber: 27\n                }, this),\n                secondary: /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  sx: {\n                    mt: 0.5\n                  },\n                  children: detail.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 384,\n                  columnNumber: 27\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 377,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 361,\n              columnNumber: 21\n            }, this), index < scanResult.details.length - 1 && /*#__PURE__*/_jsxDEV(Divider, {\n              sx: {\n                mx: 2\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 391,\n              columnNumber: 23\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 360,\n            columnNumber: 19\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 350,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 346,\n        columnNumber: 13\n      }, this), scanResult.recommendations && scanResult.recommendations.length > 0 && /*#__PURE__*/_jsxDEV(Box, {\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          fontWeight: \"600\",\n          sx: {\n            mb: 2\n          },\n          children: \"Security Recommendations\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 401,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(List, {\n          sx: {\n            background: theme => theme.palette.mode === 'dark' ? 'rgba(76, 175, 80, 0.1)' : 'rgba(76, 175, 80, 0.05)',\n            borderRadius: 2,\n            p: 1,\n            border: theme => `1px solid ${theme.palette.success.light}`\n          },\n          children: scanResult.recommendations.map((rec, index) => /*#__PURE__*/_jsxDEV(ListItem, {\n            sx: {\n              py: 1.5\n            },\n            children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  background: 'rgba(76, 175, 80, 0.1)',\n                  borderRadius: '50%',\n                  width: 32,\n                  height: 32,\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center'\n                },\n                children: /*#__PURE__*/_jsxDEV(CheckCircleIcon, {\n                  fontSize: \"small\",\n                  color: \"success\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 428,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 417,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 416,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body1\",\n                fontWeight: \"500\",\n                children: rec\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 433,\n                columnNumber: 25\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 431,\n              columnNumber: 21\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 415,\n            columnNumber: 19\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 404,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 400,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 284,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 136,\n    columnNumber: 5\n  }, this);\n}, \"mwpAZDK30D1Ki6Bbtyphf7kFPQ4=\", false, function () {\n  return [useScan];\n})), \"mwpAZDK30D1Ki6Bbtyphf7kFPQ4=\", false, function () {\n  return [useScan];\n});\n_c2 = LinkScanner;\nLinkScanner.displayName = 'LinkScanner';\nexport default LinkScanner;\nvar _c, _c2;\n$RefreshReg$(_c, \"LinkScanner$React.memo\");\n$RefreshReg$(_c2, \"LinkScanner\");", "map": {"version": 3, "names": ["React", "useState", "useCallback", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Typography", "TextField", "<PERSON><PERSON>", "Box", "CircularProgress", "<PERSON><PERSON>", "Chip", "List", "ListItem", "ListItemIcon", "ListItemText", "Divider", "Link", "LinkIcon", "Security", "SecurityIcon", "Warning", "WarningIcon", "CheckCircle", "CheckCircleIcon", "Error", "ErrorIcon", "Search", "SearchIcon", "useScan", "scanUrl", "jsxDEV", "_jsxDEV", "LinkScanner", "_s", "memo", "_c", "url", "setUrl", "isScanning", "setIsScanning", "scanResult", "setScanResult", "error", "setError", "startScan", "completeScan", "addToHistory", "addNotification", "isValidUrl", "string", "url<PERSON>bj", "URL", "protocol", "_", "handleUrlChange", "event", "value", "target", "handleScan", "trim", "result", "historyEntry", "id", "Date", "now", "type", "timestamp", "toISOString", "isSafe", "title", "message", "err", "errorMessage", "handleKeyPress", "key", "getSafetyIcon", "threatLevel", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "getSafetyColor", "sx", "background", "theme", "palette", "mode", "<PERSON><PERSON>ilter", "borderRadius", "border", "divider", "p", "position", "overflow", "content", "top", "left", "right", "height", "children", "display", "alignItems", "justifyContent", "mb", "textAlign", "width", "mr", "boxShadow", "fontSize", "variant", "component", "fontWeight", "gutterBottom", "fullWidth", "label", "placeholder", "onChange", "onKeyPress", "helperText", "disabled", "autoComplete", "spell<PERSON>heck", "InputProps", "startAdornment", "onClick", "startIcon", "size", "px", "py", "min<PERSON><PERSON><PERSON>", "transform", "severity", "icon", "summary", "gap", "flexWrap", "reputationScore", "category", "details", "length", "map", "detail", "index", "Fragment", "primary", "secondary", "mt", "description", "mx", "recommendations", "success", "light", "rec", "_c2", "displayName", "$RefreshReg$"], "sources": ["E:/PythonProjects/React.js/Smart AI Assisstant/src/components/LinkScanner.jsx"], "sourcesContent": ["import React, { useState, useCallback } from 'react';\nimport {\n  Card,\n  CardContent,\n  Typography,\n  TextField,\n  Button,\n  Box,\n  CircularProgress,\n  Alert,\n  Chip,\n  List,\n  ListItem,\n  ListItemIcon,\n  ListItemText,\n  Divider,\n} from '@mui/material';\nimport {\n  Link as LinkIcon,\n  Security as SecurityIcon,\n  Warning as WarningIcon,\n  CheckCircle as CheckCircleIcon,\n  Error as ErrorIcon,\n  Search as SearchIcon,\n} from '@mui/icons-material';\nimport { useScan } from '../contexts/ScanContext';\nimport { scanUrl } from '../services/security';\n\nconst LinkScanner = React.memo(() => {\n  const [url, setUrl] = useState('');\n  const [isScanning, setIsScanning] = useState(false);\n  const [scanResult, setScanResult] = useState(null);\n  const [error, setError] = useState('');\n  \n  const { startScan, completeScan, addToHistory, addNotification } = useScan();\n\n  // URL validation\n  const isValidUrl = useCallback((string) => {\n    try {\n      const urlObj = new URL(string);\n      return urlObj.protocol === 'http:' || urlObj.protocol === 'https:';\n    } catch (_) {\n      return false;\n    }\n  }, []);\n\n  // Optimized URL input change handler\n  const handleUrlChange = useCallback((event) => {\n    const value = event.target.value;\n    setUrl(value);\n    // Debounce error clearing and result clearing for better performance\n    if (error) setError('');\n    if (scanResult) setScanResult(null);\n  }, [error, scanResult]);\n\n  // Handle scan submission\n  const handleScan = useCallback(async () => {\n    if (!url.trim()) {\n      setError('Please enter a URL to scan');\n      return;\n    }\n\n    if (!isValidUrl(url)) {\n      setError('Please enter a valid URL (must start with http:// or https://)');\n      return;\n    }\n\n    setIsScanning(true);\n    setError('');\n    setScanResult(null);\n\n    try {\n      // Start scan in context\n      startScan('url', url);\n\n      // Perform the actual scan\n      const result = await scanUrl(url);\n\n      // Complete scan in context\n      completeScan(result);\n\n      // Set local result\n      setScanResult(result);\n\n      // Add to history\n      const historyEntry = {\n        id: Date.now(),\n        type: 'url',\n        target: url,\n        result,\n        timestamp: new Date().toISOString(),\n      };\n      addToHistory(historyEntry);\n\n      // Add notification\n      addNotification({\n        type: result.isSafe ? 'success' : 'warning',\n        title: 'URL Scan Complete',\n        message: `${url} has been scanned successfully`,\n      });\n\n    } catch (err) {\n      const errorMessage = err.message || 'Failed to scan URL';\n      setError(errorMessage);\n      \n      addNotification({\n        type: 'error',\n        title: 'Scan Failed',\n        message: errorMessage,\n      });\n    } finally {\n      setIsScanning(false);\n    }\n  }, [url, isValidUrl, startScan, completeScan, addToHistory, addNotification]);\n\n  // Handle Enter key press\n  const handleKeyPress = useCallback((event) => {\n    if (event.key === 'Enter' && !isScanning) {\n      handleScan();\n    }\n  }, [handleScan, isScanning]);\n\n  const getSafetyIcon = (isSafe, threatLevel) => {\n    if (isSafe) return <CheckCircleIcon color=\"success\" />;\n    if (threatLevel === 'high' || threatLevel === 'critical') return <ErrorIcon color=\"error\" />;\n    return <WarningIcon color=\"warning\" />;\n  };\n\n  const getSafetyColor = (isSafe, threatLevel) => {\n    if (isSafe) return 'success';\n    if (threatLevel === 'high' || threatLevel === 'critical') return 'error';\n    return 'warning';\n  };\n\n  return (\n    <Box\n      data-testid=\"url-scanner\"\n      sx={{\n        background: (theme) => theme.palette.mode === 'dark'\n          ? 'linear-gradient(135deg, rgba(30, 30, 30, 0.95) 0%, rgba(50, 50, 50, 0.95) 100%)'\n          : 'linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%)',\n        backdropFilter: 'blur(20px)',\n        borderRadius: 4,\n        border: (theme) => `1px solid ${theme.palette.divider}`,\n        p: 4,\n        position: 'relative',\n        overflow: 'hidden',\n        '&::before': {\n          content: '\"\"',\n          position: 'absolute',\n          top: 0,\n          left: 0,\n          right: 0,\n          height: '4px',\n          background: 'linear-gradient(90deg, #667eea 0%, #764ba2 100%)',\n        },\n      }}\n    >\n      {/* Header Section */}\n      <Box\n        display=\"flex\"\n        alignItems=\"center\"\n        justifyContent=\"center\"\n        mb={4}\n        sx={{\n          textAlign: 'center',\n        }}\n      >\n        <Box\n          sx={{\n            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n            borderRadius: '50%',\n            width: 60,\n            height: 60,\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            mr: 3,\n            boxShadow: '0 8px 25px rgba(102, 126, 234, 0.3)',\n          }}\n        >\n          <LinkIcon sx={{ color: 'white', fontSize: 28 }} />\n        </Box>\n        <Box>\n          <Typography variant=\"h4\" component=\"h2\" fontWeight=\"bold\" gutterBottom>\n            URL Security Scanner\n          </Typography>\n          <Typography variant=\"body1\" color=\"text.secondary\">\n            Advanced threat detection for web links and URLs\n          </Typography>\n        </Box>\n      </Box>\n\n      {/* Input Section */}\n      <Box mb={4}>\n        <Typography variant=\"h6\" gutterBottom fontWeight=\"600\" sx={{ mb: 2 }}>\n          Enter URL for Analysis\n        </Typography>\n        <TextField\n          fullWidth\n          label=\"Website URL\"\n          placeholder=\"https://example.com\"\n          value={url}\n          onChange={handleUrlChange}\n          onKeyPress={handleKeyPress}\n          error={!!error}\n          helperText={error || 'Enter a complete URL including http:// or https://'}\n          disabled={isScanning}\n          variant=\"outlined\"\n          autoComplete=\"url\"\n          spellCheck={false}\n          sx={{\n            '& .MuiOutlinedInput-root': {\n              borderRadius: 3,\n              fontSize: '1.1rem',\n              '&:hover': {\n                boxShadow: '0 4px 12px rgba(0,0,0,0.1)',\n              },\n              '&.Mui-focused': {\n                boxShadow: '0 4px 20px rgba(102, 126, 234, 0.2)',\n              },\n            },\n            '& .MuiInputLabel-root': {\n              fontSize: '1rem',\n              fontWeight: 500,\n            },\n          }}\n          InputProps={{\n            startAdornment: (\n              <Box\n                sx={{\n                  background: 'linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%)',\n                  borderRadius: '50%',\n                  width: 40,\n                  height: 40,\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  mr: 2,\n                }}\n              >\n                <LinkIcon sx={{ color: 'primary.main', fontSize: 20 }} />\n              </Box>\n            ),\n          }}\n        />\n      </Box>\n\n      {/* Action Button */}\n      <Box display=\"flex\" justifyContent=\"center\" mb={4}>\n        <Button\n          variant=\"contained\"\n          onClick={handleScan}\n          disabled={isScanning || !url.trim()}\n          startIcon={isScanning ? <CircularProgress size={20} color=\"inherit\" /> : <SearchIcon />}\n          size=\"large\"\n          sx={{\n            px: 6,\n            py: 2,\n            borderRadius: 3,\n            fontSize: '1.1rem',\n            fontWeight: 600,\n            minWidth: 200,\n            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n            boxShadow: '0 8px 25px rgba(102, 126, 234, 0.3)',\n            '&:hover': {\n              background: 'linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)',\n              transform: 'translateY(-2px)',\n              boxShadow: '0 12px 35px rgba(102, 126, 234, 0.4)',\n            },\n            '&:disabled': {\n              background: 'rgba(0,0,0,0.12)',\n              transform: 'none',\n              boxShadow: 'none',\n            },\n          }}\n        >\n          {isScanning ? 'Analyzing URL...' : 'Start Security Scan'}\n        </Button>\n      </Box>\n\n      {/* Results Section */}\n      {scanResult && (\n        <Box\n          sx={{\n            background: (theme) => theme.palette.mode === 'dark'\n              ? 'rgba(30, 30, 30, 0.5)'\n              : 'rgba(255, 255, 255, 0.7)',\n            backdropFilter: 'blur(10px)',\n            borderRadius: 3,\n            p: 4,\n            border: (theme) => `1px solid ${theme.palette.divider}`,\n          }}\n        >\n          <Typography variant=\"h5\" gutterBottom fontWeight=\"bold\" textAlign=\"center\" mb={3}>\n            Scan Results\n          </Typography>\n\n          <Alert\n            severity={getSafetyColor(scanResult.isSafe, scanResult.threatLevel)}\n            icon={getSafetyIcon(scanResult.isSafe, scanResult.threatLevel)}\n            sx={{\n              mb: 3,\n              borderRadius: 2,\n              '& .MuiAlert-message': {\n                width: '100%',\n              },\n            }}\n          >\n            <Typography variant=\"h6\" fontWeight=\"bold\" gutterBottom>\n              {scanResult.isSafe ? 'URL appears safe' : 'Potential security risks detected'}\n            </Typography>\n            <Typography variant=\"body1\">\n              {scanResult.summary || 'Scan completed successfully'}\n            </Typography>\n          </Alert>\n\n          <Box display=\"flex\" gap={2} mb={4} flexWrap=\"wrap\" justifyContent=\"center\">\n            <Chip\n              label={`Reputation Score: ${scanResult.reputationScore || 'N/A'}`}\n              variant=\"outlined\"\n              size=\"medium\"\n              sx={{\n                fontWeight: 600,\n                fontSize: '0.9rem',\n                px: 2,\n                py: 1,\n              }}\n            />\n            {scanResult.category && (\n              <Chip\n                label={`Category: ${scanResult.category}`}\n                variant=\"outlined\"\n                size=\"medium\"\n                sx={{\n                  fontWeight: 600,\n                  fontSize: '0.9rem',\n                  px: 2,\n                  py: 1,\n                }}\n              />\n            )}\n          </Box>\n\n          {scanResult.details && scanResult.details.length > 0 && (\n            <Box mb={3}>\n              <Typography variant=\"h6\" gutterBottom fontWeight=\"600\" sx={{ mb: 2 }}>\n                Detailed Analysis\n              </Typography>\n              <List\n                sx={{\n                  background: (theme) => theme.palette.mode === 'dark'\n                    ? 'rgba(255, 255, 255, 0.05)'\n                    : 'rgba(0, 0, 0, 0.02)',\n                  borderRadius: 2,\n                  p: 1,\n                }}\n              >\n                {scanResult.details.map((detail, index) => (\n                  <React.Fragment key={index}>\n                    <ListItem sx={{ py: 2 }}>\n                      <ListItemIcon>\n                        <Box\n                          sx={{\n                            background: 'linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%)',\n                            borderRadius: '50%',\n                            width: 40,\n                            height: 40,\n                            display: 'flex',\n                            alignItems: 'center',\n                            justifyContent: 'center',\n                          }}\n                        >\n                          <SecurityIcon fontSize=\"small\" color=\"primary\" />\n                        </Box>\n                      </ListItemIcon>\n                      <ListItemText\n                        primary={\n                          <Typography variant=\"subtitle1\" fontWeight=\"600\">\n                            {detail.title}\n                          </Typography>\n                        }\n                        secondary={\n                          <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mt: 0.5 }}>\n                            {detail.description}\n                          </Typography>\n                        }\n                      />\n                    </ListItem>\n                    {index < scanResult.details.length - 1 && (\n                      <Divider sx={{ mx: 2 }} />\n                    )}\n                  </React.Fragment>\n                ))}\n              </List>\n            </Box>\n          )}\n\n          {scanResult.recommendations && scanResult.recommendations.length > 0 && (\n            <Box>\n              <Typography variant=\"h6\" gutterBottom fontWeight=\"600\" sx={{ mb: 2 }}>\n                Security Recommendations\n              </Typography>\n              <List\n                sx={{\n                  background: (theme) => theme.palette.mode === 'dark'\n                    ? 'rgba(76, 175, 80, 0.1)'\n                    : 'rgba(76, 175, 80, 0.05)',\n                  borderRadius: 2,\n                  p: 1,\n                  border: (theme) => `1px solid ${theme.palette.success.light}`,\n                }}\n              >\n                {scanResult.recommendations.map((rec, index) => (\n                  <ListItem key={index} sx={{ py: 1.5 }}>\n                    <ListItemIcon>\n                      <Box\n                        sx={{\n                          background: 'rgba(76, 175, 80, 0.1)',\n                          borderRadius: '50%',\n                          width: 32,\n                          height: 32,\n                          display: 'flex',\n                          alignItems: 'center',\n                          justifyContent: 'center',\n                        }}\n                      >\n                        <CheckCircleIcon fontSize=\"small\" color=\"success\" />\n                      </Box>\n                    </ListItemIcon>\n                    <ListItemText\n                      primary={\n                        <Typography variant=\"body1\" fontWeight=\"500\">\n                          {rec}\n                        </Typography>\n                      }\n                    />\n                  </ListItem>\n                ))}\n              </List>\n            </Box>\n          )}\n        </Box>\n      )}\n    </Box>\n  );\n});\n\nLinkScanner.displayName = 'LinkScanner';\n\nexport default LinkScanner;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,WAAW,QAAQ,OAAO;AACpD,SACEC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,SAAS,EACTC,MAAM,EACNC,GAAG,EACHC,gBAAgB,EAChBC,KAAK,EACLC,IAAI,EACJC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,YAAY,EACZC,OAAO,QACF,eAAe;AACtB,SACEC,IAAI,IAAIC,QAAQ,EAChBC,QAAQ,IAAIC,YAAY,EACxBC,OAAO,IAAIC,WAAW,EACtBC,WAAW,IAAIC,eAAe,EAC9BC,KAAK,IAAIC,SAAS,EAClBC,MAAM,IAAIC,UAAU,QACf,qBAAqB;AAC5B,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SAASC,OAAO,QAAQ,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/C,MAAMC,WAAW,gBAAAC,EAAA,cAAGlC,KAAK,CAACmC,IAAI,CAAAC,EAAA,GAAAF,EAAA,CAAC,MAAM;EAAAA,EAAA;EACnC,MAAM,CAACG,GAAG,EAAEC,MAAM,CAAC,GAAGrC,QAAQ,CAAC,EAAE,CAAC;EAClC,MAAM,CAACsC,UAAU,EAAEC,aAAa,CAAC,GAAGvC,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACwC,UAAU,EAAEC,aAAa,CAAC,GAAGzC,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAAC0C,KAAK,EAAEC,QAAQ,CAAC,GAAG3C,QAAQ,CAAC,EAAE,CAAC;EAEtC,MAAM;IAAE4C,SAAS;IAAEC,YAAY;IAAEC,YAAY;IAAEC;EAAgB,CAAC,GAAGnB,OAAO,CAAC,CAAC;;EAE5E;EACA,MAAMoB,UAAU,GAAG/C,WAAW,CAAEgD,MAAM,IAAK;IACzC,IAAI;MACF,MAAMC,MAAM,GAAG,IAAIC,GAAG,CAACF,MAAM,CAAC;MAC9B,OAAOC,MAAM,CAACE,QAAQ,KAAK,OAAO,IAAIF,MAAM,CAACE,QAAQ,KAAK,QAAQ;IACpE,CAAC,CAAC,OAAOC,CAAC,EAAE;MACV,OAAO,KAAK;IACd;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMC,eAAe,GAAGrD,WAAW,CAAEsD,KAAK,IAAK;IAC7C,MAAMC,KAAK,GAAGD,KAAK,CAACE,MAAM,CAACD,KAAK;IAChCnB,MAAM,CAACmB,KAAK,CAAC;IACb;IACA,IAAId,KAAK,EAAEC,QAAQ,CAAC,EAAE,CAAC;IACvB,IAAIH,UAAU,EAAEC,aAAa,CAAC,IAAI,CAAC;EACrC,CAAC,EAAE,CAACC,KAAK,EAAEF,UAAU,CAAC,CAAC;;EAEvB;EACA,MAAMkB,UAAU,GAAGzD,WAAW,CAAC,YAAY;IACzC,IAAI,CAACmC,GAAG,CAACuB,IAAI,CAAC,CAAC,EAAE;MACfhB,QAAQ,CAAC,4BAA4B,CAAC;MACtC;IACF;IAEA,IAAI,CAACK,UAAU,CAACZ,GAAG,CAAC,EAAE;MACpBO,QAAQ,CAAC,gEAAgE,CAAC;MAC1E;IACF;IAEAJ,aAAa,CAAC,IAAI,CAAC;IACnBI,QAAQ,CAAC,EAAE,CAAC;IACZF,aAAa,CAAC,IAAI,CAAC;IAEnB,IAAI;MACF;MACAG,SAAS,CAAC,KAAK,EAAER,GAAG,CAAC;;MAErB;MACA,MAAMwB,MAAM,GAAG,MAAM/B,OAAO,CAACO,GAAG,CAAC;;MAEjC;MACAS,YAAY,CAACe,MAAM,CAAC;;MAEpB;MACAnB,aAAa,CAACmB,MAAM,CAAC;;MAErB;MACA,MAAMC,YAAY,GAAG;QACnBC,EAAE,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC;QACdC,IAAI,EAAE,KAAK;QACXR,MAAM,EAAErB,GAAG;QACXwB,MAAM;QACNM,SAAS,EAAE,IAAIH,IAAI,CAAC,CAAC,CAACI,WAAW,CAAC;MACpC,CAAC;MACDrB,YAAY,CAACe,YAAY,CAAC;;MAE1B;MACAd,eAAe,CAAC;QACdkB,IAAI,EAAEL,MAAM,CAACQ,MAAM,GAAG,SAAS,GAAG,SAAS;QAC3CC,KAAK,EAAE,mBAAmB;QAC1BC,OAAO,EAAE,GAAGlC,GAAG;MACjB,CAAC,CAAC;IAEJ,CAAC,CAAC,OAAOmC,GAAG,EAAE;MACZ,MAAMC,YAAY,GAAGD,GAAG,CAACD,OAAO,IAAI,oBAAoB;MACxD3B,QAAQ,CAAC6B,YAAY,CAAC;MAEtBzB,eAAe,CAAC;QACdkB,IAAI,EAAE,OAAO;QACbI,KAAK,EAAE,aAAa;QACpBC,OAAO,EAAEE;MACX,CAAC,CAAC;IACJ,CAAC,SAAS;MACRjC,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC,EAAE,CAACH,GAAG,EAAEY,UAAU,EAAEJ,SAAS,EAAEC,YAAY,EAAEC,YAAY,EAAEC,eAAe,CAAC,CAAC;;EAE7E;EACA,MAAM0B,cAAc,GAAGxE,WAAW,CAAEsD,KAAK,IAAK;IAC5C,IAAIA,KAAK,CAACmB,GAAG,KAAK,OAAO,IAAI,CAACpC,UAAU,EAAE;MACxCoB,UAAU,CAAC,CAAC;IACd;EACF,CAAC,EAAE,CAACA,UAAU,EAAEpB,UAAU,CAAC,CAAC;EAE5B,MAAMqC,aAAa,GAAGA,CAACP,MAAM,EAAEQ,WAAW,KAAK;IAC7C,IAAIR,MAAM,EAAE,oBAAOrC,OAAA,CAACR,eAAe;MAACsD,KAAK,EAAC;IAAS;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtD,IAAIL,WAAW,KAAK,MAAM,IAAIA,WAAW,KAAK,UAAU,EAAE,oBAAO7C,OAAA,CAACN,SAAS;MAACoD,KAAK,EAAC;IAAO;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC5F,oBAAOlD,OAAA,CAACV,WAAW;MAACwD,KAAK,EAAC;IAAS;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACxC,CAAC;EAED,MAAMC,cAAc,GAAGA,CAACd,MAAM,EAAEQ,WAAW,KAAK;IAC9C,IAAIR,MAAM,EAAE,OAAO,SAAS;IAC5B,IAAIQ,WAAW,KAAK,MAAM,IAAIA,WAAW,KAAK,UAAU,EAAE,OAAO,OAAO;IACxE,OAAO,SAAS;EAClB,CAAC;EAED,oBACE7C,OAAA,CAACxB,GAAG;IACF,eAAY,aAAa;IACzB4E,EAAE,EAAE;MACFC,UAAU,EAAGC,KAAK,IAAKA,KAAK,CAACC,OAAO,CAACC,IAAI,KAAK,MAAM,GAChD,iFAAiF,GACjF,uFAAuF;MAC3FC,cAAc,EAAE,YAAY;MAC5BC,YAAY,EAAE,CAAC;MACfC,MAAM,EAAGL,KAAK,IAAK,aAAaA,KAAK,CAACC,OAAO,CAACK,OAAO,EAAE;MACvDC,CAAC,EAAE,CAAC;MACJC,QAAQ,EAAE,UAAU;MACpBC,QAAQ,EAAE,QAAQ;MAClB,WAAW,EAAE;QACXC,OAAO,EAAE,IAAI;QACbF,QAAQ,EAAE,UAAU;QACpBG,GAAG,EAAE,CAAC;QACNC,IAAI,EAAE,CAAC;QACPC,KAAK,EAAE,CAAC;QACRC,MAAM,EAAE,KAAK;QACbf,UAAU,EAAE;MACd;IACF,CAAE;IAAAgB,QAAA,gBAGFrE,OAAA,CAACxB,GAAG;MACF8F,OAAO,EAAC,MAAM;MACdC,UAAU,EAAC,QAAQ;MACnBC,cAAc,EAAC,QAAQ;MACvBC,EAAE,EAAE,CAAE;MACNrB,EAAE,EAAE;QACFsB,SAAS,EAAE;MACb,CAAE;MAAAL,QAAA,gBAEFrE,OAAA,CAACxB,GAAG;QACF4E,EAAE,EAAE;UACFC,UAAU,EAAE,mDAAmD;UAC/DK,YAAY,EAAE,KAAK;UACnBiB,KAAK,EAAE,EAAE;UACTP,MAAM,EAAE,EAAE;UACVE,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBC,cAAc,EAAE,QAAQ;UACxBI,EAAE,EAAE,CAAC;UACLC,SAAS,EAAE;QACb,CAAE;QAAAR,QAAA,eAEFrE,OAAA,CAACd,QAAQ;UAACkE,EAAE,EAAE;YAAEN,KAAK,EAAE,OAAO;YAAEgC,QAAQ,EAAE;UAAG;QAAE;UAAA/B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/C,CAAC,eACNlD,OAAA,CAACxB,GAAG;QAAA6F,QAAA,gBACFrE,OAAA,CAAC3B,UAAU;UAAC0G,OAAO,EAAC,IAAI;UAACC,SAAS,EAAC,IAAI;UAACC,UAAU,EAAC,MAAM;UAACC,YAAY;UAAAb,QAAA,EAAC;QAEvE;UAAAtB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACblD,OAAA,CAAC3B,UAAU;UAAC0G,OAAO,EAAC,OAAO;UAACjC,KAAK,EAAC,gBAAgB;UAAAuB,QAAA,EAAC;QAEnD;UAAAtB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNlD,OAAA,CAACxB,GAAG;MAACiG,EAAE,EAAE,CAAE;MAAAJ,QAAA,gBACTrE,OAAA,CAAC3B,UAAU;QAAC0G,OAAO,EAAC,IAAI;QAACG,YAAY;QAACD,UAAU,EAAC,KAAK;QAAC7B,EAAE,EAAE;UAAEqB,EAAE,EAAE;QAAE,CAAE;QAAAJ,QAAA,EAAC;MAEtE;QAAAtB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACblD,OAAA,CAAC1B,SAAS;QACR6G,SAAS;QACTC,KAAK,EAAC,aAAa;QACnBC,WAAW,EAAC,qBAAqB;QACjC5D,KAAK,EAAEpB,GAAI;QACXiF,QAAQ,EAAE/D,eAAgB;QAC1BgE,UAAU,EAAE7C,cAAe;QAC3B/B,KAAK,EAAE,CAAC,CAACA,KAAM;QACf6E,UAAU,EAAE7E,KAAK,IAAI,oDAAqD;QAC1E8E,QAAQ,EAAElF,UAAW;QACrBwE,OAAO,EAAC,UAAU;QAClBW,YAAY,EAAC,KAAK;QAClBC,UAAU,EAAE,KAAM;QAClBvC,EAAE,EAAE;UACF,0BAA0B,EAAE;YAC1BM,YAAY,EAAE,CAAC;YACfoB,QAAQ,EAAE,QAAQ;YAClB,SAAS,EAAE;cACTD,SAAS,EAAE;YACb,CAAC;YACD,eAAe,EAAE;cACfA,SAAS,EAAE;YACb;UACF,CAAC;UACD,uBAAuB,EAAE;YACvBC,QAAQ,EAAE,MAAM;YAChBG,UAAU,EAAE;UACd;QACF,CAAE;QACFW,UAAU,EAAE;UACVC,cAAc,eACZ7F,OAAA,CAACxB,GAAG;YACF4E,EAAE,EAAE;cACFC,UAAU,EAAE,oFAAoF;cAChGK,YAAY,EAAE,KAAK;cACnBiB,KAAK,EAAE,EAAE;cACTP,MAAM,EAAE,EAAE;cACVE,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpBC,cAAc,EAAE,QAAQ;cACxBI,EAAE,EAAE;YACN,CAAE;YAAAP,QAAA,eAEFrE,OAAA,CAACd,QAAQ;cAACkE,EAAE,EAAE;gBAAEN,KAAK,EAAE,cAAc;gBAAEgC,QAAQ,EAAE;cAAG;YAAE;cAAA/B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtD;QAET;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGNlD,OAAA,CAACxB,GAAG;MAAC8F,OAAO,EAAC,MAAM;MAACE,cAAc,EAAC,QAAQ;MAACC,EAAE,EAAE,CAAE;MAAAJ,QAAA,eAChDrE,OAAA,CAACzB,MAAM;QACLwG,OAAO,EAAC,WAAW;QACnBe,OAAO,EAAEnE,UAAW;QACpB8D,QAAQ,EAAElF,UAAU,IAAI,CAACF,GAAG,CAACuB,IAAI,CAAC,CAAE;QACpCmE,SAAS,EAAExF,UAAU,gBAAGP,OAAA,CAACvB,gBAAgB;UAACuH,IAAI,EAAE,EAAG;UAAClD,KAAK,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAAGlD,OAAA,CAACJ,UAAU;UAAAmD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACxF8C,IAAI,EAAC,OAAO;QACZ5C,EAAE,EAAE;UACF6C,EAAE,EAAE,CAAC;UACLC,EAAE,EAAE,CAAC;UACLxC,YAAY,EAAE,CAAC;UACfoB,QAAQ,EAAE,QAAQ;UAClBG,UAAU,EAAE,GAAG;UACfkB,QAAQ,EAAE,GAAG;UACb9C,UAAU,EAAE,mDAAmD;UAC/DwB,SAAS,EAAE,qCAAqC;UAChD,SAAS,EAAE;YACTxB,UAAU,EAAE,mDAAmD;YAC/D+C,SAAS,EAAE,kBAAkB;YAC7BvB,SAAS,EAAE;UACb,CAAC;UACD,YAAY,EAAE;YACZxB,UAAU,EAAE,kBAAkB;YAC9B+C,SAAS,EAAE,MAAM;YACjBvB,SAAS,EAAE;UACb;QACF,CAAE;QAAAR,QAAA,EAED9D,UAAU,GAAG,kBAAkB,GAAG;MAAqB;QAAAwC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,EAGLzC,UAAU,iBACTT,OAAA,CAACxB,GAAG;MACF4E,EAAE,EAAE;QACFC,UAAU,EAAGC,KAAK,IAAKA,KAAK,CAACC,OAAO,CAACC,IAAI,KAAK,MAAM,GAChD,uBAAuB,GACvB,0BAA0B;QAC9BC,cAAc,EAAE,YAAY;QAC5BC,YAAY,EAAE,CAAC;QACfG,CAAC,EAAE,CAAC;QACJF,MAAM,EAAGL,KAAK,IAAK,aAAaA,KAAK,CAACC,OAAO,CAACK,OAAO;MACvD,CAAE;MAAAS,QAAA,gBAEFrE,OAAA,CAAC3B,UAAU;QAAC0G,OAAO,EAAC,IAAI;QAACG,YAAY;QAACD,UAAU,EAAC,MAAM;QAACP,SAAS,EAAC,QAAQ;QAACD,EAAE,EAAE,CAAE;QAAAJ,QAAA,EAAC;MAElF;QAAAtB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAEblD,OAAA,CAACtB,KAAK;QACJ2H,QAAQ,EAAElD,cAAc,CAAC1C,UAAU,CAAC4B,MAAM,EAAE5B,UAAU,CAACoC,WAAW,CAAE;QACpEyD,IAAI,EAAE1D,aAAa,CAACnC,UAAU,CAAC4B,MAAM,EAAE5B,UAAU,CAACoC,WAAW,CAAE;QAC/DO,EAAE,EAAE;UACFqB,EAAE,EAAE,CAAC;UACLf,YAAY,EAAE,CAAC;UACf,qBAAqB,EAAE;YACrBiB,KAAK,EAAE;UACT;QACF,CAAE;QAAAN,QAAA,gBAEFrE,OAAA,CAAC3B,UAAU;UAAC0G,OAAO,EAAC,IAAI;UAACE,UAAU,EAAC,MAAM;UAACC,YAAY;UAAAb,QAAA,EACpD5D,UAAU,CAAC4B,MAAM,GAAG,kBAAkB,GAAG;QAAmC;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnE,CAAC,eACblD,OAAA,CAAC3B,UAAU;UAAC0G,OAAO,EAAC,OAAO;UAAAV,QAAA,EACxB5D,UAAU,CAAC8F,OAAO,IAAI;QAA6B;UAAAxD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC,eAERlD,OAAA,CAACxB,GAAG;QAAC8F,OAAO,EAAC,MAAM;QAACkC,GAAG,EAAE,CAAE;QAAC/B,EAAE,EAAE,CAAE;QAACgC,QAAQ,EAAC,MAAM;QAACjC,cAAc,EAAC,QAAQ;QAAAH,QAAA,gBACxErE,OAAA,CAACrB,IAAI;UACHyG,KAAK,EAAE,qBAAqB3E,UAAU,CAACiG,eAAe,IAAI,KAAK,EAAG;UAClE3B,OAAO,EAAC,UAAU;UAClBiB,IAAI,EAAC,QAAQ;UACb5C,EAAE,EAAE;YACF6B,UAAU,EAAE,GAAG;YACfH,QAAQ,EAAE,QAAQ;YAClBmB,EAAE,EAAE,CAAC;YACLC,EAAE,EAAE;UACN;QAAE;UAAAnD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EACDzC,UAAU,CAACkG,QAAQ,iBAClB3G,OAAA,CAACrB,IAAI;UACHyG,KAAK,EAAE,aAAa3E,UAAU,CAACkG,QAAQ,EAAG;UAC1C5B,OAAO,EAAC,UAAU;UAClBiB,IAAI,EAAC,QAAQ;UACb5C,EAAE,EAAE;YACF6B,UAAU,EAAE,GAAG;YACfH,QAAQ,EAAE,QAAQ;YAClBmB,EAAE,EAAE,CAAC;YACLC,EAAE,EAAE;UACN;QAAE;UAAAnD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACF;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,EAELzC,UAAU,CAACmG,OAAO,IAAInG,UAAU,CAACmG,OAAO,CAACC,MAAM,GAAG,CAAC,iBAClD7G,OAAA,CAACxB,GAAG;QAACiG,EAAE,EAAE,CAAE;QAAAJ,QAAA,gBACTrE,OAAA,CAAC3B,UAAU;UAAC0G,OAAO,EAAC,IAAI;UAACG,YAAY;UAACD,UAAU,EAAC,KAAK;UAAC7B,EAAE,EAAE;YAAEqB,EAAE,EAAE;UAAE,CAAE;UAAAJ,QAAA,EAAC;QAEtE;UAAAtB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACblD,OAAA,CAACpB,IAAI;UACHwE,EAAE,EAAE;YACFC,UAAU,EAAGC,KAAK,IAAKA,KAAK,CAACC,OAAO,CAACC,IAAI,KAAK,MAAM,GAChD,2BAA2B,GAC3B,qBAAqB;YACzBE,YAAY,EAAE,CAAC;YACfG,CAAC,EAAE;UACL,CAAE;UAAAQ,QAAA,EAED5D,UAAU,CAACmG,OAAO,CAACE,GAAG,CAAC,CAACC,MAAM,EAAEC,KAAK,kBACpChH,OAAA,CAAChC,KAAK,CAACiJ,QAAQ;YAAA5C,QAAA,gBACbrE,OAAA,CAACnB,QAAQ;cAACuE,EAAE,EAAE;gBAAE8C,EAAE,EAAE;cAAE,CAAE;cAAA7B,QAAA,gBACtBrE,OAAA,CAAClB,YAAY;gBAAAuF,QAAA,eACXrE,OAAA,CAACxB,GAAG;kBACF4E,EAAE,EAAE;oBACFC,UAAU,EAAE,oFAAoF;oBAChGK,YAAY,EAAE,KAAK;oBACnBiB,KAAK,EAAE,EAAE;oBACTP,MAAM,EAAE,EAAE;oBACVE,OAAO,EAAE,MAAM;oBACfC,UAAU,EAAE,QAAQ;oBACpBC,cAAc,EAAE;kBAClB,CAAE;kBAAAH,QAAA,eAEFrE,OAAA,CAACZ,YAAY;oBAAC0F,QAAQ,EAAC,OAAO;oBAAChC,KAAK,EAAC;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9C;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACM,CAAC,eACflD,OAAA,CAACjB,YAAY;gBACXmI,OAAO,eACLlH,OAAA,CAAC3B,UAAU;kBAAC0G,OAAO,EAAC,WAAW;kBAACE,UAAU,EAAC,KAAK;kBAAAZ,QAAA,EAC7C0C,MAAM,CAACzE;gBAAK;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACb;gBACDiE,SAAS,eACPnH,OAAA,CAAC3B,UAAU;kBAAC0G,OAAO,EAAC,OAAO;kBAACjC,KAAK,EAAC,gBAAgB;kBAACM,EAAE,EAAE;oBAAEgE,EAAE,EAAE;kBAAI,CAAE;kBAAA/C,QAAA,EAChE0C,MAAM,CAACM;gBAAW;kBAAAtE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT;cACb;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CAAC,EACV8D,KAAK,GAAGvG,UAAU,CAACmG,OAAO,CAACC,MAAM,GAAG,CAAC,iBACpC7G,OAAA,CAAChB,OAAO;cAACoE,EAAE,EAAE;gBAAEkE,EAAE,EAAE;cAAE;YAAE;cAAAvE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAC1B;UAAA,GAhCkB8D,KAAK;YAAAjE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAiCV,CACjB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CACN,EAEAzC,UAAU,CAAC8G,eAAe,IAAI9G,UAAU,CAAC8G,eAAe,CAACV,MAAM,GAAG,CAAC,iBAClE7G,OAAA,CAACxB,GAAG;QAAA6F,QAAA,gBACFrE,OAAA,CAAC3B,UAAU;UAAC0G,OAAO,EAAC,IAAI;UAACG,YAAY;UAACD,UAAU,EAAC,KAAK;UAAC7B,EAAE,EAAE;YAAEqB,EAAE,EAAE;UAAE,CAAE;UAAAJ,QAAA,EAAC;QAEtE;UAAAtB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACblD,OAAA,CAACpB,IAAI;UACHwE,EAAE,EAAE;YACFC,UAAU,EAAGC,KAAK,IAAKA,KAAK,CAACC,OAAO,CAACC,IAAI,KAAK,MAAM,GAChD,wBAAwB,GACxB,yBAAyB;YAC7BE,YAAY,EAAE,CAAC;YACfG,CAAC,EAAE,CAAC;YACJF,MAAM,EAAGL,KAAK,IAAK,aAAaA,KAAK,CAACC,OAAO,CAACiE,OAAO,CAACC,KAAK;UAC7D,CAAE;UAAApD,QAAA,EAED5D,UAAU,CAAC8G,eAAe,CAACT,GAAG,CAAC,CAACY,GAAG,EAAEV,KAAK,kBACzChH,OAAA,CAACnB,QAAQ;YAAauE,EAAE,EAAE;cAAE8C,EAAE,EAAE;YAAI,CAAE;YAAA7B,QAAA,gBACpCrE,OAAA,CAAClB,YAAY;cAAAuF,QAAA,eACXrE,OAAA,CAACxB,GAAG;gBACF4E,EAAE,EAAE;kBACFC,UAAU,EAAE,wBAAwB;kBACpCK,YAAY,EAAE,KAAK;kBACnBiB,KAAK,EAAE,EAAE;kBACTP,MAAM,EAAE,EAAE;kBACVE,OAAO,EAAE,MAAM;kBACfC,UAAU,EAAE,QAAQ;kBACpBC,cAAc,EAAE;gBAClB,CAAE;gBAAAH,QAAA,eAEFrE,OAAA,CAACR,eAAe;kBAACsF,QAAQ,EAAC,OAAO;kBAAChC,KAAK,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CAAC,eACflD,OAAA,CAACjB,YAAY;cACXmI,OAAO,eACLlH,OAAA,CAAC3B,UAAU;gBAAC0G,OAAO,EAAC,OAAO;gBAACE,UAAU,EAAC,KAAK;gBAAAZ,QAAA,EACzCqD;cAAG;gBAAA3E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACM;YACb;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA,GAtBW8D,KAAK;YAAAjE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAuBV,CACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;EAAA,QA5ZoErD,OAAO;AAAA,EA4Z3E,CAAC;EAAA,QA5ZmEA,OAAO;AAAA,EA4Z1E;AAAC8H,GAAA,GAlaG1H,WAAW;AAoajBA,WAAW,CAAC2H,WAAW,GAAG,aAAa;AAEvC,eAAe3H,WAAW;AAAC,IAAAG,EAAA,EAAAuH,GAAA;AAAAE,YAAA,CAAAzH,EAAA;AAAAyH,YAAA,CAAAF,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}