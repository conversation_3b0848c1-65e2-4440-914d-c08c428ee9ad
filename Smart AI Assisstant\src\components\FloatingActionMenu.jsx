import React, { useState } from 'react';
import {
  Fab,
  SpeedDial,
  SpeedDialAction,
  SpeedDialIcon,
  Tooltip,
  Box,
} from '@mui/material';
import {
  Add as AddIcon,
  Security as SecurityIcon,
  Link as LinkIcon,
  Upload as UploadIcon,
  History as HistoryIcon,
  Analytics as AnalyticsIcon,
  AutoAwesome as AutoIcon,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';

const FloatingActionMenu = React.memo(() => {
  const [open, setOpen] = useState(false);
  const navigate = useNavigate();

  const actions = [
    {
      icon: <LinkIcon />,
      name: 'Quick URL Scan',
      action: () => {
        navigate('/');
        // Scroll to scanner section
        setTimeout(() => {
          const scanner = document.querySelector('[data-testid="url-scanner"]');
          if (scanner) {
            scanner.scrollIntoView({ behavior: 'smooth', block: 'center' });
          }
        }, 100);
      },
    },
    {
      icon: <UploadIcon />,
      name: 'Quick File Scan',
      action: () => {
        navigate('/');
        // Scroll to file scanner and trigger file input
        setTimeout(() => {
          const scanner = document.querySelector('[data-testid="file-scanner"]');
          if (scanner) {
            scanner.scrollIntoView({ behavior: 'smooth', block: 'center' });
            // Try to trigger the file input
            setTimeout(() => {
              const fileInput = scanner.querySelector('input[type="file"]');
              if (fileInput) {
                fileInput.click();
              }
            }, 500);
          }
        }, 100);
      },
    },
    {
      icon: <HistoryIcon />,
      name: 'View History',
      action: () => navigate('/history'),
    },
    {
      icon: <AnalyticsIcon />,
      name: 'Smart Features',
      action: () => navigate('/smart-features'),
    },
    {
      icon: <AutoIcon />,
      name: 'Auto Scan',
      action: () => {
        navigate('/');
        // Scroll to auto scan section
        setTimeout(() => {
          const autoScan = document.querySelector('[data-testid="auto-scan"]');
          if (autoScan) {
            autoScan.scrollIntoView({ behavior: 'smooth', block: 'center' });
          }
        }, 100);
      },
    },
  ];

  return (
    <Box
      sx={{
        position: 'fixed',
        bottom: 24,
        right: 24,
        zIndex: 1300,
      }}
    >
      <SpeedDial
        ariaLabel="Quick Actions"
        sx={{
          '& .MuiFab-primary': {
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            boxShadow: '0 8px 25px rgba(102, 126, 234, 0.4)',
            '&:hover': {
              background: 'linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)',
              boxShadow: '0 12px 35px rgba(102, 126, 234, 0.5)',
              transform: 'scale(1.1)',
            },
          },
          '& .MuiSpeedDial-actions': {
            paddingBottom: 0,
          },
        }}
        icon={<SpeedDialIcon />}
        onClose={() => setOpen(false)}
        onOpen={() => setOpen(true)}
        open={open}
        direction="up"
      >
        {actions.map((action) => (
          <SpeedDialAction
            key={action.name}
            icon={action.icon}
            tooltipTitle={action.name}
            tooltipOpen
            onClick={() => {
              setOpen(false);
              action.action();
            }}
            sx={{
              '& .MuiFab-primary': {
                background: 'linear-gradient(135deg, rgba(12, 12, 12, 0.95) 0%, rgba(26, 26, 26, 0.95) 100%)',
                backdropFilter: 'blur(25px)',
                border: '1px solid rgba(255, 255, 255, 0.1)',
                color: 'white',
                '&:hover': {
                  background: 'linear-gradient(135deg, rgba(102, 126, 234, 0.2) 0%, rgba(118, 75, 162, 0.2) 100%)',
                  transform: 'scale(1.1)',
                },
              },
            }}
          />
        ))}
      </SpeedDial>
    </Box>
  );
});

FloatingActionMenu.displayName = 'FloatingActionMenu';

export default FloatingActionMenu;
