{"ast": null, "code": "var _jsxFileName = \"E:\\\\PythonProjects\\\\React.js\\\\Smart AI Assisstant\\\\src\\\\components\\\\ThreatPrediction.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Card, CardContent, Typography, Grid, LinearProgress, Chip, List, ListItem, ListItemIcon, ListItemText, Avatar, IconButton, Tooltip, Alert, Accordion, AccordionSummary, AccordionDetails } from '@mui/material';\nimport { Psychology as AIIcon, TrendingUp as TrendingUpIcon, Warning as WarningIcon, Security as SecurityIcon, Timeline as TimelineIcon, Insights as InsightsIcon, ExpandMore as ExpandMoreIcon, AutoGraph as GraphIcon, Radar as RadarIcon, Shield as ShieldIcon } from '@mui/icons-material';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ThreatPrediction = /*#__PURE__*/_s(/*#__PURE__*/React.memo(_c = _s(() => {\n  _s();\n  const [predictions, setPredictions] = useState([{\n    type: 'Phishing Campaign',\n    probability: 87,\n    timeframe: '24-48 hours',\n    severity: 'high',\n    indicators: ['Increased suspicious email patterns', 'Domain registration spikes in target sectors', 'Social engineering attempts rising 45%'],\n    recommendation: 'Implement enhanced email filtering and user awareness training',\n    confidence: 92\n  }, {\n    type: 'Malware Distribution',\n    probability: 73,\n    timeframe: '3-5 days',\n    severity: 'medium',\n    indicators: ['Unusual file download patterns', 'Suspicious network traffic from known bad IPs', 'Increased vulnerability scanning attempts'],\n    recommendation: 'Update endpoint protection and monitor file execution',\n    confidence: 85\n  }, {\n    type: 'DDoS Attack',\n    probability: 45,\n    timeframe: '1-2 weeks',\n    severity: 'medium',\n    indicators: ['Botnet activity increase in target regions', 'Infrastructure reconnaissance detected', 'Historical attack pattern analysis'],\n    recommendation: 'Prepare DDoS mitigation strategies and backup systems',\n    confidence: 78\n  }]);\n  const [aiAnalysis, setAiAnalysis] = useState({\n    overallRisk: 68,\n    trendDirection: 'increasing',\n    keyFactors: ['Global cyber activity up 23%', 'New vulnerability disclosures', 'Seasonal attack patterns', 'Geopolitical tensions'],\n    modelAccuracy: 94.7,\n    lastUpdated: new Date()\n  });\n  const [threatTrends, setThreatTrends] = useState([{\n    category: 'Ransomware',\n    trend: 15,\n    direction: 'up'\n  }, {\n    category: 'Phishing',\n    trend: 8,\n    direction: 'down'\n  }, {\n    category: 'Malware',\n    trend: 22,\n    direction: 'up'\n  }, {\n    category: 'Data Breach',\n    trend: -5,\n    direction: 'down'\n  }, {\n    category: 'Social Engineering',\n    trend: 18,\n    direction: 'up'\n  }]);\n\n  // Simulate real-time AI updates\n  useEffect(() => {\n    const interval = setInterval(() => {\n      setAiAnalysis(prev => ({\n        ...prev,\n        overallRisk: Math.max(20, Math.min(95, prev.overallRisk + (Math.random() - 0.5) * 5)),\n        modelAccuracy: Math.max(90, Math.min(99, prev.modelAccuracy + (Math.random() - 0.5) * 0.5)),\n        lastUpdated: new Date()\n      }));\n    }, 10000);\n    return () => clearInterval(interval);\n  }, []);\n  const getSeverityColor = severity => {\n    switch (severity) {\n      case 'high':\n        return 'error';\n      case 'medium':\n        return 'warning';\n      case 'low':\n        return 'success';\n      default:\n        return 'default';\n    }\n  };\n  const getTrendColor = direction => {\n    switch (direction) {\n      case 'up':\n        return 'error';\n      case 'down':\n        return 'success';\n      case 'stable':\n        return 'info';\n      default:\n        return 'default';\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      p: 3\n    },\n    children: [/*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        mb: 4,\n        background: theme => theme.palette.mode === 'dark' ? 'linear-gradient(135deg, rgba(12, 12, 12, 0.95) 0%, rgba(26, 26, 26, 0.95) 50%, rgba(45, 45, 45, 0.95) 100%)' : 'linear-gradient(135deg, rgba(248, 250, 252, 0.95) 0%, rgba(241, 245, 249, 0.95) 50%, rgba(226, 232, 240, 0.95) 100%)',\n        backdropFilter: 'blur(25px) saturate(180%)',\n        border: '1px solid rgba(156, 39, 176, 0.2)',\n        borderRadius: 4,\n        position: 'relative',\n        overflow: 'hidden',\n        boxShadow: '0 8px 32px rgba(156, 39, 176, 0.2)',\n        transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',\n        '&:hover': {\n          transform: 'translateY(-2px)',\n          boxShadow: '0 16px 48px rgba(156, 39, 176, 0.3)'\n        },\n        '&::before': {\n          content: '\"\"',\n          position: 'absolute',\n          top: 0,\n          left: 0,\n          right: 0,\n          height: '4px',\n          background: 'linear-gradient(90deg, #667eea 0%, #764ba2 50%, #f093fb 100%)',\n          backgroundSize: '200% 200%',\n          animation: 'gradientShift 3s ease infinite'\n        },\n        '@keyframes gradientShift': {\n          '0%': {\n            backgroundPosition: '0% 50%'\n          },\n          '50%': {\n            backgroundPosition: '100% 50%'\n          },\n          '100%': {\n            backgroundPosition: '0% 50%'\n          }\n        }\n      },\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          display: \"flex\",\n          alignItems: \"center\",\n          gap: 2,\n          mb: 3,\n          children: [/*#__PURE__*/_jsxDEV(Avatar, {\n            sx: {\n              background: 'linear-gradient(135deg, #9c27b0 0%, #e91e63 100%)',\n              width: 56,\n              height: 56\n            },\n            children: /*#__PURE__*/_jsxDEV(AIIcon, {\n              sx: {\n                fontSize: 28\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 178,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 171,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            flex: 1,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h4\",\n              fontWeight: \"bold\",\n              gutterBottom: true,\n              children: \"AI Threat Prediction Engine\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 181,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              color: \"text.secondary\",\n              children: \"Advanced machine learning analysis of global threat patterns\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 184,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 180,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            textAlign: \"right\",\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h3\",\n              fontWeight: \"bold\",\n              color: \"secondary\",\n              children: [aiAnalysis.overallRisk, \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 189,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: \"Risk Level\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 192,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 188,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 170,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 3,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                gutterBottom: true,\n                children: \"Model Performance\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 201,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                display: \"flex\",\n                alignItems: \"center\",\n                gap: 2,\n                mb: 2,\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  children: \"Accuracy:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 205,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(LinearProgress, {\n                  variant: \"determinate\",\n                  value: aiAnalysis.modelAccuracy,\n                  sx: {\n                    flex: 1,\n                    height: 8,\n                    borderRadius: 4\n                  },\n                  color: \"secondary\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 206,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  fontWeight: \"bold\",\n                  children: [aiAnalysis.modelAccuracy.toFixed(1), \"%\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 212,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 204,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                color: \"text.secondary\",\n                children: [\"Last updated: \", aiAnalysis.lastUpdated.toLocaleTimeString()]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 216,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 200,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 199,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              gutterBottom: true,\n              children: \"Key Risk Factors\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 222,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(List, {\n              dense: true,\n              children: aiAnalysis.keyFactors.map((factor, index) => /*#__PURE__*/_jsxDEV(ListItem, {\n                sx: {\n                  px: 0,\n                  py: 0.5\n                },\n                children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                  sx: {\n                    minWidth: 32\n                  },\n                  children: /*#__PURE__*/_jsxDEV(InsightsIcon, {\n                    fontSize: \"small\",\n                    color: \"secondary\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 229,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 228,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    children: factor\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 233,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 231,\n                  columnNumber: 21\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 227,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 225,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 221,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 198,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 169,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 134,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        lg: 8,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          sx: {\n            background: theme => theme.palette.mode === 'dark' ? 'linear-gradient(135deg, rgba(30, 30, 30, 0.95) 0%, rgba(50, 50, 50, 0.95) 100%)' : 'linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%)',\n            backdropFilter: 'blur(20px)',\n            border: theme => `1px solid ${theme.palette.divider}`\n          },\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              display: \"flex\",\n              alignItems: \"center\",\n              gap: 2,\n              mb: 3,\n              children: [/*#__PURE__*/_jsxDEV(Avatar, {\n                sx: {\n                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                  width: 48,\n                  height: 48\n                },\n                children: /*#__PURE__*/_jsxDEV(RadarIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 267,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 260,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h5\",\n                fontWeight: \"bold\",\n                children: \"Predicted Threats\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 269,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 259,\n              columnNumber: 15\n            }, this), predictions.map((prediction, index) => /*#__PURE__*/_jsxDEV(Accordion, {\n              sx: {\n                mb: 2,\n                background: theme => theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.02)',\n                '&:before': {\n                  display: 'none'\n                }\n              },\n              children: [/*#__PURE__*/_jsxDEV(AccordionSummary, {\n                expandIcon: /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 285,\n                  columnNumber: 49\n                }, this),\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  display: \"flex\",\n                  alignItems: \"center\",\n                  gap: 2,\n                  width: \"100%\",\n                  children: [/*#__PURE__*/_jsxDEV(Avatar, {\n                    sx: {\n                      background: prediction.severity === 'high' ? 'linear-gradient(135deg, #f44336 0%, #ff5722 100%)' : prediction.severity === 'medium' ? 'linear-gradient(135deg, #ff9800 0%, #ffc107 100%)' : 'linear-gradient(135deg, #4caf50 0%, #8bc34a 100%)',\n                      width: 40,\n                      height: 40\n                    },\n                    children: /*#__PURE__*/_jsxDEV(WarningIcon, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 298,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 287,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    flex: 1,\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"h6\",\n                      fontWeight: \"600\",\n                      children: prediction.type\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 301,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Box, {\n                      display: \"flex\",\n                      alignItems: \"center\",\n                      gap: 2,\n                      mt: 1,\n                      children: [/*#__PURE__*/_jsxDEV(Chip, {\n                        label: `${prediction.probability}% probability`,\n                        size: \"small\",\n                        color: prediction.probability > 80 ? 'error' : 'warning',\n                        variant: \"outlined\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 305,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                        label: prediction.timeframe,\n                        size: \"small\",\n                        variant: \"outlined\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 311,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                        label: prediction.severity,\n                        size: \"small\",\n                        color: getSeverityColor(prediction.severity)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 316,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 304,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 300,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"h4\",\n                    fontWeight: \"bold\",\n                    color: \"primary\",\n                    children: [prediction.probability, \"%\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 323,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 286,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 285,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(AccordionDetails, {\n                children: /*#__PURE__*/_jsxDEV(Grid, {\n                  container: true,\n                  spacing: 3,\n                  children: [/*#__PURE__*/_jsxDEV(Grid, {\n                    item: true,\n                    xs: 12,\n                    md: 6,\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"subtitle2\",\n                      gutterBottom: true,\n                      fontWeight: \"600\",\n                      children: \"Threat Indicators\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 331,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(List, {\n                      dense: true,\n                      children: prediction.indicators.map((indicator, idx) => /*#__PURE__*/_jsxDEV(ListItem, {\n                        sx: {\n                          px: 0\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                          sx: {\n                            minWidth: 32\n                          },\n                          children: /*#__PURE__*/_jsxDEV(SecurityIcon, {\n                            fontSize: \"small\",\n                            color: \"warning\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 338,\n                            columnNumber: 33\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 337,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                          primary: /*#__PURE__*/_jsxDEV(Typography, {\n                            variant: \"body2\",\n                            children: indicator\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 342,\n                            columnNumber: 35\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 340,\n                          columnNumber: 31\n                        }, this)]\n                      }, idx, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 336,\n                        columnNumber: 29\n                      }, this))\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 334,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 330,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                    item: true,\n                    xs: 12,\n                    md: 6,\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"subtitle2\",\n                      gutterBottom: true,\n                      fontWeight: \"600\",\n                      children: \"Recommended Actions\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 352,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Alert, {\n                      severity: getSeverityColor(prediction.severity),\n                      sx: {\n                        mb: 2\n                      },\n                      children: prediction.recommendation\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 355,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Box, {\n                      display: \"flex\",\n                      alignItems: \"center\",\n                      gap: 1,\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        color: \"text.secondary\",\n                        children: \"AI Confidence:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 362,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                        label: `${prediction.confidence}%`,\n                        size: \"small\",\n                        color: prediction.confidence > 90 ? 'success' : 'warning',\n                        variant: \"filled\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 365,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 361,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 351,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 329,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 328,\n                columnNumber: 19\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 275,\n              columnNumber: 17\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 258,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 249,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 248,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        lg: 4,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          sx: {\n            background: theme => theme.palette.mode === 'dark' ? 'linear-gradient(135deg, rgba(30, 30, 30, 0.95) 0%, rgba(50, 50, 50, 0.95) 100%)' : 'linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%)',\n            backdropFilter: 'blur(20px)',\n            border: theme => `1px solid ${theme.palette.divider}`,\n            height: 'fit-content'\n          },\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              display: \"flex\",\n              alignItems: \"center\",\n              gap: 2,\n              mb: 3,\n              children: [/*#__PURE__*/_jsxDEV(Avatar, {\n                sx: {\n                  background: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',\n                  width: 48,\n                  height: 48\n                },\n                children: /*#__PURE__*/_jsxDEV(GraphIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 402,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 395,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                fontWeight: \"bold\",\n                children: \"Threat Trends\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 404,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 394,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(List, {\n              children: threatTrends.map((trend, index) => /*#__PURE__*/_jsxDEV(ListItem, {\n                sx: {\n                  px: 0\n                },\n                children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: /*#__PURE__*/_jsxDEV(Box, {\n                    display: \"flex\",\n                    alignItems: \"center\",\n                    justifyContent: \"space-between\",\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      fontWeight: \"600\",\n                      children: trend.category\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 415,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Box, {\n                      display: \"flex\",\n                      alignItems: \"center\",\n                      gap: 1,\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"body2\",\n                        color: getTrendColor(trend.direction),\n                        fontWeight: \"bold\",\n                        children: [trend.trend > 0 ? '+' : '', trend.trend, \"%\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 419,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(TrendingUpIcon, {\n                        fontSize: \"small\",\n                        color: getTrendColor(trend.direction),\n                        sx: {\n                          transform: trend.direction === 'down' ? 'rotate(180deg)' : 'none'\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 426,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 418,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 414,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 412,\n                  columnNumber: 21\n                }, this)\n              }, index, false, {\n                fileName: _jsxFileName,\n                lineNumber: 411,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 409,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 393,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 383,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 382,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 246,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 132,\n    columnNumber: 5\n  }, this);\n}, \"ATLGVI/VIB9R608noi2nBDo5iuE=\")), \"ATLGVI/VIB9R608noi2nBDo5iuE=\");\n_c2 = ThreatPrediction;\nThreatPrediction.displayName = 'ThreatPrediction';\nexport default ThreatPrediction;\nvar _c, _c2;\n$RefreshReg$(_c, \"ThreatPrediction$React.memo\");\n$RefreshReg$(_c2, \"ThreatPrediction\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Typography", "Grid", "LinearProgress", "Chip", "List", "ListItem", "ListItemIcon", "ListItemText", "Avatar", "IconButton", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Accordion", "AccordionSummary", "AccordionDetails", "Psychology", "AIIcon", "TrendingUp", "TrendingUpIcon", "Warning", "WarningIcon", "Security", "SecurityIcon", "Timeline", "TimelineIcon", "Insights", "InsightsIcon", "ExpandMore", "ExpandMoreIcon", "AutoGraph", "GraphIcon", "Radar", "RadarIcon", "Shield", "ShieldIcon", "jsxDEV", "_jsxDEV", "ThreatPrediction", "_s", "memo", "_c", "predictions", "setPredictions", "type", "probability", "timeframe", "severity", "indicators", "recommendation", "confidence", "aiAnalysis", "setAiAnalysis", "overallRisk", "trendDirection", "keyFactors", "modelAccuracy", "lastUpdated", "Date", "threatTrends", "setThreatTrends", "category", "trend", "direction", "interval", "setInterval", "prev", "Math", "max", "min", "random", "clearInterval", "getSeverityColor", "getTrendColor", "sx", "p", "children", "mb", "background", "theme", "palette", "mode", "<PERSON><PERSON>ilter", "border", "borderRadius", "position", "overflow", "boxShadow", "transition", "transform", "content", "top", "left", "right", "height", "backgroundSize", "animation", "backgroundPosition", "display", "alignItems", "gap", "width", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "flex", "variant", "fontWeight", "gutterBottom", "color", "textAlign", "container", "spacing", "item", "xs", "md", "value", "toFixed", "toLocaleTimeString", "dense", "map", "factor", "index", "px", "py", "min<PERSON><PERSON><PERSON>", "primary", "lg", "divider", "prediction", "expandIcon", "mt", "label", "size", "indicator", "idx", "justifyContent", "_c2", "displayName", "$RefreshReg$"], "sources": ["E:/PythonProjects/React.js/Smart AI Assisstant/src/components/ThreatPrediction.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  <PERSON>,\n  Card,\n  CardContent,\n  Typography,\n  Grid,\n  LinearProgress,\n  Chip,\n  List,\n  ListItem,\n  ListItemIcon,\n  ListItemText,\n  Avatar,\n  IconButton,\n  Tooltip,\n  Alert,\n  Accordion,\n  AccordionSummary,\n  AccordionDetails,\n} from '@mui/material';\nimport {\n  Psychology as AIIcon,\n  TrendingUp as TrendingUpIcon,\n  Warning as WarningIcon,\n  Security as SecurityIcon,\n  Timeline as TimelineIcon,\n  Insights as InsightsIcon,\n  ExpandMore as ExpandMoreIcon,\n  AutoGraph as GraphIcon,\n  Radar as RadarIcon,\n  Shield as ShieldIcon,\n} from '@mui/icons-material';\n\nconst ThreatPrediction = React.memo(() => {\n  const [predictions, setPredictions] = useState([\n    {\n      type: 'Phishing Campaign',\n      probability: 87,\n      timeframe: '24-48 hours',\n      severity: 'high',\n      indicators: [\n        'Increased suspicious email patterns',\n        'Domain registration spikes in target sectors',\n        'Social engineering attempts rising 45%',\n      ],\n      recommendation: 'Implement enhanced email filtering and user awareness training',\n      confidence: 92,\n    },\n    {\n      type: 'Malware Distribution',\n      probability: 73,\n      timeframe: '3-5 days',\n      severity: 'medium',\n      indicators: [\n        'Unusual file download patterns',\n        'Suspicious network traffic from known bad IPs',\n        'Increased vulnerability scanning attempts',\n      ],\n      recommendation: 'Update endpoint protection and monitor file execution',\n      confidence: 85,\n    },\n    {\n      type: 'DDoS Attack',\n      probability: 45,\n      timeframe: '1-2 weeks',\n      severity: 'medium',\n      indicators: [\n        'Botnet activity increase in target regions',\n        'Infrastructure reconnaissance detected',\n        'Historical attack pattern analysis',\n      ],\n      recommendation: 'Prepare DDoS mitigation strategies and backup systems',\n      confidence: 78,\n    },\n  ]);\n\n  const [aiAnalysis, setAiAnalysis] = useState({\n    overallRisk: 68,\n    trendDirection: 'increasing',\n    keyFactors: [\n      'Global cyber activity up 23%',\n      'New vulnerability disclosures',\n      'Seasonal attack patterns',\n      'Geopolitical tensions',\n    ],\n    modelAccuracy: 94.7,\n    lastUpdated: new Date(),\n  });\n\n  const [threatTrends, setThreatTrends] = useState([\n    { category: 'Ransomware', trend: 15, direction: 'up' },\n    { category: 'Phishing', trend: 8, direction: 'down' },\n    { category: 'Malware', trend: 22, direction: 'up' },\n    { category: 'Data Breach', trend: -5, direction: 'down' },\n    { category: 'Social Engineering', trend: 18, direction: 'up' },\n  ]);\n\n  // Simulate real-time AI updates\n  useEffect(() => {\n    const interval = setInterval(() => {\n      setAiAnalysis(prev => ({\n        ...prev,\n        overallRisk: Math.max(20, Math.min(95, prev.overallRisk + (Math.random() - 0.5) * 5)),\n        modelAccuracy: Math.max(90, Math.min(99, prev.modelAccuracy + (Math.random() - 0.5) * 0.5)),\n        lastUpdated: new Date(),\n      }));\n    }, 10000);\n\n    return () => clearInterval(interval);\n  }, []);\n\n  const getSeverityColor = (severity) => {\n    switch (severity) {\n      case 'high': return 'error';\n      case 'medium': return 'warning';\n      case 'low': return 'success';\n      default: return 'default';\n    }\n  };\n\n  const getTrendColor = (direction) => {\n    switch (direction) {\n      case 'up': return 'error';\n      case 'down': return 'success';\n      case 'stable': return 'info';\n      default: return 'default';\n    }\n  };\n\n  return (\n    <Box sx={{ p: 3 }}>\n      {/* AI Analysis Overview - Quantum Analytics */}\n      <Card\n        sx={{\n          mb: 4,\n          background: (theme) => theme.palette.mode === 'dark'\n            ? 'linear-gradient(135deg, rgba(12, 12, 12, 0.95) 0%, rgba(26, 26, 26, 0.95) 50%, rgba(45, 45, 45, 0.95) 100%)'\n            : 'linear-gradient(135deg, rgba(248, 250, 252, 0.95) 0%, rgba(241, 245, 249, 0.95) 50%, rgba(226, 232, 240, 0.95) 100%)',\n          backdropFilter: 'blur(25px) saturate(180%)',\n          border: '1px solid rgba(156, 39, 176, 0.2)',\n          borderRadius: 4,\n          position: 'relative',\n          overflow: 'hidden',\n          boxShadow: '0 8px 32px rgba(156, 39, 176, 0.2)',\n          transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',\n          '&:hover': {\n            transform: 'translateY(-2px)',\n            boxShadow: '0 16px 48px rgba(156, 39, 176, 0.3)',\n          },\n          '&::before': {\n            content: '\"\"',\n            position: 'absolute',\n            top: 0,\n            left: 0,\n            right: 0,\n            height: '4px',\n            background: 'linear-gradient(90deg, #667eea 0%, #764ba2 50%, #f093fb 100%)',\n            backgroundSize: '200% 200%',\n            animation: 'gradientShift 3s ease infinite',\n          },\n          '@keyframes gradientShift': {\n            '0%': { backgroundPosition: '0% 50%' },\n            '50%': { backgroundPosition: '100% 50%' },\n            '100%': { backgroundPosition: '0% 50%' },\n          },\n        }}\n      >\n        <CardContent>\n          <Box display=\"flex\" alignItems=\"center\" gap={2} mb={3}>\n            <Avatar\n              sx={{\n                background: 'linear-gradient(135deg, #9c27b0 0%, #e91e63 100%)',\n                width: 56,\n                height: 56,\n              }}\n            >\n              <AIIcon sx={{ fontSize: 28 }} />\n            </Avatar>\n            <Box flex={1}>\n              <Typography variant=\"h4\" fontWeight=\"bold\" gutterBottom>\n                AI Threat Prediction Engine\n              </Typography>\n              <Typography variant=\"body1\" color=\"text.secondary\">\n                Advanced machine learning analysis of global threat patterns\n              </Typography>\n            </Box>\n            <Box textAlign=\"right\">\n              <Typography variant=\"h3\" fontWeight=\"bold\" color=\"secondary\">\n                {aiAnalysis.overallRisk}%\n              </Typography>\n              <Typography variant=\"body2\" color=\"text.secondary\">\n                Risk Level\n              </Typography>\n            </Box>\n          </Box>\n\n          <Grid container spacing={3}>\n            <Grid item xs={12} md={6}>\n              <Box>\n                <Typography variant=\"h6\" gutterBottom>\n                  Model Performance\n                </Typography>\n                <Box display=\"flex\" alignItems=\"center\" gap={2} mb={2}>\n                  <Typography variant=\"body2\">Accuracy:</Typography>\n                  <LinearProgress\n                    variant=\"determinate\"\n                    value={aiAnalysis.modelAccuracy}\n                    sx={{ flex: 1, height: 8, borderRadius: 4 }}\n                    color=\"secondary\"\n                  />\n                  <Typography variant=\"body2\" fontWeight=\"bold\">\n                    {aiAnalysis.modelAccuracy.toFixed(1)}%\n                  </Typography>\n                </Box>\n                <Typography variant=\"caption\" color=\"text.secondary\">\n                  Last updated: {aiAnalysis.lastUpdated.toLocaleTimeString()}\n                </Typography>\n              </Box>\n            </Grid>\n            <Grid item xs={12} md={6}>\n              <Typography variant=\"h6\" gutterBottom>\n                Key Risk Factors\n              </Typography>\n              <List dense>\n                {aiAnalysis.keyFactors.map((factor, index) => (\n                  <ListItem key={index} sx={{ px: 0, py: 0.5 }}>\n                    <ListItemIcon sx={{ minWidth: 32 }}>\n                      <InsightsIcon fontSize=\"small\" color=\"secondary\" />\n                    </ListItemIcon>\n                    <ListItemText\n                      primary={\n                        <Typography variant=\"body2\">\n                          {factor}\n                        </Typography>\n                      }\n                    />\n                  </ListItem>\n                ))}\n              </List>\n            </Grid>\n          </Grid>\n        </CardContent>\n      </Card>\n\n      <Grid container spacing={3}>\n        {/* Threat Predictions */}\n        <Grid item xs={12} lg={8}>\n          <Card\n            sx={{\n              background: (theme) => theme.palette.mode === 'dark'\n                ? 'linear-gradient(135deg, rgba(30, 30, 30, 0.95) 0%, rgba(50, 50, 50, 0.95) 100%)'\n                : 'linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%)',\n              backdropFilter: 'blur(20px)',\n              border: (theme) => `1px solid ${theme.palette.divider}`,\n            }}\n          >\n            <CardContent>\n              <Box display=\"flex\" alignItems=\"center\" gap={2} mb={3}>\n                <Avatar\n                  sx={{\n                    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                    width: 48,\n                    height: 48,\n                  }}\n                >\n                  <RadarIcon />\n                </Avatar>\n                <Typography variant=\"h5\" fontWeight=\"bold\">\n                  Predicted Threats\n                </Typography>\n              </Box>\n\n              {predictions.map((prediction, index) => (\n                <Accordion\n                  key={index}\n                  sx={{\n                    mb: 2,\n                    background: (theme) => theme.palette.mode === 'dark'\n                      ? 'rgba(255, 255, 255, 0.05)'\n                      : 'rgba(0, 0, 0, 0.02)',\n                    '&:before': { display: 'none' },\n                  }}\n                >\n                  <AccordionSummary expandIcon={<ExpandMoreIcon />}>\n                    <Box display=\"flex\" alignItems=\"center\" gap={2} width=\"100%\">\n                      <Avatar\n                        sx={{\n                          background: prediction.severity === 'high' \n                            ? 'linear-gradient(135deg, #f44336 0%, #ff5722 100%)'\n                            : prediction.severity === 'medium'\n                            ? 'linear-gradient(135deg, #ff9800 0%, #ffc107 100%)'\n                            : 'linear-gradient(135deg, #4caf50 0%, #8bc34a 100%)',\n                          width: 40,\n                          height: 40,\n                        }}\n                      >\n                        <WarningIcon />\n                      </Avatar>\n                      <Box flex={1}>\n                        <Typography variant=\"h6\" fontWeight=\"600\">\n                          {prediction.type}\n                        </Typography>\n                        <Box display=\"flex\" alignItems=\"center\" gap={2} mt={1}>\n                          <Chip\n                            label={`${prediction.probability}% probability`}\n                            size=\"small\"\n                            color={prediction.probability > 80 ? 'error' : 'warning'}\n                            variant=\"outlined\"\n                          />\n                          <Chip\n                            label={prediction.timeframe}\n                            size=\"small\"\n                            variant=\"outlined\"\n                          />\n                          <Chip\n                            label={prediction.severity}\n                            size=\"small\"\n                            color={getSeverityColor(prediction.severity)}\n                          />\n                        </Box>\n                      </Box>\n                      <Typography variant=\"h4\" fontWeight=\"bold\" color=\"primary\">\n                        {prediction.probability}%\n                      </Typography>\n                    </Box>\n                  </AccordionSummary>\n                  <AccordionDetails>\n                    <Grid container spacing={3}>\n                      <Grid item xs={12} md={6}>\n                        <Typography variant=\"subtitle2\" gutterBottom fontWeight=\"600\">\n                          Threat Indicators\n                        </Typography>\n                        <List dense>\n                          {prediction.indicators.map((indicator, idx) => (\n                            <ListItem key={idx} sx={{ px: 0 }}>\n                              <ListItemIcon sx={{ minWidth: 32 }}>\n                                <SecurityIcon fontSize=\"small\" color=\"warning\" />\n                              </ListItemIcon>\n                              <ListItemText\n                                primary={\n                                  <Typography variant=\"body2\">\n                                    {indicator}\n                                  </Typography>\n                                }\n                              />\n                            </ListItem>\n                          ))}\n                        </List>\n                      </Grid>\n                      <Grid item xs={12} md={6}>\n                        <Typography variant=\"subtitle2\" gutterBottom fontWeight=\"600\">\n                          Recommended Actions\n                        </Typography>\n                        <Alert\n                          severity={getSeverityColor(prediction.severity)}\n                          sx={{ mb: 2 }}\n                        >\n                          {prediction.recommendation}\n                        </Alert>\n                        <Box display=\"flex\" alignItems=\"center\" gap={1}>\n                          <Typography variant=\"caption\" color=\"text.secondary\">\n                            AI Confidence:\n                          </Typography>\n                          <Chip\n                            label={`${prediction.confidence}%`}\n                            size=\"small\"\n                            color={prediction.confidence > 90 ? 'success' : 'warning'}\n                            variant=\"filled\"\n                          />\n                        </Box>\n                      </Grid>\n                    </Grid>\n                  </AccordionDetails>\n                </Accordion>\n              ))}\n            </CardContent>\n          </Card>\n        </Grid>\n\n        {/* Threat Trends */}\n        <Grid item xs={12} lg={4}>\n          <Card\n            sx={{\n              background: (theme) => theme.palette.mode === 'dark'\n                ? 'linear-gradient(135deg, rgba(30, 30, 30, 0.95) 0%, rgba(50, 50, 50, 0.95) 100%)'\n                : 'linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%)',\n              backdropFilter: 'blur(20px)',\n              border: (theme) => `1px solid ${theme.palette.divider}`,\n              height: 'fit-content',\n            }}\n          >\n            <CardContent>\n              <Box display=\"flex\" alignItems=\"center\" gap={2} mb={3}>\n                <Avatar\n                  sx={{\n                    background: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',\n                    width: 48,\n                    height: 48,\n                  }}\n                >\n                  <GraphIcon />\n                </Avatar>\n                <Typography variant=\"h6\" fontWeight=\"bold\">\n                  Threat Trends\n                </Typography>\n              </Box>\n\n              <List>\n                {threatTrends.map((trend, index) => (\n                  <ListItem key={index} sx={{ px: 0 }}>\n                    <ListItemText\n                      primary={\n                        <Box display=\"flex\" alignItems=\"center\" justifyContent=\"space-between\">\n                          <Typography variant=\"body2\" fontWeight=\"600\">\n                            {trend.category}\n                          </Typography>\n                          <Box display=\"flex\" alignItems=\"center\" gap={1}>\n                            <Typography\n                              variant=\"body2\"\n                              color={getTrendColor(trend.direction)}\n                              fontWeight=\"bold\"\n                            >\n                              {trend.trend > 0 ? '+' : ''}{trend.trend}%\n                            </Typography>\n                            <TrendingUpIcon\n                              fontSize=\"small\"\n                              color={getTrendColor(trend.direction)}\n                              sx={{\n                                transform: trend.direction === 'down' ? 'rotate(180deg)' : 'none',\n                              }}\n                            />\n                          </Box>\n                        </Box>\n                      }\n                    />\n                  </ListItem>\n                ))}\n              </List>\n            </CardContent>\n          </Card>\n        </Grid>\n      </Grid>\n    </Box>\n  );\n});\n\nThreatPrediction.displayName = 'ThreatPrediction';\n\nexport default ThreatPrediction;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,IAAI,EACJC,cAAc,EACdC,IAAI,EACJC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,YAAY,EACZC,MAAM,EACNC,UAAU,EACVC,OAAO,EACPC,KAAK,EACLC,SAAS,EACTC,gBAAgB,EAChBC,gBAAgB,QACX,eAAe;AACtB,SACEC,UAAU,IAAIC,MAAM,EACpBC,UAAU,IAAIC,cAAc,EAC5BC,OAAO,IAAIC,WAAW,EACtBC,QAAQ,IAAIC,YAAY,EACxBC,QAAQ,IAAIC,YAAY,EACxBC,QAAQ,IAAIC,YAAY,EACxBC,UAAU,IAAIC,cAAc,EAC5BC,SAAS,IAAIC,SAAS,EACtBC,KAAK,IAAIC,SAAS,EAClBC,MAAM,IAAIC,UAAU,QACf,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7B,MAAMC,gBAAgB,gBAAAC,EAAA,cAAG5C,KAAK,CAAC6C,IAAI,CAAAC,EAAA,GAAAF,EAAA,CAAC,MAAM;EAAAA,EAAA;EACxC,MAAM,CAACG,WAAW,EAAEC,cAAc,CAAC,GAAG/C,QAAQ,CAAC,CAC7C;IACEgD,IAAI,EAAE,mBAAmB;IACzBC,WAAW,EAAE,EAAE;IACfC,SAAS,EAAE,aAAa;IACxBC,QAAQ,EAAE,MAAM;IAChBC,UAAU,EAAE,CACV,qCAAqC,EACrC,8CAA8C,EAC9C,wCAAwC,CACzC;IACDC,cAAc,EAAE,gEAAgE;IAChFC,UAAU,EAAE;EACd,CAAC,EACD;IACEN,IAAI,EAAE,sBAAsB;IAC5BC,WAAW,EAAE,EAAE;IACfC,SAAS,EAAE,UAAU;IACrBC,QAAQ,EAAE,QAAQ;IAClBC,UAAU,EAAE,CACV,gCAAgC,EAChC,+CAA+C,EAC/C,2CAA2C,CAC5C;IACDC,cAAc,EAAE,uDAAuD;IACvEC,UAAU,EAAE;EACd,CAAC,EACD;IACEN,IAAI,EAAE,aAAa;IACnBC,WAAW,EAAE,EAAE;IACfC,SAAS,EAAE,WAAW;IACtBC,QAAQ,EAAE,QAAQ;IAClBC,UAAU,EAAE,CACV,4CAA4C,EAC5C,wCAAwC,EACxC,oCAAoC,CACrC;IACDC,cAAc,EAAE,uDAAuD;IACvEC,UAAU,EAAE;EACd,CAAC,CACF,CAAC;EAEF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGxD,QAAQ,CAAC;IAC3CyD,WAAW,EAAE,EAAE;IACfC,cAAc,EAAE,YAAY;IAC5BC,UAAU,EAAE,CACV,8BAA8B,EAC9B,+BAA+B,EAC/B,0BAA0B,EAC1B,uBAAuB,CACxB;IACDC,aAAa,EAAE,IAAI;IACnBC,WAAW,EAAE,IAAIC,IAAI,CAAC;EACxB,CAAC,CAAC;EAEF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGhE,QAAQ,CAAC,CAC/C;IAAEiE,QAAQ,EAAE,YAAY;IAAEC,KAAK,EAAE,EAAE;IAAEC,SAAS,EAAE;EAAK,CAAC,EACtD;IAAEF,QAAQ,EAAE,UAAU;IAAEC,KAAK,EAAE,CAAC;IAAEC,SAAS,EAAE;EAAO,CAAC,EACrD;IAAEF,QAAQ,EAAE,SAAS;IAAEC,KAAK,EAAE,EAAE;IAAEC,SAAS,EAAE;EAAK,CAAC,EACnD;IAAEF,QAAQ,EAAE,aAAa;IAAEC,KAAK,EAAE,CAAC,CAAC;IAAEC,SAAS,EAAE;EAAO,CAAC,EACzD;IAAEF,QAAQ,EAAE,oBAAoB;IAAEC,KAAK,EAAE,EAAE;IAAEC,SAAS,EAAE;EAAK,CAAC,CAC/D,CAAC;;EAEF;EACAlE,SAAS,CAAC,MAAM;IACd,MAAMmE,QAAQ,GAAGC,WAAW,CAAC,MAAM;MACjCb,aAAa,CAACc,IAAI,KAAK;QACrB,GAAGA,IAAI;QACPb,WAAW,EAAEc,IAAI,CAACC,GAAG,CAAC,EAAE,EAAED,IAAI,CAACE,GAAG,CAAC,EAAE,EAAEH,IAAI,CAACb,WAAW,GAAG,CAACc,IAAI,CAACG,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC;QACrFd,aAAa,EAAEW,IAAI,CAACC,GAAG,CAAC,EAAE,EAAED,IAAI,CAACE,GAAG,CAAC,EAAE,EAAEH,IAAI,CAACV,aAAa,GAAG,CAACW,IAAI,CAACG,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI,GAAG,CAAC,CAAC;QAC3Fb,WAAW,EAAE,IAAIC,IAAI,CAAC;MACxB,CAAC,CAAC,CAAC;IACL,CAAC,EAAE,KAAK,CAAC;IAET,OAAO,MAAMa,aAAa,CAACP,QAAQ,CAAC;EACtC,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMQ,gBAAgB,GAAIzB,QAAQ,IAAK;IACrC,QAAQA,QAAQ;MACd,KAAK,MAAM;QAAE,OAAO,OAAO;MAC3B,KAAK,QAAQ;QAAE,OAAO,SAAS;MAC/B,KAAK,KAAK;QAAE,OAAO,SAAS;MAC5B;QAAS,OAAO,SAAS;IAC3B;EACF,CAAC;EAED,MAAM0B,aAAa,GAAIV,SAAS,IAAK;IACnC,QAAQA,SAAS;MACf,KAAK,IAAI;QAAE,OAAO,OAAO;MACzB,KAAK,MAAM;QAAE,OAAO,SAAS;MAC7B,KAAK,QAAQ;QAAE,OAAO,MAAM;MAC5B;QAAS,OAAO,SAAS;IAC3B;EACF,CAAC;EAED,oBACE1B,OAAA,CAACvC,GAAG;IAAC4E,EAAE,EAAE;MAAEC,CAAC,EAAE;IAAE,CAAE;IAAAC,QAAA,gBAEhBvC,OAAA,CAACtC,IAAI;MACH2E,EAAE,EAAE;QACFG,EAAE,EAAE,CAAC;QACLC,UAAU,EAAGC,KAAK,IAAKA,KAAK,CAACC,OAAO,CAACC,IAAI,KAAK,MAAM,GAChD,6GAA6G,GAC7G,sHAAsH;QAC1HC,cAAc,EAAE,2BAA2B;QAC3CC,MAAM,EAAE,mCAAmC;QAC3CC,YAAY,EAAE,CAAC;QACfC,QAAQ,EAAE,UAAU;QACpBC,QAAQ,EAAE,QAAQ;QAClBC,SAAS,EAAE,oCAAoC;QAC/CC,UAAU,EAAE,uCAAuC;QACnD,SAAS,EAAE;UACTC,SAAS,EAAE,kBAAkB;UAC7BF,SAAS,EAAE;QACb,CAAC;QACD,WAAW,EAAE;UACXG,OAAO,EAAE,IAAI;UACbL,QAAQ,EAAE,UAAU;UACpBM,GAAG,EAAE,CAAC;UACNC,IAAI,EAAE,CAAC;UACPC,KAAK,EAAE,CAAC;UACRC,MAAM,EAAE,KAAK;UACbhB,UAAU,EAAE,+DAA+D;UAC3EiB,cAAc,EAAE,WAAW;UAC3BC,SAAS,EAAE;QACb,CAAC;QACD,0BAA0B,EAAE;UAC1B,IAAI,EAAE;YAAEC,kBAAkB,EAAE;UAAS,CAAC;UACtC,KAAK,EAAE;YAAEA,kBAAkB,EAAE;UAAW,CAAC;UACzC,MAAM,EAAE;YAAEA,kBAAkB,EAAE;UAAS;QACzC;MACF,CAAE;MAAArB,QAAA,eAEFvC,OAAA,CAACrC,WAAW;QAAA4E,QAAA,gBACVvC,OAAA,CAACvC,GAAG;UAACoG,OAAO,EAAC,MAAM;UAACC,UAAU,EAAC,QAAQ;UAACC,GAAG,EAAE,CAAE;UAACvB,EAAE,EAAE,CAAE;UAAAD,QAAA,gBACpDvC,OAAA,CAAC5B,MAAM;YACLiE,EAAE,EAAE;cACFI,UAAU,EAAE,mDAAmD;cAC/DuB,KAAK,EAAE,EAAE;cACTP,MAAM,EAAE;YACV,CAAE;YAAAlB,QAAA,eAEFvC,OAAA,CAACpB,MAAM;cAACyD,EAAE,EAAE;gBAAE4B,QAAQ,EAAE;cAAG;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC,eACTrE,OAAA,CAACvC,GAAG;YAAC6G,IAAI,EAAE,CAAE;YAAA/B,QAAA,gBACXvC,OAAA,CAACpC,UAAU;cAAC2G,OAAO,EAAC,IAAI;cAACC,UAAU,EAAC,MAAM;cAACC,YAAY;cAAAlC,QAAA,EAAC;YAExD;cAAA2B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbrE,OAAA,CAACpC,UAAU;cAAC2G,OAAO,EAAC,OAAO;cAACG,KAAK,EAAC,gBAAgB;cAAAnC,QAAA,EAAC;YAEnD;cAAA2B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACNrE,OAAA,CAACvC,GAAG;YAACkH,SAAS,EAAC,OAAO;YAAApC,QAAA,gBACpBvC,OAAA,CAACpC,UAAU;cAAC2G,OAAO,EAAC,IAAI;cAACC,UAAU,EAAC,MAAM;cAACE,KAAK,EAAC,WAAW;cAAAnC,QAAA,GACzDzB,UAAU,CAACE,WAAW,EAAC,GAC1B;YAAA;cAAAkD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbrE,OAAA,CAACpC,UAAU;cAAC2G,OAAO,EAAC,OAAO;cAACG,KAAK,EAAC,gBAAgB;cAAAnC,QAAA,EAAC;YAEnD;cAAA2B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENrE,OAAA,CAACnC,IAAI;UAAC+G,SAAS;UAACC,OAAO,EAAE,CAAE;UAAAtC,QAAA,gBACzBvC,OAAA,CAACnC,IAAI;YAACiH,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAzC,QAAA,eACvBvC,OAAA,CAACvC,GAAG;cAAA8E,QAAA,gBACFvC,OAAA,CAACpC,UAAU;gBAAC2G,OAAO,EAAC,IAAI;gBAACE,YAAY;gBAAAlC,QAAA,EAAC;cAEtC;gBAAA2B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbrE,OAAA,CAACvC,GAAG;gBAACoG,OAAO,EAAC,MAAM;gBAACC,UAAU,EAAC,QAAQ;gBAACC,GAAG,EAAE,CAAE;gBAACvB,EAAE,EAAE,CAAE;gBAAAD,QAAA,gBACpDvC,OAAA,CAACpC,UAAU;kBAAC2G,OAAO,EAAC,OAAO;kBAAAhC,QAAA,EAAC;gBAAS;kBAAA2B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAClDrE,OAAA,CAAClC,cAAc;kBACbyG,OAAO,EAAC,aAAa;kBACrBU,KAAK,EAAEnE,UAAU,CAACK,aAAc;kBAChCkB,EAAE,EAAE;oBAAEiC,IAAI,EAAE,CAAC;oBAAEb,MAAM,EAAE,CAAC;oBAAEV,YAAY,EAAE;kBAAE,CAAE;kBAC5C2B,KAAK,EAAC;gBAAW;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClB,CAAC,eACFrE,OAAA,CAACpC,UAAU;kBAAC2G,OAAO,EAAC,OAAO;kBAACC,UAAU,EAAC,MAAM;kBAAAjC,QAAA,GAC1CzB,UAAU,CAACK,aAAa,CAAC+D,OAAO,CAAC,CAAC,CAAC,EAAC,GACvC;gBAAA;kBAAAhB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACNrE,OAAA,CAACpC,UAAU;gBAAC2G,OAAO,EAAC,SAAS;gBAACG,KAAK,EAAC,gBAAgB;gBAAAnC,QAAA,GAAC,gBACrC,EAACzB,UAAU,CAACM,WAAW,CAAC+D,kBAAkB,CAAC,CAAC;cAAA;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACPrE,OAAA,CAACnC,IAAI;YAACiH,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAzC,QAAA,gBACvBvC,OAAA,CAACpC,UAAU;cAAC2G,OAAO,EAAC,IAAI;cAACE,YAAY;cAAAlC,QAAA,EAAC;YAEtC;cAAA2B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbrE,OAAA,CAAChC,IAAI;cAACoH,KAAK;cAAA7C,QAAA,EACRzB,UAAU,CAACI,UAAU,CAACmE,GAAG,CAAC,CAACC,MAAM,EAAEC,KAAK,kBACvCvF,OAAA,CAAC/B,QAAQ;gBAAaoE,EAAE,EAAE;kBAAEmD,EAAE,EAAE,CAAC;kBAAEC,EAAE,EAAE;gBAAI,CAAE;gBAAAlD,QAAA,gBAC3CvC,OAAA,CAAC9B,YAAY;kBAACmE,EAAE,EAAE;oBAAEqD,QAAQ,EAAE;kBAAG,CAAE;kBAAAnD,QAAA,eACjCvC,OAAA,CAACV,YAAY;oBAAC2E,QAAQ,EAAC,OAAO;oBAACS,KAAK,EAAC;kBAAW;oBAAAR,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvC,CAAC,eACfrE,OAAA,CAAC7B,YAAY;kBACXwH,OAAO,eACL3F,OAAA,CAACpC,UAAU;oBAAC2G,OAAO,EAAC,OAAO;oBAAAhC,QAAA,EACxB+C;kBAAM;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACG;gBACb;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC;cAAA,GAVWkB,KAAK;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAWV,CACX;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAEPrE,OAAA,CAACnC,IAAI;MAAC+G,SAAS;MAACC,OAAO,EAAE,CAAE;MAAAtC,QAAA,gBAEzBvC,OAAA,CAACnC,IAAI;QAACiH,IAAI;QAACC,EAAE,EAAE,EAAG;QAACa,EAAE,EAAE,CAAE;QAAArD,QAAA,eACvBvC,OAAA,CAACtC,IAAI;UACH2E,EAAE,EAAE;YACFI,UAAU,EAAGC,KAAK,IAAKA,KAAK,CAACC,OAAO,CAACC,IAAI,KAAK,MAAM,GAChD,iFAAiF,GACjF,uFAAuF;YAC3FC,cAAc,EAAE,YAAY;YAC5BC,MAAM,EAAGJ,KAAK,IAAK,aAAaA,KAAK,CAACC,OAAO,CAACkD,OAAO;UACvD,CAAE;UAAAtD,QAAA,eAEFvC,OAAA,CAACrC,WAAW;YAAA4E,QAAA,gBACVvC,OAAA,CAACvC,GAAG;cAACoG,OAAO,EAAC,MAAM;cAACC,UAAU,EAAC,QAAQ;cAACC,GAAG,EAAE,CAAE;cAACvB,EAAE,EAAE,CAAE;cAAAD,QAAA,gBACpDvC,OAAA,CAAC5B,MAAM;gBACLiE,EAAE,EAAE;kBACFI,UAAU,EAAE,mDAAmD;kBAC/DuB,KAAK,EAAE,EAAE;kBACTP,MAAM,EAAE;gBACV,CAAE;gBAAAlB,QAAA,eAEFvC,OAAA,CAACJ,SAAS;kBAAAsE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP,CAAC,eACTrE,OAAA,CAACpC,UAAU;gBAAC2G,OAAO,EAAC,IAAI;gBAACC,UAAU,EAAC,MAAM;gBAAAjC,QAAA,EAAC;cAE3C;gBAAA2B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,EAELhE,WAAW,CAACgF,GAAG,CAAC,CAACS,UAAU,EAAEP,KAAK,kBACjCvF,OAAA,CAACxB,SAAS;cAER6D,EAAE,EAAE;gBACFG,EAAE,EAAE,CAAC;gBACLC,UAAU,EAAGC,KAAK,IAAKA,KAAK,CAACC,OAAO,CAACC,IAAI,KAAK,MAAM,GAChD,2BAA2B,GAC3B,qBAAqB;gBACzB,UAAU,EAAE;kBAAEiB,OAAO,EAAE;gBAAO;cAChC,CAAE;cAAAtB,QAAA,gBAEFvC,OAAA,CAACvB,gBAAgB;gBAACsH,UAAU,eAAE/F,OAAA,CAACR,cAAc;kBAAA0E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAAA9B,QAAA,eAC/CvC,OAAA,CAACvC,GAAG;kBAACoG,OAAO,EAAC,MAAM;kBAACC,UAAU,EAAC,QAAQ;kBAACC,GAAG,EAAE,CAAE;kBAACC,KAAK,EAAC,MAAM;kBAAAzB,QAAA,gBAC1DvC,OAAA,CAAC5B,MAAM;oBACLiE,EAAE,EAAE;sBACFI,UAAU,EAAEqD,UAAU,CAACpF,QAAQ,KAAK,MAAM,GACtC,mDAAmD,GACnDoF,UAAU,CAACpF,QAAQ,KAAK,QAAQ,GAChC,mDAAmD,GACnD,mDAAmD;sBACvDsD,KAAK,EAAE,EAAE;sBACTP,MAAM,EAAE;oBACV,CAAE;oBAAAlB,QAAA,eAEFvC,OAAA,CAAChB,WAAW;sBAAAkF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CAAC,eACTrE,OAAA,CAACvC,GAAG;oBAAC6G,IAAI,EAAE,CAAE;oBAAA/B,QAAA,gBACXvC,OAAA,CAACpC,UAAU;sBAAC2G,OAAO,EAAC,IAAI;sBAACC,UAAU,EAAC,KAAK;sBAAAjC,QAAA,EACtCuD,UAAU,CAACvF;oBAAI;sBAAA2D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN,CAAC,eACbrE,OAAA,CAACvC,GAAG;sBAACoG,OAAO,EAAC,MAAM;sBAACC,UAAU,EAAC,QAAQ;sBAACC,GAAG,EAAE,CAAE;sBAACiC,EAAE,EAAE,CAAE;sBAAAzD,QAAA,gBACpDvC,OAAA,CAACjC,IAAI;wBACHkI,KAAK,EAAE,GAAGH,UAAU,CAACtF,WAAW,eAAgB;wBAChD0F,IAAI,EAAC,OAAO;wBACZxB,KAAK,EAAEoB,UAAU,CAACtF,WAAW,GAAG,EAAE,GAAG,OAAO,GAAG,SAAU;wBACzD+D,OAAO,EAAC;sBAAU;wBAAAL,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnB,CAAC,eACFrE,OAAA,CAACjC,IAAI;wBACHkI,KAAK,EAAEH,UAAU,CAACrF,SAAU;wBAC5ByF,IAAI,EAAC,OAAO;wBACZ3B,OAAO,EAAC;sBAAU;wBAAAL,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnB,CAAC,eACFrE,OAAA,CAACjC,IAAI;wBACHkI,KAAK,EAAEH,UAAU,CAACpF,QAAS;wBAC3BwF,IAAI,EAAC,OAAO;wBACZxB,KAAK,EAAEvC,gBAAgB,CAAC2D,UAAU,CAACpF,QAAQ;sBAAE;wBAAAwD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC9C,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNrE,OAAA,CAACpC,UAAU;oBAAC2G,OAAO,EAAC,IAAI;oBAACC,UAAU,EAAC,MAAM;oBAACE,KAAK,EAAC,SAAS;oBAAAnC,QAAA,GACvDuD,UAAU,CAACtF,WAAW,EAAC,GAC1B;kBAAA;oBAAA0D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACU,CAAC,eACnBrE,OAAA,CAACtB,gBAAgB;gBAAA6D,QAAA,eACfvC,OAAA,CAACnC,IAAI;kBAAC+G,SAAS;kBAACC,OAAO,EAAE,CAAE;kBAAAtC,QAAA,gBACzBvC,OAAA,CAACnC,IAAI;oBAACiH,IAAI;oBAACC,EAAE,EAAE,EAAG;oBAACC,EAAE,EAAE,CAAE;oBAAAzC,QAAA,gBACvBvC,OAAA,CAACpC,UAAU;sBAAC2G,OAAO,EAAC,WAAW;sBAACE,YAAY;sBAACD,UAAU,EAAC,KAAK;sBAAAjC,QAAA,EAAC;oBAE9D;sBAAA2B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACbrE,OAAA,CAAChC,IAAI;sBAACoH,KAAK;sBAAA7C,QAAA,EACRuD,UAAU,CAACnF,UAAU,CAAC0E,GAAG,CAAC,CAACc,SAAS,EAAEC,GAAG,kBACxCpG,OAAA,CAAC/B,QAAQ;wBAAWoE,EAAE,EAAE;0BAAEmD,EAAE,EAAE;wBAAE,CAAE;wBAAAjD,QAAA,gBAChCvC,OAAA,CAAC9B,YAAY;0BAACmE,EAAE,EAAE;4BAAEqD,QAAQ,EAAE;0BAAG,CAAE;0BAAAnD,QAAA,eACjCvC,OAAA,CAACd,YAAY;4BAAC+E,QAAQ,EAAC,OAAO;4BAACS,KAAK,EAAC;0BAAS;4BAAAR,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACrC,CAAC,eACfrE,OAAA,CAAC7B,YAAY;0BACXwH,OAAO,eACL3F,OAAA,CAACpC,UAAU;4BAAC2G,OAAO,EAAC,OAAO;4BAAAhC,QAAA,EACxB4D;0BAAS;4BAAAjC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACA;wBACb;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACF,CAAC;sBAAA,GAVW+B,GAAG;wBAAAlC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAWR,CACX;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACPrE,OAAA,CAACnC,IAAI;oBAACiH,IAAI;oBAACC,EAAE,EAAE,EAAG;oBAACC,EAAE,EAAE,CAAE;oBAAAzC,QAAA,gBACvBvC,OAAA,CAACpC,UAAU;sBAAC2G,OAAO,EAAC,WAAW;sBAACE,YAAY;sBAACD,UAAU,EAAC,KAAK;sBAAAjC,QAAA,EAAC;oBAE9D;sBAAA2B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACbrE,OAAA,CAACzB,KAAK;sBACJmC,QAAQ,EAAEyB,gBAAgB,CAAC2D,UAAU,CAACpF,QAAQ,CAAE;sBAChD2B,EAAE,EAAE;wBAAEG,EAAE,EAAE;sBAAE,CAAE;sBAAAD,QAAA,EAEbuD,UAAU,CAAClF;oBAAc;sBAAAsD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrB,CAAC,eACRrE,OAAA,CAACvC,GAAG;sBAACoG,OAAO,EAAC,MAAM;sBAACC,UAAU,EAAC,QAAQ;sBAACC,GAAG,EAAE,CAAE;sBAAAxB,QAAA,gBAC7CvC,OAAA,CAACpC,UAAU;wBAAC2G,OAAO,EAAC,SAAS;wBAACG,KAAK,EAAC,gBAAgB;wBAAAnC,QAAA,EAAC;sBAErD;wBAAA2B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eACbrE,OAAA,CAACjC,IAAI;wBACHkI,KAAK,EAAE,GAAGH,UAAU,CAACjF,UAAU,GAAI;wBACnCqF,IAAI,EAAC,OAAO;wBACZxB,KAAK,EAAEoB,UAAU,CAACjF,UAAU,GAAG,EAAE,GAAG,SAAS,GAAG,SAAU;wBAC1D0D,OAAO,EAAC;sBAAQ;wBAAAL,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACjB,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACS,CAAC;YAAA,GAlGdkB,KAAK;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAmGD,CACZ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACS;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGPrE,OAAA,CAACnC,IAAI;QAACiH,IAAI;QAACC,EAAE,EAAE,EAAG;QAACa,EAAE,EAAE,CAAE;QAAArD,QAAA,eACvBvC,OAAA,CAACtC,IAAI;UACH2E,EAAE,EAAE;YACFI,UAAU,EAAGC,KAAK,IAAKA,KAAK,CAACC,OAAO,CAACC,IAAI,KAAK,MAAM,GAChD,iFAAiF,GACjF,uFAAuF;YAC3FC,cAAc,EAAE,YAAY;YAC5BC,MAAM,EAAGJ,KAAK,IAAK,aAAaA,KAAK,CAACC,OAAO,CAACkD,OAAO,EAAE;YACvDpC,MAAM,EAAE;UACV,CAAE;UAAAlB,QAAA,eAEFvC,OAAA,CAACrC,WAAW;YAAA4E,QAAA,gBACVvC,OAAA,CAACvC,GAAG;cAACoG,OAAO,EAAC,MAAM;cAACC,UAAU,EAAC,QAAQ;cAACC,GAAG,EAAE,CAAE;cAACvB,EAAE,EAAE,CAAE;cAAAD,QAAA,gBACpDvC,OAAA,CAAC5B,MAAM;gBACLiE,EAAE,EAAE;kBACFI,UAAU,EAAE,mDAAmD;kBAC/DuB,KAAK,EAAE,EAAE;kBACTP,MAAM,EAAE;gBACV,CAAE;gBAAAlB,QAAA,eAEFvC,OAAA,CAACN,SAAS;kBAAAwE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP,CAAC,eACTrE,OAAA,CAACpC,UAAU;gBAAC2G,OAAO,EAAC,IAAI;gBAACC,UAAU,EAAC,MAAM;gBAAAjC,QAAA,EAAC;cAE3C;gBAAA2B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAENrE,OAAA,CAAChC,IAAI;cAAAuE,QAAA,EACFjB,YAAY,CAAC+D,GAAG,CAAC,CAAC5D,KAAK,EAAE8D,KAAK,kBAC7BvF,OAAA,CAAC/B,QAAQ;gBAAaoE,EAAE,EAAE;kBAAEmD,EAAE,EAAE;gBAAE,CAAE;gBAAAjD,QAAA,eAClCvC,OAAA,CAAC7B,YAAY;kBACXwH,OAAO,eACL3F,OAAA,CAACvC,GAAG;oBAACoG,OAAO,EAAC,MAAM;oBAACC,UAAU,EAAC,QAAQ;oBAACuC,cAAc,EAAC,eAAe;oBAAA9D,QAAA,gBACpEvC,OAAA,CAACpC,UAAU;sBAAC2G,OAAO,EAAC,OAAO;sBAACC,UAAU,EAAC,KAAK;sBAAAjC,QAAA,EACzCd,KAAK,CAACD;oBAAQ;sBAAA0C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL,CAAC,eACbrE,OAAA,CAACvC,GAAG;sBAACoG,OAAO,EAAC,MAAM;sBAACC,UAAU,EAAC,QAAQ;sBAACC,GAAG,EAAE,CAAE;sBAAAxB,QAAA,gBAC7CvC,OAAA,CAACpC,UAAU;wBACT2G,OAAO,EAAC,OAAO;wBACfG,KAAK,EAAEtC,aAAa,CAACX,KAAK,CAACC,SAAS,CAAE;wBACtC8C,UAAU,EAAC,MAAM;wBAAAjC,QAAA,GAEhBd,KAAK,CAACA,KAAK,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,EAAEA,KAAK,CAACA,KAAK,EAAC,GAC3C;sBAAA;wBAAAyC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eACbrE,OAAA,CAAClB,cAAc;wBACbmF,QAAQ,EAAC,OAAO;wBAChBS,KAAK,EAAEtC,aAAa,CAACX,KAAK,CAACC,SAAS,CAAE;wBACtCW,EAAE,EAAE;0BACFe,SAAS,EAAE3B,KAAK,CAACC,SAAS,KAAK,MAAM,GAAG,gBAAgB,GAAG;wBAC7D;sBAAE;wBAAAwC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBACN;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF;cAAC,GAzBWkB,KAAK;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA0BV,CACX;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC,kCAAC;AAACiC,GAAA,GA3ZGrG,gBAAgB;AA6ZtBA,gBAAgB,CAACsG,WAAW,GAAG,kBAAkB;AAEjD,eAAetG,gBAAgB;AAAC,IAAAG,EAAA,EAAAkG,GAAA;AAAAE,YAAA,CAAApG,EAAA;AAAAoG,YAAA,CAAAF,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}