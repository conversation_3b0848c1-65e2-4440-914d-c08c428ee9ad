import React from 'react';
import { Box, Typo<PERSON>, <PERSON><PERSON>, Card, CardContent } from '@mui/material';
import { Lock as LockIcon } from '@mui/icons-material';
import { useAuth } from '../contexts/AuthContext';

/**
 * ProtectedRoute Component
 * Renders children only if user is authenticated, otherwise shows login prompt
 */
const ProtectedRoute = ({ children, fallback = null }) => {
  const { isAuthenticated, isLoading } = useAuth();

  // Show loading state
  if (isLoading) {
    return (
      <Box
        display="flex"
        justifyContent="center"
        alignItems="center"
        minHeight="200px"
      >
        <Typography>Loading...</Typography>
      </Box>
    );
  }

  // Show authentication required message
  if (!isAuthenticated) {
    if (fallback) {
      return fallback;
    }

    return (
      <Box
        display="flex"
        justifyContent="center"
        alignItems="center"
        minHeight="400px"
        p={3}
      >
        <Card sx={{ maxWidth: 400, textAlign: 'center' }}>
          <CardContent sx={{ p: 4 }}>
            <Box
              sx={{
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                borderRadius: '50%',
                width: 80,
                height: 80,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                mx: 'auto',
                mb: 3,
              }}
            >
              <LockIcon sx={{ color: 'white', fontSize: 40 }} />
            </Box>
            
            <Typography variant="h5" gutterBottom fontWeight="bold">
              Authentication Required
            </Typography>
            
            <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
              Please log in to access this feature. Create an account if you don't have one yet.
            </Typography>
            
            <Button
              variant="contained"
              sx={{
                borderRadius: 3,
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                '&:hover': {
                  background: 'linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)',
                },
              }}
              onClick={() => {
                // This could trigger opening the settings dialog
                // or redirect to a login page
                console.log('Open login dialog');
              }}
            >
              Sign In / Sign Up
            </Button>
          </CardContent>
        </Card>
      </Box>
    );
  }

  // Render protected content
  return children;
};

export default ProtectedRoute;
