{"ast": null, "code": "var _jsxFileName = \"E:\\\\PythonProjects\\\\React.js\\\\Smart AI Assisstant\\\\src\\\\components\\\\SettingsDialog.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Dialog, DialogTitle, DialogContent, DialogActions, Button, Box, Typography, Tabs, Tab, TextField, Switch, FormControlLabel, Card, CardContent, Chip, List, ListItem, ListItemIcon, ListItemText, Divider, Alert, IconButton, Avatar, Grid, CircularProgress, Snackbar } from '@mui/material';\nimport { Settings as SettingsIcon, Person as PersonIcon, Security as SecurityIcon, Language as LanguageIcon, Palette as PaletteIcon, Info as InfoIcon, Star as StarIcon, Check as CheckIcon, Close as CloseIcon, Email as EmailIcon, Lock as LockIcon, Visibility as VisibilityIcon, VisibilityOff as VisibilityOffIcon, Star as CrownIcon, Diamond as DiamondIcon } from '@mui/icons-material';\nimport { useScan } from '../contexts/ScanContext';\nimport { useAuth } from '../contexts/AuthContext';\nimport authService from '../services/auth';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst SettingsDialog = /*#__PURE__*/_s(/*#__PURE__*/React.memo(_c = _s(({\n  open,\n  onClose\n}) => {\n  var _user$username;\n  _s();\n  const [activeTab, setActiveTab] = useState(0);\n  const [showPassword, setShowPassword] = useState(false);\n  const [loginForm, setLoginForm] = useState({\n    email: '',\n    password: ''\n  });\n  const [signupForm, setSignupForm] = useState({\n    username: '',\n    email: '',\n    password: '',\n    confirmPassword: ''\n  });\n  const [isLogin, setIsLogin] = useState(true);\n  const [formErrors, setFormErrors] = useState({});\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [snackbar, setSnackbar] = useState({\n    open: false,\n    message: '',\n    severity: 'success'\n  });\n  const {\n    darkMode,\n    language,\n    toggleDarkMode,\n    setLanguage\n  } = useScan();\n  const {\n    user,\n    isAuthenticated,\n    login,\n    signup,\n    logout,\n    error,\n    clearError\n  } = useAuth();\n  const handleTabChange = (event, newValue) => {\n    setActiveTab(newValue);\n    clearError();\n    setFormErrors({});\n  };\n  const showSnackbar = (message, severity = 'success') => {\n    setSnackbar({\n      open: true,\n      message,\n      severity\n    });\n  };\n  const handleSnackbarClose = () => {\n    setSnackbar({\n      ...snackbar,\n      open: false\n    });\n  };\n  const validateLoginForm = () => {\n    const errors = {};\n    if (!loginForm.email) {\n      errors.email = 'Email is required';\n    } else if (!authService.validateEmail(loginForm.email)) {\n      errors.email = 'Please enter a valid email address';\n    }\n    if (!loginForm.password) {\n      errors.password = 'Password is required';\n    }\n    setFormErrors(errors);\n    return Object.keys(errors).length === 0;\n  };\n  const validateSignupForm = () => {\n    const errors = {};\n    if (!signupForm.username) {\n      errors.username = 'Username is required';\n    } else if (signupForm.username.length < 3) {\n      errors.username = 'Username must be at least 3 characters long';\n    } else if (!/^[a-zA-Z0-9_]+$/.test(signupForm.username)) {\n      errors.username = 'Username can only contain letters, numbers, and underscores';\n    }\n    if (!signupForm.email) {\n      errors.email = 'Email is required';\n    } else if (!authService.validateEmail(signupForm.email)) {\n      errors.email = 'Please enter a valid email address';\n    }\n    if (!signupForm.password) {\n      errors.password = 'Password is required';\n    } else {\n      const passwordValidation = authService.validatePassword(signupForm.password);\n      if (!passwordValidation.isValid) {\n        errors.password = passwordValidation.errors[0];\n      }\n    }\n    if (!signupForm.confirmPassword) {\n      errors.confirmPassword = 'Please confirm your password';\n    } else if (signupForm.password !== signupForm.confirmPassword) {\n      errors.confirmPassword = 'Passwords do not match';\n    }\n    setFormErrors(errors);\n    return Object.keys(errors).length === 0;\n  };\n  const handleLogin = async e => {\n    e.preventDefault();\n    if (!validateLoginForm()) return;\n    setIsSubmitting(true);\n    clearError();\n    try {\n      const result = await login(loginForm);\n      if (result.success) {\n        showSnackbar('Login successful! Welcome back.', 'success');\n        setLoginForm({\n          email: '',\n          password: ''\n        });\n        setTimeout(() => onClose(), 1500);\n      } else {\n        showSnackbar(result.error || 'Login failed. Please try again.', 'error');\n      }\n    } catch (error) {\n      showSnackbar('An unexpected error occurred. Please try again.', 'error');\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n  const handleSignup = async e => {\n    e.preventDefault();\n    if (!validateSignupForm()) return;\n    setIsSubmitting(true);\n    clearError();\n    try {\n      const result = await signup({\n        username: signupForm.username,\n        email: signupForm.email,\n        password: signupForm.password,\n        confirmPassword: signupForm.confirmPassword\n      });\n      if (result.success) {\n        showSnackbar('Account created successfully! Welcome to AI Security Guard.', 'success');\n        setSignupForm({\n          username: '',\n          email: '',\n          password: '',\n          confirmPassword: ''\n        });\n        setTimeout(() => onClose(), 1500);\n      } else {\n        showSnackbar(result.error || 'Registration failed. Please try again.', 'error');\n      }\n    } catch (error) {\n      showSnackbar('An unexpected error occurred. Please try again.', 'error');\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n  const handleLogout = async () => {\n    try {\n      await logout();\n      showSnackbar('Logged out successfully.', 'success');\n      setTimeout(() => onClose(), 1000);\n    } catch (error) {\n      showSnackbar('Logout failed. Please try again.', 'error');\n    }\n  };\n  const TabPanel = ({\n    children,\n    value,\n    index\n  }) => /*#__PURE__*/_jsxDEV(\"div\", {\n    hidden: value !== index,\n    children: value === index && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        py: 3\n      },\n      children: children\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 203,\n      columnNumber: 27\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 202,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(Dialog, {\n    open: open,\n    onClose: onClose,\n    maxWidth: \"lg\",\n    fullWidth: true,\n    PaperProps: {\n      sx: {\n        borderRadius: 4,\n        minHeight: '80vh',\n        background: theme => theme.palette.mode === 'dark' ? 'linear-gradient(135deg, rgba(12, 12, 12, 0.98) 0%, rgba(26, 26, 26, 0.98) 50%, rgba(45, 45, 45, 0.98) 100%)' : 'linear-gradient(135deg, rgba(248, 250, 252, 0.98) 0%, rgba(241, 245, 249, 0.98) 50%, rgba(226, 232, 240, 0.98) 100%)',\n        backdropFilter: 'blur(25px) saturate(180%)',\n        border: theme => theme.palette.mode === 'dark' ? '1px solid rgba(102, 126, 234, 0.2)' : '1px solid rgba(102, 126, 234, 0.1)',\n        boxShadow: '0 25px 80px rgba(0, 0, 0, 0.3)',\n        position: 'relative',\n        overflow: 'hidden',\n        '&::before': {\n          content: '\"\"',\n          position: 'absolute',\n          top: 0,\n          left: 0,\n          right: 0,\n          height: '4px',\n          background: 'linear-gradient(90deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%)',\n          backgroundSize: '400% 400%',\n          animation: 'gradientShift 8s ease infinite'\n        },\n        '@keyframes gradientShift': {\n          '0%': {\n            backgroundPosition: '0% 50%'\n          },\n          '50%': {\n            backgroundPosition: '100% 50%'\n          },\n          '100%': {\n            backgroundPosition: '0% 50%'\n          }\n        }\n      }\n    },\n    children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n      sx: {\n        p: 0\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        display: \"flex\",\n        alignItems: \"center\",\n        justifyContent: \"space-between\",\n        sx: {\n          p: 4,\n          background: theme => theme.palette.mode === 'dark' ? 'linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%)' : 'linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%)',\n          borderBottom: theme => `1px solid ${theme.palette.divider}`\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          display: \"flex\",\n          alignItems: \"center\",\n          gap: 3,\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n              borderRadius: '50%',\n              width: 56,\n              height: 56,\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              boxShadow: '0 8px 25px rgba(102, 126, 234, 0.3)'\n            },\n            children: /*#__PURE__*/_jsxDEV(SettingsIcon, {\n              sx: {\n                color: 'white',\n                fontSize: 28\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 272,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 260,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h4\",\n              fontWeight: \"bold\",\n              sx: {\n                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                backgroundClip: 'text',\n                WebkitBackgroundClip: 'text',\n                WebkitTextFillColor: 'transparent'\n              },\n              children: \"Settings & Account\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 275,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: \"Manage your preferences and account settings\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 285,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 274,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 259,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: onClose,\n          sx: {\n            background: 'rgba(255, 255, 255, 0.1)',\n            backdropFilter: 'blur(10px)',\n            '&:hover': {\n              background: 'rgba(255, 255, 255, 0.2)',\n              transform: 'scale(1.05)'\n            },\n            transition: 'all 0.2s ease'\n          },\n          children: /*#__PURE__*/_jsxDEV(CloseIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 302,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 290,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 247,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 246,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n      sx: {\n        p: 0\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          borderBottom: 1,\n          borderColor: 'divider',\n          background: theme => theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.02)' : 'rgba(0, 0, 0, 0.02)'\n        },\n        children: /*#__PURE__*/_jsxDEV(Tabs, {\n          value: activeTab,\n          onChange: handleTabChange,\n          variant: \"fullWidth\",\n          textColor: \"primary\",\n          indicatorColor: \"primary\",\n          sx: {\n            '& .MuiTab-root': {\n              minHeight: 80,\n              fontSize: '1rem',\n              fontWeight: 600,\n              textTransform: 'none',\n              transition: 'all 0.3s ease',\n              '&:hover': {\n                background: theme => theme.palette.mode === 'dark' ? 'rgba(102, 126, 234, 0.1)' : 'rgba(102, 126, 234, 0.05)',\n                transform: 'translateY(-2px)'\n              },\n              '&.Mui-selected': {\n                background: theme => theme.palette.mode === 'dark' ? 'linear-gradient(135deg, rgba(102, 126, 234, 0.2) 0%, rgba(118, 75, 162, 0.2) 100%)' : 'linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%)'\n              }\n            },\n            '& .MuiTabs-indicator': {\n              height: 3,\n              borderRadius: '3px 3px 0 0',\n              background: 'linear-gradient(90deg, #667eea 0%, #764ba2 100%)'\n            }\n          },\n          children: [/*#__PURE__*/_jsxDEV(Tab, {\n            icon: /*#__PURE__*/_jsxDEV(PersonIcon, {\n              sx: {\n                fontSize: 24\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 350,\n              columnNumber: 21\n            }, this),\n            label: \"Account\",\n            iconPosition: \"start\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 349,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Tab, {\n            icon: /*#__PURE__*/_jsxDEV(PaletteIcon, {\n              sx: {\n                fontSize: 24\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 355,\n              columnNumber: 21\n            }, this),\n            label: \"Preferences\",\n            iconPosition: \"start\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 354,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Tab, {\n            icon: /*#__PURE__*/_jsxDEV(InfoIcon, {\n              sx: {\n                fontSize: 24\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 360,\n              columnNumber: 21\n            }, this),\n            label: \"About\",\n            iconPosition: \"start\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 359,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 317,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 308,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          p: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(TabPanel, {\n          value: activeTab,\n          index: 0,\n          children: [isAuthenticated ?\n          /*#__PURE__*/\n          // Authenticated user view\n          _jsxDEV(Box, {\n            maxWidth: 400,\n            mx: \"auto\",\n            children: [/*#__PURE__*/_jsxDEV(Card, {\n              sx: {\n                mb: 3\n              },\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                sx: {\n                  textAlign: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Avatar, {\n                  sx: {\n                    width: 80,\n                    height: 80,\n                    mx: 'auto',\n                    mb: 2,\n                    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                    fontSize: '2rem'\n                  },\n                  children: (user === null || user === void 0 ? void 0 : (_user$username = user.username) === null || _user$username === void 0 ? void 0 : _user$username.charAt(0).toUpperCase()) || 'U'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 375,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h5\",\n                  gutterBottom: true,\n                  fontWeight: \"bold\",\n                  children: (user === null || user === void 0 ? void 0 : user.username) || 'User'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 387,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  gutterBottom: true,\n                  children: user === null || user === void 0 ? void 0 : user.email\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 390,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                  label: user !== null && user !== void 0 && user.emailVerified ? 'Verified' : 'Unverified',\n                  color: user !== null && user !== void 0 && user.emailVerified ? 'success' : 'warning',\n                  size: \"small\",\n                  sx: {\n                    mt: 1\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 393,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 374,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 373,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              display: \"flex\",\n              gap: 2,\n              justifyContent: \"center\",\n              children: /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"outlined\",\n                color: \"error\",\n                onClick: handleLogout,\n                sx: {\n                  borderRadius: 3\n                },\n                children: \"Logout\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 403,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 402,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 372,\n            columnNumber: 15\n          }, this) :\n          /*#__PURE__*/\n          // Login/Signup forms\n          _jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              display: \"flex\",\n              justifyContent: \"center\",\n              mb: 3,\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                variant: isLogin ? 'contained' : 'outlined',\n                onClick: () => {\n                  setIsLogin(true);\n                  setFormErrors({});\n                  clearError();\n                },\n                sx: {\n                  mr: 1,\n                  borderRadius: 3\n                },\n                children: \"Login\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 417,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: !isLogin ? 'contained' : 'outlined',\n                onClick: () => {\n                  setIsLogin(false);\n                  setFormErrors({});\n                  clearError();\n                },\n                sx: {\n                  borderRadius: 3\n                },\n                children: \"Sign Up\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 428,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 416,\n              columnNumber: 17\n            }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n              severity: \"error\",\n              sx: {\n                mb: 2,\n                maxWidth: 400,\n                mx: 'auto'\n              },\n              children: error\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 442,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true), !isAuthenticated && isLogin ? /*#__PURE__*/_jsxDEV(Card, {\n            sx: {\n              maxWidth: 400,\n              mx: 'auto'\n            },\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                gutterBottom: true,\n                textAlign: \"center\",\n                children: \"Welcome Back\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 452,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                component: \"form\",\n                onSubmit: handleLogin,\n                children: [/*#__PURE__*/_jsxDEV(TextField, {\n                  fullWidth: true,\n                  label: \"Email\",\n                  type: \"email\",\n                  value: loginForm.email,\n                  onChange: e => {\n                    setLoginForm({\n                      ...loginForm,\n                      email: e.target.value\n                    });\n                    if (formErrors.email) {\n                      setFormErrors({\n                        ...formErrors,\n                        email: ''\n                      });\n                    }\n                  },\n                  margin: \"normal\",\n                  variant: \"outlined\",\n                  autoComplete: \"email\",\n                  error: !!formErrors.email,\n                  helperText: formErrors.email,\n                  disabled: isSubmitting,\n                  sx: {\n                    '& .MuiOutlinedInput-root': {\n                      borderRadius: 3\n                    }\n                  },\n                  InputProps: {\n                    startAdornment: /*#__PURE__*/_jsxDEV(EmailIcon, {\n                      sx: {\n                        mr: 1,\n                        color: 'text.secondary'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 479,\n                      columnNumber: 41\n                    }, this)\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 456,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                  fullWidth: true,\n                  label: \"Password\",\n                  type: showPassword ? 'text' : 'password',\n                  value: loginForm.password,\n                  onChange: e => {\n                    setLoginForm({\n                      ...loginForm,\n                      password: e.target.value\n                    });\n                    if (formErrors.password) {\n                      setFormErrors({\n                        ...formErrors,\n                        password: ''\n                      });\n                    }\n                  },\n                  margin: \"normal\",\n                  variant: \"outlined\",\n                  autoComplete: \"current-password\",\n                  error: !!formErrors.password,\n                  helperText: formErrors.password,\n                  disabled: isSubmitting,\n                  sx: {\n                    '& .MuiOutlinedInput-root': {\n                      borderRadius: 3\n                    }\n                  },\n                  InputProps: {\n                    startAdornment: /*#__PURE__*/_jsxDEV(LockIcon, {\n                      sx: {\n                        mr: 1,\n                        color: 'text.secondary'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 505,\n                      columnNumber: 41\n                    }, this),\n                    endAdornment: /*#__PURE__*/_jsxDEV(IconButton, {\n                      onClick: () => setShowPassword(!showPassword),\n                      children: showPassword ? /*#__PURE__*/_jsxDEV(VisibilityOffIcon, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 508,\n                        columnNumber: 45\n                      }, this) : /*#__PURE__*/_jsxDEV(VisibilityIcon, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 508,\n                        columnNumber: 69\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 507,\n                      columnNumber: 27\n                    }, this)\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 482,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  type: \"submit\",\n                  fullWidth: true,\n                  variant: \"contained\",\n                  disabled: isSubmitting,\n                  sx: {\n                    mt: 3,\n                    mb: 2,\n                    borderRadius: 3\n                  },\n                  children: isSubmitting ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n                    size: 24,\n                    color: \"inherit\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 521,\n                    columnNumber: 25\n                  }, this) : 'Sign In'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 513,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 455,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 451,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 450,\n            columnNumber: 15\n          }, this) : !isAuthenticated && /*#__PURE__*/_jsxDEV(Card, {\n            sx: {\n              maxWidth: 400,\n              mx: 'auto'\n            },\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                gutterBottom: true,\n                textAlign: \"center\",\n                children: \"Create Account\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 532,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                component: \"form\",\n                onSubmit: handleSignup,\n                children: [/*#__PURE__*/_jsxDEV(TextField, {\n                  fullWidth: true,\n                  label: \"Username\",\n                  value: signupForm.username,\n                  onChange: e => {\n                    setSignupForm({\n                      ...signupForm,\n                      username: e.target.value\n                    });\n                    if (formErrors.username) {\n                      setFormErrors({\n                        ...formErrors,\n                        username: ''\n                      });\n                    }\n                  },\n                  margin: \"normal\",\n                  variant: \"outlined\",\n                  autoComplete: \"username\",\n                  error: !!formErrors.username,\n                  helperText: formErrors.username,\n                  disabled: isSubmitting,\n                  sx: {\n                    '& .MuiOutlinedInput-root': {\n                      borderRadius: 3\n                    }\n                  },\n                  InputProps: {\n                    startAdornment: /*#__PURE__*/_jsxDEV(PersonIcon, {\n                      sx: {\n                        mr: 1,\n                        color: 'text.secondary'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 558,\n                      columnNumber: 41\n                    }, this)\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 536,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                  fullWidth: true,\n                  label: \"Email\",\n                  type: \"email\",\n                  value: signupForm.email,\n                  onChange: e => {\n                    setSignupForm({\n                      ...signupForm,\n                      email: e.target.value\n                    });\n                    if (formErrors.email) {\n                      setFormErrors({\n                        ...formErrors,\n                        email: ''\n                      });\n                    }\n                  },\n                  margin: \"normal\",\n                  variant: \"outlined\",\n                  autoComplete: \"email\",\n                  error: !!formErrors.email,\n                  helperText: formErrors.email,\n                  disabled: isSubmitting,\n                  sx: {\n                    '& .MuiOutlinedInput-root': {\n                      borderRadius: 3\n                    }\n                  },\n                  InputProps: {\n                    startAdornment: /*#__PURE__*/_jsxDEV(EmailIcon, {\n                      sx: {\n                        mr: 1,\n                        color: 'text.secondary'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 584,\n                      columnNumber: 41\n                    }, this)\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 561,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                  fullWidth: true,\n                  label: \"Password\",\n                  type: showPassword ? 'text' : 'password',\n                  value: signupForm.password,\n                  onChange: e => {\n                    setSignupForm({\n                      ...signupForm,\n                      password: e.target.value\n                    });\n                    if (formErrors.password) {\n                      setFormErrors({\n                        ...formErrors,\n                        password: ''\n                      });\n                    }\n                  },\n                  margin: \"normal\",\n                  variant: \"outlined\",\n                  autoComplete: \"new-password\",\n                  error: !!formErrors.password,\n                  helperText: formErrors.password,\n                  disabled: isSubmitting,\n                  sx: {\n                    '& .MuiOutlinedInput-root': {\n                      borderRadius: 3\n                    }\n                  },\n                  InputProps: {\n                    startAdornment: /*#__PURE__*/_jsxDEV(LockIcon, {\n                      sx: {\n                        mr: 1,\n                        color: 'text.secondary'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 610,\n                      columnNumber: 41\n                    }, this),\n                    endAdornment: /*#__PURE__*/_jsxDEV(IconButton, {\n                      onClick: () => setShowPassword(!showPassword),\n                      children: showPassword ? /*#__PURE__*/_jsxDEV(VisibilityOffIcon, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 613,\n                        columnNumber: 45\n                      }, this) : /*#__PURE__*/_jsxDEV(VisibilityIcon, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 613,\n                        columnNumber: 69\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 612,\n                      columnNumber: 27\n                    }, this)\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 587,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                  fullWidth: true,\n                  label: \"Confirm Password\",\n                  type: \"password\",\n                  value: signupForm.confirmPassword,\n                  onChange: e => {\n                    setSignupForm({\n                      ...signupForm,\n                      confirmPassword: e.target.value\n                    });\n                    if (formErrors.confirmPassword) {\n                      setFormErrors({\n                        ...formErrors,\n                        confirmPassword: ''\n                      });\n                    }\n                  },\n                  margin: \"normal\",\n                  variant: \"outlined\",\n                  autoComplete: \"new-password\",\n                  error: !!formErrors.confirmPassword,\n                  helperText: formErrors.confirmPassword,\n                  disabled: isSubmitting,\n                  sx: {\n                    '& .MuiOutlinedInput-root': {\n                      borderRadius: 3\n                    }\n                  },\n                  InputProps: {\n                    startAdornment: /*#__PURE__*/_jsxDEV(LockIcon, {\n                      sx: {\n                        mr: 1,\n                        color: 'text.secondary'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 641,\n                      columnNumber: 41\n                    }, this)\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 618,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  type: \"submit\",\n                  fullWidth: true,\n                  variant: \"contained\",\n                  disabled: isSubmitting,\n                  sx: {\n                    mt: 3,\n                    mb: 2,\n                    borderRadius: 3\n                  },\n                  children: isSubmitting ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n                    size: 24,\n                    color: \"inherit\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 652,\n                    columnNumber: 25\n                  }, this) : 'Create Account'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 644,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 535,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 531,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 530,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 369,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TabPanel, {\n          value: activeTab,\n          index: 1,\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            maxWidth: 700,\n            mx: \"auto\",\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h5\",\n              gutterBottom: true,\n              fontWeight: \"bold\",\n              sx: {\n                mb: 4,\n                textAlign: 'center'\n              },\n              children: \"Preferences & Settings\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 666,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              container: true,\n              spacing: 3,\n              children: [/*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                md: 6,\n                children: /*#__PURE__*/_jsxDEV(Card, {\n                  sx: {\n                    height: '100%',\n                    background: theme => theme.palette.mode === 'dark' ? 'linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%)' : 'linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%)',\n                    border: '1px solid rgba(102, 126, 234, 0.2)'\n                  },\n                  children: /*#__PURE__*/_jsxDEV(CardContent, {\n                    sx: {\n                      p: 3\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Box, {\n                      display: \"flex\",\n                      alignItems: \"center\",\n                      gap: 2,\n                      mb: 3,\n                      children: [/*#__PURE__*/_jsxDEV(LanguageIcon, {\n                        color: \"primary\",\n                        sx: {\n                          fontSize: 28\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 682,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(Box, {\n                        children: [/*#__PURE__*/_jsxDEV(Typography, {\n                          variant: \"h6\",\n                          fontWeight: \"600\",\n                          children: \"Language\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 684,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                          variant: \"body2\",\n                          color: \"text.secondary\",\n                          children: \"Choose your preferred language\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 687,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 683,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 681,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Box, {\n                      display: \"flex\",\n                      flexDirection: \"column\",\n                      gap: 2,\n                      children: [{\n                        code: 'en',\n                        label: '🇺🇸 English',\n                        name: 'English'\n                      }, {\n                        code: 'ar',\n                        label: '🇸🇦 العربية',\n                        name: 'Arabic'\n                      }, {\n                        code: 'fr',\n                        label: '🇫🇷 Français',\n                        name: 'French'\n                      }, {\n                        code: 'es',\n                        label: '🇪🇸 Español',\n                        name: 'Spanish'\n                      }, {\n                        code: 'de',\n                        label: '🇩🇪 Deutsch',\n                        name: 'German'\n                      }].map(lang => /*#__PURE__*/_jsxDEV(Button, {\n                        variant: language === lang.code ? 'contained' : 'outlined',\n                        onClick: () => setLanguage(lang.code),\n                        fullWidth: true,\n                        sx: {\n                          borderRadius: 3,\n                          justifyContent: 'flex-start',\n                          py: 1.5,\n                          background: language === lang.code ? 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' : 'transparent',\n                          '&:hover': {\n                            background: language === lang.code ? 'linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)' : 'rgba(102, 126, 234, 0.1)'\n                          }\n                        },\n                        children: lang.label\n                      }, lang.code, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 700,\n                        columnNumber: 27\n                      }, this))\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 692,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 680,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 673,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 672,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                md: 6,\n                children: /*#__PURE__*/_jsxDEV(Card, {\n                  sx: {\n                    height: '100%',\n                    background: theme => theme.palette.mode === 'dark' ? 'linear-gradient(135deg, rgba(244, 67, 54, 0.1) 0%, rgba(245, 87, 108, 0.1) 100%)' : 'linear-gradient(135deg, rgba(244, 67, 54, 0.05) 0%, rgba(245, 87, 108, 0.05) 100%)',\n                    border: '1px solid rgba(244, 67, 54, 0.2)'\n                  },\n                  children: /*#__PURE__*/_jsxDEV(CardContent, {\n                    sx: {\n                      p: 3\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Box, {\n                      display: \"flex\",\n                      alignItems: \"center\",\n                      gap: 2,\n                      mb: 3,\n                      children: [/*#__PURE__*/_jsxDEV(PaletteIcon, {\n                        color: \"error\",\n                        sx: {\n                          fontSize: 28\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 738,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(Box, {\n                        children: [/*#__PURE__*/_jsxDEV(Typography, {\n                          variant: \"h6\",\n                          fontWeight: \"600\",\n                          children: \"Font Size\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 740,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                          variant: \"body2\",\n                          color: \"text.secondary\",\n                          children: \"Accessibility-friendly option for visual comfort\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 743,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 739,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 737,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Box, {\n                      display: \"flex\",\n                      flexDirection: \"column\",\n                      gap: 2,\n                      children: [{\n                        size: 'small',\n                        label: 'Small',\n                        description: 'Compact text size'\n                      }, {\n                        size: 'medium',\n                        label: 'Medium (Default)',\n                        description: 'Standard text size'\n                      }, {\n                        size: 'large',\n                        label: 'Large',\n                        description: 'Enhanced readability'\n                      }].map(fontOption => /*#__PURE__*/_jsxDEV(Button, {\n                        variant: fontOption.size === 'medium' ? 'contained' : 'outlined',\n                        fullWidth: true,\n                        sx: {\n                          borderRadius: 3,\n                          justifyContent: 'flex-start',\n                          py: 1.5,\n                          flexDirection: 'column',\n                          alignItems: 'flex-start',\n                          background: fontOption.size === 'medium' ? 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)' : 'transparent',\n                          '&:hover': {\n                            background: fontOption.size === 'medium' ? 'linear-gradient(135deg, #e081e8 0%, #e34759 100%)' : 'rgba(244, 67, 54, 0.1)'\n                          }\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(Typography, {\n                          variant: \"subtitle1\",\n                          fontWeight: \"600\",\n                          children: fontOption.label\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 774,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                          variant: \"caption\",\n                          color: \"text.secondary\",\n                          children: fontOption.description\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 777,\n                          columnNumber: 29\n                        }, this)]\n                      }, fontOption.size, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 754,\n                        columnNumber: 27\n                      }, this))\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 748,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 736,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 729,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 728,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 670,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 665,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 664,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TabPanel, {\n          value: activeTab,\n          index: 2,\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            maxWidth: 600,\n            mx: \"auto\",\n            textAlign: \"center\",\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                borderRadius: '50%',\n                width: 80,\n                height: 80,\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                mx: 'auto',\n                mb: 3\n              },\n              children: /*#__PURE__*/_jsxDEV(SecurityIcon, {\n                sx: {\n                  color: 'white',\n                  fontSize: 40\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 806,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 793,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h4\",\n              gutterBottom: true,\n              fontWeight: \"bold\",\n              children: \"AI Security Guard\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 809,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              color: \"text.secondary\",\n              gutterBottom: true,\n              children: \"Version 1.0.0\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 813,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              sx: {\n                mb: 4,\n                lineHeight: 1.7\n              },\n              children: \"Advanced AI-powered security scanning platform providing comprehensive threat detection and analysis for URLs and files. Built with cutting-edge technology to protect your digital assets.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 817,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Card, {\n              sx: {\n                mb: 3\n              },\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  gutterBottom: true,\n                  children: \"Key Features\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 825,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(List, {\n                  children: [/*#__PURE__*/_jsxDEV(ListItem, {\n                    children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                      children: /*#__PURE__*/_jsxDEV(CheckIcon, {\n                        color: \"primary\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 831,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 830,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                      primary: \"Real-time URL threat detection\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 833,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 829,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n                    children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                      children: /*#__PURE__*/_jsxDEV(CheckIcon, {\n                        color: \"primary\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 837,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 836,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                      primary: \"Advanced file malware scanning\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 839,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 835,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n                    children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                      children: /*#__PURE__*/_jsxDEV(CheckIcon, {\n                        color: \"primary\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 843,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 842,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                      primary: \"AI-powered threat analysis\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 845,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 841,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n                    children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                      children: /*#__PURE__*/_jsxDEV(CheckIcon, {\n                        color: \"primary\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 849,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 848,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                      primary: \"Comprehensive security reports\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 851,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 847,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 828,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 824,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 823,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: [\"\\xA9 2024 AI Security Guard. All rights reserved.\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 859,\n                columnNumber: 17\n              }, this), \"Built with \\u2764\\uFE0F for digital security\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 857,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 792,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 791,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 367,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 307,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Snackbar, {\n      open: snackbar.open,\n      autoHideDuration: 6000,\n      onClose: handleSnackbarClose,\n      anchorOrigin: {\n        vertical: 'bottom',\n        horizontal: 'center'\n      },\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        onClose: handleSnackbarClose,\n        severity: snackbar.severity,\n        sx: {\n          width: '100%'\n        },\n        children: snackbar.message\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 874,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 868,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 208,\n    columnNumber: 5\n  }, this);\n}, \"INkRZHBm3kR8soAvRq+gVW9VLq4=\", false, function () {\n  return [useScan, useAuth];\n})), \"INkRZHBm3kR8soAvRq+gVW9VLq4=\", false, function () {\n  return [useScan, useAuth];\n});\n_c2 = SettingsDialog;\nSettingsDialog.displayName = 'SettingsDialog';\nexport default SettingsDialog;\nvar _c, _c2;\n$RefreshReg$(_c, \"SettingsDialog$React.memo\");\n$RefreshReg$(_c2, \"SettingsDialog\");", "map": {"version": 3, "names": ["React", "useState", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "<PERSON><PERSON>", "Box", "Typography", "Tabs", "Tab", "TextField", "Switch", "FormControlLabel", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Chip", "List", "ListItem", "ListItemIcon", "ListItemText", "Divider", "<PERSON><PERSON>", "IconButton", "Avatar", "Grid", "CircularProgress", "Snackbar", "Settings", "SettingsIcon", "Person", "PersonIcon", "Security", "SecurityIcon", "Language", "LanguageIcon", "Palette", "PaletteIcon", "Info", "InfoIcon", "Star", "StarIcon", "Check", "CheckIcon", "Close", "CloseIcon", "Email", "EmailIcon", "Lock", "LockIcon", "Visibility", "VisibilityIcon", "VisibilityOff", "VisibilityOffIcon", "CrownIcon", "Diamond", "DiamondIcon", "useScan", "useAuth", "authService", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "SettingsDialog", "_s", "memo", "_c", "open", "onClose", "_user$username", "activeTab", "setActiveTab", "showPassword", "setShowPassword", "loginForm", "setLoginForm", "email", "password", "signupForm", "setSignupForm", "username", "confirmPassword", "is<PERSON>ogin", "setIsLogin", "formErrors", "setFormErrors", "isSubmitting", "setIsSubmitting", "snackbar", "setSnackbar", "message", "severity", "darkMode", "language", "toggleDarkMode", "setLanguage", "user", "isAuthenticated", "login", "signup", "logout", "error", "clearError", "handleTabChange", "event", "newValue", "showSnackbar", "handleSnackbarClose", "validateLoginForm", "errors", "validateEmail", "Object", "keys", "length", "validateSignupForm", "test", "passwordValidation", "validatePassword", "<PERSON><PERSON><PERSON><PERSON>", "handleLogin", "e", "preventDefault", "result", "success", "setTimeout", "handleSignup", "handleLogout", "TabPanel", "children", "value", "index", "hidden", "sx", "py", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "max<PERSON><PERSON><PERSON>", "fullWidth", "PaperProps", "borderRadius", "minHeight", "background", "theme", "palette", "mode", "<PERSON><PERSON>ilter", "border", "boxShadow", "position", "overflow", "content", "top", "left", "right", "height", "backgroundSize", "animation", "backgroundPosition", "p", "display", "alignItems", "justifyContent", "borderBottom", "divider", "gap", "width", "color", "fontSize", "variant", "fontWeight", "backgroundClip", "WebkitBackgroundClip", "WebkitTextFillColor", "onClick", "transform", "transition", "borderColor", "onChange", "textColor", "indicatorColor", "textTransform", "icon", "label", "iconPosition", "mx", "mb", "textAlign", "char<PERSON>t", "toUpperCase", "gutterBottom", "emailVerified", "size", "mt", "mr", "component", "onSubmit", "type", "target", "margin", "autoComplete", "helperText", "disabled", "InputProps", "startAdornment", "endAdornment", "container", "spacing", "item", "xs", "md", "flexDirection", "code", "name", "map", "lang", "description", "fontOption", "lineHeight", "primary", "autoHideDuration", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "_c2", "displayName", "$RefreshReg$"], "sources": ["E:/PythonProjects/React.js/Smart AI Assisstant/src/components/SettingsDialog.jsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  <PERSON>alog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  Button,\n  Box,\n  Typography,\n  Tabs,\n  Tab,\n  TextField,\n  Switch,\n  FormControlLabel,\n  Card,\n  CardContent,\n  Chip,\n  List,\n  ListItem,\n  ListItemIcon,\n  ListItemText,\n  Divider,\n  Alert,\n  IconButton,\n  Avatar,\n  Grid,\n  CircularProgress,\n  Snackbar,\n} from '@mui/material';\nimport {\n  Settings as SettingsIcon,\n  Person as PersonIcon,\n  Security as SecurityIcon,\n  Language as LanguageIcon,\n  Palette as PaletteIcon,\n  Info as InfoIcon,\n  Star as StarIcon,\n  Check as CheckIcon,\n  Close as CloseIcon,\n  Email as EmailIcon,\n  Lock as LockIcon,\n  Visibility as VisibilityIcon,\n  VisibilityOff as VisibilityOffIcon,\n  Star as CrownIcon,\n  Diamond as DiamondIcon,\n} from '@mui/icons-material';\nimport { useScan } from '../contexts/ScanContext';\nimport { useAuth } from '../contexts/AuthContext';\nimport authService from '../services/auth';\n\nconst SettingsDialog = React.memo(({ open, onClose }) => {\n  const [activeTab, setActiveTab] = useState(0);\n  const [showPassword, setShowPassword] = useState(false);\n  const [loginForm, setLoginForm] = useState({ email: '', password: '' });\n  const [signupForm, setSignupForm] = useState({\n    username: '',\n    email: '',\n    password: '',\n    confirmPassword: ''\n  });\n  const [isLogin, setIsLogin] = useState(true);\n  const [formErrors, setFormErrors] = useState({});\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' });\n\n  const { darkMode, language, toggleDarkMode, setLanguage } = useScan();\n  const { user, isAuthenticated, login, signup, logout, error, clearError } = useAuth();\n\n  const handleTabChange = (event, newValue) => {\n    setActiveTab(newValue);\n    clearError();\n    setFormErrors({});\n  };\n\n  const showSnackbar = (message, severity = 'success') => {\n    setSnackbar({ open: true, message, severity });\n  };\n\n  const handleSnackbarClose = () => {\n    setSnackbar({ ...snackbar, open: false });\n  };\n\n  const validateLoginForm = () => {\n    const errors = {};\n\n    if (!loginForm.email) {\n      errors.email = 'Email is required';\n    } else if (!authService.validateEmail(loginForm.email)) {\n      errors.email = 'Please enter a valid email address';\n    }\n\n    if (!loginForm.password) {\n      errors.password = 'Password is required';\n    }\n\n    setFormErrors(errors);\n    return Object.keys(errors).length === 0;\n  };\n\n  const validateSignupForm = () => {\n    const errors = {};\n\n    if (!signupForm.username) {\n      errors.username = 'Username is required';\n    } else if (signupForm.username.length < 3) {\n      errors.username = 'Username must be at least 3 characters long';\n    } else if (!/^[a-zA-Z0-9_]+$/.test(signupForm.username)) {\n      errors.username = 'Username can only contain letters, numbers, and underscores';\n    }\n\n    if (!signupForm.email) {\n      errors.email = 'Email is required';\n    } else if (!authService.validateEmail(signupForm.email)) {\n      errors.email = 'Please enter a valid email address';\n    }\n\n    if (!signupForm.password) {\n      errors.password = 'Password is required';\n    } else {\n      const passwordValidation = authService.validatePassword(signupForm.password);\n      if (!passwordValidation.isValid) {\n        errors.password = passwordValidation.errors[0];\n      }\n    }\n\n    if (!signupForm.confirmPassword) {\n      errors.confirmPassword = 'Please confirm your password';\n    } else if (signupForm.password !== signupForm.confirmPassword) {\n      errors.confirmPassword = 'Passwords do not match';\n    }\n\n    setFormErrors(errors);\n    return Object.keys(errors).length === 0;\n  };\n\n  const handleLogin = async (e) => {\n    e.preventDefault();\n\n    if (!validateLoginForm()) return;\n\n    setIsSubmitting(true);\n    clearError();\n\n    try {\n      const result = await login(loginForm);\n\n      if (result.success) {\n        showSnackbar('Login successful! Welcome back.', 'success');\n        setLoginForm({ email: '', password: '' });\n        setTimeout(() => onClose(), 1500);\n      } else {\n        showSnackbar(result.error || 'Login failed. Please try again.', 'error');\n      }\n    } catch (error) {\n      showSnackbar('An unexpected error occurred. Please try again.', 'error');\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  const handleSignup = async (e) => {\n    e.preventDefault();\n\n    if (!validateSignupForm()) return;\n\n    setIsSubmitting(true);\n    clearError();\n\n    try {\n      const result = await signup({\n        username: signupForm.username,\n        email: signupForm.email,\n        password: signupForm.password,\n        confirmPassword: signupForm.confirmPassword,\n      });\n\n      if (result.success) {\n        showSnackbar('Account created successfully! Welcome to AI Security Guard.', 'success');\n        setSignupForm({ username: '', email: '', password: '', confirmPassword: '' });\n        setTimeout(() => onClose(), 1500);\n      } else {\n        showSnackbar(result.error || 'Registration failed. Please try again.', 'error');\n      }\n    } catch (error) {\n      showSnackbar('An unexpected error occurred. Please try again.', 'error');\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  const handleLogout = async () => {\n    try {\n      await logout();\n      showSnackbar('Logged out successfully.', 'success');\n      setTimeout(() => onClose(), 1000);\n    } catch (error) {\n      showSnackbar('Logout failed. Please try again.', 'error');\n    }\n  };\n\n  const TabPanel = ({ children, value, index }) => (\n    <div hidden={value !== index}>\n      {value === index && <Box sx={{ py: 3 }}>{children}</Box>}\n    </div>\n  );\n\n  return (\n    <Dialog\n      open={open}\n      onClose={onClose}\n      maxWidth=\"lg\"\n      fullWidth\n      PaperProps={{\n        sx: {\n          borderRadius: 4,\n          minHeight: '80vh',\n          background: (theme) => theme.palette.mode === 'dark'\n            ? 'linear-gradient(135deg, rgba(12, 12, 12, 0.98) 0%, rgba(26, 26, 26, 0.98) 50%, rgba(45, 45, 45, 0.98) 100%)'\n            : 'linear-gradient(135deg, rgba(248, 250, 252, 0.98) 0%, rgba(241, 245, 249, 0.98) 50%, rgba(226, 232, 240, 0.98) 100%)',\n          backdropFilter: 'blur(25px) saturate(180%)',\n          border: (theme) => theme.palette.mode === 'dark'\n            ? '1px solid rgba(102, 126, 234, 0.2)'\n            : '1px solid rgba(102, 126, 234, 0.1)',\n          boxShadow: '0 25px 80px rgba(0, 0, 0, 0.3)',\n          position: 'relative',\n          overflow: 'hidden',\n          '&::before': {\n            content: '\"\"',\n            position: 'absolute',\n            top: 0,\n            left: 0,\n            right: 0,\n            height: '4px',\n            background: 'linear-gradient(90deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%)',\n            backgroundSize: '400% 400%',\n            animation: 'gradientShift 8s ease infinite',\n          },\n          '@keyframes gradientShift': {\n            '0%': { backgroundPosition: '0% 50%' },\n            '50%': { backgroundPosition: '100% 50%' },\n            '100%': { backgroundPosition: '0% 50%' },\n          },\n        },\n      }}\n    >\n      <DialogTitle sx={{ p: 0 }}>\n        <Box\n          display=\"flex\"\n          alignItems=\"center\"\n          justifyContent=\"space-between\"\n          sx={{\n            p: 4,\n            background: (theme) => theme.palette.mode === 'dark'\n              ? 'linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%)'\n              : 'linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%)',\n            borderBottom: (theme) => `1px solid ${theme.palette.divider}`,\n          }}\n        >\n          <Box display=\"flex\" alignItems=\"center\" gap={3}>\n            <Box\n              sx={{\n                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                borderRadius: '50%',\n                width: 56,\n                height: 56,\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                boxShadow: '0 8px 25px rgba(102, 126, 234, 0.3)',\n              }}\n            >\n              <SettingsIcon sx={{ color: 'white', fontSize: 28 }} />\n            </Box>\n            <Box>\n              <Typography variant=\"h4\" fontWeight=\"bold\"\n                sx={{\n                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                  backgroundClip: 'text',\n                  WebkitBackgroundClip: 'text',\n                  WebkitTextFillColor: 'transparent',\n                }}\n              >\n                Settings & Account\n              </Typography>\n              <Typography variant=\"body2\" color=\"text.secondary\">\n                Manage your preferences and account settings\n              </Typography>\n            </Box>\n          </Box>\n          <IconButton\n            onClick={onClose}\n            sx={{\n              background: 'rgba(255, 255, 255, 0.1)',\n              backdropFilter: 'blur(10px)',\n              '&:hover': {\n                background: 'rgba(255, 255, 255, 0.2)',\n                transform: 'scale(1.05)',\n              },\n              transition: 'all 0.2s ease',\n            }}\n          >\n            <CloseIcon />\n          </IconButton>\n        </Box>\n      </DialogTitle>\n\n      <DialogContent sx={{ p: 0 }}>\n        <Box\n          sx={{\n            borderBottom: 1,\n            borderColor: 'divider',\n            background: (theme) => theme.palette.mode === 'dark'\n              ? 'rgba(255, 255, 255, 0.02)'\n              : 'rgba(0, 0, 0, 0.02)',\n          }}\n        >\n          <Tabs\n            value={activeTab}\n            onChange={handleTabChange}\n            variant=\"fullWidth\"\n            textColor=\"primary\"\n            indicatorColor=\"primary\"\n            sx={{\n              '& .MuiTab-root': {\n                minHeight: 80,\n                fontSize: '1rem',\n                fontWeight: 600,\n                textTransform: 'none',\n                transition: 'all 0.3s ease',\n                '&:hover': {\n                  background: (theme) => theme.palette.mode === 'dark'\n                    ? 'rgba(102, 126, 234, 0.1)'\n                    : 'rgba(102, 126, 234, 0.05)',\n                  transform: 'translateY(-2px)',\n                },\n                '&.Mui-selected': {\n                  background: (theme) => theme.palette.mode === 'dark'\n                    ? 'linear-gradient(135deg, rgba(102, 126, 234, 0.2) 0%, rgba(118, 75, 162, 0.2) 100%)'\n                    : 'linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%)',\n                },\n              },\n              '& .MuiTabs-indicator': {\n                height: 3,\n                borderRadius: '3px 3px 0 0',\n                background: 'linear-gradient(90deg, #667eea 0%, #764ba2 100%)',\n              },\n            }}\n          >\n            <Tab\n              icon={<PersonIcon sx={{ fontSize: 24 }} />}\n              label=\"Account\"\n              iconPosition=\"start\"\n            />\n            <Tab\n              icon={<PaletteIcon sx={{ fontSize: 24 }} />}\n              label=\"Preferences\"\n              iconPosition=\"start\"\n            />\n            <Tab\n              icon={<InfoIcon sx={{ fontSize: 24 }} />}\n              label=\"About\"\n              iconPosition=\"start\"\n            />\n          </Tabs>\n        </Box>\n\n        <Box sx={{ p: 3 }}>\n          {/* Account Tab */}\n          <TabPanel value={activeTab} index={0}>\n            {isAuthenticated ? (\n              // Authenticated user view\n              <Box maxWidth={400} mx=\"auto\">\n                <Card sx={{ mb: 3 }}>\n                  <CardContent sx={{ textAlign: 'center' }}>\n                    <Avatar\n                      sx={{\n                        width: 80,\n                        height: 80,\n                        mx: 'auto',\n                        mb: 2,\n                        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                        fontSize: '2rem',\n                      }}\n                    >\n                      {user?.username?.charAt(0).toUpperCase() || 'U'}\n                    </Avatar>\n                    <Typography variant=\"h5\" gutterBottom fontWeight=\"bold\">\n                      {user?.username || 'User'}\n                    </Typography>\n                    <Typography variant=\"body2\" color=\"text.secondary\" gutterBottom>\n                      {user?.email}\n                    </Typography>\n                    <Chip\n                      label={user?.emailVerified ? 'Verified' : 'Unverified'}\n                      color={user?.emailVerified ? 'success' : 'warning'}\n                      size=\"small\"\n                      sx={{ mt: 1 }}\n                    />\n                  </CardContent>\n                </Card>\n\n                <Box display=\"flex\" gap={2} justifyContent=\"center\">\n                  <Button\n                    variant=\"outlined\"\n                    color=\"error\"\n                    onClick={handleLogout}\n                    sx={{ borderRadius: 3 }}\n                  >\n                    Logout\n                  </Button>\n                </Box>\n              </Box>\n            ) : (\n              // Login/Signup forms\n              <>\n                <Box display=\"flex\" justifyContent=\"center\" mb={3}>\n                  <Button\n                    variant={isLogin ? 'contained' : 'outlined'}\n                    onClick={() => {\n                      setIsLogin(true);\n                      setFormErrors({});\n                      clearError();\n                    }}\n                    sx={{ mr: 1, borderRadius: 3 }}\n                  >\n                    Login\n                  </Button>\n                  <Button\n                    variant={!isLogin ? 'contained' : 'outlined'}\n                    onClick={() => {\n                      setIsLogin(false);\n                      setFormErrors({});\n                      clearError();\n                    }}\n                    sx={{ borderRadius: 3 }}\n                  >\n                    Sign Up\n                  </Button>\n                </Box>\n\n                {error && (\n                  <Alert severity=\"error\" sx={{ mb: 2, maxWidth: 400, mx: 'auto' }}>\n                    {error}\n                  </Alert>\n                )}\n              </>\n            )}\n\n            {!isAuthenticated && isLogin ? (\n              <Card sx={{ maxWidth: 400, mx: 'auto' }}>\n                <CardContent>\n                  <Typography variant=\"h6\" gutterBottom textAlign=\"center\">\n                    Welcome Back\n                  </Typography>\n                  <Box component=\"form\" onSubmit={handleLogin}>\n                    <TextField\n                      fullWidth\n                      label=\"Email\"\n                      type=\"email\"\n                      value={loginForm.email}\n                      onChange={(e) => {\n                        setLoginForm({ ...loginForm, email: e.target.value });\n                        if (formErrors.email) {\n                          setFormErrors({ ...formErrors, email: '' });\n                        }\n                      }}\n                      margin=\"normal\"\n                      variant=\"outlined\"\n                      autoComplete=\"email\"\n                      error={!!formErrors.email}\n                      helperText={formErrors.email}\n                      disabled={isSubmitting}\n                      sx={{\n                        '& .MuiOutlinedInput-root': {\n                          borderRadius: 3,\n                        },\n                      }}\n                      InputProps={{\n                        startAdornment: <EmailIcon sx={{ mr: 1, color: 'text.secondary' }} />,\n                      }}\n                    />\n                    <TextField\n                      fullWidth\n                      label=\"Password\"\n                      type={showPassword ? 'text' : 'password'}\n                      value={loginForm.password}\n                      onChange={(e) => {\n                        setLoginForm({ ...loginForm, password: e.target.value });\n                        if (formErrors.password) {\n                          setFormErrors({ ...formErrors, password: '' });\n                        }\n                      }}\n                      margin=\"normal\"\n                      variant=\"outlined\"\n                      autoComplete=\"current-password\"\n                      error={!!formErrors.password}\n                      helperText={formErrors.password}\n                      disabled={isSubmitting}\n                      sx={{\n                        '& .MuiOutlinedInput-root': {\n                          borderRadius: 3,\n                        },\n                      }}\n                      InputProps={{\n                        startAdornment: <LockIcon sx={{ mr: 1, color: 'text.secondary' }} />,\n                        endAdornment: (\n                          <IconButton onClick={() => setShowPassword(!showPassword)}>\n                            {showPassword ? <VisibilityOffIcon /> : <VisibilityIcon />}\n                          </IconButton>\n                        ),\n                      }}\n                    />\n                    <Button\n                      type=\"submit\"\n                      fullWidth\n                      variant=\"contained\"\n                      disabled={isSubmitting}\n                      sx={{ mt: 3, mb: 2, borderRadius: 3 }}\n                    >\n                      {isSubmitting ? (\n                        <CircularProgress size={24} color=\"inherit\" />\n                      ) : (\n                        'Sign In'\n                      )}\n                    </Button>\n                  </Box>\n                </CardContent>\n              </Card>\n            ) : !isAuthenticated && (\n              <Card sx={{ maxWidth: 400, mx: 'auto' }}>\n                <CardContent>\n                  <Typography variant=\"h6\" gutterBottom textAlign=\"center\">\n                    Create Account\n                  </Typography>\n                  <Box component=\"form\" onSubmit={handleSignup}>\n                    <TextField\n                      fullWidth\n                      label=\"Username\"\n                      value={signupForm.username}\n                      onChange={(e) => {\n                        setSignupForm({ ...signupForm, username: e.target.value });\n                        if (formErrors.username) {\n                          setFormErrors({ ...formErrors, username: '' });\n                        }\n                      }}\n                      margin=\"normal\"\n                      variant=\"outlined\"\n                      autoComplete=\"username\"\n                      error={!!formErrors.username}\n                      helperText={formErrors.username}\n                      disabled={isSubmitting}\n                      sx={{\n                        '& .MuiOutlinedInput-root': {\n                          borderRadius: 3,\n                        },\n                      }}\n                      InputProps={{\n                        startAdornment: <PersonIcon sx={{ mr: 1, color: 'text.secondary' }} />,\n                      }}\n                    />\n                    <TextField\n                      fullWidth\n                      label=\"Email\"\n                      type=\"email\"\n                      value={signupForm.email}\n                      onChange={(e) => {\n                        setSignupForm({ ...signupForm, email: e.target.value });\n                        if (formErrors.email) {\n                          setFormErrors({ ...formErrors, email: '' });\n                        }\n                      }}\n                      margin=\"normal\"\n                      variant=\"outlined\"\n                      autoComplete=\"email\"\n                      error={!!formErrors.email}\n                      helperText={formErrors.email}\n                      disabled={isSubmitting}\n                      sx={{\n                        '& .MuiOutlinedInput-root': {\n                          borderRadius: 3,\n                        },\n                      }}\n                      InputProps={{\n                        startAdornment: <EmailIcon sx={{ mr: 1, color: 'text.secondary' }} />,\n                      }}\n                    />\n                    <TextField\n                      fullWidth\n                      label=\"Password\"\n                      type={showPassword ? 'text' : 'password'}\n                      value={signupForm.password}\n                      onChange={(e) => {\n                        setSignupForm({ ...signupForm, password: e.target.value });\n                        if (formErrors.password) {\n                          setFormErrors({ ...formErrors, password: '' });\n                        }\n                      }}\n                      margin=\"normal\"\n                      variant=\"outlined\"\n                      autoComplete=\"new-password\"\n                      error={!!formErrors.password}\n                      helperText={formErrors.password}\n                      disabled={isSubmitting}\n                      sx={{\n                        '& .MuiOutlinedInput-root': {\n                          borderRadius: 3,\n                        },\n                      }}\n                      InputProps={{\n                        startAdornment: <LockIcon sx={{ mr: 1, color: 'text.secondary' }} />,\n                        endAdornment: (\n                          <IconButton onClick={() => setShowPassword(!showPassword)}>\n                            {showPassword ? <VisibilityOffIcon /> : <VisibilityIcon />}\n                          </IconButton>\n                        ),\n                      }}\n                    />\n                    <TextField\n                      fullWidth\n                      label=\"Confirm Password\"\n                      type=\"password\"\n                      value={signupForm.confirmPassword}\n                      onChange={(e) => {\n                        setSignupForm({ ...signupForm, confirmPassword: e.target.value });\n                        if (formErrors.confirmPassword) {\n                          setFormErrors({ ...formErrors, confirmPassword: '' });\n                        }\n                      }}\n                      margin=\"normal\"\n                      variant=\"outlined\"\n                      autoComplete=\"new-password\"\n                      error={!!formErrors.confirmPassword}\n                      helperText={formErrors.confirmPassword}\n                      disabled={isSubmitting}\n                      sx={{\n                        '& .MuiOutlinedInput-root': {\n                          borderRadius: 3,\n                        },\n                      }}\n                      InputProps={{\n                        startAdornment: <LockIcon sx={{ mr: 1, color: 'text.secondary' }} />,\n                      }}\n                    />\n                    <Button\n                      type=\"submit\"\n                      fullWidth\n                      variant=\"contained\"\n                      disabled={isSubmitting}\n                      sx={{ mt: 3, mb: 2, borderRadius: 3 }}\n                    >\n                      {isSubmitting ? (\n                        <CircularProgress size={24} color=\"inherit\" />\n                      ) : (\n                        'Create Account'\n                      )}\n                    </Button>\n                  </Box>\n                </CardContent>\n              </Card>\n            )}\n          </TabPanel>\n\n          {/* Preferences Tab */}\n          <TabPanel value={activeTab} index={1}>\n            <Box maxWidth={700} mx=\"auto\">\n              <Typography variant=\"h5\" gutterBottom fontWeight=\"bold\" sx={{ mb: 4, textAlign: 'center' }}>\n                Preferences & Settings\n              </Typography>\n\n              <Grid container spacing={3}>\n                {/* Language Settings */}\n                <Grid item xs={12} md={6}>\n                  <Card sx={{\n                    height: '100%',\n                    background: (theme) => theme.palette.mode === 'dark'\n                      ? 'linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%)'\n                      : 'linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%)',\n                    border: '1px solid rgba(102, 126, 234, 0.2)',\n                  }}>\n                    <CardContent sx={{ p: 3 }}>\n                      <Box display=\"flex\" alignItems=\"center\" gap={2} mb={3}>\n                        <LanguageIcon color=\"primary\" sx={{ fontSize: 28 }} />\n                        <Box>\n                          <Typography variant=\"h6\" fontWeight=\"600\">\n                            Language\n                          </Typography>\n                          <Typography variant=\"body2\" color=\"text.secondary\">\n                            Choose your preferred language\n                          </Typography>\n                        </Box>\n                      </Box>\n                      <Box display=\"flex\" flexDirection=\"column\" gap={2}>\n                        {[\n                          { code: 'en', label: '🇺🇸 English', name: 'English' },\n                          { code: 'ar', label: '🇸🇦 العربية', name: 'Arabic' },\n                          { code: 'fr', label: '🇫🇷 Français', name: 'French' },\n                          { code: 'es', label: '🇪🇸 Español', name: 'Spanish' },\n                          { code: 'de', label: '🇩🇪 Deutsch', name: 'German' },\n                        ].map((lang) => (\n                          <Button\n                            key={lang.code}\n                            variant={language === lang.code ? 'contained' : 'outlined'}\n                            onClick={() => setLanguage(lang.code)}\n                            fullWidth\n                            sx={{\n                              borderRadius: 3,\n                              justifyContent: 'flex-start',\n                              py: 1.5,\n                              background: language === lang.code\n                                ? 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'\n                                : 'transparent',\n                              '&:hover': {\n                                background: language === lang.code\n                                  ? 'linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)'\n                                  : 'rgba(102, 126, 234, 0.1)',\n                              },\n                            }}\n                          >\n                            {lang.label}\n                          </Button>\n                        ))}\n                      </Box>\n                    </CardContent>\n                  </Card>\n                </Grid>\n\n                {/* Font Size Settings */}\n                <Grid item xs={12} md={6}>\n                  <Card sx={{\n                    height: '100%',\n                    background: (theme) => theme.palette.mode === 'dark'\n                      ? 'linear-gradient(135deg, rgba(244, 67, 54, 0.1) 0%, rgba(245, 87, 108, 0.1) 100%)'\n                      : 'linear-gradient(135deg, rgba(244, 67, 54, 0.05) 0%, rgba(245, 87, 108, 0.05) 100%)',\n                    border: '1px solid rgba(244, 67, 54, 0.2)',\n                  }}>\n                    <CardContent sx={{ p: 3 }}>\n                      <Box display=\"flex\" alignItems=\"center\" gap={2} mb={3}>\n                        <PaletteIcon color=\"error\" sx={{ fontSize: 28 }} />\n                        <Box>\n                          <Typography variant=\"h6\" fontWeight=\"600\">\n                            Font Size\n                          </Typography>\n                          <Typography variant=\"body2\" color=\"text.secondary\">\n                            Accessibility-friendly option for visual comfort\n                          </Typography>\n                        </Box>\n                      </Box>\n                      <Box display=\"flex\" flexDirection=\"column\" gap={2}>\n                        {[\n                          { size: 'small', label: 'Small', description: 'Compact text size' },\n                          { size: 'medium', label: 'Medium (Default)', description: 'Standard text size' },\n                          { size: 'large', label: 'Large', description: 'Enhanced readability' },\n                        ].map((fontOption) => (\n                          <Button\n                            key={fontOption.size}\n                            variant={fontOption.size === 'medium' ? 'contained' : 'outlined'}\n                            fullWidth\n                            sx={{\n                              borderRadius: 3,\n                              justifyContent: 'flex-start',\n                              py: 1.5,\n                              flexDirection: 'column',\n                              alignItems: 'flex-start',\n                              background: fontOption.size === 'medium'\n                                ? 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)'\n                                : 'transparent',\n                              '&:hover': {\n                                background: fontOption.size === 'medium'\n                                  ? 'linear-gradient(135deg, #e081e8 0%, #e34759 100%)'\n                                  : 'rgba(244, 67, 54, 0.1)',\n                              },\n                            }}\n                          >\n                            <Typography variant=\"subtitle1\" fontWeight=\"600\">\n                              {fontOption.label}\n                            </Typography>\n                            <Typography variant=\"caption\" color=\"text.secondary\">\n                              {fontOption.description}\n                            </Typography>\n                          </Button>\n                        ))}\n                      </Box>\n                    </CardContent>\n                  </Card>\n                </Grid>\n              </Grid>\n            </Box>\n          </TabPanel>\n\n          {/* About Tab */}\n          <TabPanel value={activeTab} index={2}>\n            <Box maxWidth={600} mx=\"auto\" textAlign=\"center\">\n              <Box\n                sx={{\n                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                  borderRadius: '50%',\n                  width: 80,\n                  height: 80,\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  mx: 'auto',\n                  mb: 3,\n                }}\n              >\n                <SecurityIcon sx={{ color: 'white', fontSize: 40 }} />\n              </Box>\n\n              <Typography variant=\"h4\" gutterBottom fontWeight=\"bold\">\n                AI Security Guard\n              </Typography>\n\n              <Typography variant=\"h6\" color=\"text.secondary\" gutterBottom>\n                Version 1.0.0\n              </Typography>\n\n              <Typography variant=\"body1\" sx={{ mb: 4, lineHeight: 1.7 }}>\n                Advanced AI-powered security scanning platform providing comprehensive\n                threat detection and analysis for URLs and files. Built with cutting-edge\n                technology to protect your digital assets.\n              </Typography>\n\n              <Card sx={{ mb: 3 }}>\n                <CardContent>\n                  <Typography variant=\"h6\" gutterBottom>\n                    Key Features\n                  </Typography>\n                  <List>\n                    <ListItem>\n                      <ListItemIcon>\n                        <CheckIcon color=\"primary\" />\n                      </ListItemIcon>\n                      <ListItemText primary=\"Real-time URL threat detection\" />\n                    </ListItem>\n                    <ListItem>\n                      <ListItemIcon>\n                        <CheckIcon color=\"primary\" />\n                      </ListItemIcon>\n                      <ListItemText primary=\"Advanced file malware scanning\" />\n                    </ListItem>\n                    <ListItem>\n                      <ListItemIcon>\n                        <CheckIcon color=\"primary\" />\n                      </ListItemIcon>\n                      <ListItemText primary=\"AI-powered threat analysis\" />\n                    </ListItem>\n                    <ListItem>\n                      <ListItemIcon>\n                        <CheckIcon color=\"primary\" />\n                      </ListItemIcon>\n                      <ListItemText primary=\"Comprehensive security reports\" />\n                    </ListItem>\n                  </List>\n                </CardContent>\n              </Card>\n\n              <Typography variant=\"body2\" color=\"text.secondary\">\n                © 2024 AI Security Guard. All rights reserved.\n                <br />\n                Built with ❤️ for digital security\n              </Typography>\n            </Box>\n          </TabPanel>\n        </Box>\n      </DialogContent>\n\n      {/* Snackbar for notifications */}\n      <Snackbar\n        open={snackbar.open}\n        autoHideDuration={6000}\n        onClose={handleSnackbarClose}\n        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}\n      >\n        <Alert\n          onClose={handleSnackbarClose}\n          severity={snackbar.severity}\n          sx={{ width: '100%' }}\n        >\n          {snackbar.message}\n        </Alert>\n      </Snackbar>\n    </Dialog>\n  );\n});\n\nSettingsDialog.displayName = 'SettingsDialog';\n\nexport default SettingsDialog;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,MAAM,EACNC,GAAG,EACHC,UAAU,EACVC,IAAI,EACJC,GAAG,EACHC,SAAS,EACTC,MAAM,EACNC,gBAAgB,EAChBC,IAAI,EACJC,WAAW,EACXC,IAAI,EACJC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,YAAY,EACZC,OAAO,EACPC,KAAK,EACLC,UAAU,EACVC,MAAM,EACNC,IAAI,EACJC,gBAAgB,EAChBC,QAAQ,QACH,eAAe;AACtB,SACEC,QAAQ,IAAIC,YAAY,EACxBC,MAAM,IAAIC,UAAU,EACpBC,QAAQ,IAAIC,YAAY,EACxBC,QAAQ,IAAIC,YAAY,EACxBC,OAAO,IAAIC,WAAW,EACtBC,IAAI,IAAIC,QAAQ,EAChBC,IAAI,IAAIC,QAAQ,EAChBC,KAAK,IAAIC,SAAS,EAClBC,KAAK,IAAIC,SAAS,EAClBC,KAAK,IAAIC,SAAS,EAClBC,IAAI,IAAIC,QAAQ,EAChBC,UAAU,IAAIC,cAAc,EAC5BC,aAAa,IAAIC,iBAAiB,EAClCb,IAAI,IAAIc,SAAS,EACjBC,OAAO,IAAIC,WAAW,QACjB,qBAAqB;AAC5B,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SAASC,OAAO,QAAQ,yBAAyB;AACjD,OAAOC,WAAW,MAAM,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE3C,MAAMC,cAAc,gBAAAC,EAAA,cAAGjE,KAAK,CAACkE,IAAI,CAAAC,EAAA,GAAAF,EAAA,CAAC,CAAC;EAAEG,IAAI;EAAEC;AAAQ,CAAC,KAAK;EAAA,IAAAC,cAAA;EAAAL,EAAA;EACvD,MAAM,CAACM,SAAS,EAAEC,YAAY,CAAC,GAAGvE,QAAQ,CAAC,CAAC,CAAC;EAC7C,MAAM,CAACwE,YAAY,EAAEC,eAAe,CAAC,GAAGzE,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC0E,SAAS,EAAEC,YAAY,CAAC,GAAG3E,QAAQ,CAAC;IAAE4E,KAAK,EAAE,EAAE;IAAEC,QAAQ,EAAE;EAAG,CAAC,CAAC;EACvE,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAG/E,QAAQ,CAAC;IAC3CgF,QAAQ,EAAE,EAAE;IACZJ,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZI,eAAe,EAAE;EACnB,CAAC,CAAC;EACF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGnF,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACoF,UAAU,EAAEC,aAAa,CAAC,GAAGrF,QAAQ,CAAC,CAAC,CAAC,CAAC;EAChD,MAAM,CAACsF,YAAY,EAAEC,eAAe,CAAC,GAAGvF,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACwF,QAAQ,EAAEC,WAAW,CAAC,GAAGzF,QAAQ,CAAC;IAAEmE,IAAI,EAAE,KAAK;IAAEuB,OAAO,EAAE,EAAE;IAAEC,QAAQ,EAAE;EAAU,CAAC,CAAC;EAE3F,MAAM;IAAEC,QAAQ;IAAEC,QAAQ;IAAEC,cAAc;IAAEC;EAAY,CAAC,GAAGvC,OAAO,CAAC,CAAC;EACrE,MAAM;IAAEwC,IAAI;IAAEC,eAAe;IAAEC,KAAK;IAAEC,MAAM;IAAEC,MAAM;IAAEC,KAAK;IAAEC;EAAW,CAAC,GAAG7C,OAAO,CAAC,CAAC;EAErF,MAAM8C,eAAe,GAAGA,CAACC,KAAK,EAAEC,QAAQ,KAAK;IAC3ClC,YAAY,CAACkC,QAAQ,CAAC;IACtBH,UAAU,CAAC,CAAC;IACZjB,aAAa,CAAC,CAAC,CAAC,CAAC;EACnB,CAAC;EAED,MAAMqB,YAAY,GAAGA,CAAChB,OAAO,EAAEC,QAAQ,GAAG,SAAS,KAAK;IACtDF,WAAW,CAAC;MAAEtB,IAAI,EAAE,IAAI;MAAEuB,OAAO;MAAEC;IAAS,CAAC,CAAC;EAChD,CAAC;EAED,MAAMgB,mBAAmB,GAAGA,CAAA,KAAM;IAChClB,WAAW,CAAC;MAAE,GAAGD,QAAQ;MAAErB,IAAI,EAAE;IAAM,CAAC,CAAC;EAC3C,CAAC;EAED,MAAMyC,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,MAAMC,MAAM,GAAG,CAAC,CAAC;IAEjB,IAAI,CAACnC,SAAS,CAACE,KAAK,EAAE;MACpBiC,MAAM,CAACjC,KAAK,GAAG,mBAAmB;IACpC,CAAC,MAAM,IAAI,CAAClB,WAAW,CAACoD,aAAa,CAACpC,SAAS,CAACE,KAAK,CAAC,EAAE;MACtDiC,MAAM,CAACjC,KAAK,GAAG,oCAAoC;IACrD;IAEA,IAAI,CAACF,SAAS,CAACG,QAAQ,EAAE;MACvBgC,MAAM,CAAChC,QAAQ,GAAG,sBAAsB;IAC1C;IAEAQ,aAAa,CAACwB,MAAM,CAAC;IACrB,OAAOE,MAAM,CAACC,IAAI,CAACH,MAAM,CAAC,CAACI,MAAM,KAAK,CAAC;EACzC,CAAC;EAED,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,MAAML,MAAM,GAAG,CAAC,CAAC;IAEjB,IAAI,CAAC/B,UAAU,CAACE,QAAQ,EAAE;MACxB6B,MAAM,CAAC7B,QAAQ,GAAG,sBAAsB;IAC1C,CAAC,MAAM,IAAIF,UAAU,CAACE,QAAQ,CAACiC,MAAM,GAAG,CAAC,EAAE;MACzCJ,MAAM,CAAC7B,QAAQ,GAAG,6CAA6C;IACjE,CAAC,MAAM,IAAI,CAAC,iBAAiB,CAACmC,IAAI,CAACrC,UAAU,CAACE,QAAQ,CAAC,EAAE;MACvD6B,MAAM,CAAC7B,QAAQ,GAAG,6DAA6D;IACjF;IAEA,IAAI,CAACF,UAAU,CAACF,KAAK,EAAE;MACrBiC,MAAM,CAACjC,KAAK,GAAG,mBAAmB;IACpC,CAAC,MAAM,IAAI,CAAClB,WAAW,CAACoD,aAAa,CAAChC,UAAU,CAACF,KAAK,CAAC,EAAE;MACvDiC,MAAM,CAACjC,KAAK,GAAG,oCAAoC;IACrD;IAEA,IAAI,CAACE,UAAU,CAACD,QAAQ,EAAE;MACxBgC,MAAM,CAAChC,QAAQ,GAAG,sBAAsB;IAC1C,CAAC,MAAM;MACL,MAAMuC,kBAAkB,GAAG1D,WAAW,CAAC2D,gBAAgB,CAACvC,UAAU,CAACD,QAAQ,CAAC;MAC5E,IAAI,CAACuC,kBAAkB,CAACE,OAAO,EAAE;QAC/BT,MAAM,CAAChC,QAAQ,GAAGuC,kBAAkB,CAACP,MAAM,CAAC,CAAC,CAAC;MAChD;IACF;IAEA,IAAI,CAAC/B,UAAU,CAACG,eAAe,EAAE;MAC/B4B,MAAM,CAAC5B,eAAe,GAAG,8BAA8B;IACzD,CAAC,MAAM,IAAIH,UAAU,CAACD,QAAQ,KAAKC,UAAU,CAACG,eAAe,EAAE;MAC7D4B,MAAM,CAAC5B,eAAe,GAAG,wBAAwB;IACnD;IAEAI,aAAa,CAACwB,MAAM,CAAC;IACrB,OAAOE,MAAM,CAACC,IAAI,CAACH,MAAM,CAAC,CAACI,MAAM,KAAK,CAAC;EACzC,CAAC;EAED,MAAMM,WAAW,GAAG,MAAOC,CAAC,IAAK;IAC/BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAElB,IAAI,CAACb,iBAAiB,CAAC,CAAC,EAAE;IAE1BrB,eAAe,CAAC,IAAI,CAAC;IACrBe,UAAU,CAAC,CAAC;IAEZ,IAAI;MACF,MAAMoB,MAAM,GAAG,MAAMxB,KAAK,CAACxB,SAAS,CAAC;MAErC,IAAIgD,MAAM,CAACC,OAAO,EAAE;QAClBjB,YAAY,CAAC,iCAAiC,EAAE,SAAS,CAAC;QAC1D/B,YAAY,CAAC;UAAEC,KAAK,EAAE,EAAE;UAAEC,QAAQ,EAAE;QAAG,CAAC,CAAC;QACzC+C,UAAU,CAAC,MAAMxD,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC;MACnC,CAAC,MAAM;QACLsC,YAAY,CAACgB,MAAM,CAACrB,KAAK,IAAI,iCAAiC,EAAE,OAAO,CAAC;MAC1E;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdK,YAAY,CAAC,iDAAiD,EAAE,OAAO,CAAC;IAC1E,CAAC,SAAS;MACRnB,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;EAED,MAAMsC,YAAY,GAAG,MAAOL,CAAC,IAAK;IAChCA,CAAC,CAACC,cAAc,CAAC,CAAC;IAElB,IAAI,CAACP,kBAAkB,CAAC,CAAC,EAAE;IAE3B3B,eAAe,CAAC,IAAI,CAAC;IACrBe,UAAU,CAAC,CAAC;IAEZ,IAAI;MACF,MAAMoB,MAAM,GAAG,MAAMvB,MAAM,CAAC;QAC1BnB,QAAQ,EAAEF,UAAU,CAACE,QAAQ;QAC7BJ,KAAK,EAAEE,UAAU,CAACF,KAAK;QACvBC,QAAQ,EAAEC,UAAU,CAACD,QAAQ;QAC7BI,eAAe,EAAEH,UAAU,CAACG;MAC9B,CAAC,CAAC;MAEF,IAAIyC,MAAM,CAACC,OAAO,EAAE;QAClBjB,YAAY,CAAC,6DAA6D,EAAE,SAAS,CAAC;QACtF3B,aAAa,CAAC;UAAEC,QAAQ,EAAE,EAAE;UAAEJ,KAAK,EAAE,EAAE;UAAEC,QAAQ,EAAE,EAAE;UAAEI,eAAe,EAAE;QAAG,CAAC,CAAC;QAC7E2C,UAAU,CAAC,MAAMxD,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC;MACnC,CAAC,MAAM;QACLsC,YAAY,CAACgB,MAAM,CAACrB,KAAK,IAAI,wCAAwC,EAAE,OAAO,CAAC;MACjF;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdK,YAAY,CAAC,iDAAiD,EAAE,OAAO,CAAC;IAC1E,CAAC,SAAS;MACRnB,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;EAED,MAAMuC,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF,MAAM1B,MAAM,CAAC,CAAC;MACdM,YAAY,CAAC,0BAA0B,EAAE,SAAS,CAAC;MACnDkB,UAAU,CAAC,MAAMxD,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC;IACnC,CAAC,CAAC,OAAOiC,KAAK,EAAE;MACdK,YAAY,CAAC,kCAAkC,EAAE,OAAO,CAAC;IAC3D;EACF,CAAC;EAED,MAAMqB,QAAQ,GAAGA,CAAC;IAAEC,QAAQ;IAAEC,KAAK;IAAEC;EAAM,CAAC,kBAC1CtE,OAAA;IAAKuE,MAAM,EAAEF,KAAK,KAAKC,KAAM;IAAAF,QAAA,EAC1BC,KAAK,KAAKC,KAAK,iBAAItE,OAAA,CAACtD,GAAG;MAAC8H,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAL,QAAA,EAAEA;IAAQ;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACrD,CACN;EAED,oBACE7E,OAAA,CAAC3D,MAAM;IACLkE,IAAI,EAAEA,IAAK;IACXC,OAAO,EAAEA,OAAQ;IACjBsE,QAAQ,EAAC,IAAI;IACbC,SAAS;IACTC,UAAU,EAAE;MACVR,EAAE,EAAE;QACFS,YAAY,EAAE,CAAC;QACfC,SAAS,EAAE,MAAM;QACjBC,UAAU,EAAGC,KAAK,IAAKA,KAAK,CAACC,OAAO,CAACC,IAAI,KAAK,MAAM,GAChD,6GAA6G,GAC7G,sHAAsH;QAC1HC,cAAc,EAAE,2BAA2B;QAC3CC,MAAM,EAAGJ,KAAK,IAAKA,KAAK,CAACC,OAAO,CAACC,IAAI,KAAK,MAAM,GAC5C,oCAAoC,GACpC,oCAAoC;QACxCG,SAAS,EAAE,gCAAgC;QAC3CC,QAAQ,EAAE,UAAU;QACpBC,QAAQ,EAAE,QAAQ;QAClB,WAAW,EAAE;UACXC,OAAO,EAAE,IAAI;UACbF,QAAQ,EAAE,UAAU;UACpBG,GAAG,EAAE,CAAC;UACNC,IAAI,EAAE,CAAC;UACPC,KAAK,EAAE,CAAC;UACRC,MAAM,EAAE,KAAK;UACbb,UAAU,EAAE,yFAAyF;UACrGc,cAAc,EAAE,WAAW;UAC3BC,SAAS,EAAE;QACb,CAAC;QACD,0BAA0B,EAAE;UAC1B,IAAI,EAAE;YAAEC,kBAAkB,EAAE;UAAS,CAAC;UACtC,KAAK,EAAE;YAAEA,kBAAkB,EAAE;UAAW,CAAC;UACzC,MAAM,EAAE;YAAEA,kBAAkB,EAAE;UAAS;QACzC;MACF;IACF,CAAE;IAAA/B,QAAA,gBAEFpE,OAAA,CAAC1D,WAAW;MAACkI,EAAE,EAAE;QAAE4B,CAAC,EAAE;MAAE,CAAE;MAAAhC,QAAA,eACxBpE,OAAA,CAACtD,GAAG;QACF2J,OAAO,EAAC,MAAM;QACdC,UAAU,EAAC,QAAQ;QACnBC,cAAc,EAAC,eAAe;QAC9B/B,EAAE,EAAE;UACF4B,CAAC,EAAE,CAAC;UACJjB,UAAU,EAAGC,KAAK,IAAKA,KAAK,CAACC,OAAO,CAACC,IAAI,KAAK,MAAM,GAChD,oFAAoF,GACpF,sFAAsF;UAC1FkB,YAAY,EAAGpB,KAAK,IAAK,aAAaA,KAAK,CAACC,OAAO,CAACoB,OAAO;QAC7D,CAAE;QAAArC,QAAA,gBAEFpE,OAAA,CAACtD,GAAG;UAAC2J,OAAO,EAAC,MAAM;UAACC,UAAU,EAAC,QAAQ;UAACI,GAAG,EAAE,CAAE;UAAAtC,QAAA,gBAC7CpE,OAAA,CAACtD,GAAG;YACF8H,EAAE,EAAE;cACFW,UAAU,EAAE,mDAAmD;cAC/DF,YAAY,EAAE,KAAK;cACnB0B,KAAK,EAAE,EAAE;cACTX,MAAM,EAAE,EAAE;cACVK,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpBC,cAAc,EAAE,QAAQ;cACxBd,SAAS,EAAE;YACb,CAAE;YAAArB,QAAA,eAEFpE,OAAA,CAAChC,YAAY;cAACwG,EAAE,EAAE;gBAAEoC,KAAK,EAAE,OAAO;gBAAEC,QAAQ,EAAE;cAAG;YAAE;cAAAnC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnD,CAAC,eACN7E,OAAA,CAACtD,GAAG;YAAA0H,QAAA,gBACFpE,OAAA,CAACrD,UAAU;cAACmK,OAAO,EAAC,IAAI;cAACC,UAAU,EAAC,MAAM;cACxCvC,EAAE,EAAE;gBACFW,UAAU,EAAE,mDAAmD;gBAC/D6B,cAAc,EAAE,MAAM;gBACtBC,oBAAoB,EAAE,MAAM;gBAC5BC,mBAAmB,EAAE;cACvB,CAAE;cAAA9C,QAAA,EACH;YAED;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb7E,OAAA,CAACrD,UAAU;cAACmK,OAAO,EAAC,OAAO;cAACF,KAAK,EAAC,gBAAgB;cAAAxC,QAAA,EAAC;YAEnD;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACN7E,OAAA,CAACtC,UAAU;UACTyJ,OAAO,EAAE3G,OAAQ;UACjBgE,EAAE,EAAE;YACFW,UAAU,EAAE,0BAA0B;YACtCI,cAAc,EAAE,YAAY;YAC5B,SAAS,EAAE;cACTJ,UAAU,EAAE,0BAA0B;cACtCiC,SAAS,EAAE;YACb,CAAC;YACDC,UAAU,EAAE;UACd,CAAE;UAAAjD,QAAA,eAEFpE,OAAA,CAAChB,SAAS;YAAA0F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CAAC,eAEd7E,OAAA,CAACzD,aAAa;MAACiI,EAAE,EAAE;QAAE4B,CAAC,EAAE;MAAE,CAAE;MAAAhC,QAAA,gBAC1BpE,OAAA,CAACtD,GAAG;QACF8H,EAAE,EAAE;UACFgC,YAAY,EAAE,CAAC;UACfc,WAAW,EAAE,SAAS;UACtBnC,UAAU,EAAGC,KAAK,IAAKA,KAAK,CAACC,OAAO,CAACC,IAAI,KAAK,MAAM,GAChD,2BAA2B,GAC3B;QACN,CAAE;QAAAlB,QAAA,eAEFpE,OAAA,CAACpD,IAAI;UACHyH,KAAK,EAAE3D,SAAU;UACjB6G,QAAQ,EAAE5E,eAAgB;UAC1BmE,OAAO,EAAC,WAAW;UACnBU,SAAS,EAAC,SAAS;UACnBC,cAAc,EAAC,SAAS;UACxBjD,EAAE,EAAE;YACF,gBAAgB,EAAE;cAChBU,SAAS,EAAE,EAAE;cACb2B,QAAQ,EAAE,MAAM;cAChBE,UAAU,EAAE,GAAG;cACfW,aAAa,EAAE,MAAM;cACrBL,UAAU,EAAE,eAAe;cAC3B,SAAS,EAAE;gBACTlC,UAAU,EAAGC,KAAK,IAAKA,KAAK,CAACC,OAAO,CAACC,IAAI,KAAK,MAAM,GAChD,0BAA0B,GAC1B,2BAA2B;gBAC/B8B,SAAS,EAAE;cACb,CAAC;cACD,gBAAgB,EAAE;gBAChBjC,UAAU,EAAGC,KAAK,IAAKA,KAAK,CAACC,OAAO,CAACC,IAAI,KAAK,MAAM,GAChD,oFAAoF,GACpF;cACN;YACF,CAAC;YACD,sBAAsB,EAAE;cACtBU,MAAM,EAAE,CAAC;cACTf,YAAY,EAAE,aAAa;cAC3BE,UAAU,EAAE;YACd;UACF,CAAE;UAAAf,QAAA,gBAEFpE,OAAA,CAACnD,GAAG;YACF8K,IAAI,eAAE3H,OAAA,CAAC9B,UAAU;cAACsG,EAAE,EAAE;gBAAEqC,QAAQ,EAAE;cAAG;YAAE;cAAAnC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC3C+C,KAAK,EAAC,SAAS;YACfC,YAAY,EAAC;UAAO;YAAAnD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC,eACF7E,OAAA,CAACnD,GAAG;YACF8K,IAAI,eAAE3H,OAAA,CAACxB,WAAW;cAACgG,EAAE,EAAE;gBAAEqC,QAAQ,EAAE;cAAG;YAAE;cAAAnC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC5C+C,KAAK,EAAC,aAAa;YACnBC,YAAY,EAAC;UAAO;YAAAnD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC,eACF7E,OAAA,CAACnD,GAAG;YACF8K,IAAI,eAAE3H,OAAA,CAACtB,QAAQ;cAAC8F,EAAE,EAAE;gBAAEqC,QAAQ,EAAE;cAAG;YAAE;cAAAnC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACzC+C,KAAK,EAAC,OAAO;YACbC,YAAY,EAAC;UAAO;YAAAnD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAEN7E,OAAA,CAACtD,GAAG;QAAC8H,EAAE,EAAE;UAAE4B,CAAC,EAAE;QAAE,CAAE;QAAAhC,QAAA,gBAEhBpE,OAAA,CAACmE,QAAQ;UAACE,KAAK,EAAE3D,SAAU;UAAC4D,KAAK,EAAE,CAAE;UAAAF,QAAA,GAClC/B,eAAe;UAAA;UACd;UACArC,OAAA,CAACtD,GAAG;YAACoI,QAAQ,EAAE,GAAI;YAACgD,EAAE,EAAC,MAAM;YAAA1D,QAAA,gBAC3BpE,OAAA,CAAC/C,IAAI;cAACuH,EAAE,EAAE;gBAAEuD,EAAE,EAAE;cAAE,CAAE;cAAA3D,QAAA,eAClBpE,OAAA,CAAC9C,WAAW;gBAACsH,EAAE,EAAE;kBAAEwD,SAAS,EAAE;gBAAS,CAAE;gBAAA5D,QAAA,gBACvCpE,OAAA,CAACrC,MAAM;kBACL6G,EAAE,EAAE;oBACFmC,KAAK,EAAE,EAAE;oBACTX,MAAM,EAAE,EAAE;oBACV8B,EAAE,EAAE,MAAM;oBACVC,EAAE,EAAE,CAAC;oBACL5C,UAAU,EAAE,mDAAmD;oBAC/D0B,QAAQ,EAAE;kBACZ,CAAE;kBAAAzC,QAAA,EAED,CAAAhC,IAAI,aAAJA,IAAI,wBAAA3B,cAAA,GAAJ2B,IAAI,CAAEhB,QAAQ,cAAAX,cAAA,uBAAdA,cAAA,CAAgBwH,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,KAAI;gBAAG;kBAAAxD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzC,CAAC,eACT7E,OAAA,CAACrD,UAAU;kBAACmK,OAAO,EAAC,IAAI;kBAACqB,YAAY;kBAACpB,UAAU,EAAC,MAAM;kBAAA3C,QAAA,EACpD,CAAAhC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEhB,QAAQ,KAAI;gBAAM;kBAAAsD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf,CAAC,eACb7E,OAAA,CAACrD,UAAU;kBAACmK,OAAO,EAAC,OAAO;kBAACF,KAAK,EAAC,gBAAgB;kBAACuB,YAAY;kBAAA/D,QAAA,EAC5DhC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEpB;gBAAK;kBAAA0D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC,eACb7E,OAAA,CAAC7C,IAAI;kBACHyK,KAAK,EAAExF,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEgG,aAAa,GAAG,UAAU,GAAG,YAAa;kBACvDxB,KAAK,EAAExE,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEgG,aAAa,GAAG,SAAS,GAAG,SAAU;kBACnDC,IAAI,EAAC,OAAO;kBACZ7D,EAAE,EAAE;oBAAE8D,EAAE,EAAE;kBAAE;gBAAE;kBAAA5D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACS;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAEP7E,OAAA,CAACtD,GAAG;cAAC2J,OAAO,EAAC,MAAM;cAACK,GAAG,EAAE,CAAE;cAACH,cAAc,EAAC,QAAQ;cAAAnC,QAAA,eACjDpE,OAAA,CAACvD,MAAM;gBACLqK,OAAO,EAAC,UAAU;gBAClBF,KAAK,EAAC,OAAO;gBACbO,OAAO,EAAEjD,YAAa;gBACtBM,EAAE,EAAE;kBAAES,YAAY,EAAE;gBAAE,CAAE;gBAAAb,QAAA,EACzB;cAED;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;UAAA;UAEN;UACA7E,OAAA,CAAAE,SAAA;YAAAkE,QAAA,gBACEpE,OAAA,CAACtD,GAAG;cAAC2J,OAAO,EAAC,MAAM;cAACE,cAAc,EAAC,QAAQ;cAACwB,EAAE,EAAE,CAAE;cAAA3D,QAAA,gBAChDpE,OAAA,CAACvD,MAAM;gBACLqK,OAAO,EAAExF,OAAO,GAAG,WAAW,GAAG,UAAW;gBAC5C6F,OAAO,EAAEA,CAAA,KAAM;kBACb5F,UAAU,CAAC,IAAI,CAAC;kBAChBE,aAAa,CAAC,CAAC,CAAC,CAAC;kBACjBiB,UAAU,CAAC,CAAC;gBACd,CAAE;gBACF8B,EAAE,EAAE;kBAAE+D,EAAE,EAAE,CAAC;kBAAEtD,YAAY,EAAE;gBAAE,CAAE;gBAAAb,QAAA,EAChC;cAED;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACT7E,OAAA,CAACvD,MAAM;gBACLqK,OAAO,EAAE,CAACxF,OAAO,GAAG,WAAW,GAAG,UAAW;gBAC7C6F,OAAO,EAAEA,CAAA,KAAM;kBACb5F,UAAU,CAAC,KAAK,CAAC;kBACjBE,aAAa,CAAC,CAAC,CAAC,CAAC;kBACjBiB,UAAU,CAAC,CAAC;gBACd,CAAE;gBACF8B,EAAE,EAAE;kBAAES,YAAY,EAAE;gBAAE,CAAE;gBAAAb,QAAA,EACzB;cAED;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,EAELpC,KAAK,iBACJzC,OAAA,CAACvC,KAAK;cAACsE,QAAQ,EAAC,OAAO;cAACyC,EAAE,EAAE;gBAAEuD,EAAE,EAAE,CAAC;gBAAEjD,QAAQ,EAAE,GAAG;gBAAEgD,EAAE,EAAE;cAAO,CAAE;cAAA1D,QAAA,EAC9D3B;YAAK;cAAAiC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CACR;UAAA,eACD,CACH,EAEA,CAACxC,eAAe,IAAIf,OAAO,gBAC1BtB,OAAA,CAAC/C,IAAI;YAACuH,EAAE,EAAE;cAAEM,QAAQ,EAAE,GAAG;cAAEgD,EAAE,EAAE;YAAO,CAAE;YAAA1D,QAAA,eACtCpE,OAAA,CAAC9C,WAAW;cAAAkH,QAAA,gBACVpE,OAAA,CAACrD,UAAU;gBAACmK,OAAO,EAAC,IAAI;gBAACqB,YAAY;gBAACH,SAAS,EAAC,QAAQ;gBAAA5D,QAAA,EAAC;cAEzD;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACb7E,OAAA,CAACtD,GAAG;gBAAC8L,SAAS,EAAC,MAAM;gBAACC,QAAQ,EAAE9E,WAAY;gBAAAS,QAAA,gBAC1CpE,OAAA,CAAClD,SAAS;kBACRiI,SAAS;kBACT6C,KAAK,EAAC,OAAO;kBACbc,IAAI,EAAC,OAAO;kBACZrE,KAAK,EAAEvD,SAAS,CAACE,KAAM;kBACvBuG,QAAQ,EAAG3D,CAAC,IAAK;oBACf7C,YAAY,CAAC;sBAAE,GAAGD,SAAS;sBAAEE,KAAK,EAAE4C,CAAC,CAAC+E,MAAM,CAACtE;oBAAM,CAAC,CAAC;oBACrD,IAAI7C,UAAU,CAACR,KAAK,EAAE;sBACpBS,aAAa,CAAC;wBAAE,GAAGD,UAAU;wBAAER,KAAK,EAAE;sBAAG,CAAC,CAAC;oBAC7C;kBACF,CAAE;kBACF4H,MAAM,EAAC,QAAQ;kBACf9B,OAAO,EAAC,UAAU;kBAClB+B,YAAY,EAAC,OAAO;kBACpBpG,KAAK,EAAE,CAAC,CAACjB,UAAU,CAACR,KAAM;kBAC1B8H,UAAU,EAAEtH,UAAU,CAACR,KAAM;kBAC7B+H,QAAQ,EAAErH,YAAa;kBACvB8C,EAAE,EAAE;oBACF,0BAA0B,EAAE;sBAC1BS,YAAY,EAAE;oBAChB;kBACF,CAAE;kBACF+D,UAAU,EAAE;oBACVC,cAAc,eAAEjJ,OAAA,CAACd,SAAS;sBAACsF,EAAE,EAAE;wBAAE+D,EAAE,EAAE,CAAC;wBAAE3B,KAAK,EAAE;sBAAiB;oBAAE;sBAAAlC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBACtE;gBAAE;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACF7E,OAAA,CAAClD,SAAS;kBACRiI,SAAS;kBACT6C,KAAK,EAAC,UAAU;kBAChBc,IAAI,EAAE9H,YAAY,GAAG,MAAM,GAAG,UAAW;kBACzCyD,KAAK,EAAEvD,SAAS,CAACG,QAAS;kBAC1BsG,QAAQ,EAAG3D,CAAC,IAAK;oBACf7C,YAAY,CAAC;sBAAE,GAAGD,SAAS;sBAAEG,QAAQ,EAAE2C,CAAC,CAAC+E,MAAM,CAACtE;oBAAM,CAAC,CAAC;oBACxD,IAAI7C,UAAU,CAACP,QAAQ,EAAE;sBACvBQ,aAAa,CAAC;wBAAE,GAAGD,UAAU;wBAAEP,QAAQ,EAAE;sBAAG,CAAC,CAAC;oBAChD;kBACF,CAAE;kBACF2H,MAAM,EAAC,QAAQ;kBACf9B,OAAO,EAAC,UAAU;kBAClB+B,YAAY,EAAC,kBAAkB;kBAC/BpG,KAAK,EAAE,CAAC,CAACjB,UAAU,CAACP,QAAS;kBAC7B6H,UAAU,EAAEtH,UAAU,CAACP,QAAS;kBAChC8H,QAAQ,EAAErH,YAAa;kBACvB8C,EAAE,EAAE;oBACF,0BAA0B,EAAE;sBAC1BS,YAAY,EAAE;oBAChB;kBACF,CAAE;kBACF+D,UAAU,EAAE;oBACVC,cAAc,eAAEjJ,OAAA,CAACZ,QAAQ;sBAACoF,EAAE,EAAE;wBAAE+D,EAAE,EAAE,CAAC;wBAAE3B,KAAK,EAAE;sBAAiB;oBAAE;sBAAAlC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC;oBACpEqE,YAAY,eACVlJ,OAAA,CAACtC,UAAU;sBAACyJ,OAAO,EAAEA,CAAA,KAAMtG,eAAe,CAAC,CAACD,YAAY,CAAE;sBAAAwD,QAAA,EACvDxD,YAAY,gBAAGZ,OAAA,CAACR,iBAAiB;wBAAAkF,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,gBAAG7E,OAAA,CAACV,cAAc;wBAAAoF,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChD;kBAEhB;gBAAE;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACF7E,OAAA,CAACvD,MAAM;kBACLiM,IAAI,EAAC,QAAQ;kBACb3D,SAAS;kBACT+B,OAAO,EAAC,WAAW;kBACnBiC,QAAQ,EAAErH,YAAa;kBACvB8C,EAAE,EAAE;oBAAE8D,EAAE,EAAE,CAAC;oBAAEP,EAAE,EAAE,CAAC;oBAAE9C,YAAY,EAAE;kBAAE,CAAE;kBAAAb,QAAA,EAErC1C,YAAY,gBACX1B,OAAA,CAACnC,gBAAgB;oBAACwK,IAAI,EAAE,EAAG;oBAACzB,KAAK,EAAC;kBAAS;oBAAAlC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,GAE9C;gBACD;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,GACL,CAACxC,eAAe,iBAClBrC,OAAA,CAAC/C,IAAI;YAACuH,EAAE,EAAE;cAAEM,QAAQ,EAAE,GAAG;cAAEgD,EAAE,EAAE;YAAO,CAAE;YAAA1D,QAAA,eACtCpE,OAAA,CAAC9C,WAAW;cAAAkH,QAAA,gBACVpE,OAAA,CAACrD,UAAU;gBAACmK,OAAO,EAAC,IAAI;gBAACqB,YAAY;gBAACH,SAAS,EAAC,QAAQ;gBAAA5D,QAAA,EAAC;cAEzD;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACb7E,OAAA,CAACtD,GAAG;gBAAC8L,SAAS,EAAC,MAAM;gBAACC,QAAQ,EAAExE,YAAa;gBAAAG,QAAA,gBAC3CpE,OAAA,CAAClD,SAAS;kBACRiI,SAAS;kBACT6C,KAAK,EAAC,UAAU;kBAChBvD,KAAK,EAAEnD,UAAU,CAACE,QAAS;kBAC3BmG,QAAQ,EAAG3D,CAAC,IAAK;oBACfzC,aAAa,CAAC;sBAAE,GAAGD,UAAU;sBAAEE,QAAQ,EAAEwC,CAAC,CAAC+E,MAAM,CAACtE;oBAAM,CAAC,CAAC;oBAC1D,IAAI7C,UAAU,CAACJ,QAAQ,EAAE;sBACvBK,aAAa,CAAC;wBAAE,GAAGD,UAAU;wBAAEJ,QAAQ,EAAE;sBAAG,CAAC,CAAC;oBAChD;kBACF,CAAE;kBACFwH,MAAM,EAAC,QAAQ;kBACf9B,OAAO,EAAC,UAAU;kBAClB+B,YAAY,EAAC,UAAU;kBACvBpG,KAAK,EAAE,CAAC,CAACjB,UAAU,CAACJ,QAAS;kBAC7B0H,UAAU,EAAEtH,UAAU,CAACJ,QAAS;kBAChC2H,QAAQ,EAAErH,YAAa;kBACvB8C,EAAE,EAAE;oBACF,0BAA0B,EAAE;sBAC1BS,YAAY,EAAE;oBAChB;kBACF,CAAE;kBACF+D,UAAU,EAAE;oBACVC,cAAc,eAAEjJ,OAAA,CAAC9B,UAAU;sBAACsG,EAAE,EAAE;wBAAE+D,EAAE,EAAE,CAAC;wBAAE3B,KAAK,EAAE;sBAAiB;oBAAE;sBAAAlC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBACvE;gBAAE;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACF7E,OAAA,CAAClD,SAAS;kBACRiI,SAAS;kBACT6C,KAAK,EAAC,OAAO;kBACbc,IAAI,EAAC,OAAO;kBACZrE,KAAK,EAAEnD,UAAU,CAACF,KAAM;kBACxBuG,QAAQ,EAAG3D,CAAC,IAAK;oBACfzC,aAAa,CAAC;sBAAE,GAAGD,UAAU;sBAAEF,KAAK,EAAE4C,CAAC,CAAC+E,MAAM,CAACtE;oBAAM,CAAC,CAAC;oBACvD,IAAI7C,UAAU,CAACR,KAAK,EAAE;sBACpBS,aAAa,CAAC;wBAAE,GAAGD,UAAU;wBAAER,KAAK,EAAE;sBAAG,CAAC,CAAC;oBAC7C;kBACF,CAAE;kBACF4H,MAAM,EAAC,QAAQ;kBACf9B,OAAO,EAAC,UAAU;kBAClB+B,YAAY,EAAC,OAAO;kBACpBpG,KAAK,EAAE,CAAC,CAACjB,UAAU,CAACR,KAAM;kBAC1B8H,UAAU,EAAEtH,UAAU,CAACR,KAAM;kBAC7B+H,QAAQ,EAAErH,YAAa;kBACvB8C,EAAE,EAAE;oBACF,0BAA0B,EAAE;sBAC1BS,YAAY,EAAE;oBAChB;kBACF,CAAE;kBACF+D,UAAU,EAAE;oBACVC,cAAc,eAAEjJ,OAAA,CAACd,SAAS;sBAACsF,EAAE,EAAE;wBAAE+D,EAAE,EAAE,CAAC;wBAAE3B,KAAK,EAAE;sBAAiB;oBAAE;sBAAAlC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBACtE;gBAAE;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACF7E,OAAA,CAAClD,SAAS;kBACRiI,SAAS;kBACT6C,KAAK,EAAC,UAAU;kBAChBc,IAAI,EAAE9H,YAAY,GAAG,MAAM,GAAG,UAAW;kBACzCyD,KAAK,EAAEnD,UAAU,CAACD,QAAS;kBAC3BsG,QAAQ,EAAG3D,CAAC,IAAK;oBACfzC,aAAa,CAAC;sBAAE,GAAGD,UAAU;sBAAED,QAAQ,EAAE2C,CAAC,CAAC+E,MAAM,CAACtE;oBAAM,CAAC,CAAC;oBAC1D,IAAI7C,UAAU,CAACP,QAAQ,EAAE;sBACvBQ,aAAa,CAAC;wBAAE,GAAGD,UAAU;wBAAEP,QAAQ,EAAE;sBAAG,CAAC,CAAC;oBAChD;kBACF,CAAE;kBACF2H,MAAM,EAAC,QAAQ;kBACf9B,OAAO,EAAC,UAAU;kBAClB+B,YAAY,EAAC,cAAc;kBAC3BpG,KAAK,EAAE,CAAC,CAACjB,UAAU,CAACP,QAAS;kBAC7B6H,UAAU,EAAEtH,UAAU,CAACP,QAAS;kBAChC8H,QAAQ,EAAErH,YAAa;kBACvB8C,EAAE,EAAE;oBACF,0BAA0B,EAAE;sBAC1BS,YAAY,EAAE;oBAChB;kBACF,CAAE;kBACF+D,UAAU,EAAE;oBACVC,cAAc,eAAEjJ,OAAA,CAACZ,QAAQ;sBAACoF,EAAE,EAAE;wBAAE+D,EAAE,EAAE,CAAC;wBAAE3B,KAAK,EAAE;sBAAiB;oBAAE;sBAAAlC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC;oBACpEqE,YAAY,eACVlJ,OAAA,CAACtC,UAAU;sBAACyJ,OAAO,EAAEA,CAAA,KAAMtG,eAAe,CAAC,CAACD,YAAY,CAAE;sBAAAwD,QAAA,EACvDxD,YAAY,gBAAGZ,OAAA,CAACR,iBAAiB;wBAAAkF,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,gBAAG7E,OAAA,CAACV,cAAc;wBAAAoF,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChD;kBAEhB;gBAAE;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACF7E,OAAA,CAAClD,SAAS;kBACRiI,SAAS;kBACT6C,KAAK,EAAC,kBAAkB;kBACxBc,IAAI,EAAC,UAAU;kBACfrE,KAAK,EAAEnD,UAAU,CAACG,eAAgB;kBAClCkG,QAAQ,EAAG3D,CAAC,IAAK;oBACfzC,aAAa,CAAC;sBAAE,GAAGD,UAAU;sBAAEG,eAAe,EAAEuC,CAAC,CAAC+E,MAAM,CAACtE;oBAAM,CAAC,CAAC;oBACjE,IAAI7C,UAAU,CAACH,eAAe,EAAE;sBAC9BI,aAAa,CAAC;wBAAE,GAAGD,UAAU;wBAAEH,eAAe,EAAE;sBAAG,CAAC,CAAC;oBACvD;kBACF,CAAE;kBACFuH,MAAM,EAAC,QAAQ;kBACf9B,OAAO,EAAC,UAAU;kBAClB+B,YAAY,EAAC,cAAc;kBAC3BpG,KAAK,EAAE,CAAC,CAACjB,UAAU,CAACH,eAAgB;kBACpCyH,UAAU,EAAEtH,UAAU,CAACH,eAAgB;kBACvC0H,QAAQ,EAAErH,YAAa;kBACvB8C,EAAE,EAAE;oBACF,0BAA0B,EAAE;sBAC1BS,YAAY,EAAE;oBAChB;kBACF,CAAE;kBACF+D,UAAU,EAAE;oBACVC,cAAc,eAAEjJ,OAAA,CAACZ,QAAQ;sBAACoF,EAAE,EAAE;wBAAE+D,EAAE,EAAE,CAAC;wBAAE3B,KAAK,EAAE;sBAAiB;oBAAE;sBAAAlC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBACrE;gBAAE;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACF7E,OAAA,CAACvD,MAAM;kBACLiM,IAAI,EAAC,QAAQ;kBACb3D,SAAS;kBACT+B,OAAO,EAAC,WAAW;kBACnBiC,QAAQ,EAAErH,YAAa;kBACvB8C,EAAE,EAAE;oBAAE8D,EAAE,EAAE,CAAC;oBAAEP,EAAE,EAAE,CAAC;oBAAE9C,YAAY,EAAE;kBAAE,CAAE;kBAAAb,QAAA,EAErC1C,YAAY,gBACX1B,OAAA,CAACnC,gBAAgB;oBAACwK,IAAI,EAAE,EAAG;oBAACzB,KAAK,EAAC;kBAAS;oBAAAlC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,GAE9C;gBACD;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CACP;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,eAGX7E,OAAA,CAACmE,QAAQ;UAACE,KAAK,EAAE3D,SAAU;UAAC4D,KAAK,EAAE,CAAE;UAAAF,QAAA,eACnCpE,OAAA,CAACtD,GAAG;YAACoI,QAAQ,EAAE,GAAI;YAACgD,EAAE,EAAC,MAAM;YAAA1D,QAAA,gBAC3BpE,OAAA,CAACrD,UAAU;cAACmK,OAAO,EAAC,IAAI;cAACqB,YAAY;cAACpB,UAAU,EAAC,MAAM;cAACvC,EAAE,EAAE;gBAAEuD,EAAE,EAAE,CAAC;gBAAEC,SAAS,EAAE;cAAS,CAAE;cAAA5D,QAAA,EAAC;YAE5F;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAEb7E,OAAA,CAACpC,IAAI;cAACuL,SAAS;cAACC,OAAO,EAAE,CAAE;cAAAhF,QAAA,gBAEzBpE,OAAA,CAACpC,IAAI;gBAACyL,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAACC,EAAE,EAAE,CAAE;gBAAAnF,QAAA,eACvBpE,OAAA,CAAC/C,IAAI;kBAACuH,EAAE,EAAE;oBACRwB,MAAM,EAAE,MAAM;oBACdb,UAAU,EAAGC,KAAK,IAAKA,KAAK,CAACC,OAAO,CAACC,IAAI,KAAK,MAAM,GAChD,oFAAoF,GACpF,sFAAsF;oBAC1FE,MAAM,EAAE;kBACV,CAAE;kBAAApB,QAAA,eACApE,OAAA,CAAC9C,WAAW;oBAACsH,EAAE,EAAE;sBAAE4B,CAAC,EAAE;oBAAE,CAAE;oBAAAhC,QAAA,gBACxBpE,OAAA,CAACtD,GAAG;sBAAC2J,OAAO,EAAC,MAAM;sBAACC,UAAU,EAAC,QAAQ;sBAACI,GAAG,EAAE,CAAE;sBAACqB,EAAE,EAAE,CAAE;sBAAA3D,QAAA,gBACpDpE,OAAA,CAAC1B,YAAY;wBAACsI,KAAK,EAAC,SAAS;wBAACpC,EAAE,EAAE;0BAAEqC,QAAQ,EAAE;wBAAG;sBAAE;wBAAAnC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eACtD7E,OAAA,CAACtD,GAAG;wBAAA0H,QAAA,gBACFpE,OAAA,CAACrD,UAAU;0BAACmK,OAAO,EAAC,IAAI;0BAACC,UAAU,EAAC,KAAK;0BAAA3C,QAAA,EAAC;wBAE1C;0BAAAM,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAY,CAAC,eACb7E,OAAA,CAACrD,UAAU;0BAACmK,OAAO,EAAC,OAAO;0BAACF,KAAK,EAAC,gBAAgB;0BAAAxC,QAAA,EAAC;wBAEnD;0BAAAM,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAY,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACV,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACN7E,OAAA,CAACtD,GAAG;sBAAC2J,OAAO,EAAC,MAAM;sBAACmD,aAAa,EAAC,QAAQ;sBAAC9C,GAAG,EAAE,CAAE;sBAAAtC,QAAA,EAC/C,CACC;wBAAEqF,IAAI,EAAE,IAAI;wBAAE7B,KAAK,EAAE,cAAc;wBAAE8B,IAAI,EAAE;sBAAU,CAAC,EACtD;wBAAED,IAAI,EAAE,IAAI;wBAAE7B,KAAK,EAAE,cAAc;wBAAE8B,IAAI,EAAE;sBAAS,CAAC,EACrD;wBAAED,IAAI,EAAE,IAAI;wBAAE7B,KAAK,EAAE,eAAe;wBAAE8B,IAAI,EAAE;sBAAS,CAAC,EACtD;wBAAED,IAAI,EAAE,IAAI;wBAAE7B,KAAK,EAAE,cAAc;wBAAE8B,IAAI,EAAE;sBAAU,CAAC,EACtD;wBAAED,IAAI,EAAE,IAAI;wBAAE7B,KAAK,EAAE,cAAc;wBAAE8B,IAAI,EAAE;sBAAS,CAAC,CACtD,CAACC,GAAG,CAAEC,IAAI,iBACT5J,OAAA,CAACvD,MAAM;wBAELqK,OAAO,EAAE7E,QAAQ,KAAK2H,IAAI,CAACH,IAAI,GAAG,WAAW,GAAG,UAAW;wBAC3DtC,OAAO,EAAEA,CAAA,KAAMhF,WAAW,CAACyH,IAAI,CAACH,IAAI,CAAE;wBACtC1E,SAAS;wBACTP,EAAE,EAAE;0BACFS,YAAY,EAAE,CAAC;0BACfsB,cAAc,EAAE,YAAY;0BAC5B9B,EAAE,EAAE,GAAG;0BACPU,UAAU,EAAElD,QAAQ,KAAK2H,IAAI,CAACH,IAAI,GAC9B,mDAAmD,GACnD,aAAa;0BACjB,SAAS,EAAE;4BACTtE,UAAU,EAAElD,QAAQ,KAAK2H,IAAI,CAACH,IAAI,GAC9B,mDAAmD,GACnD;0BACN;wBACF,CAAE;wBAAArF,QAAA,EAEDwF,IAAI,CAAChC;sBAAK,GAlBNgC,IAAI,CAACH,IAAI;wBAAA/E,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAmBR,CACT;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACK;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGP7E,OAAA,CAACpC,IAAI;gBAACyL,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAACC,EAAE,EAAE,CAAE;gBAAAnF,QAAA,eACvBpE,OAAA,CAAC/C,IAAI;kBAACuH,EAAE,EAAE;oBACRwB,MAAM,EAAE,MAAM;oBACdb,UAAU,EAAGC,KAAK,IAAKA,KAAK,CAACC,OAAO,CAACC,IAAI,KAAK,MAAM,GAChD,kFAAkF,GAClF,oFAAoF;oBACxFE,MAAM,EAAE;kBACV,CAAE;kBAAApB,QAAA,eACApE,OAAA,CAAC9C,WAAW;oBAACsH,EAAE,EAAE;sBAAE4B,CAAC,EAAE;oBAAE,CAAE;oBAAAhC,QAAA,gBACxBpE,OAAA,CAACtD,GAAG;sBAAC2J,OAAO,EAAC,MAAM;sBAACC,UAAU,EAAC,QAAQ;sBAACI,GAAG,EAAE,CAAE;sBAACqB,EAAE,EAAE,CAAE;sBAAA3D,QAAA,gBACpDpE,OAAA,CAACxB,WAAW;wBAACoI,KAAK,EAAC,OAAO;wBAACpC,EAAE,EAAE;0BAAEqC,QAAQ,EAAE;wBAAG;sBAAE;wBAAAnC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eACnD7E,OAAA,CAACtD,GAAG;wBAAA0H,QAAA,gBACFpE,OAAA,CAACrD,UAAU;0BAACmK,OAAO,EAAC,IAAI;0BAACC,UAAU,EAAC,KAAK;0BAAA3C,QAAA,EAAC;wBAE1C;0BAAAM,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAY,CAAC,eACb7E,OAAA,CAACrD,UAAU;0BAACmK,OAAO,EAAC,OAAO;0BAACF,KAAK,EAAC,gBAAgB;0BAAAxC,QAAA,EAAC;wBAEnD;0BAAAM,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAY,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACV,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACN7E,OAAA,CAACtD,GAAG;sBAAC2J,OAAO,EAAC,MAAM;sBAACmD,aAAa,EAAC,QAAQ;sBAAC9C,GAAG,EAAE,CAAE;sBAAAtC,QAAA,EAC/C,CACC;wBAAEiE,IAAI,EAAE,OAAO;wBAAET,KAAK,EAAE,OAAO;wBAAEiC,WAAW,EAAE;sBAAoB,CAAC,EACnE;wBAAExB,IAAI,EAAE,QAAQ;wBAAET,KAAK,EAAE,kBAAkB;wBAAEiC,WAAW,EAAE;sBAAqB,CAAC,EAChF;wBAAExB,IAAI,EAAE,OAAO;wBAAET,KAAK,EAAE,OAAO;wBAAEiC,WAAW,EAAE;sBAAuB,CAAC,CACvE,CAACF,GAAG,CAAEG,UAAU,iBACf9J,OAAA,CAACvD,MAAM;wBAELqK,OAAO,EAAEgD,UAAU,CAACzB,IAAI,KAAK,QAAQ,GAAG,WAAW,GAAG,UAAW;wBACjEtD,SAAS;wBACTP,EAAE,EAAE;0BACFS,YAAY,EAAE,CAAC;0BACfsB,cAAc,EAAE,YAAY;0BAC5B9B,EAAE,EAAE,GAAG;0BACP+E,aAAa,EAAE,QAAQ;0BACvBlD,UAAU,EAAE,YAAY;0BACxBnB,UAAU,EAAE2E,UAAU,CAACzB,IAAI,KAAK,QAAQ,GACpC,mDAAmD,GACnD,aAAa;0BACjB,SAAS,EAAE;4BACTlD,UAAU,EAAE2E,UAAU,CAACzB,IAAI,KAAK,QAAQ,GACpC,mDAAmD,GACnD;0BACN;wBACF,CAAE;wBAAAjE,QAAA,gBAEFpE,OAAA,CAACrD,UAAU;0BAACmK,OAAO,EAAC,WAAW;0BAACC,UAAU,EAAC,KAAK;0BAAA3C,QAAA,EAC7C0F,UAAU,CAAClC;wBAAK;0BAAAlD,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACP,CAAC,eACb7E,OAAA,CAACrD,UAAU;0BAACmK,OAAO,EAAC,SAAS;0BAACF,KAAK,EAAC,gBAAgB;0BAAAxC,QAAA,EACjD0F,UAAU,CAACD;wBAAW;0BAAAnF,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACb,CAAC;sBAAA,GAxBRiF,UAAU,CAACzB,IAAI;wBAAA3D,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAyBd,CACT;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACK;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGX7E,OAAA,CAACmE,QAAQ;UAACE,KAAK,EAAE3D,SAAU;UAAC4D,KAAK,EAAE,CAAE;UAAAF,QAAA,eACnCpE,OAAA,CAACtD,GAAG;YAACoI,QAAQ,EAAE,GAAI;YAACgD,EAAE,EAAC,MAAM;YAACE,SAAS,EAAC,QAAQ;YAAA5D,QAAA,gBAC9CpE,OAAA,CAACtD,GAAG;cACF8H,EAAE,EAAE;gBACFW,UAAU,EAAE,mDAAmD;gBAC/DF,YAAY,EAAE,KAAK;gBACnB0B,KAAK,EAAE,EAAE;gBACTX,MAAM,EAAE,EAAE;gBACVK,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,QAAQ;gBACpBC,cAAc,EAAE,QAAQ;gBACxBuB,EAAE,EAAE,MAAM;gBACVC,EAAE,EAAE;cACN,CAAE;cAAA3D,QAAA,eAEFpE,OAAA,CAAC5B,YAAY;gBAACoG,EAAE,EAAE;kBAAEoC,KAAK,EAAE,OAAO;kBAAEC,QAAQ,EAAE;gBAAG;cAAE;gBAAAnC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnD,CAAC,eAEN7E,OAAA,CAACrD,UAAU;cAACmK,OAAO,EAAC,IAAI;cAACqB,YAAY;cAACpB,UAAU,EAAC,MAAM;cAAA3C,QAAA,EAAC;YAExD;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAEb7E,OAAA,CAACrD,UAAU;cAACmK,OAAO,EAAC,IAAI;cAACF,KAAK,EAAC,gBAAgB;cAACuB,YAAY;cAAA/D,QAAA,EAAC;YAE7D;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAEb7E,OAAA,CAACrD,UAAU;cAACmK,OAAO,EAAC,OAAO;cAACtC,EAAE,EAAE;gBAAEuD,EAAE,EAAE,CAAC;gBAAEgC,UAAU,EAAE;cAAI,CAAE;cAAA3F,QAAA,EAAC;YAI5D;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAEb7E,OAAA,CAAC/C,IAAI;cAACuH,EAAE,EAAE;gBAAEuD,EAAE,EAAE;cAAE,CAAE;cAAA3D,QAAA,eAClBpE,OAAA,CAAC9C,WAAW;gBAAAkH,QAAA,gBACVpE,OAAA,CAACrD,UAAU;kBAACmK,OAAO,EAAC,IAAI;kBAACqB,YAAY;kBAAA/D,QAAA,EAAC;gBAEtC;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACb7E,OAAA,CAAC5C,IAAI;kBAAAgH,QAAA,gBACHpE,OAAA,CAAC3C,QAAQ;oBAAA+G,QAAA,gBACPpE,OAAA,CAAC1C,YAAY;sBAAA8G,QAAA,eACXpE,OAAA,CAAClB,SAAS;wBAAC8H,KAAK,EAAC;sBAAS;wBAAAlC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjB,CAAC,eACf7E,OAAA,CAACzC,YAAY;sBAACyM,OAAO,EAAC;oBAAgC;sBAAAtF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjD,CAAC,eACX7E,OAAA,CAAC3C,QAAQ;oBAAA+G,QAAA,gBACPpE,OAAA,CAAC1C,YAAY;sBAAA8G,QAAA,eACXpE,OAAA,CAAClB,SAAS;wBAAC8H,KAAK,EAAC;sBAAS;wBAAAlC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjB,CAAC,eACf7E,OAAA,CAACzC,YAAY;sBAACyM,OAAO,EAAC;oBAAgC;sBAAAtF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjD,CAAC,eACX7E,OAAA,CAAC3C,QAAQ;oBAAA+G,QAAA,gBACPpE,OAAA,CAAC1C,YAAY;sBAAA8G,QAAA,eACXpE,OAAA,CAAClB,SAAS;wBAAC8H,KAAK,EAAC;sBAAS;wBAAAlC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjB,CAAC,eACf7E,OAAA,CAACzC,YAAY;sBAACyM,OAAO,EAAC;oBAA4B;sBAAAtF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7C,CAAC,eACX7E,OAAA,CAAC3C,QAAQ;oBAAA+G,QAAA,gBACPpE,OAAA,CAAC1C,YAAY;sBAAA8G,QAAA,eACXpE,OAAA,CAAClB,SAAS;wBAAC8H,KAAK,EAAC;sBAAS;wBAAAlC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjB,CAAC,eACf7E,OAAA,CAACzC,YAAY;sBAACyM,OAAO,EAAC;oBAAgC;sBAAAtF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAEP7E,OAAA,CAACrD,UAAU;cAACmK,OAAO,EAAC,OAAO;cAACF,KAAK,EAAC,gBAAgB;cAAAxC,QAAA,GAAC,mDAEjD,eAAApE,OAAA;gBAAA0E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,gDAER;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACO,CAAC,eAGhB7E,OAAA,CAAClC,QAAQ;MACPyC,IAAI,EAAEqB,QAAQ,CAACrB,IAAK;MACpB0J,gBAAgB,EAAE,IAAK;MACvBzJ,OAAO,EAAEuC,mBAAoB;MAC7BmH,YAAY,EAAE;QAAEC,QAAQ,EAAE,QAAQ;QAAEC,UAAU,EAAE;MAAS,CAAE;MAAAhG,QAAA,eAE3DpE,OAAA,CAACvC,KAAK;QACJ+C,OAAO,EAAEuC,mBAAoB;QAC7BhB,QAAQ,EAAEH,QAAQ,CAACG,QAAS;QAC5ByC,EAAE,EAAE;UAAEmC,KAAK,EAAE;QAAO,CAAE;QAAAvC,QAAA,EAErBxC,QAAQ,CAACE;MAAO;QAAA4C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEb,CAAC;EAAA,QAlzB6DjF,OAAO,EACSC,OAAO;AAAA,EAizBpF,CAAC;EAAA,QAlzB4DD,OAAO,EACSC,OAAO;AAAA,EAizBnF;AAACwK,GAAA,GAj0BGlK,cAAc;AAm0BpBA,cAAc,CAACmK,WAAW,GAAG,gBAAgB;AAE7C,eAAenK,cAAc;AAAC,IAAAG,EAAA,EAAA+J,GAAA;AAAAE,YAAA,CAAAjK,EAAA;AAAAiK,YAAA,CAAAF,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}