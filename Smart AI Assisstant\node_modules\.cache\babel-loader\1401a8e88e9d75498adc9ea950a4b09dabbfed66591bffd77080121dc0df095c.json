{"ast": null, "code": "var _jsxFileName = \"E:\\\\PythonProjects\\\\React.js\\\\Smart AI Assisstant\\\\src\\\\components\\\\SettingsDialog.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Dialog, DialogTitle, DialogContent, DialogActions, Button, Box, Typography, Tabs, Tab, TextField, Switch, FormControlLabel, Card, CardContent, Chip, List, ListItem, ListItemIcon, ListItemText, Divider, Alert, IconButton, Avatar } from '@mui/material';\nimport { Settings as SettingsIcon, Person as PersonIcon, Security as SecurityIcon, Language as LanguageIcon, Palette as PaletteIcon, Info as InfoIcon, Star as StarIcon, Check as CheckIcon, Close as CloseIcon, Email as EmailIcon, Lock as LockIcon, Visibility as VisibilityIcon, VisibilityOff as VisibilityOffIcon, Crown as CrownIcon, Diamond as DiamondIcon } from '@mui/icons-material';\nimport { useScan } from '../contexts/ScanContext';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst SettingsDialog = /*#__PURE__*/_s(/*#__PURE__*/React.memo(_c = _s(({\n  open,\n  onClose\n}) => {\n  _s();\n  const [activeTab, setActiveTab] = useState(0);\n  const [showPassword, setShowPassword] = useState(false);\n  const [loginForm, setLoginForm] = useState({\n    email: '',\n    password: ''\n  });\n  const [signupForm, setSignupForm] = useState({\n    name: '',\n    email: '',\n    password: '',\n    confirmPassword: ''\n  });\n  const [isLogin, setIsLogin] = useState(true);\n  const {\n    darkMode,\n    language,\n    toggleDarkMode,\n    setLanguage\n  } = useScan();\n  const handleTabChange = (event, newValue) => {\n    setActiveTab(newValue);\n  };\n  const pricingPlans = [{\n    name: 'Free Plan',\n    price: '$0',\n    period: 'forever',\n    description: 'Perfect for personal use',\n    features: ['10 URL scans per day', '5 file scans per day', 'Basic threat detection', 'Standard reports', 'Email support'],\n    limitations: ['Limited scan history (7 days)', 'Basic threat analysis', 'No API access'],\n    color: 'primary',\n    icon: /*#__PURE__*/_jsxDEV(SecurityIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 83,\n      columnNumber: 13\n    }, this),\n    popular: false\n  }, {\n    name: 'Pro Plan',\n    price: '$29',\n    period: 'per month',\n    description: 'Advanced security for professionals',\n    features: ['Unlimited URL scans', 'Unlimited file scans', 'Advanced AI threat detection', 'Detailed PDF reports', 'Priority support', 'API access', 'Custom integrations', 'Advanced analytics', 'Team collaboration', '90-day scan history'],\n    limitations: [],\n    color: 'secondary',\n    icon: /*#__PURE__*/_jsxDEV(CrownIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 105,\n      columnNumber: 13\n    }, this),\n    popular: true\n  }];\n  const handleLogin = e => {\n    e.preventDefault();\n    // Implement login logic here\n    console.log('Login:', loginForm);\n  };\n  const handleSignup = e => {\n    e.preventDefault();\n    // Implement signup logic here\n    console.log('Signup:', signupForm);\n  };\n  const TabPanel = ({\n    children,\n    value,\n    index\n  }) => /*#__PURE__*/_jsxDEV(\"div\", {\n    hidden: value !== index,\n    children: value === index && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        py: 3\n      },\n      children: children\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 124,\n      columnNumber: 27\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 123,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(Dialog, {\n    open: open,\n    onClose: onClose,\n    maxWidth: \"md\",\n    fullWidth: true,\n    PaperProps: {\n      sx: {\n        borderRadius: 3,\n        minHeight: '70vh'\n      }\n    },\n    children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        display: \"flex\",\n        alignItems: \"center\",\n        justifyContent: \"space-between\",\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          display: \"flex\",\n          alignItems: \"center\",\n          gap: 2,\n          children: [/*#__PURE__*/_jsxDEV(SettingsIcon, {\n            color: \"primary\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 144,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h5\",\n            fontWeight: \"bold\",\n            children: \"Settings & Account\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 145,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 143,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: onClose,\n          children: /*#__PURE__*/_jsxDEV(CloseIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 150,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 149,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 142,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 141,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n      sx: {\n        p: 0\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          borderBottom: 1,\n          borderColor: 'divider'\n        },\n        children: /*#__PURE__*/_jsxDEV(Tabs, {\n          value: activeTab,\n          onChange: handleTabChange,\n          variant: \"fullWidth\",\n          textColor: \"primary\",\n          indicatorColor: \"primary\",\n          children: [/*#__PURE__*/_jsxDEV(Tab, {\n            icon: /*#__PURE__*/_jsxDEV(PersonIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 164,\n              columnNumber: 24\n            }, this),\n            label: \"Account\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 164,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Tab, {\n            icon: /*#__PURE__*/_jsxDEV(PaletteIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 165,\n              columnNumber: 24\n            }, this),\n            label: \"Preferences\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 165,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Tab, {\n            icon: /*#__PURE__*/_jsxDEV(StarIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 166,\n              columnNumber: 24\n            }, this),\n            label: \"Plans\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 166,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Tab, {\n            icon: /*#__PURE__*/_jsxDEV(InfoIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 167,\n              columnNumber: 24\n            }, this),\n            label: \"About\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 167,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 157,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 156,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          p: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(TabPanel, {\n          value: activeTab,\n          index: 0,\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            display: \"flex\",\n            justifyContent: \"center\",\n            mb: 3,\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              variant: isLogin ? 'contained' : 'outlined',\n              onClick: () => setIsLogin(true),\n              sx: {\n                mr: 1,\n                borderRadius: 3\n              },\n              children: \"Login\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 175,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: !isLogin ? 'contained' : 'outlined',\n              onClick: () => setIsLogin(false),\n              sx: {\n                borderRadius: 3\n              },\n              children: \"Sign Up\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 182,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 174,\n            columnNumber: 13\n          }, this), isLogin ? /*#__PURE__*/_jsxDEV(Card, {\n            sx: {\n              maxWidth: 400,\n              mx: 'auto'\n            },\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                gutterBottom: true,\n                textAlign: \"center\",\n                children: \"Welcome Back\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 194,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                component: \"form\",\n                onSubmit: handleLogin,\n                children: [/*#__PURE__*/_jsxDEV(TextField, {\n                  fullWidth: true,\n                  label: \"Email\",\n                  type: \"email\",\n                  value: loginForm.email,\n                  onChange: e => setLoginForm({\n                    ...loginForm,\n                    email: e.target.value\n                  }),\n                  margin: \"normal\",\n                  InputProps: {\n                    startAdornment: /*#__PURE__*/_jsxDEV(EmailIcon, {\n                      sx: {\n                        mr: 1,\n                        color: 'text.secondary'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 206,\n                      columnNumber: 41\n                    }, this)\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 198,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                  fullWidth: true,\n                  label: \"Password\",\n                  type: showPassword ? 'text' : 'password',\n                  value: loginForm.password,\n                  onChange: e => setLoginForm({\n                    ...loginForm,\n                    password: e.target.value\n                  }),\n                  margin: \"normal\",\n                  InputProps: {\n                    startAdornment: /*#__PURE__*/_jsxDEV(LockIcon, {\n                      sx: {\n                        mr: 1,\n                        color: 'text.secondary'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 217,\n                      columnNumber: 41\n                    }, this),\n                    endAdornment: /*#__PURE__*/_jsxDEV(IconButton, {\n                      onClick: () => setShowPassword(!showPassword),\n                      children: showPassword ? /*#__PURE__*/_jsxDEV(VisibilityOffIcon, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 220,\n                        columnNumber: 45\n                      }, this) : /*#__PURE__*/_jsxDEV(VisibilityIcon, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 220,\n                        columnNumber: 69\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 219,\n                      columnNumber: 27\n                    }, this)\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 209,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  type: \"submit\",\n                  fullWidth: true,\n                  variant: \"contained\",\n                  sx: {\n                    mt: 3,\n                    mb: 2,\n                    borderRadius: 3\n                  },\n                  children: \"Sign In\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 225,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 197,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 193,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 192,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(Card, {\n            sx: {\n              maxWidth: 400,\n              mx: 'auto'\n            },\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                gutterBottom: true,\n                textAlign: \"center\",\n                children: \"Create Account\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 239,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                component: \"form\",\n                onSubmit: handleSignup,\n                children: [/*#__PURE__*/_jsxDEV(TextField, {\n                  fullWidth: true,\n                  label: \"Full Name\",\n                  value: signupForm.name,\n                  onChange: e => setSignupForm({\n                    ...signupForm,\n                    name: e.target.value\n                  }),\n                  margin: \"normal\",\n                  InputProps: {\n                    startAdornment: /*#__PURE__*/_jsxDEV(PersonIcon, {\n                      sx: {\n                        mr: 1,\n                        color: 'text.secondary'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 250,\n                      columnNumber: 41\n                    }, this)\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 243,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                  fullWidth: true,\n                  label: \"Email\",\n                  type: \"email\",\n                  value: signupForm.email,\n                  onChange: e => setSignupForm({\n                    ...signupForm,\n                    email: e.target.value\n                  }),\n                  margin: \"normal\",\n                  InputProps: {\n                    startAdornment: /*#__PURE__*/_jsxDEV(EmailIcon, {\n                      sx: {\n                        mr: 1,\n                        color: 'text.secondary'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 261,\n                      columnNumber: 41\n                    }, this)\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 253,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                  fullWidth: true,\n                  label: \"Password\",\n                  type: showPassword ? 'text' : 'password',\n                  value: signupForm.password,\n                  onChange: e => setSignupForm({\n                    ...signupForm,\n                    password: e.target.value\n                  }),\n                  margin: \"normal\",\n                  InputProps: {\n                    startAdornment: /*#__PURE__*/_jsxDEV(LockIcon, {\n                      sx: {\n                        mr: 1,\n                        color: 'text.secondary'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 272,\n                      columnNumber: 41\n                    }, this),\n                    endAdornment: /*#__PURE__*/_jsxDEV(IconButton, {\n                      onClick: () => setShowPassword(!showPassword),\n                      children: showPassword ? /*#__PURE__*/_jsxDEV(VisibilityOffIcon, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 275,\n                        columnNumber: 45\n                      }, this) : /*#__PURE__*/_jsxDEV(VisibilityIcon, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 275,\n                        columnNumber: 69\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 274,\n                      columnNumber: 27\n                    }, this)\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 264,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                  fullWidth: true,\n                  label: \"Confirm Password\",\n                  type: \"password\",\n                  value: signupForm.confirmPassword,\n                  onChange: e => setSignupForm({\n                    ...signupForm,\n                    confirmPassword: e.target.value\n                  }),\n                  margin: \"normal\",\n                  InputProps: {\n                    startAdornment: /*#__PURE__*/_jsxDEV(LockIcon, {\n                      sx: {\n                        mr: 1,\n                        color: 'text.secondary'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 288,\n                      columnNumber: 41\n                    }, this)\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 280,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  type: \"submit\",\n                  fullWidth: true,\n                  variant: \"contained\",\n                  sx: {\n                    mt: 3,\n                    mb: 2,\n                    borderRadius: 3\n                  },\n                  children: \"Create Account\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 291,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 242,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 238,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 237,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 173,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TabPanel, {\n          value: activeTab,\n          index: 1,\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            maxWidth: 500,\n            mx: \"auto\",\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              gutterBottom: true,\n              children: \"Appearance & Language\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 308,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Card, {\n              sx: {\n                mb: 3\n              },\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  display: \"flex\",\n                  alignItems: \"center\",\n                  justifyContent: \"space-between\",\n                  mb: 2,\n                  children: [/*#__PURE__*/_jsxDEV(Box, {\n                    display: \"flex\",\n                    alignItems: \"center\",\n                    gap: 2,\n                    children: [/*#__PURE__*/_jsxDEV(PaletteIcon, {\n                      color: \"primary\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 316,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Box, {\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"subtitle1\",\n                        fontWeight: \"600\",\n                        children: \"Dark Mode\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 318,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"body2\",\n                        color: \"text.secondary\",\n                        children: \"Switch between light and dark themes\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 321,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 317,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 315,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Switch, {\n                    checked: darkMode,\n                    onChange: toggleDarkMode,\n                    color: \"primary\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 326,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 314,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 313,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 312,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Card, {\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  display: \"flex\",\n                  alignItems: \"center\",\n                  gap: 2,\n                  mb: 2,\n                  children: [/*#__PURE__*/_jsxDEV(LanguageIcon, {\n                    color: \"primary\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 338,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"subtitle1\",\n                      fontWeight: \"600\",\n                      children: \"Language\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 340,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      color: \"text.secondary\",\n                      children: \"Choose your preferred language\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 343,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 339,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 337,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  display: \"flex\",\n                  gap: 2,\n                  children: [/*#__PURE__*/_jsxDEV(Button, {\n                    variant: language === 'en' ? 'contained' : 'outlined',\n                    onClick: () => setLanguage('en'),\n                    sx: {\n                      borderRadius: 3\n                    },\n                    children: \"\\uD83C\\uDDFA\\uD83C\\uDDF8 English\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 349,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Button, {\n                    variant: language === 'ar' ? 'contained' : 'outlined',\n                    onClick: () => setLanguage('ar'),\n                    sx: {\n                      borderRadius: 3\n                    },\n                    children: \"\\uD83C\\uDDF8\\uD83C\\uDDE6 \\u0627\\u0644\\u0639\\u0631\\u0628\\u064A\\u0629\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 356,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 348,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 336,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 335,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 307,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 306,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TabPanel, {\n          value: activeTab,\n          index: 2,\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            textAlign: \"center\",\n            mb: 4,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h4\",\n              gutterBottom: true,\n              fontWeight: \"bold\",\n              children: \"Simple Pricing\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 372,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              color: \"text.secondary\",\n              children: \"Pay for what matters. Enjoy everything else.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 375,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 371,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            display: \"flex\",\n            justifyContent: \"center\",\n            gap: 3,\n            flexWrap: \"wrap\",\n            children: pricingPlans.map((plan, index) => /*#__PURE__*/_jsxDEV(Card, {\n              sx: {\n                maxWidth: 350,\n                position: 'relative',\n                border: plan.popular ? '2px solid' : '1px solid',\n                borderColor: plan.popular ? 'primary.main' : 'divider',\n                transform: plan.popular ? 'scale(1.05)' : 'scale(1)',\n                transition: 'transform 0.2s ease',\n                '&:hover': {\n                  transform: plan.popular ? 'scale(1.07)' : 'scale(1.02)'\n                }\n              },\n              children: [plan.popular && /*#__PURE__*/_jsxDEV(Chip, {\n                label: \"Most Popular\",\n                color: \"primary\",\n                sx: {\n                  position: 'absolute',\n                  top: -10,\n                  left: '50%',\n                  transform: 'translateX(-50%)',\n                  fontWeight: 'bold'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 397,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(CardContent, {\n                sx: {\n                  p: 4,\n                  textAlign: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    background: `linear-gradient(135deg, ${plan.color === 'primary' ? '#667eea 0%, #764ba2' : '#f093fb 0%, #f5576c'} 100%)`,\n                    borderRadius: '50%',\n                    width: 60,\n                    height: 60,\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    mx: 'auto',\n                    mb: 2\n                  },\n                  children: /*#__PURE__*/React.cloneElement(plan.icon, {\n                    sx: {\n                      color: 'white',\n                      fontSize: 28\n                    }\n                  })\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 411,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h5\",\n                  fontWeight: \"bold\",\n                  gutterBottom: true,\n                  children: plan.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 427,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  gutterBottom: true,\n                  children: plan.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 431,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  my: 3,\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"h3\",\n                    fontWeight: \"bold\",\n                    color: \"primary\",\n                    children: plan.price\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 436,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    color: \"text.secondary\",\n                    children: plan.period\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 439,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 435,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  gutterBottom: true,\n                  fontWeight: \"600\",\n                  sx: {\n                    mt: 3\n                  },\n                  children: \"Plan Highlights\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 444,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(List, {\n                  dense: true,\n                  children: plan.features.map((feature, idx) => /*#__PURE__*/_jsxDEV(ListItem, {\n                    sx: {\n                      px: 0\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                      sx: {\n                        minWidth: 32\n                      },\n                      children: /*#__PURE__*/_jsxDEV(CheckIcon, {\n                        color: \"success\",\n                        fontSize: \"small\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 452,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 451,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                      primary: feature,\n                      primaryTypographyProps: {\n                        variant: 'body2'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 454,\n                      columnNumber: 27\n                    }, this)]\n                  }, idx, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 450,\n                    columnNumber: 25\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 448,\n                  columnNumber: 21\n                }, this), plan.limitations.length > 0 && /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(Divider, {\n                    sx: {\n                      my: 2\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 464,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    color: \"text.secondary\",\n                    gutterBottom: true,\n                    children: \"Limitations:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 465,\n                    columnNumber: 25\n                  }, this), plan.limitations.map((limitation, idx) => /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    color: \"text.secondary\",\n                    display: \"block\",\n                    children: [\"\\u2022 \", limitation]\n                  }, idx, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 469,\n                    columnNumber: 27\n                  }, this))]\n                }, void 0, true), /*#__PURE__*/_jsxDEV(Button, {\n                  variant: plan.popular ? 'contained' : 'outlined',\n                  fullWidth: true,\n                  size: \"large\",\n                  sx: {\n                    mt: 3,\n                    borderRadius: 3\n                  },\n                  children: plan.price === '$0' ? 'Current Plan' : 'Upgrade Now'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 476,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 410,\n                columnNumber: 19\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 382,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 380,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"info\",\n            sx: {\n              mt: 4,\n              borderRadius: 2\n            },\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"30-day money-back guarantee\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 491,\n                columnNumber: 17\n              }, this), \" \\u2022 Cancel anytime \\u2022 No setup fees\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 490,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 489,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 370,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TabPanel, {\n          value: activeTab,\n          index: 3,\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            maxWidth: 600,\n            mx: \"auto\",\n            textAlign: \"center\",\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                borderRadius: '50%',\n                width: 80,\n                height: 80,\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                mx: 'auto',\n                mb: 3\n              },\n              children: /*#__PURE__*/_jsxDEV(SecurityIcon, {\n                sx: {\n                  color: 'white',\n                  fontSize: 40\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 512,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 499,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h4\",\n              gutterBottom: true,\n              fontWeight: \"bold\",\n              children: \"AI Security Guard\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 515,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              color: \"text.secondary\",\n              gutterBottom: true,\n              children: \"Version 1.0.0\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 519,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              sx: {\n                mb: 4,\n                lineHeight: 1.7\n              },\n              children: \"Advanced AI-powered security scanning platform providing comprehensive threat detection and analysis for URLs and files. Built with cutting-edge technology to protect your digital assets.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 523,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Card, {\n              sx: {\n                mb: 3\n              },\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  gutterBottom: true,\n                  children: \"Key Features\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 531,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(List, {\n                  children: [/*#__PURE__*/_jsxDEV(ListItem, {\n                    children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                      children: /*#__PURE__*/_jsxDEV(CheckIcon, {\n                        color: \"primary\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 537,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 536,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                      primary: \"Real-time URL threat detection\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 539,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 535,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n                    children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                      children: /*#__PURE__*/_jsxDEV(CheckIcon, {\n                        color: \"primary\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 543,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 542,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                      primary: \"Advanced file malware scanning\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 545,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 541,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n                    children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                      children: /*#__PURE__*/_jsxDEV(CheckIcon, {\n                        color: \"primary\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 549,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 548,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                      primary: \"AI-powered threat analysis\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 551,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 547,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n                    children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                      children: /*#__PURE__*/_jsxDEV(CheckIcon, {\n                        color: \"primary\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 555,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 554,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                      primary: \"Comprehensive security reports\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 557,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 553,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 534,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 530,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 529,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: [\"\\xA9 2024 AI Security Guard. All rights reserved.\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 565,\n                columnNumber: 17\n              }, this), \"Built with \\u2764\\uFE0F for digital security\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 563,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 498,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 497,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 171,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 155,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 129,\n    columnNumber: 5\n  }, this);\n}, \"Vfkhqx+POGp5lvC5cqaLI+W+oFs=\", false, function () {\n  return [useScan];\n})), \"Vfkhqx+POGp5lvC5cqaLI+W+oFs=\", false, function () {\n  return [useScan];\n});\n_c2 = SettingsDialog;\nSettingsDialog.displayName = 'SettingsDialog';\nexport default SettingsDialog;\nvar _c, _c2;\n$RefreshReg$(_c, \"SettingsDialog$React.memo\");\n$RefreshReg$(_c2, \"SettingsDialog\");", "map": {"version": 3, "names": ["React", "useState", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "<PERSON><PERSON>", "Box", "Typography", "Tabs", "Tab", "TextField", "Switch", "FormControlLabel", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Chip", "List", "ListItem", "ListItemIcon", "ListItemText", "Divider", "<PERSON><PERSON>", "IconButton", "Avatar", "Settings", "SettingsIcon", "Person", "PersonIcon", "Security", "SecurityIcon", "Language", "LanguageIcon", "Palette", "PaletteIcon", "Info", "InfoIcon", "Star", "StarIcon", "Check", "CheckIcon", "Close", "CloseIcon", "Email", "EmailIcon", "Lock", "LockIcon", "Visibility", "VisibilityIcon", "VisibilityOff", "VisibilityOffIcon", "Crown", "CrownIcon", "Diamond", "DiamondIcon", "useScan", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "SettingsDialog", "_s", "memo", "_c", "open", "onClose", "activeTab", "setActiveTab", "showPassword", "setShowPassword", "loginForm", "setLoginForm", "email", "password", "signupForm", "setSignupForm", "name", "confirmPassword", "is<PERSON>ogin", "setIsLogin", "darkMode", "language", "toggleDarkMode", "setLanguage", "handleTabChange", "event", "newValue", "pricingPlans", "price", "period", "description", "features", "limitations", "color", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "popular", "handleLogin", "e", "preventDefault", "console", "log", "handleSignup", "TabPanel", "children", "value", "index", "hidden", "sx", "py", "max<PERSON><PERSON><PERSON>", "fullWidth", "PaperProps", "borderRadius", "minHeight", "display", "alignItems", "justifyContent", "gap", "variant", "fontWeight", "onClick", "p", "borderBottom", "borderColor", "onChange", "textColor", "indicatorColor", "label", "mb", "mr", "mx", "gutterBottom", "textAlign", "component", "onSubmit", "type", "target", "margin", "InputProps", "startAdornment", "endAdornment", "mt", "checked", "flexWrap", "map", "plan", "position", "border", "transform", "transition", "top", "left", "background", "width", "height", "cloneElement", "fontSize", "my", "dense", "feature", "idx", "px", "min<PERSON><PERSON><PERSON>", "primary", "primaryTypographyProps", "length", "limitation", "size", "severity", "lineHeight", "_c2", "displayName", "$RefreshReg$"], "sources": ["E:/PythonProjects/React.js/Smart AI Assisstant/src/components/SettingsDialog.jsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  <PERSON>alog,\n  DialogTitle,\n  <PERSON>alogContent,\n  DialogActions,\n  Button,\n  Box,\n  Typography,\n  Tabs,\n  Tab,\n  TextField,\n  Switch,\n  FormControlLabel,\n  Card,\n  CardContent,\n  Chip,\n  List,\n  ListItem,\n  ListItemIcon,\n  ListItemText,\n  Divider,\n  Alert,\n  IconButton,\n  Avatar,\n} from '@mui/material';\nimport {\n  Settings as SettingsIcon,\n  Person as PersonIcon,\n  Security as SecurityIcon,\n  Language as LanguageIcon,\n  Palette as PaletteIcon,\n  Info as InfoIcon,\n  Star as StarIcon,\n  Check as CheckIcon,\n  Close as CloseIcon,\n  Email as EmailIcon,\n  Lock as LockIcon,\n  Visibility as VisibilityIcon,\n  VisibilityOff as VisibilityOffIcon,\n  Crown as CrownIcon,\n  Diamond as DiamondIcon,\n} from '@mui/icons-material';\nimport { useScan } from '../contexts/ScanContext';\n\nconst SettingsDialog = React.memo(({ open, onClose }) => {\n  const [activeTab, setActiveTab] = useState(0);\n  const [showPassword, setShowPassword] = useState(false);\n  const [loginForm, setLoginForm] = useState({ email: '', password: '' });\n  const [signupForm, setSignupForm] = useState({ \n    name: '', \n    email: '', \n    password: '', \n    confirmPassword: '' \n  });\n  const [isLogin, setIsLogin] = useState(true);\n  \n  const { darkMode, language, toggleDarkMode, setLanguage } = useScan();\n\n  const handleTabChange = (event, newValue) => {\n    setActiveTab(newValue);\n  };\n\n  const pricingPlans = [\n    {\n      name: 'Free Plan',\n      price: '$0',\n      period: 'forever',\n      description: 'Perfect for personal use',\n      features: [\n        '10 URL scans per day',\n        '5 file scans per day',\n        'Basic threat detection',\n        'Standard reports',\n        'Email support',\n      ],\n      limitations: [\n        'Limited scan history (7 days)',\n        'Basic threat analysis',\n        'No API access',\n      ],\n      color: 'primary',\n      icon: <SecurityIcon />,\n      popular: false,\n    },\n    {\n      name: 'Pro Plan',\n      price: '$29',\n      period: 'per month',\n      description: 'Advanced security for professionals',\n      features: [\n        'Unlimited URL scans',\n        'Unlimited file scans',\n        'Advanced AI threat detection',\n        'Detailed PDF reports',\n        'Priority support',\n        'API access',\n        'Custom integrations',\n        'Advanced analytics',\n        'Team collaboration',\n        '90-day scan history',\n      ],\n      limitations: [],\n      color: 'secondary',\n      icon: <CrownIcon />,\n      popular: true,\n    },\n  ];\n\n  const handleLogin = (e) => {\n    e.preventDefault();\n    // Implement login logic here\n    console.log('Login:', loginForm);\n  };\n\n  const handleSignup = (e) => {\n    e.preventDefault();\n    // Implement signup logic here\n    console.log('Signup:', signupForm);\n  };\n\n  const TabPanel = ({ children, value, index }) => (\n    <div hidden={value !== index}>\n      {value === index && <Box sx={{ py: 3 }}>{children}</Box>}\n    </div>\n  );\n\n  return (\n    <Dialog\n      open={open}\n      onClose={onClose}\n      maxWidth=\"md\"\n      fullWidth\n      PaperProps={{\n        sx: {\n          borderRadius: 3,\n          minHeight: '70vh',\n        },\n      }}\n    >\n      <DialogTitle>\n        <Box display=\"flex\" alignItems=\"center\" justifyContent=\"space-between\">\n          <Box display=\"flex\" alignItems=\"center\" gap={2}>\n            <SettingsIcon color=\"primary\" />\n            <Typography variant=\"h5\" fontWeight=\"bold\">\n              Settings & Account\n            </Typography>\n          </Box>\n          <IconButton onClick={onClose}>\n            <CloseIcon />\n          </IconButton>\n        </Box>\n      </DialogTitle>\n\n      <DialogContent sx={{ p: 0 }}>\n        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>\n          <Tabs\n            value={activeTab}\n            onChange={handleTabChange}\n            variant=\"fullWidth\"\n            textColor=\"primary\"\n            indicatorColor=\"primary\"\n          >\n            <Tab icon={<PersonIcon />} label=\"Account\" />\n            <Tab icon={<PaletteIcon />} label=\"Preferences\" />\n            <Tab icon={<StarIcon />} label=\"Plans\" />\n            <Tab icon={<InfoIcon />} label=\"About\" />\n          </Tabs>\n        </Box>\n\n        <Box sx={{ p: 3 }}>\n          {/* Account Tab */}\n          <TabPanel value={activeTab} index={0}>\n            <Box display=\"flex\" justifyContent=\"center\" mb={3}>\n              <Button\n                variant={isLogin ? 'contained' : 'outlined'}\n                onClick={() => setIsLogin(true)}\n                sx={{ mr: 1, borderRadius: 3 }}\n              >\n                Login\n              </Button>\n              <Button\n                variant={!isLogin ? 'contained' : 'outlined'}\n                onClick={() => setIsLogin(false)}\n                sx={{ borderRadius: 3 }}\n              >\n                Sign Up\n              </Button>\n            </Box>\n\n            {isLogin ? (\n              <Card sx={{ maxWidth: 400, mx: 'auto' }}>\n                <CardContent>\n                  <Typography variant=\"h6\" gutterBottom textAlign=\"center\">\n                    Welcome Back\n                  </Typography>\n                  <Box component=\"form\" onSubmit={handleLogin}>\n                    <TextField\n                      fullWidth\n                      label=\"Email\"\n                      type=\"email\"\n                      value={loginForm.email}\n                      onChange={(e) => setLoginForm({ ...loginForm, email: e.target.value })}\n                      margin=\"normal\"\n                      InputProps={{\n                        startAdornment: <EmailIcon sx={{ mr: 1, color: 'text.secondary' }} />,\n                      }}\n                    />\n                    <TextField\n                      fullWidth\n                      label=\"Password\"\n                      type={showPassword ? 'text' : 'password'}\n                      value={loginForm.password}\n                      onChange={(e) => setLoginForm({ ...loginForm, password: e.target.value })}\n                      margin=\"normal\"\n                      InputProps={{\n                        startAdornment: <LockIcon sx={{ mr: 1, color: 'text.secondary' }} />,\n                        endAdornment: (\n                          <IconButton onClick={() => setShowPassword(!showPassword)}>\n                            {showPassword ? <VisibilityOffIcon /> : <VisibilityIcon />}\n                          </IconButton>\n                        ),\n                      }}\n                    />\n                    <Button\n                      type=\"submit\"\n                      fullWidth\n                      variant=\"contained\"\n                      sx={{ mt: 3, mb: 2, borderRadius: 3 }}\n                    >\n                      Sign In\n                    </Button>\n                  </Box>\n                </CardContent>\n              </Card>\n            ) : (\n              <Card sx={{ maxWidth: 400, mx: 'auto' }}>\n                <CardContent>\n                  <Typography variant=\"h6\" gutterBottom textAlign=\"center\">\n                    Create Account\n                  </Typography>\n                  <Box component=\"form\" onSubmit={handleSignup}>\n                    <TextField\n                      fullWidth\n                      label=\"Full Name\"\n                      value={signupForm.name}\n                      onChange={(e) => setSignupForm({ ...signupForm, name: e.target.value })}\n                      margin=\"normal\"\n                      InputProps={{\n                        startAdornment: <PersonIcon sx={{ mr: 1, color: 'text.secondary' }} />,\n                      }}\n                    />\n                    <TextField\n                      fullWidth\n                      label=\"Email\"\n                      type=\"email\"\n                      value={signupForm.email}\n                      onChange={(e) => setSignupForm({ ...signupForm, email: e.target.value })}\n                      margin=\"normal\"\n                      InputProps={{\n                        startAdornment: <EmailIcon sx={{ mr: 1, color: 'text.secondary' }} />,\n                      }}\n                    />\n                    <TextField\n                      fullWidth\n                      label=\"Password\"\n                      type={showPassword ? 'text' : 'password'}\n                      value={signupForm.password}\n                      onChange={(e) => setSignupForm({ ...signupForm, password: e.target.value })}\n                      margin=\"normal\"\n                      InputProps={{\n                        startAdornment: <LockIcon sx={{ mr: 1, color: 'text.secondary' }} />,\n                        endAdornment: (\n                          <IconButton onClick={() => setShowPassword(!showPassword)}>\n                            {showPassword ? <VisibilityOffIcon /> : <VisibilityIcon />}\n                          </IconButton>\n                        ),\n                      }}\n                    />\n                    <TextField\n                      fullWidth\n                      label=\"Confirm Password\"\n                      type=\"password\"\n                      value={signupForm.confirmPassword}\n                      onChange={(e) => setSignupForm({ ...signupForm, confirmPassword: e.target.value })}\n                      margin=\"normal\"\n                      InputProps={{\n                        startAdornment: <LockIcon sx={{ mr: 1, color: 'text.secondary' }} />,\n                      }}\n                    />\n                    <Button\n                      type=\"submit\"\n                      fullWidth\n                      variant=\"contained\"\n                      sx={{ mt: 3, mb: 2, borderRadius: 3 }}\n                    >\n                      Create Account\n                    </Button>\n                  </Box>\n                </CardContent>\n              </Card>\n            )}\n          </TabPanel>\n\n          {/* Preferences Tab */}\n          <TabPanel value={activeTab} index={1}>\n            <Box maxWidth={500} mx=\"auto\">\n              <Typography variant=\"h6\" gutterBottom>\n                Appearance & Language\n              </Typography>\n              \n              <Card sx={{ mb: 3 }}>\n                <CardContent>\n                  <Box display=\"flex\" alignItems=\"center\" justifyContent=\"space-between\" mb={2}>\n                    <Box display=\"flex\" alignItems=\"center\" gap={2}>\n                      <PaletteIcon color=\"primary\" />\n                      <Box>\n                        <Typography variant=\"subtitle1\" fontWeight=\"600\">\n                          Dark Mode\n                        </Typography>\n                        <Typography variant=\"body2\" color=\"text.secondary\">\n                          Switch between light and dark themes\n                        </Typography>\n                      </Box>\n                    </Box>\n                    <Switch\n                      checked={darkMode}\n                      onChange={toggleDarkMode}\n                      color=\"primary\"\n                    />\n                  </Box>\n                </CardContent>\n              </Card>\n\n              <Card>\n                <CardContent>\n                  <Box display=\"flex\" alignItems=\"center\" gap={2} mb={2}>\n                    <LanguageIcon color=\"primary\" />\n                    <Box>\n                      <Typography variant=\"subtitle1\" fontWeight=\"600\">\n                        Language\n                      </Typography>\n                      <Typography variant=\"body2\" color=\"text.secondary\">\n                        Choose your preferred language\n                      </Typography>\n                    </Box>\n                  </Box>\n                  <Box display=\"flex\" gap={2}>\n                    <Button\n                      variant={language === 'en' ? 'contained' : 'outlined'}\n                      onClick={() => setLanguage('en')}\n                      sx={{ borderRadius: 3 }}\n                    >\n                      🇺🇸 English\n                    </Button>\n                    <Button\n                      variant={language === 'ar' ? 'contained' : 'outlined'}\n                      onClick={() => setLanguage('ar')}\n                      sx={{ borderRadius: 3 }}\n                    >\n                      🇸🇦 العربية\n                    </Button>\n                  </Box>\n                </CardContent>\n              </Card>\n            </Box>\n          </TabPanel>\n\n          {/* Plans Tab */}\n          <TabPanel value={activeTab} index={2}>\n            <Box textAlign=\"center\" mb={4}>\n              <Typography variant=\"h4\" gutterBottom fontWeight=\"bold\">\n                Simple Pricing\n              </Typography>\n              <Typography variant=\"h6\" color=\"text.secondary\">\n                Pay for what matters. Enjoy everything else.\n              </Typography>\n            </Box>\n\n            <Box display=\"flex\" justifyContent=\"center\" gap={3} flexWrap=\"wrap\">\n              {pricingPlans.map((plan, index) => (\n                <Card\n                  key={index}\n                  sx={{\n                    maxWidth: 350,\n                    position: 'relative',\n                    border: plan.popular ? '2px solid' : '1px solid',\n                    borderColor: plan.popular ? 'primary.main' : 'divider',\n                    transform: plan.popular ? 'scale(1.05)' : 'scale(1)',\n                    transition: 'transform 0.2s ease',\n                    '&:hover': {\n                      transform: plan.popular ? 'scale(1.07)' : 'scale(1.02)',\n                    },\n                  }}\n                >\n                  {plan.popular && (\n                    <Chip\n                      label=\"Most Popular\"\n                      color=\"primary\"\n                      sx={{\n                        position: 'absolute',\n                        top: -10,\n                        left: '50%',\n                        transform: 'translateX(-50%)',\n                        fontWeight: 'bold',\n                      }}\n                    />\n                  )}\n\n                  <CardContent sx={{ p: 4, textAlign: 'center' }}>\n                    <Box\n                      sx={{\n                        background: `linear-gradient(135deg, ${plan.color === 'primary' ? '#667eea 0%, #764ba2' : '#f093fb 0%, #f5576c'} 100%)`,\n                        borderRadius: '50%',\n                        width: 60,\n                        height: 60,\n                        display: 'flex',\n                        alignItems: 'center',\n                        justifyContent: 'center',\n                        mx: 'auto',\n                        mb: 2,\n                      }}\n                    >\n                      {React.cloneElement(plan.icon, { sx: { color: 'white', fontSize: 28 } })}\n                    </Box>\n\n                    <Typography variant=\"h5\" fontWeight=\"bold\" gutterBottom>\n                      {plan.name}\n                    </Typography>\n\n                    <Typography variant=\"body2\" color=\"text.secondary\" gutterBottom>\n                      {plan.description}\n                    </Typography>\n\n                    <Box my={3}>\n                      <Typography variant=\"h3\" fontWeight=\"bold\" color=\"primary\">\n                        {plan.price}\n                      </Typography>\n                      <Typography variant=\"body2\" color=\"text.secondary\">\n                        {plan.period}\n                      </Typography>\n                    </Box>\n\n                    <Typography variant=\"h6\" gutterBottom fontWeight=\"600\" sx={{ mt: 3 }}>\n                      Plan Highlights\n                    </Typography>\n\n                    <List dense>\n                      {plan.features.map((feature, idx) => (\n                        <ListItem key={idx} sx={{ px: 0 }}>\n                          <ListItemIcon sx={{ minWidth: 32 }}>\n                            <CheckIcon color=\"success\" fontSize=\"small\" />\n                          </ListItemIcon>\n                          <ListItemText\n                            primary={feature}\n                            primaryTypographyProps={{ variant: 'body2' }}\n                          />\n                        </ListItem>\n                      ))}\n                    </List>\n\n                    {plan.limitations.length > 0 && (\n                      <>\n                        <Divider sx={{ my: 2 }} />\n                        <Typography variant=\"body2\" color=\"text.secondary\" gutterBottom>\n                          Limitations:\n                        </Typography>\n                        {plan.limitations.map((limitation, idx) => (\n                          <Typography key={idx} variant=\"caption\" color=\"text.secondary\" display=\"block\">\n                            • {limitation}\n                          </Typography>\n                        ))}\n                      </>\n                    )}\n\n                    <Button\n                      variant={plan.popular ? 'contained' : 'outlined'}\n                      fullWidth\n                      size=\"large\"\n                      sx={{ mt: 3, borderRadius: 3 }}\n                    >\n                      {plan.price === '$0' ? 'Current Plan' : 'Upgrade Now'}\n                    </Button>\n                  </CardContent>\n                </Card>\n              ))}\n            </Box>\n\n            <Alert severity=\"info\" sx={{ mt: 4, borderRadius: 2 }}>\n              <Typography variant=\"body2\">\n                <strong>30-day money-back guarantee</strong> • Cancel anytime • No setup fees\n              </Typography>\n            </Alert>\n          </TabPanel>\n\n          {/* About Tab */}\n          <TabPanel value={activeTab} index={3}>\n            <Box maxWidth={600} mx=\"auto\" textAlign=\"center\">\n              <Box\n                sx={{\n                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                  borderRadius: '50%',\n                  width: 80,\n                  height: 80,\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  mx: 'auto',\n                  mb: 3,\n                }}\n              >\n                <SecurityIcon sx={{ color: 'white', fontSize: 40 }} />\n              </Box>\n\n              <Typography variant=\"h4\" gutterBottom fontWeight=\"bold\">\n                AI Security Guard\n              </Typography>\n\n              <Typography variant=\"h6\" color=\"text.secondary\" gutterBottom>\n                Version 1.0.0\n              </Typography>\n\n              <Typography variant=\"body1\" sx={{ mb: 4, lineHeight: 1.7 }}>\n                Advanced AI-powered security scanning platform providing comprehensive\n                threat detection and analysis for URLs and files. Built with cutting-edge\n                technology to protect your digital assets.\n              </Typography>\n\n              <Card sx={{ mb: 3 }}>\n                <CardContent>\n                  <Typography variant=\"h6\" gutterBottom>\n                    Key Features\n                  </Typography>\n                  <List>\n                    <ListItem>\n                      <ListItemIcon>\n                        <CheckIcon color=\"primary\" />\n                      </ListItemIcon>\n                      <ListItemText primary=\"Real-time URL threat detection\" />\n                    </ListItem>\n                    <ListItem>\n                      <ListItemIcon>\n                        <CheckIcon color=\"primary\" />\n                      </ListItemIcon>\n                      <ListItemText primary=\"Advanced file malware scanning\" />\n                    </ListItem>\n                    <ListItem>\n                      <ListItemIcon>\n                        <CheckIcon color=\"primary\" />\n                      </ListItemIcon>\n                      <ListItemText primary=\"AI-powered threat analysis\" />\n                    </ListItem>\n                    <ListItem>\n                      <ListItemIcon>\n                        <CheckIcon color=\"primary\" />\n                      </ListItemIcon>\n                      <ListItemText primary=\"Comprehensive security reports\" />\n                    </ListItem>\n                  </List>\n                </CardContent>\n              </Card>\n\n              <Typography variant=\"body2\" color=\"text.secondary\">\n                © 2024 AI Security Guard. All rights reserved.\n                <br />\n                Built with ❤️ for digital security\n              </Typography>\n            </Box>\n          </TabPanel>\n        </Box>\n      </DialogContent>\n    </Dialog>\n  );\n});\n\nSettingsDialog.displayName = 'SettingsDialog';\n\nexport default SettingsDialog;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,MAAM,EACNC,GAAG,EACHC,UAAU,EACVC,IAAI,EACJC,GAAG,EACHC,SAAS,EACTC,MAAM,EACNC,gBAAgB,EAChBC,IAAI,EACJC,WAAW,EACXC,IAAI,EACJC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,YAAY,EACZC,OAAO,EACPC,KAAK,EACLC,UAAU,EACVC,MAAM,QACD,eAAe;AACtB,SACEC,QAAQ,IAAIC,YAAY,EACxBC,MAAM,IAAIC,UAAU,EACpBC,QAAQ,IAAIC,YAAY,EACxBC,QAAQ,IAAIC,YAAY,EACxBC,OAAO,IAAIC,WAAW,EACtBC,IAAI,IAAIC,QAAQ,EAChBC,IAAI,IAAIC,QAAQ,EAChBC,KAAK,IAAIC,SAAS,EAClBC,KAAK,IAAIC,SAAS,EAClBC,KAAK,IAAIC,SAAS,EAClBC,IAAI,IAAIC,QAAQ,EAChBC,UAAU,IAAIC,cAAc,EAC5BC,aAAa,IAAIC,iBAAiB,EAClCC,KAAK,IAAIC,SAAS,EAClBC,OAAO,IAAIC,WAAW,QACjB,qBAAqB;AAC5B,SAASC,OAAO,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAElD,MAAMC,cAAc,gBAAAC,EAAA,cAAG7D,KAAK,CAAC8D,IAAI,CAAAC,EAAA,GAAAF,EAAA,CAAC,CAAC;EAAEG,IAAI;EAAEC;AAAQ,CAAC,KAAK;EAAAJ,EAAA;EACvD,MAAM,CAACK,SAAS,EAAEC,YAAY,CAAC,GAAGlE,QAAQ,CAAC,CAAC,CAAC;EAC7C,MAAM,CAACmE,YAAY,EAAEC,eAAe,CAAC,GAAGpE,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACqE,SAAS,EAAEC,YAAY,CAAC,GAAGtE,QAAQ,CAAC;IAAEuE,KAAK,EAAE,EAAE;IAAEC,QAAQ,EAAE;EAAG,CAAC,CAAC;EACvE,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAG1E,QAAQ,CAAC;IAC3C2E,IAAI,EAAE,EAAE;IACRJ,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZI,eAAe,EAAE;EACnB,CAAC,CAAC;EACF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG9E,QAAQ,CAAC,IAAI,CAAC;EAE5C,MAAM;IAAE+E,QAAQ;IAAEC,QAAQ;IAAEC,cAAc;IAAEC;EAAY,CAAC,GAAG5B,OAAO,CAAC,CAAC;EAErE,MAAM6B,eAAe,GAAGA,CAACC,KAAK,EAAEC,QAAQ,KAAK;IAC3CnB,YAAY,CAACmB,QAAQ,CAAC;EACxB,CAAC;EAED,MAAMC,YAAY,GAAG,CACnB;IACEX,IAAI,EAAE,WAAW;IACjBY,KAAK,EAAE,IAAI;IACXC,MAAM,EAAE,SAAS;IACjBC,WAAW,EAAE,0BAA0B;IACvCC,QAAQ,EAAE,CACR,sBAAsB,EACtB,sBAAsB,EACtB,wBAAwB,EACxB,kBAAkB,EAClB,eAAe,CAChB;IACDC,WAAW,EAAE,CACX,+BAA+B,EAC/B,uBAAuB,EACvB,eAAe,CAChB;IACDC,KAAK,EAAE,SAAS;IAChBC,IAAI,eAAErC,OAAA,CAAC3B,YAAY;MAAAiE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBC,OAAO,EAAE;EACX,CAAC,EACD;IACEvB,IAAI,EAAE,UAAU;IAChBY,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,WAAW;IACnBC,WAAW,EAAE,qCAAqC;IAClDC,QAAQ,EAAE,CACR,qBAAqB,EACrB,sBAAsB,EACtB,8BAA8B,EAC9B,sBAAsB,EACtB,kBAAkB,EAClB,YAAY,EACZ,qBAAqB,EACrB,oBAAoB,EACpB,oBAAoB,EACpB,qBAAqB,CACtB;IACDC,WAAW,EAAE,EAAE;IACfC,KAAK,EAAE,WAAW;IAClBC,IAAI,eAAErC,OAAA,CAACL,SAAS;MAAA2C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACnBC,OAAO,EAAE;EACX,CAAC,CACF;EAED,MAAMC,WAAW,GAAIC,CAAC,IAAK;IACzBA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB;IACAC,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAElC,SAAS,CAAC;EAClC,CAAC;EAED,MAAMmC,YAAY,GAAIJ,CAAC,IAAK;IAC1BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB;IACAC,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE9B,UAAU,CAAC;EACpC,CAAC;EAED,MAAMgC,QAAQ,GAAGA,CAAC;IAAEC,QAAQ;IAAEC,KAAK;IAAEC;EAAM,CAAC,kBAC1CpD,OAAA;IAAKqD,MAAM,EAAEF,KAAK,KAAKC,KAAM;IAAAF,QAAA,EAC1BC,KAAK,KAAKC,KAAK,iBAAIpD,OAAA,CAAClD,GAAG;MAACwG,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAL,QAAA,EAAEA;IAAQ;MAAAZ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACrD,CACN;EAED,oBACEzC,OAAA,CAACvD,MAAM;IACL8D,IAAI,EAAEA,IAAK;IACXC,OAAO,EAAEA,OAAQ;IACjBgD,QAAQ,EAAC,IAAI;IACbC,SAAS;IACTC,UAAU,EAAE;MACVJ,EAAE,EAAE;QACFK,YAAY,EAAE,CAAC;QACfC,SAAS,EAAE;MACb;IACF,CAAE;IAAAV,QAAA,gBAEFlD,OAAA,CAACtD,WAAW;MAAAwG,QAAA,eACVlD,OAAA,CAAClD,GAAG;QAAC+G,OAAO,EAAC,MAAM;QAACC,UAAU,EAAC,QAAQ;QAACC,cAAc,EAAC,eAAe;QAAAb,QAAA,gBACpElD,OAAA,CAAClD,GAAG;UAAC+G,OAAO,EAAC,MAAM;UAACC,UAAU,EAAC,QAAQ;UAACE,GAAG,EAAE,CAAE;UAAAd,QAAA,gBAC7ClD,OAAA,CAAC/B,YAAY;YAACmE,KAAK,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAChCzC,OAAA,CAACjD,UAAU;YAACkH,OAAO,EAAC,IAAI;YAACC,UAAU,EAAC,MAAM;YAAAhB,QAAA,EAAC;UAE3C;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eACNzC,OAAA,CAAClC,UAAU;UAACqG,OAAO,EAAE3D,OAAQ;UAAA0C,QAAA,eAC3BlD,OAAA,CAACf,SAAS;YAAAqD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CAAC,eAEdzC,OAAA,CAACrD,aAAa;MAAC2G,EAAE,EAAE;QAAEc,CAAC,EAAE;MAAE,CAAE;MAAAlB,QAAA,gBAC1BlD,OAAA,CAAClD,GAAG;QAACwG,EAAE,EAAE;UAAEe,YAAY,EAAE,CAAC;UAAEC,WAAW,EAAE;QAAU,CAAE;QAAApB,QAAA,eACnDlD,OAAA,CAAChD,IAAI;UACHmG,KAAK,EAAE1C,SAAU;UACjB8D,QAAQ,EAAE5C,eAAgB;UAC1BsC,OAAO,EAAC,WAAW;UACnBO,SAAS,EAAC,SAAS;UACnBC,cAAc,EAAC,SAAS;UAAAvB,QAAA,gBAExBlD,OAAA,CAAC/C,GAAG;YAACoF,IAAI,eAAErC,OAAA,CAAC7B,UAAU;cAAAmE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAACiC,KAAK,EAAC;UAAS;YAAApC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC7CzC,OAAA,CAAC/C,GAAG;YAACoF,IAAI,eAAErC,OAAA,CAACvB,WAAW;cAAA6D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAACiC,KAAK,EAAC;UAAa;YAAApC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAClDzC,OAAA,CAAC/C,GAAG;YAACoF,IAAI,eAAErC,OAAA,CAACnB,QAAQ;cAAAyD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAACiC,KAAK,EAAC;UAAO;YAAApC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACzCzC,OAAA,CAAC/C,GAAG;YAACoF,IAAI,eAAErC,OAAA,CAACrB,QAAQ;cAAA2D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAACiC,KAAK,EAAC;UAAO;YAAApC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAENzC,OAAA,CAAClD,GAAG;QAACwG,EAAE,EAAE;UAAEc,CAAC,EAAE;QAAE,CAAE;QAAAlB,QAAA,gBAEhBlD,OAAA,CAACiD,QAAQ;UAACE,KAAK,EAAE1C,SAAU;UAAC2C,KAAK,EAAE,CAAE;UAAAF,QAAA,gBACnClD,OAAA,CAAClD,GAAG;YAAC+G,OAAO,EAAC,MAAM;YAACE,cAAc,EAAC,QAAQ;YAACY,EAAE,EAAE,CAAE;YAAAzB,QAAA,gBAChDlD,OAAA,CAACnD,MAAM;cACLoH,OAAO,EAAE5C,OAAO,GAAG,WAAW,GAAG,UAAW;cAC5C8C,OAAO,EAAEA,CAAA,KAAM7C,UAAU,CAAC,IAAI,CAAE;cAChCgC,EAAE,EAAE;gBAAEsB,EAAE,EAAE,CAAC;gBAAEjB,YAAY,EAAE;cAAE,CAAE;cAAAT,QAAA,EAChC;YAED;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTzC,OAAA,CAACnD,MAAM;cACLoH,OAAO,EAAE,CAAC5C,OAAO,GAAG,WAAW,GAAG,UAAW;cAC7C8C,OAAO,EAAEA,CAAA,KAAM7C,UAAU,CAAC,KAAK,CAAE;cACjCgC,EAAE,EAAE;gBAAEK,YAAY,EAAE;cAAE,CAAE;cAAAT,QAAA,EACzB;YAED;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,EAELpB,OAAO,gBACNrB,OAAA,CAAC3C,IAAI;YAACiG,EAAE,EAAE;cAAEE,QAAQ,EAAE,GAAG;cAAEqB,EAAE,EAAE;YAAO,CAAE;YAAA3B,QAAA,eACtClD,OAAA,CAAC1C,WAAW;cAAA4F,QAAA,gBACVlD,OAAA,CAACjD,UAAU;gBAACkH,OAAO,EAAC,IAAI;gBAACa,YAAY;gBAACC,SAAS,EAAC,QAAQ;gBAAA7B,QAAA,EAAC;cAEzD;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbzC,OAAA,CAAClD,GAAG;gBAACkI,SAAS,EAAC,MAAM;gBAACC,QAAQ,EAAEtC,WAAY;gBAAAO,QAAA,gBAC1ClD,OAAA,CAAC9C,SAAS;kBACRuG,SAAS;kBACTiB,KAAK,EAAC,OAAO;kBACbQ,IAAI,EAAC,OAAO;kBACZ/B,KAAK,EAAEtC,SAAS,CAACE,KAAM;kBACvBwD,QAAQ,EAAG3B,CAAC,IAAK9B,YAAY,CAAC;oBAAE,GAAGD,SAAS;oBAAEE,KAAK,EAAE6B,CAAC,CAACuC,MAAM,CAAChC;kBAAM,CAAC,CAAE;kBACvEiC,MAAM,EAAC,QAAQ;kBACfC,UAAU,EAAE;oBACVC,cAAc,eAAEtF,OAAA,CAACb,SAAS;sBAACmE,EAAE,EAAE;wBAAEsB,EAAE,EAAE,CAAC;wBAAExC,KAAK,EAAE;sBAAiB;oBAAE;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBACtE;gBAAE;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACFzC,OAAA,CAAC9C,SAAS;kBACRuG,SAAS;kBACTiB,KAAK,EAAC,UAAU;kBAChBQ,IAAI,EAAEvE,YAAY,GAAG,MAAM,GAAG,UAAW;kBACzCwC,KAAK,EAAEtC,SAAS,CAACG,QAAS;kBAC1BuD,QAAQ,EAAG3B,CAAC,IAAK9B,YAAY,CAAC;oBAAE,GAAGD,SAAS;oBAAEG,QAAQ,EAAE4B,CAAC,CAACuC,MAAM,CAAChC;kBAAM,CAAC,CAAE;kBAC1EiC,MAAM,EAAC,QAAQ;kBACfC,UAAU,EAAE;oBACVC,cAAc,eAAEtF,OAAA,CAACX,QAAQ;sBAACiE,EAAE,EAAE;wBAAEsB,EAAE,EAAE,CAAC;wBAAExC,KAAK,EAAE;sBAAiB;oBAAE;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC;oBACpE8C,YAAY,eACVvF,OAAA,CAAClC,UAAU;sBAACqG,OAAO,EAAEA,CAAA,KAAMvD,eAAe,CAAC,CAACD,YAAY,CAAE;sBAAAuC,QAAA,EACvDvC,YAAY,gBAAGX,OAAA,CAACP,iBAAiB;wBAAA6C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,gBAAGzC,OAAA,CAACT,cAAc;wBAAA+C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChD;kBAEhB;gBAAE;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACFzC,OAAA,CAACnD,MAAM;kBACLqI,IAAI,EAAC,QAAQ;kBACbzB,SAAS;kBACTQ,OAAO,EAAC,WAAW;kBACnBX,EAAE,EAAE;oBAAEkC,EAAE,EAAE,CAAC;oBAAEb,EAAE,EAAE,CAAC;oBAAEhB,YAAY,EAAE;kBAAE,CAAE;kBAAAT,QAAA,EACvC;gBAED;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,gBAEPzC,OAAA,CAAC3C,IAAI;YAACiG,EAAE,EAAE;cAAEE,QAAQ,EAAE,GAAG;cAAEqB,EAAE,EAAE;YAAO,CAAE;YAAA3B,QAAA,eACtClD,OAAA,CAAC1C,WAAW;cAAA4F,QAAA,gBACVlD,OAAA,CAACjD,UAAU;gBAACkH,OAAO,EAAC,IAAI;gBAACa,YAAY;gBAACC,SAAS,EAAC,QAAQ;gBAAA7B,QAAA,EAAC;cAEzD;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbzC,OAAA,CAAClD,GAAG;gBAACkI,SAAS,EAAC,MAAM;gBAACC,QAAQ,EAAEjC,YAAa;gBAAAE,QAAA,gBAC3ClD,OAAA,CAAC9C,SAAS;kBACRuG,SAAS;kBACTiB,KAAK,EAAC,WAAW;kBACjBvB,KAAK,EAAElC,UAAU,CAACE,IAAK;kBACvBoD,QAAQ,EAAG3B,CAAC,IAAK1B,aAAa,CAAC;oBAAE,GAAGD,UAAU;oBAAEE,IAAI,EAAEyB,CAAC,CAACuC,MAAM,CAAChC;kBAAM,CAAC,CAAE;kBACxEiC,MAAM,EAAC,QAAQ;kBACfC,UAAU,EAAE;oBACVC,cAAc,eAAEtF,OAAA,CAAC7B,UAAU;sBAACmF,EAAE,EAAE;wBAAEsB,EAAE,EAAE,CAAC;wBAAExC,KAAK,EAAE;sBAAiB;oBAAE;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBACvE;gBAAE;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACFzC,OAAA,CAAC9C,SAAS;kBACRuG,SAAS;kBACTiB,KAAK,EAAC,OAAO;kBACbQ,IAAI,EAAC,OAAO;kBACZ/B,KAAK,EAAElC,UAAU,CAACF,KAAM;kBACxBwD,QAAQ,EAAG3B,CAAC,IAAK1B,aAAa,CAAC;oBAAE,GAAGD,UAAU;oBAAEF,KAAK,EAAE6B,CAAC,CAACuC,MAAM,CAAChC;kBAAM,CAAC,CAAE;kBACzEiC,MAAM,EAAC,QAAQ;kBACfC,UAAU,EAAE;oBACVC,cAAc,eAAEtF,OAAA,CAACb,SAAS;sBAACmE,EAAE,EAAE;wBAAEsB,EAAE,EAAE,CAAC;wBAAExC,KAAK,EAAE;sBAAiB;oBAAE;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBACtE;gBAAE;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACFzC,OAAA,CAAC9C,SAAS;kBACRuG,SAAS;kBACTiB,KAAK,EAAC,UAAU;kBAChBQ,IAAI,EAAEvE,YAAY,GAAG,MAAM,GAAG,UAAW;kBACzCwC,KAAK,EAAElC,UAAU,CAACD,QAAS;kBAC3BuD,QAAQ,EAAG3B,CAAC,IAAK1B,aAAa,CAAC;oBAAE,GAAGD,UAAU;oBAAED,QAAQ,EAAE4B,CAAC,CAACuC,MAAM,CAAChC;kBAAM,CAAC,CAAE;kBAC5EiC,MAAM,EAAC,QAAQ;kBACfC,UAAU,EAAE;oBACVC,cAAc,eAAEtF,OAAA,CAACX,QAAQ;sBAACiE,EAAE,EAAE;wBAAEsB,EAAE,EAAE,CAAC;wBAAExC,KAAK,EAAE;sBAAiB;oBAAE;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC;oBACpE8C,YAAY,eACVvF,OAAA,CAAClC,UAAU;sBAACqG,OAAO,EAAEA,CAAA,KAAMvD,eAAe,CAAC,CAACD,YAAY,CAAE;sBAAAuC,QAAA,EACvDvC,YAAY,gBAAGX,OAAA,CAACP,iBAAiB;wBAAA6C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,gBAAGzC,OAAA,CAACT,cAAc;wBAAA+C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChD;kBAEhB;gBAAE;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACFzC,OAAA,CAAC9C,SAAS;kBACRuG,SAAS;kBACTiB,KAAK,EAAC,kBAAkB;kBACxBQ,IAAI,EAAC,UAAU;kBACf/B,KAAK,EAAElC,UAAU,CAACG,eAAgB;kBAClCmD,QAAQ,EAAG3B,CAAC,IAAK1B,aAAa,CAAC;oBAAE,GAAGD,UAAU;oBAAEG,eAAe,EAAEwB,CAAC,CAACuC,MAAM,CAAChC;kBAAM,CAAC,CAAE;kBACnFiC,MAAM,EAAC,QAAQ;kBACfC,UAAU,EAAE;oBACVC,cAAc,eAAEtF,OAAA,CAACX,QAAQ;sBAACiE,EAAE,EAAE;wBAAEsB,EAAE,EAAE,CAAC;wBAAExC,KAAK,EAAE;sBAAiB;oBAAE;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBACrE;gBAAE;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACFzC,OAAA,CAACnD,MAAM;kBACLqI,IAAI,EAAC,QAAQ;kBACbzB,SAAS;kBACTQ,OAAO,EAAC,WAAW;kBACnBX,EAAE,EAAE;oBAAEkC,EAAE,EAAE,CAAC;oBAAEb,EAAE,EAAE,CAAC;oBAAEhB,YAAY,EAAE;kBAAE,CAAE;kBAAAT,QAAA,EACvC;gBAED;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CACP;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,eAGXzC,OAAA,CAACiD,QAAQ;UAACE,KAAK,EAAE1C,SAAU;UAAC2C,KAAK,EAAE,CAAE;UAAAF,QAAA,eACnClD,OAAA,CAAClD,GAAG;YAAC0G,QAAQ,EAAE,GAAI;YAACqB,EAAE,EAAC,MAAM;YAAA3B,QAAA,gBAC3BlD,OAAA,CAACjD,UAAU;cAACkH,OAAO,EAAC,IAAI;cAACa,YAAY;cAAA5B,QAAA,EAAC;YAEtC;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAEbzC,OAAA,CAAC3C,IAAI;cAACiG,EAAE,EAAE;gBAAEqB,EAAE,EAAE;cAAE,CAAE;cAAAzB,QAAA,eAClBlD,OAAA,CAAC1C,WAAW;gBAAA4F,QAAA,eACVlD,OAAA,CAAClD,GAAG;kBAAC+G,OAAO,EAAC,MAAM;kBAACC,UAAU,EAAC,QAAQ;kBAACC,cAAc,EAAC,eAAe;kBAACY,EAAE,EAAE,CAAE;kBAAAzB,QAAA,gBAC3ElD,OAAA,CAAClD,GAAG;oBAAC+G,OAAO,EAAC,MAAM;oBAACC,UAAU,EAAC,QAAQ;oBAACE,GAAG,EAAE,CAAE;oBAAAd,QAAA,gBAC7ClD,OAAA,CAACvB,WAAW;sBAAC2D,KAAK,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC/BzC,OAAA,CAAClD,GAAG;sBAAAoG,QAAA,gBACFlD,OAAA,CAACjD,UAAU;wBAACkH,OAAO,EAAC,WAAW;wBAACC,UAAU,EAAC,KAAK;wBAAAhB,QAAA,EAAC;sBAEjD;wBAAAZ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eACbzC,OAAA,CAACjD,UAAU;wBAACkH,OAAO,EAAC,OAAO;wBAAC7B,KAAK,EAAC,gBAAgB;wBAAAc,QAAA,EAAC;sBAEnD;wBAAAZ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNzC,OAAA,CAAC7C,MAAM;oBACLsI,OAAO,EAAElE,QAAS;oBAClBgD,QAAQ,EAAE9C,cAAe;oBACzBW,KAAK,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACK;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAEPzC,OAAA,CAAC3C,IAAI;cAAA6F,QAAA,eACHlD,OAAA,CAAC1C,WAAW;gBAAA4F,QAAA,gBACVlD,OAAA,CAAClD,GAAG;kBAAC+G,OAAO,EAAC,MAAM;kBAACC,UAAU,EAAC,QAAQ;kBAACE,GAAG,EAAE,CAAE;kBAACW,EAAE,EAAE,CAAE;kBAAAzB,QAAA,gBACpDlD,OAAA,CAACzB,YAAY;oBAAC6D,KAAK,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAChCzC,OAAA,CAAClD,GAAG;oBAAAoG,QAAA,gBACFlD,OAAA,CAACjD,UAAU;sBAACkH,OAAO,EAAC,WAAW;sBAACC,UAAU,EAAC,KAAK;sBAAAhB,QAAA,EAAC;oBAEjD;sBAAAZ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACbzC,OAAA,CAACjD,UAAU;sBAACkH,OAAO,EAAC,OAAO;sBAAC7B,KAAK,EAAC,gBAAgB;sBAAAc,QAAA,EAAC;oBAEnD;sBAAAZ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNzC,OAAA,CAAClD,GAAG;kBAAC+G,OAAO,EAAC,MAAM;kBAACG,GAAG,EAAE,CAAE;kBAAAd,QAAA,gBACzBlD,OAAA,CAACnD,MAAM;oBACLoH,OAAO,EAAEzC,QAAQ,KAAK,IAAI,GAAG,WAAW,GAAG,UAAW;oBACtD2C,OAAO,EAAEA,CAAA,KAAMzC,WAAW,CAAC,IAAI,CAAE;oBACjC4B,EAAE,EAAE;sBAAEK,YAAY,EAAE;oBAAE,CAAE;oBAAAT,QAAA,EACzB;kBAED;oBAAAZ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACTzC,OAAA,CAACnD,MAAM;oBACLoH,OAAO,EAAEzC,QAAQ,KAAK,IAAI,GAAG,WAAW,GAAG,UAAW;oBACtD2C,OAAO,EAAEA,CAAA,KAAMzC,WAAW,CAAC,IAAI,CAAE;oBACjC4B,EAAE,EAAE;sBAAEK,YAAY,EAAE;oBAAE,CAAE;oBAAAT,QAAA,EACzB;kBAED;oBAAAZ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACK;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGXzC,OAAA,CAACiD,QAAQ;UAACE,KAAK,EAAE1C,SAAU;UAAC2C,KAAK,EAAE,CAAE;UAAAF,QAAA,gBACnClD,OAAA,CAAClD,GAAG;YAACiI,SAAS,EAAC,QAAQ;YAACJ,EAAE,EAAE,CAAE;YAAAzB,QAAA,gBAC5BlD,OAAA,CAACjD,UAAU;cAACkH,OAAO,EAAC,IAAI;cAACa,YAAY;cAACZ,UAAU,EAAC,MAAM;cAAAhB,QAAA,EAAC;YAExD;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbzC,OAAA,CAACjD,UAAU;cAACkH,OAAO,EAAC,IAAI;cAAC7B,KAAK,EAAC,gBAAgB;cAAAc,QAAA,EAAC;YAEhD;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAENzC,OAAA,CAAClD,GAAG;YAAC+G,OAAO,EAAC,MAAM;YAACE,cAAc,EAAC,QAAQ;YAACC,GAAG,EAAE,CAAE;YAAC0B,QAAQ,EAAC,MAAM;YAAAxC,QAAA,EAChEpB,YAAY,CAAC6D,GAAG,CAAC,CAACC,IAAI,EAAExC,KAAK,kBAC5BpD,OAAA,CAAC3C,IAAI;cAEHiG,EAAE,EAAE;gBACFE,QAAQ,EAAE,GAAG;gBACbqC,QAAQ,EAAE,UAAU;gBACpBC,MAAM,EAAEF,IAAI,CAAClD,OAAO,GAAG,WAAW,GAAG,WAAW;gBAChD4B,WAAW,EAAEsB,IAAI,CAAClD,OAAO,GAAG,cAAc,GAAG,SAAS;gBACtDqD,SAAS,EAAEH,IAAI,CAAClD,OAAO,GAAG,aAAa,GAAG,UAAU;gBACpDsD,UAAU,EAAE,qBAAqB;gBACjC,SAAS,EAAE;kBACTD,SAAS,EAAEH,IAAI,CAAClD,OAAO,GAAG,aAAa,GAAG;gBAC5C;cACF,CAAE;cAAAQ,QAAA,GAED0C,IAAI,CAAClD,OAAO,iBACX1C,OAAA,CAACzC,IAAI;gBACHmH,KAAK,EAAC,cAAc;gBACpBtC,KAAK,EAAC,SAAS;gBACfkB,EAAE,EAAE;kBACFuC,QAAQ,EAAE,UAAU;kBACpBI,GAAG,EAAE,CAAC,EAAE;kBACRC,IAAI,EAAE,KAAK;kBACXH,SAAS,EAAE,kBAAkB;kBAC7B7B,UAAU,EAAE;gBACd;cAAE;gBAAA5B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACF,eAEDzC,OAAA,CAAC1C,WAAW;gBAACgG,EAAE,EAAE;kBAAEc,CAAC,EAAE,CAAC;kBAAEW,SAAS,EAAE;gBAAS,CAAE;gBAAA7B,QAAA,gBAC7ClD,OAAA,CAAClD,GAAG;kBACFwG,EAAE,EAAE;oBACF6C,UAAU,EAAE,2BAA2BP,IAAI,CAACxD,KAAK,KAAK,SAAS,GAAG,qBAAqB,GAAG,qBAAqB,QAAQ;oBACvHuB,YAAY,EAAE,KAAK;oBACnByC,KAAK,EAAE,EAAE;oBACTC,MAAM,EAAE,EAAE;oBACVxC,OAAO,EAAE,MAAM;oBACfC,UAAU,EAAE,QAAQ;oBACpBC,cAAc,EAAE,QAAQ;oBACxBc,EAAE,EAAE,MAAM;oBACVF,EAAE,EAAE;kBACN,CAAE;kBAAAzB,QAAA,eAED3G,KAAK,CAAC+J,YAAY,CAACV,IAAI,CAACvD,IAAI,EAAE;oBAAEiB,EAAE,EAAE;sBAAElB,KAAK,EAAE,OAAO;sBAAEmE,QAAQ,EAAE;oBAAG;kBAAE,CAAC;gBAAC;kBAAAjE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrE,CAAC,eAENzC,OAAA,CAACjD,UAAU;kBAACkH,OAAO,EAAC,IAAI;kBAACC,UAAU,EAAC,MAAM;kBAACY,YAAY;kBAAA5B,QAAA,EACpD0C,IAAI,CAACzE;gBAAI;kBAAAmB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC,eAEbzC,OAAA,CAACjD,UAAU;kBAACkH,OAAO,EAAC,OAAO;kBAAC7B,KAAK,EAAC,gBAAgB;kBAAC0C,YAAY;kBAAA5B,QAAA,EAC5D0C,IAAI,CAAC3D;gBAAW;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP,CAAC,eAEbzC,OAAA,CAAClD,GAAG;kBAAC0J,EAAE,EAAE,CAAE;kBAAAtD,QAAA,gBACTlD,OAAA,CAACjD,UAAU;oBAACkH,OAAO,EAAC,IAAI;oBAACC,UAAU,EAAC,MAAM;oBAAC9B,KAAK,EAAC,SAAS;oBAAAc,QAAA,EACvD0C,IAAI,CAAC7D;kBAAK;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC,eACbzC,OAAA,CAACjD,UAAU;oBAACkH,OAAO,EAAC,OAAO;oBAAC7B,KAAK,EAAC,gBAAgB;oBAAAc,QAAA,EAC/C0C,IAAI,CAAC5D;kBAAM;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,eAENzC,OAAA,CAACjD,UAAU;kBAACkH,OAAO,EAAC,IAAI;kBAACa,YAAY;kBAACZ,UAAU,EAAC,KAAK;kBAACZ,EAAE,EAAE;oBAAEkC,EAAE,EAAE;kBAAE,CAAE;kBAAAtC,QAAA,EAAC;gBAEtE;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAEbzC,OAAA,CAACxC,IAAI;kBAACiJ,KAAK;kBAAAvD,QAAA,EACR0C,IAAI,CAAC1D,QAAQ,CAACyD,GAAG,CAAC,CAACe,OAAO,EAAEC,GAAG,kBAC9B3G,OAAA,CAACvC,QAAQ;oBAAW6F,EAAE,EAAE;sBAAEsD,EAAE,EAAE;oBAAE,CAAE;oBAAA1D,QAAA,gBAChClD,OAAA,CAACtC,YAAY;sBAAC4F,EAAE,EAAE;wBAAEuD,QAAQ,EAAE;sBAAG,CAAE;sBAAA3D,QAAA,eACjClD,OAAA,CAACjB,SAAS;wBAACqD,KAAK,EAAC,SAAS;wBAACmE,QAAQ,EAAC;sBAAO;wBAAAjE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClC,CAAC,eACfzC,OAAA,CAACrC,YAAY;sBACXmJ,OAAO,EAAEJ,OAAQ;sBACjBK,sBAAsB,EAAE;wBAAE9C,OAAO,EAAE;sBAAQ;oBAAE;sBAAA3B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9C,CAAC;kBAAA,GAPWkE,GAAG;oBAAArE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAQR,CACX;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,EAENmD,IAAI,CAACzD,WAAW,CAAC6E,MAAM,GAAG,CAAC,iBAC1BhH,OAAA,CAAAE,SAAA;kBAAAgD,QAAA,gBACElD,OAAA,CAACpC,OAAO;oBAAC0F,EAAE,EAAE;sBAAEkD,EAAE,EAAE;oBAAE;kBAAE;oBAAAlE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC1BzC,OAAA,CAACjD,UAAU;oBAACkH,OAAO,EAAC,OAAO;oBAAC7B,KAAK,EAAC,gBAAgB;oBAAC0C,YAAY;oBAAA5B,QAAA,EAAC;kBAEhE;oBAAAZ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,EACZmD,IAAI,CAACzD,WAAW,CAACwD,GAAG,CAAC,CAACsB,UAAU,EAAEN,GAAG,kBACpC3G,OAAA,CAACjD,UAAU;oBAAWkH,OAAO,EAAC,SAAS;oBAAC7B,KAAK,EAAC,gBAAgB;oBAACyB,OAAO,EAAC,OAAO;oBAAAX,QAAA,GAAC,SAC3E,EAAC+D,UAAU;kBAAA,GADEN,GAAG;oBAAArE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAER,CACb,CAAC;gBAAA,eACF,CACH,eAEDzC,OAAA,CAACnD,MAAM;kBACLoH,OAAO,EAAE2B,IAAI,CAAClD,OAAO,GAAG,WAAW,GAAG,UAAW;kBACjDe,SAAS;kBACTyD,IAAI,EAAC,OAAO;kBACZ5D,EAAE,EAAE;oBAAEkC,EAAE,EAAE,CAAC;oBAAE7B,YAAY,EAAE;kBAAE,CAAE;kBAAAT,QAAA,EAE9B0C,IAAI,CAAC7D,KAAK,KAAK,IAAI,GAAG,cAAc,GAAG;gBAAa;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA,GArGTW,KAAK;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAsGN,CACP;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENzC,OAAA,CAACnC,KAAK;YAACsJ,QAAQ,EAAC,MAAM;YAAC7D,EAAE,EAAE;cAAEkC,EAAE,EAAE,CAAC;cAAE7B,YAAY,EAAE;YAAE,CAAE;YAAAT,QAAA,eACpDlD,OAAA,CAACjD,UAAU;cAACkH,OAAO,EAAC,OAAO;cAAAf,QAAA,gBACzBlD,OAAA;gBAAAkD,QAAA,EAAQ;cAA2B;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,+CAC9C;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAGXzC,OAAA,CAACiD,QAAQ;UAACE,KAAK,EAAE1C,SAAU;UAAC2C,KAAK,EAAE,CAAE;UAAAF,QAAA,eACnClD,OAAA,CAAClD,GAAG;YAAC0G,QAAQ,EAAE,GAAI;YAACqB,EAAE,EAAC,MAAM;YAACE,SAAS,EAAC,QAAQ;YAAA7B,QAAA,gBAC9ClD,OAAA,CAAClD,GAAG;cACFwG,EAAE,EAAE;gBACF6C,UAAU,EAAE,mDAAmD;gBAC/DxC,YAAY,EAAE,KAAK;gBACnByC,KAAK,EAAE,EAAE;gBACTC,MAAM,EAAE,EAAE;gBACVxC,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,QAAQ;gBACpBC,cAAc,EAAE,QAAQ;gBACxBc,EAAE,EAAE,MAAM;gBACVF,EAAE,EAAE;cACN,CAAE;cAAAzB,QAAA,eAEFlD,OAAA,CAAC3B,YAAY;gBAACiF,EAAE,EAAE;kBAAElB,KAAK,EAAE,OAAO;kBAAEmE,QAAQ,EAAE;gBAAG;cAAE;gBAAAjE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnD,CAAC,eAENzC,OAAA,CAACjD,UAAU;cAACkH,OAAO,EAAC,IAAI;cAACa,YAAY;cAACZ,UAAU,EAAC,MAAM;cAAAhB,QAAA,EAAC;YAExD;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAEbzC,OAAA,CAACjD,UAAU;cAACkH,OAAO,EAAC,IAAI;cAAC7B,KAAK,EAAC,gBAAgB;cAAC0C,YAAY;cAAA5B,QAAA,EAAC;YAE7D;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAEbzC,OAAA,CAACjD,UAAU;cAACkH,OAAO,EAAC,OAAO;cAACX,EAAE,EAAE;gBAAEqB,EAAE,EAAE,CAAC;gBAAEyC,UAAU,EAAE;cAAI,CAAE;cAAAlE,QAAA,EAAC;YAI5D;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAEbzC,OAAA,CAAC3C,IAAI;cAACiG,EAAE,EAAE;gBAAEqB,EAAE,EAAE;cAAE,CAAE;cAAAzB,QAAA,eAClBlD,OAAA,CAAC1C,WAAW;gBAAA4F,QAAA,gBACVlD,OAAA,CAACjD,UAAU;kBAACkH,OAAO,EAAC,IAAI;kBAACa,YAAY;kBAAA5B,QAAA,EAAC;gBAEtC;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbzC,OAAA,CAACxC,IAAI;kBAAA0F,QAAA,gBACHlD,OAAA,CAACvC,QAAQ;oBAAAyF,QAAA,gBACPlD,OAAA,CAACtC,YAAY;sBAAAwF,QAAA,eACXlD,OAAA,CAACjB,SAAS;wBAACqD,KAAK,EAAC;sBAAS;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjB,CAAC,eACfzC,OAAA,CAACrC,YAAY;sBAACmJ,OAAO,EAAC;oBAAgC;sBAAAxE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjD,CAAC,eACXzC,OAAA,CAACvC,QAAQ;oBAAAyF,QAAA,gBACPlD,OAAA,CAACtC,YAAY;sBAAAwF,QAAA,eACXlD,OAAA,CAACjB,SAAS;wBAACqD,KAAK,EAAC;sBAAS;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjB,CAAC,eACfzC,OAAA,CAACrC,YAAY;sBAACmJ,OAAO,EAAC;oBAAgC;sBAAAxE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjD,CAAC,eACXzC,OAAA,CAACvC,QAAQ;oBAAAyF,QAAA,gBACPlD,OAAA,CAACtC,YAAY;sBAAAwF,QAAA,eACXlD,OAAA,CAACjB,SAAS;wBAACqD,KAAK,EAAC;sBAAS;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjB,CAAC,eACfzC,OAAA,CAACrC,YAAY;sBAACmJ,OAAO,EAAC;oBAA4B;sBAAAxE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7C,CAAC,eACXzC,OAAA,CAACvC,QAAQ;oBAAAyF,QAAA,gBACPlD,OAAA,CAACtC,YAAY;sBAAAwF,QAAA,eACXlD,OAAA,CAACjB,SAAS;wBAACqD,KAAK,EAAC;sBAAS;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjB,CAAC,eACfzC,OAAA,CAACrC,YAAY;sBAACmJ,OAAO,EAAC;oBAAgC;sBAAAxE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAEPzC,OAAA,CAACjD,UAAU;cAACkH,OAAO,EAAC,OAAO;cAAC7B,KAAK,EAAC,gBAAgB;cAAAc,QAAA,GAAC,mDAEjD,eAAAlD,OAAA;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,gDAER;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACO,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEb,CAAC;EAAA,QApgB6D3C,OAAO;AAAA,EAogBpE,CAAC;EAAA,QApgB4DA,OAAO;AAAA,EAogBnE;AAACuH,GAAA,GAhhBGlH,cAAc;AAkhBpBA,cAAc,CAACmH,WAAW,GAAG,gBAAgB;AAE7C,eAAenH,cAAc;AAAC,IAAAG,EAAA,EAAA+G,GAAA;AAAAE,YAAA,CAAAjH,EAAA;AAAAiH,YAAA,CAAAF,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}