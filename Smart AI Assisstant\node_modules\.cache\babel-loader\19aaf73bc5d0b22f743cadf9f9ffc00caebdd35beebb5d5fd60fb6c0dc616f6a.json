{"ast": null, "code": "var _jsxFileName = \"E:\\\\PythonProjects\\\\React.js\\\\Smart AI Assisstant\\\\src\\\\pages\\\\AboutPage.jsx\";\nimport React from 'react';\nimport { Container, Box, Typography, Card, CardContent, Avatar, Grid, Chip, List, ListItem, ListItemIcon, ListItemText, Divider } from '@mui/material';\nimport { Security as SecurityIcon, Business as BusinessIcon, People as PeopleIcon, Timeline as TimelineIcon, Shield as ShieldIcon, Star as StarIcon, TrendingUp as TrendingUpIcon, Public as PublicIcon, CheckCircle as CheckCircleIcon, Award as AwardIcon } from '@mui/icons-material';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AboutPage = /*#__PURE__*/React.memo(_c = () => {\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      minHeight: '100vh',\n      display: 'flex',\n      flexDirection: 'column',\n      justifyContent: 'center',\n      alignItems: 'center',\n      background: 'linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 50%, #121212 100%)',\n      position: 'relative',\n      '&::before': {\n        content: '\"\"',\n        position: 'absolute',\n        top: 0,\n        left: 0,\n        right: 0,\n        bottom: 0,\n        backgroundImage: `\n            radial-gradient(circle at 30% 40%, rgba(102, 126, 234, 0.08) 0%, transparent 50%),\n            radial-gradient(circle at 70% 60%, rgba(118, 75, 162, 0.08) 0%, transparent 50%)\n          `,\n        zIndex: -1\n      }\n    },\n    children: /*#__PURE__*/_jsxDEV(Container, {\n      maxWidth: \"xl\",\n      sx: {\n        py: 8\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        textAlign: \"center\",\n        mb: 10,\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n            borderRadius: '50%',\n            width: 120,\n            height: 120,\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            mx: 'auto',\n            mb: 4,\n            boxShadow: '0 20px 40px rgba(102, 126, 234, 0.3)',\n            position: 'relative',\n            '&::after': {\n              content: '\"\"',\n              position: 'absolute',\n              top: -10,\n              left: -10,\n              right: -10,\n              bottom: -10,\n              borderRadius: '50%',\n              border: '3px solid',\n              borderColor: 'primary.main',\n              opacity: 0.3,\n              animation: 'pulse 2s infinite'\n            },\n            '@keyframes pulse': {\n              '0%': {\n                transform: 'scale(1)',\n                opacity: 0.3\n              },\n              '50%': {\n                transform: 'scale(1.1)',\n                opacity: 0.1\n              },\n              '100%': {\n                transform: 'scale(1)',\n                opacity: 0.3\n              }\n            }\n          },\n          children: /*#__PURE__*/_jsxDEV(SecurityIcon, {\n            sx: {\n              fontSize: 60,\n              color: 'white'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 92,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 59,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h1\",\n          component: \"h1\",\n          gutterBottom: true,\n          fontWeight: \"bold\",\n          sx: {\n            background: 'linear-gradient(45deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%)',\n            backgroundSize: '400% 400%',\n            backgroundClip: 'text',\n            WebkitBackgroundClip: 'text',\n            WebkitTextFillColor: 'transparent',\n            animation: 'gradientText 8s ease infinite',\n            fontSize: {\n              xs: '2.5rem',\n              md: '4rem'\n            },\n            mb: 3,\n            '@keyframes gradientText': {\n              '0%': {\n                backgroundPosition: '0% 50%'\n              },\n              '50%': {\n                backgroundPosition: '100% 50%'\n              },\n              '100%': {\n                backgroundPosition: '0% 50%'\n              }\n            }\n          },\n          children: \"About AI Security Guard Inc.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 95,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h4\",\n          color: \"rgba(255, 255, 255, 0.8)\",\n          maxWidth: \"900px\",\n          mx: \"auto\",\n          sx: {\n            lineHeight: 1.6,\n            fontSize: {\n              xs: '1.2rem',\n              md: '1.5rem'\n            },\n            mb: 6\n          },\n          children: \"Leading the future of cybersecurity with artificial intelligence and innovation\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 119,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          display: \"flex\",\n          justifyContent: \"center\",\n          gap: 2,\n          flexWrap: \"wrap\",\n          mb: 8,\n          children: [/*#__PURE__*/_jsxDEV(Chip, {\n            label: \"Founded 2020\",\n            color: \"primary\",\n            variant: \"outlined\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 134,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Chip, {\n            label: \"10,000+ Customers\",\n            color: \"success\",\n            variant: \"outlined\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 135,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Chip, {\n            label: \"99.9% Uptime\",\n            color: \"info\",\n            variant: \"outlined\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 136,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Chip, {\n            label: \"24/7 Support\",\n            color: \"warning\",\n            variant: \"outlined\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 137,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 133,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 58,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 6,\n        mb: 10,\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 6,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            sx: {\n              height: '100%',\n              background: 'linear-gradient(135deg, rgba(12, 12, 12, 0.95) 0%, rgba(26, 26, 26, 0.95) 100%)',\n              backdropFilter: 'blur(25px)',\n              border: '1px solid rgba(102, 126, 234, 0.2)',\n              borderRadius: 4,\n              transition: 'all 0.3s ease',\n              '&:hover': {\n                transform: 'translateY(-8px)',\n                boxShadow: '0 20px 60px rgba(102, 126, 234, 0.3)'\n              }\n            },\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              sx: {\n                p: 4\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                display: \"flex\",\n                alignItems: \"center\",\n                mb: 3,\n                children: [/*#__PURE__*/_jsxDEV(BusinessIcon, {\n                  sx: {\n                    fontSize: 32,\n                    color: 'primary.main',\n                    mr: 2\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 160,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h4\",\n                  fontWeight: \"bold\",\n                  color: \"white\",\n                  children: \"Our Company\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 161,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 159,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body1\",\n                color: \"rgba(255, 255, 255, 0.9)\",\n                sx: {\n                  mb: 3,\n                  lineHeight: 1.8\n                },\n                children: \"AI Security Guard Inc. was founded in 2020 with a vision to revolutionize cybersecurity through artificial intelligence. We are a team of cybersecurity experts, AI researchers, and software engineers dedicated to protecting businesses and individuals from evolving cyber threats.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 165,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body1\",\n                color: \"rgba(255, 255, 255, 0.9)\",\n                sx: {\n                  lineHeight: 1.8\n                },\n                children: \"Our headquarters are located in San Francisco, California, with additional offices in New York, London, and Tokyo. We serve customers across 50+ countries and have processed over 100 million security scans to date.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 170,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 158,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 144,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 143,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 6,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            sx: {\n              height: '100%',\n              background: 'linear-gradient(135deg, rgba(12, 12, 12, 0.95) 0%, rgba(26, 26, 26, 0.95) 100%)',\n              backdropFilter: 'blur(25px)',\n              border: '1px solid rgba(244, 67, 54, 0.2)',\n              borderRadius: 4,\n              transition: 'all 0.3s ease',\n              '&:hover': {\n                transform: 'translateY(-8px)',\n                boxShadow: '0 20px 60px rgba(244, 67, 54, 0.3)'\n              }\n            },\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              sx: {\n                p: 4\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                display: \"flex\",\n                alignItems: \"center\",\n                mb: 3,\n                children: [/*#__PURE__*/_jsxDEV(ShieldIcon, {\n                  sx: {\n                    fontSize: 32,\n                    color: 'error.main',\n                    mr: 2\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 196,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h4\",\n                  fontWeight: \"bold\",\n                  color: \"white\",\n                  children: \"Our Mission\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 197,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 195,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body1\",\n                color: \"rgba(255, 255, 255, 0.9)\",\n                sx: {\n                  mb: 3,\n                  lineHeight: 1.8\n                },\n                children: \"To democratize advanced cybersecurity by making AI-powered threat detection accessible to organizations of all sizes. We believe that every business deserves enterprise-grade security protection, regardless of their technical expertise or budget.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 201,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body1\",\n                color: \"rgba(255, 255, 255, 0.9)\",\n                sx: {\n                  lineHeight: 1.8\n                },\n                children: \"We are committed to staying ahead of cybercriminals by continuously innovating our AI algorithms and expanding our threat intelligence network to provide the most comprehensive security coverage available.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 206,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 194,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 180,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 179,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 142,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        textAlign: \"center\",\n        mb: 10,\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h3\",\n          fontWeight: \"bold\",\n          color: \"white\",\n          mb: 6,\n          children: \"Why Choose Us?\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 217,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 4,\n          mb: 8,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              sx: {\n                background: 'linear-gradient(135deg, rgba(12, 12, 12, 0.95) 0%, rgba(26, 26, 26, 0.95) 100%)',\n                backdropFilter: 'blur(25px)',\n                border: '1px solid rgba(102, 126, 234, 0.2)',\n                borderRadius: 4,\n                height: '100%',\n                transition: 'all 0.3s ease',\n                '&:hover': {\n                  transform: 'translateY(-8px)',\n                  boxShadow: '0 20px 60px rgba(102, 126, 234, 0.3)'\n                }\n              },\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                sx: {\n                  p: 4\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h5\",\n                  color: \"white\",\n                  gutterBottom: true,\n                  fontWeight: \"600\",\n                  sx: {\n                    mb: 3\n                  },\n                  children: \"\\uD83D\\uDE80 Advanced AI Technology\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 238,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body1\",\n                  color: \"rgba(255, 255, 255, 0.9)\",\n                  sx: {\n                    lineHeight: 1.8\n                  },\n                  children: \"Our proprietary AI algorithms detect threats with 99.7% accuracy, staying ahead of emerging cyber threats through continuous machine learning and real-time threat intelligence updates.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 241,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 237,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 223,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 222,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              sx: {\n                background: 'linear-gradient(135deg, rgba(12, 12, 12, 0.95) 0%, rgba(26, 26, 26, 0.95) 100%)',\n                backdropFilter: 'blur(25px)',\n                border: '1px solid rgba(244, 67, 54, 0.2)',\n                borderRadius: 4,\n                height: '100%',\n                transition: 'all 0.3s ease',\n                '&:hover': {\n                  transform: 'translateY(-8px)',\n                  boxShadow: '0 20px 60px rgba(244, 67, 54, 0.3)'\n                }\n              },\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                sx: {\n                  p: 4\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h5\",\n                  color: \"white\",\n                  gutterBottom: true,\n                  fontWeight: \"600\",\n                  sx: {\n                    mb: 3\n                  },\n                  children: \"\\u26A1 Lightning-Fast Response\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 265,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body1\",\n                  color: \"rgba(255, 255, 255, 0.9)\",\n                  sx: {\n                    lineHeight: 1.8\n                  },\n                  children: \"Real-time threat detection and response in under 0.3 seconds, protecting your assets instantly with automated incident response and immediate threat neutralization.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 268,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 264,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 250,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 249,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              sx: {\n                background: 'linear-gradient(135deg, rgba(12, 12, 12, 0.95) 0%, rgba(26, 26, 26, 0.95) 100%)',\n                backdropFilter: 'blur(25px)',\n                border: '1px solid rgba(76, 175, 80, 0.2)',\n                borderRadius: 4,\n                height: '100%',\n                transition: 'all 0.3s ease',\n                '&:hover': {\n                  transform: 'translateY(-8px)',\n                  boxShadow: '0 20px 60px rgba(76, 175, 80, 0.3)'\n                }\n              },\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                sx: {\n                  p: 4\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h5\",\n                  color: \"white\",\n                  gutterBottom: true,\n                  fontWeight: \"600\",\n                  sx: {\n                    mb: 3\n                  },\n                  children: \"\\uD83D\\uDEE1\\uFE0F Enterprise-Grade Security\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 292,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body1\",\n                  color: \"rgba(255, 255, 255, 0.9)\",\n                  sx: {\n                    lineHeight: 1.8\n                  },\n                  children: \"SOC 2 compliant with military-grade encryption, trusted by Fortune 500 companies worldwide. Our security infrastructure meets the highest industry standards.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 295,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 291,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 277,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 276,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              sx: {\n                background: 'linear-gradient(135deg, rgba(12, 12, 12, 0.95) 0%, rgba(26, 26, 26, 0.95) 100%)',\n                backdropFilter: 'blur(25px)',\n                border: '1px solid rgba(156, 39, 176, 0.2)',\n                borderRadius: 4,\n                height: '100%',\n                transition: 'all 0.3s ease',\n                '&:hover': {\n                  transform: 'translateY(-8px)',\n                  boxShadow: '0 20px 60px rgba(156, 39, 176, 0.3)'\n                }\n              },\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                sx: {\n                  p: 4\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h5\",\n                  color: \"white\",\n                  gutterBottom: true,\n                  fontWeight: \"600\",\n                  sx: {\n                    mb: 3\n                  },\n                  children: \"\\uD83D\\uDCCA Comprehensive Analytics\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 319,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body1\",\n                  color: \"rgba(255, 255, 255, 0.9)\",\n                  sx: {\n                    lineHeight: 1.8\n                  },\n                  children: \"Detailed security reports and insights to help you understand and improve your security posture with actionable recommendations and trend analysis.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 322,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 318,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 304,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 303,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 221,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 216,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        textAlign: \"center\",\n        mb: 10,\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h3\",\n          fontWeight: \"bold\",\n          color: \"white\",\n          mb: 6,\n          children: \"Our Impact\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 334,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 4,\n          children: [{\n            icon: /*#__PURE__*/_jsxDEV(PeopleIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 339,\n              columnNumber: 23\n            }, this),\n            value: '10,000+',\n            label: 'Active Users',\n            color: '#667eea'\n          }, {\n            icon: /*#__PURE__*/_jsxDEV(ShieldIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 340,\n              columnNumber: 23\n            }, this),\n            value: '100M+',\n            label: 'Threats Blocked',\n            color: '#f093fb'\n          }, {\n            icon: /*#__PURE__*/_jsxDEV(PublicIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 341,\n              columnNumber: 23\n            }, this),\n            value: '50+',\n            label: 'Countries Served',\n            color: '#4facfe'\n          }, {\n            icon: /*#__PURE__*/_jsxDEV(TrendingUpIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 342,\n              columnNumber: 23\n            }, this),\n            value: '99.7%',\n            label: 'Detection Accuracy',\n            color: '#43e97b'\n          }].map((stat, index) => /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 6,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              sx: {\n                background: 'linear-gradient(135deg, rgba(12, 12, 12, 0.95) 0%, rgba(26, 26, 26, 0.95) 100%)',\n                backdropFilter: 'blur(25px)',\n                border: `1px solid ${stat.color}40`,\n                borderRadius: 4,\n                textAlign: 'center',\n                transition: 'all 0.3s ease',\n                '&:hover': {\n                  transform: 'translateY(-8px) scale(1.05)',\n                  boxShadow: `0 20px 60px ${stat.color}30`\n                }\n              },\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                sx: {\n                  p: 4\n                },\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    background: `linear-gradient(135deg, ${stat.color}20, ${stat.color}10)`,\n                    borderRadius: '50%',\n                    width: 64,\n                    height: 64,\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    mx: 'auto',\n                    mb: 2\n                  },\n                  children: /*#__PURE__*/React.cloneElement(stat.icon, {\n                    sx: {\n                      fontSize: 32,\n                      color: stat.color\n                    }\n                  })\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 360,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h3\",\n                  fontWeight: \"bold\",\n                  sx: {\n                    color: stat.color,\n                    mb: 1\n                  },\n                  children: stat.value\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 375,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  color: \"rgba(255, 255, 255, 0.8)\",\n                  children: stat.label\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 378,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 359,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 345,\n              columnNumber: 17\n            }, this)\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 344,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 337,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 333,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Card, {\n        sx: {\n          background: 'linear-gradient(135deg, rgba(12, 12, 12, 0.95) 0%, rgba(26, 26, 26, 0.95) 100%)',\n          backdropFilter: 'blur(25px)',\n          border: '1px solid rgba(102, 126, 234, 0.2)',\n          borderRadius: 4,\n          textAlign: 'center',\n          position: 'relative',\n          overflow: 'hidden',\n          '&::before': {\n            content: '\"\"',\n            position: 'absolute',\n            top: 0,\n            left: 0,\n            right: 0,\n            height: '4px',\n            background: 'linear-gradient(90deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%)',\n            backgroundSize: '400% 400%',\n            animation: 'gradientShift 8s ease infinite'\n          },\n          '@keyframes gradientShift': {\n            '0%': {\n              backgroundPosition: '0% 50%'\n            },\n            '50%': {\n              backgroundPosition: '100% 50%'\n            },\n            '100%': {\n              backgroundPosition: '0% 50%'\n            }\n          }\n        },\n        children: /*#__PURE__*/_jsxDEV(CardContent, {\n          sx: {\n            p: 6\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h4\",\n            fontWeight: \"bold\",\n            color: \"white\",\n            mb: 3,\n            children: \"Get in Touch\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 417,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            color: \"rgba(255, 255, 255, 0.9)\",\n            sx: {\n              mb: 4,\n              lineHeight: 1.8\n            },\n            children: \"Ready to secure your digital assets with AI-powered protection? Contact our team of cybersecurity experts today.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 420,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            container: true,\n            spacing: 4,\n            justifyContent: \"center\",\n            children: [/*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 4,\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                color: \"primary.main\",\n                gutterBottom: true,\n                children: \"Email\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 426,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body1\",\n                color: \"rgba(255, 255, 255, 0.9)\",\n                children: \"<EMAIL>\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 429,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 425,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 4,\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                color: \"primary.main\",\n                gutterBottom: true,\n                children: \"Phone\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 434,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body1\",\n                color: \"rgba(255, 255, 255, 0.9)\",\n                children: \"+****************\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 437,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 433,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 4,\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                color: \"primary.main\",\n                gutterBottom: true,\n                children: \"Address\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 442,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body1\",\n                color: \"rgba(255, 255, 255, 0.9)\",\n                children: \"San Francisco, CA, USA\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 445,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 441,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 424,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 416,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 389,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 56,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 32,\n    columnNumber: 5\n  }, this);\n});\n_c2 = AboutPage;\nAboutPage.displayName = 'AboutPage';\nexport default AboutPage;\nvar _c, _c2;\n$RefreshReg$(_c, \"AboutPage$React.memo\");\n$RefreshReg$(_c2, \"AboutPage\");", "map": {"version": 3, "names": ["React", "Container", "Box", "Typography", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Avatar", "Grid", "Chip", "List", "ListItem", "ListItemIcon", "ListItemText", "Divider", "Security", "SecurityIcon", "Business", "BusinessIcon", "People", "PeopleIcon", "Timeline", "TimelineIcon", "Shield", "ShieldIcon", "Star", "StarIcon", "TrendingUp", "TrendingUpIcon", "Public", "PublicIcon", "CheckCircle", "CheckCircleIcon", "Award", "AwardIcon", "jsxDEV", "_jsxDEV", "AboutPage", "memo", "_c", "sx", "minHeight", "display", "flexDirection", "justifyContent", "alignItems", "background", "position", "content", "top", "left", "right", "bottom", "backgroundImage", "zIndex", "children", "max<PERSON><PERSON><PERSON>", "py", "textAlign", "mb", "borderRadius", "width", "height", "mx", "boxShadow", "border", "borderColor", "opacity", "animation", "transform", "fontSize", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "component", "gutterBottom", "fontWeight", "backgroundSize", "backgroundClip", "WebkitBackgroundClip", "WebkitTextFillColor", "xs", "md", "backgroundPosition", "lineHeight", "gap", "flexWrap", "label", "container", "spacing", "item", "<PERSON><PERSON>ilter", "transition", "p", "mr", "icon", "value", "map", "stat", "index", "cloneElement", "overflow", "_c2", "displayName", "$RefreshReg$"], "sources": ["E:/PythonProjects/React.js/Smart AI Assisstant/src/pages/AboutPage.jsx"], "sourcesContent": ["import React from 'react';\nimport {\n  Container,\n  Box,\n  Typography,\n  Card,\n  CardContent,\n  Avatar,\n  Grid,\n  Chip,\n  List,\n  ListItem,\n  ListItemIcon,\n  ListItemText,\n  Divider,\n} from '@mui/material';\nimport {\n  Security as SecurityIcon,\n  Business as BusinessIcon,\n  People as PeopleIcon,\n  Timeline as TimelineIcon,\n  Shield as ShieldIcon,\n  Star as StarIcon,\n  TrendingUp as TrendingUpIcon,\n  Public as PublicIcon,\n  CheckCircle as CheckCircleIcon,\n  Award as AwardIcon,\n} from '@mui/icons-material';\n\nconst AboutPage = React.memo(() => {\n  return (\n    <Box\n      sx={{\n        minHeight: '100vh',\n        display: 'flex',\n        flexDirection: 'column',\n        justifyContent: 'center',\n        alignItems: 'center',\n        background: 'linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 50%, #121212 100%)',\n        position: 'relative',\n        '&::before': {\n          content: '\"\"',\n          position: 'absolute',\n          top: 0,\n          left: 0,\n          right: 0,\n          bottom: 0,\n          backgroundImage: `\n            radial-gradient(circle at 30% 40%, rgba(102, 126, 234, 0.08) 0%, transparent 50%),\n            radial-gradient(circle at 70% 60%, rgba(118, 75, 162, 0.08) 0%, transparent 50%)\n          `,\n          zIndex: -1,\n        },\n      }}\n    >\n      <Container maxWidth=\"xl\" sx={{ py: 8 }}>\n        {/* Hero Section */}\n        <Box textAlign=\"center\" mb={10}>\n          <Box\n            sx={{\n              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n              borderRadius: '50%',\n              width: 120,\n              height: 120,\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              mx: 'auto',\n              mb: 4,\n              boxShadow: '0 20px 40px rgba(102, 126, 234, 0.3)',\n              position: 'relative',\n              '&::after': {\n                content: '\"\"',\n                position: 'absolute',\n                top: -10,\n                left: -10,\n                right: -10,\n                bottom: -10,\n                borderRadius: '50%',\n                border: '3px solid',\n                borderColor: 'primary.main',\n                opacity: 0.3,\n                animation: 'pulse 2s infinite',\n              },\n              '@keyframes pulse': {\n                '0%': { transform: 'scale(1)', opacity: 0.3 },\n                '50%': { transform: 'scale(1.1)', opacity: 0.1 },\n                '100%': { transform: 'scale(1)', opacity: 0.3 },\n              },\n            }}\n          >\n            <SecurityIcon sx={{ fontSize: 60, color: 'white' }} />\n          </Box>\n\n          <Typography\n            variant=\"h1\"\n            component=\"h1\"\n            gutterBottom\n            fontWeight=\"bold\"\n            sx={{\n              background: 'linear-gradient(45deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%)',\n              backgroundSize: '400% 400%',\n              backgroundClip: 'text',\n              WebkitBackgroundClip: 'text',\n              WebkitTextFillColor: 'transparent',\n              animation: 'gradientText 8s ease infinite',\n              fontSize: { xs: '2.5rem', md: '4rem' },\n              mb: 3,\n              '@keyframes gradientText': {\n                '0%': { backgroundPosition: '0% 50%' },\n                '50%': { backgroundPosition: '100% 50%' },\n                '100%': { backgroundPosition: '0% 50%' },\n              },\n            }}\n          >\n            About AI Security Guard Inc.\n          </Typography>\n\n          <Typography\n            variant=\"h4\"\n            color=\"rgba(255, 255, 255, 0.8)\"\n            maxWidth=\"900px\"\n            mx=\"auto\"\n            sx={{\n              lineHeight: 1.6,\n              fontSize: { xs: '1.2rem', md: '1.5rem' },\n              mb: 6,\n            }}\n          >\n            Leading the future of cybersecurity with artificial intelligence and innovation\n          </Typography>\n\n          <Box display=\"flex\" justifyContent=\"center\" gap={2} flexWrap=\"wrap\" mb={8}>\n            <Chip label=\"Founded 2020\" color=\"primary\" variant=\"outlined\" />\n            <Chip label=\"10,000+ Customers\" color=\"success\" variant=\"outlined\" />\n            <Chip label=\"99.9% Uptime\" color=\"info\" variant=\"outlined\" />\n            <Chip label=\"24/7 Support\" color=\"warning\" variant=\"outlined\" />\n          </Box>\n        </Box>\n\n        {/* Company Overview */}\n        <Grid container spacing={6} mb={10}>\n          <Grid item xs={12} md={6}>\n            <Card\n              sx={{\n                height: '100%',\n                background: 'linear-gradient(135deg, rgba(12, 12, 12, 0.95) 0%, rgba(26, 26, 26, 0.95) 100%)',\n                backdropFilter: 'blur(25px)',\n                border: '1px solid rgba(102, 126, 234, 0.2)',\n                borderRadius: 4,\n                transition: 'all 0.3s ease',\n                '&:hover': {\n                  transform: 'translateY(-8px)',\n                  boxShadow: '0 20px 60px rgba(102, 126, 234, 0.3)',\n                },\n              }}\n            >\n              <CardContent sx={{ p: 4 }}>\n                <Box display=\"flex\" alignItems=\"center\" mb={3}>\n                  <BusinessIcon sx={{ fontSize: 32, color: 'primary.main', mr: 2 }} />\n                  <Typography variant=\"h4\" fontWeight=\"bold\" color=\"white\">\n                    Our Company\n                  </Typography>\n                </Box>\n                <Typography variant=\"body1\" color=\"rgba(255, 255, 255, 0.9)\" sx={{ mb: 3, lineHeight: 1.8 }}>\n                  AI Security Guard Inc. was founded in 2020 with a vision to revolutionize cybersecurity through\n                  artificial intelligence. We are a team of cybersecurity experts, AI researchers, and software\n                  engineers dedicated to protecting businesses and individuals from evolving cyber threats.\n                </Typography>\n                <Typography variant=\"body1\" color=\"rgba(255, 255, 255, 0.9)\" sx={{ lineHeight: 1.8 }}>\n                  Our headquarters are located in San Francisco, California, with additional offices in New York,\n                  London, and Tokyo. We serve customers across 50+ countries and have processed over 100 million\n                  security scans to date.\n                </Typography>\n              </CardContent>\n            </Card>\n          </Grid>\n\n          <Grid item xs={12} md={6}>\n            <Card\n              sx={{\n                height: '100%',\n                background: 'linear-gradient(135deg, rgba(12, 12, 12, 0.95) 0%, rgba(26, 26, 26, 0.95) 100%)',\n                backdropFilter: 'blur(25px)',\n                border: '1px solid rgba(244, 67, 54, 0.2)',\n                borderRadius: 4,\n                transition: 'all 0.3s ease',\n                '&:hover': {\n                  transform: 'translateY(-8px)',\n                  boxShadow: '0 20px 60px rgba(244, 67, 54, 0.3)',\n                },\n              }}\n            >\n              <CardContent sx={{ p: 4 }}>\n                <Box display=\"flex\" alignItems=\"center\" mb={3}>\n                  <ShieldIcon sx={{ fontSize: 32, color: 'error.main', mr: 2 }} />\n                  <Typography variant=\"h4\" fontWeight=\"bold\" color=\"white\">\n                    Our Mission\n                  </Typography>\n                </Box>\n                <Typography variant=\"body1\" color=\"rgba(255, 255, 255, 0.9)\" sx={{ mb: 3, lineHeight: 1.8 }}>\n                  To democratize advanced cybersecurity by making AI-powered threat detection accessible to\n                  organizations of all sizes. We believe that every business deserves enterprise-grade security\n                  protection, regardless of their technical expertise or budget.\n                </Typography>\n                <Typography variant=\"body1\" color=\"rgba(255, 255, 255, 0.9)\" sx={{ lineHeight: 1.8 }}>\n                  We are committed to staying ahead of cybercriminals by continuously innovating our AI algorithms\n                  and expanding our threat intelligence network to provide the most comprehensive security coverage available.\n                </Typography>\n              </CardContent>\n            </Card>\n          </Grid>\n        </Grid>\n\n        {/* Why Choose Us Section */}\n        <Box textAlign=\"center\" mb={10}>\n          <Typography variant=\"h3\" fontWeight=\"bold\" color=\"white\" mb={6}>\n            Why Choose Us?\n          </Typography>\n\n          <Grid container spacing={4} mb={8}>\n            <Grid item xs={12} md={6}>\n              <Card\n                sx={{\n                  background: 'linear-gradient(135deg, rgba(12, 12, 12, 0.95) 0%, rgba(26, 26, 26, 0.95) 100%)',\n                  backdropFilter: 'blur(25px)',\n                  border: '1px solid rgba(102, 126, 234, 0.2)',\n                  borderRadius: 4,\n                  height: '100%',\n                  transition: 'all 0.3s ease',\n                  '&:hover': {\n                    transform: 'translateY(-8px)',\n                    boxShadow: '0 20px 60px rgba(102, 126, 234, 0.3)',\n                  },\n                }}\n              >\n                <CardContent sx={{ p: 4 }}>\n                  <Typography variant=\"h5\" color=\"white\" gutterBottom fontWeight=\"600\" sx={{ mb: 3 }}>\n                    🚀 Advanced AI Technology\n                  </Typography>\n                  <Typography variant=\"body1\" color=\"rgba(255, 255, 255, 0.9)\" sx={{ lineHeight: 1.8 }}>\n                    Our proprietary AI algorithms detect threats with 99.7% accuracy, staying ahead of emerging cyber threats\n                    through continuous machine learning and real-time threat intelligence updates.\n                  </Typography>\n                </CardContent>\n              </Card>\n            </Grid>\n\n            <Grid item xs={12} md={6}>\n              <Card\n                sx={{\n                  background: 'linear-gradient(135deg, rgba(12, 12, 12, 0.95) 0%, rgba(26, 26, 26, 0.95) 100%)',\n                  backdropFilter: 'blur(25px)',\n                  border: '1px solid rgba(244, 67, 54, 0.2)',\n                  borderRadius: 4,\n                  height: '100%',\n                  transition: 'all 0.3s ease',\n                  '&:hover': {\n                    transform: 'translateY(-8px)',\n                    boxShadow: '0 20px 60px rgba(244, 67, 54, 0.3)',\n                  },\n                }}\n              >\n                <CardContent sx={{ p: 4 }}>\n                  <Typography variant=\"h5\" color=\"white\" gutterBottom fontWeight=\"600\" sx={{ mb: 3 }}>\n                    ⚡ Lightning-Fast Response\n                  </Typography>\n                  <Typography variant=\"body1\" color=\"rgba(255, 255, 255, 0.9)\" sx={{ lineHeight: 1.8 }}>\n                    Real-time threat detection and response in under 0.3 seconds, protecting your assets instantly\n                    with automated incident response and immediate threat neutralization.\n                  </Typography>\n                </CardContent>\n              </Card>\n            </Grid>\n\n            <Grid item xs={12} md={6}>\n              <Card\n                sx={{\n                  background: 'linear-gradient(135deg, rgba(12, 12, 12, 0.95) 0%, rgba(26, 26, 26, 0.95) 100%)',\n                  backdropFilter: 'blur(25px)',\n                  border: '1px solid rgba(76, 175, 80, 0.2)',\n                  borderRadius: 4,\n                  height: '100%',\n                  transition: 'all 0.3s ease',\n                  '&:hover': {\n                    transform: 'translateY(-8px)',\n                    boxShadow: '0 20px 60px rgba(76, 175, 80, 0.3)',\n                  },\n                }}\n              >\n                <CardContent sx={{ p: 4 }}>\n                  <Typography variant=\"h5\" color=\"white\" gutterBottom fontWeight=\"600\" sx={{ mb: 3 }}>\n                    🛡️ Enterprise-Grade Security\n                  </Typography>\n                  <Typography variant=\"body1\" color=\"rgba(255, 255, 255, 0.9)\" sx={{ lineHeight: 1.8 }}>\n                    SOC 2 compliant with military-grade encryption, trusted by Fortune 500 companies worldwide.\n                    Our security infrastructure meets the highest industry standards.\n                  </Typography>\n                </CardContent>\n              </Card>\n            </Grid>\n\n            <Grid item xs={12} md={6}>\n              <Card\n                sx={{\n                  background: 'linear-gradient(135deg, rgba(12, 12, 12, 0.95) 0%, rgba(26, 26, 26, 0.95) 100%)',\n                  backdropFilter: 'blur(25px)',\n                  border: '1px solid rgba(156, 39, 176, 0.2)',\n                  borderRadius: 4,\n                  height: '100%',\n                  transition: 'all 0.3s ease',\n                  '&:hover': {\n                    transform: 'translateY(-8px)',\n                    boxShadow: '0 20px 60px rgba(156, 39, 176, 0.3)',\n                  },\n                }}\n              >\n                <CardContent sx={{ p: 4 }}>\n                  <Typography variant=\"h5\" color=\"white\" gutterBottom fontWeight=\"600\" sx={{ mb: 3 }}>\n                    📊 Comprehensive Analytics\n                  </Typography>\n                  <Typography variant=\"body1\" color=\"rgba(255, 255, 255, 0.9)\" sx={{ lineHeight: 1.8 }}>\n                    Detailed security reports and insights to help you understand and improve your security posture\n                    with actionable recommendations and trend analysis.\n                  </Typography>\n                </CardContent>\n              </Card>\n            </Grid>\n          </Grid>\n        </Box>\n\n        {/* Key Statistics */}\n        <Box textAlign=\"center\" mb={10}>\n          <Typography variant=\"h3\" fontWeight=\"bold\" color=\"white\" mb={6}>\n            Our Impact\n          </Typography>\n          <Grid container spacing={4}>\n            {[\n              { icon: <PeopleIcon />, value: '10,000+', label: 'Active Users', color: '#667eea' },\n              { icon: <ShieldIcon />, value: '100M+', label: 'Threats Blocked', color: '#f093fb' },\n              { icon: <PublicIcon />, value: '50+', label: 'Countries Served', color: '#4facfe' },\n              { icon: <TrendingUpIcon />, value: '99.7%', label: 'Detection Accuracy', color: '#43e97b' },\n            ].map((stat, index) => (\n              <Grid item xs={6} md={3} key={index}>\n                <Card\n                  sx={{\n                    background: 'linear-gradient(135deg, rgba(12, 12, 12, 0.95) 0%, rgba(26, 26, 26, 0.95) 100%)',\n                    backdropFilter: 'blur(25px)',\n                    border: `1px solid ${stat.color}40`,\n                    borderRadius: 4,\n                    textAlign: 'center',\n                    transition: 'all 0.3s ease',\n                    '&:hover': {\n                      transform: 'translateY(-8px) scale(1.05)',\n                      boxShadow: `0 20px 60px ${stat.color}30`,\n                    },\n                  }}\n                >\n                  <CardContent sx={{ p: 4 }}>\n                    <Box\n                      sx={{\n                        background: `linear-gradient(135deg, ${stat.color}20, ${stat.color}10)`,\n                        borderRadius: '50%',\n                        width: 64,\n                        height: 64,\n                        display: 'flex',\n                        alignItems: 'center',\n                        justifyContent: 'center',\n                        mx: 'auto',\n                        mb: 2,\n                      }}\n                    >\n                      {React.cloneElement(stat.icon, { sx: { fontSize: 32, color: stat.color } })}\n                    </Box>\n                    <Typography variant=\"h3\" fontWeight=\"bold\" sx={{ color: stat.color, mb: 1 }}>\n                      {stat.value}\n                    </Typography>\n                    <Typography variant=\"h6\" color=\"rgba(255, 255, 255, 0.8)\">\n                      {stat.label}\n                    </Typography>\n                  </CardContent>\n                </Card>\n              </Grid>\n            ))}\n          </Grid>\n        </Box>\n\n        {/* Contact Information */}\n        <Card\n          sx={{\n            background: 'linear-gradient(135deg, rgba(12, 12, 12, 0.95) 0%, rgba(26, 26, 26, 0.95) 100%)',\n            backdropFilter: 'blur(25px)',\n            border: '1px solid rgba(102, 126, 234, 0.2)',\n            borderRadius: 4,\n            textAlign: 'center',\n            position: 'relative',\n            overflow: 'hidden',\n            '&::before': {\n              content: '\"\"',\n              position: 'absolute',\n              top: 0,\n              left: 0,\n              right: 0,\n              height: '4px',\n              background: 'linear-gradient(90deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%)',\n              backgroundSize: '400% 400%',\n              animation: 'gradientShift 8s ease infinite',\n            },\n            '@keyframes gradientShift': {\n              '0%': { backgroundPosition: '0% 50%' },\n              '50%': { backgroundPosition: '100% 50%' },\n              '100%': { backgroundPosition: '0% 50%' },\n            },\n          }}\n        >\n          <CardContent sx={{ p: 6 }}>\n            <Typography variant=\"h4\" fontWeight=\"bold\" color=\"white\" mb={3}>\n              Get in Touch\n            </Typography>\n            <Typography variant=\"body1\" color=\"rgba(255, 255, 255, 0.9)\" sx={{ mb: 4, lineHeight: 1.8 }}>\n              Ready to secure your digital assets with AI-powered protection?\n              Contact our team of cybersecurity experts today.\n            </Typography>\n            <Grid container spacing={4} justifyContent=\"center\">\n              <Grid item xs={12} md={4}>\n                <Typography variant=\"h6\" color=\"primary.main\" gutterBottom>\n                  Email\n                </Typography>\n                <Typography variant=\"body1\" color=\"rgba(255, 255, 255, 0.9)\">\n                  <EMAIL>\n                </Typography>\n              </Grid>\n              <Grid item xs={12} md={4}>\n                <Typography variant=\"h6\" color=\"primary.main\" gutterBottom>\n                  Phone\n                </Typography>\n                <Typography variant=\"body1\" color=\"rgba(255, 255, 255, 0.9)\">\n                  +****************\n                </Typography>\n              </Grid>\n              <Grid item xs={12} md={4}>\n                <Typography variant=\"h6\" color=\"primary.main\" gutterBottom>\n                  Address\n                </Typography>\n                <Typography variant=\"body1\" color=\"rgba(255, 255, 255, 0.9)\">\n                  San Francisco, CA, USA\n                </Typography>\n              </Grid>\n            </Grid>\n          </CardContent>\n        </Card>\n      </Container>\n    </Box>\n  );\n});\n\nAboutPage.displayName = 'AboutPage';\n\nexport default AboutPage;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SACEC,SAAS,EACTC,GAAG,EACHC,UAAU,EACVC,IAAI,EACJC,WAAW,EACXC,MAAM,EACNC,IAAI,EACJC,IAAI,EACJC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,YAAY,EACZC,OAAO,QACF,eAAe;AACtB,SACEC,QAAQ,IAAIC,YAAY,EACxBC,QAAQ,IAAIC,YAAY,EACxBC,MAAM,IAAIC,UAAU,EACpBC,QAAQ,IAAIC,YAAY,EACxBC,MAAM,IAAIC,UAAU,EACpBC,IAAI,IAAIC,QAAQ,EAChBC,UAAU,IAAIC,cAAc,EAC5BC,MAAM,IAAIC,UAAU,EACpBC,WAAW,IAAIC,eAAe,EAC9BC,KAAK,IAAIC,SAAS,QACb,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7B,MAAMC,SAAS,gBAAGpC,KAAK,CAACqC,IAAI,CAAAC,EAAA,GAACA,CAAA,KAAM;EACjC,oBACEH,OAAA,CAACjC,GAAG;IACFqC,EAAE,EAAE;MACFC,SAAS,EAAE,OAAO;MAClBC,OAAO,EAAE,MAAM;MACfC,aAAa,EAAE,QAAQ;MACvBC,cAAc,EAAE,QAAQ;MACxBC,UAAU,EAAE,QAAQ;MACpBC,UAAU,EAAE,gEAAgE;MAC5EC,QAAQ,EAAE,UAAU;MACpB,WAAW,EAAE;QACXC,OAAO,EAAE,IAAI;QACbD,QAAQ,EAAE,UAAU;QACpBE,GAAG,EAAE,CAAC;QACNC,IAAI,EAAE,CAAC;QACPC,KAAK,EAAE,CAAC;QACRC,MAAM,EAAE,CAAC;QACTC,eAAe,EAAE;AAC3B;AACA;AACA,WAAW;QACDC,MAAM,EAAE,CAAC;MACX;IACF,CAAE;IAAAC,QAAA,eAEFnB,OAAA,CAAClC,SAAS;MAACsD,QAAQ,EAAC,IAAI;MAAChB,EAAE,EAAE;QAAEiB,EAAE,EAAE;MAAE,CAAE;MAAAF,QAAA,gBAErCnB,OAAA,CAACjC,GAAG;QAACuD,SAAS,EAAC,QAAQ;QAACC,EAAE,EAAE,EAAG;QAAAJ,QAAA,gBAC7BnB,OAAA,CAACjC,GAAG;UACFqC,EAAE,EAAE;YACFM,UAAU,EAAE,mDAAmD;YAC/Dc,YAAY,EAAE,KAAK;YACnBC,KAAK,EAAE,GAAG;YACVC,MAAM,EAAE,GAAG;YACXpB,OAAO,EAAE,MAAM;YACfG,UAAU,EAAE,QAAQ;YACpBD,cAAc,EAAE,QAAQ;YACxBmB,EAAE,EAAE,MAAM;YACVJ,EAAE,EAAE,CAAC;YACLK,SAAS,EAAE,sCAAsC;YACjDjB,QAAQ,EAAE,UAAU;YACpB,UAAU,EAAE;cACVC,OAAO,EAAE,IAAI;cACbD,QAAQ,EAAE,UAAU;cACpBE,GAAG,EAAE,CAAC,EAAE;cACRC,IAAI,EAAE,CAAC,EAAE;cACTC,KAAK,EAAE,CAAC,EAAE;cACVC,MAAM,EAAE,CAAC,EAAE;cACXQ,YAAY,EAAE,KAAK;cACnBK,MAAM,EAAE,WAAW;cACnBC,WAAW,EAAE,cAAc;cAC3BC,OAAO,EAAE,GAAG;cACZC,SAAS,EAAE;YACb,CAAC;YACD,kBAAkB,EAAE;cAClB,IAAI,EAAE;gBAAEC,SAAS,EAAE,UAAU;gBAAEF,OAAO,EAAE;cAAI,CAAC;cAC7C,KAAK,EAAE;gBAAEE,SAAS,EAAE,YAAY;gBAAEF,OAAO,EAAE;cAAI,CAAC;cAChD,MAAM,EAAE;gBAAEE,SAAS,EAAE,UAAU;gBAAEF,OAAO,EAAE;cAAI;YAChD;UACF,CAAE;UAAAZ,QAAA,eAEFnB,OAAA,CAACpB,YAAY;YAACwB,EAAE,EAAE;cAAE8B,QAAQ,EAAE,EAAE;cAAEC,KAAK,EAAE;YAAQ;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnD,CAAC,eAENvC,OAAA,CAAChC,UAAU;UACTwE,OAAO,EAAC,IAAI;UACZC,SAAS,EAAC,IAAI;UACdC,YAAY;UACZC,UAAU,EAAC,MAAM;UACjBvC,EAAE,EAAE;YACFM,UAAU,EAAE,yFAAyF;YACrGkC,cAAc,EAAE,WAAW;YAC3BC,cAAc,EAAE,MAAM;YACtBC,oBAAoB,EAAE,MAAM;YAC5BC,mBAAmB,EAAE,aAAa;YAClCf,SAAS,EAAE,+BAA+B;YAC1CE,QAAQ,EAAE;cAAEc,EAAE,EAAE,QAAQ;cAAEC,EAAE,EAAE;YAAO,CAAC;YACtC1B,EAAE,EAAE,CAAC;YACL,yBAAyB,EAAE;cACzB,IAAI,EAAE;gBAAE2B,kBAAkB,EAAE;cAAS,CAAC;cACtC,KAAK,EAAE;gBAAEA,kBAAkB,EAAE;cAAW,CAAC;cACzC,MAAM,EAAE;gBAAEA,kBAAkB,EAAE;cAAS;YACzC;UACF,CAAE;UAAA/B,QAAA,EACH;QAED;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAEbvC,OAAA,CAAChC,UAAU;UACTwE,OAAO,EAAC,IAAI;UACZL,KAAK,EAAC,0BAA0B;UAChCf,QAAQ,EAAC,OAAO;UAChBO,EAAE,EAAC,MAAM;UACTvB,EAAE,EAAE;YACF+C,UAAU,EAAE,GAAG;YACfjB,QAAQ,EAAE;cAAEc,EAAE,EAAE,QAAQ;cAAEC,EAAE,EAAE;YAAS,CAAC;YACxC1B,EAAE,EAAE;UACN,CAAE;UAAAJ,QAAA,EACH;QAED;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAEbvC,OAAA,CAACjC,GAAG;UAACuC,OAAO,EAAC,MAAM;UAACE,cAAc,EAAC,QAAQ;UAAC4C,GAAG,EAAE,CAAE;UAACC,QAAQ,EAAC,MAAM;UAAC9B,EAAE,EAAE,CAAE;UAAAJ,QAAA,gBACxEnB,OAAA,CAAC3B,IAAI;YAACiF,KAAK,EAAC,cAAc;YAACnB,KAAK,EAAC,SAAS;YAACK,OAAO,EAAC;UAAU;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAChEvC,OAAA,CAAC3B,IAAI;YAACiF,KAAK,EAAC,mBAAmB;YAACnB,KAAK,EAAC,SAAS;YAACK,OAAO,EAAC;UAAU;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACrEvC,OAAA,CAAC3B,IAAI;YAACiF,KAAK,EAAC,cAAc;YAACnB,KAAK,EAAC,MAAM;YAACK,OAAO,EAAC;UAAU;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC7DvC,OAAA,CAAC3B,IAAI;YAACiF,KAAK,EAAC,cAAc;YAACnB,KAAK,EAAC,SAAS;YAACK,OAAO,EAAC;UAAU;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7D,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNvC,OAAA,CAAC5B,IAAI;QAACmF,SAAS;QAACC,OAAO,EAAE,CAAE;QAACjC,EAAE,EAAE,EAAG;QAAAJ,QAAA,gBACjCnB,OAAA,CAAC5B,IAAI;UAACqF,IAAI;UAACT,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAA9B,QAAA,eACvBnB,OAAA,CAAC/B,IAAI;YACHmC,EAAE,EAAE;cACFsB,MAAM,EAAE,MAAM;cACdhB,UAAU,EAAE,iFAAiF;cAC7FgD,cAAc,EAAE,YAAY;cAC5B7B,MAAM,EAAE,oCAAoC;cAC5CL,YAAY,EAAE,CAAC;cACfmC,UAAU,EAAE,eAAe;cAC3B,SAAS,EAAE;gBACT1B,SAAS,EAAE,kBAAkB;gBAC7BL,SAAS,EAAE;cACb;YACF,CAAE;YAAAT,QAAA,eAEFnB,OAAA,CAAC9B,WAAW;cAACkC,EAAE,EAAE;gBAAEwD,CAAC,EAAE;cAAE,CAAE;cAAAzC,QAAA,gBACxBnB,OAAA,CAACjC,GAAG;gBAACuC,OAAO,EAAC,MAAM;gBAACG,UAAU,EAAC,QAAQ;gBAACc,EAAE,EAAE,CAAE;gBAAAJ,QAAA,gBAC5CnB,OAAA,CAAClB,YAAY;kBAACsB,EAAE,EAAE;oBAAE8B,QAAQ,EAAE,EAAE;oBAAEC,KAAK,EAAE,cAAc;oBAAE0B,EAAE,EAAE;kBAAE;gBAAE;kBAAAzB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACpEvC,OAAA,CAAChC,UAAU;kBAACwE,OAAO,EAAC,IAAI;kBAACG,UAAU,EAAC,MAAM;kBAACR,KAAK,EAAC,OAAO;kBAAAhB,QAAA,EAAC;gBAEzD;kBAAAiB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACNvC,OAAA,CAAChC,UAAU;gBAACwE,OAAO,EAAC,OAAO;gBAACL,KAAK,EAAC,0BAA0B;gBAAC/B,EAAE,EAAE;kBAAEmB,EAAE,EAAE,CAAC;kBAAE4B,UAAU,EAAE;gBAAI,CAAE;gBAAAhC,QAAA,EAAC;cAI7F;gBAAAiB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbvC,OAAA,CAAChC,UAAU;gBAACwE,OAAO,EAAC,OAAO;gBAACL,KAAK,EAAC,0BAA0B;gBAAC/B,EAAE,EAAE;kBAAE+C,UAAU,EAAE;gBAAI,CAAE;gBAAAhC,QAAA,EAAC;cAItF;gBAAAiB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEPvC,OAAA,CAAC5B,IAAI;UAACqF,IAAI;UAACT,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAA9B,QAAA,eACvBnB,OAAA,CAAC/B,IAAI;YACHmC,EAAE,EAAE;cACFsB,MAAM,EAAE,MAAM;cACdhB,UAAU,EAAE,iFAAiF;cAC7FgD,cAAc,EAAE,YAAY;cAC5B7B,MAAM,EAAE,kCAAkC;cAC1CL,YAAY,EAAE,CAAC;cACfmC,UAAU,EAAE,eAAe;cAC3B,SAAS,EAAE;gBACT1B,SAAS,EAAE,kBAAkB;gBAC7BL,SAAS,EAAE;cACb;YACF,CAAE;YAAAT,QAAA,eAEFnB,OAAA,CAAC9B,WAAW;cAACkC,EAAE,EAAE;gBAAEwD,CAAC,EAAE;cAAE,CAAE;cAAAzC,QAAA,gBACxBnB,OAAA,CAACjC,GAAG;gBAACuC,OAAO,EAAC,MAAM;gBAACG,UAAU,EAAC,QAAQ;gBAACc,EAAE,EAAE,CAAE;gBAAAJ,QAAA,gBAC5CnB,OAAA,CAACZ,UAAU;kBAACgB,EAAE,EAAE;oBAAE8B,QAAQ,EAAE,EAAE;oBAAEC,KAAK,EAAE,YAAY;oBAAE0B,EAAE,EAAE;kBAAE;gBAAE;kBAAAzB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAChEvC,OAAA,CAAChC,UAAU;kBAACwE,OAAO,EAAC,IAAI;kBAACG,UAAU,EAAC,MAAM;kBAACR,KAAK,EAAC,OAAO;kBAAAhB,QAAA,EAAC;gBAEzD;kBAAAiB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACNvC,OAAA,CAAChC,UAAU;gBAACwE,OAAO,EAAC,OAAO;gBAACL,KAAK,EAAC,0BAA0B;gBAAC/B,EAAE,EAAE;kBAAEmB,EAAE,EAAE,CAAC;kBAAE4B,UAAU,EAAE;gBAAI,CAAE;gBAAAhC,QAAA,EAAC;cAI7F;gBAAAiB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbvC,OAAA,CAAChC,UAAU;gBAACwE,OAAO,EAAC,OAAO;gBAACL,KAAK,EAAC,0BAA0B;gBAAC/B,EAAE,EAAE;kBAAE+C,UAAU,EAAE;gBAAI,CAAE;gBAAAhC,QAAA,EAAC;cAGtF;gBAAAiB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGPvC,OAAA,CAACjC,GAAG;QAACuD,SAAS,EAAC,QAAQ;QAACC,EAAE,EAAE,EAAG;QAAAJ,QAAA,gBAC7BnB,OAAA,CAAChC,UAAU;UAACwE,OAAO,EAAC,IAAI;UAACG,UAAU,EAAC,MAAM;UAACR,KAAK,EAAC,OAAO;UAACZ,EAAE,EAAE,CAAE;UAAAJ,QAAA,EAAC;QAEhE;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAEbvC,OAAA,CAAC5B,IAAI;UAACmF,SAAS;UAACC,OAAO,EAAE,CAAE;UAACjC,EAAE,EAAE,CAAE;UAAAJ,QAAA,gBAChCnB,OAAA,CAAC5B,IAAI;YAACqF,IAAI;YAACT,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA9B,QAAA,eACvBnB,OAAA,CAAC/B,IAAI;cACHmC,EAAE,EAAE;gBACFM,UAAU,EAAE,iFAAiF;gBAC7FgD,cAAc,EAAE,YAAY;gBAC5B7B,MAAM,EAAE,oCAAoC;gBAC5CL,YAAY,EAAE,CAAC;gBACfE,MAAM,EAAE,MAAM;gBACdiC,UAAU,EAAE,eAAe;gBAC3B,SAAS,EAAE;kBACT1B,SAAS,EAAE,kBAAkB;kBAC7BL,SAAS,EAAE;gBACb;cACF,CAAE;cAAAT,QAAA,eAEFnB,OAAA,CAAC9B,WAAW;gBAACkC,EAAE,EAAE;kBAAEwD,CAAC,EAAE;gBAAE,CAAE;gBAAAzC,QAAA,gBACxBnB,OAAA,CAAChC,UAAU;kBAACwE,OAAO,EAAC,IAAI;kBAACL,KAAK,EAAC,OAAO;kBAACO,YAAY;kBAACC,UAAU,EAAC,KAAK;kBAACvC,EAAE,EAAE;oBAAEmB,EAAE,EAAE;kBAAE,CAAE;kBAAAJ,QAAA,EAAC;gBAEpF;kBAAAiB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbvC,OAAA,CAAChC,UAAU;kBAACwE,OAAO,EAAC,OAAO;kBAACL,KAAK,EAAC,0BAA0B;kBAAC/B,EAAE,EAAE;oBAAE+C,UAAU,EAAE;kBAAI,CAAE;kBAAAhC,QAAA,EAAC;gBAGtF;kBAAAiB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEPvC,OAAA,CAAC5B,IAAI;YAACqF,IAAI;YAACT,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA9B,QAAA,eACvBnB,OAAA,CAAC/B,IAAI;cACHmC,EAAE,EAAE;gBACFM,UAAU,EAAE,iFAAiF;gBAC7FgD,cAAc,EAAE,YAAY;gBAC5B7B,MAAM,EAAE,kCAAkC;gBAC1CL,YAAY,EAAE,CAAC;gBACfE,MAAM,EAAE,MAAM;gBACdiC,UAAU,EAAE,eAAe;gBAC3B,SAAS,EAAE;kBACT1B,SAAS,EAAE,kBAAkB;kBAC7BL,SAAS,EAAE;gBACb;cACF,CAAE;cAAAT,QAAA,eAEFnB,OAAA,CAAC9B,WAAW;gBAACkC,EAAE,EAAE;kBAAEwD,CAAC,EAAE;gBAAE,CAAE;gBAAAzC,QAAA,gBACxBnB,OAAA,CAAChC,UAAU;kBAACwE,OAAO,EAAC,IAAI;kBAACL,KAAK,EAAC,OAAO;kBAACO,YAAY;kBAACC,UAAU,EAAC,KAAK;kBAACvC,EAAE,EAAE;oBAAEmB,EAAE,EAAE;kBAAE,CAAE;kBAAAJ,QAAA,EAAC;gBAEpF;kBAAAiB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbvC,OAAA,CAAChC,UAAU;kBAACwE,OAAO,EAAC,OAAO;kBAACL,KAAK,EAAC,0BAA0B;kBAAC/B,EAAE,EAAE;oBAAE+C,UAAU,EAAE;kBAAI,CAAE;kBAAAhC,QAAA,EAAC;gBAGtF;kBAAAiB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEPvC,OAAA,CAAC5B,IAAI;YAACqF,IAAI;YAACT,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA9B,QAAA,eACvBnB,OAAA,CAAC/B,IAAI;cACHmC,EAAE,EAAE;gBACFM,UAAU,EAAE,iFAAiF;gBAC7FgD,cAAc,EAAE,YAAY;gBAC5B7B,MAAM,EAAE,kCAAkC;gBAC1CL,YAAY,EAAE,CAAC;gBACfE,MAAM,EAAE,MAAM;gBACdiC,UAAU,EAAE,eAAe;gBAC3B,SAAS,EAAE;kBACT1B,SAAS,EAAE,kBAAkB;kBAC7BL,SAAS,EAAE;gBACb;cACF,CAAE;cAAAT,QAAA,eAEFnB,OAAA,CAAC9B,WAAW;gBAACkC,EAAE,EAAE;kBAAEwD,CAAC,EAAE;gBAAE,CAAE;gBAAAzC,QAAA,gBACxBnB,OAAA,CAAChC,UAAU;kBAACwE,OAAO,EAAC,IAAI;kBAACL,KAAK,EAAC,OAAO;kBAACO,YAAY;kBAACC,UAAU,EAAC,KAAK;kBAACvC,EAAE,EAAE;oBAAEmB,EAAE,EAAE;kBAAE,CAAE;kBAAAJ,QAAA,EAAC;gBAEpF;kBAAAiB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbvC,OAAA,CAAChC,UAAU;kBAACwE,OAAO,EAAC,OAAO;kBAACL,KAAK,EAAC,0BAA0B;kBAAC/B,EAAE,EAAE;oBAAE+C,UAAU,EAAE;kBAAI,CAAE;kBAAAhC,QAAA,EAAC;gBAGtF;kBAAAiB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEPvC,OAAA,CAAC5B,IAAI;YAACqF,IAAI;YAACT,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA9B,QAAA,eACvBnB,OAAA,CAAC/B,IAAI;cACHmC,EAAE,EAAE;gBACFM,UAAU,EAAE,iFAAiF;gBAC7FgD,cAAc,EAAE,YAAY;gBAC5B7B,MAAM,EAAE,mCAAmC;gBAC3CL,YAAY,EAAE,CAAC;gBACfE,MAAM,EAAE,MAAM;gBACdiC,UAAU,EAAE,eAAe;gBAC3B,SAAS,EAAE;kBACT1B,SAAS,EAAE,kBAAkB;kBAC7BL,SAAS,EAAE;gBACb;cACF,CAAE;cAAAT,QAAA,eAEFnB,OAAA,CAAC9B,WAAW;gBAACkC,EAAE,EAAE;kBAAEwD,CAAC,EAAE;gBAAE,CAAE;gBAAAzC,QAAA,gBACxBnB,OAAA,CAAChC,UAAU;kBAACwE,OAAO,EAAC,IAAI;kBAACL,KAAK,EAAC,OAAO;kBAACO,YAAY;kBAACC,UAAU,EAAC,KAAK;kBAACvC,EAAE,EAAE;oBAAEmB,EAAE,EAAE;kBAAE,CAAE;kBAAAJ,QAAA,EAAC;gBAEpF;kBAAAiB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbvC,OAAA,CAAChC,UAAU;kBAACwE,OAAO,EAAC,OAAO;kBAACL,KAAK,EAAC,0BAA0B;kBAAC/B,EAAE,EAAE;oBAAE+C,UAAU,EAAE;kBAAI,CAAE;kBAAAhC,QAAA,EAAC;gBAGtF;kBAAAiB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGNvC,OAAA,CAACjC,GAAG;QAACuD,SAAS,EAAC,QAAQ;QAACC,EAAE,EAAE,EAAG;QAAAJ,QAAA,gBAC7BnB,OAAA,CAAChC,UAAU;UAACwE,OAAO,EAAC,IAAI;UAACG,UAAU,EAAC,MAAM;UAACR,KAAK,EAAC,OAAO;UAACZ,EAAE,EAAE,CAAE;UAAAJ,QAAA,EAAC;QAEhE;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbvC,OAAA,CAAC5B,IAAI;UAACmF,SAAS;UAACC,OAAO,EAAE,CAAE;UAAArC,QAAA,EACxB,CACC;YAAE2C,IAAI,eAAE9D,OAAA,CAAChB,UAAU;cAAAoD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;YAAEwB,KAAK,EAAE,SAAS;YAAET,KAAK,EAAE,cAAc;YAAEnB,KAAK,EAAE;UAAU,CAAC,EACnF;YAAE2B,IAAI,eAAE9D,OAAA,CAACZ,UAAU;cAAAgD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;YAAEwB,KAAK,EAAE,OAAO;YAAET,KAAK,EAAE,iBAAiB;YAAEnB,KAAK,EAAE;UAAU,CAAC,EACpF;YAAE2B,IAAI,eAAE9D,OAAA,CAACN,UAAU;cAAA0C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;YAAEwB,KAAK,EAAE,KAAK;YAAET,KAAK,EAAE,kBAAkB;YAAEnB,KAAK,EAAE;UAAU,CAAC,EACnF;YAAE2B,IAAI,eAAE9D,OAAA,CAACR,cAAc;cAAA4C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;YAAEwB,KAAK,EAAE,OAAO;YAAET,KAAK,EAAE,oBAAoB;YAAEnB,KAAK,EAAE;UAAU,CAAC,CAC5F,CAAC6B,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBAChBlE,OAAA,CAAC5B,IAAI;YAACqF,IAAI;YAACT,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAA9B,QAAA,eACtBnB,OAAA,CAAC/B,IAAI;cACHmC,EAAE,EAAE;gBACFM,UAAU,EAAE,iFAAiF;gBAC7FgD,cAAc,EAAE,YAAY;gBAC5B7B,MAAM,EAAE,aAAaoC,IAAI,CAAC9B,KAAK,IAAI;gBACnCX,YAAY,EAAE,CAAC;gBACfF,SAAS,EAAE,QAAQ;gBACnBqC,UAAU,EAAE,eAAe;gBAC3B,SAAS,EAAE;kBACT1B,SAAS,EAAE,8BAA8B;kBACzCL,SAAS,EAAE,eAAeqC,IAAI,CAAC9B,KAAK;gBACtC;cACF,CAAE;cAAAhB,QAAA,eAEFnB,OAAA,CAAC9B,WAAW;gBAACkC,EAAE,EAAE;kBAAEwD,CAAC,EAAE;gBAAE,CAAE;gBAAAzC,QAAA,gBACxBnB,OAAA,CAACjC,GAAG;kBACFqC,EAAE,EAAE;oBACFM,UAAU,EAAE,2BAA2BuD,IAAI,CAAC9B,KAAK,OAAO8B,IAAI,CAAC9B,KAAK,KAAK;oBACvEX,YAAY,EAAE,KAAK;oBACnBC,KAAK,EAAE,EAAE;oBACTC,MAAM,EAAE,EAAE;oBACVpB,OAAO,EAAE,MAAM;oBACfG,UAAU,EAAE,QAAQ;oBACpBD,cAAc,EAAE,QAAQ;oBACxBmB,EAAE,EAAE,MAAM;oBACVJ,EAAE,EAAE;kBACN,CAAE;kBAAAJ,QAAA,eAEDtD,KAAK,CAACsG,YAAY,CAACF,IAAI,CAACH,IAAI,EAAE;oBAAE1D,EAAE,EAAE;sBAAE8B,QAAQ,EAAE,EAAE;sBAAEC,KAAK,EAAE8B,IAAI,CAAC9B;oBAAM;kBAAE,CAAC;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxE,CAAC,eACNvC,OAAA,CAAChC,UAAU;kBAACwE,OAAO,EAAC,IAAI;kBAACG,UAAU,EAAC,MAAM;kBAACvC,EAAE,EAAE;oBAAE+B,KAAK,EAAE8B,IAAI,CAAC9B,KAAK;oBAAEZ,EAAE,EAAE;kBAAE,CAAE;kBAAAJ,QAAA,EACzE8C,IAAI,CAACF;gBAAK;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC,eACbvC,OAAA,CAAChC,UAAU;kBAACwE,OAAO,EAAC,IAAI;kBAACL,KAAK,EAAC,0BAA0B;kBAAAhB,QAAA,EACtD8C,IAAI,CAACX;gBAAK;kBAAAlB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC,GAtCqB2B,KAAK;YAAA9B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAuC7B,CACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGNvC,OAAA,CAAC/B,IAAI;QACHmC,EAAE,EAAE;UACFM,UAAU,EAAE,iFAAiF;UAC7FgD,cAAc,EAAE,YAAY;UAC5B7B,MAAM,EAAE,oCAAoC;UAC5CL,YAAY,EAAE,CAAC;UACfF,SAAS,EAAE,QAAQ;UACnBX,QAAQ,EAAE,UAAU;UACpByD,QAAQ,EAAE,QAAQ;UAClB,WAAW,EAAE;YACXxD,OAAO,EAAE,IAAI;YACbD,QAAQ,EAAE,UAAU;YACpBE,GAAG,EAAE,CAAC;YACNC,IAAI,EAAE,CAAC;YACPC,KAAK,EAAE,CAAC;YACRW,MAAM,EAAE,KAAK;YACbhB,UAAU,EAAE,yFAAyF;YACrGkC,cAAc,EAAE,WAAW;YAC3BZ,SAAS,EAAE;UACb,CAAC;UACD,0BAA0B,EAAE;YAC1B,IAAI,EAAE;cAAEkB,kBAAkB,EAAE;YAAS,CAAC;YACtC,KAAK,EAAE;cAAEA,kBAAkB,EAAE;YAAW,CAAC;YACzC,MAAM,EAAE;cAAEA,kBAAkB,EAAE;YAAS;UACzC;QACF,CAAE;QAAA/B,QAAA,eAEFnB,OAAA,CAAC9B,WAAW;UAACkC,EAAE,EAAE;YAAEwD,CAAC,EAAE;UAAE,CAAE;UAAAzC,QAAA,gBACxBnB,OAAA,CAAChC,UAAU;YAACwE,OAAO,EAAC,IAAI;YAACG,UAAU,EAAC,MAAM;YAACR,KAAK,EAAC,OAAO;YAACZ,EAAE,EAAE,CAAE;YAAAJ,QAAA,EAAC;UAEhE;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbvC,OAAA,CAAChC,UAAU;YAACwE,OAAO,EAAC,OAAO;YAACL,KAAK,EAAC,0BAA0B;YAAC/B,EAAE,EAAE;cAAEmB,EAAE,EAAE,CAAC;cAAE4B,UAAU,EAAE;YAAI,CAAE;YAAAhC,QAAA,EAAC;UAG7F;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbvC,OAAA,CAAC5B,IAAI;YAACmF,SAAS;YAACC,OAAO,EAAE,CAAE;YAAChD,cAAc,EAAC,QAAQ;YAAAW,QAAA,gBACjDnB,OAAA,CAAC5B,IAAI;cAACqF,IAAI;cAACT,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAA9B,QAAA,gBACvBnB,OAAA,CAAChC,UAAU;gBAACwE,OAAO,EAAC,IAAI;gBAACL,KAAK,EAAC,cAAc;gBAACO,YAAY;gBAAAvB,QAAA,EAAC;cAE3D;gBAAAiB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbvC,OAAA,CAAChC,UAAU;gBAACwE,OAAO,EAAC,OAAO;gBAACL,KAAK,EAAC,0BAA0B;gBAAAhB,QAAA,EAAC;cAE7D;gBAAAiB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACPvC,OAAA,CAAC5B,IAAI;cAACqF,IAAI;cAACT,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAA9B,QAAA,gBACvBnB,OAAA,CAAChC,UAAU;gBAACwE,OAAO,EAAC,IAAI;gBAACL,KAAK,EAAC,cAAc;gBAACO,YAAY;gBAAAvB,QAAA,EAAC;cAE3D;gBAAAiB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbvC,OAAA,CAAChC,UAAU;gBAACwE,OAAO,EAAC,OAAO;gBAACL,KAAK,EAAC,0BAA0B;gBAAAhB,QAAA,EAAC;cAE7D;gBAAAiB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACPvC,OAAA,CAAC5B,IAAI;cAACqF,IAAI;cAACT,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAA9B,QAAA,gBACvBnB,OAAA,CAAChC,UAAU;gBAACwE,OAAO,EAAC,IAAI;gBAACL,KAAK,EAAC,cAAc;gBAACO,YAAY;gBAAAvB,QAAA,EAAC;cAE3D;gBAAAiB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbvC,OAAA,CAAChC,UAAU;gBAACwE,OAAO,EAAC,OAAO;gBAACL,KAAK,EAAC,0BAA0B;gBAAAhB,QAAA,EAAC;cAE7D;gBAAAiB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACT,CAAC;AAEV,CAAC,CAAC;AAAC8B,GAAA,GAzaGpE,SAAS;AA2afA,SAAS,CAACqE,WAAW,GAAG,WAAW;AAEnC,eAAerE,SAAS;AAAC,IAAAE,EAAA,EAAAkE,GAAA;AAAAE,YAAA,CAAApE,EAAA;AAAAoE,YAAA,CAAAF,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}