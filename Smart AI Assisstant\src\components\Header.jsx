import React from 'react';
import {
  A<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  IconButton,
  Switch,
  FormControlLabel,
  Box,
  Button,
  Menu,
  MenuItem,
  Container,
  Tooltip,
  Avatar,
  Badge,
  Divider,
} from '@mui/material';
import {
  Security as SecurityIcon,
  DarkMode as DarkModeIcon,
  LightMode as LightModeIcon,
  Language as LanguageIcon,
  History as HistoryIcon,
  Home as HomeIcon,
  Info as InfoIcon,
  Notifications as NotificationsIcon,
  Settings as SettingsIcon,
  Star as StarIcon,
} from '@mui/icons-material';
import { useNavigate, useLocation } from 'react-router-dom';
import { useScan } from '../contexts/ScanContext';
import EnhancedSettingsDialog from './EnhancedSettingsDialog';
import NotificationDetailDialog from './NotificationDetailDialog';

const Header = React.memo(() => {
  const navigate = useNavigate();
  const location = useLocation();
  const { darkMode, language, setLanguage, notifications } = useScan();
  const [languageAnchor, setLanguageAnchor] = React.useState(null);
  const [settingsAnchor, setSettingsAnchor] = React.useState(null);
  const [notificationAnchor, setNotificationAnchor] = React.useState(null);
  const [settingsDialogOpen, setSettingsDialogOpen] = React.useState(false);
  const [selectedNotification, setSelectedNotification] = React.useState(null);
  const [notificationDetailOpen, setNotificationDetailOpen] = React.useState(false);

  // Sample notification history
  const [notificationHistory, setNotificationHistory] = React.useState([
    {
      id: 1,
      title: 'Welcome to AI Security Guard',
      message: 'You successfully logged in to the Platform. Have a nice tour!',
      timestamp: new Date().toISOString(),
      type: 'success',
      read: false,
    },
    {
      id: 2,
      title: 'Security Scan Completed',
      message: 'Your recent URL scan has been completed successfully.',
      timestamp: new Date(Date.now() - 3600000).toISOString(),
      type: 'info',
      read: true,
    },
    {
      id: 3,
      title: 'Threat Detected',
      message: 'A potential threat was detected and blocked automatically.',
      timestamp: new Date(Date.now() - 7200000).toISOString(),
      type: 'warning',
      read: true,
    },
  ]);

  const handleLanguageClick = (event) => {
    setLanguageAnchor(event.currentTarget);
  };

  const handleLanguageClose = () => {
    setLanguageAnchor(null);
  };

  const handleLanguageSelect = (lang) => {
    setLanguage(lang);
    handleLanguageClose();
  };

  const handleSettingsClick = (event) => {
    setSettingsAnchor(event.currentTarget);
  };

  const handleSettingsClose = () => {
    setSettingsAnchor(null);
  };

  const handleOpenSettingsDialog = () => {
    setSettingsDialogOpen(true);
    handleSettingsClose();
  };

  const handleNotificationClick = (event) => {
    setNotificationAnchor(event.currentTarget);
  };

  const handleNotificationClose = () => {
    setNotificationAnchor(null);
  };

  const markNotificationAsRead = (notificationId) => {
    setNotificationHistory(prev =>
      prev.map(notif =>
        notif.id === notificationId ? { ...notif, read: true } : notif
      )
    );
  };

  const handleNotificationDetailClick = (notification) => {
    setSelectedNotification(notification);
    setNotificationDetailOpen(true);
    setNotificationAnchor(null);
    markNotificationAsRead(notification.id);
  };

  const unreadCount = notificationHistory.filter(notif => !notif.read).length;

  const isHomePage = location.pathname === '/';
  const isSmartFeaturesPage = location.pathname === '/smart-features';
  const isHistoryPage = location.pathname === '/history';
  const isAboutPage = location.pathname === '/about';
  const isPlansPage = location.pathname === '/plans';

  const navigationItems = [
    { label: 'Home', path: '/', icon: <HomeIcon />, active: isHomePage },
    { label: 'Smart Features', path: '/smart-features', icon: <SecurityIcon />, active: isSmartFeaturesPage },
    { label: 'History', path: '/history', icon: <HistoryIcon />, active: isHistoryPage },
    { label: 'Plans', path: '/plans', icon: <StarIcon />, active: isPlansPage },
    { label: 'About', path: '/about', icon: <InfoIcon />, active: isAboutPage },
  ];

  return (
    <AppBar
      position="sticky"
      elevation={0}
      sx={{
        background: (theme) => theme.palette.mode === 'dark'
          ? 'linear-gradient(135deg, rgba(12, 12, 12, 0.98) 0%, rgba(26, 26, 26, 0.98) 50%, rgba(45, 45, 45, 0.98) 100%)'
          : 'linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%)',
        backdropFilter: 'blur(25px) saturate(180%)',
        borderBottom: (theme) => theme.palette.mode === 'dark'
          ? '1px solid rgba(255, 255, 255, 0.1)'
          : '1px solid rgba(255, 255, 255, 0.2)',
        boxShadow: (theme) => theme.palette.mode === 'dark'
          ? '0 4px 20px rgba(102, 126, 234, 0.1)'
          : '0 4px 20px rgba(102, 126, 234, 0.3)',
        position: 'relative',
        color: 'white',
        '&::after': {
          content: '""',
          position: 'absolute',
          bottom: 0,
          left: 0,
          right: 0,
          height: '2px',
          background: (theme) => theme.palette.mode === 'dark'
            ? 'linear-gradient(90deg, transparent 0%, #667eea 50%, transparent 100%)'
            : 'linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.8) 50%, transparent 100%)',
          opacity: 0.6,
        },
      }}
    >
      <Container maxWidth="xl">
        <Toolbar sx={{ justifyContent: 'space-between', py: 1 }}>
          {/* Logo and Title */}
          <Box display="flex" alignItems="center" gap={2}>
            <IconButton
              edge="start"
              color="inherit"
              aria-label="security logo"
              onClick={() => navigate('/')}
              sx={{
                background: 'rgba(255,255,255,0.1)',
                backdropFilter: 'blur(10px)',
                '&:hover': {
                  background: 'rgba(255,255,255,0.2)',
                  transform: 'scale(1.05)',
                },
                transition: 'all 0.2s ease',
              }}
            >
              <SecurityIcon fontSize="large" />
            </IconButton>
            <Box>
              <Typography
                variant="h4"
                component="h1"
                sx={{
                  fontWeight: 800,
                  cursor: 'pointer',
                  display: { xs: 'none', md: 'block' },
                  color: 'white',
                  textShadow: '0 2px 8px rgba(0,0,0,0.3)',
                  '&:hover': {
                    textShadow: '0 2px 12px rgba(255,255,255,0.3)',
                  },
                  transition: 'all 0.3s ease',
                }}
                onClick={() => navigate('/')}
              >
                AI Security Guard
              </Typography>
              <Typography
                variant="h6"
                component="h1"
                sx={{
                  fontWeight: 700,
                  cursor: 'pointer',
                  display: { xs: 'block', md: 'none' },
                  color: 'white',
                }}
                onClick={() => navigate('/')}
              >
                AI Security
              </Typography>
              <Typography
                variant="caption"
                sx={{
                  display: { xs: 'none', md: 'block' },
                  background: 'linear-gradient(90deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%)',
                  backgroundSize: '200% 200%',
                  backgroundClip: 'text',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                  animation: 'movingText 4s ease infinite',
                  fontSize: '0.75rem',
                  fontWeight: 600,
                  letterSpacing: '0.5px',
                  textTransform: 'uppercase',
                  '@keyframes movingText': {
                    '0%': { backgroundPosition: '0% 50%' },
                    '50%': { backgroundPosition: '100% 50%' },
                    '100%': { backgroundPosition: '0% 50%' },
                  },
                }}
              >
                Advanced Threat Detection Platform
              </Typography>
            </Box>
          </Box>

          {/* Navigation and Controls */}
          <Box display="flex" alignItems="center" gap={2}>
            {/* Desktop Navigation */}
            <Box display={{ xs: 'none', md: 'flex' }} gap={1}>
              {navigationItems.map((item) => (
                <Button
                  key={item.path}
                  color="inherit"
                  startIcon={item.icon}
                  onClick={() => navigate(item.path)}
                  variant={item.active ? 'contained' : 'text'}
                  sx={{
                    borderRadius: 3,
                    px: 3,
                    py: 1,
                    fontWeight: 600,
                    textTransform: 'none',
                    background: item.active
                      ? 'rgba(255,255,255,0.2)'
                      : 'transparent',
                    backdropFilter: item.active ? 'blur(10px)' : 'none',
                    border: item.active ? '1px solid rgba(255,255,255,0.3)' : 'none',
                    '&:hover': {
                      background: 'rgba(255,255,255,0.15)',
                      transform: 'translateY(-1px)',
                    },
                    transition: 'all 0.2s ease',
                  }}
                >
                  {item.label}
                </Button>
              ))}
            </Box>

            {/* Mobile Navigation */}
            <Box display={{ xs: 'flex', md: 'none' }} gap={0.5}>
              {navigationItems.map((item) => (
                <Tooltip key={item.path} title={item.label}>
                  <IconButton
                    color="inherit"
                    onClick={() => navigate(item.path)}
                    sx={{
                      background: item.active
                        ? 'rgba(255,255,255,0.2)'
                        : 'transparent',
                      backdropFilter: item.active ? 'blur(10px)' : 'none',
                      border: item.active ? '1px solid rgba(255,255,255,0.3)' : 'none',
                      '&:hover': {
                        background: 'rgba(255,255,255,0.15)',
                      },
                    }}
                  >
                    {item.icon}
                  </IconButton>
                </Tooltip>
              ))}
            </Box>

            {/* Notifications */}
            <Tooltip title="Notifications">
              <IconButton
                color="inherit"
                onClick={handleNotificationClick}
                sx={{
                  background: 'rgba(255,255,255,0.1)',
                  '&:hover': {
                    background: 'rgba(255,255,255,0.2)',
                  },
                }}
              >
                <Badge badgeContent={unreadCount} color="error">
                  <NotificationsIcon />
                </Badge>
              </IconButton>
            </Tooltip>

            {/* Settings Menu */}
            <Tooltip title="Settings">
              <IconButton
                color="inherit"
                onClick={handleSettingsClick}
                sx={{
                  background: 'rgba(255,255,255,0.1)',
                  '&:hover': {
                    background: 'rgba(255,255,255,0.2)',
                  },
                }}
              >
                <SettingsIcon />
              </IconButton>
            </Tooltip>

            <Menu
              anchorEl={settingsAnchor}
              open={Boolean(settingsAnchor)}
              onClose={handleSettingsClose}
              anchorOrigin={{
                vertical: 'bottom',
                horizontal: 'right',
              }}
              transformOrigin={{
                vertical: 'top',
                horizontal: 'right',
              }}
              PaperProps={{
                sx: {
                  mt: 1,
                  borderRadius: 2,
                  minWidth: 200,
                },
              }}
            >
              {/* Settings Options */}
              <MenuItem onClick={handleOpenSettingsDialog} sx={{ gap: 2 }}>
                <SettingsIcon />
                Account & Settings
              </MenuItem>

              {/* Language Selector */}
              <MenuItem onClick={handleLanguageClick} sx={{ gap: 2 }}>
                <LanguageIcon />
                Language
              </MenuItem>
            </Menu>

            {/* Language Menu */}
            <Menu
              anchorEl={languageAnchor}
              open={Boolean(languageAnchor)}
              onClose={handleLanguageClose}
              anchorOrigin={{
                vertical: 'bottom',
                horizontal: 'right',
              }}
              transformOrigin={{
                vertical: 'top',
                horizontal: 'right',
              }}
              PaperProps={{
                sx: {
                  mt: 1,
                  borderRadius: 2,
                },
              }}
            >
              <MenuItem
                onClick={() => handleLanguageSelect('en')}
                selected={language === 'en'}
              >
                🇺🇸 English
              </MenuItem>
              <MenuItem
                onClick={() => handleLanguageSelect('ar')}
                selected={language === 'ar'}
              >
                🇸🇦 العربية
              </MenuItem>
            </Menu>

            {/* Notification Menu */}
            <Menu
              anchorEl={notificationAnchor}
              open={Boolean(notificationAnchor)}
              onClose={handleNotificationClose}
              anchorOrigin={{
                vertical: 'bottom',
                horizontal: 'right',
              }}
              transformOrigin={{
                vertical: 'top',
                horizontal: 'right',
              }}
              PaperProps={{
                sx: {
                  mt: 1,
                  borderRadius: 3,
                  minWidth: 350,
                  maxWidth: 400,
                  maxHeight: 500,
                  background: (theme) => theme.palette.mode === 'dark'
                    ? 'linear-gradient(135deg, rgba(12, 12, 12, 0.98) 0%, rgba(26, 26, 26, 0.98) 100%)'
                    : 'linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(248, 250, 252, 0.98) 100%)',
                  backdropFilter: 'blur(25px)',
                  border: '1px solid rgba(102, 126, 234, 0.2)',
                  boxShadow: '0 20px 60px rgba(0, 0, 0, 0.3)',
                },
              }}
            >
              <Box sx={{ p: 2, borderBottom: '1px solid rgba(102, 126, 234, 0.2)' }}>
                <Typography variant="h6" fontWeight="bold" color="primary.main">
                  Notifications
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {unreadCount > 0 ? `${unreadCount} unread notifications` : 'All caught up!'}
                </Typography>
              </Box>
              <Box sx={{ maxHeight: 350, overflow: 'auto' }}>
                {notificationHistory.length === 0 ? (
                  <Box sx={{ p: 3, textAlign: 'center' }}>
                    <NotificationsIcon sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
                    <Typography variant="body2" color="text.secondary">
                      No notifications yet
                    </Typography>
                  </Box>
                ) : (
                  notificationHistory.map((notification, index) => (
                    <Box key={notification.id}>
                      <MenuItem
                        onClick={() => handleNotificationDetailClick(notification)}
                        sx={{
                          py: 2,
                          px: 3,
                          alignItems: 'flex-start',
                          background: !notification.read
                            ? 'rgba(102, 126, 234, 0.05)'
                            : 'transparent',
                          '&:hover': {
                            background: 'rgba(102, 126, 234, 0.1)',
                          },
                        }}
                      >
                        <Box sx={{ mr: 2, mt: 0.5 }}>
                          <Box
                            sx={{
                              width: 8,
                              height: 8,
                              borderRadius: '50%',
                              background: notification.type === 'success' ? '#4caf50' :
                                         notification.type === 'warning' ? '#ff9800' :
                                         notification.type === 'error' ? '#f44336' : '#2196f3',
                              opacity: notification.read ? 0.3 : 1,
                            }}
                          />
                        </Box>
                        <Box sx={{ flex: 1 }}>
                          <Typography
                            variant="subtitle2"
                            fontWeight={notification.read ? 400 : 600}
                            sx={{ mb: 0.5 }}
                          >
                            {notification.title}
                          </Typography>
                          <Typography
                            variant="body2"
                            color="text.secondary"
                            sx={{ mb: 1, lineHeight: 1.4 }}
                          >
                            {notification.message}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {new Date(notification.timestamp).toLocaleString()}
                          </Typography>
                        </Box>
                      </MenuItem>
                      {index < notificationHistory.length - 1 && (
                        <Divider sx={{ mx: 2 }} />
                      )}
                    </Box>
                  ))
                )}
              </Box>
            </Menu>

          </Box>
        </Toolbar>
      </Container>

      {/* Settings Dialog */}
      <EnhancedSettingsDialog
        open={settingsDialogOpen}
        onClose={() => setSettingsDialogOpen(false)}
      />

      {/* Notification Detail Dialog */}
      <NotificationDetailDialog
        open={notificationDetailOpen}
        onClose={() => setNotificationDetailOpen(false)}
        notification={selectedNotification}
      />
    </AppBar>
  );
});

Header.displayName = 'Header';

export default Header;
