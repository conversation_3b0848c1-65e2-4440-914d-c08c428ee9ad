import React, { useState, useCallback } from 'react';
import {
  Card,
  CardContent,
  Typography,
  TextField,
  Button,
  Box,
  CircularProgress,
  Alert,
  Chip,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider,
} from '@mui/material';
import {
  Link as LinkIcon,
  Security as SecurityIcon,
  Warning as WarningIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Search as SearchIcon,
} from '@mui/icons-material';
import { useScan } from '../contexts/ScanContext';
import { scanUrl } from '../services/security';

const LinkScanner = React.memo(() => {
  const [url, setUrl] = useState('');
  const [isScanning, setIsScanning] = useState(false);
  const [scanResult, setScanResult] = useState(null);
  const [error, setError] = useState('');
  
  const { startScan, completeScan, addToHistory, addNotification } = useScan();

  // URL validation
  const isValidUrl = useCallback((string) => {
    try {
      const urlObj = new URL(string);
      return urlObj.protocol === 'http:' || urlObj.protocol === 'https:';
    } catch (_) {
      return false;
    }
  }, []);

  // Optimized URL input change handler
  const handleUrlChange = useCallback((event) => {
    const value = event.target.value;
    setUrl(value);
    // Debounce error clearing and result clearing for better performance
    if (error) setError('');
    if (scanResult) setScanResult(null);
  }, [error, scanResult]);

  // Handle scan submission
  const handleScan = useCallback(async () => {
    if (!url.trim()) {
      setError('Please enter a URL to scan');
      return;
    }

    if (!isValidUrl(url)) {
      setError('Please enter a valid URL (must start with http:// or https://)');
      return;
    }

    setIsScanning(true);
    setError('');
    setScanResult(null);

    try {
      // Start scan in context
      startScan('url', url);

      // Perform the actual scan
      const result = await scanUrl(url);

      // Complete scan in context
      completeScan(result);

      // Set local result
      setScanResult(result);

      // Add to history
      const historyEntry = {
        id: Date.now(),
        type: 'url',
        target: url,
        result,
        timestamp: new Date().toISOString(),
      };
      addToHistory(historyEntry);

      // Add notification
      addNotification({
        type: result.isSafe ? 'success' : 'warning',
        title: 'URL Scan Complete',
        message: `${url} has been scanned successfully`,
      });

    } catch (err) {
      const errorMessage = err.message || 'Failed to scan URL';
      setError(errorMessage);
      
      addNotification({
        type: 'error',
        title: 'Scan Failed',
        message: errorMessage,
      });
    } finally {
      setIsScanning(false);
    }
  }, [url, isValidUrl, startScan, completeScan, addToHistory, addNotification]);

  // Handle Enter key press
  const handleKeyPress = useCallback((event) => {
    if (event.key === 'Enter' && !isScanning) {
      handleScan();
    }
  }, [handleScan, isScanning]);

  const getSafetyIcon = (isSafe, threatLevel) => {
    if (isSafe) return <CheckCircleIcon color="success" />;
    if (threatLevel === 'high' || threatLevel === 'critical') return <ErrorIcon color="error" />;
    return <WarningIcon color="warning" />;
  };

  const getSafetyColor = (isSafe, threatLevel) => {
    if (isSafe) return 'success';
    if (threatLevel === 'high' || threatLevel === 'critical') return 'error';
    return 'warning';
  };

  return (
    <Box
      data-testid="url-scanner"
      sx={{
        background: (theme) => theme.palette.mode === 'dark'
          ? 'linear-gradient(135deg, rgba(30, 30, 30, 0.95) 0%, rgba(50, 50, 50, 0.95) 100%)'
          : 'linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%)',
        backdropFilter: 'blur(20px)',
        borderRadius: 4,
        border: (theme) => `1px solid ${theme.palette.divider}`,
        p: 4,
        position: 'relative',
        overflow: 'hidden',
        '&::before': {
          content: '""',
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          height: '4px',
          background: 'linear-gradient(90deg, #667eea 0%, #764ba2 100%)',
        },
      }}
    >
      {/* Header Section */}
      <Box
        display="flex"
        alignItems="center"
        justifyContent="center"
        mb={4}
        sx={{
          textAlign: 'center',
        }}
      >
        <Box
          sx={{
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            borderRadius: '50%',
            width: 60,
            height: 60,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            mr: 3,
            boxShadow: '0 8px 25px rgba(102, 126, 234, 0.3)',
          }}
        >
          <LinkIcon sx={{ color: 'white', fontSize: 28 }} />
        </Box>
        <Box>
          <Typography variant="h4" component="h2" fontWeight="bold" gutterBottom>
            URL Security Scanner
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Advanced threat detection for web links and URLs
          </Typography>
        </Box>
      </Box>

      {/* Input Section */}
      <Box mb={4}>
        <Typography variant="h6" gutterBottom fontWeight="600" sx={{ mb: 2 }}>
          Enter URL for Analysis
        </Typography>
        <TextField
          fullWidth
          label="Website URL"
          placeholder="https://example.com"
          value={url}
          onChange={handleUrlChange}
          onKeyPress={handleKeyPress}
          error={!!error}
          helperText={error || 'Enter a complete URL including http:// or https://'}
          disabled={isScanning}
          variant="outlined"
          autoComplete="url"
          spellCheck={false}
          sx={{
            '& .MuiOutlinedInput-root': {
              borderRadius: 3,
              fontSize: '1.1rem',
              '&:hover': {
                boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
              },
              '&.Mui-focused': {
                boxShadow: '0 4px 20px rgba(102, 126, 234, 0.2)',
              },
            },
            '& .MuiInputLabel-root': {
              fontSize: '1rem',
              fontWeight: 500,
            },
          }}
          InputProps={{
            startAdornment: (
              <Box
                sx={{
                  background: 'linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%)',
                  borderRadius: '50%',
                  width: 40,
                  height: 40,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  mr: 2,
                }}
              >
                <LinkIcon sx={{ color: 'primary.main', fontSize: 20 }} />
              </Box>
            ),
          }}
        />
      </Box>

      {/* Action Button */}
      <Box display="flex" justifyContent="center" mb={4}>
        <Button
          variant="contained"
          onClick={handleScan}
          disabled={isScanning || !url.trim()}
          startIcon={isScanning ? <CircularProgress size={20} color="inherit" /> : <SearchIcon />}
          size="large"
          sx={{
            px: 6,
            py: 2,
            borderRadius: 3,
            fontSize: '1.1rem',
            fontWeight: 600,
            minWidth: 200,
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            boxShadow: '0 8px 25px rgba(102, 126, 234, 0.3)',
            '&:hover': {
              background: 'linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)',
              transform: 'translateY(-2px)',
              boxShadow: '0 12px 35px rgba(102, 126, 234, 0.4)',
            },
            '&:disabled': {
              background: 'rgba(0,0,0,0.12)',
              transform: 'none',
              boxShadow: 'none',
            },
          }}
        >
          {isScanning ? 'Analyzing URL...' : 'Start Security Scan'}
        </Button>
      </Box>

      {/* Results Section */}
      {scanResult && (
        <Box
          sx={{
            background: (theme) => theme.palette.mode === 'dark'
              ? 'rgba(30, 30, 30, 0.5)'
              : 'rgba(255, 255, 255, 0.7)',
            backdropFilter: 'blur(10px)',
            borderRadius: 3,
            p: 4,
            border: (theme) => `1px solid ${theme.palette.divider}`,
          }}
        >
          <Typography variant="h5" gutterBottom fontWeight="bold" textAlign="center" mb={3}>
            Scan Results
          </Typography>

          <Alert
            severity={getSafetyColor(scanResult.isSafe, scanResult.threatLevel)}
            icon={getSafetyIcon(scanResult.isSafe, scanResult.threatLevel)}
            sx={{
              mb: 3,
              borderRadius: 2,
              '& .MuiAlert-message': {
                width: '100%',
              },
            }}
          >
            <Typography variant="h6" fontWeight="bold" gutterBottom>
              {scanResult.isSafe ? 'URL appears safe' : 'Potential security risks detected'}
            </Typography>
            <Typography variant="body1">
              {scanResult.summary || 'Scan completed successfully'}
            </Typography>
          </Alert>

          <Box display="flex" gap={2} mb={4} flexWrap="wrap" justifyContent="center">
            <Chip
              label={`Reputation Score: ${scanResult.reputationScore || 'N/A'}`}
              variant="outlined"
              size="medium"
              sx={{
                fontWeight: 600,
                fontSize: '0.9rem',
                px: 2,
                py: 1,
              }}
            />
            {scanResult.category && (
              <Chip
                label={`Category: ${scanResult.category}`}
                variant="outlined"
                size="medium"
                sx={{
                  fontWeight: 600,
                  fontSize: '0.9rem',
                  px: 2,
                  py: 1,
                }}
              />
            )}
          </Box>

          {scanResult.details && scanResult.details.length > 0 && (
            <Box mb={3}>
              <Typography variant="h6" gutterBottom fontWeight="600" sx={{ mb: 2 }}>
                Detailed Analysis
              </Typography>
              <List
                sx={{
                  background: (theme) => theme.palette.mode === 'dark'
                    ? 'rgba(255, 255, 255, 0.05)'
                    : 'rgba(0, 0, 0, 0.02)',
                  borderRadius: 2,
                  p: 1,
                }}
              >
                {scanResult.details.map((detail, index) => (
                  <React.Fragment key={index}>
                    <ListItem sx={{ py: 2 }}>
                      <ListItemIcon>
                        <Box
                          sx={{
                            background: 'linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%)',
                            borderRadius: '50%',
                            width: 40,
                            height: 40,
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                          }}
                        >
                          <SecurityIcon fontSize="small" color="primary" />
                        </Box>
                      </ListItemIcon>
                      <ListItemText
                        primary={
                          <Typography variant="subtitle1" fontWeight="600">
                            {detail.title}
                          </Typography>
                        }
                        secondary={
                          <Typography variant="body2" color="text.secondary" sx={{ mt: 0.5 }}>
                            {detail.description}
                          </Typography>
                        }
                      />
                    </ListItem>
                    {index < scanResult.details.length - 1 && (
                      <Divider sx={{ mx: 2 }} />
                    )}
                  </React.Fragment>
                ))}
              </List>
            </Box>
          )}

          {scanResult.recommendations && scanResult.recommendations.length > 0 && (
            <Box>
              <Typography variant="h6" gutterBottom fontWeight="600" sx={{ mb: 2 }}>
                Security Recommendations
              </Typography>
              <List
                sx={{
                  background: (theme) => theme.palette.mode === 'dark'
                    ? 'rgba(76, 175, 80, 0.1)'
                    : 'rgba(76, 175, 80, 0.05)',
                  borderRadius: 2,
                  p: 1,
                  border: (theme) => `1px solid ${theme.palette.success.light}`,
                }}
              >
                {scanResult.recommendations.map((rec, index) => (
                  <ListItem key={index} sx={{ py: 1.5 }}>
                    <ListItemIcon>
                      <Box
                        sx={{
                          background: 'rgba(76, 175, 80, 0.1)',
                          borderRadius: '50%',
                          width: 32,
                          height: 32,
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                        }}
                      >
                        <CheckCircleIcon fontSize="small" color="success" />
                      </Box>
                    </ListItemIcon>
                    <ListItemText
                      primary={
                        <Typography variant="body1" fontWeight="500">
                          {rec}
                        </Typography>
                      }
                    />
                  </ListItem>
                ))}
              </List>
            </Box>
          )}
        </Box>
      )}
    </Box>
  );
});

LinkScanner.displayName = 'LinkScanner';

export default LinkScanner;
