{"ast": null, "code": "var _jsxFileName = \"E:\\\\PythonProjects\\\\React.js\\\\Smart AI Assisstant\\\\src\\\\components\\\\SettingsDialog.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Dialog, DialogTitle, DialogContent, DialogActions, Button, Box, Typography, Tabs, Tab, TextField, Switch, FormControlLabel, Card, CardContent, Chip, List, ListItem, ListItemIcon, ListItemText, Divider, Alert, IconButton, Avatar, Grid } from '@mui/material';\nimport { Settings as SettingsIcon, Person as PersonIcon, Security as SecurityIcon, Language as LanguageIcon, Palette as PaletteIcon, Info as InfoIcon, Star as StarIcon, Check as CheckIcon, Close as CloseIcon, Email as EmailIcon, Lock as LockIcon, Visibility as VisibilityIcon, VisibilityOff as VisibilityOffIcon, Star as CrownIcon, Diamond as DiamondIcon } from '@mui/icons-material';\nimport { useScan } from '../contexts/ScanContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SettingsDialog = /*#__PURE__*/_s(/*#__PURE__*/React.memo(_c = _s(({\n  open,\n  onClose\n}) => {\n  _s();\n  const [activeTab, setActiveTab] = useState(0);\n  const [showPassword, setShowPassword] = useState(false);\n  const [loginForm, setLoginForm] = useState({\n    email: '',\n    password: ''\n  });\n  const [signupForm, setSignupForm] = useState({\n    name: '',\n    email: '',\n    password: '',\n    confirmPassword: ''\n  });\n  const [isLogin, setIsLogin] = useState(true);\n  const {\n    darkMode,\n    language,\n    toggleDarkMode,\n    setLanguage\n  } = useScan();\n  const handleTabChange = (event, newValue) => {\n    setActiveTab(newValue);\n  };\n  const handleLogin = e => {\n    e.preventDefault();\n    // Implement login logic here\n    console.log('Login:', loginForm);\n  };\n  const handleSignup = e => {\n    e.preventDefault();\n    // Implement signup logic here\n    console.log('Signup:', signupForm);\n  };\n  const TabPanel = ({\n    children,\n    value,\n    index\n  }) => /*#__PURE__*/_jsxDEV(\"div\", {\n    hidden: value !== index,\n    children: value === index && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        py: 3\n      },\n      children: children\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 81,\n      columnNumber: 27\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 80,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(Dialog, {\n    open: open,\n    onClose: onClose,\n    maxWidth: \"lg\",\n    fullWidth: true,\n    PaperProps: {\n      sx: {\n        borderRadius: 4,\n        minHeight: '80vh',\n        background: theme => theme.palette.mode === 'dark' ? 'linear-gradient(135deg, rgba(12, 12, 12, 0.98) 0%, rgba(26, 26, 26, 0.98) 50%, rgba(45, 45, 45, 0.98) 100%)' : 'linear-gradient(135deg, rgba(248, 250, 252, 0.98) 0%, rgba(241, 245, 249, 0.98) 50%, rgba(226, 232, 240, 0.98) 100%)',\n        backdropFilter: 'blur(25px) saturate(180%)',\n        border: theme => theme.palette.mode === 'dark' ? '1px solid rgba(102, 126, 234, 0.2)' : '1px solid rgba(102, 126, 234, 0.1)',\n        boxShadow: '0 25px 80px rgba(0, 0, 0, 0.3)',\n        position: 'relative',\n        overflow: 'hidden',\n        '&::before': {\n          content: '\"\"',\n          position: 'absolute',\n          top: 0,\n          left: 0,\n          right: 0,\n          height: '4px',\n          background: 'linear-gradient(90deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%)',\n          backgroundSize: '400% 400%',\n          animation: 'gradientShift 8s ease infinite'\n        },\n        '@keyframes gradientShift': {\n          '0%': {\n            backgroundPosition: '0% 50%'\n          },\n          '50%': {\n            backgroundPosition: '100% 50%'\n          },\n          '100%': {\n            backgroundPosition: '0% 50%'\n          }\n        }\n      }\n    },\n    children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n      sx: {\n        p: 0\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        display: \"flex\",\n        alignItems: \"center\",\n        justifyContent: \"space-between\",\n        sx: {\n          p: 4,\n          background: theme => theme.palette.mode === 'dark' ? 'linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%)' : 'linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%)',\n          borderBottom: theme => `1px solid ${theme.palette.divider}`\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          display: \"flex\",\n          alignItems: \"center\",\n          gap: 3,\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n              borderRadius: '50%',\n              width: 56,\n              height: 56,\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              boxShadow: '0 8px 25px rgba(102, 126, 234, 0.3)'\n            },\n            children: /*#__PURE__*/_jsxDEV(SettingsIcon, {\n              sx: {\n                color: 'white',\n                fontSize: 28\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 150,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 138,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h4\",\n              fontWeight: \"bold\",\n              sx: {\n                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                backgroundClip: 'text',\n                WebkitBackgroundClip: 'text',\n                WebkitTextFillColor: 'transparent'\n              },\n              children: \"Settings & Account\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 153,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: \"Manage your preferences and account settings\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 163,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 152,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 137,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: onClose,\n          sx: {\n            background: 'rgba(255, 255, 255, 0.1)',\n            backdropFilter: 'blur(10px)',\n            '&:hover': {\n              background: 'rgba(255, 255, 255, 0.2)',\n              transform: 'scale(1.05)'\n            },\n            transition: 'all 0.2s ease'\n          },\n          children: /*#__PURE__*/_jsxDEV(CloseIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 180,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 168,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 125,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 124,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n      sx: {\n        p: 0\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          borderBottom: 1,\n          borderColor: 'divider',\n          background: theme => theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.02)' : 'rgba(0, 0, 0, 0.02)'\n        },\n        children: /*#__PURE__*/_jsxDEV(Tabs, {\n          value: activeTab,\n          onChange: handleTabChange,\n          variant: \"fullWidth\",\n          textColor: \"primary\",\n          indicatorColor: \"primary\",\n          sx: {\n            '& .MuiTab-root': {\n              minHeight: 80,\n              fontSize: '1rem',\n              fontWeight: 600,\n              textTransform: 'none',\n              transition: 'all 0.3s ease',\n              '&:hover': {\n                background: theme => theme.palette.mode === 'dark' ? 'rgba(102, 126, 234, 0.1)' : 'rgba(102, 126, 234, 0.05)',\n                transform: 'translateY(-2px)'\n              },\n              '&.Mui-selected': {\n                background: theme => theme.palette.mode === 'dark' ? 'linear-gradient(135deg, rgba(102, 126, 234, 0.2) 0%, rgba(118, 75, 162, 0.2) 100%)' : 'linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%)'\n              }\n            },\n            '& .MuiTabs-indicator': {\n              height: 3,\n              borderRadius: '3px 3px 0 0',\n              background: 'linear-gradient(90deg, #667eea 0%, #764ba2 100%)'\n            }\n          },\n          children: [/*#__PURE__*/_jsxDEV(Tab, {\n            icon: /*#__PURE__*/_jsxDEV(PersonIcon, {\n              sx: {\n                fontSize: 24\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 228,\n              columnNumber: 21\n            }, this),\n            label: \"Account\",\n            iconPosition: \"start\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 227,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Tab, {\n            icon: /*#__PURE__*/_jsxDEV(PaletteIcon, {\n              sx: {\n                fontSize: 24\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 233,\n              columnNumber: 21\n            }, this),\n            label: \"Preferences\",\n            iconPosition: \"start\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 232,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Tab, {\n            icon: /*#__PURE__*/_jsxDEV(InfoIcon, {\n              sx: {\n                fontSize: 24\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 238,\n              columnNumber: 21\n            }, this),\n            label: \"About\",\n            iconPosition: \"start\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 237,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 195,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 186,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          p: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(TabPanel, {\n          value: activeTab,\n          index: 0,\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            display: \"flex\",\n            justifyContent: \"center\",\n            mb: 3,\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              variant: isLogin ? 'contained' : 'outlined',\n              onClick: () => setIsLogin(true),\n              sx: {\n                mr: 1,\n                borderRadius: 3\n              },\n              children: \"Login\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 249,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: !isLogin ? 'contained' : 'outlined',\n              onClick: () => setIsLogin(false),\n              sx: {\n                borderRadius: 3\n              },\n              children: \"Sign Up\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 256,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 248,\n            columnNumber: 13\n          }, this), isLogin ? /*#__PURE__*/_jsxDEV(Card, {\n            sx: {\n              maxWidth: 400,\n              mx: 'auto'\n            },\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                gutterBottom: true,\n                textAlign: \"center\",\n                children: \"Welcome Back\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 268,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                component: \"form\",\n                onSubmit: handleLogin,\n                children: [/*#__PURE__*/_jsxDEV(TextField, {\n                  fullWidth: true,\n                  label: \"Email\",\n                  type: \"email\",\n                  value: loginForm.email,\n                  onChange: e => setLoginForm({\n                    ...loginForm,\n                    email: e.target.value\n                  }),\n                  margin: \"normal\",\n                  variant: \"outlined\",\n                  autoComplete: \"email\",\n                  sx: {\n                    '& .MuiOutlinedInput-root': {\n                      borderRadius: 3\n                    }\n                  },\n                  InputProps: {\n                    startAdornment: /*#__PURE__*/_jsxDEV(EmailIcon, {\n                      sx: {\n                        mr: 1,\n                        color: 'text.secondary'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 287,\n                      columnNumber: 41\n                    }, this)\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 272,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                  fullWidth: true,\n                  label: \"Password\",\n                  type: showPassword ? 'text' : 'password',\n                  value: loginForm.password,\n                  onChange: e => setLoginForm({\n                    ...loginForm,\n                    password: e.target.value\n                  }),\n                  margin: \"normal\",\n                  variant: \"outlined\",\n                  autoComplete: \"current-password\",\n                  sx: {\n                    '& .MuiOutlinedInput-root': {\n                      borderRadius: 3\n                    }\n                  },\n                  InputProps: {\n                    startAdornment: /*#__PURE__*/_jsxDEV(LockIcon, {\n                      sx: {\n                        mr: 1,\n                        color: 'text.secondary'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 305,\n                      columnNumber: 41\n                    }, this),\n                    endAdornment: /*#__PURE__*/_jsxDEV(IconButton, {\n                      onClick: () => setShowPassword(!showPassword),\n                      children: showPassword ? /*#__PURE__*/_jsxDEV(VisibilityOffIcon, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 308,\n                        columnNumber: 45\n                      }, this) : /*#__PURE__*/_jsxDEV(VisibilityIcon, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 308,\n                        columnNumber: 69\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 307,\n                      columnNumber: 27\n                    }, this)\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 290,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  type: \"submit\",\n                  fullWidth: true,\n                  variant: \"contained\",\n                  sx: {\n                    mt: 3,\n                    mb: 2,\n                    borderRadius: 3\n                  },\n                  children: \"Sign In\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 313,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 271,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 267,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 266,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(Card, {\n            sx: {\n              maxWidth: 400,\n              mx: 'auto'\n            },\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                gutterBottom: true,\n                textAlign: \"center\",\n                children: \"Create Account\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 327,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                component: \"form\",\n                onSubmit: handleSignup,\n                children: [/*#__PURE__*/_jsxDEV(TextField, {\n                  fullWidth: true,\n                  label: \"Full Name\",\n                  value: signupForm.name,\n                  onChange: e => setSignupForm({\n                    ...signupForm,\n                    name: e.target.value\n                  }),\n                  margin: \"normal\",\n                  variant: \"outlined\",\n                  autoComplete: \"name\",\n                  sx: {\n                    '& .MuiOutlinedInput-root': {\n                      borderRadius: 3\n                    }\n                  },\n                  InputProps: {\n                    startAdornment: /*#__PURE__*/_jsxDEV(PersonIcon, {\n                      sx: {\n                        mr: 1,\n                        color: 'text.secondary'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 345,\n                      columnNumber: 41\n                    }, this)\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 331,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                  fullWidth: true,\n                  label: \"Email\",\n                  type: \"email\",\n                  value: signupForm.email,\n                  onChange: e => setSignupForm({\n                    ...signupForm,\n                    email: e.target.value\n                  }),\n                  margin: \"normal\",\n                  variant: \"outlined\",\n                  autoComplete: \"email\",\n                  sx: {\n                    '& .MuiOutlinedInput-root': {\n                      borderRadius: 3\n                    }\n                  },\n                  InputProps: {\n                    startAdornment: /*#__PURE__*/_jsxDEV(EmailIcon, {\n                      sx: {\n                        mr: 1,\n                        color: 'text.secondary'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 363,\n                      columnNumber: 41\n                    }, this)\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 348,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                  fullWidth: true,\n                  label: \"Password\",\n                  type: showPassword ? 'text' : 'password',\n                  value: signupForm.password,\n                  onChange: e => setSignupForm({\n                    ...signupForm,\n                    password: e.target.value\n                  }),\n                  margin: \"normal\",\n                  variant: \"outlined\",\n                  autoComplete: \"new-password\",\n                  sx: {\n                    '& .MuiOutlinedInput-root': {\n                      borderRadius: 3\n                    }\n                  },\n                  InputProps: {\n                    startAdornment: /*#__PURE__*/_jsxDEV(LockIcon, {\n                      sx: {\n                        mr: 1,\n                        color: 'text.secondary'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 381,\n                      columnNumber: 41\n                    }, this),\n                    endAdornment: /*#__PURE__*/_jsxDEV(IconButton, {\n                      onClick: () => setShowPassword(!showPassword),\n                      children: showPassword ? /*#__PURE__*/_jsxDEV(VisibilityOffIcon, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 384,\n                        columnNumber: 45\n                      }, this) : /*#__PURE__*/_jsxDEV(VisibilityIcon, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 384,\n                        columnNumber: 69\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 383,\n                      columnNumber: 27\n                    }, this)\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 366,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                  fullWidth: true,\n                  label: \"Confirm Password\",\n                  type: \"password\",\n                  value: signupForm.confirmPassword,\n                  onChange: e => setSignupForm({\n                    ...signupForm,\n                    confirmPassword: e.target.value\n                  }),\n                  margin: \"normal\",\n                  variant: \"outlined\",\n                  autoComplete: \"new-password\",\n                  sx: {\n                    '& .MuiOutlinedInput-root': {\n                      borderRadius: 3\n                    }\n                  },\n                  InputProps: {\n                    startAdornment: /*#__PURE__*/_jsxDEV(LockIcon, {\n                      sx: {\n                        mr: 1,\n                        color: 'text.secondary'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 404,\n                      columnNumber: 41\n                    }, this)\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 389,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  type: \"submit\",\n                  fullWidth: true,\n                  variant: \"contained\",\n                  sx: {\n                    mt: 3,\n                    mb: 2,\n                    borderRadius: 3\n                  },\n                  children: \"Create Account\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 407,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 330,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 326,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 325,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 247,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TabPanel, {\n          value: activeTab,\n          index: 1,\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            maxWidth: 700,\n            mx: \"auto\",\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h5\",\n              gutterBottom: true,\n              fontWeight: \"bold\",\n              sx: {\n                mb: 4,\n                textAlign: 'center'\n              },\n              children: \"Preferences & Settings\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 424,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              container: true,\n              spacing: 3,\n              children: [/*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                md: 6,\n                children: /*#__PURE__*/_jsxDEV(Card, {\n                  sx: {\n                    height: '100%',\n                    background: theme => theme.palette.mode === 'dark' ? 'linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%)' : 'linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%)',\n                    border: '1px solid rgba(102, 126, 234, 0.2)'\n                  },\n                  children: /*#__PURE__*/_jsxDEV(CardContent, {\n                    sx: {\n                      p: 3\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Box, {\n                      display: \"flex\",\n                      alignItems: \"center\",\n                      gap: 2,\n                      mb: 3,\n                      children: [/*#__PURE__*/_jsxDEV(LanguageIcon, {\n                        color: \"primary\",\n                        sx: {\n                          fontSize: 28\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 440,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(Box, {\n                        children: [/*#__PURE__*/_jsxDEV(Typography, {\n                          variant: \"h6\",\n                          fontWeight: \"600\",\n                          children: \"Language\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 442,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                          variant: \"body2\",\n                          color: \"text.secondary\",\n                          children: \"Choose your preferred language\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 445,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 441,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 439,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Box, {\n                      display: \"flex\",\n                      flexDirection: \"column\",\n                      gap: 2,\n                      children: [{\n                        code: 'en',\n                        label: '🇺🇸 English',\n                        name: 'English'\n                      }, {\n                        code: 'ar',\n                        label: '🇸🇦 العربية',\n                        name: 'Arabic'\n                      }, {\n                        code: 'fr',\n                        label: '🇫🇷 Français',\n                        name: 'French'\n                      }, {\n                        code: 'es',\n                        label: '🇪🇸 Español',\n                        name: 'Spanish'\n                      }, {\n                        code: 'de',\n                        label: '🇩🇪 Deutsch',\n                        name: 'German'\n                      }].map(lang => /*#__PURE__*/_jsxDEV(Button, {\n                        variant: language === lang.code ? 'contained' : 'outlined',\n                        onClick: () => setLanguage(lang.code),\n                        fullWidth: true,\n                        sx: {\n                          borderRadius: 3,\n                          justifyContent: 'flex-start',\n                          py: 1.5,\n                          background: language === lang.code ? 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' : 'transparent',\n                          '&:hover': {\n                            background: language === lang.code ? 'linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)' : 'rgba(102, 126, 234, 0.1)'\n                          }\n                        },\n                        children: lang.label\n                      }, lang.code, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 458,\n                        columnNumber: 27\n                      }, this))\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 450,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 438,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 431,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 430,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                md: 6,\n                children: /*#__PURE__*/_jsxDEV(Card, {\n                  sx: {\n                    height: '100%',\n                    background: theme => theme.palette.mode === 'dark' ? 'linear-gradient(135deg, rgba(244, 67, 54, 0.1) 0%, rgba(245, 87, 108, 0.1) 100%)' : 'linear-gradient(135deg, rgba(244, 67, 54, 0.05) 0%, rgba(245, 87, 108, 0.05) 100%)',\n                    border: '1px solid rgba(244, 67, 54, 0.2)'\n                  },\n                  children: /*#__PURE__*/_jsxDEV(CardContent, {\n                    sx: {\n                      p: 3\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Box, {\n                      display: \"flex\",\n                      alignItems: \"center\",\n                      gap: 2,\n                      mb: 3,\n                      children: [/*#__PURE__*/_jsxDEV(PaletteIcon, {\n                        color: \"error\",\n                        sx: {\n                          fontSize: 28\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 496,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(Box, {\n                        children: [/*#__PURE__*/_jsxDEV(Typography, {\n                          variant: \"h6\",\n                          fontWeight: \"600\",\n                          children: \"Font Size\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 498,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                          variant: \"body2\",\n                          color: \"text.secondary\",\n                          children: \"Accessibility-friendly option for visual comfort\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 501,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 497,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 495,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Box, {\n                      display: \"flex\",\n                      flexDirection: \"column\",\n                      gap: 2,\n                      children: [{\n                        size: 'small',\n                        label: 'Small',\n                        description: 'Compact text size'\n                      }, {\n                        size: 'medium',\n                        label: 'Medium (Default)',\n                        description: 'Standard text size'\n                      }, {\n                        size: 'large',\n                        label: 'Large',\n                        description: 'Enhanced readability'\n                      }].map(fontOption => /*#__PURE__*/_jsxDEV(Button, {\n                        variant: fontOption.size === 'medium' ? 'contained' : 'outlined',\n                        fullWidth: true,\n                        sx: {\n                          borderRadius: 3,\n                          justifyContent: 'flex-start',\n                          py: 1.5,\n                          flexDirection: 'column',\n                          alignItems: 'flex-start',\n                          background: fontOption.size === 'medium' ? 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)' : 'transparent',\n                          '&:hover': {\n                            background: fontOption.size === 'medium' ? 'linear-gradient(135deg, #e081e8 0%, #e34759 100%)' : 'rgba(244, 67, 54, 0.1)'\n                          }\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(Typography, {\n                          variant: \"subtitle1\",\n                          fontWeight: \"600\",\n                          children: fontOption.label\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 532,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                          variant: \"caption\",\n                          color: \"text.secondary\",\n                          children: fontOption.description\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 535,\n                          columnNumber: 29\n                        }, this)]\n                      }, fontOption.size, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 512,\n                        columnNumber: 27\n                      }, this))\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 506,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 494,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 487,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 486,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 428,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 423,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 422,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TabPanel, {\n          value: activeTab,\n          index: 2,\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            maxWidth: 600,\n            mx: \"auto\",\n            textAlign: \"center\",\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                borderRadius: '50%',\n                width: 80,\n                height: 80,\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                mx: 'auto',\n                mb: 3\n              },\n              children: /*#__PURE__*/_jsxDEV(SecurityIcon, {\n                sx: {\n                  color: 'white',\n                  fontSize: 40\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 564,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 551,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h4\",\n              gutterBottom: true,\n              fontWeight: \"bold\",\n              children: \"AI Security Guard\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 567,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              color: \"text.secondary\",\n              gutterBottom: true,\n              children: \"Version 1.0.0\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 571,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              sx: {\n                mb: 4,\n                lineHeight: 1.7\n              },\n              children: \"Advanced AI-powered security scanning platform providing comprehensive threat detection and analysis for URLs and files. Built with cutting-edge technology to protect your digital assets.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 575,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Card, {\n              sx: {\n                mb: 3\n              },\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  gutterBottom: true,\n                  children: \"Key Features\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 583,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(List, {\n                  children: [/*#__PURE__*/_jsxDEV(ListItem, {\n                    children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                      children: /*#__PURE__*/_jsxDEV(CheckIcon, {\n                        color: \"primary\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 589,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 588,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                      primary: \"Real-time URL threat detection\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 591,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 587,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n                    children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                      children: /*#__PURE__*/_jsxDEV(CheckIcon, {\n                        color: \"primary\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 595,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 594,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                      primary: \"Advanced file malware scanning\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 597,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 593,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n                    children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                      children: /*#__PURE__*/_jsxDEV(CheckIcon, {\n                        color: \"primary\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 601,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 600,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                      primary: \"AI-powered threat analysis\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 603,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 599,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n                    children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                      children: /*#__PURE__*/_jsxDEV(CheckIcon, {\n                        color: \"primary\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 607,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 606,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                      primary: \"Comprehensive security reports\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 609,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 605,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 586,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 582,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 581,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: [\"\\xA9 2024 AI Security Guard. All rights reserved.\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 617,\n                columnNumber: 17\n              }, this), \"Built with \\u2764\\uFE0F for digital security\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 615,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 550,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 549,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 245,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 185,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 86,\n    columnNumber: 5\n  }, this);\n}, \"Vfkhqx+POGp5lvC5cqaLI+W+oFs=\", false, function () {\n  return [useScan];\n})), \"Vfkhqx+POGp5lvC5cqaLI+W+oFs=\", false, function () {\n  return [useScan];\n});\n_c2 = SettingsDialog;\nSettingsDialog.displayName = 'SettingsDialog';\nexport default SettingsDialog;\nvar _c, _c2;\n$RefreshReg$(_c, \"SettingsDialog$React.memo\");\n$RefreshReg$(_c2, \"SettingsDialog\");", "map": {"version": 3, "names": ["React", "useState", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "<PERSON><PERSON>", "Box", "Typography", "Tabs", "Tab", "TextField", "Switch", "FormControlLabel", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Chip", "List", "ListItem", "ListItemIcon", "ListItemText", "Divider", "<PERSON><PERSON>", "IconButton", "Avatar", "Grid", "Settings", "SettingsIcon", "Person", "PersonIcon", "Security", "SecurityIcon", "Language", "LanguageIcon", "Palette", "PaletteIcon", "Info", "InfoIcon", "Star", "StarIcon", "Check", "CheckIcon", "Close", "CloseIcon", "Email", "EmailIcon", "Lock", "LockIcon", "Visibility", "VisibilityIcon", "VisibilityOff", "VisibilityOffIcon", "CrownIcon", "Diamond", "DiamondIcon", "useScan", "jsxDEV", "_jsxDEV", "SettingsDialog", "_s", "memo", "_c", "open", "onClose", "activeTab", "setActiveTab", "showPassword", "setShowPassword", "loginForm", "setLoginForm", "email", "password", "signupForm", "setSignupForm", "name", "confirmPassword", "is<PERSON>ogin", "setIsLogin", "darkMode", "language", "toggleDarkMode", "setLanguage", "handleTabChange", "event", "newValue", "handleLogin", "e", "preventDefault", "console", "log", "handleSignup", "TabPanel", "children", "value", "index", "hidden", "sx", "py", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "max<PERSON><PERSON><PERSON>", "fullWidth", "PaperProps", "borderRadius", "minHeight", "background", "theme", "palette", "mode", "<PERSON><PERSON>ilter", "border", "boxShadow", "position", "overflow", "content", "top", "left", "right", "height", "backgroundSize", "animation", "backgroundPosition", "p", "display", "alignItems", "justifyContent", "borderBottom", "divider", "gap", "width", "color", "fontSize", "variant", "fontWeight", "backgroundClip", "WebkitBackgroundClip", "WebkitTextFillColor", "onClick", "transform", "transition", "borderColor", "onChange", "textColor", "indicatorColor", "textTransform", "icon", "label", "iconPosition", "mb", "mr", "mx", "gutterBottom", "textAlign", "component", "onSubmit", "type", "target", "margin", "autoComplete", "InputProps", "startAdornment", "endAdornment", "mt", "container", "spacing", "item", "xs", "md", "flexDirection", "code", "map", "lang", "size", "description", "fontOption", "lineHeight", "primary", "_c2", "displayName", "$RefreshReg$"], "sources": ["E:/PythonProjects/React.js/Smart AI Assisstant/src/components/SettingsDialog.jsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  <PERSON><PERSON>,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  <PERSON>ton,\n  Box,\n  Typography,\n  Tabs,\n  Tab,\n  TextField,\n  Switch,\n  FormControlLabel,\n  Card,\n  CardContent,\n  Chip,\n  List,\n  ListItem,\n  ListItemIcon,\n  ListItemText,\n  Divider,\n  Alert,\n  IconButton,\n  Avatar,\n  Grid,\n} from '@mui/material';\nimport {\n  Settings as SettingsIcon,\n  Person as PersonIcon,\n  Security as SecurityIcon,\n  Language as LanguageIcon,\n  Palette as PaletteIcon,\n  Info as InfoIcon,\n  Star as StarIcon,\n  Check as CheckIcon,\n  Close as CloseIcon,\n  Email as EmailIcon,\n  Lock as LockIcon,\n  Visibility as VisibilityIcon,\n  VisibilityOff as VisibilityOffIcon,\n  Star as CrownIcon,\n  Diamond as DiamondIcon,\n} from '@mui/icons-material';\nimport { useScan } from '../contexts/ScanContext';\n\nconst SettingsDialog = React.memo(({ open, onClose }) => {\n  const [activeTab, setActiveTab] = useState(0);\n  const [showPassword, setShowPassword] = useState(false);\n  const [loginForm, setLoginForm] = useState({ email: '', password: '' });\n  const [signupForm, setSignupForm] = useState({ \n    name: '', \n    email: '', \n    password: '', \n    confirmPassword: '' \n  });\n  const [isLogin, setIsLogin] = useState(true);\n  \n  const { darkMode, language, toggleDarkMode, setLanguage } = useScan();\n\n  const handleTabChange = (event, newValue) => {\n    setActiveTab(newValue);\n  };\n\n\n\n  const handleLogin = (e) => {\n    e.preventDefault();\n    // Implement login logic here\n    console.log('Login:', loginForm);\n  };\n\n  const handleSignup = (e) => {\n    e.preventDefault();\n    // Implement signup logic here\n    console.log('Signup:', signupForm);\n  };\n\n  const TabPanel = ({ children, value, index }) => (\n    <div hidden={value !== index}>\n      {value === index && <Box sx={{ py: 3 }}>{children}</Box>}\n    </div>\n  );\n\n  return (\n    <Dialog\n      open={open}\n      onClose={onClose}\n      maxWidth=\"lg\"\n      fullWidth\n      PaperProps={{\n        sx: {\n          borderRadius: 4,\n          minHeight: '80vh',\n          background: (theme) => theme.palette.mode === 'dark'\n            ? 'linear-gradient(135deg, rgba(12, 12, 12, 0.98) 0%, rgba(26, 26, 26, 0.98) 50%, rgba(45, 45, 45, 0.98) 100%)'\n            : 'linear-gradient(135deg, rgba(248, 250, 252, 0.98) 0%, rgba(241, 245, 249, 0.98) 50%, rgba(226, 232, 240, 0.98) 100%)',\n          backdropFilter: 'blur(25px) saturate(180%)',\n          border: (theme) => theme.palette.mode === 'dark'\n            ? '1px solid rgba(102, 126, 234, 0.2)'\n            : '1px solid rgba(102, 126, 234, 0.1)',\n          boxShadow: '0 25px 80px rgba(0, 0, 0, 0.3)',\n          position: 'relative',\n          overflow: 'hidden',\n          '&::before': {\n            content: '\"\"',\n            position: 'absolute',\n            top: 0,\n            left: 0,\n            right: 0,\n            height: '4px',\n            background: 'linear-gradient(90deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%)',\n            backgroundSize: '400% 400%',\n            animation: 'gradientShift 8s ease infinite',\n          },\n          '@keyframes gradientShift': {\n            '0%': { backgroundPosition: '0% 50%' },\n            '50%': { backgroundPosition: '100% 50%' },\n            '100%': { backgroundPosition: '0% 50%' },\n          },\n        },\n      }}\n    >\n      <DialogTitle sx={{ p: 0 }}>\n        <Box\n          display=\"flex\"\n          alignItems=\"center\"\n          justifyContent=\"space-between\"\n          sx={{\n            p: 4,\n            background: (theme) => theme.palette.mode === 'dark'\n              ? 'linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%)'\n              : 'linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%)',\n            borderBottom: (theme) => `1px solid ${theme.palette.divider}`,\n          }}\n        >\n          <Box display=\"flex\" alignItems=\"center\" gap={3}>\n            <Box\n              sx={{\n                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                borderRadius: '50%',\n                width: 56,\n                height: 56,\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                boxShadow: '0 8px 25px rgba(102, 126, 234, 0.3)',\n              }}\n            >\n              <SettingsIcon sx={{ color: 'white', fontSize: 28 }} />\n            </Box>\n            <Box>\n              <Typography variant=\"h4\" fontWeight=\"bold\"\n                sx={{\n                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                  backgroundClip: 'text',\n                  WebkitBackgroundClip: 'text',\n                  WebkitTextFillColor: 'transparent',\n                }}\n              >\n                Settings & Account\n              </Typography>\n              <Typography variant=\"body2\" color=\"text.secondary\">\n                Manage your preferences and account settings\n              </Typography>\n            </Box>\n          </Box>\n          <IconButton\n            onClick={onClose}\n            sx={{\n              background: 'rgba(255, 255, 255, 0.1)',\n              backdropFilter: 'blur(10px)',\n              '&:hover': {\n                background: 'rgba(255, 255, 255, 0.2)',\n                transform: 'scale(1.05)',\n              },\n              transition: 'all 0.2s ease',\n            }}\n          >\n            <CloseIcon />\n          </IconButton>\n        </Box>\n      </DialogTitle>\n\n      <DialogContent sx={{ p: 0 }}>\n        <Box\n          sx={{\n            borderBottom: 1,\n            borderColor: 'divider',\n            background: (theme) => theme.palette.mode === 'dark'\n              ? 'rgba(255, 255, 255, 0.02)'\n              : 'rgba(0, 0, 0, 0.02)',\n          }}\n        >\n          <Tabs\n            value={activeTab}\n            onChange={handleTabChange}\n            variant=\"fullWidth\"\n            textColor=\"primary\"\n            indicatorColor=\"primary\"\n            sx={{\n              '& .MuiTab-root': {\n                minHeight: 80,\n                fontSize: '1rem',\n                fontWeight: 600,\n                textTransform: 'none',\n                transition: 'all 0.3s ease',\n                '&:hover': {\n                  background: (theme) => theme.palette.mode === 'dark'\n                    ? 'rgba(102, 126, 234, 0.1)'\n                    : 'rgba(102, 126, 234, 0.05)',\n                  transform: 'translateY(-2px)',\n                },\n                '&.Mui-selected': {\n                  background: (theme) => theme.palette.mode === 'dark'\n                    ? 'linear-gradient(135deg, rgba(102, 126, 234, 0.2) 0%, rgba(118, 75, 162, 0.2) 100%)'\n                    : 'linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%)',\n                },\n              },\n              '& .MuiTabs-indicator': {\n                height: 3,\n                borderRadius: '3px 3px 0 0',\n                background: 'linear-gradient(90deg, #667eea 0%, #764ba2 100%)',\n              },\n            }}\n          >\n            <Tab\n              icon={<PersonIcon sx={{ fontSize: 24 }} />}\n              label=\"Account\"\n              iconPosition=\"start\"\n            />\n            <Tab\n              icon={<PaletteIcon sx={{ fontSize: 24 }} />}\n              label=\"Preferences\"\n              iconPosition=\"start\"\n            />\n            <Tab\n              icon={<InfoIcon sx={{ fontSize: 24 }} />}\n              label=\"About\"\n              iconPosition=\"start\"\n            />\n          </Tabs>\n        </Box>\n\n        <Box sx={{ p: 3 }}>\n          {/* Account Tab */}\n          <TabPanel value={activeTab} index={0}>\n            <Box display=\"flex\" justifyContent=\"center\" mb={3}>\n              <Button\n                variant={isLogin ? 'contained' : 'outlined'}\n                onClick={() => setIsLogin(true)}\n                sx={{ mr: 1, borderRadius: 3 }}\n              >\n                Login\n              </Button>\n              <Button\n                variant={!isLogin ? 'contained' : 'outlined'}\n                onClick={() => setIsLogin(false)}\n                sx={{ borderRadius: 3 }}\n              >\n                Sign Up\n              </Button>\n            </Box>\n\n            {isLogin ? (\n              <Card sx={{ maxWidth: 400, mx: 'auto' }}>\n                <CardContent>\n                  <Typography variant=\"h6\" gutterBottom textAlign=\"center\">\n                    Welcome Back\n                  </Typography>\n                  <Box component=\"form\" onSubmit={handleLogin}>\n                    <TextField\n                      fullWidth\n                      label=\"Email\"\n                      type=\"email\"\n                      value={loginForm.email}\n                      onChange={(e) => setLoginForm({ ...loginForm, email: e.target.value })}\n                      margin=\"normal\"\n                      variant=\"outlined\"\n                      autoComplete=\"email\"\n                      sx={{\n                        '& .MuiOutlinedInput-root': {\n                          borderRadius: 3,\n                        },\n                      }}\n                      InputProps={{\n                        startAdornment: <EmailIcon sx={{ mr: 1, color: 'text.secondary' }} />,\n                      }}\n                    />\n                    <TextField\n                      fullWidth\n                      label=\"Password\"\n                      type={showPassword ? 'text' : 'password'}\n                      value={loginForm.password}\n                      onChange={(e) => setLoginForm({ ...loginForm, password: e.target.value })}\n                      margin=\"normal\"\n                      variant=\"outlined\"\n                      autoComplete=\"current-password\"\n                      sx={{\n                        '& .MuiOutlinedInput-root': {\n                          borderRadius: 3,\n                        },\n                      }}\n                      InputProps={{\n                        startAdornment: <LockIcon sx={{ mr: 1, color: 'text.secondary' }} />,\n                        endAdornment: (\n                          <IconButton onClick={() => setShowPassword(!showPassword)}>\n                            {showPassword ? <VisibilityOffIcon /> : <VisibilityIcon />}\n                          </IconButton>\n                        ),\n                      }}\n                    />\n                    <Button\n                      type=\"submit\"\n                      fullWidth\n                      variant=\"contained\"\n                      sx={{ mt: 3, mb: 2, borderRadius: 3 }}\n                    >\n                      Sign In\n                    </Button>\n                  </Box>\n                </CardContent>\n              </Card>\n            ) : (\n              <Card sx={{ maxWidth: 400, mx: 'auto' }}>\n                <CardContent>\n                  <Typography variant=\"h6\" gutterBottom textAlign=\"center\">\n                    Create Account\n                  </Typography>\n                  <Box component=\"form\" onSubmit={handleSignup}>\n                    <TextField\n                      fullWidth\n                      label=\"Full Name\"\n                      value={signupForm.name}\n                      onChange={(e) => setSignupForm({ ...signupForm, name: e.target.value })}\n                      margin=\"normal\"\n                      variant=\"outlined\"\n                      autoComplete=\"name\"\n                      sx={{\n                        '& .MuiOutlinedInput-root': {\n                          borderRadius: 3,\n                        },\n                      }}\n                      InputProps={{\n                        startAdornment: <PersonIcon sx={{ mr: 1, color: 'text.secondary' }} />,\n                      }}\n                    />\n                    <TextField\n                      fullWidth\n                      label=\"Email\"\n                      type=\"email\"\n                      value={signupForm.email}\n                      onChange={(e) => setSignupForm({ ...signupForm, email: e.target.value })}\n                      margin=\"normal\"\n                      variant=\"outlined\"\n                      autoComplete=\"email\"\n                      sx={{\n                        '& .MuiOutlinedInput-root': {\n                          borderRadius: 3,\n                        },\n                      }}\n                      InputProps={{\n                        startAdornment: <EmailIcon sx={{ mr: 1, color: 'text.secondary' }} />,\n                      }}\n                    />\n                    <TextField\n                      fullWidth\n                      label=\"Password\"\n                      type={showPassword ? 'text' : 'password'}\n                      value={signupForm.password}\n                      onChange={(e) => setSignupForm({ ...signupForm, password: e.target.value })}\n                      margin=\"normal\"\n                      variant=\"outlined\"\n                      autoComplete=\"new-password\"\n                      sx={{\n                        '& .MuiOutlinedInput-root': {\n                          borderRadius: 3,\n                        },\n                      }}\n                      InputProps={{\n                        startAdornment: <LockIcon sx={{ mr: 1, color: 'text.secondary' }} />,\n                        endAdornment: (\n                          <IconButton onClick={() => setShowPassword(!showPassword)}>\n                            {showPassword ? <VisibilityOffIcon /> : <VisibilityIcon />}\n                          </IconButton>\n                        ),\n                      }}\n                    />\n                    <TextField\n                      fullWidth\n                      label=\"Confirm Password\"\n                      type=\"password\"\n                      value={signupForm.confirmPassword}\n                      onChange={(e) => setSignupForm({ ...signupForm, confirmPassword: e.target.value })}\n                      margin=\"normal\"\n                      variant=\"outlined\"\n                      autoComplete=\"new-password\"\n                      sx={{\n                        '& .MuiOutlinedInput-root': {\n                          borderRadius: 3,\n                        },\n                      }}\n                      InputProps={{\n                        startAdornment: <LockIcon sx={{ mr: 1, color: 'text.secondary' }} />,\n                      }}\n                    />\n                    <Button\n                      type=\"submit\"\n                      fullWidth\n                      variant=\"contained\"\n                      sx={{ mt: 3, mb: 2, borderRadius: 3 }}\n                    >\n                      Create Account\n                    </Button>\n                  </Box>\n                </CardContent>\n              </Card>\n            )}\n          </TabPanel>\n\n          {/* Preferences Tab */}\n          <TabPanel value={activeTab} index={1}>\n            <Box maxWidth={700} mx=\"auto\">\n              <Typography variant=\"h5\" gutterBottom fontWeight=\"bold\" sx={{ mb: 4, textAlign: 'center' }}>\n                Preferences & Settings\n              </Typography>\n\n              <Grid container spacing={3}>\n                {/* Language Settings */}\n                <Grid item xs={12} md={6}>\n                  <Card sx={{\n                    height: '100%',\n                    background: (theme) => theme.palette.mode === 'dark'\n                      ? 'linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%)'\n                      : 'linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%)',\n                    border: '1px solid rgba(102, 126, 234, 0.2)',\n                  }}>\n                    <CardContent sx={{ p: 3 }}>\n                      <Box display=\"flex\" alignItems=\"center\" gap={2} mb={3}>\n                        <LanguageIcon color=\"primary\" sx={{ fontSize: 28 }} />\n                        <Box>\n                          <Typography variant=\"h6\" fontWeight=\"600\">\n                            Language\n                          </Typography>\n                          <Typography variant=\"body2\" color=\"text.secondary\">\n                            Choose your preferred language\n                          </Typography>\n                        </Box>\n                      </Box>\n                      <Box display=\"flex\" flexDirection=\"column\" gap={2}>\n                        {[\n                          { code: 'en', label: '🇺🇸 English', name: 'English' },\n                          { code: 'ar', label: '🇸🇦 العربية', name: 'Arabic' },\n                          { code: 'fr', label: '🇫🇷 Français', name: 'French' },\n                          { code: 'es', label: '🇪🇸 Español', name: 'Spanish' },\n                          { code: 'de', label: '🇩🇪 Deutsch', name: 'German' },\n                        ].map((lang) => (\n                          <Button\n                            key={lang.code}\n                            variant={language === lang.code ? 'contained' : 'outlined'}\n                            onClick={() => setLanguage(lang.code)}\n                            fullWidth\n                            sx={{\n                              borderRadius: 3,\n                              justifyContent: 'flex-start',\n                              py: 1.5,\n                              background: language === lang.code\n                                ? 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'\n                                : 'transparent',\n                              '&:hover': {\n                                background: language === lang.code\n                                  ? 'linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)'\n                                  : 'rgba(102, 126, 234, 0.1)',\n                              },\n                            }}\n                          >\n                            {lang.label}\n                          </Button>\n                        ))}\n                      </Box>\n                    </CardContent>\n                  </Card>\n                </Grid>\n\n                {/* Font Size Settings */}\n                <Grid item xs={12} md={6}>\n                  <Card sx={{\n                    height: '100%',\n                    background: (theme) => theme.palette.mode === 'dark'\n                      ? 'linear-gradient(135deg, rgba(244, 67, 54, 0.1) 0%, rgba(245, 87, 108, 0.1) 100%)'\n                      : 'linear-gradient(135deg, rgba(244, 67, 54, 0.05) 0%, rgba(245, 87, 108, 0.05) 100%)',\n                    border: '1px solid rgba(244, 67, 54, 0.2)',\n                  }}>\n                    <CardContent sx={{ p: 3 }}>\n                      <Box display=\"flex\" alignItems=\"center\" gap={2} mb={3}>\n                        <PaletteIcon color=\"error\" sx={{ fontSize: 28 }} />\n                        <Box>\n                          <Typography variant=\"h6\" fontWeight=\"600\">\n                            Font Size\n                          </Typography>\n                          <Typography variant=\"body2\" color=\"text.secondary\">\n                            Accessibility-friendly option for visual comfort\n                          </Typography>\n                        </Box>\n                      </Box>\n                      <Box display=\"flex\" flexDirection=\"column\" gap={2}>\n                        {[\n                          { size: 'small', label: 'Small', description: 'Compact text size' },\n                          { size: 'medium', label: 'Medium (Default)', description: 'Standard text size' },\n                          { size: 'large', label: 'Large', description: 'Enhanced readability' },\n                        ].map((fontOption) => (\n                          <Button\n                            key={fontOption.size}\n                            variant={fontOption.size === 'medium' ? 'contained' : 'outlined'}\n                            fullWidth\n                            sx={{\n                              borderRadius: 3,\n                              justifyContent: 'flex-start',\n                              py: 1.5,\n                              flexDirection: 'column',\n                              alignItems: 'flex-start',\n                              background: fontOption.size === 'medium'\n                                ? 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)'\n                                : 'transparent',\n                              '&:hover': {\n                                background: fontOption.size === 'medium'\n                                  ? 'linear-gradient(135deg, #e081e8 0%, #e34759 100%)'\n                                  : 'rgba(244, 67, 54, 0.1)',\n                              },\n                            }}\n                          >\n                            <Typography variant=\"subtitle1\" fontWeight=\"600\">\n                              {fontOption.label}\n                            </Typography>\n                            <Typography variant=\"caption\" color=\"text.secondary\">\n                              {fontOption.description}\n                            </Typography>\n                          </Button>\n                        ))}\n                      </Box>\n                    </CardContent>\n                  </Card>\n                </Grid>\n              </Grid>\n            </Box>\n          </TabPanel>\n\n          {/* About Tab */}\n          <TabPanel value={activeTab} index={2}>\n            <Box maxWidth={600} mx=\"auto\" textAlign=\"center\">\n              <Box\n                sx={{\n                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                  borderRadius: '50%',\n                  width: 80,\n                  height: 80,\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  mx: 'auto',\n                  mb: 3,\n                }}\n              >\n                <SecurityIcon sx={{ color: 'white', fontSize: 40 }} />\n              </Box>\n\n              <Typography variant=\"h4\" gutterBottom fontWeight=\"bold\">\n                AI Security Guard\n              </Typography>\n\n              <Typography variant=\"h6\" color=\"text.secondary\" gutterBottom>\n                Version 1.0.0\n              </Typography>\n\n              <Typography variant=\"body1\" sx={{ mb: 4, lineHeight: 1.7 }}>\n                Advanced AI-powered security scanning platform providing comprehensive\n                threat detection and analysis for URLs and files. Built with cutting-edge\n                technology to protect your digital assets.\n              </Typography>\n\n              <Card sx={{ mb: 3 }}>\n                <CardContent>\n                  <Typography variant=\"h6\" gutterBottom>\n                    Key Features\n                  </Typography>\n                  <List>\n                    <ListItem>\n                      <ListItemIcon>\n                        <CheckIcon color=\"primary\" />\n                      </ListItemIcon>\n                      <ListItemText primary=\"Real-time URL threat detection\" />\n                    </ListItem>\n                    <ListItem>\n                      <ListItemIcon>\n                        <CheckIcon color=\"primary\" />\n                      </ListItemIcon>\n                      <ListItemText primary=\"Advanced file malware scanning\" />\n                    </ListItem>\n                    <ListItem>\n                      <ListItemIcon>\n                        <CheckIcon color=\"primary\" />\n                      </ListItemIcon>\n                      <ListItemText primary=\"AI-powered threat analysis\" />\n                    </ListItem>\n                    <ListItem>\n                      <ListItemIcon>\n                        <CheckIcon color=\"primary\" />\n                      </ListItemIcon>\n                      <ListItemText primary=\"Comprehensive security reports\" />\n                    </ListItem>\n                  </List>\n                </CardContent>\n              </Card>\n\n              <Typography variant=\"body2\" color=\"text.secondary\">\n                © 2024 AI Security Guard. All rights reserved.\n                <br />\n                Built with ❤️ for digital security\n              </Typography>\n            </Box>\n          </TabPanel>\n        </Box>\n      </DialogContent>\n    </Dialog>\n  );\n});\n\nSettingsDialog.displayName = 'SettingsDialog';\n\nexport default SettingsDialog;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,MAAM,EACNC,GAAG,EACHC,UAAU,EACVC,IAAI,EACJC,GAAG,EACHC,SAAS,EACTC,MAAM,EACNC,gBAAgB,EAChBC,IAAI,EACJC,WAAW,EACXC,IAAI,EACJC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,YAAY,EACZC,OAAO,EACPC,KAAK,EACLC,UAAU,EACVC,MAAM,EACNC,IAAI,QACC,eAAe;AACtB,SACEC,QAAQ,IAAIC,YAAY,EACxBC,MAAM,IAAIC,UAAU,EACpBC,QAAQ,IAAIC,YAAY,EACxBC,QAAQ,IAAIC,YAAY,EACxBC,OAAO,IAAIC,WAAW,EACtBC,IAAI,IAAIC,QAAQ,EAChBC,IAAI,IAAIC,QAAQ,EAChBC,KAAK,IAAIC,SAAS,EAClBC,KAAK,IAAIC,SAAS,EAClBC,KAAK,IAAIC,SAAS,EAClBC,IAAI,IAAIC,QAAQ,EAChBC,UAAU,IAAIC,cAAc,EAC5BC,aAAa,IAAIC,iBAAiB,EAClCb,IAAI,IAAIc,SAAS,EACjBC,OAAO,IAAIC,WAAW,QACjB,qBAAqB;AAC5B,SAASC,OAAO,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElD,MAAMC,cAAc,gBAAAC,EAAA,cAAG3D,KAAK,CAAC4D,IAAI,CAAAC,EAAA,GAAAF,EAAA,CAAC,CAAC;EAAEG,IAAI;EAAEC;AAAQ,CAAC,KAAK;EAAAJ,EAAA;EACvD,MAAM,CAACK,SAAS,EAAEC,YAAY,CAAC,GAAGhE,QAAQ,CAAC,CAAC,CAAC;EAC7C,MAAM,CAACiE,YAAY,EAAEC,eAAe,CAAC,GAAGlE,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACmE,SAAS,EAAEC,YAAY,CAAC,GAAGpE,QAAQ,CAAC;IAAEqE,KAAK,EAAE,EAAE;IAAEC,QAAQ,EAAE;EAAG,CAAC,CAAC;EACvE,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGxE,QAAQ,CAAC;IAC3CyE,IAAI,EAAE,EAAE;IACRJ,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZI,eAAe,EAAE;EACnB,CAAC,CAAC;EACF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG5E,QAAQ,CAAC,IAAI,CAAC;EAE5C,MAAM;IAAE6E,QAAQ;IAAEC,QAAQ;IAAEC,cAAc;IAAEC;EAAY,CAAC,GAAG1B,OAAO,CAAC,CAAC;EAErE,MAAM2B,eAAe,GAAGA,CAACC,KAAK,EAAEC,QAAQ,KAAK;IAC3CnB,YAAY,CAACmB,QAAQ,CAAC;EACxB,CAAC;EAID,MAAMC,WAAW,GAAIC,CAAC,IAAK;IACzBA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB;IACAC,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAErB,SAAS,CAAC;EAClC,CAAC;EAED,MAAMsB,YAAY,GAAIJ,CAAC,IAAK;IAC1BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB;IACAC,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEjB,UAAU,CAAC;EACpC,CAAC;EAED,MAAMmB,QAAQ,GAAGA,CAAC;IAAEC,QAAQ;IAAEC,KAAK;IAAEC;EAAM,CAAC,kBAC1CrC,OAAA;IAAKsC,MAAM,EAAEF,KAAK,KAAKC,KAAM;IAAAF,QAAA,EAC1BC,KAAK,KAAKC,KAAK,iBAAIrC,OAAA,CAAClD,GAAG;MAACyF,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAL,QAAA,EAAEA;IAAQ;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACrD,CACN;EAED,oBACE5C,OAAA,CAACvD,MAAM;IACL4D,IAAI,EAAEA,IAAK;IACXC,OAAO,EAAEA,OAAQ;IACjBuC,QAAQ,EAAC,IAAI;IACbC,SAAS;IACTC,UAAU,EAAE;MACVR,EAAE,EAAE;QACFS,YAAY,EAAE,CAAC;QACfC,SAAS,EAAE,MAAM;QACjBC,UAAU,EAAGC,KAAK,IAAKA,KAAK,CAACC,OAAO,CAACC,IAAI,KAAK,MAAM,GAChD,6GAA6G,GAC7G,sHAAsH;QAC1HC,cAAc,EAAE,2BAA2B;QAC3CC,MAAM,EAAGJ,KAAK,IAAKA,KAAK,CAACC,OAAO,CAACC,IAAI,KAAK,MAAM,GAC5C,oCAAoC,GACpC,oCAAoC;QACxCG,SAAS,EAAE,gCAAgC;QAC3CC,QAAQ,EAAE,UAAU;QACpBC,QAAQ,EAAE,QAAQ;QAClB,WAAW,EAAE;UACXC,OAAO,EAAE,IAAI;UACbF,QAAQ,EAAE,UAAU;UACpBG,GAAG,EAAE,CAAC;UACNC,IAAI,EAAE,CAAC;UACPC,KAAK,EAAE,CAAC;UACRC,MAAM,EAAE,KAAK;UACbb,UAAU,EAAE,yFAAyF;UACrGc,cAAc,EAAE,WAAW;UAC3BC,SAAS,EAAE;QACb,CAAC;QACD,0BAA0B,EAAE;UAC1B,IAAI,EAAE;YAAEC,kBAAkB,EAAE;UAAS,CAAC;UACtC,KAAK,EAAE;YAAEA,kBAAkB,EAAE;UAAW,CAAC;UACzC,MAAM,EAAE;YAAEA,kBAAkB,EAAE;UAAS;QACzC;MACF;IACF,CAAE;IAAA/B,QAAA,gBAEFnC,OAAA,CAACtD,WAAW;MAAC6F,EAAE,EAAE;QAAE4B,CAAC,EAAE;MAAE,CAAE;MAAAhC,QAAA,eACxBnC,OAAA,CAAClD,GAAG;QACFsH,OAAO,EAAC,MAAM;QACdC,UAAU,EAAC,QAAQ;QACnBC,cAAc,EAAC,eAAe;QAC9B/B,EAAE,EAAE;UACF4B,CAAC,EAAE,CAAC;UACJjB,UAAU,EAAGC,KAAK,IAAKA,KAAK,CAACC,OAAO,CAACC,IAAI,KAAK,MAAM,GAChD,oFAAoF,GACpF,sFAAsF;UAC1FkB,YAAY,EAAGpB,KAAK,IAAK,aAAaA,KAAK,CAACC,OAAO,CAACoB,OAAO;QAC7D,CAAE;QAAArC,QAAA,gBAEFnC,OAAA,CAAClD,GAAG;UAACsH,OAAO,EAAC,MAAM;UAACC,UAAU,EAAC,QAAQ;UAACI,GAAG,EAAE,CAAE;UAAAtC,QAAA,gBAC7CnC,OAAA,CAAClD,GAAG;YACFyF,EAAE,EAAE;cACFW,UAAU,EAAE,mDAAmD;cAC/DF,YAAY,EAAE,KAAK;cACnB0B,KAAK,EAAE,EAAE;cACTX,MAAM,EAAE,EAAE;cACVK,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpBC,cAAc,EAAE,QAAQ;cACxBd,SAAS,EAAE;YACb,CAAE;YAAArB,QAAA,eAEFnC,OAAA,CAAC9B,YAAY;cAACqE,EAAE,EAAE;gBAAEoC,KAAK,EAAE,OAAO;gBAAEC,QAAQ,EAAE;cAAG;YAAE;cAAAnC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnD,CAAC,eACN5C,OAAA,CAAClD,GAAG;YAAAqF,QAAA,gBACFnC,OAAA,CAACjD,UAAU;cAAC8H,OAAO,EAAC,IAAI;cAACC,UAAU,EAAC,MAAM;cACxCvC,EAAE,EAAE;gBACFW,UAAU,EAAE,mDAAmD;gBAC/D6B,cAAc,EAAE,MAAM;gBACtBC,oBAAoB,EAAE,MAAM;gBAC5BC,mBAAmB,EAAE;cACvB,CAAE;cAAA9C,QAAA,EACH;YAED;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb5C,OAAA,CAACjD,UAAU;cAAC8H,OAAO,EAAC,OAAO;cAACF,KAAK,EAAC,gBAAgB;cAAAxC,QAAA,EAAC;YAEnD;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACN5C,OAAA,CAAClC,UAAU;UACToH,OAAO,EAAE5E,OAAQ;UACjBiC,EAAE,EAAE;YACFW,UAAU,EAAE,0BAA0B;YACtCI,cAAc,EAAE,YAAY;YAC5B,SAAS,EAAE;cACTJ,UAAU,EAAE,0BAA0B;cACtCiC,SAAS,EAAE;YACb,CAAC;YACDC,UAAU,EAAE;UACd,CAAE;UAAAjD,QAAA,eAEFnC,OAAA,CAACd,SAAS;YAAAuD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CAAC,eAEd5C,OAAA,CAACrD,aAAa;MAAC4F,EAAE,EAAE;QAAE4B,CAAC,EAAE;MAAE,CAAE;MAAAhC,QAAA,gBAC1BnC,OAAA,CAAClD,GAAG;QACFyF,EAAE,EAAE;UACFgC,YAAY,EAAE,CAAC;UACfc,WAAW,EAAE,SAAS;UACtBnC,UAAU,EAAGC,KAAK,IAAKA,KAAK,CAACC,OAAO,CAACC,IAAI,KAAK,MAAM,GAChD,2BAA2B,GAC3B;QACN,CAAE;QAAAlB,QAAA,eAEFnC,OAAA,CAAChD,IAAI;UACHoF,KAAK,EAAE7B,SAAU;UACjB+E,QAAQ,EAAE7D,eAAgB;UAC1BoD,OAAO,EAAC,WAAW;UACnBU,SAAS,EAAC,SAAS;UACnBC,cAAc,EAAC,SAAS;UACxBjD,EAAE,EAAE;YACF,gBAAgB,EAAE;cAChBU,SAAS,EAAE,EAAE;cACb2B,QAAQ,EAAE,MAAM;cAChBE,UAAU,EAAE,GAAG;cACfW,aAAa,EAAE,MAAM;cACrBL,UAAU,EAAE,eAAe;cAC3B,SAAS,EAAE;gBACTlC,UAAU,EAAGC,KAAK,IAAKA,KAAK,CAACC,OAAO,CAACC,IAAI,KAAK,MAAM,GAChD,0BAA0B,GAC1B,2BAA2B;gBAC/B8B,SAAS,EAAE;cACb,CAAC;cACD,gBAAgB,EAAE;gBAChBjC,UAAU,EAAGC,KAAK,IAAKA,KAAK,CAACC,OAAO,CAACC,IAAI,KAAK,MAAM,GAChD,oFAAoF,GACpF;cACN;YACF,CAAC;YACD,sBAAsB,EAAE;cACtBU,MAAM,EAAE,CAAC;cACTf,YAAY,EAAE,aAAa;cAC3BE,UAAU,EAAE;YACd;UACF,CAAE;UAAAf,QAAA,gBAEFnC,OAAA,CAAC/C,GAAG;YACFyI,IAAI,eAAE1F,OAAA,CAAC5B,UAAU;cAACmE,EAAE,EAAE;gBAAEqC,QAAQ,EAAE;cAAG;YAAE;cAAAnC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC3C+C,KAAK,EAAC,SAAS;YACfC,YAAY,EAAC;UAAO;YAAAnD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC,eACF5C,OAAA,CAAC/C,GAAG;YACFyI,IAAI,eAAE1F,OAAA,CAACtB,WAAW;cAAC6D,EAAE,EAAE;gBAAEqC,QAAQ,EAAE;cAAG;YAAE;cAAAnC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC5C+C,KAAK,EAAC,aAAa;YACnBC,YAAY,EAAC;UAAO;YAAAnD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC,eACF5C,OAAA,CAAC/C,GAAG;YACFyI,IAAI,eAAE1F,OAAA,CAACpB,QAAQ;cAAC2D,EAAE,EAAE;gBAAEqC,QAAQ,EAAE;cAAG;YAAE;cAAAnC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACzC+C,KAAK,EAAC,OAAO;YACbC,YAAY,EAAC;UAAO;YAAAnD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAEN5C,OAAA,CAAClD,GAAG;QAACyF,EAAE,EAAE;UAAE4B,CAAC,EAAE;QAAE,CAAE;QAAAhC,QAAA,gBAEhBnC,OAAA,CAACkC,QAAQ;UAACE,KAAK,EAAE7B,SAAU;UAAC8B,KAAK,EAAE,CAAE;UAAAF,QAAA,gBACnCnC,OAAA,CAAClD,GAAG;YAACsH,OAAO,EAAC,MAAM;YAACE,cAAc,EAAC,QAAQ;YAACuB,EAAE,EAAE,CAAE;YAAA1D,QAAA,gBAChDnC,OAAA,CAACnD,MAAM;cACLgI,OAAO,EAAE1D,OAAO,GAAG,WAAW,GAAG,UAAW;cAC5C+D,OAAO,EAAEA,CAAA,KAAM9D,UAAU,CAAC,IAAI,CAAE;cAChCmB,EAAE,EAAE;gBAAEuD,EAAE,EAAE,CAAC;gBAAE9C,YAAY,EAAE;cAAE,CAAE;cAAAb,QAAA,EAChC;YAED;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT5C,OAAA,CAACnD,MAAM;cACLgI,OAAO,EAAE,CAAC1D,OAAO,GAAG,WAAW,GAAG,UAAW;cAC7C+D,OAAO,EAAEA,CAAA,KAAM9D,UAAU,CAAC,KAAK,CAAE;cACjCmB,EAAE,EAAE;gBAAES,YAAY,EAAE;cAAE,CAAE;cAAAb,QAAA,EACzB;YAED;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,EAELzB,OAAO,gBACNnB,OAAA,CAAC3C,IAAI;YAACkF,EAAE,EAAE;cAAEM,QAAQ,EAAE,GAAG;cAAEkD,EAAE,EAAE;YAAO,CAAE;YAAA5D,QAAA,eACtCnC,OAAA,CAAC1C,WAAW;cAAA6E,QAAA,gBACVnC,OAAA,CAACjD,UAAU;gBAAC8H,OAAO,EAAC,IAAI;gBAACmB,YAAY;gBAACC,SAAS,EAAC,QAAQ;gBAAA9D,QAAA,EAAC;cAEzD;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACb5C,OAAA,CAAClD,GAAG;gBAACoJ,SAAS,EAAC,MAAM;gBAACC,QAAQ,EAAEvE,WAAY;gBAAAO,QAAA,gBAC1CnC,OAAA,CAAC9C,SAAS;kBACR4F,SAAS;kBACT6C,KAAK,EAAC,OAAO;kBACbS,IAAI,EAAC,OAAO;kBACZhE,KAAK,EAAEzB,SAAS,CAACE,KAAM;kBACvByE,QAAQ,EAAGzD,CAAC,IAAKjB,YAAY,CAAC;oBAAE,GAAGD,SAAS;oBAAEE,KAAK,EAAEgB,CAAC,CAACwE,MAAM,CAACjE;kBAAM,CAAC,CAAE;kBACvEkE,MAAM,EAAC,QAAQ;kBACfzB,OAAO,EAAC,UAAU;kBAClB0B,YAAY,EAAC,OAAO;kBACpBhE,EAAE,EAAE;oBACF,0BAA0B,EAAE;sBAC1BS,YAAY,EAAE;oBAChB;kBACF,CAAE;kBACFwD,UAAU,EAAE;oBACVC,cAAc,eAAEzG,OAAA,CAACZ,SAAS;sBAACmD,EAAE,EAAE;wBAAEuD,EAAE,EAAE,CAAC;wBAAEnB,KAAK,EAAE;sBAAiB;oBAAE;sBAAAlC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBACtE;gBAAE;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACF5C,OAAA,CAAC9C,SAAS;kBACR4F,SAAS;kBACT6C,KAAK,EAAC,UAAU;kBAChBS,IAAI,EAAE3F,YAAY,GAAG,MAAM,GAAG,UAAW;kBACzC2B,KAAK,EAAEzB,SAAS,CAACG,QAAS;kBAC1BwE,QAAQ,EAAGzD,CAAC,IAAKjB,YAAY,CAAC;oBAAE,GAAGD,SAAS;oBAAEG,QAAQ,EAAEe,CAAC,CAACwE,MAAM,CAACjE;kBAAM,CAAC,CAAE;kBAC1EkE,MAAM,EAAC,QAAQ;kBACfzB,OAAO,EAAC,UAAU;kBAClB0B,YAAY,EAAC,kBAAkB;kBAC/BhE,EAAE,EAAE;oBACF,0BAA0B,EAAE;sBAC1BS,YAAY,EAAE;oBAChB;kBACF,CAAE;kBACFwD,UAAU,EAAE;oBACVC,cAAc,eAAEzG,OAAA,CAACV,QAAQ;sBAACiD,EAAE,EAAE;wBAAEuD,EAAE,EAAE,CAAC;wBAAEnB,KAAK,EAAE;sBAAiB;oBAAE;sBAAAlC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC;oBACpE8D,YAAY,eACV1G,OAAA,CAAClC,UAAU;sBAACoH,OAAO,EAAEA,CAAA,KAAMxE,eAAe,CAAC,CAACD,YAAY,CAAE;sBAAA0B,QAAA,EACvD1B,YAAY,gBAAGT,OAAA,CAACN,iBAAiB;wBAAA+C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,gBAAG5C,OAAA,CAACR,cAAc;wBAAAiD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChD;kBAEhB;gBAAE;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACF5C,OAAA,CAACnD,MAAM;kBACLuJ,IAAI,EAAC,QAAQ;kBACbtD,SAAS;kBACT+B,OAAO,EAAC,WAAW;kBACnBtC,EAAE,EAAE;oBAAEoE,EAAE,EAAE,CAAC;oBAAEd,EAAE,EAAE,CAAC;oBAAE7C,YAAY,EAAE;kBAAE,CAAE;kBAAAb,QAAA,EACvC;gBAED;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,gBAEP5C,OAAA,CAAC3C,IAAI;YAACkF,EAAE,EAAE;cAAEM,QAAQ,EAAE,GAAG;cAAEkD,EAAE,EAAE;YAAO,CAAE;YAAA5D,QAAA,eACtCnC,OAAA,CAAC1C,WAAW;cAAA6E,QAAA,gBACVnC,OAAA,CAACjD,UAAU;gBAAC8H,OAAO,EAAC,IAAI;gBAACmB,YAAY;gBAACC,SAAS,EAAC,QAAQ;gBAAA9D,QAAA,EAAC;cAEzD;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACb5C,OAAA,CAAClD,GAAG;gBAACoJ,SAAS,EAAC,MAAM;gBAACC,QAAQ,EAAElE,YAAa;gBAAAE,QAAA,gBAC3CnC,OAAA,CAAC9C,SAAS;kBACR4F,SAAS;kBACT6C,KAAK,EAAC,WAAW;kBACjBvD,KAAK,EAAErB,UAAU,CAACE,IAAK;kBACvBqE,QAAQ,EAAGzD,CAAC,IAAKb,aAAa,CAAC;oBAAE,GAAGD,UAAU;oBAAEE,IAAI,EAAEY,CAAC,CAACwE,MAAM,CAACjE;kBAAM,CAAC,CAAE;kBACxEkE,MAAM,EAAC,QAAQ;kBACfzB,OAAO,EAAC,UAAU;kBAClB0B,YAAY,EAAC,MAAM;kBACnBhE,EAAE,EAAE;oBACF,0BAA0B,EAAE;sBAC1BS,YAAY,EAAE;oBAChB;kBACF,CAAE;kBACFwD,UAAU,EAAE;oBACVC,cAAc,eAAEzG,OAAA,CAAC5B,UAAU;sBAACmE,EAAE,EAAE;wBAAEuD,EAAE,EAAE,CAAC;wBAAEnB,KAAK,EAAE;sBAAiB;oBAAE;sBAAAlC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBACvE;gBAAE;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACF5C,OAAA,CAAC9C,SAAS;kBACR4F,SAAS;kBACT6C,KAAK,EAAC,OAAO;kBACbS,IAAI,EAAC,OAAO;kBACZhE,KAAK,EAAErB,UAAU,CAACF,KAAM;kBACxByE,QAAQ,EAAGzD,CAAC,IAAKb,aAAa,CAAC;oBAAE,GAAGD,UAAU;oBAAEF,KAAK,EAAEgB,CAAC,CAACwE,MAAM,CAACjE;kBAAM,CAAC,CAAE;kBACzEkE,MAAM,EAAC,QAAQ;kBACfzB,OAAO,EAAC,UAAU;kBAClB0B,YAAY,EAAC,OAAO;kBACpBhE,EAAE,EAAE;oBACF,0BAA0B,EAAE;sBAC1BS,YAAY,EAAE;oBAChB;kBACF,CAAE;kBACFwD,UAAU,EAAE;oBACVC,cAAc,eAAEzG,OAAA,CAACZ,SAAS;sBAACmD,EAAE,EAAE;wBAAEuD,EAAE,EAAE,CAAC;wBAAEnB,KAAK,EAAE;sBAAiB;oBAAE;sBAAAlC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBACtE;gBAAE;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACF5C,OAAA,CAAC9C,SAAS;kBACR4F,SAAS;kBACT6C,KAAK,EAAC,UAAU;kBAChBS,IAAI,EAAE3F,YAAY,GAAG,MAAM,GAAG,UAAW;kBACzC2B,KAAK,EAAErB,UAAU,CAACD,QAAS;kBAC3BwE,QAAQ,EAAGzD,CAAC,IAAKb,aAAa,CAAC;oBAAE,GAAGD,UAAU;oBAAED,QAAQ,EAAEe,CAAC,CAACwE,MAAM,CAACjE;kBAAM,CAAC,CAAE;kBAC5EkE,MAAM,EAAC,QAAQ;kBACfzB,OAAO,EAAC,UAAU;kBAClB0B,YAAY,EAAC,cAAc;kBAC3BhE,EAAE,EAAE;oBACF,0BAA0B,EAAE;sBAC1BS,YAAY,EAAE;oBAChB;kBACF,CAAE;kBACFwD,UAAU,EAAE;oBACVC,cAAc,eAAEzG,OAAA,CAACV,QAAQ;sBAACiD,EAAE,EAAE;wBAAEuD,EAAE,EAAE,CAAC;wBAAEnB,KAAK,EAAE;sBAAiB;oBAAE;sBAAAlC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC;oBACpE8D,YAAY,eACV1G,OAAA,CAAClC,UAAU;sBAACoH,OAAO,EAAEA,CAAA,KAAMxE,eAAe,CAAC,CAACD,YAAY,CAAE;sBAAA0B,QAAA,EACvD1B,YAAY,gBAAGT,OAAA,CAACN,iBAAiB;wBAAA+C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,gBAAG5C,OAAA,CAACR,cAAc;wBAAAiD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChD;kBAEhB;gBAAE;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACF5C,OAAA,CAAC9C,SAAS;kBACR4F,SAAS;kBACT6C,KAAK,EAAC,kBAAkB;kBACxBS,IAAI,EAAC,UAAU;kBACfhE,KAAK,EAAErB,UAAU,CAACG,eAAgB;kBAClCoE,QAAQ,EAAGzD,CAAC,IAAKb,aAAa,CAAC;oBAAE,GAAGD,UAAU;oBAAEG,eAAe,EAAEW,CAAC,CAACwE,MAAM,CAACjE;kBAAM,CAAC,CAAE;kBACnFkE,MAAM,EAAC,QAAQ;kBACfzB,OAAO,EAAC,UAAU;kBAClB0B,YAAY,EAAC,cAAc;kBAC3BhE,EAAE,EAAE;oBACF,0BAA0B,EAAE;sBAC1BS,YAAY,EAAE;oBAChB;kBACF,CAAE;kBACFwD,UAAU,EAAE;oBACVC,cAAc,eAAEzG,OAAA,CAACV,QAAQ;sBAACiD,EAAE,EAAE;wBAAEuD,EAAE,EAAE,CAAC;wBAAEnB,KAAK,EAAE;sBAAiB;oBAAE;sBAAAlC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBACrE;gBAAE;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACF5C,OAAA,CAACnD,MAAM;kBACLuJ,IAAI,EAAC,QAAQ;kBACbtD,SAAS;kBACT+B,OAAO,EAAC,WAAW;kBACnBtC,EAAE,EAAE;oBAAEoE,EAAE,EAAE,CAAC;oBAAEd,EAAE,EAAE,CAAC;oBAAE7C,YAAY,EAAE;kBAAE,CAAE;kBAAAb,QAAA,EACvC;gBAED;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CACP;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,eAGX5C,OAAA,CAACkC,QAAQ;UAACE,KAAK,EAAE7B,SAAU;UAAC8B,KAAK,EAAE,CAAE;UAAAF,QAAA,eACnCnC,OAAA,CAAClD,GAAG;YAAC+F,QAAQ,EAAE,GAAI;YAACkD,EAAE,EAAC,MAAM;YAAA5D,QAAA,gBAC3BnC,OAAA,CAACjD,UAAU;cAAC8H,OAAO,EAAC,IAAI;cAACmB,YAAY;cAAClB,UAAU,EAAC,MAAM;cAACvC,EAAE,EAAE;gBAAEsD,EAAE,EAAE,CAAC;gBAAEI,SAAS,EAAE;cAAS,CAAE;cAAA9D,QAAA,EAAC;YAE5F;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAEb5C,OAAA,CAAChC,IAAI;cAAC4I,SAAS;cAACC,OAAO,EAAE,CAAE;cAAA1E,QAAA,gBAEzBnC,OAAA,CAAChC,IAAI;gBAAC8I,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAACC,EAAE,EAAE,CAAE;gBAAA7E,QAAA,eACvBnC,OAAA,CAAC3C,IAAI;kBAACkF,EAAE,EAAE;oBACRwB,MAAM,EAAE,MAAM;oBACdb,UAAU,EAAGC,KAAK,IAAKA,KAAK,CAACC,OAAO,CAACC,IAAI,KAAK,MAAM,GAChD,oFAAoF,GACpF,sFAAsF;oBAC1FE,MAAM,EAAE;kBACV,CAAE;kBAAApB,QAAA,eACAnC,OAAA,CAAC1C,WAAW;oBAACiF,EAAE,EAAE;sBAAE4B,CAAC,EAAE;oBAAE,CAAE;oBAAAhC,QAAA,gBACxBnC,OAAA,CAAClD,GAAG;sBAACsH,OAAO,EAAC,MAAM;sBAACC,UAAU,EAAC,QAAQ;sBAACI,GAAG,EAAE,CAAE;sBAACoB,EAAE,EAAE,CAAE;sBAAA1D,QAAA,gBACpDnC,OAAA,CAACxB,YAAY;wBAACmG,KAAK,EAAC,SAAS;wBAACpC,EAAE,EAAE;0BAAEqC,QAAQ,EAAE;wBAAG;sBAAE;wBAAAnC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eACtD5C,OAAA,CAAClD,GAAG;wBAAAqF,QAAA,gBACFnC,OAAA,CAACjD,UAAU;0BAAC8H,OAAO,EAAC,IAAI;0BAACC,UAAU,EAAC,KAAK;0BAAA3C,QAAA,EAAC;wBAE1C;0BAAAM,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAY,CAAC,eACb5C,OAAA,CAACjD,UAAU;0BAAC8H,OAAO,EAAC,OAAO;0BAACF,KAAK,EAAC,gBAAgB;0BAAAxC,QAAA,EAAC;wBAEnD;0BAAAM,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAY,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACV,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACN5C,OAAA,CAAClD,GAAG;sBAACsH,OAAO,EAAC,MAAM;sBAAC6C,aAAa,EAAC,QAAQ;sBAACxC,GAAG,EAAE,CAAE;sBAAAtC,QAAA,EAC/C,CACC;wBAAE+E,IAAI,EAAE,IAAI;wBAAEvB,KAAK,EAAE,cAAc;wBAAE1E,IAAI,EAAE;sBAAU,CAAC,EACtD;wBAAEiG,IAAI,EAAE,IAAI;wBAAEvB,KAAK,EAAE,cAAc;wBAAE1E,IAAI,EAAE;sBAAS,CAAC,EACrD;wBAAEiG,IAAI,EAAE,IAAI;wBAAEvB,KAAK,EAAE,eAAe;wBAAE1E,IAAI,EAAE;sBAAS,CAAC,EACtD;wBAAEiG,IAAI,EAAE,IAAI;wBAAEvB,KAAK,EAAE,cAAc;wBAAE1E,IAAI,EAAE;sBAAU,CAAC,EACtD;wBAAEiG,IAAI,EAAE,IAAI;wBAAEvB,KAAK,EAAE,cAAc;wBAAE1E,IAAI,EAAE;sBAAS,CAAC,CACtD,CAACkG,GAAG,CAAEC,IAAI,iBACTpH,OAAA,CAACnD,MAAM;wBAELgI,OAAO,EAAEvD,QAAQ,KAAK8F,IAAI,CAACF,IAAI,GAAG,WAAW,GAAG,UAAW;wBAC3DhC,OAAO,EAAEA,CAAA,KAAM1D,WAAW,CAAC4F,IAAI,CAACF,IAAI,CAAE;wBACtCpE,SAAS;wBACTP,EAAE,EAAE;0BACFS,YAAY,EAAE,CAAC;0BACfsB,cAAc,EAAE,YAAY;0BAC5B9B,EAAE,EAAE,GAAG;0BACPU,UAAU,EAAE5B,QAAQ,KAAK8F,IAAI,CAACF,IAAI,GAC9B,mDAAmD,GACnD,aAAa;0BACjB,SAAS,EAAE;4BACThE,UAAU,EAAE5B,QAAQ,KAAK8F,IAAI,CAACF,IAAI,GAC9B,mDAAmD,GACnD;0BACN;wBACF,CAAE;wBAAA/E,QAAA,EAEDiF,IAAI,CAACzB;sBAAK,GAlBNyB,IAAI,CAACF,IAAI;wBAAAzE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAmBR,CACT;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACK;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGP5C,OAAA,CAAChC,IAAI;gBAAC8I,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAACC,EAAE,EAAE,CAAE;gBAAA7E,QAAA,eACvBnC,OAAA,CAAC3C,IAAI;kBAACkF,EAAE,EAAE;oBACRwB,MAAM,EAAE,MAAM;oBACdb,UAAU,EAAGC,KAAK,IAAKA,KAAK,CAACC,OAAO,CAACC,IAAI,KAAK,MAAM,GAChD,kFAAkF,GAClF,oFAAoF;oBACxFE,MAAM,EAAE;kBACV,CAAE;kBAAApB,QAAA,eACAnC,OAAA,CAAC1C,WAAW;oBAACiF,EAAE,EAAE;sBAAE4B,CAAC,EAAE;oBAAE,CAAE;oBAAAhC,QAAA,gBACxBnC,OAAA,CAAClD,GAAG;sBAACsH,OAAO,EAAC,MAAM;sBAACC,UAAU,EAAC,QAAQ;sBAACI,GAAG,EAAE,CAAE;sBAACoB,EAAE,EAAE,CAAE;sBAAA1D,QAAA,gBACpDnC,OAAA,CAACtB,WAAW;wBAACiG,KAAK,EAAC,OAAO;wBAACpC,EAAE,EAAE;0BAAEqC,QAAQ,EAAE;wBAAG;sBAAE;wBAAAnC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eACnD5C,OAAA,CAAClD,GAAG;wBAAAqF,QAAA,gBACFnC,OAAA,CAACjD,UAAU;0BAAC8H,OAAO,EAAC,IAAI;0BAACC,UAAU,EAAC,KAAK;0BAAA3C,QAAA,EAAC;wBAE1C;0BAAAM,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAY,CAAC,eACb5C,OAAA,CAACjD,UAAU;0BAAC8H,OAAO,EAAC,OAAO;0BAACF,KAAK,EAAC,gBAAgB;0BAAAxC,QAAA,EAAC;wBAEnD;0BAAAM,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAY,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACV,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACN5C,OAAA,CAAClD,GAAG;sBAACsH,OAAO,EAAC,MAAM;sBAAC6C,aAAa,EAAC,QAAQ;sBAACxC,GAAG,EAAE,CAAE;sBAAAtC,QAAA,EAC/C,CACC;wBAAEkF,IAAI,EAAE,OAAO;wBAAE1B,KAAK,EAAE,OAAO;wBAAE2B,WAAW,EAAE;sBAAoB,CAAC,EACnE;wBAAED,IAAI,EAAE,QAAQ;wBAAE1B,KAAK,EAAE,kBAAkB;wBAAE2B,WAAW,EAAE;sBAAqB,CAAC,EAChF;wBAAED,IAAI,EAAE,OAAO;wBAAE1B,KAAK,EAAE,OAAO;wBAAE2B,WAAW,EAAE;sBAAuB,CAAC,CACvE,CAACH,GAAG,CAAEI,UAAU,iBACfvH,OAAA,CAACnD,MAAM;wBAELgI,OAAO,EAAE0C,UAAU,CAACF,IAAI,KAAK,QAAQ,GAAG,WAAW,GAAG,UAAW;wBACjEvE,SAAS;wBACTP,EAAE,EAAE;0BACFS,YAAY,EAAE,CAAC;0BACfsB,cAAc,EAAE,YAAY;0BAC5B9B,EAAE,EAAE,GAAG;0BACPyE,aAAa,EAAE,QAAQ;0BACvB5C,UAAU,EAAE,YAAY;0BACxBnB,UAAU,EAAEqE,UAAU,CAACF,IAAI,KAAK,QAAQ,GACpC,mDAAmD,GACnD,aAAa;0BACjB,SAAS,EAAE;4BACTnE,UAAU,EAAEqE,UAAU,CAACF,IAAI,KAAK,QAAQ,GACpC,mDAAmD,GACnD;0BACN;wBACF,CAAE;wBAAAlF,QAAA,gBAEFnC,OAAA,CAACjD,UAAU;0BAAC8H,OAAO,EAAC,WAAW;0BAACC,UAAU,EAAC,KAAK;0BAAA3C,QAAA,EAC7CoF,UAAU,CAAC5B;wBAAK;0BAAAlD,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACP,CAAC,eACb5C,OAAA,CAACjD,UAAU;0BAAC8H,OAAO,EAAC,SAAS;0BAACF,KAAK,EAAC,gBAAgB;0BAAAxC,QAAA,EACjDoF,UAAU,CAACD;wBAAW;0BAAA7E,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACb,CAAC;sBAAA,GAxBR2E,UAAU,CAACF,IAAI;wBAAA5E,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAyBd,CACT;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACK;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGX5C,OAAA,CAACkC,QAAQ;UAACE,KAAK,EAAE7B,SAAU;UAAC8B,KAAK,EAAE,CAAE;UAAAF,QAAA,eACnCnC,OAAA,CAAClD,GAAG;YAAC+F,QAAQ,EAAE,GAAI;YAACkD,EAAE,EAAC,MAAM;YAACE,SAAS,EAAC,QAAQ;YAAA9D,QAAA,gBAC9CnC,OAAA,CAAClD,GAAG;cACFyF,EAAE,EAAE;gBACFW,UAAU,EAAE,mDAAmD;gBAC/DF,YAAY,EAAE,KAAK;gBACnB0B,KAAK,EAAE,EAAE;gBACTX,MAAM,EAAE,EAAE;gBACVK,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,QAAQ;gBACpBC,cAAc,EAAE,QAAQ;gBACxByB,EAAE,EAAE,MAAM;gBACVF,EAAE,EAAE;cACN,CAAE;cAAA1D,QAAA,eAEFnC,OAAA,CAAC1B,YAAY;gBAACiE,EAAE,EAAE;kBAAEoC,KAAK,EAAE,OAAO;kBAAEC,QAAQ,EAAE;gBAAG;cAAE;gBAAAnC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnD,CAAC,eAEN5C,OAAA,CAACjD,UAAU;cAAC8H,OAAO,EAAC,IAAI;cAACmB,YAAY;cAAClB,UAAU,EAAC,MAAM;cAAA3C,QAAA,EAAC;YAExD;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAEb5C,OAAA,CAACjD,UAAU;cAAC8H,OAAO,EAAC,IAAI;cAACF,KAAK,EAAC,gBAAgB;cAACqB,YAAY;cAAA7D,QAAA,EAAC;YAE7D;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAEb5C,OAAA,CAACjD,UAAU;cAAC8H,OAAO,EAAC,OAAO;cAACtC,EAAE,EAAE;gBAAEsD,EAAE,EAAE,CAAC;gBAAE2B,UAAU,EAAE;cAAI,CAAE;cAAArF,QAAA,EAAC;YAI5D;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAEb5C,OAAA,CAAC3C,IAAI;cAACkF,EAAE,EAAE;gBAAEsD,EAAE,EAAE;cAAE,CAAE;cAAA1D,QAAA,eAClBnC,OAAA,CAAC1C,WAAW;gBAAA6E,QAAA,gBACVnC,OAAA,CAACjD,UAAU;kBAAC8H,OAAO,EAAC,IAAI;kBAACmB,YAAY;kBAAA7D,QAAA,EAAC;gBAEtC;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACb5C,OAAA,CAACxC,IAAI;kBAAA2E,QAAA,gBACHnC,OAAA,CAACvC,QAAQ;oBAAA0E,QAAA,gBACPnC,OAAA,CAACtC,YAAY;sBAAAyE,QAAA,eACXnC,OAAA,CAAChB,SAAS;wBAAC2F,KAAK,EAAC;sBAAS;wBAAAlC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjB,CAAC,eACf5C,OAAA,CAACrC,YAAY;sBAAC8J,OAAO,EAAC;oBAAgC;sBAAAhF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjD,CAAC,eACX5C,OAAA,CAACvC,QAAQ;oBAAA0E,QAAA,gBACPnC,OAAA,CAACtC,YAAY;sBAAAyE,QAAA,eACXnC,OAAA,CAAChB,SAAS;wBAAC2F,KAAK,EAAC;sBAAS;wBAAAlC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjB,CAAC,eACf5C,OAAA,CAACrC,YAAY;sBAAC8J,OAAO,EAAC;oBAAgC;sBAAAhF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjD,CAAC,eACX5C,OAAA,CAACvC,QAAQ;oBAAA0E,QAAA,gBACPnC,OAAA,CAACtC,YAAY;sBAAAyE,QAAA,eACXnC,OAAA,CAAChB,SAAS;wBAAC2F,KAAK,EAAC;sBAAS;wBAAAlC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjB,CAAC,eACf5C,OAAA,CAACrC,YAAY;sBAAC8J,OAAO,EAAC;oBAA4B;sBAAAhF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7C,CAAC,eACX5C,OAAA,CAACvC,QAAQ;oBAAA0E,QAAA,gBACPnC,OAAA,CAACtC,YAAY;sBAAAyE,QAAA,eACXnC,OAAA,CAAChB,SAAS;wBAAC2F,KAAK,EAAC;sBAAS;wBAAAlC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjB,CAAC,eACf5C,OAAA,CAACrC,YAAY;sBAAC8J,OAAO,EAAC;oBAAgC;sBAAAhF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAEP5C,OAAA,CAACjD,UAAU;cAAC8H,OAAO,EAAC,OAAO;cAACF,KAAK,EAAC,gBAAgB;cAAAxC,QAAA,GAAC,mDAEjD,eAAAnC,OAAA;gBAAAyC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,gDAER;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACO,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEb,CAAC;EAAA,QAvjB6D9C,OAAO;AAAA,EAujBpE,CAAC;EAAA,QAvjB4DA,OAAO;AAAA,EAujBnE;AAAC4H,GAAA,GAnkBGzH,cAAc;AAqkBpBA,cAAc,CAAC0H,WAAW,GAAG,gBAAgB;AAE7C,eAAe1H,cAAc;AAAC,IAAAG,EAAA,EAAAsH,GAAA;AAAAE,YAAA,CAAAxH,EAAA;AAAAwH,YAAA,CAAAF,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}