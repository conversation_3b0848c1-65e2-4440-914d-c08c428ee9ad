"use client";

import createSvgIcon from "./utils/createSvgIcon.js";
import { jsx as _jsx } from "react/jsx-runtime";
export default createSvgIcon([/*#__PURE__*/_jsx("path", {
  fillOpacity: ".3",
  d: "M17 14.76V8h5.92C19.97 5.51 16.16 4 12 4 7.31 4 3.07 5.9 0 8.98l6.35 6.36C7.8 13.89 9.79 13 12 13c1.89 0 3.63.66 5 1.76"
}, "0"), /*#__PURE__*/_jsx("path", {
  d: "M6.35 15.34 12 21l5-5.01v-1.23c-1.37-1.1-3.11-1.76-5-1.76-2.21 0-4.2.89-5.65 2.34M19 18h2v2h-2zm0-8h2v6h-2z"
}, "1")], 'SignalWifiStatusbarConnectedNoInternet1TwoTone');