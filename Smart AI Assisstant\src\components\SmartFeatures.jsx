import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  Avatar,
  Chip,
  LinearProgress,
  IconButton,
  Tooltip,
  Badge,
} from '@mui/material';
import {
  Psychology as AIIcon,
  Speed as SpeedIcon,
  Security as SecurityIcon,
  Analytics as AnalyticsIcon,
  AutoAwesome as AutoIcon,
  Shield as ShieldIcon,
  Visibility as VisibilityIcon,
  TrendingUp as TrendingIcon,
} from '@mui/icons-material';

const SmartFeatures = React.memo(() => {
  const [realTimeStats, setRealTimeStats] = useState({
    threatsBlocked: 1247,
    scansCompleted: 8934,
    aiAccuracy: 99.7,
    responseTime: 0.3,
  });

  // Simulate real-time updates
  useEffect(() => {
    const interval = setInterval(() => {
      setRealTimeStats(prev => ({
        threatsBlocked: prev.threatsBlocked + Math.floor(Math.random() * 3),
        scansCompleted: prev.scansCompleted + Math.floor(Math.random() * 5),
        aiAccuracy: Math.min(99.9, prev.aiAccuracy + (Math.random() - 0.5) * 0.1),
        responseTime: Math.max(0.1, prev.responseTime + (Math.random() - 0.5) * 0.1),
      }));
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  const smartFeatures = [
    {
      title: 'AI Threat Prediction',
      description: 'Advanced machine learning algorithms predict threats before they strike',
      icon: <AIIcon />,
      gradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      value: `${realTimeStats.aiAccuracy.toFixed(1)}%`,
      label: 'Accuracy',
    },
    {
      title: 'Real-Time Protection',
      description: 'Continuous monitoring and instant threat response',
      icon: <ShieldIcon />,
      gradient: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
      value: `${realTimeStats.responseTime.toFixed(1)}s`,
      label: 'Response Time',
    },
    {
      title: 'Smart Analytics',
      description: 'Intelligent insights and predictive security analytics',
      icon: <AnalyticsIcon />,
      gradient: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
      value: realTimeStats.scansCompleted.toLocaleString(),
      label: 'Scans Completed',
    },
    {
      title: 'Auto Defense',
      description: 'Automated threat mitigation and security responses',
      icon: <AutoIcon />,
      gradient: 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)',
      value: realTimeStats.threatsBlocked.toLocaleString(),
      label: 'Threats Blocked',
    },
  ];

  return (
    <Box>
      <Typography 
        variant="h4" 
        textAlign="center" 
        mb={4}
        sx={{
          background: 'linear-gradient(45deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%)',
          backgroundSize: '400% 400%',
          backgroundClip: 'text',
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent',
          animation: 'gradientText 6s ease infinite',
          fontWeight: 'bold',
          '@keyframes gradientText': {
            '0%': { backgroundPosition: '0% 50%' },
            '50%': { backgroundPosition: '100% 50%' },
            '100%': { backgroundPosition: '0% 50%' },
          },
        }}
      >
        Smart Security Features
      </Typography>

      <Grid container spacing={3}>
        {smartFeatures.map((feature, index) => (
          <Grid item xs={12} sm={6} md={3} key={index}>
            <Card
              sx={{
                height: '100%',
                background: 'linear-gradient(135deg, rgba(12, 12, 12, 0.95) 0%, rgba(26, 26, 26, 0.95) 100%)',
                backdropFilter: 'blur(25px)',
                border: '1px solid rgba(255, 255, 255, 0.1)',
                borderRadius: 4,
                position: 'relative',
                overflow: 'hidden',
                transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                '&:hover': {
                  transform: 'translateY(-8px) scale(1.02)',
                  boxShadow: '0 20px 60px rgba(102, 126, 234, 0.3)',
                  border: '1px solid rgba(102, 126, 234, 0.4)',
                },
                '&::before': {
                  content: '""',
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  right: 0,
                  height: '3px',
                  background: feature.gradient,
                  backgroundSize: '200% 200%',
                  animation: 'gradientShift 4s ease infinite',
                },
                '@keyframes gradientShift': {
                  '0%': { backgroundPosition: '0% 50%' },
                  '50%': { backgroundPosition: '100% 50%' },
                  '100%': { backgroundPosition: '0% 50%' },
                },
              }}
            >
              <CardContent sx={{ p: 3, textAlign: 'center' }}>
                <Avatar
                  sx={{
                    background: feature.gradient,
                    width: 56,
                    height: 56,
                    mx: 'auto',
                    mb: 2,
                    boxShadow: '0 8px 25px rgba(102, 126, 234, 0.3)',
                  }}
                >
                  {React.cloneElement(feature.icon, { sx: { fontSize: 28 } })}
                </Avatar>

                <Typography variant="h6" fontWeight="bold" color="white" gutterBottom>
                  {feature.title}
                </Typography>

                <Typography 
                  variant="body2" 
                  color="rgba(255, 255, 255, 0.7)" 
                  mb={3}
                  sx={{ minHeight: 40 }}
                >
                  {feature.description}
                </Typography>

                <Box
                  sx={{
                    background: 'rgba(255, 255, 255, 0.05)',
                    borderRadius: 2,
                    p: 2,
                    border: '1px solid rgba(255, 255, 255, 0.1)',
                  }}
                >
                  <Typography 
                    variant="h4" 
                    fontWeight="bold"
                    sx={{
                      background: feature.gradient,
                      backgroundClip: 'text',
                      WebkitBackgroundClip: 'text',
                      WebkitTextFillColor: 'transparent',
                    }}
                  >
                    {feature.value}
                  </Typography>
                  <Typography variant="caption" color="rgba(255, 255, 255, 0.6)">
                    {feature.label}
                  </Typography>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>
    </Box>
  );
});

SmartFeatures.displayName = 'SmartFeatures';

export default SmartFeatures;
