{"name": "raw-body", "description": "Get and validate the raw body of a readable stream.", "version": "2.5.2", "author": "<PERSON> <<EMAIL>> (http://jongleberry.com)", "contributors": ["<PERSON> <<EMAIL>>", "Raynos <<EMAIL>>"], "license": "MIT", "repository": "stream-utils/raw-body", "dependencies": {"bytes": "3.1.2", "http-errors": "2.0.0", "iconv-lite": "0.4.24", "unpipe": "1.0.0"}, "devDependencies": {"bluebird": "3.7.2", "eslint": "8.34.0", "eslint-config-standard": "15.0.1", "eslint-plugin-import": "2.27.5", "eslint-plugin-markdown": "3.0.0", "eslint-plugin-node": "11.1.0", "eslint-plugin-promise": "6.1.1", "eslint-plugin-standard": "4.1.0", "mocha": "10.2.0", "nyc": "15.1.0", "readable-stream": "2.3.7", "safe-buffer": "5.2.1"}, "engines": {"node": ">= 0.8"}, "files": ["HISTORY.md", "LICENSE", "README.md", "SECURITY.md", "index.d.ts", "index.js"], "scripts": {"lint": "eslint .", "test": "mocha --trace-deprecation --reporter spec --bail --check-leaks test/", "test-ci": "nyc --reporter=lcovonly --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test"}}