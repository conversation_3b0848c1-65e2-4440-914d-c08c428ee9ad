{"ast": null, "code": "var _jsxFileName = \"E:\\\\PythonProjects\\\\React.js\\\\Smart AI Assisstant\\\\src\\\\components\\\\Footer.jsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Box, Container, Grid, Typography, Link, IconButton, Divider, Stack, Chip } from '@mui/material';\nimport { Security as SecurityIcon, Email as EmailIcon, Phone as PhoneIcon, LocationOn as LocationIcon, GitHub as GitHubIcon, LinkedIn as LinkedInIcon, Twitter as TwitterIcon, Facebook as FacebookIcon, Shield as ShieldIcon, Verified as VerifiedIcon, Copyright as CopyrightIcon, Chat as DiscordIcon } from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Footer = /*#__PURE__*/_s(/*#__PURE__*/React.memo(_c = _s(() => {\n  _s();\n  const navigate = useNavigate();\n  const currentYear = new Date().getFullYear();\n  const footerSections = [{\n    title: 'About Us',\n    links: [{\n      label: 'Our Mission',\n      path: '/about'\n    }, {\n      label: 'Security Team',\n      path: '/about'\n    }, {\n      label: 'Careers',\n      path: '/about'\n    }, {\n      label: 'Press Kit',\n      path: '/about'\n    }]\n  }, {\n    title: 'Contact',\n    links: [{\n      label: 'Support Center',\n      path: '/about'\n    }, {\n      label: 'Enterprise Sales',\n      path: '/about'\n    }, {\n      label: 'Partnership',\n      path: '/about'\n    }, {\n      label: 'Bug Reports',\n      path: '/about'\n    }]\n  }, {\n    title: 'Legal/Privacy',\n    links: [{\n      label: 'Privacy Policy',\n      path: '/about'\n    }, {\n      label: 'Terms of Service',\n      path: '/about'\n    }, {\n      label: 'Security Policy',\n      path: '/about'\n    }, {\n      label: 'GDPR Compliance',\n      path: '/about'\n    }]\n  }];\n  const socialLinks = [{\n    icon: /*#__PURE__*/_jsxDEV(GitHubIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 65,\n      columnNumber: 13\n    }, this),\n    label: 'GitHub',\n    url: 'https://github.com',\n    color: '#667eea'\n  }, {\n    icon: /*#__PURE__*/_jsxDEV(LinkedInIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 71,\n      columnNumber: 13\n    }, this),\n    label: 'LinkedIn',\n    url: 'https://linkedin.com',\n    color: '#0077B5'\n  }, {\n    icon: /*#__PURE__*/_jsxDEV(TwitterIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 77,\n      columnNumber: 13\n    }, this),\n    label: 'Twitter',\n    url: 'https://twitter.com',\n    color: '#1DA1F2'\n  }, {\n    icon: /*#__PURE__*/_jsxDEV(DiscordIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 83,\n      columnNumber: 13\n    }, this),\n    label: 'Discord',\n    url: 'https://discord.com',\n    color: '#7289DA'\n  }];\n  const handleLinkClick = path => {\n    navigate(path);\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    component: \"footer\",\n    sx: {\n      background: theme => theme.palette.mode === 'dark' ? 'linear-gradient(135deg, rgba(12, 12, 12, 0.98) 0%, rgba(26, 26, 26, 0.98) 50%, rgba(45, 45, 45, 0.98) 100%)' : 'linear-gradient(135deg, rgba(26, 32, 44, 0.98) 0%, rgba(45, 55, 72, 0.98) 50%, rgba(74, 85, 104, 0.98) 100%)',\n      backdropFilter: 'blur(25px) saturate(180%)',\n      color: 'white',\n      mt: 'auto',\n      py: 8,\n      position: 'relative',\n      overflow: 'hidden',\n      '&::before': {\n        content: '\"\"',\n        position: 'absolute',\n        top: 0,\n        left: 0,\n        right: 0,\n        height: '4px',\n        background: 'linear-gradient(90deg, #667eea 0%, #764ba2 20%, #f093fb 40%, #f5576c 60%, #4facfe 80%, #00f2fe 100%)',\n        backgroundSize: '400% 400%',\n        animation: 'footerGradient 12s ease infinite'\n      },\n      '@keyframes footerGradient': {\n        '0%': {\n          backgroundPosition: '0% 50%'\n        },\n        '50%': {\n          backgroundPosition: '100% 50%'\n        },\n        '100%': {\n          backgroundPosition: '0% 50%'\n        }\n      }\n    },\n    children: /*#__PURE__*/_jsxDEV(Container, {\n      maxWidth: \"xl\",\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 6,\n        sx: {\n          mb: 6\n        },\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          lg: 4,\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              mb: 4\n            },\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              display: \"flex\",\n              alignItems: \"center\",\n              gap: 2,\n              mb: 3,\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                  borderRadius: '50%',\n                  width: 50,\n                  height: 50,\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  boxShadow: '0 8px 25px rgba(102, 126, 234, 0.3)'\n                },\n                children: /*#__PURE__*/_jsxDEV(SecurityIcon, {\n                  sx: {\n                    fontSize: 28,\n                    color: 'white'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 144,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 132,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h4\",\n                fontWeight: \"bold\",\n                children: \"AI Security Guard\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 146,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 131,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              sx: {\n                mb: 4,\n                opacity: 0.9,\n                lineHeight: 1.8,\n                fontSize: '1.1rem'\n              },\n              children: \"Advanced AI-powered security scanning platform providing comprehensive threat detection and analysis for URLs and files. Protecting your digital assets with cutting-edge technology.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 150,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Stack, {\n              direction: \"row\",\n              spacing: 1,\n              flexWrap: \"wrap\",\n              gap: 1,\n              children: [/*#__PURE__*/_jsxDEV(Chip, {\n                icon: /*#__PURE__*/_jsxDEV(ShieldIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 167,\n                  columnNumber: 25\n                }, this),\n                label: \"SOC 2 Compliant\",\n                size: \"small\",\n                sx: {\n                  bgcolor: 'rgba(76, 175, 80, 0.2)',\n                  color: '#4caf50',\n                  border: '1px solid rgba(76, 175, 80, 0.3)'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 166,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                icon: /*#__PURE__*/_jsxDEV(VerifiedIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 177,\n                  columnNumber: 25\n                }, this),\n                label: \"ISO 27001\",\n                size: \"small\",\n                sx: {\n                  bgcolor: 'rgba(33, 150, 243, 0.2)',\n                  color: '#2196f3',\n                  border: '1px solid rgba(33, 150, 243, 0.3)'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 176,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 165,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 130,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              gutterBottom: true,\n              fontWeight: \"600\",\n              children: \"Contact Information\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 191,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Stack, {\n              spacing: 1,\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                display: \"flex\",\n                alignItems: \"center\",\n                gap: 1,\n                children: [/*#__PURE__*/_jsxDEV(EmailIcon, {\n                  sx: {\n                    fontSize: 18,\n                    opacity: 0.7\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 196,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    opacity: 0.9\n                  },\n                  children: \"<EMAIL>\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 197,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 195,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                display: \"flex\",\n                alignItems: \"center\",\n                gap: 1,\n                children: [/*#__PURE__*/_jsxDEV(PhoneIcon, {\n                  sx: {\n                    fontSize: 18,\n                    opacity: 0.7\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 202,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    opacity: 0.9\n                  },\n                  children: \"+****************\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 203,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 201,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                display: \"flex\",\n                alignItems: \"center\",\n                gap: 1,\n                children: [/*#__PURE__*/_jsxDEV(LocationIcon, {\n                  sx: {\n                    fontSize: 18,\n                    opacity: 0.7\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 208,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    opacity: 0.9\n                  },\n                  children: \"San Francisco, CA, USA\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 209,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 207,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 194,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 190,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 129,\n          columnNumber: 11\n        }, this), footerSections.map((section, index) => /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 6,\n          sm: 3,\n          md: 2,\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            gutterBottom: true,\n            fontWeight: \"600\",\n            sx: {\n              mb: 2\n            },\n            children: section.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 220,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Stack, {\n            spacing: 1,\n            children: section.links.map((link, linkIndex) => /*#__PURE__*/_jsxDEV(Link, {\n              component: \"button\",\n              variant: \"body2\",\n              onClick: () => handleLinkClick(link.path),\n              sx: {\n                color: 'rgba(255, 255, 255, 0.8)',\n                textDecoration: 'none',\n                textAlign: 'left',\n                background: 'none',\n                border: 'none',\n                cursor: 'pointer',\n                padding: 0,\n                transition: 'color 0.2s ease',\n                '&:hover': {\n                  color: '#64b5f6',\n                  textDecoration: 'underline'\n                }\n              },\n              children: link.label\n            }, linkIndex, false, {\n              fileName: _jsxFileName,\n              lineNumber: 225,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 223,\n            columnNumber: 15\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 219,\n          columnNumber: 13\n        }, this))]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 127,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Divider, {\n        sx: {\n          bgcolor: 'rgba(255, 255, 255, 0.1)',\n          mb: 3\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 253,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        display: \"flex\",\n        justifyContent: \"space-between\",\n        alignItems: \"center\",\n        flexWrap: \"wrap\",\n        gap: 2,\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          display: \"flex\",\n          alignItems: \"center\",\n          gap: 1,\n          children: [/*#__PURE__*/_jsxDEV(CopyrightIcon, {\n            sx: {\n              fontSize: 16,\n              opacity: 0.7\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 265,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            sx: {\n              opacity: 0.8\n            },\n            children: [currentYear, \" AI Security Guard. All rights reserved.\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 266,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 264,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          display: \"flex\",\n          gap: 3,\n          flexWrap: \"wrap\",\n          children: [/*#__PURE__*/_jsxDEV(Link, {\n            component: \"button\",\n            variant: \"body2\",\n            onClick: () => handleLinkClick('/about'),\n            sx: {\n              color: 'rgba(255, 255, 255, 0.8)',\n              textDecoration: 'none',\n              background: 'none',\n              border: 'none',\n              cursor: 'pointer',\n              '&:hover': {\n                color: '#64b5f6'\n              }\n            },\n            children: \"Privacy Policy\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 273,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            component: \"button\",\n            variant: \"body2\",\n            onClick: () => handleLinkClick('/about'),\n            sx: {\n              color: 'rgba(255, 255, 255, 0.8)',\n              textDecoration: 'none',\n              background: 'none',\n              border: 'none',\n              cursor: 'pointer',\n              '&:hover': {\n                color: '#64b5f6'\n              }\n            },\n            children: \"Terms of Service\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 288,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            component: \"button\",\n            variant: \"body2\",\n            onClick: () => handleLinkClick('/about'),\n            sx: {\n              color: 'rgba(255, 255, 255, 0.8)',\n              textDecoration: 'none',\n              background: 'none',\n              border: 'none',\n              cursor: 'pointer',\n              '&:hover': {\n                color: '#64b5f6'\n              }\n            },\n            children: \"Cookie Policy\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 303,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 272,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          display: \"flex\",\n          gap: 1,\n          children: socialLinks.map((social, index) => /*#__PURE__*/_jsxDEV(IconButton, {\n            href: social.url,\n            target: \"_blank\",\n            rel: \"noopener noreferrer\",\n            sx: {\n              color: 'rgba(255, 255, 255, 0.7)',\n              background: 'rgba(255, 255, 255, 0.05)',\n              border: '1px solid rgba(255, 255, 255, 0.1)',\n              borderRadius: 2,\n              width: 48,\n              height: 48,\n              transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',\n              '&:hover': {\n                color: social.color,\n                background: `linear-gradient(135deg, ${social.color}15, ${social.color}25)`,\n                border: `1px solid ${social.color}40`,\n                transform: 'translateY(-4px) scale(1.1)',\n                boxShadow: `0 8px 25px ${social.color}30`\n              }\n            },\n            \"aria-label\": social.label,\n            children: social.icon\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 323,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 321,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 256,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        display: \"flex\",\n        justifyContent: \"center\",\n        alignItems: \"center\",\n        gap: 2,\n        mt: 3,\n        pt: 3,\n        sx: {\n          borderTop: '1px solid rgba(255, 255, 255, 0.1)'\n        },\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"caption\",\n          sx: {\n            opacity: 0.6,\n            textAlign: 'center'\n          },\n          children: \"Trusted by 10,000+ organizations worldwide \\u2022 99.9% uptime \\u2022 24/7 security monitoring\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 362,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 353,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 125,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 95,\n    columnNumber: 5\n  }, this);\n}, \"CzcTeTziyjMsSrAVmHuCCb6+Bfg=\", false, function () {\n  return [useNavigate];\n})), \"CzcTeTziyjMsSrAVmHuCCb6+Bfg=\", false, function () {\n  return [useNavigate];\n});\n_c2 = Footer;\nFooter.displayName = 'Footer';\nexport default Footer;\nvar _c, _c2;\n$RefreshReg$(_c, \"Footer$React.memo\");\n$RefreshReg$(_c2, \"Footer\");", "map": {"version": 3, "names": ["React", "Box", "Container", "Grid", "Typography", "Link", "IconButton", "Divider", "<PERSON><PERSON>", "Chip", "Security", "SecurityIcon", "Email", "EmailIcon", "Phone", "PhoneIcon", "LocationOn", "LocationIcon", "GitHub", "GitHubIcon", "LinkedIn", "LinkedInIcon", "Twitter", "TwitterIcon", "Facebook", "FacebookIcon", "Shield", "ShieldIcon", "Verified", "VerifiedIcon", "Copyright", "CopyrightIcon", "Cha<PERSON>", "DiscordIcon", "useNavigate", "jsxDEV", "_jsxDEV", "Footer", "_s", "memo", "_c", "navigate", "currentYear", "Date", "getFullYear", "footerSections", "title", "links", "label", "path", "socialLinks", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "url", "color", "handleLinkClick", "component", "sx", "background", "theme", "palette", "mode", "<PERSON><PERSON>ilter", "mt", "py", "position", "overflow", "content", "top", "left", "right", "height", "backgroundSize", "animation", "backgroundPosition", "children", "max<PERSON><PERSON><PERSON>", "container", "spacing", "mb", "item", "xs", "lg", "display", "alignItems", "gap", "borderRadius", "width", "justifyContent", "boxShadow", "fontSize", "variant", "fontWeight", "opacity", "lineHeight", "direction", "flexWrap", "size", "bgcolor", "border", "gutterBottom", "map", "section", "index", "sm", "md", "link", "linkIndex", "onClick", "textDecoration", "textAlign", "cursor", "padding", "transition", "social", "href", "target", "rel", "transform", "pt", "borderTop", "_c2", "displayName", "$RefreshReg$"], "sources": ["E:/PythonProjects/React.js/Smart AI Assisstant/src/components/Footer.jsx"], "sourcesContent": ["import React from 'react';\nimport {\n  Box,\n  Container,\n  Grid,\n  <PERSON>po<PERSON>,\n  Link,\n  IconButton,\n  Divider,\n  <PERSON><PERSON>,\n  Chip,\n} from '@mui/material';\nimport {\n  Security as SecurityIcon,\n  Email as EmailIcon,\n  Phone as PhoneIcon,\n  LocationOn as LocationIcon,\n  GitHub as GitHubIcon,\n  LinkedIn as LinkedInIcon,\n  Twitter as TwitterIcon,\n  Facebook as FacebookIcon,\n  Shield as ShieldIcon,\n  Verified as VerifiedIcon,\n  Copyright as CopyrightIcon,\n  Chat as DiscordIcon,\n} from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\n\nconst Footer = React.memo(() => {\n  const navigate = useNavigate();\n  const currentYear = new Date().getFullYear();\n\n  const footerSections = [\n    {\n      title: 'About Us',\n      links: [\n        { label: 'Our Mission', path: '/about' },\n        { label: 'Security Team', path: '/about' },\n        { label: 'Careers', path: '/about' },\n        { label: 'Press Kit', path: '/about' },\n      ],\n    },\n    {\n      title: 'Contact',\n      links: [\n        { label: 'Support Center', path: '/about' },\n        { label: 'Enterprise Sales', path: '/about' },\n        { label: 'Partnership', path: '/about' },\n        { label: 'Bug Reports', path: '/about' },\n      ],\n    },\n    {\n      title: 'Legal/Privacy',\n      links: [\n        { label: 'Privacy Policy', path: '/about' },\n        { label: 'Terms of Service', path: '/about' },\n        { label: 'Security Policy', path: '/about' },\n        { label: 'GDPR Compliance', path: '/about' },\n      ],\n    },\n  ];\n\n  const socialLinks = [\n    {\n      icon: <GitHubIcon />,\n      label: 'GitHub',\n      url: 'https://github.com',\n      color: '#667eea'\n    },\n    {\n      icon: <LinkedInIcon />,\n      label: 'LinkedIn',\n      url: 'https://linkedin.com',\n      color: '#0077B5'\n    },\n    {\n      icon: <TwitterIcon />,\n      label: 'Twitter',\n      url: 'https://twitter.com',\n      color: '#1DA1F2'\n    },\n    {\n      icon: <DiscordIcon />,\n      label: 'Discord',\n      url: 'https://discord.com',\n      color: '#7289DA'\n    },\n  ];\n\n  const handleLinkClick = (path) => {\n    navigate(path);\n  };\n\n  return (\n    <Box\n      component=\"footer\"\n      sx={{\n        background: (theme) => theme.palette.mode === 'dark'\n          ? 'linear-gradient(135deg, rgba(12, 12, 12, 0.98) 0%, rgba(26, 26, 26, 0.98) 50%, rgba(45, 45, 45, 0.98) 100%)'\n          : 'linear-gradient(135deg, rgba(26, 32, 44, 0.98) 0%, rgba(45, 55, 72, 0.98) 50%, rgba(74, 85, 104, 0.98) 100%)',\n        backdropFilter: 'blur(25px) saturate(180%)',\n        color: 'white',\n        mt: 'auto',\n        py: 8,\n        position: 'relative',\n        overflow: 'hidden',\n        '&::before': {\n          content: '\"\"',\n          position: 'absolute',\n          top: 0,\n          left: 0,\n          right: 0,\n          height: '4px',\n          background: 'linear-gradient(90deg, #667eea 0%, #764ba2 20%, #f093fb 40%, #f5576c 60%, #4facfe 80%, #00f2fe 100%)',\n          backgroundSize: '400% 400%',\n          animation: 'footerGradient 12s ease infinite',\n        },\n        '@keyframes footerGradient': {\n          '0%': { backgroundPosition: '0% 50%' },\n          '50%': { backgroundPosition: '100% 50%' },\n          '100%': { backgroundPosition: '0% 50%' },\n        },\n      }}\n    >\n      <Container maxWidth=\"xl\">\n        {/* Main Footer Content */}\n        <Grid container spacing={6} sx={{ mb: 6 }}>\n          {/* Company Info */}\n          <Grid item xs={12} lg={4}>\n            <Box sx={{ mb: 4 }}>\n              <Box display=\"flex\" alignItems=\"center\" gap={2} mb={3}>\n                <Box\n                  sx={{\n                    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                    borderRadius: '50%',\n                    width: 50,\n                    height: 50,\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    boxShadow: '0 8px 25px rgba(102, 126, 234, 0.3)',\n                  }}\n                >\n                  <SecurityIcon sx={{ fontSize: 28, color: 'white' }} />\n                </Box>\n                <Typography variant=\"h4\" fontWeight=\"bold\">\n                  AI Security Guard\n                </Typography>\n              </Box>\n              <Typography\n                variant=\"body1\"\n                sx={{\n                  mb: 4,\n                  opacity: 0.9,\n                  lineHeight: 1.8,\n                  fontSize: '1.1rem',\n                }}\n              >\n                Advanced AI-powered security scanning platform providing comprehensive\n                threat detection and analysis for URLs and files. Protecting your\n                digital assets with cutting-edge technology.\n              </Typography>\n              \n              {/* Security Badges */}\n              <Stack direction=\"row\" spacing={1} flexWrap=\"wrap\" gap={1}>\n                <Chip\n                  icon={<ShieldIcon />}\n                  label=\"SOC 2 Compliant\"\n                  size=\"small\"\n                  sx={{ \n                    bgcolor: 'rgba(76, 175, 80, 0.2)', \n                    color: '#4caf50',\n                    border: '1px solid rgba(76, 175, 80, 0.3)'\n                  }}\n                />\n                <Chip\n                  icon={<VerifiedIcon />}\n                  label=\"ISO 27001\"\n                  size=\"small\"\n                  sx={{ \n                    bgcolor: 'rgba(33, 150, 243, 0.2)', \n                    color: '#2196f3',\n                    border: '1px solid rgba(33, 150, 243, 0.3)'\n                  }}\n                />\n              </Stack>\n            </Box>\n\n            {/* Contact Info */}\n            <Box>\n              <Typography variant=\"h6\" gutterBottom fontWeight=\"600\">\n                Contact Information\n              </Typography>\n              <Stack spacing={1}>\n                <Box display=\"flex\" alignItems=\"center\" gap={1}>\n                  <EmailIcon sx={{ fontSize: 18, opacity: 0.7 }} />\n                  <Typography variant=\"body2\" sx={{ opacity: 0.9 }}>\n                    <EMAIL>\n                  </Typography>\n                </Box>\n                <Box display=\"flex\" alignItems=\"center\" gap={1}>\n                  <PhoneIcon sx={{ fontSize: 18, opacity: 0.7 }} />\n                  <Typography variant=\"body2\" sx={{ opacity: 0.9 }}>\n                    +****************\n                  </Typography>\n                </Box>\n                <Box display=\"flex\" alignItems=\"center\" gap={1}>\n                  <LocationIcon sx={{ fontSize: 18, opacity: 0.7 }} />\n                  <Typography variant=\"body2\" sx={{ opacity: 0.9 }}>\n                    San Francisco, CA, USA\n                  </Typography>\n                </Box>\n              </Stack>\n            </Box>\n          </Grid>\n\n          {/* Footer Links */}\n          {footerSections.map((section, index) => (\n            <Grid item xs={6} sm={3} md={2} key={index}>\n              <Typography variant=\"h6\" gutterBottom fontWeight=\"600\" sx={{ mb: 2 }}>\n                {section.title}\n              </Typography>\n              <Stack spacing={1}>\n                {section.links.map((link, linkIndex) => (\n                  <Link\n                    key={linkIndex}\n                    component=\"button\"\n                    variant=\"body2\"\n                    onClick={() => handleLinkClick(link.path)}\n                    sx={{\n                      color: 'rgba(255, 255, 255, 0.8)',\n                      textDecoration: 'none',\n                      textAlign: 'left',\n                      background: 'none',\n                      border: 'none',\n                      cursor: 'pointer',\n                      padding: 0,\n                      transition: 'color 0.2s ease',\n                      '&:hover': {\n                        color: '#64b5f6',\n                        textDecoration: 'underline',\n                      },\n                    }}\n                  >\n                    {link.label}\n                  </Link>\n                ))}\n              </Stack>\n            </Grid>\n          ))}\n        </Grid>\n\n        <Divider sx={{ bgcolor: 'rgba(255, 255, 255, 0.1)', mb: 3 }} />\n\n        {/* Bottom Footer */}\n        <Box\n          display=\"flex\"\n          justifyContent=\"space-between\"\n          alignItems=\"center\"\n          flexWrap=\"wrap\"\n          gap={2}\n        >\n          {/* Copyright */}\n          <Box display=\"flex\" alignItems=\"center\" gap={1}>\n            <CopyrightIcon sx={{ fontSize: 16, opacity: 0.7 }} />\n            <Typography variant=\"body2\" sx={{ opacity: 0.8 }}>\n              {currentYear} AI Security Guard. All rights reserved.\n            </Typography>\n          </Box>\n\n          {/* Legal Links */}\n          <Box display=\"flex\" gap={3} flexWrap=\"wrap\">\n            <Link\n              component=\"button\"\n              variant=\"body2\"\n              onClick={() => handleLinkClick('/about')}\n              sx={{\n                color: 'rgba(255, 255, 255, 0.8)',\n                textDecoration: 'none',\n                background: 'none',\n                border: 'none',\n                cursor: 'pointer',\n                '&:hover': { color: '#64b5f6' },\n              }}\n            >\n              Privacy Policy\n            </Link>\n            <Link\n              component=\"button\"\n              variant=\"body2\"\n              onClick={() => handleLinkClick('/about')}\n              sx={{\n                color: 'rgba(255, 255, 255, 0.8)',\n                textDecoration: 'none',\n                background: 'none',\n                border: 'none',\n                cursor: 'pointer',\n                '&:hover': { color: '#64b5f6' },\n              }}\n            >\n              Terms of Service\n            </Link>\n            <Link\n              component=\"button\"\n              variant=\"body2\"\n              onClick={() => handleLinkClick('/about')}\n              sx={{\n                color: 'rgba(255, 255, 255, 0.8)',\n                textDecoration: 'none',\n                background: 'none',\n                border: 'none',\n                cursor: 'pointer',\n                '&:hover': { color: '#64b5f6' },\n              }}\n            >\n              Cookie Policy\n            </Link>\n          </Box>\n\n          {/* Social Links */}\n          <Box display=\"flex\" gap={1}>\n            {socialLinks.map((social, index) => (\n              <IconButton\n                key={index}\n                href={social.url}\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                sx={{\n                  color: 'rgba(255, 255, 255, 0.7)',\n                  background: 'rgba(255, 255, 255, 0.05)',\n                  border: '1px solid rgba(255, 255, 255, 0.1)',\n                  borderRadius: 2,\n                  width: 48,\n                  height: 48,\n                  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',\n                  '&:hover': {\n                    color: social.color,\n                    background: `linear-gradient(135deg, ${social.color}15, ${social.color}25)`,\n                    border: `1px solid ${social.color}40`,\n                    transform: 'translateY(-4px) scale(1.1)',\n                    boxShadow: `0 8px 25px ${social.color}30`,\n                  },\n                }}\n                aria-label={social.label}\n              >\n                {social.icon}\n              </IconButton>\n            ))}\n          </Box>\n        </Box>\n\n        {/* Trust Indicators */}\n        <Box\n          display=\"flex\"\n          justifyContent=\"center\"\n          alignItems=\"center\"\n          gap={2}\n          mt={3}\n          pt={3}\n          sx={{ borderTop: '1px solid rgba(255, 255, 255, 0.1)' }}\n        >\n          <Typography variant=\"caption\" sx={{ opacity: 0.6, textAlign: 'center' }}>\n            Trusted by 10,000+ organizations worldwide • 99.9% uptime • 24/7 security monitoring\n          </Typography>\n        </Box>\n      </Container>\n    </Box>\n  );\n});\n\nFooter.displayName = 'Footer';\n\nexport default Footer;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SACEC,GAAG,EACHC,SAAS,EACTC,IAAI,EACJC,UAAU,EACVC,IAAI,EACJC,UAAU,EACVC,OAAO,EACPC,KAAK,EACLC,IAAI,QACC,eAAe;AACtB,SACEC,QAAQ,IAAIC,YAAY,EACxBC,KAAK,IAAIC,SAAS,EAClBC,KAAK,IAAIC,SAAS,EAClBC,UAAU,IAAIC,YAAY,EAC1BC,MAAM,IAAIC,UAAU,EACpBC,QAAQ,IAAIC,YAAY,EACxBC,OAAO,IAAIC,WAAW,EACtBC,QAAQ,IAAIC,YAAY,EACxBC,MAAM,IAAIC,UAAU,EACpBC,QAAQ,IAAIC,YAAY,EACxBC,SAAS,IAAIC,aAAa,EAC1BC,IAAI,IAAIC,WAAW,QACd,qBAAqB;AAC5B,SAASC,WAAW,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/C,MAAMC,MAAM,gBAAAC,EAAA,cAAGtC,KAAK,CAACuC,IAAI,CAAAC,EAAA,GAAAF,EAAA,CAAC,MAAM;EAAAA,EAAA;EAC9B,MAAMG,QAAQ,GAAGP,WAAW,CAAC,CAAC;EAC9B,MAAMQ,WAAW,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;EAE5C,MAAMC,cAAc,GAAG,CACrB;IACEC,KAAK,EAAE,UAAU;IACjBC,KAAK,EAAE,CACL;MAAEC,KAAK,EAAE,aAAa;MAAEC,IAAI,EAAE;IAAS,CAAC,EACxC;MAAED,KAAK,EAAE,eAAe;MAAEC,IAAI,EAAE;IAAS,CAAC,EAC1C;MAAED,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAS,CAAC,EACpC;MAAED,KAAK,EAAE,WAAW;MAAEC,IAAI,EAAE;IAAS,CAAC;EAE1C,CAAC,EACD;IACEH,KAAK,EAAE,SAAS;IAChBC,KAAK,EAAE,CACL;MAAEC,KAAK,EAAE,gBAAgB;MAAEC,IAAI,EAAE;IAAS,CAAC,EAC3C;MAAED,KAAK,EAAE,kBAAkB;MAAEC,IAAI,EAAE;IAAS,CAAC,EAC7C;MAAED,KAAK,EAAE,aAAa;MAAEC,IAAI,EAAE;IAAS,CAAC,EACxC;MAAED,KAAK,EAAE,aAAa;MAAEC,IAAI,EAAE;IAAS,CAAC;EAE5C,CAAC,EACD;IACEH,KAAK,EAAE,eAAe;IACtBC,KAAK,EAAE,CACL;MAAEC,KAAK,EAAE,gBAAgB;MAAEC,IAAI,EAAE;IAAS,CAAC,EAC3C;MAAED,KAAK,EAAE,kBAAkB;MAAEC,IAAI,EAAE;IAAS,CAAC,EAC7C;MAAED,KAAK,EAAE,iBAAiB;MAAEC,IAAI,EAAE;IAAS,CAAC,EAC5C;MAAED,KAAK,EAAE,iBAAiB;MAAEC,IAAI,EAAE;IAAS,CAAC;EAEhD,CAAC,CACF;EAED,MAAMC,WAAW,GAAG,CAClB;IACEC,IAAI,eAAEf,OAAA,CAACjB,UAAU;MAAAiC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACpBP,KAAK,EAAE,QAAQ;IACfQ,GAAG,EAAE,oBAAoB;IACzBC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,IAAI,eAAEf,OAAA,CAACf,YAAY;MAAA+B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBP,KAAK,EAAE,UAAU;IACjBQ,GAAG,EAAE,sBAAsB;IAC3BC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,IAAI,eAAEf,OAAA,CAACb,WAAW;MAAA6B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACrBP,KAAK,EAAE,SAAS;IAChBQ,GAAG,EAAE,qBAAqB;IAC1BC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,IAAI,eAAEf,OAAA,CAACH,WAAW;MAAAmB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACrBP,KAAK,EAAE,SAAS;IAChBQ,GAAG,EAAE,qBAAqB;IAC1BC,KAAK,EAAE;EACT,CAAC,CACF;EAED,MAAMC,eAAe,GAAIT,IAAI,IAAK;IAChCR,QAAQ,CAACQ,IAAI,CAAC;EAChB,CAAC;EAED,oBACEb,OAAA,CAACnC,GAAG;IACF0D,SAAS,EAAC,QAAQ;IAClBC,EAAE,EAAE;MACFC,UAAU,EAAGC,KAAK,IAAKA,KAAK,CAACC,OAAO,CAACC,IAAI,KAAK,MAAM,GAChD,6GAA6G,GAC7G,8GAA8G;MAClHC,cAAc,EAAE,2BAA2B;MAC3CR,KAAK,EAAE,OAAO;MACdS,EAAE,EAAE,MAAM;MACVC,EAAE,EAAE,CAAC;MACLC,QAAQ,EAAE,UAAU;MACpBC,QAAQ,EAAE,QAAQ;MAClB,WAAW,EAAE;QACXC,OAAO,EAAE,IAAI;QACbF,QAAQ,EAAE,UAAU;QACpBG,GAAG,EAAE,CAAC;QACNC,IAAI,EAAE,CAAC;QACPC,KAAK,EAAE,CAAC;QACRC,MAAM,EAAE,KAAK;QACbb,UAAU,EAAE,sGAAsG;QAClHc,cAAc,EAAE,WAAW;QAC3BC,SAAS,EAAE;MACb,CAAC;MACD,2BAA2B,EAAE;QAC3B,IAAI,EAAE;UAAEC,kBAAkB,EAAE;QAAS,CAAC;QACtC,KAAK,EAAE;UAAEA,kBAAkB,EAAE;QAAW,CAAC;QACzC,MAAM,EAAE;UAAEA,kBAAkB,EAAE;QAAS;MACzC;IACF,CAAE;IAAAC,QAAA,eAEF1C,OAAA,CAAClC,SAAS;MAAC6E,QAAQ,EAAC,IAAI;MAAAD,QAAA,gBAEtB1C,OAAA,CAACjC,IAAI;QAAC6E,SAAS;QAACC,OAAO,EAAE,CAAE;QAACrB,EAAE,EAAE;UAAEsB,EAAE,EAAE;QAAE,CAAE;QAAAJ,QAAA,gBAExC1C,OAAA,CAACjC,IAAI;UAACgF,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAP,QAAA,gBACvB1C,OAAA,CAACnC,GAAG;YAAC2D,EAAE,EAAE;cAAEsB,EAAE,EAAE;YAAE,CAAE;YAAAJ,QAAA,gBACjB1C,OAAA,CAACnC,GAAG;cAACqF,OAAO,EAAC,MAAM;cAACC,UAAU,EAAC,QAAQ;cAACC,GAAG,EAAE,CAAE;cAACN,EAAE,EAAE,CAAE;cAAAJ,QAAA,gBACpD1C,OAAA,CAACnC,GAAG;gBACF2D,EAAE,EAAE;kBACFC,UAAU,EAAE,mDAAmD;kBAC/D4B,YAAY,EAAE,KAAK;kBACnBC,KAAK,EAAE,EAAE;kBACThB,MAAM,EAAE,EAAE;kBACVY,OAAO,EAAE,MAAM;kBACfC,UAAU,EAAE,QAAQ;kBACpBI,cAAc,EAAE,QAAQ;kBACxBC,SAAS,EAAE;gBACb,CAAE;gBAAAd,QAAA,eAEF1C,OAAA,CAACzB,YAAY;kBAACiD,EAAE,EAAE;oBAAEiC,QAAQ,EAAE,EAAE;oBAAEpC,KAAK,EAAE;kBAAQ;gBAAE;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnD,CAAC,eACNnB,OAAA,CAAChC,UAAU;gBAAC0F,OAAO,EAAC,IAAI;gBAACC,UAAU,EAAC,MAAM;gBAAAjB,QAAA,EAAC;cAE3C;gBAAA1B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACNnB,OAAA,CAAChC,UAAU;cACT0F,OAAO,EAAC,OAAO;cACflC,EAAE,EAAE;gBACFsB,EAAE,EAAE,CAAC;gBACLc,OAAO,EAAE,GAAG;gBACZC,UAAU,EAAE,GAAG;gBACfJ,QAAQ,EAAE;cACZ,CAAE;cAAAf,QAAA,EACH;YAID;cAAA1B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAGbnB,OAAA,CAAC5B,KAAK;cAAC0F,SAAS,EAAC,KAAK;cAACjB,OAAO,EAAE,CAAE;cAACkB,QAAQ,EAAC,MAAM;cAACX,GAAG,EAAE,CAAE;cAAAV,QAAA,gBACxD1C,OAAA,CAAC3B,IAAI;gBACH0C,IAAI,eAAEf,OAAA,CAACT,UAAU;kBAAAyB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACrBP,KAAK,EAAC,iBAAiB;gBACvBoD,IAAI,EAAC,OAAO;gBACZxC,EAAE,EAAE;kBACFyC,OAAO,EAAE,wBAAwB;kBACjC5C,KAAK,EAAE,SAAS;kBAChB6C,MAAM,EAAE;gBACV;cAAE;gBAAAlD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACFnB,OAAA,CAAC3B,IAAI;gBACH0C,IAAI,eAAEf,OAAA,CAACP,YAAY;kBAAAuB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACvBP,KAAK,EAAC,WAAW;gBACjBoD,IAAI,EAAC,OAAO;gBACZxC,EAAE,EAAE;kBACFyC,OAAO,EAAE,yBAAyB;kBAClC5C,KAAK,EAAE,SAAS;kBAChB6C,MAAM,EAAE;gBACV;cAAE;gBAAAlD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAGNnB,OAAA,CAACnC,GAAG;YAAA6E,QAAA,gBACF1C,OAAA,CAAChC,UAAU;cAAC0F,OAAO,EAAC,IAAI;cAACS,YAAY;cAACR,UAAU,EAAC,KAAK;cAAAjB,QAAA,EAAC;YAEvD;cAAA1B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbnB,OAAA,CAAC5B,KAAK;cAACyE,OAAO,EAAE,CAAE;cAAAH,QAAA,gBAChB1C,OAAA,CAACnC,GAAG;gBAACqF,OAAO,EAAC,MAAM;gBAACC,UAAU,EAAC,QAAQ;gBAACC,GAAG,EAAE,CAAE;gBAAAV,QAAA,gBAC7C1C,OAAA,CAACvB,SAAS;kBAAC+C,EAAE,EAAE;oBAAEiC,QAAQ,EAAE,EAAE;oBAAEG,OAAO,EAAE;kBAAI;gBAAE;kBAAA5C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACjDnB,OAAA,CAAChC,UAAU;kBAAC0F,OAAO,EAAC,OAAO;kBAAClC,EAAE,EAAE;oBAAEoC,OAAO,EAAE;kBAAI,CAAE;kBAAAlB,QAAA,EAAC;gBAElD;kBAAA1B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACNnB,OAAA,CAACnC,GAAG;gBAACqF,OAAO,EAAC,MAAM;gBAACC,UAAU,EAAC,QAAQ;gBAACC,GAAG,EAAE,CAAE;gBAAAV,QAAA,gBAC7C1C,OAAA,CAACrB,SAAS;kBAAC6C,EAAE,EAAE;oBAAEiC,QAAQ,EAAE,EAAE;oBAAEG,OAAO,EAAE;kBAAI;gBAAE;kBAAA5C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACjDnB,OAAA,CAAChC,UAAU;kBAAC0F,OAAO,EAAC,OAAO;kBAAClC,EAAE,EAAE;oBAAEoC,OAAO,EAAE;kBAAI,CAAE;kBAAAlB,QAAA,EAAC;gBAElD;kBAAA1B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACNnB,OAAA,CAACnC,GAAG;gBAACqF,OAAO,EAAC,MAAM;gBAACC,UAAU,EAAC,QAAQ;gBAACC,GAAG,EAAE,CAAE;gBAAAV,QAAA,gBAC7C1C,OAAA,CAACnB,YAAY;kBAAC2C,EAAE,EAAE;oBAAEiC,QAAQ,EAAE,EAAE;oBAAEG,OAAO,EAAE;kBAAI;gBAAE;kBAAA5C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACpDnB,OAAA,CAAChC,UAAU;kBAAC0F,OAAO,EAAC,OAAO;kBAAClC,EAAE,EAAE;oBAAEoC,OAAO,EAAE;kBAAI,CAAE;kBAAAlB,QAAA,EAAC;gBAElD;kBAAA1B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,EAGNV,cAAc,CAAC2D,GAAG,CAAC,CAACC,OAAO,EAAEC,KAAK,kBACjCtE,OAAA,CAACjC,IAAI;UAACgF,IAAI;UAACC,EAAE,EAAE,CAAE;UAACuB,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAA9B,QAAA,gBAC7B1C,OAAA,CAAChC,UAAU;YAAC0F,OAAO,EAAC,IAAI;YAACS,YAAY;YAACR,UAAU,EAAC,KAAK;YAACnC,EAAE,EAAE;cAAEsB,EAAE,EAAE;YAAE,CAAE;YAAAJ,QAAA,EAClE2B,OAAO,CAAC3D;UAAK;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eACbnB,OAAA,CAAC5B,KAAK;YAACyE,OAAO,EAAE,CAAE;YAAAH,QAAA,EACf2B,OAAO,CAAC1D,KAAK,CAACyD,GAAG,CAAC,CAACK,IAAI,EAAEC,SAAS,kBACjC1E,OAAA,CAAC/B,IAAI;cAEHsD,SAAS,EAAC,QAAQ;cAClBmC,OAAO,EAAC,OAAO;cACfiB,OAAO,EAAEA,CAAA,KAAMrD,eAAe,CAACmD,IAAI,CAAC5D,IAAI,CAAE;cAC1CW,EAAE,EAAE;gBACFH,KAAK,EAAE,0BAA0B;gBACjCuD,cAAc,EAAE,MAAM;gBACtBC,SAAS,EAAE,MAAM;gBACjBpD,UAAU,EAAE,MAAM;gBAClByC,MAAM,EAAE,MAAM;gBACdY,MAAM,EAAE,SAAS;gBACjBC,OAAO,EAAE,CAAC;gBACVC,UAAU,EAAE,iBAAiB;gBAC7B,SAAS,EAAE;kBACT3D,KAAK,EAAE,SAAS;kBAChBuD,cAAc,EAAE;gBAClB;cACF,CAAE;cAAAlC,QAAA,EAED+B,IAAI,CAAC7D;YAAK,GAnBN8D,SAAS;cAAA1D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAoBV,CACP;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA,GA7B2BmD,KAAK;UAAAtD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA8BpC,CACP,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAEPnB,OAAA,CAAC7B,OAAO;QAACqD,EAAE,EAAE;UAAEyC,OAAO,EAAE,0BAA0B;UAAEnB,EAAE,EAAE;QAAE;MAAE;QAAA9B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAG/DnB,OAAA,CAACnC,GAAG;QACFqF,OAAO,EAAC,MAAM;QACdK,cAAc,EAAC,eAAe;QAC9BJ,UAAU,EAAC,QAAQ;QACnBY,QAAQ,EAAC,MAAM;QACfX,GAAG,EAAE,CAAE;QAAAV,QAAA,gBAGP1C,OAAA,CAACnC,GAAG;UAACqF,OAAO,EAAC,MAAM;UAACC,UAAU,EAAC,QAAQ;UAACC,GAAG,EAAE,CAAE;UAAAV,QAAA,gBAC7C1C,OAAA,CAACL,aAAa;YAAC6B,EAAE,EAAE;cAAEiC,QAAQ,EAAE,EAAE;cAAEG,OAAO,EAAE;YAAI;UAAE;YAAA5C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACrDnB,OAAA,CAAChC,UAAU;YAAC0F,OAAO,EAAC,OAAO;YAAClC,EAAE,EAAE;cAAEoC,OAAO,EAAE;YAAI,CAAE;YAAAlB,QAAA,GAC9CpC,WAAW,EAAC,0CACf;UAAA;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAGNnB,OAAA,CAACnC,GAAG;UAACqF,OAAO,EAAC,MAAM;UAACE,GAAG,EAAE,CAAE;UAACW,QAAQ,EAAC,MAAM;UAAArB,QAAA,gBACzC1C,OAAA,CAAC/B,IAAI;YACHsD,SAAS,EAAC,QAAQ;YAClBmC,OAAO,EAAC,OAAO;YACfiB,OAAO,EAAEA,CAAA,KAAMrD,eAAe,CAAC,QAAQ,CAAE;YACzCE,EAAE,EAAE;cACFH,KAAK,EAAE,0BAA0B;cACjCuD,cAAc,EAAE,MAAM;cACtBnD,UAAU,EAAE,MAAM;cAClByC,MAAM,EAAE,MAAM;cACdY,MAAM,EAAE,SAAS;cACjB,SAAS,EAAE;gBAAEzD,KAAK,EAAE;cAAU;YAChC,CAAE;YAAAqB,QAAA,EACH;UAED;YAAA1B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACPnB,OAAA,CAAC/B,IAAI;YACHsD,SAAS,EAAC,QAAQ;YAClBmC,OAAO,EAAC,OAAO;YACfiB,OAAO,EAAEA,CAAA,KAAMrD,eAAe,CAAC,QAAQ,CAAE;YACzCE,EAAE,EAAE;cACFH,KAAK,EAAE,0BAA0B;cACjCuD,cAAc,EAAE,MAAM;cACtBnD,UAAU,EAAE,MAAM;cAClByC,MAAM,EAAE,MAAM;cACdY,MAAM,EAAE,SAAS;cACjB,SAAS,EAAE;gBAAEzD,KAAK,EAAE;cAAU;YAChC,CAAE;YAAAqB,QAAA,EACH;UAED;YAAA1B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACPnB,OAAA,CAAC/B,IAAI;YACHsD,SAAS,EAAC,QAAQ;YAClBmC,OAAO,EAAC,OAAO;YACfiB,OAAO,EAAEA,CAAA,KAAMrD,eAAe,CAAC,QAAQ,CAAE;YACzCE,EAAE,EAAE;cACFH,KAAK,EAAE,0BAA0B;cACjCuD,cAAc,EAAE,MAAM;cACtBnD,UAAU,EAAE,MAAM;cAClByC,MAAM,EAAE,MAAM;cACdY,MAAM,EAAE,SAAS;cACjB,SAAS,EAAE;gBAAEzD,KAAK,EAAE;cAAU;YAChC,CAAE;YAAAqB,QAAA,EACH;UAED;YAAA1B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAGNnB,OAAA,CAACnC,GAAG;UAACqF,OAAO,EAAC,MAAM;UAACE,GAAG,EAAE,CAAE;UAAAV,QAAA,EACxB5B,WAAW,CAACsD,GAAG,CAAC,CAACa,MAAM,EAAEX,KAAK,kBAC7BtE,OAAA,CAAC9B,UAAU;YAETgH,IAAI,EAAED,MAAM,CAAC7D,GAAI;YACjB+D,MAAM,EAAC,QAAQ;YACfC,GAAG,EAAC,qBAAqB;YACzB5D,EAAE,EAAE;cACFH,KAAK,EAAE,0BAA0B;cACjCI,UAAU,EAAE,2BAA2B;cACvCyC,MAAM,EAAE,oCAAoC;cAC5Cb,YAAY,EAAE,CAAC;cACfC,KAAK,EAAE,EAAE;cACThB,MAAM,EAAE,EAAE;cACV0C,UAAU,EAAE,uCAAuC;cACnD,SAAS,EAAE;gBACT3D,KAAK,EAAE4D,MAAM,CAAC5D,KAAK;gBACnBI,UAAU,EAAE,2BAA2BwD,MAAM,CAAC5D,KAAK,OAAO4D,MAAM,CAAC5D,KAAK,KAAK;gBAC3E6C,MAAM,EAAE,aAAae,MAAM,CAAC5D,KAAK,IAAI;gBACrCgE,SAAS,EAAE,6BAA6B;gBACxC7B,SAAS,EAAE,cAAcyB,MAAM,CAAC5D,KAAK;cACvC;YACF,CAAE;YACF,cAAY4D,MAAM,CAACrE,KAAM;YAAA8B,QAAA,EAExBuC,MAAM,CAAClE;UAAI,GAtBPuD,KAAK;YAAAtD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAuBA,CACb;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNnB,OAAA,CAACnC,GAAG;QACFqF,OAAO,EAAC,MAAM;QACdK,cAAc,EAAC,QAAQ;QACvBJ,UAAU,EAAC,QAAQ;QACnBC,GAAG,EAAE,CAAE;QACPtB,EAAE,EAAE,CAAE;QACNwD,EAAE,EAAE,CAAE;QACN9D,EAAE,EAAE;UAAE+D,SAAS,EAAE;QAAqC,CAAE;QAAA7C,QAAA,eAExD1C,OAAA,CAAChC,UAAU;UAAC0F,OAAO,EAAC,SAAS;UAAClC,EAAE,EAAE;YAAEoC,OAAO,EAAE,GAAG;YAAEiB,SAAS,EAAE;UAAS,CAAE;UAAAnC,QAAA,EAAC;QAEzE;UAAA1B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACT,CAAC;AAEV,CAAC;EAAA,QAnVkBrB,WAAW;AAAA,EAmV7B,CAAC;EAAA,QAnViBA,WAAW;AAAA,EAmV5B;AAAC0F,GAAA,GApVGvF,MAAM;AAsVZA,MAAM,CAACwF,WAAW,GAAG,QAAQ;AAE7B,eAAexF,MAAM;AAAC,IAAAG,EAAA,EAAAoF,GAAA;AAAAE,YAAA,CAAAtF,EAAA;AAAAsF,YAAA,CAAAF,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}