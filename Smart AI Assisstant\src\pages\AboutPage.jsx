import React from 'react';
import {
  Container,
  Box,
  Typography,
  Card,
  CardContent,
  Avatar,
  Grid,
  Chip,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider,
} from '@mui/material';
import {
  Security as SecurityIcon,
  Business as BusinessIcon,
  People as PeopleIcon,
  Timeline as TimelineIcon,
  Shield as ShieldIcon,
  Star as StarIcon,
  TrendingUp as TrendingUpIcon,
  Public as PublicIcon,
  CheckCircle as CheckCircleIcon,
  Award as AwardIcon,
} from '@mui/icons-material';

const AboutPage = React.memo(() => {
  return (
    <Box
      sx={{
        minHeight: '100vh',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center',
        background: 'linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 50%, #121212 100%)',
        position: 'relative',
        '&::before': {
          content: '""',
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundImage: `
            radial-gradient(circle at 30% 40%, rgba(102, 126, 234, 0.08) 0%, transparent 50%),
            radial-gradient(circle at 70% 60%, rgba(118, 75, 162, 0.08) 0%, transparent 50%)
          `,
          zIndex: -1,
        },
      }}
    >
      <Container maxWidth="xl" sx={{ py: 8 }}>
        {/* Hero Section */}
        <Box textAlign="center" mb={10}>
          <Box
            sx={{
              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
              borderRadius: '50%',
              width: 120,
              height: 120,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              mx: 'auto',
              mb: 4,
              boxShadow: '0 20px 40px rgba(102, 126, 234, 0.3)',
              position: 'relative',
              '&::after': {
                content: '""',
                position: 'absolute',
                top: -10,
                left: -10,
                right: -10,
                bottom: -10,
                borderRadius: '50%',
                border: '3px solid',
                borderColor: 'primary.main',
                opacity: 0.3,
                animation: 'pulse 2s infinite',
              },
              '@keyframes pulse': {
                '0%': { transform: 'scale(1)', opacity: 0.3 },
                '50%': { transform: 'scale(1.1)', opacity: 0.1 },
                '100%': { transform: 'scale(1)', opacity: 0.3 },
              },
            }}
          >
            <SecurityIcon sx={{ fontSize: 60, color: 'white' }} />
          </Box>

          <Typography
            variant="h1"
            component="h1"
            gutterBottom
            fontWeight="bold"
            sx={{
              background: 'linear-gradient(45deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%)',
              backgroundSize: '400% 400%',
              backgroundClip: 'text',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              animation: 'gradientText 8s ease infinite',
              fontSize: { xs: '2.5rem', md: '4rem' },
              mb: 3,
              '@keyframes gradientText': {
                '0%': { backgroundPosition: '0% 50%' },
                '50%': { backgroundPosition: '100% 50%' },
                '100%': { backgroundPosition: '0% 50%' },
              },
            }}
          >
            About AI Security Guard Inc.
          </Typography>

          <Typography
            variant="h4"
            color="rgba(255, 255, 255, 0.8)"
            maxWidth="900px"
            mx="auto"
            sx={{
              lineHeight: 1.6,
              fontSize: { xs: '1.2rem', md: '1.5rem' },
              mb: 6,
            }}
          >
            Leading the future of cybersecurity with artificial intelligence and innovation
          </Typography>

          <Box display="flex" justifyContent="center" gap={2} flexWrap="wrap" mb={8}>
            <Chip label="Founded 2020" color="primary" variant="outlined" />
            <Chip label="10,000+ Customers" color="success" variant="outlined" />
            <Chip label="99.9% Uptime" color="info" variant="outlined" />
            <Chip label="24/7 Support" color="warning" variant="outlined" />
          </Box>
        </Box>

        {/* Company Overview */}
        <Grid container spacing={6} mb={10}>
          <Grid item xs={12} md={6}>
            <Card
              sx={{
                height: '100%',
                background: 'linear-gradient(135deg, rgba(12, 12, 12, 0.95) 0%, rgba(26, 26, 26, 0.95) 100%)',
                backdropFilter: 'blur(25px)',
                border: '1px solid rgba(102, 126, 234, 0.2)',
                borderRadius: 4,
                transition: 'all 0.3s ease',
                '&:hover': {
                  transform: 'translateY(-8px)',
                  boxShadow: '0 20px 60px rgba(102, 126, 234, 0.3)',
                },
              }}
            >
              <CardContent sx={{ p: 4 }}>
                <Box display="flex" alignItems="center" mb={3}>
                  <BusinessIcon sx={{ fontSize: 32, color: 'primary.main', mr: 2 }} />
                  <Typography variant="h4" fontWeight="bold" color="white">
                    Our Company
                  </Typography>
                </Box>
                <Typography variant="body1" color="rgba(255, 255, 255, 0.9)" sx={{ mb: 3, lineHeight: 1.8 }}>
                  AI Security Guard Inc. was founded in 2020 with a vision to revolutionize cybersecurity through
                  artificial intelligence. We are a team of cybersecurity experts, AI researchers, and software
                  engineers dedicated to protecting businesses and individuals from evolving cyber threats.
                </Typography>
                <Typography variant="body1" color="rgba(255, 255, 255, 0.9)" sx={{ lineHeight: 1.8 }}>
                  Our headquarters are located in San Francisco, California, with additional offices in New York,
                  London, and Tokyo. We serve customers across 50+ countries and have processed over 100 million
                  security scans to date.
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={6}>
            <Card
              sx={{
                height: '100%',
                background: 'linear-gradient(135deg, rgba(12, 12, 12, 0.95) 0%, rgba(26, 26, 26, 0.95) 100%)',
                backdropFilter: 'blur(25px)',
                border: '1px solid rgba(244, 67, 54, 0.2)',
                borderRadius: 4,
                transition: 'all 0.3s ease',
                '&:hover': {
                  transform: 'translateY(-8px)',
                  boxShadow: '0 20px 60px rgba(244, 67, 54, 0.3)',
                },
              }}
            >
              <CardContent sx={{ p: 4 }}>
                <Box display="flex" alignItems="center" mb={3}>
                  <ShieldIcon sx={{ fontSize: 32, color: 'error.main', mr: 2 }} />
                  <Typography variant="h4" fontWeight="bold" color="white">
                    Our Mission
                  </Typography>
                </Box>
                <Typography variant="body1" color="rgba(255, 255, 255, 0.9)" sx={{ mb: 3, lineHeight: 1.8 }}>
                  To democratize advanced cybersecurity by making AI-powered threat detection accessible to
                  organizations of all sizes. We believe that every business deserves enterprise-grade security
                  protection, regardless of their technical expertise or budget.
                </Typography>
                <Typography variant="body1" color="rgba(255, 255, 255, 0.9)" sx={{ lineHeight: 1.8 }}>
                  We are committed to staying ahead of cybercriminals by continuously innovating our AI algorithms
                  and expanding our threat intelligence network to provide the most comprehensive security coverage available.
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Why Choose Us Section */}
        <Box textAlign="center" mb={10}>
          <Typography variant="h3" fontWeight="bold" color="white" mb={6}>
            Why Choose Us?
          </Typography>

          <Grid container spacing={4} mb={8}>
            <Grid item xs={12} md={6}>
              <Card
                sx={{
                  background: 'linear-gradient(135deg, rgba(12, 12, 12, 0.95) 0%, rgba(26, 26, 26, 0.95) 100%)',
                  backdropFilter: 'blur(25px)',
                  border: '1px solid rgba(102, 126, 234, 0.2)',
                  borderRadius: 4,
                  height: '100%',
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    transform: 'translateY(-8px)',
                    boxShadow: '0 20px 60px rgba(102, 126, 234, 0.3)',
                  },
                }}
              >
                <CardContent sx={{ p: 4 }}>
                  <Typography variant="h5" color="white" gutterBottom fontWeight="600" sx={{ mb: 3 }}>
                    🚀 Advanced AI Technology
                  </Typography>
                  <Typography variant="body1" color="rgba(255, 255, 255, 0.9)" sx={{ lineHeight: 1.8 }}>
                    Our proprietary AI algorithms detect threats with 99.7% accuracy, staying ahead of emerging cyber threats
                    through continuous machine learning and real-time threat intelligence updates.
                  </Typography>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} md={6}>
              <Card
                sx={{
                  background: 'linear-gradient(135deg, rgba(12, 12, 12, 0.95) 0%, rgba(26, 26, 26, 0.95) 100%)',
                  backdropFilter: 'blur(25px)',
                  border: '1px solid rgba(244, 67, 54, 0.2)',
                  borderRadius: 4,
                  height: '100%',
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    transform: 'translateY(-8px)',
                    boxShadow: '0 20px 60px rgba(244, 67, 54, 0.3)',
                  },
                }}
              >
                <CardContent sx={{ p: 4 }}>
                  <Typography variant="h5" color="white" gutterBottom fontWeight="600" sx={{ mb: 3 }}>
                    ⚡ Lightning-Fast Response
                  </Typography>
                  <Typography variant="body1" color="rgba(255, 255, 255, 0.9)" sx={{ lineHeight: 1.8 }}>
                    Real-time threat detection and response in under 0.3 seconds, protecting your assets instantly
                    with automated incident response and immediate threat neutralization.
                  </Typography>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} md={6}>
              <Card
                sx={{
                  background: 'linear-gradient(135deg, rgba(12, 12, 12, 0.95) 0%, rgba(26, 26, 26, 0.95) 100%)',
                  backdropFilter: 'blur(25px)',
                  border: '1px solid rgba(76, 175, 80, 0.2)',
                  borderRadius: 4,
                  height: '100%',
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    transform: 'translateY(-8px)',
                    boxShadow: '0 20px 60px rgba(76, 175, 80, 0.3)',
                  },
                }}
              >
                <CardContent sx={{ p: 4 }}>
                  <Typography variant="h5" color="white" gutterBottom fontWeight="600" sx={{ mb: 3 }}>
                    🛡️ Enterprise-Grade Security
                  </Typography>
                  <Typography variant="body1" color="rgba(255, 255, 255, 0.9)" sx={{ lineHeight: 1.8 }}>
                    SOC 2 compliant with military-grade encryption, trusted by Fortune 500 companies worldwide.
                    Our security infrastructure meets the highest industry standards.
                  </Typography>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} md={6}>
              <Card
                sx={{
                  background: 'linear-gradient(135deg, rgba(12, 12, 12, 0.95) 0%, rgba(26, 26, 26, 0.95) 100%)',
                  backdropFilter: 'blur(25px)',
                  border: '1px solid rgba(156, 39, 176, 0.2)',
                  borderRadius: 4,
                  height: '100%',
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    transform: 'translateY(-8px)',
                    boxShadow: '0 20px 60px rgba(156, 39, 176, 0.3)',
                  },
                }}
              >
                <CardContent sx={{ p: 4 }}>
                  <Typography variant="h5" color="white" gutterBottom fontWeight="600" sx={{ mb: 3 }}>
                    📊 Comprehensive Analytics
                  </Typography>
                  <Typography variant="body1" color="rgba(255, 255, 255, 0.9)" sx={{ lineHeight: 1.8 }}>
                    Detailed security reports and insights to help you understand and improve your security posture
                    with actionable recommendations and trend analysis.
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </Box>

        {/* Key Statistics */}
        <Box textAlign="center" mb={10}>
          <Typography variant="h3" fontWeight="bold" color="white" mb={6}>
            Our Impact
          </Typography>
          <Grid container spacing={4}>
            {[
              { icon: <PeopleIcon />, value: '10,000+', label: 'Active Users', color: '#667eea' },
              { icon: <ShieldIcon />, value: '100M+', label: 'Threats Blocked', color: '#f093fb' },
              { icon: <PublicIcon />, value: '50+', label: 'Countries Served', color: '#4facfe' },
              { icon: <TrendingUpIcon />, value: '99.7%', label: 'Detection Accuracy', color: '#43e97b' },
            ].map((stat, index) => (
              <Grid item xs={6} md={3} key={index}>
                <Card
                  sx={{
                    background: 'linear-gradient(135deg, rgba(12, 12, 12, 0.95) 0%, rgba(26, 26, 26, 0.95) 100%)',
                    backdropFilter: 'blur(25px)',
                    border: `1px solid ${stat.color}40`,
                    borderRadius: 4,
                    textAlign: 'center',
                    transition: 'all 0.3s ease',
                    '&:hover': {
                      transform: 'translateY(-8px) scale(1.05)',
                      boxShadow: `0 20px 60px ${stat.color}30`,
                    },
                  }}
                >
                  <CardContent sx={{ p: 4 }}>
                    <Box
                      sx={{
                        background: `linear-gradient(135deg, ${stat.color}20, ${stat.color}10)`,
                        borderRadius: '50%',
                        width: 64,
                        height: 64,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        mx: 'auto',
                        mb: 2,
                      }}
                    >
                      {React.cloneElement(stat.icon, { sx: { fontSize: 32, color: stat.color } })}
                    </Box>
                    <Typography variant="h3" fontWeight="bold" sx={{ color: stat.color, mb: 1 }}>
                      {stat.value}
                    </Typography>
                    <Typography variant="h6" color="rgba(255, 255, 255, 0.8)">
                      {stat.label}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </Box>

        {/* Contact Information */}
        <Card
          sx={{
            background: 'linear-gradient(135deg, rgba(12, 12, 12, 0.95) 0%, rgba(26, 26, 26, 0.95) 100%)',
            backdropFilter: 'blur(25px)',
            border: '1px solid rgba(102, 126, 234, 0.2)',
            borderRadius: 4,
            textAlign: 'center',
            position: 'relative',
            overflow: 'hidden',
            '&::before': {
              content: '""',
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              height: '4px',
              background: 'linear-gradient(90deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%)',
              backgroundSize: '400% 400%',
              animation: 'gradientShift 8s ease infinite',
            },
            '@keyframes gradientShift': {
              '0%': { backgroundPosition: '0% 50%' },
              '50%': { backgroundPosition: '100% 50%' },
              '100%': { backgroundPosition: '0% 50%' },
            },
          }}
        >
          <CardContent sx={{ p: 6 }}>
            <Typography variant="h4" fontWeight="bold" color="white" mb={3}>
              Get in Touch
            </Typography>
            <Typography variant="body1" color="rgba(255, 255, 255, 0.9)" sx={{ mb: 4, lineHeight: 1.8 }}>
              Ready to secure your digital assets with AI-powered protection?
              Contact our team of cybersecurity experts today.
            </Typography>
            <Grid container spacing={4} justifyContent="center">
              <Grid item xs={12} md={4}>
                <Typography variant="h6" color="primary.main" gutterBottom>
                  Email
                </Typography>
                <Typography variant="body1" color="rgba(255, 255, 255, 0.9)">
                  <EMAIL>
                </Typography>
              </Grid>
              <Grid item xs={12} md={4}>
                <Typography variant="h6" color="primary.main" gutterBottom>
                  Phone
                </Typography>
                <Typography variant="body1" color="rgba(255, 255, 255, 0.9)">
                  +****************
                </Typography>
              </Grid>
              <Grid item xs={12} md={4}>
                <Typography variant="h6" color="primary.main" gutterBottom>
                  Address
                </Typography>
                <Typography variant="body1" color="rgba(255, 255, 255, 0.9)">
                  San Francisco, CA, USA
                </Typography>
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      </Container>
    </Box>
  );
});

AboutPage.displayName = 'AboutPage';

export default AboutPage;
