{"ast": null, "code": "var _jsxFileName = \"E:\\\\PythonProjects\\\\React.js\\\\Smart AI Assisstant\\\\src\\\\pages\\\\HomePage.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Container, Grid, Box, Typography, Card, CardContent, Tabs, Tab, Alert, Snackbar, Fade, Button, Chip } from '@mui/material';\nimport { Security as SecurityIcon, Link as LinkIcon, InsertDriveFile as FileIcon, Assessment as AssessmentIcon } from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport { useScan } from '../contexts/ScanContext';\nimport LinkScanner from '../components/LinkScanner';\nimport FileUploader from '../components/FileUploader';\nimport ResultView from '../components/ResultView';\nimport ThreatMeter from '../components/ThreatMeter';\nimport SmartDashboard from '../components/SmartDashboard';\nimport ThreatPrediction from '../components/ThreatPrediction';\nimport AutoScanSystem from '../components/AutoScanSystem';\nimport SmartNotifications from '../components/SmartNotifications';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst HomePage = /*#__PURE__*/_s(/*#__PURE__*/React.memo(_c = _s(() => {\n  _s();\n  const [activeTab, setActiveTab] = useState(0);\n  const [notification, setNotification] = useState(null);\n  const navigate = useNavigate();\n  const {\n    currentScan,\n    isScanning,\n    threatLevel,\n    notifications,\n    removeNotification\n  } = useScan();\n  const handleTabChange = (event, newValue) => {\n    setActiveTab(newValue);\n  };\n  const handleCloseNotification = notificationId => {\n    removeNotification(notificationId);\n  };\n  const handleDownloadReport = scanResult => {\n    // This would integrate with a PDF generation service\n    console.log('Downloading report for:', scanResult);\n    setNotification({\n      type: 'info',\n      message: 'Report download feature will be available soon.'\n    });\n  };\n  const handleShareResult = scanResult => {\n    // This would integrate with sharing functionality\n    console.log('Sharing result:', scanResult);\n    setNotification({\n      type: 'info',\n      message: 'Result sharing feature will be available soon.'\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      width: '100%',\n      minHeight: '100vh',\n      display: 'flex',\n      flexDirection: 'column',\n      justifyContent: 'center',\n      alignItems: 'center',\n      py: 4,\n      position: 'relative',\n      background: 'linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 50%, #121212 100%)',\n      '&::before': {\n        content: '\"\"',\n        position: 'absolute',\n        top: 0,\n        left: 0,\n        right: 0,\n        bottom: 0,\n        backgroundImage: `\n            radial-gradient(circle at 20% 50%, rgba(102, 126, 234, 0.1) 0%, transparent 50%),\n            radial-gradient(circle at 80% 20%, rgba(118, 75, 162, 0.1) 0%, transparent 50%),\n            radial-gradient(circle at 40% 80%, rgba(240, 147, 251, 0.08) 0%, transparent 50%),\n            linear-gradient(135deg, rgba(15, 15, 15, 0.6) 0%, rgba(26, 26, 26, 0.6) 100%)\n          `,\n        zIndex: -2\n      },\n      '&::after': {\n        content: '\"\"',\n        position: 'absolute',\n        top: 0,\n        left: 0,\n        right: 0,\n        bottom: 0,\n        backgroundImage: `\n            url(\"data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23667eea' fill-opacity='0.03'%3E%3Ccircle cx='30' cy='30' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E\")\n          `,\n        animation: 'particleFloat 20s linear infinite',\n        zIndex: -1\n      },\n      '@keyframes particleFloat': {\n        '0%': {\n          transform: 'translateY(0px) translateX(0px)'\n        },\n        '33%': {\n          transform: 'translateY(-10px) translateX(5px)'\n        },\n        '66%': {\n          transform: 'translateY(5px) translateX(-5px)'\n        },\n        '100%': {\n          transform: 'translateY(0px) translateX(0px)'\n        }\n      }\n    },\n    children: [/*#__PURE__*/_jsxDEV(Container, {\n      maxWidth: \"xl\",\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        textAlign: \"center\",\n        mb: 8,\n        sx: {\n          position: 'relative',\n          py: 8,\n          px: 4,\n          borderRadius: 4,\n          overflow: 'hidden',\n          '&::before': {\n            content: '\"\"',\n            position: 'absolute',\n            top: 0,\n            left: 0,\n            right: 0,\n            bottom: 0,\n            background: 'linear-gradient(45deg, rgba(102, 126, 234, 0.08) 0%, rgba(118, 75, 162, 0.08) 25%, rgba(240, 147, 251, 0.08) 50%, rgba(79, 172, 254, 0.08) 75%, rgba(102, 126, 234, 0.08) 100%)',\n            backgroundSize: '400% 400%',\n            animation: 'cyberWaves 15s ease infinite',\n            zIndex: -2\n          },\n          '&::after': {\n            content: '\"\"',\n            position: 'absolute',\n            top: 0,\n            left: 0,\n            right: 0,\n            bottom: 0,\n            background: theme => theme.palette.mode === 'dark' ? 'linear-gradient(135deg, rgba(12, 12, 12, 0.4) 0%, rgba(26, 26, 26, 0.4) 50%, rgba(45, 45, 45, 0.4) 100%)' : 'linear-gradient(135deg, rgba(248, 250, 252, 0.6) 0%, rgba(241, 245, 249, 0.6) 50%, rgba(226, 232, 240, 0.6) 100%)',\n            backdropFilter: 'blur(20px) saturate(180%)',\n            zIndex: -1\n          },\n          '@keyframes cyberWaves': {\n            '0%': {\n              backgroundPosition: '0% 50%'\n            },\n            '50%': {\n              backgroundPosition: '100% 50%'\n            },\n            '100%': {\n              backgroundPosition: '0% 50%'\n            }\n          },\n          '@keyframes neuralPulse': {\n            '0%': {\n              backgroundPosition: '0% 50%'\n            },\n            '50%': {\n              backgroundPosition: '100% 50%'\n            },\n            '100%': {\n              backgroundPosition: '0% 50%'\n            }\n          },\n          '@keyframes quantumGlow': {\n            '0%': {\n              transform: 'scale(1)',\n              opacity: 0.3\n            },\n            '50%': {\n              transform: 'scale(1.05)',\n              opacity: 0.1\n            },\n            '100%': {\n              transform: 'scale(1)',\n              opacity: 0.3\n            }\n          }\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n            borderRadius: '50%',\n            width: 140,\n            height: 140,\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            mx: 'auto',\n            mb: 4,\n            boxShadow: '0 20px 60px rgba(102, 126, 234, 0.4)',\n            position: 'relative',\n            '&::before': {\n              content: '\"\"',\n              position: 'absolute',\n              top: -8,\n              left: -8,\n              right: -8,\n              bottom: -8,\n              borderRadius: '50%',\n              background: 'linear-gradient(45deg, #667eea, #764ba2, #f093fb, #f5576c)',\n              backgroundSize: '400% 400%',\n              animation: 'neuralPulse 3s ease infinite',\n              opacity: 0.7,\n              zIndex: -1\n            },\n            '&::after': {\n              content: '\"\"',\n              position: 'absolute',\n              top: -15,\n              left: -15,\n              right: -15,\n              bottom: -15,\n              borderRadius: '50%',\n              border: '3px solid rgba(102, 126, 234, 0.3)',\n              animation: 'quantumGlow 2s infinite'\n            }\n          },\n          children: /*#__PURE__*/_jsxDEV(SecurityIcon, {\n            sx: {\n              fontSize: 70,\n              color: 'white'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 215,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 175,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h1\",\n          component: \"h1\",\n          gutterBottom: true,\n          fontWeight: \"bold\",\n          sx: {\n            background: 'linear-gradient(45deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%)',\n            backgroundSize: '400% 400%',\n            backgroundClip: 'text',\n            WebkitBackgroundClip: 'text',\n            WebkitTextFillColor: 'transparent',\n            animation: 'neuralPulse 8s ease infinite',\n            mb: 3,\n            fontSize: {\n              xs: '2.5rem',\n              md: '4rem'\n            }\n          },\n          children: \"AI Security Guard\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 219,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h4\",\n          color: \"text.secondary\",\n          maxWidth: \"900px\",\n          mx: \"auto\",\n          sx: {\n            mb: 6,\n            lineHeight: 1.6,\n            fontWeight: 300,\n            fontSize: {\n              xs: '1.2rem',\n              md: '1.5rem'\n            }\n          },\n          children: \"Next-generation cybersecurity powered by advanced artificial intelligence. Protect your digital assets with real-time threat detection and predictive analytics.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 238,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'center',\n            gap: 3,\n            flexWrap: 'wrap',\n            mb: 6\n          },\n          children: [{\n            label: 'Real-time Analytics',\n            gradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'\n          }, {\n            label: 'AI Threat Prediction',\n            gradient: 'linear-gradient(135deg, #9c27b0 0%, #e91e63 100%)'\n          }, {\n            label: 'Automated Response',\n            gradient: 'linear-gradient(135deg, #4caf50 0%, #8bc34a 100%)'\n          }, {\n            label: 'Smart Notifications',\n            gradient: 'linear-gradient(135deg, #2196f3 0%, #9c27b0 100%)'\n          }].map((feature, index) => /*#__PURE__*/_jsxDEV(Chip, {\n            label: feature.label,\n            variant: \"outlined\",\n            sx: {\n              borderColor: 'transparent',\n              background: feature.gradient,\n              color: 'white',\n              fontWeight: 600,\n              fontSize: '1rem',\n              px: 3,\n              py: 2,\n              height: 'auto',\n              borderRadius: 3,\n              transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',\n              boxShadow: '0 4px 15px rgba(0,0,0,0.2)',\n              '&:hover': {\n                transform: 'translateY(-3px) scale(1.05)',\n                boxShadow: '0 8px 25px rgba(0,0,0,0.3)'\n              }\n            }\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 269,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 254,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          display: \"flex\",\n          justifyContent: \"center\",\n          gap: 3,\n          flexWrap: \"wrap\",\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            size: \"large\",\n            sx: {\n              px: 6,\n              py: 2,\n              borderRadius: 3,\n              fontSize: '1.1rem',\n              fontWeight: 600,\n              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n              boxShadow: '0 8px 25px rgba(102, 126, 234, 0.4)',\n              '&:hover': {\n                background: 'linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)',\n                transform: 'translateY(-2px)',\n                boxShadow: '0 12px 35px rgba(102, 126, 234, 0.5)'\n              }\n            },\n            children: \"Start Security Scan\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 296,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outlined\",\n            size: \"large\",\n            onClick: () => navigate('/smart-features'),\n            sx: {\n              px: 6,\n              py: 2,\n              borderRadius: 3,\n              fontSize: '1.1rem',\n              fontWeight: 600,\n              borderColor: 'primary.main',\n              color: 'primary.main',\n              borderWidth: 2,\n              '&:hover': {\n                borderWidth: 2,\n                background: 'linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%)',\n                transform: 'translateY(-2px)',\n                boxShadow: '0 8px 25px rgba(102, 126, 234, 0.2)'\n              }\n            },\n            children: \"Explore AI Features\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 316,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 295,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 123,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        display: \"flex\",\n        justifyContent: \"center\",\n        width: \"100%\",\n        children: /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 4,\n          maxWidth: \"1400px\",\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            lg: 8,\n            children: [/*#__PURE__*/_jsxDEV(Card, {\n              sx: {\n                mb: 4,\n                background: theme => theme.palette.mode === 'dark' ? 'linear-gradient(135deg, rgba(12, 12, 12, 0.95) 0%, rgba(26, 26, 26, 0.95) 50%, rgba(45, 45, 45, 0.95) 100%)' : 'linear-gradient(135deg, rgba(248, 250, 252, 0.95) 0%, rgba(241, 245, 249, 0.95) 50%, rgba(226, 232, 240, 0.95) 100%)',\n                backdropFilter: 'blur(25px) saturate(180%)',\n                border: theme => theme.palette.mode === 'dark' ? '1px solid rgba(255, 255, 255, 0.1)' : '1px solid rgba(0, 0, 0, 0.1)',\n                borderRadius: 4,\n                overflow: 'hidden',\n                position: 'relative',\n                boxShadow: '0 8px 32px rgba(102, 126, 234, 0.15)',\n                transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',\n                '&:hover': {\n                  transform: 'translateY(-2px)',\n                  boxShadow: '0 16px 48px rgba(102, 126, 234, 0.2)'\n                }\n              },\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                sx: {\n                  p: 4\n                },\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    borderBottom: 1,\n                    borderColor: 'divider',\n                    mb: 4\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Tabs, {\n                    value: activeTab,\n                    onChange: handleTabChange,\n                    variant: \"fullWidth\",\n                    textColor: \"primary\",\n                    indicatorColor: \"primary\",\n                    sx: {\n                      '& .MuiTab-root': {\n                        minHeight: 72,\n                        fontSize: '1rem',\n                        fontWeight: 600,\n                        textTransform: 'none'\n                      }\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Tab, {\n                      icon: /*#__PURE__*/_jsxDEV(LinkIcon, {\n                        sx: {\n                          fontSize: 28\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 386,\n                        columnNumber: 31\n                      }, this),\n                      label: \"URL Security Scanner\",\n                      iconPosition: \"start\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 385,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Tab, {\n                      icon: /*#__PURE__*/_jsxDEV(FileIcon, {\n                        sx: {\n                          fontSize: 28\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 391,\n                        columnNumber: 31\n                      }, this),\n                      label: \"File Security Scanner\",\n                      iconPosition: \"start\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 390,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Tab, {\n                      icon: /*#__PURE__*/_jsxDEV(SecurityIcon, {\n                        sx: {\n                          fontSize: 28\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 396,\n                        columnNumber: 31\n                      }, this),\n                      label: \"Smart Dashboard\",\n                      iconPosition: \"start\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 395,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Tab, {\n                      icon: /*#__PURE__*/_jsxDEV(AssessmentIcon, {\n                        sx: {\n                          fontSize: 28\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 401,\n                        columnNumber: 31\n                      }, this),\n                      label: \"Threat Prediction\",\n                      iconPosition: \"start\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 400,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 370,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 369,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  children: [activeTab === 0 && /*#__PURE__*/_jsxDEV(Fade, {\n                    in: activeTab === 0,\n                    timeout: 300,\n                    children: /*#__PURE__*/_jsxDEV(Box, {\n                      children: /*#__PURE__*/_jsxDEV(LinkScanner, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 413,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 412,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 411,\n                    columnNumber: 23\n                  }, this), activeTab === 1 && /*#__PURE__*/_jsxDEV(Fade, {\n                    in: activeTab === 1,\n                    timeout: 300,\n                    children: /*#__PURE__*/_jsxDEV(Box, {\n                      children: /*#__PURE__*/_jsxDEV(FileUploader, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 421,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 420,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 419,\n                    columnNumber: 23\n                  }, this), activeTab === 2 && /*#__PURE__*/_jsxDEV(Fade, {\n                    in: activeTab === 2,\n                    timeout: 300,\n                    children: /*#__PURE__*/_jsxDEV(Box, {\n                      children: /*#__PURE__*/_jsxDEV(SmartDashboard, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 429,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 428,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 427,\n                    columnNumber: 23\n                  }, this), activeTab === 3 && /*#__PURE__*/_jsxDEV(Fade, {\n                    in: activeTab === 3,\n                    timeout: 300,\n                    children: /*#__PURE__*/_jsxDEV(Box, {\n                      children: /*#__PURE__*/_jsxDEV(ThreatPrediction, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 437,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 436,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 435,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 409,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 367,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 346,\n              columnNumber: 15\n            }, this), (currentScan || isScanning) && /*#__PURE__*/_jsxDEV(Fade, {\n              in: true,\n              timeout: 500,\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                children: /*#__PURE__*/_jsxDEV(ResultView, {\n                  scanResult: currentScan,\n                  onDownloadReport: handleDownloadReport,\n                  onShareResult: handleShareResult\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 449,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 448,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 447,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 345,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            lg: 4,\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              mb: 4,\n              children: /*#__PURE__*/_jsxDEV(ThreatMeter, {\n                threatData: currentScan,\n                showDetails: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 463,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 462,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Card, {\n              sx: {\n                mb: 4,\n                background: theme => theme.palette.mode === 'dark' ? 'linear-gradient(135deg, rgba(102, 126, 234, 0.15) 0%, rgba(118, 75, 162, 0.15) 50%, rgba(240, 147, 251, 0.15) 100%)' : 'linear-gradient(135deg, rgba(102, 126, 234, 0.08) 0%, rgba(118, 75, 162, 0.08) 50%, rgba(240, 147, 251, 0.08) 100%)',\n                backdropFilter: 'blur(20px) saturate(180%)',\n                border: '1px solid rgba(102, 126, 234, 0.2)',\n                borderRadius: 4,\n                position: 'relative',\n                overflow: 'hidden',\n                boxShadow: '0 8px 32px rgba(102, 126, 234, 0.2)',\n                '&::before': {\n                  content: '\"\"',\n                  position: 'absolute',\n                  top: 0,\n                  left: 0,\n                  right: 0,\n                  height: '4px',\n                  background: 'linear-gradient(90deg, #667eea 0%, #764ba2 50%, #f093fb 100%)'\n                }\n              },\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                sx: {\n                  p: 3\n                },\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  display: \"flex\",\n                  alignItems: \"center\",\n                  gap: 1,\n                  mb: 3,\n                  children: [/*#__PURE__*/_jsxDEV(SecurityIcon, {\n                    color: \"primary\",\n                    sx: {\n                      fontSize: 28\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 495,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"h6\",\n                    fontWeight: \"bold\",\n                    children: \"Security Best Practices\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 496,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 494,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  component: \"ul\",\n                  sx: {\n                    pl: 2,\n                    m: 0,\n                    '& li': {\n                      mb: 1.5\n                    }\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    component: \"li\",\n                    variant: \"body2\",\n                    sx: {\n                      lineHeight: 1.6\n                    },\n                    children: \"Always verify URLs before clicking, especially in emails\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 500,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    component: \"li\",\n                    variant: \"body2\",\n                    sx: {\n                      lineHeight: 1.6\n                    },\n                    children: \"Scan files from unknown sources before opening\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 503,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    component: \"li\",\n                    variant: \"body2\",\n                    sx: {\n                      lineHeight: 1.6\n                    },\n                    children: \"Keep your antivirus software updated\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 506,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    component: \"li\",\n                    variant: \"body2\",\n                    sx: {\n                      lineHeight: 1.6\n                    },\n                    children: \"Use strong, unique passwords for all accounts\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 509,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    component: \"li\",\n                    variant: \"body2\",\n                    sx: {\n                      lineHeight: 1.6\n                    },\n                    children: \"Enable two-factor authentication when available\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 512,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    component: \"li\",\n                    variant: \"body2\",\n                    sx: {\n                      lineHeight: 1.6\n                    },\n                    children: \"Regularly backup your important data\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 515,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 499,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 493,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 470,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Card, {\n              sx: {\n                background: theme => theme.palette.mode === 'dark' ? 'linear-gradient(135deg, rgba(76, 175, 80, 0.15) 0%, rgba(139, 195, 74, 0.15) 100%)' : 'linear-gradient(135deg, rgba(76, 175, 80, 0.08) 0%, rgba(139, 195, 74, 0.08) 100%)',\n                backdropFilter: 'blur(20px) saturate(180%)',\n                border: '1px solid rgba(76, 175, 80, 0.2)',\n                borderRadius: 4,\n                position: 'relative',\n                overflow: 'hidden',\n                boxShadow: '0 8px 32px rgba(76, 175, 80, 0.2)',\n                '&::before': {\n                  content: '\"\"',\n                  position: 'absolute',\n                  top: 0,\n                  left: 0,\n                  right: 0,\n                  height: '4px',\n                  background: 'linear-gradient(90deg, #4caf50 0%, #8bc34a 100%)'\n                }\n              },\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                sx: {\n                  p: 3\n                },\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  display: \"flex\",\n                  alignItems: \"center\",\n                  gap: 1,\n                  mb: 3,\n                  children: [/*#__PURE__*/_jsxDEV(AssessmentIcon, {\n                    color: \"success\",\n                    sx: {\n                      fontSize: 28\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 547,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"h6\",\n                    fontWeight: \"bold\",\n                    children: \"Platform Features\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 548,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 546,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  children: [/*#__PURE__*/_jsxDEV(Box, {\n                    display: \"flex\",\n                    alignItems: \"center\",\n                    gap: 2,\n                    mb: 2,\n                    children: [/*#__PURE__*/_jsxDEV(LinkIcon, {\n                      color: \"primary\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 553,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      fontWeight: \"500\",\n                      children: \"Real-time URL threat detection\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 554,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 552,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    display: \"flex\",\n                    alignItems: \"center\",\n                    gap: 2,\n                    mb: 2,\n                    children: [/*#__PURE__*/_jsxDEV(FileIcon, {\n                      color: \"primary\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 560,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      fontWeight: \"500\",\n                      children: \"Advanced file malware scanning\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 561,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 559,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    display: \"flex\",\n                    alignItems: \"center\",\n                    gap: 2,\n                    mb: 2,\n                    children: [/*#__PURE__*/_jsxDEV(SecurityIcon, {\n                      color: \"primary\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 567,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      fontWeight: \"500\",\n                      children: \"AI-powered threat analysis\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 568,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 566,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    display: \"flex\",\n                    alignItems: \"center\",\n                    gap: 2,\n                    children: [/*#__PURE__*/_jsxDEV(AssessmentIcon, {\n                      color: \"primary\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 574,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      fontWeight: \"500\",\n                      children: \"Detailed security reports\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 575,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 573,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 551,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 545,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 523,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 460,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 343,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 342,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 121,\n      columnNumber: 7\n    }, this), notifications.map(notification => /*#__PURE__*/_jsxDEV(Snackbar, {\n      open: true,\n      autoHideDuration: 6000,\n      onClose: () => handleCloseNotification(notification.id),\n      anchorOrigin: {\n        vertical: 'bottom',\n        horizontal: 'right'\n      },\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        onClose: () => handleCloseNotification(notification.id),\n        severity: notification.type,\n        variant: \"filled\",\n        sx: {\n          width: '100%',\n          borderRadius: 2,\n          boxShadow: '0 8px 32px rgba(0,0,0,0.12)'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle2\",\n          fontWeight: \"bold\",\n          children: notification.title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 606,\n          columnNumber: 13\n        }, this), notification.message && /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          children: notification.message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 610,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 596,\n        columnNumber: 11\n      }, this)\n    }, notification.id, false, {\n      fileName: _jsxFileName,\n      lineNumber: 589,\n      columnNumber: 9\n    }, this)), notification && /*#__PURE__*/_jsxDEV(Snackbar, {\n      open: true,\n      autoHideDuration: 4000,\n      onClose: () => setNotification(null),\n      anchorOrigin: {\n        vertical: 'bottom',\n        horizontal: 'center'\n      },\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        onClose: () => setNotification(null),\n        severity: notification.type,\n        variant: \"filled\",\n        sx: {\n          borderRadius: 2,\n          boxShadow: '0 8px 32px rgba(0,0,0,0.12)'\n        },\n        children: notification.message\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 626,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 620,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 74,\n    columnNumber: 5\n  }, this);\n}, \"UfI/CENxLvYOx40AaSKFcfyM5pQ=\", false, function () {\n  return [useNavigate, useScan];\n})), \"UfI/CENxLvYOx40AaSKFcfyM5pQ=\", false, function () {\n  return [useNavigate, useScan];\n});\n_c2 = HomePage;\nHomePage.displayName = 'HomePage';\nexport default HomePage;\nvar _c, _c2;\n$RefreshReg$(_c, \"HomePage$React.memo\");\n$RefreshReg$(_c2, \"HomePage\");", "map": {"version": 3, "names": ["React", "useState", "Container", "Grid", "Box", "Typography", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Tabs", "Tab", "<PERSON><PERSON>", "Snackbar", "Fade", "<PERSON><PERSON>", "Chip", "Security", "SecurityIcon", "Link", "LinkIcon", "InsertDriveFile", "FileIcon", "Assessment", "AssessmentIcon", "useNavigate", "useScan", "LinkScanner", "FileUploader", "ResultView", "ThreatMeter", "SmartDashboard", "ThreatPrediction", "AutoScanSystem", "SmartNotifications", "jsxDEV", "_jsxDEV", "HomePage", "_s", "memo", "_c", "activeTab", "setActiveTab", "notification", "setNotification", "navigate", "currentScan", "isScanning", "threatLevel", "notifications", "removeNotification", "handleTabChange", "event", "newValue", "handleCloseNotification", "notificationId", "handleDownloadReport", "scanResult", "console", "log", "type", "message", "handleShareResult", "sx", "width", "minHeight", "display", "flexDirection", "justifyContent", "alignItems", "py", "position", "background", "content", "top", "left", "right", "bottom", "backgroundImage", "zIndex", "animation", "transform", "children", "max<PERSON><PERSON><PERSON>", "textAlign", "mb", "px", "borderRadius", "overflow", "backgroundSize", "theme", "palette", "mode", "<PERSON><PERSON>ilter", "backgroundPosition", "opacity", "height", "mx", "boxShadow", "border", "fontSize", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "component", "gutterBottom", "fontWeight", "backgroundClip", "WebkitBackgroundClip", "WebkitTextFillColor", "xs", "md", "lineHeight", "gap", "flexWrap", "label", "gradient", "map", "feature", "index", "borderColor", "transition", "size", "onClick", "borderWidth", "container", "spacing", "item", "lg", "p", "borderBottom", "value", "onChange", "textColor", "indicatorColor", "textTransform", "icon", "iconPosition", "in", "timeout", "onDownloadReport", "onShareResult", "threatData", "showDetails", "pl", "m", "open", "autoHideDuration", "onClose", "id", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "severity", "title", "_c2", "displayName", "$RefreshReg$"], "sources": ["E:/PythonProjects/React.js/Smart AI Assisstant/src/pages/HomePage.jsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  Container,\n  Grid,\n  Box,\n  Typography,\n  Card,\n  CardContent,\n  Tabs,\n  Tab,\n  Alert,\n  <PERSON>nackbar,\n  Fade,\n  Button,\n  Chip,\n} from '@mui/material';\nimport {\n  Security as SecurityIcon,\n  Link as LinkIcon,\n  InsertDriveFile as FileIcon,\n  Assessment as AssessmentIcon,\n} from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport { useScan } from '../contexts/ScanContext';\nimport LinkScanner from '../components/LinkScanner';\nimport FileUploader from '../components/FileUploader';\nimport ResultView from '../components/ResultView';\nimport ThreatMeter from '../components/ThreatMeter';\nimport SmartDashboard from '../components/SmartDashboard';\nimport ThreatPrediction from '../components/ThreatPrediction';\nimport AutoScanSystem from '../components/AutoScanSystem';\nimport SmartNotifications from '../components/SmartNotifications';\n\nconst HomePage = React.memo(() => {\n  const [activeTab, setActiveTab] = useState(0);\n  const [notification, setNotification] = useState(null);\n  const navigate = useNavigate();\n\n  const {\n    currentScan,\n    isScanning,\n    threatLevel,\n    notifications,\n    removeNotification\n  } = useScan();\n\n  const handleTabChange = (event, newValue) => {\n    setActiveTab(newValue);\n  };\n\n  const handleCloseNotification = (notificationId) => {\n    removeNotification(notificationId);\n  };\n\n  const handleDownloadReport = (scanResult) => {\n    // This would integrate with a PDF generation service\n    console.log('Downloading report for:', scanResult);\n    setNotification({\n      type: 'info',\n      message: 'Report download feature will be available soon.',\n    });\n  };\n\n  const handleShareResult = (scanResult) => {\n    // This would integrate with sharing functionality\n    console.log('Sharing result:', scanResult);\n    setNotification({\n      type: 'info',\n      message: 'Result sharing feature will be available soon.',\n    });\n  };\n\n  return (\n    <Box\n      sx={{\n        width: '100%',\n        minHeight: '100vh',\n        display: 'flex',\n        flexDirection: 'column',\n        justifyContent: 'center',\n        alignItems: 'center',\n        py: 4,\n        position: 'relative',\n        background: 'linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 50%, #121212 100%)',\n        '&::before': {\n          content: '\"\"',\n          position: 'absolute',\n          top: 0,\n          left: 0,\n          right: 0,\n          bottom: 0,\n          backgroundImage: `\n            radial-gradient(circle at 20% 50%, rgba(102, 126, 234, 0.1) 0%, transparent 50%),\n            radial-gradient(circle at 80% 20%, rgba(118, 75, 162, 0.1) 0%, transparent 50%),\n            radial-gradient(circle at 40% 80%, rgba(240, 147, 251, 0.08) 0%, transparent 50%),\n            linear-gradient(135deg, rgba(15, 15, 15, 0.6) 0%, rgba(26, 26, 26, 0.6) 100%)\n          `,\n          zIndex: -2,\n        },\n        '&::after': {\n          content: '\"\"',\n          position: 'absolute',\n          top: 0,\n          left: 0,\n          right: 0,\n          bottom: 0,\n          backgroundImage: `\n            url(\"data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23667eea' fill-opacity='0.03'%3E%3Ccircle cx='30' cy='30' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E\")\n          `,\n          animation: 'particleFloat 20s linear infinite',\n          zIndex: -1,\n        },\n        '@keyframes particleFloat': {\n          '0%': { transform: 'translateY(0px) translateX(0px)' },\n          '33%': { transform: 'translateY(-10px) translateX(5px)' },\n          '66%': { transform: 'translateY(5px) translateX(-5px)' },\n          '100%': { transform: 'translateY(0px) translateX(0px)' },\n        },\n      }}\n    >\n      <Container maxWidth=\"xl\">\n        {/* Professional Hero Section - Quantum Shield */}\n        <Box\n          textAlign=\"center\"\n          mb={8}\n          sx={{\n            position: 'relative',\n            py: 8,\n            px: 4,\n            borderRadius: 4,\n            overflow: 'hidden',\n            '&::before': {\n              content: '\"\"',\n              position: 'absolute',\n              top: 0,\n              left: 0,\n              right: 0,\n              bottom: 0,\n              background: 'linear-gradient(45deg, rgba(102, 126, 234, 0.08) 0%, rgba(118, 75, 162, 0.08) 25%, rgba(240, 147, 251, 0.08) 50%, rgba(79, 172, 254, 0.08) 75%, rgba(102, 126, 234, 0.08) 100%)',\n              backgroundSize: '400% 400%',\n              animation: 'cyberWaves 15s ease infinite',\n              zIndex: -2,\n            },\n            '&::after': {\n              content: '\"\"',\n              position: 'absolute',\n              top: 0,\n              left: 0,\n              right: 0,\n              bottom: 0,\n              background: (theme) => theme.palette.mode === 'dark'\n                ? 'linear-gradient(135deg, rgba(12, 12, 12, 0.4) 0%, rgba(26, 26, 26, 0.4) 50%, rgba(45, 45, 45, 0.4) 100%)'\n                : 'linear-gradient(135deg, rgba(248, 250, 252, 0.6) 0%, rgba(241, 245, 249, 0.6) 50%, rgba(226, 232, 240, 0.6) 100%)',\n              backdropFilter: 'blur(20px) saturate(180%)',\n              zIndex: -1,\n            },\n            '@keyframes cyberWaves': {\n              '0%': { backgroundPosition: '0% 50%' },\n              '50%': { backgroundPosition: '100% 50%' },\n              '100%': { backgroundPosition: '0% 50%' },\n            },\n            '@keyframes neuralPulse': {\n              '0%': { backgroundPosition: '0% 50%' },\n              '50%': { backgroundPosition: '100% 50%' },\n              '100%': { backgroundPosition: '0% 50%' },\n            },\n            '@keyframes quantumGlow': {\n              '0%': { transform: 'scale(1)', opacity: 0.3 },\n              '50%': { transform: 'scale(1.05)', opacity: 0.1 },\n              '100%': { transform: 'scale(1)', opacity: 0.3 },\n            },\n          }}\n        >\n          {/* Quantum Shield Icon */}\n          <Box\n            sx={{\n              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n              borderRadius: '50%',\n              width: 140,\n              height: 140,\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              mx: 'auto',\n              mb: 4,\n              boxShadow: '0 20px 60px rgba(102, 126, 234, 0.4)',\n              position: 'relative',\n              '&::before': {\n                content: '\"\"',\n                position: 'absolute',\n                top: -8,\n                left: -8,\n                right: -8,\n                bottom: -8,\n                borderRadius: '50%',\n                background: 'linear-gradient(45deg, #667eea, #764ba2, #f093fb, #f5576c)',\n                backgroundSize: '400% 400%',\n                animation: 'neuralPulse 3s ease infinite',\n                opacity: 0.7,\n                zIndex: -1,\n              },\n              '&::after': {\n                content: '\"\"',\n                position: 'absolute',\n                top: -15,\n                left: -15,\n                right: -15,\n                bottom: -15,\n                borderRadius: '50%',\n                border: '3px solid rgba(102, 126, 234, 0.3)',\n                animation: 'quantumGlow 2s infinite',\n              },\n            }}\n          >\n            <SecurityIcon sx={{ fontSize: 70, color: 'white' }} />\n          </Box>\n\n          {/* Neural Pulse Title */}\n          <Typography\n            variant=\"h1\"\n            component=\"h1\"\n            gutterBottom\n            fontWeight=\"bold\"\n            sx={{\n              background: 'linear-gradient(45deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%)',\n              backgroundSize: '400% 400%',\n              backgroundClip: 'text',\n              WebkitBackgroundClip: 'text',\n              WebkitTextFillColor: 'transparent',\n              animation: 'neuralPulse 8s ease infinite',\n              mb: 3,\n              fontSize: { xs: '2.5rem', md: '4rem' },\n            }}\n          >\n            AI Security Guard\n          </Typography>\n\n          <Typography\n            variant=\"h4\"\n            color=\"text.secondary\"\n            maxWidth=\"900px\"\n            mx=\"auto\"\n            sx={{\n              mb: 6,\n              lineHeight: 1.6,\n              fontWeight: 300,\n              fontSize: { xs: '1.2rem', md: '1.5rem' },\n            }}\n          >\n            Next-generation cybersecurity powered by advanced artificial intelligence.\n            Protect your digital assets with real-time threat detection and predictive analytics.\n          </Typography>\n          {/* Professional Feature Chips */}\n          <Box\n            sx={{\n              display: 'flex',\n              justifyContent: 'center',\n              gap: 3,\n              flexWrap: 'wrap',\n              mb: 6,\n            }}\n          >\n            {[\n              { label: 'Real-time Analytics', gradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' },\n              { label: 'AI Threat Prediction', gradient: 'linear-gradient(135deg, #9c27b0 0%, #e91e63 100%)' },\n              { label: 'Automated Response', gradient: 'linear-gradient(135deg, #4caf50 0%, #8bc34a 100%)' },\n              { label: 'Smart Notifications', gradient: 'linear-gradient(135deg, #2196f3 0%, #9c27b0 100%)' },\n            ].map((feature, index) => (\n              <Chip\n                key={index}\n                label={feature.label}\n                variant=\"outlined\"\n                sx={{\n                  borderColor: 'transparent',\n                  background: feature.gradient,\n                  color: 'white',\n                  fontWeight: 600,\n                  fontSize: '1rem',\n                  px: 3,\n                  py: 2,\n                  height: 'auto',\n                  borderRadius: 3,\n                  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',\n                  boxShadow: '0 4px 15px rgba(0,0,0,0.2)',\n                  '&:hover': {\n                    transform: 'translateY(-3px) scale(1.05)',\n                    boxShadow: '0 8px 25px rgba(0,0,0,0.3)',\n                  },\n                }}\n              />\n            ))}\n          </Box>\n\n          {/* CTA Buttons */}\n          <Box display=\"flex\" justifyContent=\"center\" gap={3} flexWrap=\"wrap\">\n            <Button\n              variant=\"contained\"\n              size=\"large\"\n              sx={{\n                px: 6,\n                py: 2,\n                borderRadius: 3,\n                fontSize: '1.1rem',\n                fontWeight: 600,\n                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                boxShadow: '0 8px 25px rgba(102, 126, 234, 0.4)',\n                '&:hover': {\n                  background: 'linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)',\n                  transform: 'translateY(-2px)',\n                  boxShadow: '0 12px 35px rgba(102, 126, 234, 0.5)',\n                },\n              }}\n            >\n              Start Security Scan\n            </Button>\n            <Button\n              variant=\"outlined\"\n              size=\"large\"\n              onClick={() => navigate('/smart-features')}\n              sx={{\n                px: 6,\n                py: 2,\n                borderRadius: 3,\n                fontSize: '1.1rem',\n                fontWeight: 600,\n                borderColor: 'primary.main',\n                color: 'primary.main',\n                borderWidth: 2,\n                '&:hover': {\n                  borderWidth: 2,\n                  background: 'linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%)',\n                  transform: 'translateY(-2px)',\n                  boxShadow: '0 8px 25px rgba(102, 126, 234, 0.2)',\n                },\n              }}\n            >\n              Explore AI Features\n            </Button>\n          </Box>\n        </Box>\n\n        <Box display=\"flex\" justifyContent=\"center\" width=\"100%\">\n          <Grid container spacing={4} maxWidth=\"1400px\">\n            {/* Left Column - Scanning Tools */}\n            <Grid item xs={12} lg={8}>\n              <Card\n                sx={{\n                  mb: 4,\n                  background: (theme) => theme.palette.mode === 'dark'\n                    ? 'linear-gradient(135deg, rgba(12, 12, 12, 0.95) 0%, rgba(26, 26, 26, 0.95) 50%, rgba(45, 45, 45, 0.95) 100%)'\n                    : 'linear-gradient(135deg, rgba(248, 250, 252, 0.95) 0%, rgba(241, 245, 249, 0.95) 50%, rgba(226, 232, 240, 0.95) 100%)',\n                  backdropFilter: 'blur(25px) saturate(180%)',\n                  border: (theme) => theme.palette.mode === 'dark'\n                    ? '1px solid rgba(255, 255, 255, 0.1)'\n                    : '1px solid rgba(0, 0, 0, 0.1)',\n                  borderRadius: 4,\n                  overflow: 'hidden',\n                  position: 'relative',\n                  boxShadow: '0 8px 32px rgba(102, 126, 234, 0.15)',\n                  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',\n                  '&:hover': {\n                    transform: 'translateY(-2px)',\n                    boxShadow: '0 16px 48px rgba(102, 126, 234, 0.2)',\n                  },\n                }}\n              >\n                <CardContent sx={{ p: 4 }}>\n                  {/* Tabs for different scan types */}\n                  <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 4 }}>\n                    <Tabs\n                      value={activeTab}\n                      onChange={handleTabChange}\n                      variant=\"fullWidth\"\n                      textColor=\"primary\"\n                      indicatorColor=\"primary\"\n                      sx={{\n                        '& .MuiTab-root': {\n                          minHeight: 72,\n                          fontSize: '1rem',\n                          fontWeight: 600,\n                          textTransform: 'none',\n                        },\n                      }}\n                    >\n                      <Tab\n                        icon={<LinkIcon sx={{ fontSize: 28 }} />}\n                        label=\"URL Security Scanner\"\n                        iconPosition=\"start\"\n                      />\n                      <Tab\n                        icon={<FileIcon sx={{ fontSize: 28 }} />}\n                        label=\"File Security Scanner\"\n                        iconPosition=\"start\"\n                      />\n                      <Tab\n                        icon={<SecurityIcon sx={{ fontSize: 28 }} />}\n                        label=\"Smart Dashboard\"\n                        iconPosition=\"start\"\n                      />\n                      <Tab\n                        icon={<AssessmentIcon sx={{ fontSize: 28 }} />}\n                        label=\"Threat Prediction\"\n                        iconPosition=\"start\"\n                      />\n                    </Tabs>\n                  </Box>\n\n                  {/* Tab Panels */}\n                  <Box>\n                    {activeTab === 0 && (\n                      <Fade in={activeTab === 0} timeout={300}>\n                        <Box>\n                          <LinkScanner />\n                        </Box>\n                      </Fade>\n                    )}\n\n                    {activeTab === 1 && (\n                      <Fade in={activeTab === 1} timeout={300}>\n                        <Box>\n                          <FileUploader />\n                        </Box>\n                      </Fade>\n                    )}\n\n                    {activeTab === 2 && (\n                      <Fade in={activeTab === 2} timeout={300}>\n                        <Box>\n                          <SmartDashboard />\n                        </Box>\n                      </Fade>\n                    )}\n\n                    {activeTab === 3 && (\n                      <Fade in={activeTab === 3} timeout={300}>\n                        <Box>\n                          <ThreatPrediction />\n                        </Box>\n                      </Fade>\n                    )}\n                  </Box>\n                </CardContent>\n              </Card>\n\n              {/* Results Section */}\n              {(currentScan || isScanning) && (\n                <Fade in={true} timeout={500}>\n                  <Box>\n                    <ResultView\n                      scanResult={currentScan}\n                      onDownloadReport={handleDownloadReport}\n                      onShareResult={handleShareResult}\n                    />\n                  </Box>\n                </Fade>\n              )}\n            </Grid>\n\n            {/* Right Column - Threat Meter and Info */}\n            <Grid item xs={12} lg={4}>\n              {/* Threat Level Indicator */}\n              <Box mb={4}>\n                <ThreatMeter\n                  threatData={currentScan}\n                  showDetails={true}\n                />\n              </Box>\n\n              {/* Security Tips Card - Cyber Pulse */}\n              <Card\n                sx={{\n                  mb: 4,\n                  background: (theme) => theme.palette.mode === 'dark'\n                    ? 'linear-gradient(135deg, rgba(102, 126, 234, 0.15) 0%, rgba(118, 75, 162, 0.15) 50%, rgba(240, 147, 251, 0.15) 100%)'\n                    : 'linear-gradient(135deg, rgba(102, 126, 234, 0.08) 0%, rgba(118, 75, 162, 0.08) 50%, rgba(240, 147, 251, 0.08) 100%)',\n                  backdropFilter: 'blur(20px) saturate(180%)',\n                  border: '1px solid rgba(102, 126, 234, 0.2)',\n                  borderRadius: 4,\n                  position: 'relative',\n                  overflow: 'hidden',\n                  boxShadow: '0 8px 32px rgba(102, 126, 234, 0.2)',\n                  '&::before': {\n                    content: '\"\"',\n                    position: 'absolute',\n                    top: 0,\n                    left: 0,\n                    right: 0,\n                    height: '4px',\n                    background: 'linear-gradient(90deg, #667eea 0%, #764ba2 50%, #f093fb 100%)',\n                  },\n                }}\n              >\n                <CardContent sx={{ p: 3 }}>\n                  <Box display=\"flex\" alignItems=\"center\" gap={1} mb={3}>\n                    <SecurityIcon color=\"primary\" sx={{ fontSize: 28 }} />\n                    <Typography variant=\"h6\" fontWeight=\"bold\">Security Best Practices</Typography>\n                  </Box>\n\n                  <Box component=\"ul\" sx={{ pl: 2, m: 0, '& li': { mb: 1.5 } }}>\n                    <Typography component=\"li\" variant=\"body2\" sx={{ lineHeight: 1.6 }}>\n                      Always verify URLs before clicking, especially in emails\n                    </Typography>\n                    <Typography component=\"li\" variant=\"body2\" sx={{ lineHeight: 1.6 }}>\n                      Scan files from unknown sources before opening\n                    </Typography>\n                    <Typography component=\"li\" variant=\"body2\" sx={{ lineHeight: 1.6 }}>\n                      Keep your antivirus software updated\n                    </Typography>\n                    <Typography component=\"li\" variant=\"body2\" sx={{ lineHeight: 1.6 }}>\n                      Use strong, unique passwords for all accounts\n                    </Typography>\n                    <Typography component=\"li\" variant=\"body2\" sx={{ lineHeight: 1.6 }}>\n                      Enable two-factor authentication when available\n                    </Typography>\n                    <Typography component=\"li\" variant=\"body2\" sx={{ lineHeight: 1.6 }}>\n                      Regularly backup your important data\n                    </Typography>\n                  </Box>\n                </CardContent>\n              </Card>\n\n              {/* Features Card - AI Intelligence */}\n              <Card\n                sx={{\n                  background: (theme) => theme.palette.mode === 'dark'\n                    ? 'linear-gradient(135deg, rgba(76, 175, 80, 0.15) 0%, rgba(139, 195, 74, 0.15) 100%)'\n                    : 'linear-gradient(135deg, rgba(76, 175, 80, 0.08) 0%, rgba(139, 195, 74, 0.08) 100%)',\n                  backdropFilter: 'blur(20px) saturate(180%)',\n                  border: '1px solid rgba(76, 175, 80, 0.2)',\n                  borderRadius: 4,\n                  position: 'relative',\n                  overflow: 'hidden',\n                  boxShadow: '0 8px 32px rgba(76, 175, 80, 0.2)',\n                  '&::before': {\n                    content: '\"\"',\n                    position: 'absolute',\n                    top: 0,\n                    left: 0,\n                    right: 0,\n                    height: '4px',\n                    background: 'linear-gradient(90deg, #4caf50 0%, #8bc34a 100%)',\n                  },\n                }}\n              >\n                <CardContent sx={{ p: 3 }}>\n                  <Box display=\"flex\" alignItems=\"center\" gap={1} mb={3}>\n                    <AssessmentIcon color=\"success\" sx={{ fontSize: 28 }} />\n                    <Typography variant=\"h6\" fontWeight=\"bold\">Platform Features</Typography>\n                  </Box>\n\n                  <Box>\n                    <Box display=\"flex\" alignItems=\"center\" gap={2} mb={2}>\n                      <LinkIcon color=\"primary\" />\n                      <Typography variant=\"body2\" fontWeight=\"500\">\n                        Real-time URL threat detection\n                      </Typography>\n                    </Box>\n\n                    <Box display=\"flex\" alignItems=\"center\" gap={2} mb={2}>\n                      <FileIcon color=\"primary\" />\n                      <Typography variant=\"body2\" fontWeight=\"500\">\n                        Advanced file malware scanning\n                      </Typography>\n                    </Box>\n\n                    <Box display=\"flex\" alignItems=\"center\" gap={2} mb={2}>\n                      <SecurityIcon color=\"primary\" />\n                      <Typography variant=\"body2\" fontWeight=\"500\">\n                        AI-powered threat analysis\n                      </Typography>\n                    </Box>\n\n                    <Box display=\"flex\" alignItems=\"center\" gap={2}>\n                      <AssessmentIcon color=\"primary\" />\n                      <Typography variant=\"body2\" fontWeight=\"500\">\n                        Detailed security reports\n                      </Typography>\n                    </Box>\n                  </Box>\n                </CardContent>\n              </Card>\n            </Grid>\n          </Grid>\n        </Box>\n      </Container>\n\n      {/* Notifications */}\n      {notifications.map((notification) => (\n        <Snackbar\n          key={notification.id}\n          open={true}\n          autoHideDuration={6000}\n          onClose={() => handleCloseNotification(notification.id)}\n          anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}\n        >\n          <Alert\n            onClose={() => handleCloseNotification(notification.id)}\n            severity={notification.type}\n            variant=\"filled\"\n            sx={{\n              width: '100%',\n              borderRadius: 2,\n              boxShadow: '0 8px 32px rgba(0,0,0,0.12)',\n            }}\n          >\n            <Typography variant=\"subtitle2\" fontWeight=\"bold\">\n              {notification.title}\n            </Typography>\n            {notification.message && (\n              <Typography variant=\"body2\">\n                {notification.message}\n              </Typography>\n            )}\n          </Alert>\n        </Snackbar>\n      ))}\n\n      {/* Custom notification */}\n      {notification && (\n        <Snackbar\n          open={true}\n          autoHideDuration={4000}\n          onClose={() => setNotification(null)}\n          anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}\n        >\n          <Alert\n            onClose={() => setNotification(null)}\n            severity={notification.type}\n            variant=\"filled\"\n            sx={{\n              borderRadius: 2,\n              boxShadow: '0 8px 32px rgba(0,0,0,0.12)',\n            }}\n          >\n            {notification.message}\n          </Alert>\n        </Snackbar>\n      )}\n    </Box>\n  );\n});\n\nHomePage.displayName = 'HomePage';\n\nexport default HomePage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,SAAS,EACTC,IAAI,EACJC,GAAG,EACHC,UAAU,EACVC,IAAI,EACJC,WAAW,EACXC,IAAI,EACJC,GAAG,EACHC,KAAK,EACLC,QAAQ,EACRC,IAAI,EACJC,MAAM,EACNC,IAAI,QACC,eAAe;AACtB,SACEC,QAAQ,IAAIC,YAAY,EACxBC,IAAI,IAAIC,QAAQ,EAChBC,eAAe,IAAIC,QAAQ,EAC3BC,UAAU,IAAIC,cAAc,QACvB,qBAAqB;AAC5B,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,OAAO,QAAQ,yBAAyB;AACjD,OAAOC,WAAW,MAAM,2BAA2B;AACnD,OAAOC,YAAY,MAAM,4BAA4B;AACrD,OAAOC,UAAU,MAAM,0BAA0B;AACjD,OAAOC,WAAW,MAAM,2BAA2B;AACnD,OAAOC,cAAc,MAAM,8BAA8B;AACzD,OAAOC,gBAAgB,MAAM,gCAAgC;AAC7D,OAAOC,cAAc,MAAM,8BAA8B;AACzD,OAAOC,kBAAkB,MAAM,kCAAkC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElE,MAAMC,QAAQ,gBAAAC,EAAA,cAAGpC,KAAK,CAACqC,IAAI,CAAAC,EAAA,GAAAF,EAAA,CAAC,MAAM;EAAAA,EAAA;EAChC,MAAM,CAACG,SAAS,EAAEC,YAAY,CAAC,GAAGvC,QAAQ,CAAC,CAAC,CAAC;EAC7C,MAAM,CAACwC,YAAY,EAAEC,eAAe,CAAC,GAAGzC,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM0C,QAAQ,GAAGpB,WAAW,CAAC,CAAC;EAE9B,MAAM;IACJqB,WAAW;IACXC,UAAU;IACVC,WAAW;IACXC,aAAa;IACbC;EACF,CAAC,GAAGxB,OAAO,CAAC,CAAC;EAEb,MAAMyB,eAAe,GAAGA,CAACC,KAAK,EAAEC,QAAQ,KAAK;IAC3CX,YAAY,CAACW,QAAQ,CAAC;EACxB,CAAC;EAED,MAAMC,uBAAuB,GAAIC,cAAc,IAAK;IAClDL,kBAAkB,CAACK,cAAc,CAAC;EACpC,CAAC;EAED,MAAMC,oBAAoB,GAAIC,UAAU,IAAK;IAC3C;IACAC,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEF,UAAU,CAAC;IAClDb,eAAe,CAAC;MACdgB,IAAI,EAAE,MAAM;MACZC,OAAO,EAAE;IACX,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,iBAAiB,GAAIL,UAAU,IAAK;IACxC;IACAC,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEF,UAAU,CAAC;IAC1Cb,eAAe,CAAC;MACdgB,IAAI,EAAE,MAAM;MACZC,OAAO,EAAE;IACX,CAAC,CAAC;EACJ,CAAC;EAED,oBACEzB,OAAA,CAAC9B,GAAG;IACFyD,EAAE,EAAE;MACFC,KAAK,EAAE,MAAM;MACbC,SAAS,EAAE,OAAO;MAClBC,OAAO,EAAE,MAAM;MACfC,aAAa,EAAE,QAAQ;MACvBC,cAAc,EAAE,QAAQ;MACxBC,UAAU,EAAE,QAAQ;MACpBC,EAAE,EAAE,CAAC;MACLC,QAAQ,EAAE,UAAU;MACpBC,UAAU,EAAE,gEAAgE;MAC5E,WAAW,EAAE;QACXC,OAAO,EAAE,IAAI;QACbF,QAAQ,EAAE,UAAU;QACpBG,GAAG,EAAE,CAAC;QACNC,IAAI,EAAE,CAAC;QACPC,KAAK,EAAE,CAAC;QACRC,MAAM,EAAE,CAAC;QACTC,eAAe,EAAE;AAC3B;AACA;AACA;AACA;AACA,WAAW;QACDC,MAAM,EAAE,CAAC;MACX,CAAC;MACD,UAAU,EAAE;QACVN,OAAO,EAAE,IAAI;QACbF,QAAQ,EAAE,UAAU;QACpBG,GAAG,EAAE,CAAC;QACNC,IAAI,EAAE,CAAC;QACPC,KAAK,EAAE,CAAC;QACRC,MAAM,EAAE,CAAC;QACTC,eAAe,EAAE;AAC3B;AACA,WAAW;QACDE,SAAS,EAAE,mCAAmC;QAC9CD,MAAM,EAAE,CAAC;MACX,CAAC;MACD,0BAA0B,EAAE;QAC1B,IAAI,EAAE;UAAEE,SAAS,EAAE;QAAkC,CAAC;QACtD,KAAK,EAAE;UAAEA,SAAS,EAAE;QAAoC,CAAC;QACzD,KAAK,EAAE;UAAEA,SAAS,EAAE;QAAmC,CAAC;QACxD,MAAM,EAAE;UAAEA,SAAS,EAAE;QAAkC;MACzD;IACF,CAAE;IAAAC,QAAA,gBAEF9C,OAAA,CAAChC,SAAS;MAAC+E,QAAQ,EAAC,IAAI;MAAAD,QAAA,gBAEtB9C,OAAA,CAAC9B,GAAG;QACF8E,SAAS,EAAC,QAAQ;QAClBC,EAAE,EAAE,CAAE;QACNtB,EAAE,EAAE;UACFQ,QAAQ,EAAE,UAAU;UACpBD,EAAE,EAAE,CAAC;UACLgB,EAAE,EAAE,CAAC;UACLC,YAAY,EAAE,CAAC;UACfC,QAAQ,EAAE,QAAQ;UAClB,WAAW,EAAE;YACXf,OAAO,EAAE,IAAI;YACbF,QAAQ,EAAE,UAAU;YACpBG,GAAG,EAAE,CAAC;YACNC,IAAI,EAAE,CAAC;YACPC,KAAK,EAAE,CAAC;YACRC,MAAM,EAAE,CAAC;YACTL,UAAU,EAAE,iLAAiL;YAC7LiB,cAAc,EAAE,WAAW;YAC3BT,SAAS,EAAE,8BAA8B;YACzCD,MAAM,EAAE,CAAC;UACX,CAAC;UACD,UAAU,EAAE;YACVN,OAAO,EAAE,IAAI;YACbF,QAAQ,EAAE,UAAU;YACpBG,GAAG,EAAE,CAAC;YACNC,IAAI,EAAE,CAAC;YACPC,KAAK,EAAE,CAAC;YACRC,MAAM,EAAE,CAAC;YACTL,UAAU,EAAGkB,KAAK,IAAKA,KAAK,CAACC,OAAO,CAACC,IAAI,KAAK,MAAM,GAChD,0GAA0G,GAC1G,mHAAmH;YACvHC,cAAc,EAAE,2BAA2B;YAC3Cd,MAAM,EAAE,CAAC;UACX,CAAC;UACD,uBAAuB,EAAE;YACvB,IAAI,EAAE;cAAEe,kBAAkB,EAAE;YAAS,CAAC;YACtC,KAAK,EAAE;cAAEA,kBAAkB,EAAE;YAAW,CAAC;YACzC,MAAM,EAAE;cAAEA,kBAAkB,EAAE;YAAS;UACzC,CAAC;UACD,wBAAwB,EAAE;YACxB,IAAI,EAAE;cAAEA,kBAAkB,EAAE;YAAS,CAAC;YACtC,KAAK,EAAE;cAAEA,kBAAkB,EAAE;YAAW,CAAC;YACzC,MAAM,EAAE;cAAEA,kBAAkB,EAAE;YAAS;UACzC,CAAC;UACD,wBAAwB,EAAE;YACxB,IAAI,EAAE;cAAEb,SAAS,EAAE,UAAU;cAAEc,OAAO,EAAE;YAAI,CAAC;YAC7C,KAAK,EAAE;cAAEd,SAAS,EAAE,aAAa;cAAEc,OAAO,EAAE;YAAI,CAAC;YACjD,MAAM,EAAE;cAAEd,SAAS,EAAE,UAAU;cAAEc,OAAO,EAAE;YAAI;UAChD;QACF,CAAE;QAAAb,QAAA,gBAGF9C,OAAA,CAAC9B,GAAG;UACFyD,EAAE,EAAE;YACFS,UAAU,EAAE,mDAAmD;YAC/De,YAAY,EAAE,KAAK;YACnBvB,KAAK,EAAE,GAAG;YACVgC,MAAM,EAAE,GAAG;YACX9B,OAAO,EAAE,MAAM;YACfG,UAAU,EAAE,QAAQ;YACpBD,cAAc,EAAE,QAAQ;YACxB6B,EAAE,EAAE,MAAM;YACVZ,EAAE,EAAE,CAAC;YACLa,SAAS,EAAE,sCAAsC;YACjD3B,QAAQ,EAAE,UAAU;YACpB,WAAW,EAAE;cACXE,OAAO,EAAE,IAAI;cACbF,QAAQ,EAAE,UAAU;cACpBG,GAAG,EAAE,CAAC,CAAC;cACPC,IAAI,EAAE,CAAC,CAAC;cACRC,KAAK,EAAE,CAAC,CAAC;cACTC,MAAM,EAAE,CAAC,CAAC;cACVU,YAAY,EAAE,KAAK;cACnBf,UAAU,EAAE,4DAA4D;cACxEiB,cAAc,EAAE,WAAW;cAC3BT,SAAS,EAAE,8BAA8B;cACzCe,OAAO,EAAE,GAAG;cACZhB,MAAM,EAAE,CAAC;YACX,CAAC;YACD,UAAU,EAAE;cACVN,OAAO,EAAE,IAAI;cACbF,QAAQ,EAAE,UAAU;cACpBG,GAAG,EAAE,CAAC,EAAE;cACRC,IAAI,EAAE,CAAC,EAAE;cACTC,KAAK,EAAE,CAAC,EAAE;cACVC,MAAM,EAAE,CAAC,EAAE;cACXU,YAAY,EAAE,KAAK;cACnBY,MAAM,EAAE,oCAAoC;cAC5CnB,SAAS,EAAE;YACb;UACF,CAAE;UAAAE,QAAA,eAEF9C,OAAA,CAAClB,YAAY;YAAC6C,EAAE,EAAE;cAAEqC,QAAQ,EAAE,EAAE;cAAEC,KAAK,EAAE;YAAQ;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnD,CAAC,eAGNrE,OAAA,CAAC7B,UAAU;UACTmG,OAAO,EAAC,IAAI;UACZC,SAAS,EAAC,IAAI;UACdC,YAAY;UACZC,UAAU,EAAC,MAAM;UACjB9C,EAAE,EAAE;YACFS,UAAU,EAAE,yFAAyF;YACrGiB,cAAc,EAAE,WAAW;YAC3BqB,cAAc,EAAE,MAAM;YACtBC,oBAAoB,EAAE,MAAM;YAC5BC,mBAAmB,EAAE,aAAa;YAClChC,SAAS,EAAE,8BAA8B;YACzCK,EAAE,EAAE,CAAC;YACLe,QAAQ,EAAE;cAAEa,EAAE,EAAE,QAAQ;cAAEC,EAAE,EAAE;YAAO;UACvC,CAAE;UAAAhC,QAAA,EACH;QAED;UAAAoB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAEbrE,OAAA,CAAC7B,UAAU;UACTmG,OAAO,EAAC,IAAI;UACZL,KAAK,EAAC,gBAAgB;UACtBlB,QAAQ,EAAC,OAAO;UAChBc,EAAE,EAAC,MAAM;UACTlC,EAAE,EAAE;YACFsB,EAAE,EAAE,CAAC;YACL8B,UAAU,EAAE,GAAG;YACfN,UAAU,EAAE,GAAG;YACfT,QAAQ,EAAE;cAAEa,EAAE,EAAE,QAAQ;cAAEC,EAAE,EAAE;YAAS;UACzC,CAAE;UAAAhC,QAAA,EACH;QAGD;UAAAoB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAEbrE,OAAA,CAAC9B,GAAG;UACFyD,EAAE,EAAE;YACFG,OAAO,EAAE,MAAM;YACfE,cAAc,EAAE,QAAQ;YACxBgD,GAAG,EAAE,CAAC;YACNC,QAAQ,EAAE,MAAM;YAChBhC,EAAE,EAAE;UACN,CAAE;UAAAH,QAAA,EAED,CACC;YAAEoC,KAAK,EAAE,qBAAqB;YAAEC,QAAQ,EAAE;UAAoD,CAAC,EAC/F;YAAED,KAAK,EAAE,sBAAsB;YAAEC,QAAQ,EAAE;UAAoD,CAAC,EAChG;YAAED,KAAK,EAAE,oBAAoB;YAAEC,QAAQ,EAAE;UAAoD,CAAC,EAC9F;YAAED,KAAK,EAAE,qBAAqB;YAAEC,QAAQ,EAAE;UAAoD,CAAC,CAChG,CAACC,GAAG,CAAC,CAACC,OAAO,EAAEC,KAAK,kBACnBtF,OAAA,CAACpB,IAAI;YAEHsG,KAAK,EAAEG,OAAO,CAACH,KAAM;YACrBZ,OAAO,EAAC,UAAU;YAClB3C,EAAE,EAAE;cACF4D,WAAW,EAAE,aAAa;cAC1BnD,UAAU,EAAEiD,OAAO,CAACF,QAAQ;cAC5BlB,KAAK,EAAE,OAAO;cACdQ,UAAU,EAAE,GAAG;cACfT,QAAQ,EAAE,MAAM;cAChBd,EAAE,EAAE,CAAC;cACLhB,EAAE,EAAE,CAAC;cACL0B,MAAM,EAAE,MAAM;cACdT,YAAY,EAAE,CAAC;cACfqC,UAAU,EAAE,uCAAuC;cACnD1B,SAAS,EAAE,4BAA4B;cACvC,SAAS,EAAE;gBACTjB,SAAS,EAAE,8BAA8B;gBACzCiB,SAAS,EAAE;cACb;YACF;UAAE,GAnBGwB,KAAK;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAoBX,CACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGNrE,OAAA,CAAC9B,GAAG;UAAC4D,OAAO,EAAC,MAAM;UAACE,cAAc,EAAC,QAAQ;UAACgD,GAAG,EAAE,CAAE;UAACC,QAAQ,EAAC,MAAM;UAAAnC,QAAA,gBACjE9C,OAAA,CAACrB,MAAM;YACL2F,OAAO,EAAC,WAAW;YACnBmB,IAAI,EAAC,OAAO;YACZ9D,EAAE,EAAE;cACFuB,EAAE,EAAE,CAAC;cACLhB,EAAE,EAAE,CAAC;cACLiB,YAAY,EAAE,CAAC;cACfa,QAAQ,EAAE,QAAQ;cAClBS,UAAU,EAAE,GAAG;cACfrC,UAAU,EAAE,mDAAmD;cAC/D0B,SAAS,EAAE,qCAAqC;cAChD,SAAS,EAAE;gBACT1B,UAAU,EAAE,mDAAmD;gBAC/DS,SAAS,EAAE,kBAAkB;gBAC7BiB,SAAS,EAAE;cACb;YACF,CAAE;YAAAhB,QAAA,EACH;UAED;YAAAoB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTrE,OAAA,CAACrB,MAAM;YACL2F,OAAO,EAAC,UAAU;YAClBmB,IAAI,EAAC,OAAO;YACZC,OAAO,EAAEA,CAAA,KAAMjF,QAAQ,CAAC,iBAAiB,CAAE;YAC3CkB,EAAE,EAAE;cACFuB,EAAE,EAAE,CAAC;cACLhB,EAAE,EAAE,CAAC;cACLiB,YAAY,EAAE,CAAC;cACfa,QAAQ,EAAE,QAAQ;cAClBS,UAAU,EAAE,GAAG;cACfc,WAAW,EAAE,cAAc;cAC3BtB,KAAK,EAAE,cAAc;cACrB0B,WAAW,EAAE,CAAC;cACd,SAAS,EAAE;gBACTA,WAAW,EAAE,CAAC;gBACdvD,UAAU,EAAE,oFAAoF;gBAChGS,SAAS,EAAE,kBAAkB;gBAC7BiB,SAAS,EAAE;cACb;YACF,CAAE;YAAAhB,QAAA,EACH;UAED;YAAAoB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENrE,OAAA,CAAC9B,GAAG;QAAC4D,OAAO,EAAC,MAAM;QAACE,cAAc,EAAC,QAAQ;QAACJ,KAAK,EAAC,MAAM;QAAAkB,QAAA,eACtD9C,OAAA,CAAC/B,IAAI;UAAC2H,SAAS;UAACC,OAAO,EAAE,CAAE;UAAC9C,QAAQ,EAAC,QAAQ;UAAAD,QAAA,gBAE3C9C,OAAA,CAAC/B,IAAI;YAAC6H,IAAI;YAACjB,EAAE,EAAE,EAAG;YAACkB,EAAE,EAAE,CAAE;YAAAjD,QAAA,gBACvB9C,OAAA,CAAC5B,IAAI;cACHuD,EAAE,EAAE;gBACFsB,EAAE,EAAE,CAAC;gBACLb,UAAU,EAAGkB,KAAK,IAAKA,KAAK,CAACC,OAAO,CAACC,IAAI,KAAK,MAAM,GAChD,6GAA6G,GAC7G,sHAAsH;gBAC1HC,cAAc,EAAE,2BAA2B;gBAC3CM,MAAM,EAAGT,KAAK,IAAKA,KAAK,CAACC,OAAO,CAACC,IAAI,KAAK,MAAM,GAC5C,oCAAoC,GACpC,8BAA8B;gBAClCL,YAAY,EAAE,CAAC;gBACfC,QAAQ,EAAE,QAAQ;gBAClBjB,QAAQ,EAAE,UAAU;gBACpB2B,SAAS,EAAE,sCAAsC;gBACjD0B,UAAU,EAAE,uCAAuC;gBACnD,SAAS,EAAE;kBACT3C,SAAS,EAAE,kBAAkB;kBAC7BiB,SAAS,EAAE;gBACb;cACF,CAAE;cAAAhB,QAAA,eAEF9C,OAAA,CAAC3B,WAAW;gBAACsD,EAAE,EAAE;kBAAEqE,CAAC,EAAE;gBAAE,CAAE;gBAAAlD,QAAA,gBAExB9C,OAAA,CAAC9B,GAAG;kBAACyD,EAAE,EAAE;oBAAEsE,YAAY,EAAE,CAAC;oBAAEV,WAAW,EAAE,SAAS;oBAAEtC,EAAE,EAAE;kBAAE,CAAE;kBAAAH,QAAA,eAC1D9C,OAAA,CAAC1B,IAAI;oBACH4H,KAAK,EAAE7F,SAAU;oBACjB8F,QAAQ,EAAEpF,eAAgB;oBAC1BuD,OAAO,EAAC,WAAW;oBACnB8B,SAAS,EAAC,SAAS;oBACnBC,cAAc,EAAC,SAAS;oBACxB1E,EAAE,EAAE;sBACF,gBAAgB,EAAE;wBAChBE,SAAS,EAAE,EAAE;wBACbmC,QAAQ,EAAE,MAAM;wBAChBS,UAAU,EAAE,GAAG;wBACf6B,aAAa,EAAE;sBACjB;oBACF,CAAE;oBAAAxD,QAAA,gBAEF9C,OAAA,CAACzB,GAAG;sBACFgI,IAAI,eAAEvG,OAAA,CAAChB,QAAQ;wBAAC2C,EAAE,EAAE;0BAAEqC,QAAQ,EAAE;wBAAG;sBAAE;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAE;sBACzCa,KAAK,EAAC,sBAAsB;sBAC5BsB,YAAY,EAAC;oBAAO;sBAAAtC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrB,CAAC,eACFrE,OAAA,CAACzB,GAAG;sBACFgI,IAAI,eAAEvG,OAAA,CAACd,QAAQ;wBAACyC,EAAE,EAAE;0BAAEqC,QAAQ,EAAE;wBAAG;sBAAE;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAE;sBACzCa,KAAK,EAAC,uBAAuB;sBAC7BsB,YAAY,EAAC;oBAAO;sBAAAtC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrB,CAAC,eACFrE,OAAA,CAACzB,GAAG;sBACFgI,IAAI,eAAEvG,OAAA,CAAClB,YAAY;wBAAC6C,EAAE,EAAE;0BAAEqC,QAAQ,EAAE;wBAAG;sBAAE;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAE;sBAC7Ca,KAAK,EAAC,iBAAiB;sBACvBsB,YAAY,EAAC;oBAAO;sBAAAtC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrB,CAAC,eACFrE,OAAA,CAACzB,GAAG;sBACFgI,IAAI,eAAEvG,OAAA,CAACZ,cAAc;wBAACuC,EAAE,EAAE;0BAAEqC,QAAQ,EAAE;wBAAG;sBAAE;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAE;sBAC/Ca,KAAK,EAAC,mBAAmB;sBACzBsB,YAAY,EAAC;oBAAO;sBAAAtC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrB,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eAGNrE,OAAA,CAAC9B,GAAG;kBAAA4E,QAAA,GACDzC,SAAS,KAAK,CAAC,iBACdL,OAAA,CAACtB,IAAI;oBAAC+H,EAAE,EAAEpG,SAAS,KAAK,CAAE;oBAACqG,OAAO,EAAE,GAAI;oBAAA5D,QAAA,eACtC9C,OAAA,CAAC9B,GAAG;sBAAA4E,QAAA,eACF9C,OAAA,CAACT,WAAW;wBAAA2E,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACZ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CACP,EAEAhE,SAAS,KAAK,CAAC,iBACdL,OAAA,CAACtB,IAAI;oBAAC+H,EAAE,EAAEpG,SAAS,KAAK,CAAE;oBAACqG,OAAO,EAAE,GAAI;oBAAA5D,QAAA,eACtC9C,OAAA,CAAC9B,GAAG;sBAAA4E,QAAA,eACF9C,OAAA,CAACR,YAAY;wBAAA0E,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACb;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CACP,EAEAhE,SAAS,KAAK,CAAC,iBACdL,OAAA,CAACtB,IAAI;oBAAC+H,EAAE,EAAEpG,SAAS,KAAK,CAAE;oBAACqG,OAAO,EAAE,GAAI;oBAAA5D,QAAA,eACtC9C,OAAA,CAAC9B,GAAG;sBAAA4E,QAAA,eACF9C,OAAA,CAACL,cAAc;wBAAAuE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACf;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CACP,EAEAhE,SAAS,KAAK,CAAC,iBACdL,OAAA,CAACtB,IAAI;oBAAC+H,EAAE,EAAEpG,SAAS,KAAK,CAAE;oBAACqG,OAAO,EAAE,GAAI;oBAAA5D,QAAA,eACtC9C,OAAA,CAAC9B,GAAG;sBAAA4E,QAAA,eACF9C,OAAA,CAACJ,gBAAgB;wBAAAsE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjB;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CACP;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACK;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,EAGN,CAAC3D,WAAW,IAAIC,UAAU,kBACzBX,OAAA,CAACtB,IAAI;cAAC+H,EAAE,EAAE,IAAK;cAACC,OAAO,EAAE,GAAI;cAAA5D,QAAA,eAC3B9C,OAAA,CAAC9B,GAAG;gBAAA4E,QAAA,eACF9C,OAAA,CAACP,UAAU;kBACT4B,UAAU,EAAEX,WAAY;kBACxBiG,gBAAgB,EAAEvF,oBAAqB;kBACvCwF,aAAa,EAAElF;gBAAkB;kBAAAwC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CACP;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC,eAGPrE,OAAA,CAAC/B,IAAI;YAAC6H,IAAI;YAACjB,EAAE,EAAE,EAAG;YAACkB,EAAE,EAAE,CAAE;YAAAjD,QAAA,gBAEvB9C,OAAA,CAAC9B,GAAG;cAAC+E,EAAE,EAAE,CAAE;cAAAH,QAAA,eACT9C,OAAA,CAACN,WAAW;gBACVmH,UAAU,EAAEnG,WAAY;gBACxBoG,WAAW,EAAE;cAAK;gBAAA5C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAGNrE,OAAA,CAAC5B,IAAI;cACHuD,EAAE,EAAE;gBACFsB,EAAE,EAAE,CAAC;gBACLb,UAAU,EAAGkB,KAAK,IAAKA,KAAK,CAACC,OAAO,CAACC,IAAI,KAAK,MAAM,GAChD,qHAAqH,GACrH,qHAAqH;gBACzHC,cAAc,EAAE,2BAA2B;gBAC3CM,MAAM,EAAE,oCAAoC;gBAC5CZ,YAAY,EAAE,CAAC;gBACfhB,QAAQ,EAAE,UAAU;gBACpBiB,QAAQ,EAAE,QAAQ;gBAClBU,SAAS,EAAE,qCAAqC;gBAChD,WAAW,EAAE;kBACXzB,OAAO,EAAE,IAAI;kBACbF,QAAQ,EAAE,UAAU;kBACpBG,GAAG,EAAE,CAAC;kBACNC,IAAI,EAAE,CAAC;kBACPC,KAAK,EAAE,CAAC;kBACRoB,MAAM,EAAE,KAAK;kBACbxB,UAAU,EAAE;gBACd;cACF,CAAE;cAAAU,QAAA,eAEF9C,OAAA,CAAC3B,WAAW;gBAACsD,EAAE,EAAE;kBAAEqE,CAAC,EAAE;gBAAE,CAAE;gBAAAlD,QAAA,gBACxB9C,OAAA,CAAC9B,GAAG;kBAAC4D,OAAO,EAAC,MAAM;kBAACG,UAAU,EAAC,QAAQ;kBAAC+C,GAAG,EAAE,CAAE;kBAAC/B,EAAE,EAAE,CAAE;kBAAAH,QAAA,gBACpD9C,OAAA,CAAClB,YAAY;oBAACmF,KAAK,EAAC,SAAS;oBAACtC,EAAE,EAAE;sBAAEqC,QAAQ,EAAE;oBAAG;kBAAE;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACtDrE,OAAA,CAAC7B,UAAU;oBAACmG,OAAO,EAAC,IAAI;oBAACG,UAAU,EAAC,MAAM;oBAAA3B,QAAA,EAAC;kBAAuB;oBAAAoB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5E,CAAC,eAENrE,OAAA,CAAC9B,GAAG;kBAACqG,SAAS,EAAC,IAAI;kBAAC5C,EAAE,EAAE;oBAAEoF,EAAE,EAAE,CAAC;oBAAEC,CAAC,EAAE,CAAC;oBAAE,MAAM,EAAE;sBAAE/D,EAAE,EAAE;oBAAI;kBAAE,CAAE;kBAAAH,QAAA,gBAC3D9C,OAAA,CAAC7B,UAAU;oBAACoG,SAAS,EAAC,IAAI;oBAACD,OAAO,EAAC,OAAO;oBAAC3C,EAAE,EAAE;sBAAEoD,UAAU,EAAE;oBAAI,CAAE;oBAAAjC,QAAA,EAAC;kBAEpE;oBAAAoB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACbrE,OAAA,CAAC7B,UAAU;oBAACoG,SAAS,EAAC,IAAI;oBAACD,OAAO,EAAC,OAAO;oBAAC3C,EAAE,EAAE;sBAAEoD,UAAU,EAAE;oBAAI,CAAE;oBAAAjC,QAAA,EAAC;kBAEpE;oBAAAoB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACbrE,OAAA,CAAC7B,UAAU;oBAACoG,SAAS,EAAC,IAAI;oBAACD,OAAO,EAAC,OAAO;oBAAC3C,EAAE,EAAE;sBAAEoD,UAAU,EAAE;oBAAI,CAAE;oBAAAjC,QAAA,EAAC;kBAEpE;oBAAAoB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACbrE,OAAA,CAAC7B,UAAU;oBAACoG,SAAS,EAAC,IAAI;oBAACD,OAAO,EAAC,OAAO;oBAAC3C,EAAE,EAAE;sBAAEoD,UAAU,EAAE;oBAAI,CAAE;oBAAAjC,QAAA,EAAC;kBAEpE;oBAAAoB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACbrE,OAAA,CAAC7B,UAAU;oBAACoG,SAAS,EAAC,IAAI;oBAACD,OAAO,EAAC,OAAO;oBAAC3C,EAAE,EAAE;sBAAEoD,UAAU,EAAE;oBAAI,CAAE;oBAAAjC,QAAA,EAAC;kBAEpE;oBAAAoB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACbrE,OAAA,CAAC7B,UAAU;oBAACoG,SAAS,EAAC,IAAI;oBAACD,OAAO,EAAC,OAAO;oBAAC3C,EAAE,EAAE;sBAAEoD,UAAU,EAAE;oBAAI,CAAE;oBAAAjC,QAAA,EAAC;kBAEpE;oBAAAoB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACK;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAGPrE,OAAA,CAAC5B,IAAI;cACHuD,EAAE,EAAE;gBACFS,UAAU,EAAGkB,KAAK,IAAKA,KAAK,CAACC,OAAO,CAACC,IAAI,KAAK,MAAM,GAChD,oFAAoF,GACpF,oFAAoF;gBACxFC,cAAc,EAAE,2BAA2B;gBAC3CM,MAAM,EAAE,kCAAkC;gBAC1CZ,YAAY,EAAE,CAAC;gBACfhB,QAAQ,EAAE,UAAU;gBACpBiB,QAAQ,EAAE,QAAQ;gBAClBU,SAAS,EAAE,mCAAmC;gBAC9C,WAAW,EAAE;kBACXzB,OAAO,EAAE,IAAI;kBACbF,QAAQ,EAAE,UAAU;kBACpBG,GAAG,EAAE,CAAC;kBACNC,IAAI,EAAE,CAAC;kBACPC,KAAK,EAAE,CAAC;kBACRoB,MAAM,EAAE,KAAK;kBACbxB,UAAU,EAAE;gBACd;cACF,CAAE;cAAAU,QAAA,eAEF9C,OAAA,CAAC3B,WAAW;gBAACsD,EAAE,EAAE;kBAAEqE,CAAC,EAAE;gBAAE,CAAE;gBAAAlD,QAAA,gBACxB9C,OAAA,CAAC9B,GAAG;kBAAC4D,OAAO,EAAC,MAAM;kBAACG,UAAU,EAAC,QAAQ;kBAAC+C,GAAG,EAAE,CAAE;kBAAC/B,EAAE,EAAE,CAAE;kBAAAH,QAAA,gBACpD9C,OAAA,CAACZ,cAAc;oBAAC6E,KAAK,EAAC,SAAS;oBAACtC,EAAE,EAAE;sBAAEqC,QAAQ,EAAE;oBAAG;kBAAE;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACxDrE,OAAA,CAAC7B,UAAU;oBAACmG,OAAO,EAAC,IAAI;oBAACG,UAAU,EAAC,MAAM;oBAAA3B,QAAA,EAAC;kBAAiB;oBAAAoB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtE,CAAC,eAENrE,OAAA,CAAC9B,GAAG;kBAAA4E,QAAA,gBACF9C,OAAA,CAAC9B,GAAG;oBAAC4D,OAAO,EAAC,MAAM;oBAACG,UAAU,EAAC,QAAQ;oBAAC+C,GAAG,EAAE,CAAE;oBAAC/B,EAAE,EAAE,CAAE;oBAAAH,QAAA,gBACpD9C,OAAA,CAAChB,QAAQ;sBAACiF,KAAK,EAAC;oBAAS;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC5BrE,OAAA,CAAC7B,UAAU;sBAACmG,OAAO,EAAC,OAAO;sBAACG,UAAU,EAAC,KAAK;sBAAA3B,QAAA,EAAC;oBAE7C;sBAAAoB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC,eAENrE,OAAA,CAAC9B,GAAG;oBAAC4D,OAAO,EAAC,MAAM;oBAACG,UAAU,EAAC,QAAQ;oBAAC+C,GAAG,EAAE,CAAE;oBAAC/B,EAAE,EAAE,CAAE;oBAAAH,QAAA,gBACpD9C,OAAA,CAACd,QAAQ;sBAAC+E,KAAK,EAAC;oBAAS;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC5BrE,OAAA,CAAC7B,UAAU;sBAACmG,OAAO,EAAC,OAAO;sBAACG,UAAU,EAAC,KAAK;sBAAA3B,QAAA,EAAC;oBAE7C;sBAAAoB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC,eAENrE,OAAA,CAAC9B,GAAG;oBAAC4D,OAAO,EAAC,MAAM;oBAACG,UAAU,EAAC,QAAQ;oBAAC+C,GAAG,EAAE,CAAE;oBAAC/B,EAAE,EAAE,CAAE;oBAAAH,QAAA,gBACpD9C,OAAA,CAAClB,YAAY;sBAACmF,KAAK,EAAC;oBAAS;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAChCrE,OAAA,CAAC7B,UAAU;sBAACmG,OAAO,EAAC,OAAO;sBAACG,UAAU,EAAC,KAAK;sBAAA3B,QAAA,EAAC;oBAE7C;sBAAAoB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC,eAENrE,OAAA,CAAC9B,GAAG;oBAAC4D,OAAO,EAAC,MAAM;oBAACG,UAAU,EAAC,QAAQ;oBAAC+C,GAAG,EAAE,CAAE;oBAAAlC,QAAA,gBAC7C9C,OAAA,CAACZ,cAAc;sBAAC6E,KAAK,EAAC;oBAAS;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAClCrE,OAAA,CAAC7B,UAAU;sBAACmG,OAAO,EAAC,OAAO;sBAACG,UAAU,EAAC,KAAK;sBAAA3B,QAAA,EAAC;oBAE7C;sBAAAoB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACK;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC,EAGXxD,aAAa,CAACuE,GAAG,CAAE7E,YAAY,iBAC9BP,OAAA,CAACvB,QAAQ;MAEPwI,IAAI,EAAE,IAAK;MACXC,gBAAgB,EAAE,IAAK;MACvBC,OAAO,EAAEA,CAAA,KAAMjG,uBAAuB,CAACX,YAAY,CAAC6G,EAAE,CAAE;MACxDC,YAAY,EAAE;QAAEC,QAAQ,EAAE,QAAQ;QAAEC,UAAU,EAAE;MAAQ,CAAE;MAAAzE,QAAA,eAE1D9C,OAAA,CAACxB,KAAK;QACJ2I,OAAO,EAAEA,CAAA,KAAMjG,uBAAuB,CAACX,YAAY,CAAC6G,EAAE,CAAE;QACxDI,QAAQ,EAAEjH,YAAY,CAACiB,IAAK;QAC5B8C,OAAO,EAAC,QAAQ;QAChB3C,EAAE,EAAE;UACFC,KAAK,EAAE,MAAM;UACbuB,YAAY,EAAE,CAAC;UACfW,SAAS,EAAE;QACb,CAAE;QAAAhB,QAAA,gBAEF9C,OAAA,CAAC7B,UAAU;UAACmG,OAAO,EAAC,WAAW;UAACG,UAAU,EAAC,MAAM;UAAA3B,QAAA,EAC9CvC,YAAY,CAACkH;QAAK;UAAAvD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,EACZ9D,YAAY,CAACkB,OAAO,iBACnBzB,OAAA,CAAC7B,UAAU;UAACmG,OAAO,EAAC,OAAO;UAAAxB,QAAA,EACxBvC,YAAY,CAACkB;QAAO;UAAAyC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CACb;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC,GAxBH9D,YAAY,CAAC6G,EAAE;MAAAlD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAyBZ,CACX,CAAC,EAGD9D,YAAY,iBACXP,OAAA,CAACvB,QAAQ;MACPwI,IAAI,EAAE,IAAK;MACXC,gBAAgB,EAAE,IAAK;MACvBC,OAAO,EAAEA,CAAA,KAAM3G,eAAe,CAAC,IAAI,CAAE;MACrC6G,YAAY,EAAE;QAAEC,QAAQ,EAAE,QAAQ;QAAEC,UAAU,EAAE;MAAS,CAAE;MAAAzE,QAAA,eAE3D9C,OAAA,CAACxB,KAAK;QACJ2I,OAAO,EAAEA,CAAA,KAAM3G,eAAe,CAAC,IAAI,CAAE;QACrCgH,QAAQ,EAAEjH,YAAY,CAACiB,IAAK;QAC5B8C,OAAO,EAAC,QAAQ;QAChB3C,EAAE,EAAE;UACFwB,YAAY,EAAE,CAAC;UACfW,SAAS,EAAE;QACb,CAAE;QAAAhB,QAAA,EAEDvC,YAAY,CAACkB;MAAO;QAAAyC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CACX;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;EAAA,QA5lBkBhF,WAAW,EAQxBC,OAAO;AAAA,EAolBZ,CAAC;EAAA,QA5lBiBD,WAAW,EAQxBC,OAAO;AAAA,EAolBX;AAACoI,GAAA,GA/lBGzH,QAAQ;AAimBdA,QAAQ,CAAC0H,WAAW,GAAG,UAAU;AAEjC,eAAe1H,QAAQ;AAAC,IAAAG,EAAA,EAAAsH,GAAA;AAAAE,YAAA,CAAAxH,EAAA;AAAAwH,YAAA,CAAAF,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}