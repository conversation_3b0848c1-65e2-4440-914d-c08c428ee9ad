{"ast": null, "code": "var _jsxFileName = \"E:\\\\PythonProjects\\\\React.js\\\\Smart AI Assisstant\\\\src\\\\components\\\\FileUploader.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useCallback } from 'react';\nimport { useDropzone } from 'react-dropzone';\nimport { Card, CardContent, Typography, Box, Button, LinearProgress, Alert, Chip, List, ListItem, ListItemIcon, ListItemText, IconButton, Divider } from '@mui/material';\nimport { CloudUpload as CloudUploadIcon, InsertDriveFile as FileIcon, Delete as DeleteIcon, Security as SecurityIcon, Warning as WarningIcon, CheckCircle as CheckCircleIcon, Error as ErrorIcon } from '@mui/icons-material';\nimport { useScan } from '../contexts/ScanContext';\nimport { scanFile } from '../services/security';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst FileUploader = /*#__PURE__*/_s(/*#__PURE__*/React.memo(_c = _s(() => {\n  _s();\n  const [files, setFiles] = useState([]);\n  const [isScanning, setIsScanning] = useState(false);\n  const [scanResults, setScanResults] = useState([]);\n  const [uploadProgress, setUploadProgress] = useState(0);\n  const {\n    startScan,\n    completeScan,\n    addToHistory,\n    addNotification\n  } = useScan();\n\n  // File validation\n  const validateFile = useCallback(file => {\n    const maxSize = 50 * 1024 * 1024; // 50MB\n    const allowedTypes = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'text/plain', 'image/jpeg', 'image/png', 'image/gif', 'application/zip', 'application/x-zip-compressed'];\n    if (file.size > maxSize) {\n      return {\n        valid: false,\n        error: 'File size exceeds 50MB limit'\n      };\n    }\n    if (!allowedTypes.includes(file.type)) {\n      return {\n        valid: false,\n        error: 'File type not supported'\n      };\n    }\n    return {\n      valid: true\n    };\n  }, []);\n\n  // Handle file drop\n  const onDrop = useCallback((acceptedFiles, rejectedFiles) => {\n    // Handle rejected files\n    rejectedFiles.forEach(({\n      file,\n      errors\n    }) => {\n      addNotification({\n        type: 'error',\n        title: 'File Rejected',\n        message: `${file.name}: ${errors.map(e => e.message).join(', ')}`\n      });\n    });\n\n    // Validate and add accepted files\n    const validFiles = [];\n    acceptedFiles.forEach(file => {\n      const validation = validateFile(file);\n      if (validation.valid) {\n        validFiles.push({\n          id: Date.now() + Math.random(),\n          file,\n          status: 'pending',\n          result: null\n        });\n      } else {\n        addNotification({\n          type: 'error',\n          title: 'Invalid File',\n          message: `${file.name}: ${validation.error}`\n        });\n      }\n    });\n    setFiles(prev => [...prev, ...validFiles]);\n  }, [validateFile, addNotification]);\n\n  // Configure dropzone\n  const {\n    getRootProps,\n    getInputProps,\n    isDragActive\n  } = useDropzone({\n    onDrop,\n    maxSize: 50 * 1024 * 1024,\n    // 50MB\n    multiple: true\n  });\n\n  // Remove file\n  const removeFile = useCallback(fileId => {\n    setFiles(prev => prev.filter(f => f.id !== fileId));\n    setScanResults(prev => prev.filter(r => r.fileId !== fileId));\n  }, []);\n\n  // Scan all files\n  const handleScanFiles = useCallback(async () => {\n    if (files.length === 0) return;\n    setIsScanning(true);\n    setUploadProgress(0);\n    setScanResults([]);\n    try {\n      startScan('files', `${files.length} files`);\n      const results = [];\n      const totalFiles = files.length;\n      for (let i = 0; i < files.length; i++) {\n        const fileData = files[i];\n        try {\n          // Update progress\n          setUploadProgress((i + 1) / totalFiles * 100);\n\n          // Scan individual file\n          const result = await scanFile(fileData.file);\n          const scanResult = {\n            fileId: fileData.id,\n            fileName: fileData.file.name,\n            fileSize: fileData.file.size,\n            result,\n            status: 'completed'\n          };\n          results.push(scanResult);\n          setScanResults(prev => [...prev, scanResult]);\n\n          // Update file status\n          setFiles(prev => prev.map(f => f.id === fileData.id ? {\n            ...f,\n            status: 'scanned',\n            result\n          } : f));\n        } catch (error) {\n          const errorResult = {\n            fileId: fileData.id,\n            fileName: fileData.file.name,\n            fileSize: fileData.file.size,\n            error: error.message,\n            status: 'error'\n          };\n          results.push(errorResult);\n          setScanResults(prev => [...prev, errorResult]);\n\n          // Update file status\n          setFiles(prev => prev.map(f => f.id === fileData.id ? {\n            ...f,\n            status: 'error',\n            error: error.message\n          } : f));\n        }\n      }\n\n      // Complete scan\n      const overallResult = {\n        filesScanned: totalFiles,\n        threatsFound: results.filter(r => r.result && !r.result.isSafe).length,\n        cleanFiles: results.filter(r => r.result && r.result.isSafe).length,\n        errors: results.filter(r => r.error).length\n      };\n      completeScan(overallResult);\n\n      // Add to history\n      const historyEntry = {\n        id: Date.now(),\n        type: 'files',\n        target: `${totalFiles} files`,\n        result: overallResult,\n        timestamp: new Date().toISOString(),\n        files: results\n      };\n      addToHistory(historyEntry);\n\n      // Add notification\n      addNotification({\n        type: overallResult.threatsFound > 0 ? 'warning' : 'success',\n        title: 'File Scan Complete',\n        message: `Scanned ${totalFiles} files. ${overallResult.threatsFound} threats found.`\n      });\n    } catch (error) {\n      addNotification({\n        type: 'error',\n        title: 'Scan Failed',\n        message: error.message || 'Failed to scan files'\n      });\n    } finally {\n      setIsScanning(false);\n      setUploadProgress(0);\n    }\n  }, [files, startScan, completeScan, addToHistory, addNotification]);\n\n  // Format file size\n  const formatFileSize = bytes => {\n    if (bytes === 0) return '0 Bytes';\n    const k = 1024;\n    const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n  };\n\n  // Get status icon\n  const getStatusIcon = (status, result) => {\n    switch (status) {\n      case 'pending':\n        return /*#__PURE__*/_jsxDEV(FileIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 230,\n          columnNumber: 16\n        }, this);\n      case 'scanned':\n        return result !== null && result !== void 0 && result.isSafe ? /*#__PURE__*/_jsxDEV(CheckCircleIcon, {\n          color: \"success\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 232,\n          columnNumber: 33\n        }, this) : /*#__PURE__*/_jsxDEV(WarningIcon, {\n          color: \"warning\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 232,\n          columnNumber: 71\n        }, this);\n      case 'error':\n        return /*#__PURE__*/_jsxDEV(ErrorIcon, {\n          color: \"error\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 234,\n          columnNumber: 16\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(FileIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 236,\n          columnNumber: 16\n        }, this);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      background: theme => theme.palette.mode === 'dark' ? 'linear-gradient(135deg, rgba(30, 30, 30, 0.95) 0%, rgba(50, 50, 50, 0.95) 100%)' : 'linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%)',\n      backdropFilter: 'blur(20px)',\n      borderRadius: 4,\n      border: theme => `1px solid ${theme.palette.divider}`,\n      p: 4,\n      position: 'relative',\n      overflow: 'hidden',\n      '&::before': {\n        content: '\"\"',\n        position: 'absolute',\n        top: 0,\n        left: 0,\n        right: 0,\n        height: '4px',\n        background: 'linear-gradient(90deg, #667eea 0%, #764ba2 100%)'\n      }\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      display: \"flex\",\n      alignItems: \"center\",\n      justifyContent: \"center\",\n      mb: 4,\n      sx: {\n        textAlign: 'center'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n          borderRadius: '50%',\n          width: 60,\n          height: 60,\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          mr: 3,\n          boxShadow: '0 8px 25px rgba(102, 126, 234, 0.3)'\n        },\n        children: /*#__PURE__*/_jsxDEV(CloudUploadIcon, {\n          sx: {\n            color: 'white',\n            fontSize: 28\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 286,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 273,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h4\",\n          component: \"h2\",\n          fontWeight: \"bold\",\n          gutterBottom: true,\n          children: \"File Security Scanner\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 289,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body1\",\n          color: \"text.secondary\",\n          children: \"Advanced malware detection for files and documents\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 292,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 288,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 264,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      ...getRootProps(),\n      sx: {\n        border: '3px dashed',\n        borderColor: isDragActive ? 'primary.main' : 'grey.300',\n        borderRadius: 4,\n        p: 6,\n        textAlign: 'center',\n        cursor: 'pointer',\n        background: isDragActive ? 'linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%)' : theme => theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.02)' : 'rgba(0, 0, 0, 0.02)',\n        transition: 'all 0.3s ease',\n        mb: 4,\n        position: 'relative',\n        overflow: 'hidden',\n        '&:hover': {\n          borderColor: 'primary.main',\n          background: 'linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%)',\n          transform: 'scale(1.01)'\n        },\n        '&::before': {\n          content: '\"\"',\n          position: 'absolute',\n          top: 0,\n          left: 0,\n          right: 0,\n          bottom: 0,\n          background: isDragActive ? 'linear-gradient(45deg, transparent 30%, rgba(102, 126, 234, 0.1) 50%, transparent 70%)' : 'none',\n          animation: isDragActive ? 'shimmer 1.5s infinite' : 'none'\n        },\n        '@keyframes shimmer': {\n          '0%': {\n            transform: 'translateX(-100%)'\n          },\n          '100%': {\n            transform: 'translateX(100%)'\n          }\n        }\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n        ...getInputProps()\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 340,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          background: 'linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%)',\n          borderRadius: '50%',\n          width: 80,\n          height: 80,\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          mx: 'auto',\n          mb: 3,\n          transition: 'transform 0.3s ease',\n          transform: isDragActive ? 'scale(1.1)' : 'scale(1)'\n        },\n        children: /*#__PURE__*/_jsxDEV(CloudUploadIcon, {\n          sx: {\n            fontSize: 40,\n            color: 'primary.main'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 357,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 342,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h5\",\n        gutterBottom: true,\n        fontWeight: \"600\",\n        children: isDragActive ? 'Drop files here to scan' : 'Drag & drop files here'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 360,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        color: \"text.secondary\",\n        gutterBottom: true,\n        sx: {\n          mb: 2\n        },\n        children: \"or click to select files from your device\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 363,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body1\",\n        color: \"text.secondary\",\n        sx: {\n          mb: 2\n        },\n        children: \"Supported formats: PDF, DOC, XLS, TXT, Images, ZIP\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 366,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body2\",\n        color: \"text.secondary\",\n        children: \"Maximum file size: 50MB per file\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 369,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 299,\n      columnNumber: 7\n    }, this), files.length > 0 && /*#__PURE__*/_jsxDEV(Box, {\n      mb: 3,\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"subtitle1\",\n        gutterBottom: true,\n        children: [\"Selected Files (\", files.length, \")\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 377,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(List, {\n        children: files.map((fileData, index) => /*#__PURE__*/_jsxDEV(React.Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(ListItem, {\n            secondaryAction: /*#__PURE__*/_jsxDEV(IconButton, {\n              edge: \"end\",\n              onClick: () => removeFile(fileData.id),\n              disabled: isScanning,\n              children: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 390,\n                columnNumber: 25\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 385,\n              columnNumber: 23\n            }, this),\n            children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n              children: getStatusIcon(fileData.status, fileData.result)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 394,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: fileData.file.name,\n              secondary: /*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  display: \"block\",\n                  children: formatFileSize(fileData.file.size)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 401,\n                  columnNumber: 27\n                }, this), fileData.status === 'error' && /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  color: \"error\",\n                  children: [\"Error: \", fileData.error]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 405,\n                  columnNumber: 29\n                }, this), fileData.result && /*#__PURE__*/_jsxDEV(Box, {\n                  mt: 1,\n                  children: /*#__PURE__*/_jsxDEV(Chip, {\n                    size: \"small\",\n                    label: fileData.result.isSafe ? 'Clean' : 'Threat Detected',\n                    color: fileData.result.isSafe ? 'success' : 'error',\n                    variant: \"outlined\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 411,\n                    columnNumber: 31\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 410,\n                  columnNumber: 29\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 400,\n                columnNumber: 25\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 397,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 383,\n            columnNumber: 19\n          }, this), index < files.length - 1 && /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 423,\n            columnNumber: 48\n          }, this)]\n        }, fileData.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 382,\n          columnNumber: 17\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 380,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 376,\n      columnNumber: 11\n    }, this), isScanning && /*#__PURE__*/_jsxDEV(Box, {\n      mb: 3,\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body2\",\n        gutterBottom: true,\n        children: [\"Scanning files... \", Math.round(uploadProgress), \"%\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 433,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(LinearProgress, {\n        variant: \"determinate\",\n        value: uploadProgress\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 436,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 432,\n      columnNumber: 11\n    }, this), /*#__PURE__*/_jsxDEV(Button, {\n      variant: \"contained\",\n      color: \"primary\",\n      onClick: handleScanFiles,\n      disabled: files.length === 0 || isScanning,\n      startIcon: /*#__PURE__*/_jsxDEV(SecurityIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 446,\n        columnNumber: 22\n      }, this),\n      fullWidth: true,\n      size: \"large\",\n      children: isScanning ? 'Scanning Files...' : `Scan ${files.length} File${files.length !== 1 ? 's' : ''}`\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 441,\n      columnNumber: 9\n    }, this), scanResults.length > 0 && !isScanning && /*#__PURE__*/_jsxDEV(Box, {\n      mt: 3,\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        severity: scanResults.some(r => r.result && !r.result.isSafe) ? 'warning' : 'success',\n        icon: /*#__PURE__*/_jsxDEV(SecurityIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 458,\n          columnNumber: 21\n        }, this),\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle1\",\n          fontWeight: \"bold\",\n          children: \"Scan Complete\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 460,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          children: [scanResults.filter(r => {\n            var _r$result;\n            return (_r$result = r.result) === null || _r$result === void 0 ? void 0 : _r$result.isSafe;\n          }).length, \" files clean, \", ' ', scanResults.filter(r => r.result && !r.result.isSafe).length, \" threats detected, \", ' ', scanResults.filter(r => r.error).length, \" errors\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 463,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 456,\n        columnNumber: 13\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 455,\n      columnNumber: 11\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 241,\n    columnNumber: 5\n  }, this);\n}, \"6PA7DFIbInioocer4jBAkgEK9i0=\", false, function () {\n  return [useScan, useDropzone];\n})), \"6PA7DFIbInioocer4jBAkgEK9i0=\", false, function () {\n  return [useScan, useDropzone];\n});\n_c2 = FileUploader;\nFileUploader.displayName = 'FileUploader';\nexport default FileUploader;\nvar _c, _c2;\n$RefreshReg$(_c, \"FileUploader$React.memo\");\n$RefreshReg$(_c2, \"FileUploader\");", "map": {"version": 3, "names": ["React", "useState", "useCallback", "useDropzone", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Typography", "Box", "<PERSON><PERSON>", "LinearProgress", "<PERSON><PERSON>", "Chip", "List", "ListItem", "ListItemIcon", "ListItemText", "IconButton", "Divider", "CloudUpload", "CloudUploadIcon", "InsertDriveFile", "FileIcon", "Delete", "DeleteIcon", "Security", "SecurityIcon", "Warning", "WarningIcon", "CheckCircle", "CheckCircleIcon", "Error", "ErrorIcon", "useScan", "scanFile", "jsxDEV", "_jsxDEV", "FileUploader", "_s", "memo", "_c", "files", "setFiles", "isScanning", "setIsScanning", "scanResults", "setScanResults", "uploadProgress", "setUploadProgress", "startScan", "completeScan", "addToHistory", "addNotification", "validateFile", "file", "maxSize", "allowedTypes", "size", "valid", "error", "includes", "type", "onDrop", "acceptedFiles", "rejectedFiles", "for<PERSON>ach", "errors", "title", "message", "name", "map", "e", "join", "validFiles", "validation", "push", "id", "Date", "now", "Math", "random", "status", "result", "prev", "getRootProps", "getInputProps", "isDragActive", "multiple", "removeFile", "fileId", "filter", "f", "r", "handleScanFiles", "length", "results", "totalFiles", "i", "fileData", "scanResult", "fileName", "fileSize", "errorResult", "overallResult", "filesScanned", "threatsFound", "isSafe", "cleanFiles", "historyEntry", "target", "timestamp", "toISOString", "formatFileSize", "bytes", "k", "sizes", "floor", "log", "parseFloat", "pow", "toFixed", "getStatusIcon", "_jsxFileName", "lineNumber", "columnNumber", "color", "sx", "background", "theme", "palette", "mode", "<PERSON><PERSON>ilter", "borderRadius", "border", "divider", "p", "position", "overflow", "content", "top", "left", "right", "height", "children", "display", "alignItems", "justifyContent", "mb", "textAlign", "width", "mr", "boxShadow", "fontSize", "variant", "component", "fontWeight", "gutterBottom", "borderColor", "cursor", "transition", "transform", "bottom", "animation", "mx", "index", "Fragment", "secondaryAction", "edge", "onClick", "disabled", "primary", "secondary", "mt", "label", "round", "value", "startIcon", "fullWidth", "severity", "some", "icon", "_r$result", "_c2", "displayName", "$RefreshReg$"], "sources": ["E:/PythonProjects/React.js/Smart AI Assisstant/src/components/FileUploader.jsx"], "sourcesContent": ["import React, { useState, useCallback } from 'react';\nimport { useDropzone } from 'react-dropzone';\nimport {\n  Card,\n  CardContent,\n  Typography,\n  Box,\n  Button,\n  LinearProgress,\n  Alert,\n  Chip,\n  List,\n  ListItem,\n  ListItemIcon,\n  ListItemText,\n  IconButton,\n  Divider,\n} from '@mui/material';\nimport {\n  CloudUpload as CloudUploadIcon,\n  InsertDriveFile as FileIcon,\n  Delete as DeleteIcon,\n  Security as SecurityIcon,\n  Warning as WarningIcon,\n  CheckCircle as CheckCircleIcon,\n  Error as ErrorIcon,\n} from '@mui/icons-material';\nimport { useScan } from '../contexts/ScanContext';\nimport { scanFile } from '../services/security';\n\nconst FileUploader = React.memo(() => {\n  const [files, setFiles] = useState([]);\n  const [isScanning, setIsScanning] = useState(false);\n  const [scanResults, setScanResults] = useState([]);\n  const [uploadProgress, setUploadProgress] = useState(0);\n  \n  const { startScan, completeScan, addToHistory, addNotification } = useScan();\n\n  // File validation\n  const validateFile = useCallback((file) => {\n    const maxSize = 50 * 1024 * 1024; // 50MB\n    const allowedTypes = [\n      'application/pdf',\n      'application/msword',\n      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',\n      'application/vnd.ms-excel',\n      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',\n      'text/plain',\n      'image/jpeg',\n      'image/png',\n      'image/gif',\n      'application/zip',\n      'application/x-zip-compressed',\n    ];\n\n    if (file.size > maxSize) {\n      return { valid: false, error: 'File size exceeds 50MB limit' };\n    }\n\n    if (!allowedTypes.includes(file.type)) {\n      return { valid: false, error: 'File type not supported' };\n    }\n\n    return { valid: true };\n  }, []);\n\n  // Handle file drop\n  const onDrop = useCallback((acceptedFiles, rejectedFiles) => {\n    // Handle rejected files\n    rejectedFiles.forEach(({ file, errors }) => {\n      addNotification({\n        type: 'error',\n        title: 'File Rejected',\n        message: `${file.name}: ${errors.map(e => e.message).join(', ')}`,\n      });\n    });\n\n    // Validate and add accepted files\n    const validFiles = [];\n    acceptedFiles.forEach(file => {\n      const validation = validateFile(file);\n      if (validation.valid) {\n        validFiles.push({\n          id: Date.now() + Math.random(),\n          file,\n          status: 'pending',\n          result: null,\n        });\n      } else {\n        addNotification({\n          type: 'error',\n          title: 'Invalid File',\n          message: `${file.name}: ${validation.error}`,\n        });\n      }\n    });\n\n    setFiles(prev => [...prev, ...validFiles]);\n  }, [validateFile, addNotification]);\n\n  // Configure dropzone\n  const { getRootProps, getInputProps, isDragActive } = useDropzone({\n    onDrop,\n    maxSize: 50 * 1024 * 1024, // 50MB\n    multiple: true,\n  });\n\n  // Remove file\n  const removeFile = useCallback((fileId) => {\n    setFiles(prev => prev.filter(f => f.id !== fileId));\n    setScanResults(prev => prev.filter(r => r.fileId !== fileId));\n  }, []);\n\n  // Scan all files\n  const handleScanFiles = useCallback(async () => {\n    if (files.length === 0) return;\n\n    setIsScanning(true);\n    setUploadProgress(0);\n    setScanResults([]);\n\n    try {\n      startScan('files', `${files.length} files`);\n\n      const results = [];\n      const totalFiles = files.length;\n\n      for (let i = 0; i < files.length; i++) {\n        const fileData = files[i];\n        \n        try {\n          // Update progress\n          setUploadProgress(((i + 1) / totalFiles) * 100);\n\n          // Scan individual file\n          const result = await scanFile(fileData.file);\n          \n          const scanResult = {\n            fileId: fileData.id,\n            fileName: fileData.file.name,\n            fileSize: fileData.file.size,\n            result,\n            status: 'completed',\n          };\n\n          results.push(scanResult);\n          setScanResults(prev => [...prev, scanResult]);\n\n          // Update file status\n          setFiles(prev => prev.map(f => \n            f.id === fileData.id \n              ? { ...f, status: 'scanned', result }\n              : f\n          ));\n\n        } catch (error) {\n          const errorResult = {\n            fileId: fileData.id,\n            fileName: fileData.file.name,\n            fileSize: fileData.file.size,\n            error: error.message,\n            status: 'error',\n          };\n\n          results.push(errorResult);\n          setScanResults(prev => [...prev, errorResult]);\n\n          // Update file status\n          setFiles(prev => prev.map(f => \n            f.id === fileData.id \n              ? { ...f, status: 'error', error: error.message }\n              : f\n          ));\n        }\n      }\n\n      // Complete scan\n      const overallResult = {\n        filesScanned: totalFiles,\n        threatsFound: results.filter(r => r.result && !r.result.isSafe).length,\n        cleanFiles: results.filter(r => r.result && r.result.isSafe).length,\n        errors: results.filter(r => r.error).length,\n      };\n\n      completeScan(overallResult);\n\n      // Add to history\n      const historyEntry = {\n        id: Date.now(),\n        type: 'files',\n        target: `${totalFiles} files`,\n        result: overallResult,\n        timestamp: new Date().toISOString(),\n        files: results,\n      };\n      addToHistory(historyEntry);\n\n      // Add notification\n      addNotification({\n        type: overallResult.threatsFound > 0 ? 'warning' : 'success',\n        title: 'File Scan Complete',\n        message: `Scanned ${totalFiles} files. ${overallResult.threatsFound} threats found.`,\n      });\n\n    } catch (error) {\n      addNotification({\n        type: 'error',\n        title: 'Scan Failed',\n        message: error.message || 'Failed to scan files',\n      });\n    } finally {\n      setIsScanning(false);\n      setUploadProgress(0);\n    }\n  }, [files, startScan, completeScan, addToHistory, addNotification]);\n\n  // Format file size\n  const formatFileSize = (bytes) => {\n    if (bytes === 0) return '0 Bytes';\n    const k = 1024;\n    const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n  };\n\n  // Get status icon\n  const getStatusIcon = (status, result) => {\n    switch (status) {\n      case 'pending':\n        return <FileIcon />;\n      case 'scanned':\n        return result?.isSafe ? <CheckCircleIcon color=\"success\" /> : <WarningIcon color=\"warning\" />;\n      case 'error':\n        return <ErrorIcon color=\"error\" />;\n      default:\n        return <FileIcon />;\n    }\n  };\n\n  return (\n    <Box\n      sx={{\n        background: (theme) => theme.palette.mode === 'dark'\n          ? 'linear-gradient(135deg, rgba(30, 30, 30, 0.95) 0%, rgba(50, 50, 50, 0.95) 100%)'\n          : 'linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%)',\n        backdropFilter: 'blur(20px)',\n        borderRadius: 4,\n        border: (theme) => `1px solid ${theme.palette.divider}`,\n        p: 4,\n        position: 'relative',\n        overflow: 'hidden',\n        '&::before': {\n          content: '\"\"',\n          position: 'absolute',\n          top: 0,\n          left: 0,\n          right: 0,\n          height: '4px',\n          background: 'linear-gradient(90deg, #667eea 0%, #764ba2 100%)',\n        },\n      }}\n    >\n      {/* Header Section */}\n      <Box\n        display=\"flex\"\n        alignItems=\"center\"\n        justifyContent=\"center\"\n        mb={4}\n        sx={{\n          textAlign: 'center',\n        }}\n      >\n        <Box\n          sx={{\n            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n            borderRadius: '50%',\n            width: 60,\n            height: 60,\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            mr: 3,\n            boxShadow: '0 8px 25px rgba(102, 126, 234, 0.3)',\n          }}\n        >\n          <CloudUploadIcon sx={{ color: 'white', fontSize: 28 }} />\n        </Box>\n        <Box>\n          <Typography variant=\"h4\" component=\"h2\" fontWeight=\"bold\" gutterBottom>\n            File Security Scanner\n          </Typography>\n          <Typography variant=\"body1\" color=\"text.secondary\">\n            Advanced malware detection for files and documents\n          </Typography>\n        </Box>\n      </Box>\n\n      {/* Enhanced Dropzone */}\n      <Box\n        {...getRootProps()}\n        sx={{\n          border: '3px dashed',\n          borderColor: isDragActive ? 'primary.main' : 'grey.300',\n          borderRadius: 4,\n          p: 6,\n          textAlign: 'center',\n          cursor: 'pointer',\n          background: isDragActive\n            ? 'linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%)'\n            : (theme) => theme.palette.mode === 'dark'\n              ? 'rgba(255, 255, 255, 0.02)'\n              : 'rgba(0, 0, 0, 0.02)',\n          transition: 'all 0.3s ease',\n          mb: 4,\n          position: 'relative',\n          overflow: 'hidden',\n          '&:hover': {\n            borderColor: 'primary.main',\n            background: 'linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%)',\n            transform: 'scale(1.01)',\n          },\n          '&::before': {\n            content: '\"\"',\n            position: 'absolute',\n            top: 0,\n            left: 0,\n            right: 0,\n            bottom: 0,\n            background: isDragActive\n              ? 'linear-gradient(45deg, transparent 30%, rgba(102, 126, 234, 0.1) 50%, transparent 70%)'\n              : 'none',\n            animation: isDragActive ? 'shimmer 1.5s infinite' : 'none',\n          },\n          '@keyframes shimmer': {\n            '0%': { transform: 'translateX(-100%)' },\n            '100%': { transform: 'translateX(100%)' },\n          },\n        }}\n      >\n        <input {...getInputProps()} />\n\n        <Box\n          sx={{\n            background: 'linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%)',\n            borderRadius: '50%',\n            width: 80,\n            height: 80,\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            mx: 'auto',\n            mb: 3,\n            transition: 'transform 0.3s ease',\n            transform: isDragActive ? 'scale(1.1)' : 'scale(1)',\n          }}\n        >\n          <CloudUploadIcon sx={{ fontSize: 40, color: 'primary.main' }} />\n        </Box>\n\n        <Typography variant=\"h5\" gutterBottom fontWeight=\"600\">\n          {isDragActive ? 'Drop files here to scan' : 'Drag & drop files here'}\n        </Typography>\n        <Typography variant=\"h6\" color=\"text.secondary\" gutterBottom sx={{ mb: 2 }}>\n          or click to select files from your device\n        </Typography>\n        <Typography variant=\"body1\" color=\"text.secondary\" sx={{ mb: 2 }}>\n          Supported formats: PDF, DOC, XLS, TXT, Images, ZIP\n        </Typography>\n        <Typography variant=\"body2\" color=\"text.secondary\">\n          Maximum file size: 50MB per file\n        </Typography>\n      </Box>\n\n        {/* File List */}\n        {files.length > 0 && (\n          <Box mb={3}>\n            <Typography variant=\"subtitle1\" gutterBottom>\n              Selected Files ({files.length})\n            </Typography>\n            <List>\n              {files.map((fileData, index) => (\n                <React.Fragment key={fileData.id}>\n                  <ListItem\n                    secondaryAction={\n                      <IconButton\n                        edge=\"end\"\n                        onClick={() => removeFile(fileData.id)}\n                        disabled={isScanning}\n                      >\n                        <DeleteIcon />\n                      </IconButton>\n                    }\n                  >\n                    <ListItemIcon>\n                      {getStatusIcon(fileData.status, fileData.result)}\n                    </ListItemIcon>\n                    <ListItemText\n                      primary={fileData.file.name}\n                      secondary={\n                        <Box>\n                          <Typography variant=\"caption\" display=\"block\">\n                            {formatFileSize(fileData.file.size)}\n                          </Typography>\n                          {fileData.status === 'error' && (\n                            <Typography variant=\"caption\" color=\"error\">\n                              Error: {fileData.error}\n                            </Typography>\n                          )}\n                          {fileData.result && (\n                            <Box mt={1}>\n                              <Chip\n                                size=\"small\"\n                                label={fileData.result.isSafe ? 'Clean' : 'Threat Detected'}\n                                color={fileData.result.isSafe ? 'success' : 'error'}\n                                variant=\"outlined\"\n                              />\n                            </Box>\n                          )}\n                        </Box>\n                      }\n                    />\n                  </ListItem>\n                  {index < files.length - 1 && <Divider />}\n                </React.Fragment>\n              ))}\n            </List>\n          </Box>\n        )}\n\n        {/* Scan Progress */}\n        {isScanning && (\n          <Box mb={3}>\n            <Typography variant=\"body2\" gutterBottom>\n              Scanning files... {Math.round(uploadProgress)}%\n            </Typography>\n            <LinearProgress variant=\"determinate\" value={uploadProgress} />\n          </Box>\n        )}\n\n        {/* Scan Button */}\n        <Button\n          variant=\"contained\"\n          color=\"primary\"\n          onClick={handleScanFiles}\n          disabled={files.length === 0 || isScanning}\n          startIcon={<SecurityIcon />}\n          fullWidth\n          size=\"large\"\n        >\n          {isScanning ? 'Scanning Files...' : `Scan ${files.length} File${files.length !== 1 ? 's' : ''}`}\n        </Button>\n\n        {/* Scan Results Summary */}\n        {scanResults.length > 0 && !isScanning && (\n          <Box mt={3}>\n            <Alert\n              severity={scanResults.some(r => r.result && !r.result.isSafe) ? 'warning' : 'success'}\n              icon={<SecurityIcon />}\n            >\n              <Typography variant=\"subtitle1\" fontWeight=\"bold\">\n                Scan Complete\n              </Typography>\n              <Typography variant=\"body2\">\n                {scanResults.filter(r => r.result?.isSafe).length} files clean, {' '}\n                {scanResults.filter(r => r.result && !r.result.isSafe).length} threats detected, {' '}\n                {scanResults.filter(r => r.error).length} errors\n              </Typography>\n            </Alert>\n          </Box>\n        )}\n    </Box>\n  );\n});\n\nFileUploader.displayName = 'FileUploader';\n\nexport default FileUploader;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,WAAW,QAAQ,OAAO;AACpD,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SACEC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,GAAG,EACHC,MAAM,EACNC,cAAc,EACdC,KAAK,EACLC,IAAI,EACJC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,YAAY,EACZC,UAAU,EACVC,OAAO,QACF,eAAe;AACtB,SACEC,WAAW,IAAIC,eAAe,EAC9BC,eAAe,IAAIC,QAAQ,EAC3BC,MAAM,IAAIC,UAAU,EACpBC,QAAQ,IAAIC,YAAY,EACxBC,OAAO,IAAIC,WAAW,EACtBC,WAAW,IAAIC,eAAe,EAC9BC,KAAK,IAAIC,SAAS,QACb,qBAAqB;AAC5B,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SAASC,QAAQ,QAAQ,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhD,MAAMC,YAAY,gBAAAC,EAAA,cAAGrC,KAAK,CAACsC,IAAI,CAAAC,EAAA,GAAAF,EAAA,CAAC,MAAM;EAAAA,EAAA;EACpC,MAAM,CAACG,KAAK,EAAEC,QAAQ,CAAC,GAAGxC,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACyC,UAAU,EAAEC,aAAa,CAAC,GAAG1C,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC2C,WAAW,EAAEC,cAAc,CAAC,GAAG5C,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC6C,cAAc,EAAEC,iBAAiB,CAAC,GAAG9C,QAAQ,CAAC,CAAC,CAAC;EAEvD,MAAM;IAAE+C,SAAS;IAAEC,YAAY;IAAEC,YAAY;IAAEC;EAAgB,CAAC,GAAGnB,OAAO,CAAC,CAAC;;EAE5E;EACA,MAAMoB,YAAY,GAAGlD,WAAW,CAAEmD,IAAI,IAAK;IACzC,MAAMC,OAAO,GAAG,EAAE,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC;IAClC,MAAMC,YAAY,GAAG,CACnB,iBAAiB,EACjB,oBAAoB,EACpB,yEAAyE,EACzE,0BAA0B,EAC1B,mEAAmE,EACnE,YAAY,EACZ,YAAY,EACZ,WAAW,EACX,WAAW,EACX,iBAAiB,EACjB,8BAA8B,CAC/B;IAED,IAAIF,IAAI,CAACG,IAAI,GAAGF,OAAO,EAAE;MACvB,OAAO;QAAEG,KAAK,EAAE,KAAK;QAAEC,KAAK,EAAE;MAA+B,CAAC;IAChE;IAEA,IAAI,CAACH,YAAY,CAACI,QAAQ,CAACN,IAAI,CAACO,IAAI,CAAC,EAAE;MACrC,OAAO;QAAEH,KAAK,EAAE,KAAK;QAAEC,KAAK,EAAE;MAA0B,CAAC;IAC3D;IAEA,OAAO;MAAED,KAAK,EAAE;IAAK,CAAC;EACxB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMI,MAAM,GAAG3D,WAAW,CAAC,CAAC4D,aAAa,EAAEC,aAAa,KAAK;IAC3D;IACAA,aAAa,CAACC,OAAO,CAAC,CAAC;MAAEX,IAAI;MAAEY;IAAO,CAAC,KAAK;MAC1Cd,eAAe,CAAC;QACdS,IAAI,EAAE,OAAO;QACbM,KAAK,EAAE,eAAe;QACtBC,OAAO,EAAE,GAAGd,IAAI,CAACe,IAAI,KAAKH,MAAM,CAACI,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACH,OAAO,CAAC,CAACI,IAAI,CAAC,IAAI,CAAC;MACjE,CAAC,CAAC;IACJ,CAAC,CAAC;;IAEF;IACA,MAAMC,UAAU,GAAG,EAAE;IACrBV,aAAa,CAACE,OAAO,CAACX,IAAI,IAAI;MAC5B,MAAMoB,UAAU,GAAGrB,YAAY,CAACC,IAAI,CAAC;MACrC,IAAIoB,UAAU,CAAChB,KAAK,EAAE;QACpBe,UAAU,CAACE,IAAI,CAAC;UACdC,EAAE,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGC,IAAI,CAACC,MAAM,CAAC,CAAC;UAC9B1B,IAAI;UACJ2B,MAAM,EAAE,SAAS;UACjBC,MAAM,EAAE;QACV,CAAC,CAAC;MACJ,CAAC,MAAM;QACL9B,eAAe,CAAC;UACdS,IAAI,EAAE,OAAO;UACbM,KAAK,EAAE,cAAc;UACrBC,OAAO,EAAE,GAAGd,IAAI,CAACe,IAAI,KAAKK,UAAU,CAACf,KAAK;QAC5C,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;IAEFjB,QAAQ,CAACyC,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE,GAAGV,UAAU,CAAC,CAAC;EAC5C,CAAC,EAAE,CAACpB,YAAY,EAAED,eAAe,CAAC,CAAC;;EAEnC;EACA,MAAM;IAAEgC,YAAY;IAAEC,aAAa;IAAEC;EAAa,CAAC,GAAGlF,WAAW,CAAC;IAChE0D,MAAM;IACNP,OAAO,EAAE,EAAE,GAAG,IAAI,GAAG,IAAI;IAAE;IAC3BgC,QAAQ,EAAE;EACZ,CAAC,CAAC;;EAEF;EACA,MAAMC,UAAU,GAAGrF,WAAW,CAAEsF,MAAM,IAAK;IACzC/C,QAAQ,CAACyC,IAAI,IAAIA,IAAI,CAACO,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACf,EAAE,KAAKa,MAAM,CAAC,CAAC;IACnD3C,cAAc,CAACqC,IAAI,IAAIA,IAAI,CAACO,MAAM,CAACE,CAAC,IAAIA,CAAC,CAACH,MAAM,KAAKA,MAAM,CAAC,CAAC;EAC/D,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMI,eAAe,GAAG1F,WAAW,CAAC,YAAY;IAC9C,IAAIsC,KAAK,CAACqD,MAAM,KAAK,CAAC,EAAE;IAExBlD,aAAa,CAAC,IAAI,CAAC;IACnBI,iBAAiB,CAAC,CAAC,CAAC;IACpBF,cAAc,CAAC,EAAE,CAAC;IAElB,IAAI;MACFG,SAAS,CAAC,OAAO,EAAE,GAAGR,KAAK,CAACqD,MAAM,QAAQ,CAAC;MAE3C,MAAMC,OAAO,GAAG,EAAE;MAClB,MAAMC,UAAU,GAAGvD,KAAK,CAACqD,MAAM;MAE/B,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGxD,KAAK,CAACqD,MAAM,EAAEG,CAAC,EAAE,EAAE;QACrC,MAAMC,QAAQ,GAAGzD,KAAK,CAACwD,CAAC,CAAC;QAEzB,IAAI;UACF;UACAjD,iBAAiB,CAAE,CAACiD,CAAC,GAAG,CAAC,IAAID,UAAU,GAAI,GAAG,CAAC;;UAE/C;UACA,MAAMd,MAAM,GAAG,MAAMhD,QAAQ,CAACgE,QAAQ,CAAC5C,IAAI,CAAC;UAE5C,MAAM6C,UAAU,GAAG;YACjBV,MAAM,EAAES,QAAQ,CAACtB,EAAE;YACnBwB,QAAQ,EAAEF,QAAQ,CAAC5C,IAAI,CAACe,IAAI;YAC5BgC,QAAQ,EAAEH,QAAQ,CAAC5C,IAAI,CAACG,IAAI;YAC5ByB,MAAM;YACND,MAAM,EAAE;UACV,CAAC;UAEDc,OAAO,CAACpB,IAAI,CAACwB,UAAU,CAAC;UACxBrD,cAAc,CAACqC,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEgB,UAAU,CAAC,CAAC;;UAE7C;UACAzD,QAAQ,CAACyC,IAAI,IAAIA,IAAI,CAACb,GAAG,CAACqB,CAAC,IACzBA,CAAC,CAACf,EAAE,KAAKsB,QAAQ,CAACtB,EAAE,GAChB;YAAE,GAAGe,CAAC;YAAEV,MAAM,EAAE,SAAS;YAAEC;UAAO,CAAC,GACnCS,CACN,CAAC,CAAC;QAEJ,CAAC,CAAC,OAAOhC,KAAK,EAAE;UACd,MAAM2C,WAAW,GAAG;YAClBb,MAAM,EAAES,QAAQ,CAACtB,EAAE;YACnBwB,QAAQ,EAAEF,QAAQ,CAAC5C,IAAI,CAACe,IAAI;YAC5BgC,QAAQ,EAAEH,QAAQ,CAAC5C,IAAI,CAACG,IAAI;YAC5BE,KAAK,EAAEA,KAAK,CAACS,OAAO;YACpBa,MAAM,EAAE;UACV,CAAC;UAEDc,OAAO,CAACpB,IAAI,CAAC2B,WAAW,CAAC;UACzBxD,cAAc,CAACqC,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEmB,WAAW,CAAC,CAAC;;UAE9C;UACA5D,QAAQ,CAACyC,IAAI,IAAIA,IAAI,CAACb,GAAG,CAACqB,CAAC,IACzBA,CAAC,CAACf,EAAE,KAAKsB,QAAQ,CAACtB,EAAE,GAChB;YAAE,GAAGe,CAAC;YAAEV,MAAM,EAAE,OAAO;YAAEtB,KAAK,EAAEA,KAAK,CAACS;UAAQ,CAAC,GAC/CuB,CACN,CAAC,CAAC;QACJ;MACF;;MAEA;MACA,MAAMY,aAAa,GAAG;QACpBC,YAAY,EAAER,UAAU;QACxBS,YAAY,EAAEV,OAAO,CAACL,MAAM,CAACE,CAAC,IAAIA,CAAC,CAACV,MAAM,IAAI,CAACU,CAAC,CAACV,MAAM,CAACwB,MAAM,CAAC,CAACZ,MAAM;QACtEa,UAAU,EAAEZ,OAAO,CAACL,MAAM,CAACE,CAAC,IAAIA,CAAC,CAACV,MAAM,IAAIU,CAAC,CAACV,MAAM,CAACwB,MAAM,CAAC,CAACZ,MAAM;QACnE5B,MAAM,EAAE6B,OAAO,CAACL,MAAM,CAACE,CAAC,IAAIA,CAAC,CAACjC,KAAK,CAAC,CAACmC;MACvC,CAAC;MAED5C,YAAY,CAACqD,aAAa,CAAC;;MAE3B;MACA,MAAMK,YAAY,GAAG;QACnBhC,EAAE,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC;QACdjB,IAAI,EAAE,OAAO;QACbgD,MAAM,EAAE,GAAGb,UAAU,QAAQ;QAC7Bd,MAAM,EAAEqB,aAAa;QACrBO,SAAS,EAAE,IAAIjC,IAAI,CAAC,CAAC,CAACkC,WAAW,CAAC,CAAC;QACnCtE,KAAK,EAAEsD;MACT,CAAC;MACD5C,YAAY,CAACyD,YAAY,CAAC;;MAE1B;MACAxD,eAAe,CAAC;QACdS,IAAI,EAAE0C,aAAa,CAACE,YAAY,GAAG,CAAC,GAAG,SAAS,GAAG,SAAS;QAC5DtC,KAAK,EAAE,oBAAoB;QAC3BC,OAAO,EAAE,WAAW4B,UAAU,WAAWO,aAAa,CAACE,YAAY;MACrE,CAAC,CAAC;IAEJ,CAAC,CAAC,OAAO9C,KAAK,EAAE;MACdP,eAAe,CAAC;QACdS,IAAI,EAAE,OAAO;QACbM,KAAK,EAAE,aAAa;QACpBC,OAAO,EAAET,KAAK,CAACS,OAAO,IAAI;MAC5B,CAAC,CAAC;IACJ,CAAC,SAAS;MACRxB,aAAa,CAAC,KAAK,CAAC;MACpBI,iBAAiB,CAAC,CAAC,CAAC;IACtB;EACF,CAAC,EAAE,CAACP,KAAK,EAAEQ,SAAS,EAAEC,YAAY,EAAEC,YAAY,EAAEC,eAAe,CAAC,CAAC;;EAEnE;EACA,MAAM4D,cAAc,GAAIC,KAAK,IAAK;IAChC,IAAIA,KAAK,KAAK,CAAC,EAAE,OAAO,SAAS;IACjC,MAAMC,CAAC,GAAG,IAAI;IACd,MAAMC,KAAK,GAAG,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IACzC,MAAMlB,CAAC,GAAGlB,IAAI,CAACqC,KAAK,CAACrC,IAAI,CAACsC,GAAG,CAACJ,KAAK,CAAC,GAAGlC,IAAI,CAACsC,GAAG,CAACH,CAAC,CAAC,CAAC;IACnD,OAAOI,UAAU,CAAC,CAACL,KAAK,GAAGlC,IAAI,CAACwC,GAAG,CAACL,CAAC,EAAEjB,CAAC,CAAC,EAAEuB,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGL,KAAK,CAAClB,CAAC,CAAC;EACzE,CAAC;;EAED;EACA,MAAMwB,aAAa,GAAGA,CAACxC,MAAM,EAAEC,MAAM,KAAK;IACxC,QAAQD,MAAM;MACZ,KAAK,SAAS;QACZ,oBAAO7C,OAAA,CAACd,QAAQ;UAAA8E,QAAA,EAAAsB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACrB,KAAK,SAAS;QACZ,OAAO1C,MAAM,aAANA,MAAM,eAANA,MAAM,CAAEwB,MAAM,gBAAGtE,OAAA,CAACN,eAAe;UAAC+F,KAAK,EAAC;QAAS;UAAAzB,QAAA,EAAAsB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAAGxF,OAAA,CAACR,WAAW;UAACiG,KAAK,EAAC;QAAS;UAAAzB,QAAA,EAAAsB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC/F,KAAK,OAAO;QACV,oBAAOxF,OAAA,CAACJ,SAAS;UAAC6F,KAAK,EAAC;QAAO;UAAAzB,QAAA,EAAAsB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACpC;QACE,oBAAOxF,OAAA,CAACd,QAAQ;UAAA8E,QAAA,EAAAsB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;IACvB;EACF,CAAC;EAED,oBACExF,OAAA,CAAC5B,GAAG;IACFsH,EAAE,EAAE;MACFC,UAAU,EAAGC,KAAK,IAAKA,KAAK,CAACC,OAAO,CAACC,IAAI,KAAK,MAAM,GAChD,iFAAiF,GACjF,uFAAuF;MAC3FC,cAAc,EAAE,YAAY;MAC5BC,YAAY,EAAE,CAAC;MACfC,MAAM,EAAGL,KAAK,IAAK,aAAaA,KAAK,CAACC,OAAO,CAACK,OAAO,EAAE;MACvDC,CAAC,EAAE,CAAC;MACJC,QAAQ,EAAE,UAAU;MACpBC,QAAQ,EAAE,QAAQ;MAClB,WAAW,EAAE;QACXC,OAAO,EAAE,IAAI;QACbF,QAAQ,EAAE,UAAU;QACpBG,GAAG,EAAE,CAAC;QACNC,IAAI,EAAE,CAAC;QACPC,KAAK,EAAE,CAAC;QACRC,MAAM,EAAE,KAAK;QACbf,UAAU,EAAE;MACd;IACF,CAAE;IAAAgB,QAAA,gBAGF3G,OAAA,CAAC5B,GAAG;MACFwI,OAAO,EAAC,MAAM;MACdC,UAAU,EAAC,QAAQ;MACnBC,cAAc,EAAC,QAAQ;MACvBC,EAAE,EAAE,CAAE;MACNrB,EAAE,EAAE;QACFsB,SAAS,EAAE;MACb,CAAE;MAAAL,QAAA,gBAEF3G,OAAA,CAAC5B,GAAG;QACFsH,EAAE,EAAE;UACFC,UAAU,EAAE,mDAAmD;UAC/DK,YAAY,EAAE,KAAK;UACnBiB,KAAK,EAAE,EAAE;UACTP,MAAM,EAAE,EAAE;UACVE,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBC,cAAc,EAAE,QAAQ;UACxBI,EAAE,EAAE,CAAC;UACLC,SAAS,EAAE;QACb,CAAE;QAAAR,QAAA,eAEF3G,OAAA,CAAChB,eAAe;UAAC0G,EAAE,EAAE;YAAED,KAAK,EAAE,OAAO;YAAE2B,QAAQ,EAAE;UAAG;QAAE;UAAApD,QAAA,EAAAsB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAxB,QAAA,EAAAsB,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtD,CAAC,eACNxF,OAAA,CAAC5B,GAAG;QAAAuI,QAAA,gBACF3G,OAAA,CAAC7B,UAAU;UAACkJ,OAAO,EAAC,IAAI;UAACC,SAAS,EAAC,IAAI;UAACC,UAAU,EAAC,MAAM;UAACC,YAAY;UAAAb,QAAA,EAAC;QAEvE;UAAA3C,QAAA,EAAAsB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbxF,OAAA,CAAC7B,UAAU;UAACkJ,OAAO,EAAC,OAAO;UAAC5B,KAAK,EAAC,gBAAgB;UAAAkB,QAAA,EAAC;QAEnD;UAAA3C,QAAA,EAAAsB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAxB,QAAA,EAAAsB,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAxB,QAAA,EAAAsB,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNxF,OAAA,CAAC5B,GAAG;MAAA,GACE4E,YAAY,CAAC,CAAC;MAClB0C,EAAE,EAAE;QACFO,MAAM,EAAE,YAAY;QACpBwB,WAAW,EAAEvE,YAAY,GAAG,cAAc,GAAG,UAAU;QACvD8C,YAAY,EAAE,CAAC;QACfG,CAAC,EAAE,CAAC;QACJa,SAAS,EAAE,QAAQ;QACnBU,MAAM,EAAE,SAAS;QACjB/B,UAAU,EAAEzC,YAAY,GACpB,oFAAoF,GACnF0C,KAAK,IAAKA,KAAK,CAACC,OAAO,CAACC,IAAI,KAAK,MAAM,GACtC,2BAA2B,GAC3B,qBAAqB;QAC3B6B,UAAU,EAAE,eAAe;QAC3BZ,EAAE,EAAE,CAAC;QACLX,QAAQ,EAAE,UAAU;QACpBC,QAAQ,EAAE,QAAQ;QAClB,SAAS,EAAE;UACToB,WAAW,EAAE,cAAc;UAC3B9B,UAAU,EAAE,sFAAsF;UAClGiC,SAAS,EAAE;QACb,CAAC;QACD,WAAW,EAAE;UACXtB,OAAO,EAAE,IAAI;UACbF,QAAQ,EAAE,UAAU;UACpBG,GAAG,EAAE,CAAC;UACNC,IAAI,EAAE,CAAC;UACPC,KAAK,EAAE,CAAC;UACRoB,MAAM,EAAE,CAAC;UACTlC,UAAU,EAAEzC,YAAY,GACpB,wFAAwF,GACxF,MAAM;UACV4E,SAAS,EAAE5E,YAAY,GAAG,uBAAuB,GAAG;QACtD,CAAC;QACD,oBAAoB,EAAE;UACpB,IAAI,EAAE;YAAE0E,SAAS,EAAE;UAAoB,CAAC;UACxC,MAAM,EAAE;YAAEA,SAAS,EAAE;UAAmB;QAC1C;MACF,CAAE;MAAAjB,QAAA,gBAEF3G,OAAA;QAAA,GAAWiD,aAAa,CAAC;MAAC;QAAAe,QAAA,EAAAsB,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eAE9BxF,OAAA,CAAC5B,GAAG;QACFsH,EAAE,EAAE;UACFC,UAAU,EAAE,oFAAoF;UAChGK,YAAY,EAAE,KAAK;UACnBiB,KAAK,EAAE,EAAE;UACTP,MAAM,EAAE,EAAE;UACVE,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBC,cAAc,EAAE,QAAQ;UACxBiB,EAAE,EAAE,MAAM;UACVhB,EAAE,EAAE,CAAC;UACLY,UAAU,EAAE,qBAAqB;UACjCC,SAAS,EAAE1E,YAAY,GAAG,YAAY,GAAG;QAC3C,CAAE;QAAAyD,QAAA,eAEF3G,OAAA,CAAChB,eAAe;UAAC0G,EAAE,EAAE;YAAE0B,QAAQ,EAAE,EAAE;YAAE3B,KAAK,EAAE;UAAe;QAAE;UAAAzB,QAAA,EAAAsB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAxB,QAAA,EAAAsB,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7D,CAAC,eAENxF,OAAA,CAAC7B,UAAU;QAACkJ,OAAO,EAAC,IAAI;QAACG,YAAY;QAACD,UAAU,EAAC,KAAK;QAAAZ,QAAA,EACnDzD,YAAY,GAAG,yBAAyB,GAAG;MAAwB;QAAAc,QAAA,EAAAsB,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1D,CAAC,eACbxF,OAAA,CAAC7B,UAAU;QAACkJ,OAAO,EAAC,IAAI;QAAC5B,KAAK,EAAC,gBAAgB;QAAC+B,YAAY;QAAC9B,EAAE,EAAE;UAAEqB,EAAE,EAAE;QAAE,CAAE;QAAAJ,QAAA,EAAC;MAE5E;QAAA3C,QAAA,EAAAsB,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbxF,OAAA,CAAC7B,UAAU;QAACkJ,OAAO,EAAC,OAAO;QAAC5B,KAAK,EAAC,gBAAgB;QAACC,EAAE,EAAE;UAAEqB,EAAE,EAAE;QAAE,CAAE;QAAAJ,QAAA,EAAC;MAElE;QAAA3C,QAAA,EAAAsB,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbxF,OAAA,CAAC7B,UAAU;QAACkJ,OAAO,EAAC,OAAO;QAAC5B,KAAK,EAAC,gBAAgB;QAAAkB,QAAA,EAAC;MAEnD;QAAA3C,QAAA,EAAAsB,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC;IAAA;MAAAxB,QAAA,EAAAsB,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,EAGHnF,KAAK,CAACqD,MAAM,GAAG,CAAC,iBACf1D,OAAA,CAAC5B,GAAG;MAAC2I,EAAE,EAAE,CAAE;MAAAJ,QAAA,gBACT3G,OAAA,CAAC7B,UAAU;QAACkJ,OAAO,EAAC,WAAW;QAACG,YAAY;QAAAb,QAAA,GAAC,kBAC3B,EAACtG,KAAK,CAACqD,MAAM,EAAC,GAChC;MAAA;QAAAM,QAAA,EAAAsB,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbxF,OAAA,CAACvB,IAAI;QAAAkI,QAAA,EACFtG,KAAK,CAAC6B,GAAG,CAAC,CAAC4B,QAAQ,EAAEkE,KAAK,kBACzBhI,OAAA,CAACnC,KAAK,CAACoK,QAAQ;UAAAtB,QAAA,gBACb3G,OAAA,CAACtB,QAAQ;YACPwJ,eAAe,eACblI,OAAA,CAACnB,UAAU;cACTsJ,IAAI,EAAC,KAAK;cACVC,OAAO,EAAEA,CAAA,KAAMhF,UAAU,CAACU,QAAQ,CAACtB,EAAE,CAAE;cACvC6F,QAAQ,EAAE9H,UAAW;cAAAoG,QAAA,eAErB3G,OAAA,CAACZ,UAAU;gBAAA4E,QAAA,EAAAsB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAxB,QAAA,EAAAsB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CACb;YAAAmB,QAAA,gBAED3G,OAAA,CAACrB,YAAY;cAAAgI,QAAA,EACVtB,aAAa,CAACvB,QAAQ,CAACjB,MAAM,EAAEiB,QAAQ,CAAChB,MAAM;YAAC;cAAAkB,QAAA,EAAAsB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC,CAAC,eACfxF,OAAA,CAACpB,YAAY;cACX0J,OAAO,EAAExE,QAAQ,CAAC5C,IAAI,CAACe,IAAK;cAC5BsG,SAAS,eACPvI,OAAA,CAAC5B,GAAG;gBAAAuI,QAAA,gBACF3G,OAAA,CAAC7B,UAAU;kBAACkJ,OAAO,EAAC,SAAS;kBAACT,OAAO,EAAC,OAAO;kBAAAD,QAAA,EAC1C/B,cAAc,CAACd,QAAQ,CAAC5C,IAAI,CAACG,IAAI;gBAAC;kBAAA2C,QAAA,EAAAsB,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB,CAAC,EACZ1B,QAAQ,CAACjB,MAAM,KAAK,OAAO,iBAC1B7C,OAAA,CAAC7B,UAAU;kBAACkJ,OAAO,EAAC,SAAS;kBAAC5B,KAAK,EAAC,OAAO;kBAAAkB,QAAA,GAAC,SACnC,EAAC7C,QAAQ,CAACvC,KAAK;gBAAA;kBAAAyC,QAAA,EAAAsB,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACZ,CACb,EACA1B,QAAQ,CAAChB,MAAM,iBACd9C,OAAA,CAAC5B,GAAG;kBAACoK,EAAE,EAAE,CAAE;kBAAA7B,QAAA,eACT3G,OAAA,CAACxB,IAAI;oBACH6C,IAAI,EAAC,OAAO;oBACZoH,KAAK,EAAE3E,QAAQ,CAAChB,MAAM,CAACwB,MAAM,GAAG,OAAO,GAAG,iBAAkB;oBAC5DmB,KAAK,EAAE3B,QAAQ,CAAChB,MAAM,CAACwB,MAAM,GAAG,SAAS,GAAG,OAAQ;oBACpD+C,OAAO,EAAC;kBAAU;oBAAArD,QAAA,EAAAsB,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnB;gBAAC;kBAAAxB,QAAA,EAAAsB,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CACN;cAAA;gBAAAxB,QAAA,EAAAsB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YACN;cAAAxB,QAAA,EAAAsB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAxB,QAAA,EAAAsB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC,EACVwC,KAAK,GAAG3H,KAAK,CAACqD,MAAM,GAAG,CAAC,iBAAI1D,OAAA,CAAClB,OAAO;YAAAkF,QAAA,EAAAsB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA,GAzCrB1B,QAAQ,CAACtB,EAAE;UAAAwB,QAAA,EAAAsB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA0ChB,CACjB;MAAC;QAAAxB,QAAA,EAAAsB,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAxB,QAAA,EAAAsB,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CACN,EAGAjF,UAAU,iBACTP,OAAA,CAAC5B,GAAG;MAAC2I,EAAE,EAAE,CAAE;MAAAJ,QAAA,gBACT3G,OAAA,CAAC7B,UAAU;QAACkJ,OAAO,EAAC,OAAO;QAACG,YAAY;QAAAb,QAAA,GAAC,oBACrB,EAAChE,IAAI,CAAC+F,KAAK,CAAC/H,cAAc,CAAC,EAAC,GAChD;MAAA;QAAAqD,QAAA,EAAAsB,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbxF,OAAA,CAAC1B,cAAc;QAAC+I,OAAO,EAAC,aAAa;QAACsB,KAAK,EAAEhI;MAAe;QAAAqD,QAAA,EAAAsB,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAxB,QAAA,EAAAsB,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5D,CACN,eAGDxF,OAAA,CAAC3B,MAAM;MACLgJ,OAAO,EAAC,WAAW;MACnB5B,KAAK,EAAC,SAAS;MACf2C,OAAO,EAAE3E,eAAgB;MACzB4E,QAAQ,EAAEhI,KAAK,CAACqD,MAAM,KAAK,CAAC,IAAInD,UAAW;MAC3CqI,SAAS,eAAE5I,OAAA,CAACV,YAAY;QAAA0E,QAAA,EAAAsB,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAE;MAC5BqD,SAAS;MACTxH,IAAI,EAAC,OAAO;MAAAsF,QAAA,EAEXpG,UAAU,GAAG,mBAAmB,GAAG,QAAQF,KAAK,CAACqD,MAAM,QAAQrD,KAAK,CAACqD,MAAM,KAAK,CAAC,GAAG,GAAG,GAAG,EAAE;IAAE;MAAAM,QAAA,EAAAsB,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzF,CAAC,EAGR/E,WAAW,CAACiD,MAAM,GAAG,CAAC,IAAI,CAACnD,UAAU,iBACpCP,OAAA,CAAC5B,GAAG;MAACoK,EAAE,EAAE,CAAE;MAAA7B,QAAA,eACT3G,OAAA,CAACzB,KAAK;QACJuK,QAAQ,EAAErI,WAAW,CAACsI,IAAI,CAACvF,CAAC,IAAIA,CAAC,CAACV,MAAM,IAAI,CAACU,CAAC,CAACV,MAAM,CAACwB,MAAM,CAAC,GAAG,SAAS,GAAG,SAAU;QACtF0E,IAAI,eAAEhJ,OAAA,CAACV,YAAY;UAAA0E,QAAA,EAAAsB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAAAmB,QAAA,gBAEvB3G,OAAA,CAAC7B,UAAU;UAACkJ,OAAO,EAAC,WAAW;UAACE,UAAU,EAAC,MAAM;UAAAZ,QAAA,EAAC;QAElD;UAAA3C,QAAA,EAAAsB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbxF,OAAA,CAAC7B,UAAU;UAACkJ,OAAO,EAAC,OAAO;UAAAV,QAAA,GACxBlG,WAAW,CAAC6C,MAAM,CAACE,CAAC;YAAA,IAAAyF,SAAA;YAAA,QAAAA,SAAA,GAAIzF,CAAC,CAACV,MAAM,cAAAmG,SAAA,uBAARA,SAAA,CAAU3E,MAAM;UAAA,EAAC,CAACZ,MAAM,EAAC,gBAAc,EAAC,GAAG,EACnEjD,WAAW,CAAC6C,MAAM,CAACE,CAAC,IAAIA,CAAC,CAACV,MAAM,IAAI,CAACU,CAAC,CAACV,MAAM,CAACwB,MAAM,CAAC,CAACZ,MAAM,EAAC,qBAAmB,EAAC,GAAG,EACpFjD,WAAW,CAAC6C,MAAM,CAACE,CAAC,IAAIA,CAAC,CAACjC,KAAK,CAAC,CAACmC,MAAM,EAAC,SAC3C;QAAA;UAAAM,QAAA,EAAAsB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAxB,QAAA,EAAAsB,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAxB,QAAA,EAAAsB,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACN;EAAA;IAAAxB,QAAA,EAAAsB,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEV,CAAC;EAAA,QApboE3F,OAAO,EAiEpB7B,WAAW;AAAA,EAmXlE,CAAC;EAAA,QApbmE6B,OAAO,EAiEpB7B,WAAW;AAAA,EAmXjE;AAACkL,GAAA,GA1bGjJ,YAAY;AA4blBA,YAAY,CAACkJ,WAAW,GAAG,cAAc;AAEzC,eAAelJ,YAAY;AAAC,IAAAG,EAAA,EAAA8I,GAAA;AAAAE,YAAA,CAAAhJ,EAAA;AAAAgJ,YAAA,CAAAF,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}