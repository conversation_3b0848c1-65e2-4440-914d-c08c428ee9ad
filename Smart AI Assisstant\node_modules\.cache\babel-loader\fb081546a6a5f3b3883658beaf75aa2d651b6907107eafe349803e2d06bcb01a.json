{"ast": null, "code": "var _jsxFileName = \"E:\\\\PythonProjects\\\\React.js\\\\Smart AI Assisstant\\\\src\\\\components\\\\Header.jsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { AppBar, Toolbar, Typography, IconButton, Switch, FormControlLabel, Box, Button, Menu, MenuItem, Container, Tooltip, Avatar, Badge, Divider } from '@mui/material';\nimport { Security as SecurityIcon, DarkMode as DarkModeIcon, LightMode as LightModeIcon, Language as LanguageIcon, History as HistoryIcon, Home as HomeIcon, Info as InfoIcon, Notifications as NotificationsIcon, Settings as SettingsIcon, Star as StarIcon } from '@mui/icons-material';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport { useScan } from '../contexts/ScanContext';\nimport EnhancedSettingsDialog from './EnhancedSettingsDialog';\nimport NotificationDetailDialog from './NotificationDetailDialog';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Header = /*#__PURE__*/_s(/*#__PURE__*/React.memo(_c = _s(() => {\n  _s();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const {\n    darkMode,\n    language,\n    setLanguage,\n    notifications\n  } = useScan();\n  const [languageAnchor, setLanguageAnchor] = React.useState(null);\n  const [settingsAnchor, setSettingsAnchor] = React.useState(null);\n  const [notificationAnchor, setNotificationAnchor] = React.useState(null);\n  const [settingsDialogOpen, setSettingsDialogOpen] = React.useState(false);\n  const [selectedNotification, setSelectedNotification] = React.useState(null);\n  const [notificationDetailOpen, setNotificationDetailOpen] = React.useState(false);\n\n  // Sample notification history\n  const [notificationHistory, setNotificationHistory] = React.useState([{\n    id: 1,\n    title: 'Welcome to AI Security Guard',\n    message: 'You successfully logged in to the Platform. Have a nice tour!',\n    timestamp: new Date().toISOString(),\n    type: 'success',\n    read: false\n  }, {\n    id: 2,\n    title: 'Security Scan Completed',\n    message: 'Your recent URL scan has been completed successfully.',\n    timestamp: new Date(Date.now() - 3600000).toISOString(),\n    type: 'info',\n    read: true\n  }, {\n    id: 3,\n    title: 'Threat Detected',\n    message: 'A potential threat was detected and blocked automatically.',\n    timestamp: new Date(Date.now() - 7200000).toISOString(),\n    type: 'warning',\n    read: true\n  }]);\n  const handleLanguageClick = event => {\n    setLanguageAnchor(event.currentTarget);\n  };\n  const handleLanguageClose = () => {\n    setLanguageAnchor(null);\n  };\n  const handleLanguageSelect = lang => {\n    setLanguage(lang);\n    handleLanguageClose();\n  };\n  const handleSettingsClick = event => {\n    setSettingsAnchor(event.currentTarget);\n  };\n  const handleSettingsClose = () => {\n    setSettingsAnchor(null);\n  };\n  const handleOpenSettingsDialog = () => {\n    setSettingsDialogOpen(true);\n    handleSettingsClose();\n  };\n  const handleNotificationClick = event => {\n    setNotificationAnchor(event.currentTarget);\n  };\n  const handleNotificationClose = () => {\n    setNotificationAnchor(null);\n  };\n  const markNotificationAsRead = notificationId => {\n    setNotificationHistory(prev => prev.map(notif => notif.id === notificationId ? {\n      ...notif,\n      read: true\n    } : notif));\n  };\n  const handleNotificationDetailClick = notification => {\n    setSelectedNotification(notification);\n    setNotificationDetailOpen(true);\n    setNotificationAnchor(null);\n    markNotificationAsRead(notification.id);\n  };\n  const unreadCount = notificationHistory.filter(notif => !notif.read).length;\n  const isHomePage = location.pathname === '/';\n  const isSmartFeaturesPage = location.pathname === '/smart-features';\n  const isHistoryPage = location.pathname === '/history';\n  const isAboutPage = location.pathname === '/about';\n  const isPlansPage = location.pathname === '/plans';\n  const navigationItems = [{\n    label: 'Home',\n    path: '/',\n    icon: /*#__PURE__*/_jsxDEV(HomeIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 133,\n      columnNumber: 39\n    }, this),\n    active: isHomePage\n  }, {\n    label: 'Smart Features',\n    path: '/smart-features',\n    icon: /*#__PURE__*/_jsxDEV(SecurityIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 134,\n      columnNumber: 63\n    }, this),\n    active: isSmartFeaturesPage\n  }, {\n    label: 'History',\n    path: '/history',\n    icon: /*#__PURE__*/_jsxDEV(HistoryIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 135,\n      columnNumber: 49\n    }, this),\n    active: isHistoryPage\n  }, {\n    label: 'Plans',\n    path: '/plans',\n    icon: /*#__PURE__*/_jsxDEV(StarIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 136,\n      columnNumber: 45\n    }, this),\n    active: isPlansPage\n  }, {\n    label: 'About',\n    path: '/about',\n    icon: /*#__PURE__*/_jsxDEV(InfoIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 137,\n      columnNumber: 45\n    }, this),\n    active: isAboutPage\n  }];\n  return /*#__PURE__*/_jsxDEV(AppBar, {\n    position: \"sticky\",\n    elevation: 0,\n    sx: {\n      background: theme => theme.palette.mode === 'dark' ? 'linear-gradient(135deg, rgba(12, 12, 12, 0.98) 0%, rgba(26, 26, 26, 0.98) 50%, rgba(45, 45, 45, 0.98) 100%)' : 'linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%)',\n      backdropFilter: 'blur(25px) saturate(180%)',\n      borderBottom: theme => theme.palette.mode === 'dark' ? '1px solid rgba(255, 255, 255, 0.1)' : '1px solid rgba(255, 255, 255, 0.2)',\n      boxShadow: theme => theme.palette.mode === 'dark' ? '0 4px 20px rgba(102, 126, 234, 0.1)' : '0 4px 20px rgba(102, 126, 234, 0.3)',\n      position: 'relative',\n      color: 'white',\n      '&::after': {\n        content: '\"\"',\n        position: 'absolute',\n        bottom: 0,\n        left: 0,\n        right: 0,\n        height: '2px',\n        background: theme => theme.palette.mode === 'dark' ? 'linear-gradient(90deg, transparent 0%, #667eea 50%, transparent 100%)' : 'linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.8) 50%, transparent 100%)',\n        opacity: 0.6\n      }\n    },\n    children: [/*#__PURE__*/_jsxDEV(Container, {\n      maxWidth: \"xl\",\n      children: /*#__PURE__*/_jsxDEV(Toolbar, {\n        sx: {\n          justifyContent: 'space-between',\n          py: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          display: \"flex\",\n          alignItems: \"center\",\n          gap: 2,\n          children: [/*#__PURE__*/_jsxDEV(IconButton, {\n            edge: \"start\",\n            color: \"inherit\",\n            \"aria-label\": \"security logo\",\n            onClick: () => navigate('/'),\n            sx: {\n              background: 'rgba(255,255,255,0.1)',\n              backdropFilter: 'blur(10px)',\n              '&:hover': {\n                background: 'rgba(255,255,255,0.2)',\n                transform: 'scale(1.05)'\n              },\n              transition: 'all 0.2s ease'\n            },\n            children: /*#__PURE__*/_jsxDEV(SecurityIcon, {\n              fontSize: \"large\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 190,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 175,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h4\",\n              component: \"h1\",\n              sx: {\n                fontWeight: 800,\n                cursor: 'pointer',\n                display: {\n                  xs: 'none',\n                  md: 'block'\n                },\n                color: 'white',\n                textShadow: '0 2px 8px rgba(0,0,0,0.3)',\n                '&:hover': {\n                  textShadow: '0 2px 12px rgba(255,255,255,0.3)'\n                },\n                transition: 'all 0.3s ease'\n              },\n              onClick: () => navigate('/'),\n              children: \"AI Security Guard\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 193,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              component: \"h1\",\n              sx: {\n                fontWeight: 700,\n                cursor: 'pointer',\n                display: {\n                  xs: 'block',\n                  md: 'none'\n                },\n                color: 'white'\n              },\n              onClick: () => navigate('/'),\n              children: \"AI Security\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 211,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              sx: {\n                display: {\n                  xs: 'none',\n                  md: 'block'\n                },\n                background: 'linear-gradient(90deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%)',\n                backgroundSize: '200% 200%',\n                backgroundClip: 'text',\n                WebkitBackgroundClip: 'text',\n                WebkitTextFillColor: 'transparent',\n                animation: 'movingText 4s ease infinite',\n                fontSize: '0.75rem',\n                fontWeight: 600,\n                letterSpacing: '0.5px',\n                textTransform: 'uppercase',\n                '@keyframes movingText': {\n                  '0%': {\n                    backgroundPosition: '0% 50%'\n                  },\n                  '50%': {\n                    backgroundPosition: '100% 50%'\n                  },\n                  '100%': {\n                    backgroundPosition: '0% 50%'\n                  }\n                }\n              },\n              children: \"Advanced Threat Detection Platform\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 224,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 192,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 174,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          display: \"flex\",\n          alignItems: \"center\",\n          gap: 2,\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            display: {\n              xs: 'none',\n              md: 'flex'\n            },\n            gap: 1,\n            children: navigationItems.map(item => /*#__PURE__*/_jsxDEV(Button, {\n              color: \"inherit\",\n              startIcon: item.icon,\n              onClick: () => navigate(item.path),\n              variant: item.active ? 'contained' : 'text',\n              sx: {\n                borderRadius: 3,\n                px: 3,\n                py: 1,\n                fontWeight: 600,\n                textTransform: 'none',\n                background: item.active ? 'rgba(255,255,255,0.2)' : 'transparent',\n                backdropFilter: item.active ? 'blur(10px)' : 'none',\n                border: item.active ? '1px solid rgba(255,255,255,0.3)' : 'none',\n                '&:hover': {\n                  background: 'rgba(255,255,255,0.15)',\n                  transform: 'translateY(-1px)'\n                },\n                transition: 'all 0.2s ease'\n              },\n              children: item.label\n            }, item.path, false, {\n              fileName: _jsxFileName,\n              lineNumber: 255,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 253,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            display: {\n              xs: 'flex',\n              md: 'none'\n            },\n            gap: 0.5,\n            children: navigationItems.map(item => /*#__PURE__*/_jsxDEV(Tooltip, {\n              title: item.label,\n              children: /*#__PURE__*/_jsxDEV(IconButton, {\n                color: \"inherit\",\n                onClick: () => navigate(item.path),\n                sx: {\n                  background: item.active ? 'rgba(255,255,255,0.2)' : 'transparent',\n                  backdropFilter: item.active ? 'blur(10px)' : 'none',\n                  border: item.active ? '1px solid rgba(255,255,255,0.3)' : 'none',\n                  '&:hover': {\n                    background: 'rgba(255,255,255,0.15)'\n                  }\n                },\n                children: item.icon\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 288,\n                columnNumber: 19\n              }, this)\n            }, item.path, false, {\n              fileName: _jsxFileName,\n              lineNumber: 287,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 285,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n            title: \"Notifications\",\n            children: /*#__PURE__*/_jsxDEV(IconButton, {\n              color: \"inherit\",\n              onClick: handleNotificationClick,\n              sx: {\n                background: 'rgba(255,255,255,0.1)',\n                '&:hover': {\n                  background: 'rgba(255,255,255,0.2)'\n                }\n              },\n              children: /*#__PURE__*/_jsxDEV(Badge, {\n                badgeContent: unreadCount,\n                color: \"error\",\n                children: /*#__PURE__*/_jsxDEV(NotificationsIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 321,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 320,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 310,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 309,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n            title: \"Settings\",\n            children: /*#__PURE__*/_jsxDEV(IconButton, {\n              color: \"inherit\",\n              onClick: handleSettingsClick,\n              sx: {\n                background: 'rgba(255,255,255,0.1)',\n                '&:hover': {\n                  background: 'rgba(255,255,255,0.2)'\n                }\n              },\n              children: /*#__PURE__*/_jsxDEV(SettingsIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 338,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 328,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 327,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Menu, {\n            anchorEl: settingsAnchor,\n            open: Boolean(settingsAnchor),\n            onClose: handleSettingsClose,\n            anchorOrigin: {\n              vertical: 'bottom',\n              horizontal: 'right'\n            },\n            transformOrigin: {\n              vertical: 'top',\n              horizontal: 'right'\n            },\n            PaperProps: {\n              sx: {\n                mt: 1,\n                borderRadius: 2,\n                minWidth: 200\n              }\n            },\n            children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: handleOpenSettingsDialog,\n              sx: {\n                gap: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(SettingsIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 364,\n                columnNumber: 17\n              }, this), \"Account & Settings\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 363,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: handleLanguageClick,\n              sx: {\n                gap: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(LanguageIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 370,\n                columnNumber: 17\n              }, this), \"Language\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 369,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => setSettingsDialogOpen(true),\n              sx: {\n                gap: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(SettingsIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 376,\n                columnNumber: 17\n              }, this), \"Settings\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 375,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 342,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Menu, {\n            anchorEl: languageAnchor,\n            open: Boolean(languageAnchor),\n            onClose: handleLanguageClose,\n            anchorOrigin: {\n              vertical: 'bottom',\n              horizontal: 'right'\n            },\n            transformOrigin: {\n              vertical: 'top',\n              horizontal: 'right'\n            },\n            PaperProps: {\n              sx: {\n                mt: 1,\n                borderRadius: 2\n              }\n            },\n            children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => handleLanguageSelect('en'),\n              selected: language === 'en',\n              children: \"\\uD83C\\uDDFA\\uD83C\\uDDF8 English\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 401,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => handleLanguageSelect('ar'),\n              selected: language === 'ar',\n              children: \"\\uD83C\\uDDF8\\uD83C\\uDDE6 \\u0627\\u0644\\u0639\\u0631\\u0628\\u064A\\u0629\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 407,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 382,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Menu, {\n            anchorEl: notificationAnchor,\n            open: Boolean(notificationAnchor),\n            onClose: handleNotificationClose,\n            anchorOrigin: {\n              vertical: 'bottom',\n              horizontal: 'right'\n            },\n            transformOrigin: {\n              vertical: 'top',\n              horizontal: 'right'\n            },\n            PaperProps: {\n              sx: {\n                mt: 1,\n                borderRadius: 3,\n                minWidth: 350,\n                maxWidth: 400,\n                maxHeight: 500,\n                background: theme => theme.palette.mode === 'dark' ? 'linear-gradient(135deg, rgba(12, 12, 12, 0.98) 0%, rgba(26, 26, 26, 0.98) 100%)' : 'linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(248, 250, 252, 0.98) 100%)',\n                backdropFilter: 'blur(25px)',\n                border: '1px solid rgba(102, 126, 234, 0.2)',\n                boxShadow: '0 20px 60px rgba(0, 0, 0, 0.3)'\n              }\n            },\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                p: 2,\n                borderBottom: '1px solid rgba(102, 126, 234, 0.2)'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                fontWeight: \"bold\",\n                color: \"primary.main\",\n                children: \"Notifications\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 445,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                children: unreadCount > 0 ? `${unreadCount} unread notifications` : 'All caught up!'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 448,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 444,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                maxHeight: 350,\n                overflow: 'auto'\n              },\n              children: notificationHistory.length === 0 ? /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  p: 3,\n                  textAlign: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(NotificationsIcon, {\n                  sx: {\n                    fontSize: 48,\n                    color: 'text.secondary',\n                    mb: 2\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 455,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: \"No notifications yet\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 456,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 454,\n                columnNumber: 19\n              }, this) : notificationHistory.map((notification, index) => /*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                  onClick: () => handleNotificationDetailClick(notification),\n                  sx: {\n                    py: 2,\n                    px: 3,\n                    alignItems: 'flex-start',\n                    background: !notification.read ? 'rgba(102, 126, 234, 0.05)' : 'transparent',\n                    '&:hover': {\n                      background: 'rgba(102, 126, 234, 0.1)'\n                    }\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      mr: 2,\n                      mt: 0.5\n                    },\n                    children: /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        width: 8,\n                        height: 8,\n                        borderRadius: '50%',\n                        background: notification.type === 'success' ? '#4caf50' : notification.type === 'warning' ? '#ff9800' : notification.type === 'error' ? '#f44336' : '#2196f3',\n                        opacity: notification.read ? 0.3 : 1\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 478,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 477,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      flex: 1\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"subtitle2\",\n                      fontWeight: notification.read ? 400 : 600,\n                      sx: {\n                        mb: 0.5\n                      },\n                      children: notification.title\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 491,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      color: \"text.secondary\",\n                      sx: {\n                        mb: 1,\n                        lineHeight: 1.4\n                      },\n                      children: notification.message\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 498,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"caption\",\n                      color: \"text.secondary\",\n                      children: new Date(notification.timestamp).toLocaleString()\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 505,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 490,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 463,\n                  columnNumber: 23\n                }, this), index < notificationHistory.length - 1 && /*#__PURE__*/_jsxDEV(Divider, {\n                  sx: {\n                    mx: 2\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 511,\n                  columnNumber: 25\n                }, this)]\n              }, notification.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 462,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 452,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 416,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 251,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 172,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 171,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(EnhancedSettingsDialog, {\n      open: settingsDialogOpen,\n      onClose: () => setSettingsDialogOpen(false)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 524,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(NotificationDetailDialog, {\n      open: notificationDetailOpen,\n      onClose: () => setNotificationDetailOpen(false),\n      notification: selectedNotification\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 530,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 141,\n    columnNumber: 5\n  }, this);\n}, \"pWWoQaQlDlOpMLGxHYmsjPjx7S0=\", false, function () {\n  return [useNavigate, useLocation, useScan];\n})), \"pWWoQaQlDlOpMLGxHYmsjPjx7S0=\", false, function () {\n  return [useNavigate, useLocation, useScan];\n});\n_c2 = Header;\nHeader.displayName = 'Header';\nexport default Header;\nvar _c, _c2;\n$RefreshReg$(_c, \"Header$React.memo\");\n$RefreshReg$(_c2, \"Header\");", "map": {"version": 3, "names": ["React", "AppBar", "<PERSON><PERSON><PERSON>", "Typography", "IconButton", "Switch", "FormControlLabel", "Box", "<PERSON><PERSON>", "<PERSON><PERSON>", "MenuItem", "Container", "<PERSON><PERSON><PERSON>", "Avatar", "Badge", "Divider", "Security", "SecurityIcon", "DarkMode", "DarkModeIcon", "LightMode", "LightModeIcon", "Language", "LanguageIcon", "History", "HistoryIcon", "Home", "HomeIcon", "Info", "InfoIcon", "Notifications", "NotificationsIcon", "Settings", "SettingsIcon", "Star", "StarIcon", "useNavigate", "useLocation", "useScan", "EnhancedSettingsDialog", "NotificationDetailDialog", "jsxDEV", "_jsxDEV", "Header", "_s", "memo", "_c", "navigate", "location", "darkMode", "language", "setLanguage", "notifications", "languageAnchor", "setLanguageAnchor", "useState", "settingsAnchor", "setSettingsAnchor", "notificationAnchor", "setNotificationAnchor", "settingsDialogOpen", "setSettingsDialogOpen", "selectedNotification", "setSelectedNotification", "notificationDetailOpen", "setNotificationDetailOpen", "notificationHistory", "setNotificationHistory", "id", "title", "message", "timestamp", "Date", "toISOString", "type", "read", "now", "handleLanguageClick", "event", "currentTarget", "handleLanguageClose", "handleLanguageSelect", "lang", "handleSettingsClick", "handleSettingsClose", "handleOpenSettingsDialog", "handleNotificationClick", "handleNotificationClose", "markNotificationAsRead", "notificationId", "prev", "map", "notif", "handleNotificationDetailClick", "notification", "unreadCount", "filter", "length", "isHomePage", "pathname", "isSmartFeaturesPage", "isHistoryPage", "isAboutPage", "isPlansPage", "navigationItems", "label", "path", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "active", "position", "elevation", "sx", "background", "theme", "palette", "mode", "<PERSON><PERSON>ilter", "borderBottom", "boxShadow", "color", "content", "bottom", "left", "right", "height", "opacity", "children", "max<PERSON><PERSON><PERSON>", "justifyContent", "py", "display", "alignItems", "gap", "edge", "onClick", "transform", "transition", "fontSize", "variant", "component", "fontWeight", "cursor", "xs", "md", "textShadow", "backgroundSize", "backgroundClip", "WebkitBackgroundClip", "WebkitTextFillColor", "animation", "letterSpacing", "textTransform", "backgroundPosition", "item", "startIcon", "borderRadius", "px", "border", "badgeContent", "anchorEl", "open", "Boolean", "onClose", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "transform<PERSON><PERSON>in", "PaperProps", "mt", "min<PERSON><PERSON><PERSON>", "selected", "maxHeight", "p", "overflow", "textAlign", "mb", "index", "mr", "width", "flex", "lineHeight", "toLocaleString", "mx", "_c2", "displayName", "$RefreshReg$"], "sources": ["E:/PythonProjects/React.js/Smart AI Assisstant/src/components/Header.jsx"], "sourcesContent": ["import React from 'react';\nimport {\n  A<PERSON><PERSON><PERSON>,\n  <PERSON><PERSON><PERSON>,\n  <PERSON><PERSON><PERSON>,\n  IconButton,\n  Switch,\n  FormControlLabel,\n  Box,\n  Button,\n  Menu,\n  MenuItem,\n  Container,\n  Tooltip,\n  Avatar,\n  Badge,\n  Divider,\n} from '@mui/material';\nimport {\n  Security as SecurityIcon,\n  DarkMode as DarkModeIcon,\n  LightMode as LightModeIcon,\n  Language as LanguageIcon,\n  History as HistoryIcon,\n  Home as HomeIcon,\n  Info as InfoIcon,\n  Notifications as NotificationsIcon,\n  Settings as SettingsIcon,\n  Star as StarIcon,\n} from '@mui/icons-material';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport { useScan } from '../contexts/ScanContext';\nimport EnhancedSettingsDialog from './EnhancedSettingsDialog';\nimport NotificationDetailDialog from './NotificationDetailDialog';\n\nconst Header = React.memo(() => {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const { darkMode, language, setLanguage, notifications } = useScan();\n  const [languageAnchor, setLanguageAnchor] = React.useState(null);\n  const [settingsAnchor, setSettingsAnchor] = React.useState(null);\n  const [notificationAnchor, setNotificationAnchor] = React.useState(null);\n  const [settingsDialogOpen, setSettingsDialogOpen] = React.useState(false);\n  const [selectedNotification, setSelectedNotification] = React.useState(null);\n  const [notificationDetailOpen, setNotificationDetailOpen] = React.useState(false);\n\n  // Sample notification history\n  const [notificationHistory, setNotificationHistory] = React.useState([\n    {\n      id: 1,\n      title: 'Welcome to AI Security Guard',\n      message: 'You successfully logged in to the Platform. Have a nice tour!',\n      timestamp: new Date().toISOString(),\n      type: 'success',\n      read: false,\n    },\n    {\n      id: 2,\n      title: 'Security Scan Completed',\n      message: 'Your recent URL scan has been completed successfully.',\n      timestamp: new Date(Date.now() - 3600000).toISOString(),\n      type: 'info',\n      read: true,\n    },\n    {\n      id: 3,\n      title: 'Threat Detected',\n      message: 'A potential threat was detected and blocked automatically.',\n      timestamp: new Date(Date.now() - 7200000).toISOString(),\n      type: 'warning',\n      read: true,\n    },\n  ]);\n\n  const handleLanguageClick = (event) => {\n    setLanguageAnchor(event.currentTarget);\n  };\n\n  const handleLanguageClose = () => {\n    setLanguageAnchor(null);\n  };\n\n  const handleLanguageSelect = (lang) => {\n    setLanguage(lang);\n    handleLanguageClose();\n  };\n\n  const handleSettingsClick = (event) => {\n    setSettingsAnchor(event.currentTarget);\n  };\n\n  const handleSettingsClose = () => {\n    setSettingsAnchor(null);\n  };\n\n  const handleOpenSettingsDialog = () => {\n    setSettingsDialogOpen(true);\n    handleSettingsClose();\n  };\n\n  const handleNotificationClick = (event) => {\n    setNotificationAnchor(event.currentTarget);\n  };\n\n  const handleNotificationClose = () => {\n    setNotificationAnchor(null);\n  };\n\n  const markNotificationAsRead = (notificationId) => {\n    setNotificationHistory(prev =>\n      prev.map(notif =>\n        notif.id === notificationId ? { ...notif, read: true } : notif\n      )\n    );\n  };\n\n  const handleNotificationDetailClick = (notification) => {\n    setSelectedNotification(notification);\n    setNotificationDetailOpen(true);\n    setNotificationAnchor(null);\n    markNotificationAsRead(notification.id);\n  };\n\n  const unreadCount = notificationHistory.filter(notif => !notif.read).length;\n\n  const isHomePage = location.pathname === '/';\n  const isSmartFeaturesPage = location.pathname === '/smart-features';\n  const isHistoryPage = location.pathname === '/history';\n  const isAboutPage = location.pathname === '/about';\n  const isPlansPage = location.pathname === '/plans';\n\n  const navigationItems = [\n    { label: 'Home', path: '/', icon: <HomeIcon />, active: isHomePage },\n    { label: 'Smart Features', path: '/smart-features', icon: <SecurityIcon />, active: isSmartFeaturesPage },\n    { label: 'History', path: '/history', icon: <HistoryIcon />, active: isHistoryPage },\n    { label: 'Plans', path: '/plans', icon: <StarIcon />, active: isPlansPage },\n    { label: 'About', path: '/about', icon: <InfoIcon />, active: isAboutPage },\n  ];\n\n  return (\n    <AppBar\n      position=\"sticky\"\n      elevation={0}\n      sx={{\n        background: (theme) => theme.palette.mode === 'dark'\n          ? 'linear-gradient(135deg, rgba(12, 12, 12, 0.98) 0%, rgba(26, 26, 26, 0.98) 50%, rgba(45, 45, 45, 0.98) 100%)'\n          : 'linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%)',\n        backdropFilter: 'blur(25px) saturate(180%)',\n        borderBottom: (theme) => theme.palette.mode === 'dark'\n          ? '1px solid rgba(255, 255, 255, 0.1)'\n          : '1px solid rgba(255, 255, 255, 0.2)',\n        boxShadow: (theme) => theme.palette.mode === 'dark'\n          ? '0 4px 20px rgba(102, 126, 234, 0.1)'\n          : '0 4px 20px rgba(102, 126, 234, 0.3)',\n        position: 'relative',\n        color: 'white',\n        '&::after': {\n          content: '\"\"',\n          position: 'absolute',\n          bottom: 0,\n          left: 0,\n          right: 0,\n          height: '2px',\n          background: (theme) => theme.palette.mode === 'dark'\n            ? 'linear-gradient(90deg, transparent 0%, #667eea 50%, transparent 100%)'\n            : 'linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.8) 50%, transparent 100%)',\n          opacity: 0.6,\n        },\n      }}\n    >\n      <Container maxWidth=\"xl\">\n        <Toolbar sx={{ justifyContent: 'space-between', py: 1 }}>\n          {/* Logo and Title */}\n          <Box display=\"flex\" alignItems=\"center\" gap={2}>\n            <IconButton\n              edge=\"start\"\n              color=\"inherit\"\n              aria-label=\"security logo\"\n              onClick={() => navigate('/')}\n              sx={{\n                background: 'rgba(255,255,255,0.1)',\n                backdropFilter: 'blur(10px)',\n                '&:hover': {\n                  background: 'rgba(255,255,255,0.2)',\n                  transform: 'scale(1.05)',\n                },\n                transition: 'all 0.2s ease',\n              }}\n            >\n              <SecurityIcon fontSize=\"large\" />\n            </IconButton>\n            <Box>\n              <Typography\n                variant=\"h4\"\n                component=\"h1\"\n                sx={{\n                  fontWeight: 800,\n                  cursor: 'pointer',\n                  display: { xs: 'none', md: 'block' },\n                  color: 'white',\n                  textShadow: '0 2px 8px rgba(0,0,0,0.3)',\n                  '&:hover': {\n                    textShadow: '0 2px 12px rgba(255,255,255,0.3)',\n                  },\n                  transition: 'all 0.3s ease',\n                }}\n                onClick={() => navigate('/')}\n              >\n                AI Security Guard\n              </Typography>\n              <Typography\n                variant=\"h6\"\n                component=\"h1\"\n                sx={{\n                  fontWeight: 700,\n                  cursor: 'pointer',\n                  display: { xs: 'block', md: 'none' },\n                  color: 'white',\n                }}\n                onClick={() => navigate('/')}\n              >\n                AI Security\n              </Typography>\n              <Typography\n                variant=\"caption\"\n                sx={{\n                  display: { xs: 'none', md: 'block' },\n                  background: 'linear-gradient(90deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%)',\n                  backgroundSize: '200% 200%',\n                  backgroundClip: 'text',\n                  WebkitBackgroundClip: 'text',\n                  WebkitTextFillColor: 'transparent',\n                  animation: 'movingText 4s ease infinite',\n                  fontSize: '0.75rem',\n                  fontWeight: 600,\n                  letterSpacing: '0.5px',\n                  textTransform: 'uppercase',\n                  '@keyframes movingText': {\n                    '0%': { backgroundPosition: '0% 50%' },\n                    '50%': { backgroundPosition: '100% 50%' },\n                    '100%': { backgroundPosition: '0% 50%' },\n                  },\n                }}\n              >\n                Advanced Threat Detection Platform\n              </Typography>\n            </Box>\n          </Box>\n\n          {/* Navigation and Controls */}\n          <Box display=\"flex\" alignItems=\"center\" gap={2}>\n            {/* Desktop Navigation */}\n            <Box display={{ xs: 'none', md: 'flex' }} gap={1}>\n              {navigationItems.map((item) => (\n                <Button\n                  key={item.path}\n                  color=\"inherit\"\n                  startIcon={item.icon}\n                  onClick={() => navigate(item.path)}\n                  variant={item.active ? 'contained' : 'text'}\n                  sx={{\n                    borderRadius: 3,\n                    px: 3,\n                    py: 1,\n                    fontWeight: 600,\n                    textTransform: 'none',\n                    background: item.active\n                      ? 'rgba(255,255,255,0.2)'\n                      : 'transparent',\n                    backdropFilter: item.active ? 'blur(10px)' : 'none',\n                    border: item.active ? '1px solid rgba(255,255,255,0.3)' : 'none',\n                    '&:hover': {\n                      background: 'rgba(255,255,255,0.15)',\n                      transform: 'translateY(-1px)',\n                    },\n                    transition: 'all 0.2s ease',\n                  }}\n                >\n                  {item.label}\n                </Button>\n              ))}\n            </Box>\n\n            {/* Mobile Navigation */}\n            <Box display={{ xs: 'flex', md: 'none' }} gap={0.5}>\n              {navigationItems.map((item) => (\n                <Tooltip key={item.path} title={item.label}>\n                  <IconButton\n                    color=\"inherit\"\n                    onClick={() => navigate(item.path)}\n                    sx={{\n                      background: item.active\n                        ? 'rgba(255,255,255,0.2)'\n                        : 'transparent',\n                      backdropFilter: item.active ? 'blur(10px)' : 'none',\n                      border: item.active ? '1px solid rgba(255,255,255,0.3)' : 'none',\n                      '&:hover': {\n                        background: 'rgba(255,255,255,0.15)',\n                      },\n                    }}\n                  >\n                    {item.icon}\n                  </IconButton>\n                </Tooltip>\n              ))}\n            </Box>\n\n            {/* Notifications */}\n            <Tooltip title=\"Notifications\">\n              <IconButton\n                color=\"inherit\"\n                onClick={handleNotificationClick}\n                sx={{\n                  background: 'rgba(255,255,255,0.1)',\n                  '&:hover': {\n                    background: 'rgba(255,255,255,0.2)',\n                  },\n                }}\n              >\n                <Badge badgeContent={unreadCount} color=\"error\">\n                  <NotificationsIcon />\n                </Badge>\n              </IconButton>\n            </Tooltip>\n\n            {/* Settings Menu */}\n            <Tooltip title=\"Settings\">\n              <IconButton\n                color=\"inherit\"\n                onClick={handleSettingsClick}\n                sx={{\n                  background: 'rgba(255,255,255,0.1)',\n                  '&:hover': {\n                    background: 'rgba(255,255,255,0.2)',\n                  },\n                }}\n              >\n                <SettingsIcon />\n              </IconButton>\n            </Tooltip>\n\n            <Menu\n              anchorEl={settingsAnchor}\n              open={Boolean(settingsAnchor)}\n              onClose={handleSettingsClose}\n              anchorOrigin={{\n                vertical: 'bottom',\n                horizontal: 'right',\n              }}\n              transformOrigin={{\n                vertical: 'top',\n                horizontal: 'right',\n              }}\n              PaperProps={{\n                sx: {\n                  mt: 1,\n                  borderRadius: 2,\n                  minWidth: 200,\n                },\n              }}\n            >\n              {/* Settings Options */}\n              <MenuItem onClick={handleOpenSettingsDialog} sx={{ gap: 2 }}>\n                <SettingsIcon />\n                Account & Settings\n              </MenuItem>\n\n              {/* Language Selector */}\n              <MenuItem onClick={handleLanguageClick} sx={{ gap: 2 }}>\n                <LanguageIcon />\n                Language\n              </MenuItem>\n\n              {/* Settings */}\n              <MenuItem onClick={() => setSettingsDialogOpen(true)} sx={{ gap: 2 }}>\n                <SettingsIcon />\n                Settings\n              </MenuItem>\n            </Menu>\n\n            {/* Language Menu */}\n            <Menu\n              anchorEl={languageAnchor}\n              open={Boolean(languageAnchor)}\n              onClose={handleLanguageClose}\n              anchorOrigin={{\n                vertical: 'bottom',\n                horizontal: 'right',\n              }}\n              transformOrigin={{\n                vertical: 'top',\n                horizontal: 'right',\n              }}\n              PaperProps={{\n                sx: {\n                  mt: 1,\n                  borderRadius: 2,\n                },\n              }}\n            >\n              <MenuItem\n                onClick={() => handleLanguageSelect('en')}\n                selected={language === 'en'}\n              >\n                🇺🇸 English\n              </MenuItem>\n              <MenuItem\n                onClick={() => handleLanguageSelect('ar')}\n                selected={language === 'ar'}\n              >\n                🇸🇦 العربية\n              </MenuItem>\n            </Menu>\n\n            {/* Notification Menu */}\n            <Menu\n              anchorEl={notificationAnchor}\n              open={Boolean(notificationAnchor)}\n              onClose={handleNotificationClose}\n              anchorOrigin={{\n                vertical: 'bottom',\n                horizontal: 'right',\n              }}\n              transformOrigin={{\n                vertical: 'top',\n                horizontal: 'right',\n              }}\n              PaperProps={{\n                sx: {\n                  mt: 1,\n                  borderRadius: 3,\n                  minWidth: 350,\n                  maxWidth: 400,\n                  maxHeight: 500,\n                  background: (theme) => theme.palette.mode === 'dark'\n                    ? 'linear-gradient(135deg, rgba(12, 12, 12, 0.98) 0%, rgba(26, 26, 26, 0.98) 100%)'\n                    : 'linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(248, 250, 252, 0.98) 100%)',\n                  backdropFilter: 'blur(25px)',\n                  border: '1px solid rgba(102, 126, 234, 0.2)',\n                  boxShadow: '0 20px 60px rgba(0, 0, 0, 0.3)',\n                },\n              }}\n            >\n              <Box sx={{ p: 2, borderBottom: '1px solid rgba(102, 126, 234, 0.2)' }}>\n                <Typography variant=\"h6\" fontWeight=\"bold\" color=\"primary.main\">\n                  Notifications\n                </Typography>\n                <Typography variant=\"body2\" color=\"text.secondary\">\n                  {unreadCount > 0 ? `${unreadCount} unread notifications` : 'All caught up!'}\n                </Typography>\n              </Box>\n              <Box sx={{ maxHeight: 350, overflow: 'auto' }}>\n                {notificationHistory.length === 0 ? (\n                  <Box sx={{ p: 3, textAlign: 'center' }}>\n                    <NotificationsIcon sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />\n                    <Typography variant=\"body2\" color=\"text.secondary\">\n                      No notifications yet\n                    </Typography>\n                  </Box>\n                ) : (\n                  notificationHistory.map((notification, index) => (\n                    <Box key={notification.id}>\n                      <MenuItem\n                        onClick={() => handleNotificationDetailClick(notification)}\n                        sx={{\n                          py: 2,\n                          px: 3,\n                          alignItems: 'flex-start',\n                          background: !notification.read\n                            ? 'rgba(102, 126, 234, 0.05)'\n                            : 'transparent',\n                          '&:hover': {\n                            background: 'rgba(102, 126, 234, 0.1)',\n                          },\n                        }}\n                      >\n                        <Box sx={{ mr: 2, mt: 0.5 }}>\n                          <Box\n                            sx={{\n                              width: 8,\n                              height: 8,\n                              borderRadius: '50%',\n                              background: notification.type === 'success' ? '#4caf50' :\n                                         notification.type === 'warning' ? '#ff9800' :\n                                         notification.type === 'error' ? '#f44336' : '#2196f3',\n                              opacity: notification.read ? 0.3 : 1,\n                            }}\n                          />\n                        </Box>\n                        <Box sx={{ flex: 1 }}>\n                          <Typography\n                            variant=\"subtitle2\"\n                            fontWeight={notification.read ? 400 : 600}\n                            sx={{ mb: 0.5 }}\n                          >\n                            {notification.title}\n                          </Typography>\n                          <Typography\n                            variant=\"body2\"\n                            color=\"text.secondary\"\n                            sx={{ mb: 1, lineHeight: 1.4 }}\n                          >\n                            {notification.message}\n                          </Typography>\n                          <Typography variant=\"caption\" color=\"text.secondary\">\n                            {new Date(notification.timestamp).toLocaleString()}\n                          </Typography>\n                        </Box>\n                      </MenuItem>\n                      {index < notificationHistory.length - 1 && (\n                        <Divider sx={{ mx: 2 }} />\n                      )}\n                    </Box>\n                  ))\n                )}\n              </Box>\n            </Menu>\n\n          </Box>\n        </Toolbar>\n      </Container>\n\n      {/* Settings Dialog */}\n      <EnhancedSettingsDialog\n        open={settingsDialogOpen}\n        onClose={() => setSettingsDialogOpen(false)}\n      />\n\n      {/* Notification Detail Dialog */}\n      <NotificationDetailDialog\n        open={notificationDetailOpen}\n        onClose={() => setNotificationDetailOpen(false)}\n        notification={selectedNotification}\n      />\n    </AppBar>\n  );\n});\n\nHeader.displayName = 'Header';\n\nexport default Header;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SACEC,MAAM,EACNC,OAAO,EACPC,UAAU,EACVC,UAAU,EACVC,MAAM,EACNC,gBAAgB,EAChBC,GAAG,EACHC,MAAM,EACNC,IAAI,EACJC,QAAQ,EACRC,SAAS,EACTC,OAAO,EACPC,MAAM,EACNC,KAAK,EACLC,OAAO,QACF,eAAe;AACtB,SACEC,QAAQ,IAAIC,YAAY,EACxBC,QAAQ,IAAIC,YAAY,EACxBC,SAAS,IAAIC,aAAa,EAC1BC,QAAQ,IAAIC,YAAY,EACxBC,OAAO,IAAIC,WAAW,EACtBC,IAAI,IAAIC,QAAQ,EAChBC,IAAI,IAAIC,QAAQ,EAChBC,aAAa,IAAIC,iBAAiB,EAClCC,QAAQ,IAAIC,YAAY,EACxBC,IAAI,IAAIC,QAAQ,QACX,qBAAqB;AAC5B,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,SAASC,OAAO,QAAQ,yBAAyB;AACjD,OAAOC,sBAAsB,MAAM,0BAA0B;AAC7D,OAAOC,wBAAwB,MAAM,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElE,MAAMC,MAAM,gBAAAC,EAAA,cAAG5C,KAAK,CAAC6C,IAAI,CAAAC,EAAA,GAAAF,EAAA,CAAC,MAAM;EAAAA,EAAA;EAC9B,MAAMG,QAAQ,GAAGX,WAAW,CAAC,CAAC;EAC9B,MAAMY,QAAQ,GAAGX,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEY,QAAQ;IAAEC,QAAQ;IAAEC,WAAW;IAAEC;EAAc,CAAC,GAAGd,OAAO,CAAC,CAAC;EACpE,MAAM,CAACe,cAAc,EAAEC,iBAAiB,CAAC,GAAGtD,KAAK,CAACuD,QAAQ,CAAC,IAAI,CAAC;EAChE,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGzD,KAAK,CAACuD,QAAQ,CAAC,IAAI,CAAC;EAChE,MAAM,CAACG,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG3D,KAAK,CAACuD,QAAQ,CAAC,IAAI,CAAC;EACxE,MAAM,CAACK,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG7D,KAAK,CAACuD,QAAQ,CAAC,KAAK,CAAC;EACzE,MAAM,CAACO,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG/D,KAAK,CAACuD,QAAQ,CAAC,IAAI,CAAC;EAC5E,MAAM,CAACS,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGjE,KAAK,CAACuD,QAAQ,CAAC,KAAK,CAAC;;EAEjF;EACA,MAAM,CAACW,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGnE,KAAK,CAACuD,QAAQ,CAAC,CACnE;IACEa,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,8BAA8B;IACrCC,OAAO,EAAE,+DAA+D;IACxEC,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;IACnCC,IAAI,EAAE,SAAS;IACfC,IAAI,EAAE;EACR,CAAC,EACD;IACEP,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,yBAAyB;IAChCC,OAAO,EAAE,uDAAuD;IAChEC,SAAS,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACI,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC,CAACH,WAAW,CAAC,CAAC;IACvDC,IAAI,EAAE,MAAM;IACZC,IAAI,EAAE;EACR,CAAC,EACD;IACEP,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,iBAAiB;IACxBC,OAAO,EAAE,4DAA4D;IACrEC,SAAS,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACI,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC,CAACH,WAAW,CAAC,CAAC;IACvDC,IAAI,EAAE,SAAS;IACfC,IAAI,EAAE;EACR,CAAC,CACF,CAAC;EAEF,MAAME,mBAAmB,GAAIC,KAAK,IAAK;IACrCxB,iBAAiB,CAACwB,KAAK,CAACC,aAAa,CAAC;EACxC,CAAC;EAED,MAAMC,mBAAmB,GAAGA,CAAA,KAAM;IAChC1B,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;EAED,MAAM2B,oBAAoB,GAAIC,IAAI,IAAK;IACrC/B,WAAW,CAAC+B,IAAI,CAAC;IACjBF,mBAAmB,CAAC,CAAC;EACvB,CAAC;EAED,MAAMG,mBAAmB,GAAIL,KAAK,IAAK;IACrCrB,iBAAiB,CAACqB,KAAK,CAACC,aAAa,CAAC;EACxC,CAAC;EAED,MAAMK,mBAAmB,GAAGA,CAAA,KAAM;IAChC3B,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;EAED,MAAM4B,wBAAwB,GAAGA,CAAA,KAAM;IACrCxB,qBAAqB,CAAC,IAAI,CAAC;IAC3BuB,mBAAmB,CAAC,CAAC;EACvB,CAAC;EAED,MAAME,uBAAuB,GAAIR,KAAK,IAAK;IACzCnB,qBAAqB,CAACmB,KAAK,CAACC,aAAa,CAAC;EAC5C,CAAC;EAED,MAAMQ,uBAAuB,GAAGA,CAAA,KAAM;IACpC5B,qBAAqB,CAAC,IAAI,CAAC;EAC7B,CAAC;EAED,MAAM6B,sBAAsB,GAAIC,cAAc,IAAK;IACjDtB,sBAAsB,CAACuB,IAAI,IACzBA,IAAI,CAACC,GAAG,CAACC,KAAK,IACZA,KAAK,CAACxB,EAAE,KAAKqB,cAAc,GAAG;MAAE,GAAGG,KAAK;MAAEjB,IAAI,EAAE;IAAK,CAAC,GAAGiB,KAC3D,CACF,CAAC;EACH,CAAC;EAED,MAAMC,6BAA6B,GAAIC,YAAY,IAAK;IACtD/B,uBAAuB,CAAC+B,YAAY,CAAC;IACrC7B,yBAAyB,CAAC,IAAI,CAAC;IAC/BN,qBAAqB,CAAC,IAAI,CAAC;IAC3B6B,sBAAsB,CAACM,YAAY,CAAC1B,EAAE,CAAC;EACzC,CAAC;EAED,MAAM2B,WAAW,GAAG7B,mBAAmB,CAAC8B,MAAM,CAACJ,KAAK,IAAI,CAACA,KAAK,CAACjB,IAAI,CAAC,CAACsB,MAAM;EAE3E,MAAMC,UAAU,GAAGlD,QAAQ,CAACmD,QAAQ,KAAK,GAAG;EAC5C,MAAMC,mBAAmB,GAAGpD,QAAQ,CAACmD,QAAQ,KAAK,iBAAiB;EACnE,MAAME,aAAa,GAAGrD,QAAQ,CAACmD,QAAQ,KAAK,UAAU;EACtD,MAAMG,WAAW,GAAGtD,QAAQ,CAACmD,QAAQ,KAAK,QAAQ;EAClD,MAAMI,WAAW,GAAGvD,QAAQ,CAACmD,QAAQ,KAAK,QAAQ;EAElD,MAAMK,eAAe,GAAG,CACtB;IAAEC,KAAK,EAAE,MAAM;IAAEC,IAAI,EAAE,GAAG;IAAEC,IAAI,eAAEjE,OAAA,CAACf,QAAQ;MAAAiF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,MAAM,EAAEd;EAAW,CAAC,EACpE;IAAEO,KAAK,EAAE,gBAAgB;IAAEC,IAAI,EAAE,iBAAiB;IAAEC,IAAI,eAAEjE,OAAA,CAACzB,YAAY;MAAA2F,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,MAAM,EAAEZ;EAAoB,CAAC,EACzG;IAAEK,KAAK,EAAE,SAAS;IAAEC,IAAI,EAAE,UAAU;IAAEC,IAAI,eAAEjE,OAAA,CAACjB,WAAW;MAAAmF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,MAAM,EAAEX;EAAc,CAAC,EACpF;IAAEI,KAAK,EAAE,OAAO;IAAEC,IAAI,EAAE,QAAQ;IAAEC,IAAI,eAAEjE,OAAA,CAACP,QAAQ;MAAAyE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,MAAM,EAAET;EAAY,CAAC,EAC3E;IAAEE,KAAK,EAAE,OAAO;IAAEC,IAAI,EAAE,QAAQ;IAAEC,IAAI,eAAEjE,OAAA,CAACb,QAAQ;MAAA+E,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,MAAM,EAAEV;EAAY,CAAC,CAC5E;EAED,oBACE5D,OAAA,CAACzC,MAAM;IACLgH,QAAQ,EAAC,QAAQ;IACjBC,SAAS,EAAE,CAAE;IACbC,EAAE,EAAE;MACFC,UAAU,EAAGC,KAAK,IAAKA,KAAK,CAACC,OAAO,CAACC,IAAI,KAAK,MAAM,GAChD,6GAA6G,GAC7G,gEAAgE;MACpEC,cAAc,EAAE,2BAA2B;MAC3CC,YAAY,EAAGJ,KAAK,IAAKA,KAAK,CAACC,OAAO,CAACC,IAAI,KAAK,MAAM,GAClD,oCAAoC,GACpC,oCAAoC;MACxCG,SAAS,EAAGL,KAAK,IAAKA,KAAK,CAACC,OAAO,CAACC,IAAI,KAAK,MAAM,GAC/C,qCAAqC,GACrC,qCAAqC;MACzCN,QAAQ,EAAE,UAAU;MACpBU,KAAK,EAAE,OAAO;MACd,UAAU,EAAE;QACVC,OAAO,EAAE,IAAI;QACbX,QAAQ,EAAE,UAAU;QACpBY,MAAM,EAAE,CAAC;QACTC,IAAI,EAAE,CAAC;QACPC,KAAK,EAAE,CAAC;QACRC,MAAM,EAAE,KAAK;QACbZ,UAAU,EAAGC,KAAK,IAAKA,KAAK,CAACC,OAAO,CAACC,IAAI,KAAK,MAAM,GAChD,uEAAuE,GACvE,wFAAwF;QAC5FU,OAAO,EAAE;MACX;IACF,CAAE;IAAAC,QAAA,gBAEFxF,OAAA,CAAC/B,SAAS;MAACwH,QAAQ,EAAC,IAAI;MAAAD,QAAA,eACtBxF,OAAA,CAACxC,OAAO;QAACiH,EAAE,EAAE;UAAEiB,cAAc,EAAE,eAAe;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAH,QAAA,gBAEtDxF,OAAA,CAACnC,GAAG;UAAC+H,OAAO,EAAC,MAAM;UAACC,UAAU,EAAC,QAAQ;UAACC,GAAG,EAAE,CAAE;UAAAN,QAAA,gBAC7CxF,OAAA,CAACtC,UAAU;YACTqI,IAAI,EAAC,OAAO;YACZd,KAAK,EAAC,SAAS;YACf,cAAW,eAAe;YAC1Be,OAAO,EAAEA,CAAA,KAAM3F,QAAQ,CAAC,GAAG,CAAE;YAC7BoE,EAAE,EAAE;cACFC,UAAU,EAAE,uBAAuB;cACnCI,cAAc,EAAE,YAAY;cAC5B,SAAS,EAAE;gBACTJ,UAAU,EAAE,uBAAuB;gBACnCuB,SAAS,EAAE;cACb,CAAC;cACDC,UAAU,EAAE;YACd,CAAE;YAAAV,QAAA,eAEFxF,OAAA,CAACzB,YAAY;cAAC4H,QAAQ,EAAC;YAAO;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvB,CAAC,eACbrE,OAAA,CAACnC,GAAG;YAAA2H,QAAA,gBACFxF,OAAA,CAACvC,UAAU;cACT2I,OAAO,EAAC,IAAI;cACZC,SAAS,EAAC,IAAI;cACd5B,EAAE,EAAE;gBACF6B,UAAU,EAAE,GAAG;gBACfC,MAAM,EAAE,SAAS;gBACjBX,OAAO,EAAE;kBAAEY,EAAE,EAAE,MAAM;kBAAEC,EAAE,EAAE;gBAAQ,CAAC;gBACpCxB,KAAK,EAAE,OAAO;gBACdyB,UAAU,EAAE,2BAA2B;gBACvC,SAAS,EAAE;kBACTA,UAAU,EAAE;gBACd,CAAC;gBACDR,UAAU,EAAE;cACd,CAAE;cACFF,OAAO,EAAEA,CAAA,KAAM3F,QAAQ,CAAC,GAAG,CAAE;cAAAmF,QAAA,EAC9B;YAED;cAAAtB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbrE,OAAA,CAACvC,UAAU;cACT2I,OAAO,EAAC,IAAI;cACZC,SAAS,EAAC,IAAI;cACd5B,EAAE,EAAE;gBACF6B,UAAU,EAAE,GAAG;gBACfC,MAAM,EAAE,SAAS;gBACjBX,OAAO,EAAE;kBAAEY,EAAE,EAAE,OAAO;kBAAEC,EAAE,EAAE;gBAAO,CAAC;gBACpCxB,KAAK,EAAE;cACT,CAAE;cACFe,OAAO,EAAEA,CAAA,KAAM3F,QAAQ,CAAC,GAAG,CAAE;cAAAmF,QAAA,EAC9B;YAED;cAAAtB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbrE,OAAA,CAACvC,UAAU;cACT2I,OAAO,EAAC,SAAS;cACjB3B,EAAE,EAAE;gBACFmB,OAAO,EAAE;kBAAEY,EAAE,EAAE,MAAM;kBAAEC,EAAE,EAAE;gBAAQ,CAAC;gBACpC/B,UAAU,EAAE,yFAAyF;gBACrGiC,cAAc,EAAE,WAAW;gBAC3BC,cAAc,EAAE,MAAM;gBACtBC,oBAAoB,EAAE,MAAM;gBAC5BC,mBAAmB,EAAE,aAAa;gBAClCC,SAAS,EAAE,6BAA6B;gBACxCZ,QAAQ,EAAE,SAAS;gBACnBG,UAAU,EAAE,GAAG;gBACfU,aAAa,EAAE,OAAO;gBACtBC,aAAa,EAAE,WAAW;gBAC1B,uBAAuB,EAAE;kBACvB,IAAI,EAAE;oBAAEC,kBAAkB,EAAE;kBAAS,CAAC;kBACtC,KAAK,EAAE;oBAAEA,kBAAkB,EAAE;kBAAW,CAAC;kBACzC,MAAM,EAAE;oBAAEA,kBAAkB,EAAE;kBAAS;gBACzC;cACF,CAAE;cAAA1B,QAAA,EACH;YAED;cAAAtB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNrE,OAAA,CAACnC,GAAG;UAAC+H,OAAO,EAAC,MAAM;UAACC,UAAU,EAAC,QAAQ;UAACC,GAAG,EAAE,CAAE;UAAAN,QAAA,gBAE7CxF,OAAA,CAACnC,GAAG;YAAC+H,OAAO,EAAE;cAAEY,EAAE,EAAE,MAAM;cAAEC,EAAE,EAAE;YAAO,CAAE;YAACX,GAAG,EAAE,CAAE;YAAAN,QAAA,EAC9C1B,eAAe,CAACb,GAAG,CAAEkE,IAAI,iBACxBnH,OAAA,CAAClC,MAAM;cAELmH,KAAK,EAAC,SAAS;cACfmC,SAAS,EAAED,IAAI,CAAClD,IAAK;cACrB+B,OAAO,EAAEA,CAAA,KAAM3F,QAAQ,CAAC8G,IAAI,CAACnD,IAAI,CAAE;cACnCoC,OAAO,EAAEe,IAAI,CAAC7C,MAAM,GAAG,WAAW,GAAG,MAAO;cAC5CG,EAAE,EAAE;gBACF4C,YAAY,EAAE,CAAC;gBACfC,EAAE,EAAE,CAAC;gBACL3B,EAAE,EAAE,CAAC;gBACLW,UAAU,EAAE,GAAG;gBACfW,aAAa,EAAE,MAAM;gBACrBvC,UAAU,EAAEyC,IAAI,CAAC7C,MAAM,GACnB,uBAAuB,GACvB,aAAa;gBACjBQ,cAAc,EAAEqC,IAAI,CAAC7C,MAAM,GAAG,YAAY,GAAG,MAAM;gBACnDiD,MAAM,EAAEJ,IAAI,CAAC7C,MAAM,GAAG,iCAAiC,GAAG,MAAM;gBAChE,SAAS,EAAE;kBACTI,UAAU,EAAE,wBAAwB;kBACpCuB,SAAS,EAAE;gBACb,CAAC;gBACDC,UAAU,EAAE;cACd,CAAE;cAAAV,QAAA,EAED2B,IAAI,CAACpD;YAAK,GAvBNoD,IAAI,CAACnD,IAAI;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAwBR,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGNrE,OAAA,CAACnC,GAAG;YAAC+H,OAAO,EAAE;cAAEY,EAAE,EAAE,MAAM;cAAEC,EAAE,EAAE;YAAO,CAAE;YAACX,GAAG,EAAE,GAAI;YAAAN,QAAA,EAChD1B,eAAe,CAACb,GAAG,CAAEkE,IAAI,iBACxBnH,OAAA,CAAC9B,OAAO;cAAiByD,KAAK,EAAEwF,IAAI,CAACpD,KAAM;cAAAyB,QAAA,eACzCxF,OAAA,CAACtC,UAAU;gBACTuH,KAAK,EAAC,SAAS;gBACfe,OAAO,EAAEA,CAAA,KAAM3F,QAAQ,CAAC8G,IAAI,CAACnD,IAAI,CAAE;gBACnCS,EAAE,EAAE;kBACFC,UAAU,EAAEyC,IAAI,CAAC7C,MAAM,GACnB,uBAAuB,GACvB,aAAa;kBACjBQ,cAAc,EAAEqC,IAAI,CAAC7C,MAAM,GAAG,YAAY,GAAG,MAAM;kBACnDiD,MAAM,EAAEJ,IAAI,CAAC7C,MAAM,GAAG,iCAAiC,GAAG,MAAM;kBAChE,SAAS,EAAE;oBACTI,UAAU,EAAE;kBACd;gBACF,CAAE;gBAAAc,QAAA,EAED2B,IAAI,CAAClD;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA;YAAC,GAhBD8C,IAAI,CAACnD,IAAI;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAiBd,CACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGNrE,OAAA,CAAC9B,OAAO;YAACyD,KAAK,EAAC,eAAe;YAAA6D,QAAA,eAC5BxF,OAAA,CAACtC,UAAU;cACTuH,KAAK,EAAC,SAAS;cACfe,OAAO,EAAEpD,uBAAwB;cACjC6B,EAAE,EAAE;gBACFC,UAAU,EAAE,uBAAuB;gBACnC,SAAS,EAAE;kBACTA,UAAU,EAAE;gBACd;cACF,CAAE;cAAAc,QAAA,eAEFxF,OAAA,CAAC5B,KAAK;gBAACoJ,YAAY,EAAEnE,WAAY;gBAAC4B,KAAK,EAAC,OAAO;gBAAAO,QAAA,eAC7CxF,OAAA,CAACX,iBAAiB;kBAAA6E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAGVrE,OAAA,CAAC9B,OAAO;YAACyD,KAAK,EAAC,UAAU;YAAA6D,QAAA,eACvBxF,OAAA,CAACtC,UAAU;cACTuH,KAAK,EAAC,SAAS;cACfe,OAAO,EAAEvD,mBAAoB;cAC7BgC,EAAE,EAAE;gBACFC,UAAU,EAAE,uBAAuB;gBACnC,SAAS,EAAE;kBACTA,UAAU,EAAE;gBACd;cACF,CAAE;cAAAc,QAAA,eAEFxF,OAAA,CAACT,YAAY;gBAAA2E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAEVrE,OAAA,CAACjC,IAAI;YACH0J,QAAQ,EAAE3G,cAAe;YACzB4G,IAAI,EAAEC,OAAO,CAAC7G,cAAc,CAAE;YAC9B8G,OAAO,EAAElF,mBAAoB;YAC7BmF,YAAY,EAAE;cACZC,QAAQ,EAAE,QAAQ;cAClBC,UAAU,EAAE;YACd,CAAE;YACFC,eAAe,EAAE;cACfF,QAAQ,EAAE,KAAK;cACfC,UAAU,EAAE;YACd,CAAE;YACFE,UAAU,EAAE;cACVxD,EAAE,EAAE;gBACFyD,EAAE,EAAE,CAAC;gBACLb,YAAY,EAAE,CAAC;gBACfc,QAAQ,EAAE;cACZ;YACF,CAAE;YAAA3C,QAAA,gBAGFxF,OAAA,CAAChC,QAAQ;cAACgI,OAAO,EAAErD,wBAAyB;cAAC8B,EAAE,EAAE;gBAAEqB,GAAG,EAAE;cAAE,CAAE;cAAAN,QAAA,gBAC1DxF,OAAA,CAACT,YAAY;gBAAA2E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,sBAElB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eAGXrE,OAAA,CAAChC,QAAQ;cAACgI,OAAO,EAAE7D,mBAAoB;cAACsC,EAAE,EAAE;gBAAEqB,GAAG,EAAE;cAAE,CAAE;cAAAN,QAAA,gBACrDxF,OAAA,CAACnB,YAAY;gBAAAqF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,YAElB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eAGXrE,OAAA,CAAChC,QAAQ;cAACgI,OAAO,EAAEA,CAAA,KAAM7E,qBAAqB,CAAC,IAAI,CAAE;cAACsD,EAAE,EAAE;gBAAEqB,GAAG,EAAE;cAAE,CAAE;cAAAN,QAAA,gBACnExF,OAAA,CAACT,YAAY;gBAAA2E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,YAElB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP,CAAC,eAGPrE,OAAA,CAACjC,IAAI;YACH0J,QAAQ,EAAE9G,cAAe;YACzB+G,IAAI,EAAEC,OAAO,CAAChH,cAAc,CAAE;YAC9BiH,OAAO,EAAEtF,mBAAoB;YAC7BuF,YAAY,EAAE;cACZC,QAAQ,EAAE,QAAQ;cAClBC,UAAU,EAAE;YACd,CAAE;YACFC,eAAe,EAAE;cACfF,QAAQ,EAAE,KAAK;cACfC,UAAU,EAAE;YACd,CAAE;YACFE,UAAU,EAAE;cACVxD,EAAE,EAAE;gBACFyD,EAAE,EAAE,CAAC;gBACLb,YAAY,EAAE;cAChB;YACF,CAAE;YAAA7B,QAAA,gBAEFxF,OAAA,CAAChC,QAAQ;cACPgI,OAAO,EAAEA,CAAA,KAAMzD,oBAAoB,CAAC,IAAI,CAAE;cAC1C6F,QAAQ,EAAE5H,QAAQ,KAAK,IAAK;cAAAgF,QAAA,EAC7B;YAED;cAAAtB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACXrE,OAAA,CAAChC,QAAQ;cACPgI,OAAO,EAAEA,CAAA,KAAMzD,oBAAoB,CAAC,IAAI,CAAE;cAC1C6F,QAAQ,EAAE5H,QAAQ,KAAK,IAAK;cAAAgF,QAAA,EAC7B;YAED;cAAAtB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP,CAAC,eAGPrE,OAAA,CAACjC,IAAI;YACH0J,QAAQ,EAAEzG,kBAAmB;YAC7B0G,IAAI,EAAEC,OAAO,CAAC3G,kBAAkB,CAAE;YAClC4G,OAAO,EAAE/E,uBAAwB;YACjCgF,YAAY,EAAE;cACZC,QAAQ,EAAE,QAAQ;cAClBC,UAAU,EAAE;YACd,CAAE;YACFC,eAAe,EAAE;cACfF,QAAQ,EAAE,KAAK;cACfC,UAAU,EAAE;YACd,CAAE;YACFE,UAAU,EAAE;cACVxD,EAAE,EAAE;gBACFyD,EAAE,EAAE,CAAC;gBACLb,YAAY,EAAE,CAAC;gBACfc,QAAQ,EAAE,GAAG;gBACb1C,QAAQ,EAAE,GAAG;gBACb4C,SAAS,EAAE,GAAG;gBACd3D,UAAU,EAAGC,KAAK,IAAKA,KAAK,CAACC,OAAO,CAACC,IAAI,KAAK,MAAM,GAChD,iFAAiF,GACjF,uFAAuF;gBAC3FC,cAAc,EAAE,YAAY;gBAC5ByC,MAAM,EAAE,oCAAoC;gBAC5CvC,SAAS,EAAE;cACb;YACF,CAAE;YAAAQ,QAAA,gBAEFxF,OAAA,CAACnC,GAAG;cAAC4G,EAAE,EAAE;gBAAE6D,CAAC,EAAE,CAAC;gBAAEvD,YAAY,EAAE;cAAqC,CAAE;cAAAS,QAAA,gBACpExF,OAAA,CAACvC,UAAU;gBAAC2I,OAAO,EAAC,IAAI;gBAACE,UAAU,EAAC,MAAM;gBAACrB,KAAK,EAAC,cAAc;gBAAAO,QAAA,EAAC;cAEhE;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbrE,OAAA,CAACvC,UAAU;gBAAC2I,OAAO,EAAC,OAAO;gBAACnB,KAAK,EAAC,gBAAgB;gBAAAO,QAAA,EAC/CnC,WAAW,GAAG,CAAC,GAAG,GAAGA,WAAW,uBAAuB,GAAG;cAAgB;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACNrE,OAAA,CAACnC,GAAG;cAAC4G,EAAE,EAAE;gBAAE4D,SAAS,EAAE,GAAG;gBAAEE,QAAQ,EAAE;cAAO,CAAE;cAAA/C,QAAA,EAC3ChE,mBAAmB,CAAC+B,MAAM,KAAK,CAAC,gBAC/BvD,OAAA,CAACnC,GAAG;gBAAC4G,EAAE,EAAE;kBAAE6D,CAAC,EAAE,CAAC;kBAAEE,SAAS,EAAE;gBAAS,CAAE;gBAAAhD,QAAA,gBACrCxF,OAAA,CAACX,iBAAiB;kBAACoF,EAAE,EAAE;oBAAE0B,QAAQ,EAAE,EAAE;oBAAElB,KAAK,EAAE,gBAAgB;oBAAEwD,EAAE,EAAE;kBAAE;gBAAE;kBAAAvE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC3ErE,OAAA,CAACvC,UAAU;kBAAC2I,OAAO,EAAC,OAAO;kBAACnB,KAAK,EAAC,gBAAgB;kBAAAO,QAAA,EAAC;gBAEnD;kBAAAtB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,GAEN7C,mBAAmB,CAACyB,GAAG,CAAC,CAACG,YAAY,EAAEsF,KAAK,kBAC1C1I,OAAA,CAACnC,GAAG;gBAAA2H,QAAA,gBACFxF,OAAA,CAAChC,QAAQ;kBACPgI,OAAO,EAAEA,CAAA,KAAM7C,6BAA6B,CAACC,YAAY,CAAE;kBAC3DqB,EAAE,EAAE;oBACFkB,EAAE,EAAE,CAAC;oBACL2B,EAAE,EAAE,CAAC;oBACLzB,UAAU,EAAE,YAAY;oBACxBnB,UAAU,EAAE,CAACtB,YAAY,CAACnB,IAAI,GAC1B,2BAA2B,GAC3B,aAAa;oBACjB,SAAS,EAAE;sBACTyC,UAAU,EAAE;oBACd;kBACF,CAAE;kBAAAc,QAAA,gBAEFxF,OAAA,CAACnC,GAAG;oBAAC4G,EAAE,EAAE;sBAAEkE,EAAE,EAAE,CAAC;sBAAET,EAAE,EAAE;oBAAI,CAAE;oBAAA1C,QAAA,eAC1BxF,OAAA,CAACnC,GAAG;sBACF4G,EAAE,EAAE;wBACFmE,KAAK,EAAE,CAAC;wBACRtD,MAAM,EAAE,CAAC;wBACT+B,YAAY,EAAE,KAAK;wBACnB3C,UAAU,EAAEtB,YAAY,CAACpB,IAAI,KAAK,SAAS,GAAG,SAAS,GAC5CoB,YAAY,CAACpB,IAAI,KAAK,SAAS,GAAG,SAAS,GAC3CoB,YAAY,CAACpB,IAAI,KAAK,OAAO,GAAG,SAAS,GAAG,SAAS;wBAChEuD,OAAO,EAAEnC,YAAY,CAACnB,IAAI,GAAG,GAAG,GAAG;sBACrC;oBAAE;sBAAAiC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,eACNrE,OAAA,CAACnC,GAAG;oBAAC4G,EAAE,EAAE;sBAAEoE,IAAI,EAAE;oBAAE,CAAE;oBAAArD,QAAA,gBACnBxF,OAAA,CAACvC,UAAU;sBACT2I,OAAO,EAAC,WAAW;sBACnBE,UAAU,EAAElD,YAAY,CAACnB,IAAI,GAAG,GAAG,GAAG,GAAI;sBAC1CwC,EAAE,EAAE;wBAAEgE,EAAE,EAAE;sBAAI,CAAE;sBAAAjD,QAAA,EAEfpC,YAAY,CAACzB;oBAAK;sBAAAuC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACT,CAAC,eACbrE,OAAA,CAACvC,UAAU;sBACT2I,OAAO,EAAC,OAAO;sBACfnB,KAAK,EAAC,gBAAgB;sBACtBR,EAAE,EAAE;wBAAEgE,EAAE,EAAE,CAAC;wBAAEK,UAAU,EAAE;sBAAI,CAAE;sBAAAtD,QAAA,EAE9BpC,YAAY,CAACxB;oBAAO;sBAAAsC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACX,CAAC,eACbrE,OAAA,CAACvC,UAAU;sBAAC2I,OAAO,EAAC,SAAS;sBAACnB,KAAK,EAAC,gBAAgB;sBAAAO,QAAA,EACjD,IAAI1D,IAAI,CAACsB,YAAY,CAACvB,SAAS,CAAC,CAACkH,cAAc,CAAC;oBAAC;sBAAA7E,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,EACVqE,KAAK,GAAGlH,mBAAmB,CAAC+B,MAAM,GAAG,CAAC,iBACrCvD,OAAA,CAAC3B,OAAO;kBAACoG,EAAE,EAAE;oBAAEuE,EAAE,EAAE;kBAAE;gBAAE;kBAAA9E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAC1B;cAAA,GAlDOjB,YAAY,CAAC1B,EAAE;gBAAAwC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAmDpB,CACN;YACF;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGZrE,OAAA,CAACH,sBAAsB;MACrB6H,IAAI,EAAExG,kBAAmB;MACzB0G,OAAO,EAAEA,CAAA,KAAMzG,qBAAqB,CAAC,KAAK;IAAE;MAAA+C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7C,CAAC,eAGFrE,OAAA,CAACF,wBAAwB;MACvB4H,IAAI,EAAEpG,sBAAuB;MAC7BsG,OAAO,EAAEA,CAAA,KAAMrG,yBAAyB,CAAC,KAAK,CAAE;MAChD6B,YAAY,EAAEhC;IAAqB;MAAA8C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACI,CAAC;AAEb,CAAC;EAAA,QApfkB3E,WAAW,EACXC,WAAW,EAC+BC,OAAO;AAAA,EAkfnE,CAAC;EAAA,QApfiBF,WAAW,EACXC,WAAW,EAC+BC,OAAO;AAAA,EAkflE;AAACqJ,GAAA,GArfGhJ,MAAM;AAufZA,MAAM,CAACiJ,WAAW,GAAG,QAAQ;AAE7B,eAAejJ,MAAM;AAAC,IAAAG,EAAA,EAAA6I,GAAA;AAAAE,YAAA,CAAA/I,EAAA;AAAA+I,YAAA,CAAAF,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}