{"version": 3, "file": "AsyncScheduler.js", "sourceRoot": "", "sources": ["../../../../src/internal/scheduler/AsyncScheduler.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAAA,0CAAyC;AAKzC;IAAoC,kCAAS;IAgB3C,wBAAY,eAA8B,EAAE,GAAiC;QAAjC,oBAAA,EAAA,MAAoB,qBAAS,CAAC,GAAG;QAA7E,YACE,kBAAM,eAAe,EAAE,GAAG,CAAC,SAC5B;QAjBM,aAAO,GAA4B,EAAE,CAAC;QAMtC,aAAO,GAAY,KAAK,CAAC;;IAWhC,CAAC;IAEM,8BAAK,GAAZ,UAAa,MAAwB;QAC3B,IAAA,OAAO,GAAK,IAAI,QAAT,CAAU;QAEzB,IAAI,IAAI,CAAC,OAAO,EAAE;YAChB,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACrB,OAAO;SACR;QAED,IAAI,KAAU,CAAC;QACf,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;QAEpB,GAAG;YACD,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE;gBACxD,MAAM;aACP;SACF,QAAQ,CAAC,MAAM,GAAG,OAAO,CAAC,KAAK,EAAG,CAAC,EAAE;QAEtC,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;QAErB,IAAI,KAAK,EAAE;YACT,OAAO,CAAC,MAAM,GAAG,OAAO,CAAC,KAAK,EAAG,CAAC,EAAE;gBAClC,MAAM,CAAC,WAAW,EAAE,CAAC;aACtB;YACD,MAAM,KAAK,CAAC;SACb;IACH,CAAC;IACH,qBAAC;AAAD,CAAC,AA9CD,CAAoC,qBAAS,GA8C5C;AA9CY,wCAAc"}