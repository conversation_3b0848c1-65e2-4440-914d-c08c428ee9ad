{"version": 3, "names": ["_is", "require", "_validate", "VISITOR_KEYS", "exports", "ALIAS_KEYS", "FLIPPED_ALIAS_KEYS", "NODE_FIELDS", "BUILDER_KEYS", "DEPRECATED_KEYS", "NODE_PARENT_VALIDATIONS", "getType", "val", "Array", "isArray", "validate", "validateType", "typeNames", "assertNodeType", "validateOptional", "optional", "validateOptionalType", "arrayOf", "elementType", "chain", "assertValueType", "assertEach", "arrayOfType", "validateArrayOfType", "callback", "child<PERSON><PERSON><PERSON><PERSON>", "process", "env", "BABEL_TYPES_8_BREAKING", "validate<PERSON><PERSON><PERSON>", "validator", "node", "key", "i", "subKey", "toString", "length", "v", "each", "assertOneOf", "values", "includes", "TypeError", "JSON", "stringify", "oneOf", "allExpandedTypes", "types", "expandedTypes", "Set", "push", "set", "valType", "type", "has", "is", "oneOfNodeTypes", "assertNodeOrValueType", "primitiveType", "oneOfNodeOrValueTypes", "assertShape", "shape", "keys", "Object", "errors", "property", "validateField", "error", "message", "join", "shapeOf", "assertOptionalChainStart", "_current", "current", "callee", "object", "fns", "args", "fn", "chainOf", "Error", "validTypeOpts", "valid<PERSON>ield<PERSON>eys", "store", "defineAliasedType", "aliases", "opts", "defined", "_store$opts$inherits$", "inherits", "slice", "additional", "filter", "a", "unshift", "defineType", "fields", "getOwnPropertyNames", "field", "def", "default", "deprecated", "visitor", "builder", "k", "depre<PERSON><PERSON><PERSON><PERSON>", "concat", "undefined", "for<PERSON>ach", "alias"], "sources": ["../../src/definitions/utils.ts"], "sourcesContent": ["import is from \"../validators/is.ts\";\nimport { validateField, validateChild } from \"../validators/validate.ts\";\nimport type * as t from \"../index.ts\";\n\nexport const VISITOR_KEYS: Record<string, string[]> = {};\nexport const ALIAS_KEYS: Partial<Record<NodeTypesWithoutComment, string[]>> =\n  {};\nexport const FLIPPED_ALIAS_KEYS: Record<string, NodeTypesWithoutComment[]> = {};\nexport const NODE_FIELDS: Record<string, FieldDefinitions> = {};\nexport const BUILDER_KEYS: Record<string, string[]> = {};\nexport const DEPRECATED_KEYS: Record<string, NodeTypesWithoutComment> = {};\nexport const NODE_PARENT_VALIDATIONS: Record<string, Validator> = {};\n\nfunction getType(val: any) {\n  if (Array.isArray(val)) {\n    return \"array\";\n  } else if (val === null) {\n    return \"null\";\n  } else {\n    return typeof val;\n  }\n}\n\ntype NodeTypesWithoutComment = t.Node[\"type\"] | keyof t.Aliases;\n\ntype NodeTypes = NodeTypesWithoutComment | t.Comment[\"type\"];\n\ntype PrimitiveTypes = ReturnType<typeof getType>;\n\ntype FieldDefinitions = {\n  [x: string]: FieldOptions;\n};\n\ntype DefineTypeOpts = {\n  fields?: FieldDefinitions;\n  visitor?: Array<string>;\n  aliases?: Array<string>;\n  builder?: Array<string>;\n  inherits?: NodeTypes;\n  deprecatedAlias?: string;\n  validate?: Validator;\n};\n\nexport type Validator = (\n  | { type: PrimitiveTypes }\n  | { each: Validator }\n  | { chainOf: Validator[] }\n  | { oneOf: any[] }\n  | { oneOfNodeTypes: NodeTypes[] }\n  | { oneOfNodeOrValueTypes: (NodeTypes | PrimitiveTypes)[] }\n  | { shapeOf: { [x: string]: FieldOptions } }\n  | object\n) &\n  ((node: t.Node, key: string | { toString(): string }, val: any) => void);\n\nexport type FieldOptions = {\n  default?: string | number | boolean | [];\n  optional?: boolean;\n  deprecated?: boolean;\n  validate?: Validator;\n};\n\nexport function validate(validate: Validator): FieldOptions {\n  return { validate };\n}\n\nexport function validateType(...typeNames: NodeTypes[]) {\n  return validate(assertNodeType(...typeNames));\n}\n\nexport function validateOptional(validate: Validator): FieldOptions {\n  return { validate, optional: true };\n}\n\nexport function validateOptionalType(...typeNames: NodeTypes[]): FieldOptions {\n  return { validate: assertNodeType(...typeNames), optional: true };\n}\n\nexport function arrayOf(elementType: Validator): Validator {\n  return chain(assertValueType(\"array\"), assertEach(elementType));\n}\n\nexport function arrayOfType(...typeNames: NodeTypes[]) {\n  return arrayOf(assertNodeType(...typeNames));\n}\n\nexport function validateArrayOfType(...typeNames: NodeTypes[]) {\n  return validate(arrayOfType(...typeNames));\n}\n\nexport function assertEach(callback: Validator): Validator {\n  const childValidator =\n    process.env.BABEL_8_BREAKING || process.env.BABEL_TYPES_8_BREAKING\n      ? validateChild\n      : () => {};\n\n  function validator(node: t.Node, key: string, val: any) {\n    if (!Array.isArray(val)) return;\n\n    let i = 0;\n    // We lazily concatenate strings here for performance reasons.\n    // Concatenating the strings is expensive because we are actually concatenating a string and a number,\n    // so V8 cannot just create a \"rope string\" but has to allocate memory for the string resulting from the number\n    // This string is very rarely used, only in error paths, so we can skip the concatenation cost in most cases\n    const subKey = {\n      toString() {\n        return `${key}[${i}]`;\n      },\n    };\n\n    for (; i < val.length; i++) {\n      const v = val[i];\n      callback(node, subKey, v);\n      childValidator(node, subKey, v);\n    }\n  }\n  validator.each = callback;\n  return validator;\n}\n\nexport function assertOneOf(...values: Array<any>): Validator {\n  function validate(node: any, key: string, val: any) {\n    if (!values.includes(val)) {\n      throw new TypeError(\n        `Property ${key} expected value to be one of ${JSON.stringify(\n          values,\n        )} but got ${JSON.stringify(val)}`,\n      );\n    }\n  }\n\n  validate.oneOf = values;\n\n  return validate;\n}\n\nexport const allExpandedTypes: {\n  types: NodeTypes[];\n  set: Set<string>;\n}[] = [];\n\nexport function assertNodeType(...types: NodeTypes[]): Validator {\n  const expandedTypes = new Set<string>();\n\n  allExpandedTypes.push({ types, set: expandedTypes });\n\n  function validate(node: t.Node, key: string, val: any) {\n    const valType = val?.type;\n    if (valType != null) {\n      if (expandedTypes.has(valType)) {\n        validateChild(node, key, val);\n        return;\n      }\n      if (valType === \"Placeholder\") {\n        for (const type of types) {\n          if (is(type, val)) {\n            validateChild(node, key, val);\n            return;\n          }\n        }\n      }\n    }\n\n    throw new TypeError(\n      `Property ${key} of ${\n        node.type\n      } expected node to be of a type ${JSON.stringify(\n        types,\n      )} but instead got ${JSON.stringify(valType)}`,\n    );\n  }\n\n  validate.oneOfNodeTypes = types;\n\n  return validate;\n}\n\nexport function assertNodeOrValueType(\n  ...types: (NodeTypes | PrimitiveTypes)[]\n): Validator {\n  function validate(node: t.Node, key: string, val: any) {\n    const primitiveType = getType(val);\n    for (const type of types) {\n      if (primitiveType === type || is(type, val)) {\n        validateChild(node, key, val);\n        return;\n      }\n    }\n\n    throw new TypeError(\n      `Property ${key} of ${\n        node.type\n      } expected node to be of a type ${JSON.stringify(\n        types,\n      )} but instead got ${JSON.stringify(val?.type)}`,\n    );\n  }\n\n  validate.oneOfNodeOrValueTypes = types;\n\n  return validate;\n}\n\nexport function assertValueType(type: PrimitiveTypes): Validator {\n  function validate(node: t.Node, key: string, val: any) {\n    if (getType(val) === type) {\n      return;\n    }\n\n    throw new TypeError(\n      `Property ${key} expected type of ${type} but got ${getType(val)}`,\n    );\n  }\n\n  validate.type = type;\n\n  return validate;\n}\n\nexport function assertShape(shape: { [x: string]: FieldOptions }): Validator {\n  const keys = Object.keys(shape);\n  function validate(node: t.Node, key: string, val: any) {\n    const errors = [];\n    for (const property of keys) {\n      try {\n        validateField(node, property, val[property], shape[property]);\n      } catch (error) {\n        if (error instanceof TypeError) {\n          errors.push(error.message);\n          continue;\n        }\n        throw error;\n      }\n    }\n    if (errors.length) {\n      throw new TypeError(\n        `Property ${key} of ${\n          node.type\n        } expected to have the following:\\n${errors.join(\"\\n\")}`,\n      );\n    }\n  }\n\n  validate.shapeOf = shape;\n\n  return validate;\n}\n\nexport function assertOptionalChainStart(): Validator {\n  function validate(node: t.Node) {\n    let current = node;\n    while (node) {\n      const { type } = current;\n      if (type === \"OptionalCallExpression\") {\n        if (current.optional) return;\n        current = current.callee;\n        continue;\n      }\n\n      if (type === \"OptionalMemberExpression\") {\n        if (current.optional) return;\n        current = current.object;\n        continue;\n      }\n\n      break;\n    }\n\n    throw new TypeError(\n      `Non-optional ${node.type} must chain from an optional OptionalMemberExpression or OptionalCallExpression. Found chain from ${current?.type}`,\n    );\n  }\n\n  return validate;\n}\n\nexport function chain(...fns: Array<Validator>): Validator {\n  function validate(...args: Parameters<Validator>) {\n    for (const fn of fns) {\n      fn(...args);\n    }\n  }\n  validate.chainOf = fns;\n\n  if (\n    fns.length >= 2 &&\n    \"type\" in fns[0] &&\n    fns[0].type === \"array\" &&\n    !(\"each\" in fns[1])\n  ) {\n    throw new Error(\n      `An assertValueType(\"array\") validator can only be followed by an assertEach(...) validator.`,\n    );\n  }\n\n  return validate;\n}\n\nconst validTypeOpts = new Set([\n  \"aliases\",\n  \"builder\",\n  \"deprecatedAlias\",\n  \"fields\",\n  \"inherits\",\n  \"visitor\",\n  \"validate\",\n]);\nconst validFieldKeys = new Set([\n  \"default\",\n  \"optional\",\n  \"deprecated\",\n  \"validate\",\n]);\n\nconst store = {} as Record<string, DefineTypeOpts>;\n\n// Wraps defineType to ensure these aliases are included.\nexport function defineAliasedType(...aliases: string[]) {\n  return (type: string, opts: DefineTypeOpts = {}) => {\n    let defined = opts.aliases;\n    if (!defined) {\n      if (opts.inherits) defined = store[opts.inherits].aliases?.slice();\n      defined ??= [];\n      opts.aliases = defined;\n    }\n    const additional = aliases.filter(a => !defined.includes(a));\n    defined.unshift(...additional);\n    defineType(type, opts);\n  };\n}\n\nexport default function defineType(type: string, opts: DefineTypeOpts = {}) {\n  const inherits = (opts.inherits && store[opts.inherits]) || {};\n\n  let fields = opts.fields;\n  if (!fields) {\n    fields = {};\n    if (inherits.fields) {\n      const keys = Object.getOwnPropertyNames(inherits.fields);\n      for (const key of keys) {\n        const field = inherits.fields[key];\n        const def = field.default;\n        if (\n          Array.isArray(def) ? def.length > 0 : def && typeof def === \"object\"\n        ) {\n          throw new Error(\n            \"field defaults can only be primitives or empty arrays currently\",\n          );\n        }\n        fields[key] = {\n          default: Array.isArray(def) ? [] : def,\n          optional: field.optional,\n          deprecated: field.deprecated,\n          validate: field.validate,\n        };\n      }\n    }\n  }\n\n  const visitor: Array<string> = opts.visitor || inherits.visitor || [];\n  const aliases: Array<string> = opts.aliases || inherits.aliases || [];\n  const builder: Array<string> =\n    opts.builder || inherits.builder || opts.visitor || [];\n\n  for (const k of Object.keys(opts)) {\n    if (!validTypeOpts.has(k)) {\n      throw new Error(`Unknown type option \"${k}\" on ${type}`);\n    }\n  }\n\n  if (opts.deprecatedAlias) {\n    DEPRECATED_KEYS[opts.deprecatedAlias] = type as NodeTypesWithoutComment;\n  }\n\n  // ensure all field keys are represented in `fields`\n  for (const key of visitor.concat(builder)) {\n    fields[key] = fields[key] || {};\n  }\n\n  for (const key of Object.keys(fields)) {\n    const field = fields[key];\n\n    if (field.default !== undefined && !builder.includes(key)) {\n      field.optional = true;\n    }\n    if (field.default === undefined) {\n      field.default = null;\n    } else if (!field.validate && field.default != null) {\n      field.validate = assertValueType(getType(field.default));\n    }\n\n    for (const k of Object.keys(field)) {\n      if (!validFieldKeys.has(k)) {\n        throw new Error(`Unknown field key \"${k}\" on ${type}.${key}`);\n      }\n    }\n  }\n\n  VISITOR_KEYS[type] = opts.visitor = visitor;\n  BUILDER_KEYS[type] = opts.builder = builder;\n  NODE_FIELDS[type] = opts.fields = fields;\n  ALIAS_KEYS[type as NodeTypesWithoutComment] = opts.aliases = aliases;\n  aliases.forEach(alias => {\n    FLIPPED_ALIAS_KEYS[alias] = FLIPPED_ALIAS_KEYS[alias] || [];\n    FLIPPED_ALIAS_KEYS[alias].push(type as NodeTypesWithoutComment);\n  });\n\n  if (opts.validate) {\n    NODE_PARENT_VALIDATIONS[type] = opts.validate;\n  }\n\n  store[type] = opts;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,GAAA,GAAAC,OAAA;AACA,IAAAC,SAAA,GAAAD,OAAA;AAGO,MAAME,YAAsC,GAAAC,OAAA,CAAAD,YAAA,GAAG,CAAC,CAAC;AACjD,MAAME,UAA8D,GAAAD,OAAA,CAAAC,UAAA,GACzE,CAAC,CAAC;AACG,MAAMC,kBAA6D,GAAAF,OAAA,CAAAE,kBAAA,GAAG,CAAC,CAAC;AACxE,MAAMC,WAA6C,GAAAH,OAAA,CAAAG,WAAA,GAAG,CAAC,CAAC;AACxD,MAAMC,YAAsC,GAAAJ,OAAA,CAAAI,YAAA,GAAG,CAAC,CAAC;AACjD,MAAMC,eAAwD,GAAAL,OAAA,CAAAK,eAAA,GAAG,CAAC,CAAC;AACnE,MAAMC,uBAAkD,GAAAN,OAAA,CAAAM,uBAAA,GAAG,CAAC,CAAC;AAEpE,SAASC,OAAOA,CAACC,GAAQ,EAAE;EACzB,IAAIC,KAAK,CAACC,OAAO,CAACF,GAAG,CAAC,EAAE;IACtB,OAAO,OAAO;EAChB,CAAC,MAAM,IAAIA,GAAG,KAAK,IAAI,EAAE;IACvB,OAAO,MAAM;EACf,CAAC,MAAM;IACL,OAAO,OAAOA,GAAG;EACnB;AACF;AAyCO,SAASG,QAAQA,CAACA,QAAmB,EAAgB;EAC1D,OAAO;IAAEA;EAAS,CAAC;AACrB;AAEO,SAASC,YAAYA,CAAC,GAAGC,SAAsB,EAAE;EACtD,OAAOF,QAAQ,CAACG,cAAc,CAAC,GAAGD,SAAS,CAAC,CAAC;AAC/C;AAEO,SAASE,gBAAgBA,CAACJ,QAAmB,EAAgB;EAClE,OAAO;IAAEA,QAAQ;IAAEK,QAAQ,EAAE;EAAK,CAAC;AACrC;AAEO,SAASC,oBAAoBA,CAAC,GAAGJ,SAAsB,EAAgB;EAC5E,OAAO;IAAEF,QAAQ,EAAEG,cAAc,CAAC,GAAGD,SAAS,CAAC;IAAEG,QAAQ,EAAE;EAAK,CAAC;AACnE;AAEO,SAASE,OAAOA,CAACC,WAAsB,EAAa;EACzD,OAAOC,KAAK,CAACC,eAAe,CAAC,OAAO,CAAC,EAAEC,UAAU,CAACH,WAAW,CAAC,CAAC;AACjE;AAEO,SAASI,WAAWA,CAAC,GAAGV,SAAsB,EAAE;EACrD,OAAOK,OAAO,CAACJ,cAAc,CAAC,GAAGD,SAAS,CAAC,CAAC;AAC9C;AAEO,SAASW,mBAAmBA,CAAC,GAAGX,SAAsB,EAAE;EAC7D,OAAOF,QAAQ,CAACY,WAAW,CAAC,GAAGV,SAAS,CAAC,CAAC;AAC5C;AAEO,SAASS,UAAUA,CAACG,QAAmB,EAAa;EACzD,MAAMC,cAAc,GACcC,OAAO,CAACC,GAAG,CAACC,sBAAsB,GAC9DC,uBAAa,GACb,MAAM,CAAC,CAAC;EAEd,SAASC,SAASA,CAACC,IAAY,EAAEC,GAAW,EAAEzB,GAAQ,EAAE;IACtD,IAAI,CAACC,KAAK,CAACC,OAAO,CAACF,GAAG,CAAC,EAAE;IAEzB,IAAI0B,CAAC,GAAG,CAAC;IAKT,MAAMC,MAAM,GAAG;MACbC,QAAQA,CAAA,EAAG;QACT,OAAO,GAAGH,GAAG,IAAIC,CAAC,GAAG;MACvB;IACF,CAAC;IAED,OAAOA,CAAC,GAAG1B,GAAG,CAAC6B,MAAM,EAAEH,CAAC,EAAE,EAAE;MAC1B,MAAMI,CAAC,GAAG9B,GAAG,CAAC0B,CAAC,CAAC;MAChBT,QAAQ,CAACO,IAAI,EAAEG,MAAM,EAAEG,CAAC,CAAC;MACzBZ,cAAc,CAACM,IAAI,EAAEG,MAAM,EAAEG,CAAC,CAAC;IACjC;EACF;EACAP,SAAS,CAACQ,IAAI,GAAGd,QAAQ;EACzB,OAAOM,SAAS;AAClB;AAEO,SAASS,WAAWA,CAAC,GAAGC,MAAkB,EAAa;EAC5D,SAAS9B,QAAQA,CAACqB,IAAS,EAAEC,GAAW,EAAEzB,GAAQ,EAAE;IAClD,IAAI,CAACiC,MAAM,CAACC,QAAQ,CAAClC,GAAG,CAAC,EAAE;MACzB,MAAM,IAAImC,SAAS,CACjB,YAAYV,GAAG,gCAAgCW,IAAI,CAACC,SAAS,CAC3DJ,MACF,CAAC,YAAYG,IAAI,CAACC,SAAS,CAACrC,GAAG,CAAC,EAClC,CAAC;IACH;EACF;EAEAG,QAAQ,CAACmC,KAAK,GAAGL,MAAM;EAEvB,OAAO9B,QAAQ;AACjB;AAEO,MAAMoC,gBAGV,GAAA/C,OAAA,CAAA+C,gBAAA,GAAG,EAAE;AAED,SAASjC,cAAcA,CAAC,GAAGkC,KAAkB,EAAa;EAC/D,MAAMC,aAAa,GAAG,IAAIC,GAAG,CAAS,CAAC;EAEvCH,gBAAgB,CAACI,IAAI,CAAC;IAAEH,KAAK;IAAEI,GAAG,EAAEH;EAAc,CAAC,CAAC;EAEpD,SAAStC,QAAQA,CAACqB,IAAY,EAAEC,GAAW,EAAEzB,GAAQ,EAAE;IACrD,MAAM6C,OAAO,GAAG7C,GAAG,oBAAHA,GAAG,CAAE8C,IAAI;IACzB,IAAID,OAAO,IAAI,IAAI,EAAE;MACnB,IAAIJ,aAAa,CAACM,GAAG,CAACF,OAAO,CAAC,EAAE;QAC9B,IAAAvB,uBAAa,EAACE,IAAI,EAAEC,GAAG,EAAEzB,GAAG,CAAC;QAC7B;MACF;MACA,IAAI6C,OAAO,KAAK,aAAa,EAAE;QAC7B,KAAK,MAAMC,IAAI,IAAIN,KAAK,EAAE;UACxB,IAAI,IAAAQ,WAAE,EAACF,IAAI,EAAE9C,GAAG,CAAC,EAAE;YACjB,IAAAsB,uBAAa,EAACE,IAAI,EAAEC,GAAG,EAAEzB,GAAG,CAAC;YAC7B;UACF;QACF;MACF;IACF;IAEA,MAAM,IAAImC,SAAS,CACjB,YAAYV,GAAG,OACbD,IAAI,CAACsB,IAAI,kCACuBV,IAAI,CAACC,SAAS,CAC9CG,KACF,CAAC,oBAAoBJ,IAAI,CAACC,SAAS,CAACQ,OAAO,CAAC,EAC9C,CAAC;EACH;EAEA1C,QAAQ,CAAC8C,cAAc,GAAGT,KAAK;EAE/B,OAAOrC,QAAQ;AACjB;AAEO,SAAS+C,qBAAqBA,CACnC,GAAGV,KAAqC,EAC7B;EACX,SAASrC,QAAQA,CAACqB,IAAY,EAAEC,GAAW,EAAEzB,GAAQ,EAAE;IACrD,MAAMmD,aAAa,GAAGpD,OAAO,CAACC,GAAG,CAAC;IAClC,KAAK,MAAM8C,IAAI,IAAIN,KAAK,EAAE;MACxB,IAAIW,aAAa,KAAKL,IAAI,IAAI,IAAAE,WAAE,EAACF,IAAI,EAAE9C,GAAG,CAAC,EAAE;QAC3C,IAAAsB,uBAAa,EAACE,IAAI,EAAEC,GAAG,EAAEzB,GAAG,CAAC;QAC7B;MACF;IACF;IAEA,MAAM,IAAImC,SAAS,CACjB,YAAYV,GAAG,OACbD,IAAI,CAACsB,IAAI,kCACuBV,IAAI,CAACC,SAAS,CAC9CG,KACF,CAAC,oBAAoBJ,IAAI,CAACC,SAAS,CAACrC,GAAG,oBAAHA,GAAG,CAAE8C,IAAI,CAAC,EAChD,CAAC;EACH;EAEA3C,QAAQ,CAACiD,qBAAqB,GAAGZ,KAAK;EAEtC,OAAOrC,QAAQ;AACjB;AAEO,SAASU,eAAeA,CAACiC,IAAoB,EAAa;EAC/D,SAAS3C,QAAQA,CAACqB,IAAY,EAAEC,GAAW,EAAEzB,GAAQ,EAAE;IACrD,IAAID,OAAO,CAACC,GAAG,CAAC,KAAK8C,IAAI,EAAE;MACzB;IACF;IAEA,MAAM,IAAIX,SAAS,CACjB,YAAYV,GAAG,qBAAqBqB,IAAI,YAAY/C,OAAO,CAACC,GAAG,CAAC,EAClE,CAAC;EACH;EAEAG,QAAQ,CAAC2C,IAAI,GAAGA,IAAI;EAEpB,OAAO3C,QAAQ;AACjB;AAEO,SAASkD,WAAWA,CAACC,KAAoC,EAAa;EAC3E,MAAMC,IAAI,GAAGC,MAAM,CAACD,IAAI,CAACD,KAAK,CAAC;EAC/B,SAASnD,QAAQA,CAACqB,IAAY,EAAEC,GAAW,EAAEzB,GAAQ,EAAE;IACrD,MAAMyD,MAAM,GAAG,EAAE;IACjB,KAAK,MAAMC,QAAQ,IAAIH,IAAI,EAAE;MAC3B,IAAI;QACF,IAAAI,uBAAa,EAACnC,IAAI,EAAEkC,QAAQ,EAAE1D,GAAG,CAAC0D,QAAQ,CAAC,EAAEJ,KAAK,CAACI,QAAQ,CAAC,CAAC;MAC/D,CAAC,CAAC,OAAOE,KAAK,EAAE;QACd,IAAIA,KAAK,YAAYzB,SAAS,EAAE;UAC9BsB,MAAM,CAACd,IAAI,CAACiB,KAAK,CAACC,OAAO,CAAC;UAC1B;QACF;QACA,MAAMD,KAAK;MACb;IACF;IACA,IAAIH,MAAM,CAAC5B,MAAM,EAAE;MACjB,MAAM,IAAIM,SAAS,CACjB,YAAYV,GAAG,OACbD,IAAI,CAACsB,IAAI,qCAC0BW,MAAM,CAACK,IAAI,CAAC,IAAI,CAAC,EACxD,CAAC;IACH;EACF;EAEA3D,QAAQ,CAAC4D,OAAO,GAAGT,KAAK;EAExB,OAAOnD,QAAQ;AACjB;AAEO,SAAS6D,wBAAwBA,CAAA,EAAc;EACpD,SAAS7D,QAAQA,CAACqB,IAAY,EAAE;IAAA,IAAAyC,QAAA;IAC9B,IAAIC,OAAO,GAAG1C,IAAI;IAClB,OAAOA,IAAI,EAAE;MACX,MAAM;QAAEsB;MAAK,CAAC,GAAGoB,OAAO;MACxB,IAAIpB,IAAI,KAAK,wBAAwB,EAAE;QACrC,IAAIoB,OAAO,CAAC1D,QAAQ,EAAE;QACtB0D,OAAO,GAAGA,OAAO,CAACC,MAAM;QACxB;MACF;MAEA,IAAIrB,IAAI,KAAK,0BAA0B,EAAE;QACvC,IAAIoB,OAAO,CAAC1D,QAAQ,EAAE;QACtB0D,OAAO,GAAGA,OAAO,CAACE,MAAM;QACxB;MACF;MAEA;IACF;IAEA,MAAM,IAAIjC,SAAS,CACjB,gBAAgBX,IAAI,CAACsB,IAAI,sGAAAmB,QAAA,GAAqGC,OAAO,qBAAPD,QAAA,CAASnB,IAAI,EAC7I,CAAC;EACH;EAEA,OAAO3C,QAAQ;AACjB;AAEO,SAASS,KAAKA,CAAC,GAAGyD,GAAqB,EAAa;EACzD,SAASlE,QAAQA,CAAC,GAAGmE,IAA2B,EAAE;IAChD,KAAK,MAAMC,EAAE,IAAIF,GAAG,EAAE;MACpBE,EAAE,CAAC,GAAGD,IAAI,CAAC;IACb;EACF;EACAnE,QAAQ,CAACqE,OAAO,GAAGH,GAAG;EAEtB,IACEA,GAAG,CAACxC,MAAM,IAAI,CAAC,IACf,MAAM,IAAIwC,GAAG,CAAC,CAAC,CAAC,IAChBA,GAAG,CAAC,CAAC,CAAC,CAACvB,IAAI,KAAK,OAAO,IACvB,EAAE,MAAM,IAAIuB,GAAG,CAAC,CAAC,CAAC,CAAC,EACnB;IACA,MAAM,IAAII,KAAK,CACb,6FACF,CAAC;EACH;EAEA,OAAOtE,QAAQ;AACjB;AAEA,MAAMuE,aAAa,GAAG,IAAIhC,GAAG,CAAC,CAC5B,SAAS,EACT,SAAS,EACT,iBAAiB,EACjB,QAAQ,EACR,UAAU,EACV,SAAS,EACT,UAAU,CACX,CAAC;AACF,MAAMiC,cAAc,GAAG,IAAIjC,GAAG,CAAC,CAC7B,SAAS,EACT,UAAU,EACV,YAAY,EACZ,UAAU,CACX,CAAC;AAEF,MAAMkC,KAAK,GAAG,CAAC,CAAmC;AAG3C,SAASC,iBAAiBA,CAAC,GAAGC,OAAiB,EAAE;EACtD,OAAO,CAAChC,IAAY,EAAEiC,IAAoB,GAAG,CAAC,CAAC,KAAK;IAClD,IAAIC,OAAO,GAAGD,IAAI,CAACD,OAAO;IAC1B,IAAI,CAACE,OAAO,EAAE;MAAA,IAAAC,qBAAA;MACZ,IAAIF,IAAI,CAACG,QAAQ,EAAEF,OAAO,IAAAC,qBAAA,GAAGL,KAAK,CAACG,IAAI,CAACG,QAAQ,CAAC,CAACJ,OAAO,qBAA5BG,qBAAA,CAA8BE,KAAK,CAAC,CAAC;MAClEH,OAAO,WAAPA,OAAO,GAAPA,OAAO,GAAK,EAAE;MACdD,IAAI,CAACD,OAAO,GAAGE,OAAO;IACxB;IACA,MAAMI,UAAU,GAAGN,OAAO,CAACO,MAAM,CAACC,CAAC,IAAI,CAACN,OAAO,CAAC9C,QAAQ,CAACoD,CAAC,CAAC,CAAC;IAC5DN,OAAO,CAACO,OAAO,CAAC,GAAGH,UAAU,CAAC;IAC9BI,UAAU,CAAC1C,IAAI,EAAEiC,IAAI,CAAC;EACxB,CAAC;AACH;AAEe,SAASS,UAAUA,CAAC1C,IAAY,EAAEiC,IAAoB,GAAG,CAAC,CAAC,EAAE;EAC1E,MAAMG,QAAQ,GAAIH,IAAI,CAACG,QAAQ,IAAIN,KAAK,CAACG,IAAI,CAACG,QAAQ,CAAC,IAAK,CAAC,CAAC;EAE9D,IAAIO,MAAM,GAAGV,IAAI,CAACU,MAAM;EACxB,IAAI,CAACA,MAAM,EAAE;IACXA,MAAM,GAAG,CAAC,CAAC;IACX,IAAIP,QAAQ,CAACO,MAAM,EAAE;MACnB,MAAMlC,IAAI,GAAGC,MAAM,CAACkC,mBAAmB,CAACR,QAAQ,CAACO,MAAM,CAAC;MACxD,KAAK,MAAMhE,GAAG,IAAI8B,IAAI,EAAE;QACtB,MAAMoC,KAAK,GAAGT,QAAQ,CAACO,MAAM,CAAChE,GAAG,CAAC;QAClC,MAAMmE,GAAG,GAAGD,KAAK,CAACE,OAAO;QACzB,IACE5F,KAAK,CAACC,OAAO,CAAC0F,GAAG,CAAC,GAAGA,GAAG,CAAC/D,MAAM,GAAG,CAAC,GAAG+D,GAAG,IAAI,OAAOA,GAAG,KAAK,QAAQ,EACpE;UACA,MAAM,IAAInB,KAAK,CACb,iEACF,CAAC;QACH;QACAgB,MAAM,CAAChE,GAAG,CAAC,GAAG;UACZoE,OAAO,EAAE5F,KAAK,CAACC,OAAO,CAAC0F,GAAG,CAAC,GAAG,EAAE,GAAGA,GAAG;UACtCpF,QAAQ,EAAEmF,KAAK,CAACnF,QAAQ;UACxBsF,UAAU,EAAEH,KAAK,CAACG,UAAU;UAC5B3F,QAAQ,EAAEwF,KAAK,CAACxF;QAClB,CAAC;MACH;IACF;EACF;EAEA,MAAM4F,OAAsB,GAAGhB,IAAI,CAACgB,OAAO,IAAIb,QAAQ,CAACa,OAAO,IAAI,EAAE;EACrE,MAAMjB,OAAsB,GAAGC,IAAI,CAACD,OAAO,IAAII,QAAQ,CAACJ,OAAO,IAAI,EAAE;EACrE,MAAMkB,OAAsB,GAC1BjB,IAAI,CAACiB,OAAO,IAAId,QAAQ,CAACc,OAAO,IAAIjB,IAAI,CAACgB,OAAO,IAAI,EAAE;EAExD,KAAK,MAAME,CAAC,IAAIzC,MAAM,CAACD,IAAI,CAACwB,IAAI,CAAC,EAAE;IACjC,IAAI,CAACL,aAAa,CAAC3B,GAAG,CAACkD,CAAC,CAAC,EAAE;MACzB,MAAM,IAAIxB,KAAK,CAAC,wBAAwBwB,CAAC,QAAQnD,IAAI,EAAE,CAAC;IAC1D;EACF;EAEA,IAAIiC,IAAI,CAACmB,eAAe,EAAE;IACxBrG,eAAe,CAACkF,IAAI,CAACmB,eAAe,CAAC,GAAGpD,IAA+B;EACzE;EAGA,KAAK,MAAMrB,GAAG,IAAIsE,OAAO,CAACI,MAAM,CAACH,OAAO,CAAC,EAAE;IACzCP,MAAM,CAAChE,GAAG,CAAC,GAAGgE,MAAM,CAAChE,GAAG,CAAC,IAAI,CAAC,CAAC;EACjC;EAEA,KAAK,MAAMA,GAAG,IAAI+B,MAAM,CAACD,IAAI,CAACkC,MAAM,CAAC,EAAE;IACrC,MAAME,KAAK,GAAGF,MAAM,CAAChE,GAAG,CAAC;IAEzB,IAAIkE,KAAK,CAACE,OAAO,KAAKO,SAAS,IAAI,CAACJ,OAAO,CAAC9D,QAAQ,CAACT,GAAG,CAAC,EAAE;MACzDkE,KAAK,CAACnF,QAAQ,GAAG,IAAI;IACvB;IACA,IAAImF,KAAK,CAACE,OAAO,KAAKO,SAAS,EAAE;MAC/BT,KAAK,CAACE,OAAO,GAAG,IAAI;IACtB,CAAC,MAAM,IAAI,CAACF,KAAK,CAACxF,QAAQ,IAAIwF,KAAK,CAACE,OAAO,IAAI,IAAI,EAAE;MACnDF,KAAK,CAACxF,QAAQ,GAAGU,eAAe,CAACd,OAAO,CAAC4F,KAAK,CAACE,OAAO,CAAC,CAAC;IAC1D;IAEA,KAAK,MAAMI,CAAC,IAAIzC,MAAM,CAACD,IAAI,CAACoC,KAAK,CAAC,EAAE;MAClC,IAAI,CAAChB,cAAc,CAAC5B,GAAG,CAACkD,CAAC,CAAC,EAAE;QAC1B,MAAM,IAAIxB,KAAK,CAAC,sBAAsBwB,CAAC,QAAQnD,IAAI,IAAIrB,GAAG,EAAE,CAAC;MAC/D;IACF;EACF;EAEAlC,YAAY,CAACuD,IAAI,CAAC,GAAGiC,IAAI,CAACgB,OAAO,GAAGA,OAAO;EAC3CnG,YAAY,CAACkD,IAAI,CAAC,GAAGiC,IAAI,CAACiB,OAAO,GAAGA,OAAO;EAC3CrG,WAAW,CAACmD,IAAI,CAAC,GAAGiC,IAAI,CAACU,MAAM,GAAGA,MAAM;EACxChG,UAAU,CAACqD,IAAI,CAA4B,GAAGiC,IAAI,CAACD,OAAO,GAAGA,OAAO;EACpEA,OAAO,CAACuB,OAAO,CAACC,KAAK,IAAI;IACvB5G,kBAAkB,CAAC4G,KAAK,CAAC,GAAG5G,kBAAkB,CAAC4G,KAAK,CAAC,IAAI,EAAE;IAC3D5G,kBAAkB,CAAC4G,KAAK,CAAC,CAAC3D,IAAI,CAACG,IAA+B,CAAC;EACjE,CAAC,CAAC;EAEF,IAAIiC,IAAI,CAAC5E,QAAQ,EAAE;IACjBL,uBAAuB,CAACgD,IAAI,CAAC,GAAGiC,IAAI,CAAC5E,QAAQ;EAC/C;EAEAyE,KAAK,CAAC9B,IAAI,CAAC,GAAGiC,IAAI;AACpB", "ignoreList": []}