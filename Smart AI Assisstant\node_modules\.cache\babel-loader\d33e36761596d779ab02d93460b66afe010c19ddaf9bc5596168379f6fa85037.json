{"ast": null, "code": "var _jsxFileName = \"E:\\\\PythonProjects\\\\React.js\\\\Smart AI Assisstant\\\\src\\\\pages\\\\HomePage.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Container, Grid, Box, Typography, Card, CardContent, Tabs, Tab, Alert, Snackbar, Fade, Button, Chip } from '@mui/material';\nimport { Security as SecurityIcon, Link as LinkIcon, InsertDriveFile as FileIcon, Assessment as AssessmentIcon } from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport { useScan } from '../contexts/ScanContext';\nimport LinkScanner from '../components/LinkScanner';\nimport FileUploader from '../components/FileUploader';\nimport ResultView from '../components/ResultView';\nimport ThreatMeter from '../components/ThreatMeter';\nimport SmartDashboard from '../components/SmartDashboard';\nimport ThreatPrediction from '../components/ThreatPrediction';\nimport AutoScanSystem from '../components/AutoScanSystem';\nimport SmartNotifications from '../components/SmartNotifications';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst HomePage = /*#__PURE__*/_s(/*#__PURE__*/React.memo(_c = _s(() => {\n  _s();\n  const [activeTab, setActiveTab] = useState(0);\n  const [notification, setNotification] = useState(null);\n  const navigate = useNavigate();\n  const {\n    currentScan,\n    isScanning,\n    threatLevel,\n    notifications,\n    removeNotification\n  } = useScan();\n  const handleTabChange = (event, newValue) => {\n    setActiveTab(newValue);\n  };\n  const handleCloseNotification = notificationId => {\n    removeNotification(notificationId);\n  };\n  const handleDownloadReport = scanResult => {\n    // This would integrate with a PDF generation service\n    console.log('Downloading report for:', scanResult);\n    setNotification({\n      type: 'info',\n      message: 'Report download feature will be available soon.'\n    });\n  };\n  const handleShareResult = scanResult => {\n    // This would integrate with sharing functionality\n    console.log('Sharing result:', scanResult);\n    setNotification({\n      type: 'info',\n      message: 'Result sharing feature will be available soon.'\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      width: '100%',\n      minHeight: '100vh',\n      display: 'flex',\n      flexDirection: 'column',\n      justifyContent: 'center',\n      alignItems: 'center',\n      py: 4\n    },\n    children: [/*#__PURE__*/_jsxDEV(Container, {\n      maxWidth: \"xl\",\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        textAlign: \"center\",\n        mb: 8,\n        sx: {\n          position: 'relative',\n          py: 8,\n          px: 4,\n          borderRadius: 4,\n          overflow: 'hidden',\n          '&::before': {\n            content: '\"\"',\n            position: 'absolute',\n            top: 0,\n            left: 0,\n            right: 0,\n            bottom: 0,\n            background: 'linear-gradient(45deg, rgba(102, 126, 234, 0.08) 0%, rgba(118, 75, 162, 0.08) 25%, rgba(240, 147, 251, 0.08) 50%, rgba(79, 172, 254, 0.08) 75%, rgba(102, 126, 234, 0.08) 100%)',\n            backgroundSize: '400% 400%',\n            animation: 'cyberWaves 15s ease infinite',\n            zIndex: -2\n          },\n          '&::after': {\n            content: '\"\"',\n            position: 'absolute',\n            top: 0,\n            left: 0,\n            right: 0,\n            bottom: 0,\n            background: theme => theme.palette.mode === 'dark' ? 'linear-gradient(135deg, rgba(12, 12, 12, 0.4) 0%, rgba(26, 26, 26, 0.4) 50%, rgba(45, 45, 45, 0.4) 100%)' : 'linear-gradient(135deg, rgba(248, 250, 252, 0.6) 0%, rgba(241, 245, 249, 0.6) 50%, rgba(226, 232, 240, 0.6) 100%)',\n            backdropFilter: 'blur(20px) saturate(180%)',\n            zIndex: -1\n          },\n          '@keyframes cyberWaves': {\n            '0%': {\n              backgroundPosition: '0% 50%'\n            },\n            '50%': {\n              backgroundPosition: '100% 50%'\n            },\n            '100%': {\n              backgroundPosition: '0% 50%'\n            }\n          },\n          '@keyframes neuralPulse': {\n            '0%': {\n              backgroundPosition: '0% 50%'\n            },\n            '50%': {\n              backgroundPosition: '100% 50%'\n            },\n            '100%': {\n              backgroundPosition: '0% 50%'\n            }\n          },\n          '@keyframes quantumGlow': {\n            '0%': {\n              transform: 'scale(1)',\n              opacity: 0.3\n            },\n            '50%': {\n              transform: 'scale(1.05)',\n              opacity: 0.1\n            },\n            '100%': {\n              transform: 'scale(1)',\n              opacity: 0.3\n            }\n          }\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n            borderRadius: '50%',\n            width: 140,\n            height: 140,\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            mx: 'auto',\n            mb: 4,\n            boxShadow: '0 20px 60px rgba(102, 126, 234, 0.4)',\n            position: 'relative',\n            '&::before': {\n              content: '\"\"',\n              position: 'absolute',\n              top: -8,\n              left: -8,\n              right: -8,\n              bottom: -8,\n              borderRadius: '50%',\n              background: 'linear-gradient(45deg, #667eea, #764ba2, #f093fb, #f5576c)',\n              backgroundSize: '400% 400%',\n              animation: 'neuralPulse 3s ease infinite',\n              opacity: 0.7,\n              zIndex: -1\n            },\n            '&::after': {\n              content: '\"\"',\n              position: 'absolute',\n              top: -15,\n              left: -15,\n              right: -15,\n              bottom: -15,\n              borderRadius: '50%',\n              border: '3px solid rgba(102, 126, 234, 0.3)',\n              animation: 'quantumGlow 2s infinite'\n            }\n          },\n          children: /*#__PURE__*/_jsxDEV(SecurityIcon, {\n            sx: {\n              fontSize: 70,\n              color: 'white'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 179,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 139,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h1\",\n          component: \"h1\",\n          gutterBottom: true,\n          fontWeight: \"bold\",\n          sx: {\n            background: 'linear-gradient(45deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%)',\n            backgroundSize: '400% 400%',\n            backgroundClip: 'text',\n            WebkitBackgroundClip: 'text',\n            WebkitTextFillColor: 'transparent',\n            animation: 'neuralPulse 8s ease infinite',\n            mb: 3,\n            fontSize: {\n              xs: '2.5rem',\n              md: '4rem'\n            }\n          },\n          children: \"AI Security Guard\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h4\",\n          color: \"text.secondary\",\n          maxWidth: \"900px\",\n          mx: \"auto\",\n          sx: {\n            mb: 6,\n            lineHeight: 1.6,\n            fontWeight: 300,\n            fontSize: {\n              xs: '1.2rem',\n              md: '1.5rem'\n            }\n          },\n          children: \"Next-generation cybersecurity powered by advanced artificial intelligence. Protect your digital assets with real-time threat detection and predictive analytics.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 202,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'center',\n            gap: 3,\n            flexWrap: 'wrap',\n            mb: 6\n          },\n          children: [{\n            label: 'Real-time Analytics',\n            gradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'\n          }, {\n            label: 'AI Threat Prediction',\n            gradient: 'linear-gradient(135deg, #9c27b0 0%, #e91e63 100%)'\n          }, {\n            label: 'Automated Response',\n            gradient: 'linear-gradient(135deg, #4caf50 0%, #8bc34a 100%)'\n          }, {\n            label: 'Smart Notifications',\n            gradient: 'linear-gradient(135deg, #2196f3 0%, #9c27b0 100%)'\n          }].map((feature, index) => /*#__PURE__*/_jsxDEV(Chip, {\n            label: feature.label,\n            variant: \"outlined\",\n            sx: {\n              borderColor: 'transparent',\n              background: feature.gradient,\n              color: 'white',\n              fontWeight: 600,\n              fontSize: '1rem',\n              px: 3,\n              py: 2,\n              height: 'auto',\n              borderRadius: 3,\n              transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',\n              boxShadow: '0 4px 15px rgba(0,0,0,0.2)',\n              '&:hover': {\n                transform: 'translateY(-3px) scale(1.05)',\n                boxShadow: '0 8px 25px rgba(0,0,0,0.3)'\n              }\n            }\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 233,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 218,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          display: \"flex\",\n          justifyContent: \"center\",\n          gap: 3,\n          flexWrap: \"wrap\",\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            size: \"large\",\n            sx: {\n              px: 6,\n              py: 2,\n              borderRadius: 3,\n              fontSize: '1.1rem',\n              fontWeight: 600,\n              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n              boxShadow: '0 8px 25px rgba(102, 126, 234, 0.4)',\n              '&:hover': {\n                background: 'linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)',\n                transform: 'translateY(-2px)',\n                boxShadow: '0 12px 35px rgba(102, 126, 234, 0.5)'\n              }\n            },\n            children: \"Start Security Scan\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 260,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outlined\",\n            size: \"large\",\n            onClick: () => navigate('/smart-features'),\n            sx: {\n              px: 6,\n              py: 2,\n              borderRadius: 3,\n              fontSize: '1.1rem',\n              fontWeight: 600,\n              borderColor: 'primary.main',\n              color: 'primary.main',\n              borderWidth: 2,\n              '&:hover': {\n                borderWidth: 2,\n                background: 'linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%)',\n                transform: 'translateY(-2px)',\n                boxShadow: '0 8px 25px rgba(102, 126, 234, 0.2)'\n              }\n            },\n            children: \"Explore AI Features\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 280,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 259,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 87,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        display: \"flex\",\n        justifyContent: \"center\",\n        width: \"100%\",\n        children: /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 4,\n          maxWidth: \"1400px\",\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            lg: 8,\n            children: [/*#__PURE__*/_jsxDEV(Card, {\n              sx: {\n                mb: 4,\n                background: theme => theme.palette.mode === 'dark' ? 'linear-gradient(135deg, rgba(12, 12, 12, 0.95) 0%, rgba(26, 26, 26, 0.95) 50%, rgba(45, 45, 45, 0.95) 100%)' : 'linear-gradient(135deg, rgba(248, 250, 252, 0.95) 0%, rgba(241, 245, 249, 0.95) 50%, rgba(226, 232, 240, 0.95) 100%)',\n                backdropFilter: 'blur(25px) saturate(180%)',\n                border: theme => theme.palette.mode === 'dark' ? '1px solid rgba(255, 255, 255, 0.1)' : '1px solid rgba(0, 0, 0, 0.1)',\n                borderRadius: 4,\n                overflow: 'hidden',\n                position: 'relative',\n                boxShadow: '0 8px 32px rgba(102, 126, 234, 0.15)',\n                transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',\n                '&:hover': {\n                  transform: 'translateY(-2px)',\n                  boxShadow: '0 16px 48px rgba(102, 126, 234, 0.2)'\n                }\n              },\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                sx: {\n                  p: 4\n                },\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    borderBottom: 1,\n                    borderColor: 'divider',\n                    mb: 4\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Tabs, {\n                    value: activeTab,\n                    onChange: handleTabChange,\n                    variant: \"fullWidth\",\n                    textColor: \"primary\",\n                    indicatorColor: \"primary\",\n                    sx: {\n                      '& .MuiTab-root': {\n                        minHeight: 72,\n                        fontSize: '1rem',\n                        fontWeight: 600,\n                        textTransform: 'none'\n                      }\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Tab, {\n                      icon: /*#__PURE__*/_jsxDEV(LinkIcon, {\n                        sx: {\n                          fontSize: 28\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 350,\n                        columnNumber: 31\n                      }, this),\n                      label: \"URL Security Scanner\",\n                      iconPosition: \"start\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 349,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Tab, {\n                      icon: /*#__PURE__*/_jsxDEV(FileIcon, {\n                        sx: {\n                          fontSize: 28\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 355,\n                        columnNumber: 31\n                      }, this),\n                      label: \"File Security Scanner\",\n                      iconPosition: \"start\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 354,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Tab, {\n                      icon: /*#__PURE__*/_jsxDEV(SecurityIcon, {\n                        sx: {\n                          fontSize: 28\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 360,\n                        columnNumber: 31\n                      }, this),\n                      label: \"Smart Dashboard\",\n                      iconPosition: \"start\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 359,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Tab, {\n                      icon: /*#__PURE__*/_jsxDEV(AssessmentIcon, {\n                        sx: {\n                          fontSize: 28\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 365,\n                        columnNumber: 31\n                      }, this),\n                      label: \"Threat Prediction\",\n                      iconPosition: \"start\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 364,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 334,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 333,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  children: [activeTab === 0 && /*#__PURE__*/_jsxDEV(Fade, {\n                    in: activeTab === 0,\n                    timeout: 300,\n                    children: /*#__PURE__*/_jsxDEV(Box, {\n                      children: /*#__PURE__*/_jsxDEV(LinkScanner, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 377,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 376,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 375,\n                    columnNumber: 23\n                  }, this), activeTab === 1 && /*#__PURE__*/_jsxDEV(Fade, {\n                    in: activeTab === 1,\n                    timeout: 300,\n                    children: /*#__PURE__*/_jsxDEV(Box, {\n                      children: /*#__PURE__*/_jsxDEV(FileUploader, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 385,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 384,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 383,\n                    columnNumber: 23\n                  }, this), activeTab === 2 && /*#__PURE__*/_jsxDEV(Fade, {\n                    in: activeTab === 2,\n                    timeout: 300,\n                    children: /*#__PURE__*/_jsxDEV(Box, {\n                      children: /*#__PURE__*/_jsxDEV(SmartDashboard, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 393,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 392,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 391,\n                    columnNumber: 23\n                  }, this), activeTab === 3 && /*#__PURE__*/_jsxDEV(Fade, {\n                    in: activeTab === 3,\n                    timeout: 300,\n                    children: /*#__PURE__*/_jsxDEV(Box, {\n                      children: /*#__PURE__*/_jsxDEV(ThreatPrediction, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 401,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 400,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 399,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 373,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 331,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 310,\n              columnNumber: 15\n            }, this), (currentScan || isScanning) && /*#__PURE__*/_jsxDEV(Fade, {\n              in: true,\n              timeout: 500,\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                children: /*#__PURE__*/_jsxDEV(ResultView, {\n                  scanResult: currentScan,\n                  onDownloadReport: handleDownloadReport,\n                  onShareResult: handleShareResult\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 413,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 412,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 411,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 309,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            lg: 4,\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              mb: 4,\n              children: /*#__PURE__*/_jsxDEV(ThreatMeter, {\n                threatData: currentScan,\n                showDetails: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 427,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 426,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Card, {\n              sx: {\n                mb: 4,\n                background: theme => theme.palette.mode === 'dark' ? 'linear-gradient(135deg, rgba(102, 126, 234, 0.15) 0%, rgba(118, 75, 162, 0.15) 50%, rgba(240, 147, 251, 0.15) 100%)' : 'linear-gradient(135deg, rgba(102, 126, 234, 0.08) 0%, rgba(118, 75, 162, 0.08) 50%, rgba(240, 147, 251, 0.08) 100%)',\n                backdropFilter: 'blur(20px) saturate(180%)',\n                border: '1px solid rgba(102, 126, 234, 0.2)',\n                borderRadius: 4,\n                position: 'relative',\n                overflow: 'hidden',\n                boxShadow: '0 8px 32px rgba(102, 126, 234, 0.2)',\n                '&::before': {\n                  content: '\"\"',\n                  position: 'absolute',\n                  top: 0,\n                  left: 0,\n                  right: 0,\n                  height: '4px',\n                  background: 'linear-gradient(90deg, #667eea 0%, #764ba2 50%, #f093fb 100%)'\n                }\n              },\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                sx: {\n                  p: 3\n                },\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  display: \"flex\",\n                  alignItems: \"center\",\n                  gap: 1,\n                  mb: 3,\n                  children: [/*#__PURE__*/_jsxDEV(SecurityIcon, {\n                    color: \"primary\",\n                    sx: {\n                      fontSize: 28\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 459,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"h6\",\n                    fontWeight: \"bold\",\n                    children: \"Security Best Practices\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 460,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 458,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  component: \"ul\",\n                  sx: {\n                    pl: 2,\n                    m: 0,\n                    '& li': {\n                      mb: 1.5\n                    }\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    component: \"li\",\n                    variant: \"body2\",\n                    sx: {\n                      lineHeight: 1.6\n                    },\n                    children: \"Always verify URLs before clicking, especially in emails\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 464,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    component: \"li\",\n                    variant: \"body2\",\n                    sx: {\n                      lineHeight: 1.6\n                    },\n                    children: \"Scan files from unknown sources before opening\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 467,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    component: \"li\",\n                    variant: \"body2\",\n                    sx: {\n                      lineHeight: 1.6\n                    },\n                    children: \"Keep your antivirus software updated\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 470,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    component: \"li\",\n                    variant: \"body2\",\n                    sx: {\n                      lineHeight: 1.6\n                    },\n                    children: \"Use strong, unique passwords for all accounts\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 473,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    component: \"li\",\n                    variant: \"body2\",\n                    sx: {\n                      lineHeight: 1.6\n                    },\n                    children: \"Enable two-factor authentication when available\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 476,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    component: \"li\",\n                    variant: \"body2\",\n                    sx: {\n                      lineHeight: 1.6\n                    },\n                    children: \"Regularly backup your important data\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 479,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 463,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 457,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 434,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Card, {\n              sx: {\n                background: theme => theme.palette.mode === 'dark' ? 'linear-gradient(135deg, rgba(76, 175, 80, 0.15) 0%, rgba(139, 195, 74, 0.15) 100%)' : 'linear-gradient(135deg, rgba(76, 175, 80, 0.08) 0%, rgba(139, 195, 74, 0.08) 100%)',\n                backdropFilter: 'blur(20px) saturate(180%)',\n                border: '1px solid rgba(76, 175, 80, 0.2)',\n                borderRadius: 4,\n                position: 'relative',\n                overflow: 'hidden',\n                boxShadow: '0 8px 32px rgba(76, 175, 80, 0.2)',\n                '&::before': {\n                  content: '\"\"',\n                  position: 'absolute',\n                  top: 0,\n                  left: 0,\n                  right: 0,\n                  height: '4px',\n                  background: 'linear-gradient(90deg, #4caf50 0%, #8bc34a 100%)'\n                }\n              },\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                sx: {\n                  p: 3\n                },\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  display: \"flex\",\n                  alignItems: \"center\",\n                  gap: 1,\n                  mb: 3,\n                  children: [/*#__PURE__*/_jsxDEV(AssessmentIcon, {\n                    color: \"success\",\n                    sx: {\n                      fontSize: 28\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 511,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"h6\",\n                    fontWeight: \"bold\",\n                    children: \"Platform Features\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 512,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 510,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  children: [/*#__PURE__*/_jsxDEV(Box, {\n                    display: \"flex\",\n                    alignItems: \"center\",\n                    gap: 2,\n                    mb: 2,\n                    children: [/*#__PURE__*/_jsxDEV(LinkIcon, {\n                      color: \"primary\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 517,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      fontWeight: \"500\",\n                      children: \"Real-time URL threat detection\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 518,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 516,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    display: \"flex\",\n                    alignItems: \"center\",\n                    gap: 2,\n                    mb: 2,\n                    children: [/*#__PURE__*/_jsxDEV(FileIcon, {\n                      color: \"primary\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 524,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      fontWeight: \"500\",\n                      children: \"Advanced file malware scanning\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 525,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 523,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    display: \"flex\",\n                    alignItems: \"center\",\n                    gap: 2,\n                    mb: 2,\n                    children: [/*#__PURE__*/_jsxDEV(SecurityIcon, {\n                      color: \"primary\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 531,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      fontWeight: \"500\",\n                      children: \"AI-powered threat analysis\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 532,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 530,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    display: \"flex\",\n                    alignItems: \"center\",\n                    gap: 2,\n                    children: [/*#__PURE__*/_jsxDEV(AssessmentIcon, {\n                      color: \"primary\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 538,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      fontWeight: \"500\",\n                      children: \"Detailed security reports\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 539,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 537,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 515,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 509,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 487,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 424,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 307,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 306,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 85,\n      columnNumber: 7\n    }, this), notifications.map(notification => /*#__PURE__*/_jsxDEV(Snackbar, {\n      open: true,\n      autoHideDuration: 6000,\n      onClose: () => handleCloseNotification(notification.id),\n      anchorOrigin: {\n        vertical: 'bottom',\n        horizontal: 'right'\n      },\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        onClose: () => handleCloseNotification(notification.id),\n        severity: notification.type,\n        variant: \"filled\",\n        sx: {\n          width: '100%',\n          borderRadius: 2,\n          boxShadow: '0 8px 32px rgba(0,0,0,0.12)'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle2\",\n          fontWeight: \"bold\",\n          children: notification.title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 570,\n          columnNumber: 13\n        }, this), notification.message && /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          children: notification.message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 574,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 560,\n        columnNumber: 11\n      }, this)\n    }, notification.id, false, {\n      fileName: _jsxFileName,\n      lineNumber: 553,\n      columnNumber: 9\n    }, this)), notification && /*#__PURE__*/_jsxDEV(Snackbar, {\n      open: true,\n      autoHideDuration: 4000,\n      onClose: () => setNotification(null),\n      anchorOrigin: {\n        vertical: 'bottom',\n        horizontal: 'center'\n      },\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        onClose: () => setNotification(null),\n        severity: notification.type,\n        variant: \"filled\",\n        sx: {\n          borderRadius: 2,\n          boxShadow: '0 8px 32px rgba(0,0,0,0.12)'\n        },\n        children: notification.message\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 590,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 584,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 74,\n    columnNumber: 5\n  }, this);\n}, \"UfI/CENxLvYOx40AaSKFcfyM5pQ=\", false, function () {\n  return [useNavigate, useScan];\n})), \"UfI/CENxLvYOx40AaSKFcfyM5pQ=\", false, function () {\n  return [useNavigate, useScan];\n});\n_c2 = HomePage;\nHomePage.displayName = 'HomePage';\nexport default HomePage;\nvar _c, _c2;\n$RefreshReg$(_c, \"HomePage$React.memo\");\n$RefreshReg$(_c2, \"HomePage\");", "map": {"version": 3, "names": ["React", "useState", "Container", "Grid", "Box", "Typography", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Tabs", "Tab", "<PERSON><PERSON>", "Snackbar", "Fade", "<PERSON><PERSON>", "Chip", "Security", "SecurityIcon", "Link", "LinkIcon", "InsertDriveFile", "FileIcon", "Assessment", "AssessmentIcon", "useNavigate", "useScan", "LinkScanner", "FileUploader", "ResultView", "ThreatMeter", "SmartDashboard", "ThreatPrediction", "AutoScanSystem", "SmartNotifications", "jsxDEV", "_jsxDEV", "HomePage", "_s", "memo", "_c", "activeTab", "setActiveTab", "notification", "setNotification", "navigate", "currentScan", "isScanning", "threatLevel", "notifications", "removeNotification", "handleTabChange", "event", "newValue", "handleCloseNotification", "notificationId", "handleDownloadReport", "scanResult", "console", "log", "type", "message", "handleShareResult", "sx", "width", "minHeight", "display", "flexDirection", "justifyContent", "alignItems", "py", "children", "max<PERSON><PERSON><PERSON>", "textAlign", "mb", "position", "px", "borderRadius", "overflow", "content", "top", "left", "right", "bottom", "background", "backgroundSize", "animation", "zIndex", "theme", "palette", "mode", "<PERSON><PERSON>ilter", "backgroundPosition", "transform", "opacity", "height", "mx", "boxShadow", "border", "fontSize", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "component", "gutterBottom", "fontWeight", "backgroundClip", "WebkitBackgroundClip", "WebkitTextFillColor", "xs", "md", "lineHeight", "gap", "flexWrap", "label", "gradient", "map", "feature", "index", "borderColor", "transition", "size", "onClick", "borderWidth", "container", "spacing", "item", "lg", "p", "borderBottom", "value", "onChange", "textColor", "indicatorColor", "textTransform", "icon", "iconPosition", "in", "timeout", "onDownloadReport", "onShareResult", "threatData", "showDetails", "pl", "m", "open", "autoHideDuration", "onClose", "id", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "severity", "title", "_c2", "displayName", "$RefreshReg$"], "sources": ["E:/PythonProjects/React.js/Smart AI Assisstant/src/pages/HomePage.jsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  Container,\n  Grid,\n  Box,\n  Typography,\n  Card,\n  CardContent,\n  Tabs,\n  Tab,\n  Alert,\n  <PERSON>nackbar,\n  Fade,\n  Button,\n  Chip,\n} from '@mui/material';\nimport {\n  Security as SecurityIcon,\n  Link as LinkIcon,\n  InsertDriveFile as FileIcon,\n  Assessment as AssessmentIcon,\n} from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport { useScan } from '../contexts/ScanContext';\nimport LinkScanner from '../components/LinkScanner';\nimport FileUploader from '../components/FileUploader';\nimport ResultView from '../components/ResultView';\nimport ThreatMeter from '../components/ThreatMeter';\nimport SmartDashboard from '../components/SmartDashboard';\nimport ThreatPrediction from '../components/ThreatPrediction';\nimport AutoScanSystem from '../components/AutoScanSystem';\nimport SmartNotifications from '../components/SmartNotifications';\n\nconst HomePage = React.memo(() => {\n  const [activeTab, setActiveTab] = useState(0);\n  const [notification, setNotification] = useState(null);\n  const navigate = useNavigate();\n\n  const {\n    currentScan,\n    isScanning,\n    threatLevel,\n    notifications,\n    removeNotification\n  } = useScan();\n\n  const handleTabChange = (event, newValue) => {\n    setActiveTab(newValue);\n  };\n\n  const handleCloseNotification = (notificationId) => {\n    removeNotification(notificationId);\n  };\n\n  const handleDownloadReport = (scanResult) => {\n    // This would integrate with a PDF generation service\n    console.log('Downloading report for:', scanResult);\n    setNotification({\n      type: 'info',\n      message: 'Report download feature will be available soon.',\n    });\n  };\n\n  const handleShareResult = (scanResult) => {\n    // This would integrate with sharing functionality\n    console.log('Sharing result:', scanResult);\n    setNotification({\n      type: 'info',\n      message: 'Result sharing feature will be available soon.',\n    });\n  };\n\n  return (\n    <Box\n      sx={{\n        width: '100%',\n        minHeight: '100vh',\n        display: 'flex',\n        flexDirection: 'column',\n        justifyContent: 'center',\n        alignItems: 'center',\n        py: 4,\n      }}\n    >\n      <Container maxWidth=\"xl\">\n        {/* Professional Hero Section - Quantum Shield */}\n        <Box\n          textAlign=\"center\"\n          mb={8}\n          sx={{\n            position: 'relative',\n            py: 8,\n            px: 4,\n            borderRadius: 4,\n            overflow: 'hidden',\n            '&::before': {\n              content: '\"\"',\n              position: 'absolute',\n              top: 0,\n              left: 0,\n              right: 0,\n              bottom: 0,\n              background: 'linear-gradient(45deg, rgba(102, 126, 234, 0.08) 0%, rgba(118, 75, 162, 0.08) 25%, rgba(240, 147, 251, 0.08) 50%, rgba(79, 172, 254, 0.08) 75%, rgba(102, 126, 234, 0.08) 100%)',\n              backgroundSize: '400% 400%',\n              animation: 'cyberWaves 15s ease infinite',\n              zIndex: -2,\n            },\n            '&::after': {\n              content: '\"\"',\n              position: 'absolute',\n              top: 0,\n              left: 0,\n              right: 0,\n              bottom: 0,\n              background: (theme) => theme.palette.mode === 'dark'\n                ? 'linear-gradient(135deg, rgba(12, 12, 12, 0.4) 0%, rgba(26, 26, 26, 0.4) 50%, rgba(45, 45, 45, 0.4) 100%)'\n                : 'linear-gradient(135deg, rgba(248, 250, 252, 0.6) 0%, rgba(241, 245, 249, 0.6) 50%, rgba(226, 232, 240, 0.6) 100%)',\n              backdropFilter: 'blur(20px) saturate(180%)',\n              zIndex: -1,\n            },\n            '@keyframes cyberWaves': {\n              '0%': { backgroundPosition: '0% 50%' },\n              '50%': { backgroundPosition: '100% 50%' },\n              '100%': { backgroundPosition: '0% 50%' },\n            },\n            '@keyframes neuralPulse': {\n              '0%': { backgroundPosition: '0% 50%' },\n              '50%': { backgroundPosition: '100% 50%' },\n              '100%': { backgroundPosition: '0% 50%' },\n            },\n            '@keyframes quantumGlow': {\n              '0%': { transform: 'scale(1)', opacity: 0.3 },\n              '50%': { transform: 'scale(1.05)', opacity: 0.1 },\n              '100%': { transform: 'scale(1)', opacity: 0.3 },\n            },\n          }}\n        >\n          {/* Quantum Shield Icon */}\n          <Box\n            sx={{\n              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n              borderRadius: '50%',\n              width: 140,\n              height: 140,\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              mx: 'auto',\n              mb: 4,\n              boxShadow: '0 20px 60px rgba(102, 126, 234, 0.4)',\n              position: 'relative',\n              '&::before': {\n                content: '\"\"',\n                position: 'absolute',\n                top: -8,\n                left: -8,\n                right: -8,\n                bottom: -8,\n                borderRadius: '50%',\n                background: 'linear-gradient(45deg, #667eea, #764ba2, #f093fb, #f5576c)',\n                backgroundSize: '400% 400%',\n                animation: 'neuralPulse 3s ease infinite',\n                opacity: 0.7,\n                zIndex: -1,\n              },\n              '&::after': {\n                content: '\"\"',\n                position: 'absolute',\n                top: -15,\n                left: -15,\n                right: -15,\n                bottom: -15,\n                borderRadius: '50%',\n                border: '3px solid rgba(102, 126, 234, 0.3)',\n                animation: 'quantumGlow 2s infinite',\n              },\n            }}\n          >\n            <SecurityIcon sx={{ fontSize: 70, color: 'white' }} />\n          </Box>\n\n          {/* Neural Pulse Title */}\n          <Typography\n            variant=\"h1\"\n            component=\"h1\"\n            gutterBottom\n            fontWeight=\"bold\"\n            sx={{\n              background: 'linear-gradient(45deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%)',\n              backgroundSize: '400% 400%',\n              backgroundClip: 'text',\n              WebkitBackgroundClip: 'text',\n              WebkitTextFillColor: 'transparent',\n              animation: 'neuralPulse 8s ease infinite',\n              mb: 3,\n              fontSize: { xs: '2.5rem', md: '4rem' },\n            }}\n          >\n            AI Security Guard\n          </Typography>\n\n          <Typography\n            variant=\"h4\"\n            color=\"text.secondary\"\n            maxWidth=\"900px\"\n            mx=\"auto\"\n            sx={{\n              mb: 6,\n              lineHeight: 1.6,\n              fontWeight: 300,\n              fontSize: { xs: '1.2rem', md: '1.5rem' },\n            }}\n          >\n            Next-generation cybersecurity powered by advanced artificial intelligence.\n            Protect your digital assets with real-time threat detection and predictive analytics.\n          </Typography>\n          {/* Professional Feature Chips */}\n          <Box\n            sx={{\n              display: 'flex',\n              justifyContent: 'center',\n              gap: 3,\n              flexWrap: 'wrap',\n              mb: 6,\n            }}\n          >\n            {[\n              { label: 'Real-time Analytics', gradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' },\n              { label: 'AI Threat Prediction', gradient: 'linear-gradient(135deg, #9c27b0 0%, #e91e63 100%)' },\n              { label: 'Automated Response', gradient: 'linear-gradient(135deg, #4caf50 0%, #8bc34a 100%)' },\n              { label: 'Smart Notifications', gradient: 'linear-gradient(135deg, #2196f3 0%, #9c27b0 100%)' },\n            ].map((feature, index) => (\n              <Chip\n                key={index}\n                label={feature.label}\n                variant=\"outlined\"\n                sx={{\n                  borderColor: 'transparent',\n                  background: feature.gradient,\n                  color: 'white',\n                  fontWeight: 600,\n                  fontSize: '1rem',\n                  px: 3,\n                  py: 2,\n                  height: 'auto',\n                  borderRadius: 3,\n                  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',\n                  boxShadow: '0 4px 15px rgba(0,0,0,0.2)',\n                  '&:hover': {\n                    transform: 'translateY(-3px) scale(1.05)',\n                    boxShadow: '0 8px 25px rgba(0,0,0,0.3)',\n                  },\n                }}\n              />\n            ))}\n          </Box>\n\n          {/* CTA Buttons */}\n          <Box display=\"flex\" justifyContent=\"center\" gap={3} flexWrap=\"wrap\">\n            <Button\n              variant=\"contained\"\n              size=\"large\"\n              sx={{\n                px: 6,\n                py: 2,\n                borderRadius: 3,\n                fontSize: '1.1rem',\n                fontWeight: 600,\n                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                boxShadow: '0 8px 25px rgba(102, 126, 234, 0.4)',\n                '&:hover': {\n                  background: 'linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)',\n                  transform: 'translateY(-2px)',\n                  boxShadow: '0 12px 35px rgba(102, 126, 234, 0.5)',\n                },\n              }}\n            >\n              Start Security Scan\n            </Button>\n            <Button\n              variant=\"outlined\"\n              size=\"large\"\n              onClick={() => navigate('/smart-features')}\n              sx={{\n                px: 6,\n                py: 2,\n                borderRadius: 3,\n                fontSize: '1.1rem',\n                fontWeight: 600,\n                borderColor: 'primary.main',\n                color: 'primary.main',\n                borderWidth: 2,\n                '&:hover': {\n                  borderWidth: 2,\n                  background: 'linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%)',\n                  transform: 'translateY(-2px)',\n                  boxShadow: '0 8px 25px rgba(102, 126, 234, 0.2)',\n                },\n              }}\n            >\n              Explore AI Features\n            </Button>\n          </Box>\n        </Box>\n\n        <Box display=\"flex\" justifyContent=\"center\" width=\"100%\">\n          <Grid container spacing={4} maxWidth=\"1400px\">\n            {/* Left Column - Scanning Tools */}\n            <Grid item xs={12} lg={8}>\n              <Card\n                sx={{\n                  mb: 4,\n                  background: (theme) => theme.palette.mode === 'dark'\n                    ? 'linear-gradient(135deg, rgba(12, 12, 12, 0.95) 0%, rgba(26, 26, 26, 0.95) 50%, rgba(45, 45, 45, 0.95) 100%)'\n                    : 'linear-gradient(135deg, rgba(248, 250, 252, 0.95) 0%, rgba(241, 245, 249, 0.95) 50%, rgba(226, 232, 240, 0.95) 100%)',\n                  backdropFilter: 'blur(25px) saturate(180%)',\n                  border: (theme) => theme.palette.mode === 'dark'\n                    ? '1px solid rgba(255, 255, 255, 0.1)'\n                    : '1px solid rgba(0, 0, 0, 0.1)',\n                  borderRadius: 4,\n                  overflow: 'hidden',\n                  position: 'relative',\n                  boxShadow: '0 8px 32px rgba(102, 126, 234, 0.15)',\n                  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',\n                  '&:hover': {\n                    transform: 'translateY(-2px)',\n                    boxShadow: '0 16px 48px rgba(102, 126, 234, 0.2)',\n                  },\n                }}\n              >\n                <CardContent sx={{ p: 4 }}>\n                  {/* Tabs for different scan types */}\n                  <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 4 }}>\n                    <Tabs\n                      value={activeTab}\n                      onChange={handleTabChange}\n                      variant=\"fullWidth\"\n                      textColor=\"primary\"\n                      indicatorColor=\"primary\"\n                      sx={{\n                        '& .MuiTab-root': {\n                          minHeight: 72,\n                          fontSize: '1rem',\n                          fontWeight: 600,\n                          textTransform: 'none',\n                        },\n                      }}\n                    >\n                      <Tab\n                        icon={<LinkIcon sx={{ fontSize: 28 }} />}\n                        label=\"URL Security Scanner\"\n                        iconPosition=\"start\"\n                      />\n                      <Tab\n                        icon={<FileIcon sx={{ fontSize: 28 }} />}\n                        label=\"File Security Scanner\"\n                        iconPosition=\"start\"\n                      />\n                      <Tab\n                        icon={<SecurityIcon sx={{ fontSize: 28 }} />}\n                        label=\"Smart Dashboard\"\n                        iconPosition=\"start\"\n                      />\n                      <Tab\n                        icon={<AssessmentIcon sx={{ fontSize: 28 }} />}\n                        label=\"Threat Prediction\"\n                        iconPosition=\"start\"\n                      />\n                    </Tabs>\n                  </Box>\n\n                  {/* Tab Panels */}\n                  <Box>\n                    {activeTab === 0 && (\n                      <Fade in={activeTab === 0} timeout={300}>\n                        <Box>\n                          <LinkScanner />\n                        </Box>\n                      </Fade>\n                    )}\n\n                    {activeTab === 1 && (\n                      <Fade in={activeTab === 1} timeout={300}>\n                        <Box>\n                          <FileUploader />\n                        </Box>\n                      </Fade>\n                    )}\n\n                    {activeTab === 2 && (\n                      <Fade in={activeTab === 2} timeout={300}>\n                        <Box>\n                          <SmartDashboard />\n                        </Box>\n                      </Fade>\n                    )}\n\n                    {activeTab === 3 && (\n                      <Fade in={activeTab === 3} timeout={300}>\n                        <Box>\n                          <ThreatPrediction />\n                        </Box>\n                      </Fade>\n                    )}\n                  </Box>\n                </CardContent>\n              </Card>\n\n              {/* Results Section */}\n              {(currentScan || isScanning) && (\n                <Fade in={true} timeout={500}>\n                  <Box>\n                    <ResultView\n                      scanResult={currentScan}\n                      onDownloadReport={handleDownloadReport}\n                      onShareResult={handleShareResult}\n                    />\n                  </Box>\n                </Fade>\n              )}\n            </Grid>\n\n            {/* Right Column - Threat Meter and Info */}\n            <Grid item xs={12} lg={4}>\n              {/* Threat Level Indicator */}\n              <Box mb={4}>\n                <ThreatMeter\n                  threatData={currentScan}\n                  showDetails={true}\n                />\n              </Box>\n\n              {/* Security Tips Card - Cyber Pulse */}\n              <Card\n                sx={{\n                  mb: 4,\n                  background: (theme) => theme.palette.mode === 'dark'\n                    ? 'linear-gradient(135deg, rgba(102, 126, 234, 0.15) 0%, rgba(118, 75, 162, 0.15) 50%, rgba(240, 147, 251, 0.15) 100%)'\n                    : 'linear-gradient(135deg, rgba(102, 126, 234, 0.08) 0%, rgba(118, 75, 162, 0.08) 50%, rgba(240, 147, 251, 0.08) 100%)',\n                  backdropFilter: 'blur(20px) saturate(180%)',\n                  border: '1px solid rgba(102, 126, 234, 0.2)',\n                  borderRadius: 4,\n                  position: 'relative',\n                  overflow: 'hidden',\n                  boxShadow: '0 8px 32px rgba(102, 126, 234, 0.2)',\n                  '&::before': {\n                    content: '\"\"',\n                    position: 'absolute',\n                    top: 0,\n                    left: 0,\n                    right: 0,\n                    height: '4px',\n                    background: 'linear-gradient(90deg, #667eea 0%, #764ba2 50%, #f093fb 100%)',\n                  },\n                }}\n              >\n                <CardContent sx={{ p: 3 }}>\n                  <Box display=\"flex\" alignItems=\"center\" gap={1} mb={3}>\n                    <SecurityIcon color=\"primary\" sx={{ fontSize: 28 }} />\n                    <Typography variant=\"h6\" fontWeight=\"bold\">Security Best Practices</Typography>\n                  </Box>\n\n                  <Box component=\"ul\" sx={{ pl: 2, m: 0, '& li': { mb: 1.5 } }}>\n                    <Typography component=\"li\" variant=\"body2\" sx={{ lineHeight: 1.6 }}>\n                      Always verify URLs before clicking, especially in emails\n                    </Typography>\n                    <Typography component=\"li\" variant=\"body2\" sx={{ lineHeight: 1.6 }}>\n                      Scan files from unknown sources before opening\n                    </Typography>\n                    <Typography component=\"li\" variant=\"body2\" sx={{ lineHeight: 1.6 }}>\n                      Keep your antivirus software updated\n                    </Typography>\n                    <Typography component=\"li\" variant=\"body2\" sx={{ lineHeight: 1.6 }}>\n                      Use strong, unique passwords for all accounts\n                    </Typography>\n                    <Typography component=\"li\" variant=\"body2\" sx={{ lineHeight: 1.6 }}>\n                      Enable two-factor authentication when available\n                    </Typography>\n                    <Typography component=\"li\" variant=\"body2\" sx={{ lineHeight: 1.6 }}>\n                      Regularly backup your important data\n                    </Typography>\n                  </Box>\n                </CardContent>\n              </Card>\n\n              {/* Features Card - AI Intelligence */}\n              <Card\n                sx={{\n                  background: (theme) => theme.palette.mode === 'dark'\n                    ? 'linear-gradient(135deg, rgba(76, 175, 80, 0.15) 0%, rgba(139, 195, 74, 0.15) 100%)'\n                    : 'linear-gradient(135deg, rgba(76, 175, 80, 0.08) 0%, rgba(139, 195, 74, 0.08) 100%)',\n                  backdropFilter: 'blur(20px) saturate(180%)',\n                  border: '1px solid rgba(76, 175, 80, 0.2)',\n                  borderRadius: 4,\n                  position: 'relative',\n                  overflow: 'hidden',\n                  boxShadow: '0 8px 32px rgba(76, 175, 80, 0.2)',\n                  '&::before': {\n                    content: '\"\"',\n                    position: 'absolute',\n                    top: 0,\n                    left: 0,\n                    right: 0,\n                    height: '4px',\n                    background: 'linear-gradient(90deg, #4caf50 0%, #8bc34a 100%)',\n                  },\n                }}\n              >\n                <CardContent sx={{ p: 3 }}>\n                  <Box display=\"flex\" alignItems=\"center\" gap={1} mb={3}>\n                    <AssessmentIcon color=\"success\" sx={{ fontSize: 28 }} />\n                    <Typography variant=\"h6\" fontWeight=\"bold\">Platform Features</Typography>\n                  </Box>\n\n                  <Box>\n                    <Box display=\"flex\" alignItems=\"center\" gap={2} mb={2}>\n                      <LinkIcon color=\"primary\" />\n                      <Typography variant=\"body2\" fontWeight=\"500\">\n                        Real-time URL threat detection\n                      </Typography>\n                    </Box>\n\n                    <Box display=\"flex\" alignItems=\"center\" gap={2} mb={2}>\n                      <FileIcon color=\"primary\" />\n                      <Typography variant=\"body2\" fontWeight=\"500\">\n                        Advanced file malware scanning\n                      </Typography>\n                    </Box>\n\n                    <Box display=\"flex\" alignItems=\"center\" gap={2} mb={2}>\n                      <SecurityIcon color=\"primary\" />\n                      <Typography variant=\"body2\" fontWeight=\"500\">\n                        AI-powered threat analysis\n                      </Typography>\n                    </Box>\n\n                    <Box display=\"flex\" alignItems=\"center\" gap={2}>\n                      <AssessmentIcon color=\"primary\" />\n                      <Typography variant=\"body2\" fontWeight=\"500\">\n                        Detailed security reports\n                      </Typography>\n                    </Box>\n                  </Box>\n                </CardContent>\n              </Card>\n            </Grid>\n          </Grid>\n        </Box>\n      </Container>\n\n      {/* Notifications */}\n      {notifications.map((notification) => (\n        <Snackbar\n          key={notification.id}\n          open={true}\n          autoHideDuration={6000}\n          onClose={() => handleCloseNotification(notification.id)}\n          anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}\n        >\n          <Alert\n            onClose={() => handleCloseNotification(notification.id)}\n            severity={notification.type}\n            variant=\"filled\"\n            sx={{\n              width: '100%',\n              borderRadius: 2,\n              boxShadow: '0 8px 32px rgba(0,0,0,0.12)',\n            }}\n          >\n            <Typography variant=\"subtitle2\" fontWeight=\"bold\">\n              {notification.title}\n            </Typography>\n            {notification.message && (\n              <Typography variant=\"body2\">\n                {notification.message}\n              </Typography>\n            )}\n          </Alert>\n        </Snackbar>\n      ))}\n\n      {/* Custom notification */}\n      {notification && (\n        <Snackbar\n          open={true}\n          autoHideDuration={4000}\n          onClose={() => setNotification(null)}\n          anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}\n        >\n          <Alert\n            onClose={() => setNotification(null)}\n            severity={notification.type}\n            variant=\"filled\"\n            sx={{\n              borderRadius: 2,\n              boxShadow: '0 8px 32px rgba(0,0,0,0.12)',\n            }}\n          >\n            {notification.message}\n          </Alert>\n        </Snackbar>\n      )}\n    </Box>\n  );\n});\n\nHomePage.displayName = 'HomePage';\n\nexport default HomePage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,SAAS,EACTC,IAAI,EACJC,GAAG,EACHC,UAAU,EACVC,IAAI,EACJC,WAAW,EACXC,IAAI,EACJC,GAAG,EACHC,KAAK,EACLC,QAAQ,EACRC,IAAI,EACJC,MAAM,EACNC,IAAI,QACC,eAAe;AACtB,SACEC,QAAQ,IAAIC,YAAY,EACxBC,IAAI,IAAIC,QAAQ,EAChBC,eAAe,IAAIC,QAAQ,EAC3BC,UAAU,IAAIC,cAAc,QACvB,qBAAqB;AAC5B,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,OAAO,QAAQ,yBAAyB;AACjD,OAAOC,WAAW,MAAM,2BAA2B;AACnD,OAAOC,YAAY,MAAM,4BAA4B;AACrD,OAAOC,UAAU,MAAM,0BAA0B;AACjD,OAAOC,WAAW,MAAM,2BAA2B;AACnD,OAAOC,cAAc,MAAM,8BAA8B;AACzD,OAAOC,gBAAgB,MAAM,gCAAgC;AAC7D,OAAOC,cAAc,MAAM,8BAA8B;AACzD,OAAOC,kBAAkB,MAAM,kCAAkC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElE,MAAMC,QAAQ,gBAAAC,EAAA,cAAGpC,KAAK,CAACqC,IAAI,CAAAC,EAAA,GAAAF,EAAA,CAAC,MAAM;EAAAA,EAAA;EAChC,MAAM,CAACG,SAAS,EAAEC,YAAY,CAAC,GAAGvC,QAAQ,CAAC,CAAC,CAAC;EAC7C,MAAM,CAACwC,YAAY,EAAEC,eAAe,CAAC,GAAGzC,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM0C,QAAQ,GAAGpB,WAAW,CAAC,CAAC;EAE9B,MAAM;IACJqB,WAAW;IACXC,UAAU;IACVC,WAAW;IACXC,aAAa;IACbC;EACF,CAAC,GAAGxB,OAAO,CAAC,CAAC;EAEb,MAAMyB,eAAe,GAAGA,CAACC,KAAK,EAAEC,QAAQ,KAAK;IAC3CX,YAAY,CAACW,QAAQ,CAAC;EACxB,CAAC;EAED,MAAMC,uBAAuB,GAAIC,cAAc,IAAK;IAClDL,kBAAkB,CAACK,cAAc,CAAC;EACpC,CAAC;EAED,MAAMC,oBAAoB,GAAIC,UAAU,IAAK;IAC3C;IACAC,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEF,UAAU,CAAC;IAClDb,eAAe,CAAC;MACdgB,IAAI,EAAE,MAAM;MACZC,OAAO,EAAE;IACX,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,iBAAiB,GAAIL,UAAU,IAAK;IACxC;IACAC,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEF,UAAU,CAAC;IAC1Cb,eAAe,CAAC;MACdgB,IAAI,EAAE,MAAM;MACZC,OAAO,EAAE;IACX,CAAC,CAAC;EACJ,CAAC;EAED,oBACEzB,OAAA,CAAC9B,GAAG;IACFyD,EAAE,EAAE;MACFC,KAAK,EAAE,MAAM;MACbC,SAAS,EAAE,OAAO;MAClBC,OAAO,EAAE,MAAM;MACfC,aAAa,EAAE,QAAQ;MACvBC,cAAc,EAAE,QAAQ;MACxBC,UAAU,EAAE,QAAQ;MACpBC,EAAE,EAAE;IACN,CAAE;IAAAC,QAAA,gBAEFnC,OAAA,CAAChC,SAAS;MAACoE,QAAQ,EAAC,IAAI;MAAAD,QAAA,gBAEtBnC,OAAA,CAAC9B,GAAG;QACFmE,SAAS,EAAC,QAAQ;QAClBC,EAAE,EAAE,CAAE;QACNX,EAAE,EAAE;UACFY,QAAQ,EAAE,UAAU;UACpBL,EAAE,EAAE,CAAC;UACLM,EAAE,EAAE,CAAC;UACLC,YAAY,EAAE,CAAC;UACfC,QAAQ,EAAE,QAAQ;UAClB,WAAW,EAAE;YACXC,OAAO,EAAE,IAAI;YACbJ,QAAQ,EAAE,UAAU;YACpBK,GAAG,EAAE,CAAC;YACNC,IAAI,EAAE,CAAC;YACPC,KAAK,EAAE,CAAC;YACRC,MAAM,EAAE,CAAC;YACTC,UAAU,EAAE,iLAAiL;YAC7LC,cAAc,EAAE,WAAW;YAC3BC,SAAS,EAAE,8BAA8B;YACzCC,MAAM,EAAE,CAAC;UACX,CAAC;UACD,UAAU,EAAE;YACVR,OAAO,EAAE,IAAI;YACbJ,QAAQ,EAAE,UAAU;YACpBK,GAAG,EAAE,CAAC;YACNC,IAAI,EAAE,CAAC;YACPC,KAAK,EAAE,CAAC;YACRC,MAAM,EAAE,CAAC;YACTC,UAAU,EAAGI,KAAK,IAAKA,KAAK,CAACC,OAAO,CAACC,IAAI,KAAK,MAAM,GAChD,0GAA0G,GAC1G,mHAAmH;YACvHC,cAAc,EAAE,2BAA2B;YAC3CJ,MAAM,EAAE,CAAC;UACX,CAAC;UACD,uBAAuB,EAAE;YACvB,IAAI,EAAE;cAAEK,kBAAkB,EAAE;YAAS,CAAC;YACtC,KAAK,EAAE;cAAEA,kBAAkB,EAAE;YAAW,CAAC;YACzC,MAAM,EAAE;cAAEA,kBAAkB,EAAE;YAAS;UACzC,CAAC;UACD,wBAAwB,EAAE;YACxB,IAAI,EAAE;cAAEA,kBAAkB,EAAE;YAAS,CAAC;YACtC,KAAK,EAAE;cAAEA,kBAAkB,EAAE;YAAW,CAAC;YACzC,MAAM,EAAE;cAAEA,kBAAkB,EAAE;YAAS;UACzC,CAAC;UACD,wBAAwB,EAAE;YACxB,IAAI,EAAE;cAAEC,SAAS,EAAE,UAAU;cAAEC,OAAO,EAAE;YAAI,CAAC;YAC7C,KAAK,EAAE;cAAED,SAAS,EAAE,aAAa;cAAEC,OAAO,EAAE;YAAI,CAAC;YACjD,MAAM,EAAE;cAAED,SAAS,EAAE,UAAU;cAAEC,OAAO,EAAE;YAAI;UAChD;QACF,CAAE;QAAAvB,QAAA,gBAGFnC,OAAA,CAAC9B,GAAG;UACFyD,EAAE,EAAE;YACFqB,UAAU,EAAE,mDAAmD;YAC/DP,YAAY,EAAE,KAAK;YACnBb,KAAK,EAAE,GAAG;YACV+B,MAAM,EAAE,GAAG;YACX7B,OAAO,EAAE,MAAM;YACfG,UAAU,EAAE,QAAQ;YACpBD,cAAc,EAAE,QAAQ;YACxB4B,EAAE,EAAE,MAAM;YACVtB,EAAE,EAAE,CAAC;YACLuB,SAAS,EAAE,sCAAsC;YACjDtB,QAAQ,EAAE,UAAU;YACpB,WAAW,EAAE;cACXI,OAAO,EAAE,IAAI;cACbJ,QAAQ,EAAE,UAAU;cACpBK,GAAG,EAAE,CAAC,CAAC;cACPC,IAAI,EAAE,CAAC,CAAC;cACRC,KAAK,EAAE,CAAC,CAAC;cACTC,MAAM,EAAE,CAAC,CAAC;cACVN,YAAY,EAAE,KAAK;cACnBO,UAAU,EAAE,4DAA4D;cACxEC,cAAc,EAAE,WAAW;cAC3BC,SAAS,EAAE,8BAA8B;cACzCQ,OAAO,EAAE,GAAG;cACZP,MAAM,EAAE,CAAC;YACX,CAAC;YACD,UAAU,EAAE;cACVR,OAAO,EAAE,IAAI;cACbJ,QAAQ,EAAE,UAAU;cACpBK,GAAG,EAAE,CAAC,EAAE;cACRC,IAAI,EAAE,CAAC,EAAE;cACTC,KAAK,EAAE,CAAC,EAAE;cACVC,MAAM,EAAE,CAAC,EAAE;cACXN,YAAY,EAAE,KAAK;cACnBqB,MAAM,EAAE,oCAAoC;cAC5CZ,SAAS,EAAE;YACb;UACF,CAAE;UAAAf,QAAA,eAEFnC,OAAA,CAAClB,YAAY;YAAC6C,EAAE,EAAE;cAAEoC,QAAQ,EAAE,EAAE;cAAEC,KAAK,EAAE;YAAQ;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnD,CAAC,eAGNpE,OAAA,CAAC7B,UAAU;UACTkG,OAAO,EAAC,IAAI;UACZC,SAAS,EAAC,IAAI;UACdC,YAAY;UACZC,UAAU,EAAC,MAAM;UACjB7C,EAAE,EAAE;YACFqB,UAAU,EAAE,yFAAyF;YACrGC,cAAc,EAAE,WAAW;YAC3BwB,cAAc,EAAE,MAAM;YACtBC,oBAAoB,EAAE,MAAM;YAC5BC,mBAAmB,EAAE,aAAa;YAClCzB,SAAS,EAAE,8BAA8B;YACzCZ,EAAE,EAAE,CAAC;YACLyB,QAAQ,EAAE;cAAEa,EAAE,EAAE,QAAQ;cAAEC,EAAE,EAAE;YAAO;UACvC,CAAE;UAAA1C,QAAA,EACH;QAED;UAAA8B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAEbpE,OAAA,CAAC7B,UAAU;UACTkG,OAAO,EAAC,IAAI;UACZL,KAAK,EAAC,gBAAgB;UACtB5B,QAAQ,EAAC,OAAO;UAChBwB,EAAE,EAAC,MAAM;UACTjC,EAAE,EAAE;YACFW,EAAE,EAAE,CAAC;YACLwC,UAAU,EAAE,GAAG;YACfN,UAAU,EAAE,GAAG;YACfT,QAAQ,EAAE;cAAEa,EAAE,EAAE,QAAQ;cAAEC,EAAE,EAAE;YAAS;UACzC,CAAE;UAAA1C,QAAA,EACH;QAGD;UAAA8B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAEbpE,OAAA,CAAC9B,GAAG;UACFyD,EAAE,EAAE;YACFG,OAAO,EAAE,MAAM;YACfE,cAAc,EAAE,QAAQ;YACxB+C,GAAG,EAAE,CAAC;YACNC,QAAQ,EAAE,MAAM;YAChB1C,EAAE,EAAE;UACN,CAAE;UAAAH,QAAA,EAED,CACC;YAAE8C,KAAK,EAAE,qBAAqB;YAAEC,QAAQ,EAAE;UAAoD,CAAC,EAC/F;YAAED,KAAK,EAAE,sBAAsB;YAAEC,QAAQ,EAAE;UAAoD,CAAC,EAChG;YAAED,KAAK,EAAE,oBAAoB;YAAEC,QAAQ,EAAE;UAAoD,CAAC,EAC9F;YAAED,KAAK,EAAE,qBAAqB;YAAEC,QAAQ,EAAE;UAAoD,CAAC,CAChG,CAACC,GAAG,CAAC,CAACC,OAAO,EAAEC,KAAK,kBACnBrF,OAAA,CAACpB,IAAI;YAEHqG,KAAK,EAAEG,OAAO,CAACH,KAAM;YACrBZ,OAAO,EAAC,UAAU;YAClB1C,EAAE,EAAE;cACF2D,WAAW,EAAE,aAAa;cAC1BtC,UAAU,EAAEoC,OAAO,CAACF,QAAQ;cAC5BlB,KAAK,EAAE,OAAO;cACdQ,UAAU,EAAE,GAAG;cACfT,QAAQ,EAAE,MAAM;cAChBvB,EAAE,EAAE,CAAC;cACLN,EAAE,EAAE,CAAC;cACLyB,MAAM,EAAE,MAAM;cACdlB,YAAY,EAAE,CAAC;cACf8C,UAAU,EAAE,uCAAuC;cACnD1B,SAAS,EAAE,4BAA4B;cACvC,SAAS,EAAE;gBACTJ,SAAS,EAAE,8BAA8B;gBACzCI,SAAS,EAAE;cACb;YACF;UAAE,GAnBGwB,KAAK;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAoBX,CACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGNpE,OAAA,CAAC9B,GAAG;UAAC4D,OAAO,EAAC,MAAM;UAACE,cAAc,EAAC,QAAQ;UAAC+C,GAAG,EAAE,CAAE;UAACC,QAAQ,EAAC,MAAM;UAAA7C,QAAA,gBACjEnC,OAAA,CAACrB,MAAM;YACL0F,OAAO,EAAC,WAAW;YACnBmB,IAAI,EAAC,OAAO;YACZ7D,EAAE,EAAE;cACFa,EAAE,EAAE,CAAC;cACLN,EAAE,EAAE,CAAC;cACLO,YAAY,EAAE,CAAC;cACfsB,QAAQ,EAAE,QAAQ;cAClBS,UAAU,EAAE,GAAG;cACfxB,UAAU,EAAE,mDAAmD;cAC/Da,SAAS,EAAE,qCAAqC;cAChD,SAAS,EAAE;gBACTb,UAAU,EAAE,mDAAmD;gBAC/DS,SAAS,EAAE,kBAAkB;gBAC7BI,SAAS,EAAE;cACb;YACF,CAAE;YAAA1B,QAAA,EACH;UAED;YAAA8B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTpE,OAAA,CAACrB,MAAM;YACL0F,OAAO,EAAC,UAAU;YAClBmB,IAAI,EAAC,OAAO;YACZC,OAAO,EAAEA,CAAA,KAAMhF,QAAQ,CAAC,iBAAiB,CAAE;YAC3CkB,EAAE,EAAE;cACFa,EAAE,EAAE,CAAC;cACLN,EAAE,EAAE,CAAC;cACLO,YAAY,EAAE,CAAC;cACfsB,QAAQ,EAAE,QAAQ;cAClBS,UAAU,EAAE,GAAG;cACfc,WAAW,EAAE,cAAc;cAC3BtB,KAAK,EAAE,cAAc;cACrB0B,WAAW,EAAE,CAAC;cACd,SAAS,EAAE;gBACTA,WAAW,EAAE,CAAC;gBACd1C,UAAU,EAAE,oFAAoF;gBAChGS,SAAS,EAAE,kBAAkB;gBAC7BI,SAAS,EAAE;cACb;YACF,CAAE;YAAA1B,QAAA,EACH;UAED;YAAA8B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENpE,OAAA,CAAC9B,GAAG;QAAC4D,OAAO,EAAC,MAAM;QAACE,cAAc,EAAC,QAAQ;QAACJ,KAAK,EAAC,MAAM;QAAAO,QAAA,eACtDnC,OAAA,CAAC/B,IAAI;UAAC0H,SAAS;UAACC,OAAO,EAAE,CAAE;UAACxD,QAAQ,EAAC,QAAQ;UAAAD,QAAA,gBAE3CnC,OAAA,CAAC/B,IAAI;YAAC4H,IAAI;YAACjB,EAAE,EAAE,EAAG;YAACkB,EAAE,EAAE,CAAE;YAAA3D,QAAA,gBACvBnC,OAAA,CAAC5B,IAAI;cACHuD,EAAE,EAAE;gBACFW,EAAE,EAAE,CAAC;gBACLU,UAAU,EAAGI,KAAK,IAAKA,KAAK,CAACC,OAAO,CAACC,IAAI,KAAK,MAAM,GAChD,6GAA6G,GAC7G,sHAAsH;gBAC1HC,cAAc,EAAE,2BAA2B;gBAC3CO,MAAM,EAAGV,KAAK,IAAKA,KAAK,CAACC,OAAO,CAACC,IAAI,KAAK,MAAM,GAC5C,oCAAoC,GACpC,8BAA8B;gBAClCb,YAAY,EAAE,CAAC;gBACfC,QAAQ,EAAE,QAAQ;gBAClBH,QAAQ,EAAE,UAAU;gBACpBsB,SAAS,EAAE,sCAAsC;gBACjD0B,UAAU,EAAE,uCAAuC;gBACnD,SAAS,EAAE;kBACT9B,SAAS,EAAE,kBAAkB;kBAC7BI,SAAS,EAAE;gBACb;cACF,CAAE;cAAA1B,QAAA,eAEFnC,OAAA,CAAC3B,WAAW;gBAACsD,EAAE,EAAE;kBAAEoE,CAAC,EAAE;gBAAE,CAAE;gBAAA5D,QAAA,gBAExBnC,OAAA,CAAC9B,GAAG;kBAACyD,EAAE,EAAE;oBAAEqE,YAAY,EAAE,CAAC;oBAAEV,WAAW,EAAE,SAAS;oBAAEhD,EAAE,EAAE;kBAAE,CAAE;kBAAAH,QAAA,eAC1DnC,OAAA,CAAC1B,IAAI;oBACH2H,KAAK,EAAE5F,SAAU;oBACjB6F,QAAQ,EAAEnF,eAAgB;oBAC1BsD,OAAO,EAAC,WAAW;oBACnB8B,SAAS,EAAC,SAAS;oBACnBC,cAAc,EAAC,SAAS;oBACxBzE,EAAE,EAAE;sBACF,gBAAgB,EAAE;wBAChBE,SAAS,EAAE,EAAE;wBACbkC,QAAQ,EAAE,MAAM;wBAChBS,UAAU,EAAE,GAAG;wBACf6B,aAAa,EAAE;sBACjB;oBACF,CAAE;oBAAAlE,QAAA,gBAEFnC,OAAA,CAACzB,GAAG;sBACF+H,IAAI,eAAEtG,OAAA,CAAChB,QAAQ;wBAAC2C,EAAE,EAAE;0BAAEoC,QAAQ,EAAE;wBAAG;sBAAE;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAE;sBACzCa,KAAK,EAAC,sBAAsB;sBAC5BsB,YAAY,EAAC;oBAAO;sBAAAtC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrB,CAAC,eACFpE,OAAA,CAACzB,GAAG;sBACF+H,IAAI,eAAEtG,OAAA,CAACd,QAAQ;wBAACyC,EAAE,EAAE;0BAAEoC,QAAQ,EAAE;wBAAG;sBAAE;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAE;sBACzCa,KAAK,EAAC,uBAAuB;sBAC7BsB,YAAY,EAAC;oBAAO;sBAAAtC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrB,CAAC,eACFpE,OAAA,CAACzB,GAAG;sBACF+H,IAAI,eAAEtG,OAAA,CAAClB,YAAY;wBAAC6C,EAAE,EAAE;0BAAEoC,QAAQ,EAAE;wBAAG;sBAAE;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAE;sBAC7Ca,KAAK,EAAC,iBAAiB;sBACvBsB,YAAY,EAAC;oBAAO;sBAAAtC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrB,CAAC,eACFpE,OAAA,CAACzB,GAAG;sBACF+H,IAAI,eAAEtG,OAAA,CAACZ,cAAc;wBAACuC,EAAE,EAAE;0BAAEoC,QAAQ,EAAE;wBAAG;sBAAE;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAE;sBAC/Ca,KAAK,EAAC,mBAAmB;sBACzBsB,YAAY,EAAC;oBAAO;sBAAAtC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrB,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eAGNpE,OAAA,CAAC9B,GAAG;kBAAAiE,QAAA,GACD9B,SAAS,KAAK,CAAC,iBACdL,OAAA,CAACtB,IAAI;oBAAC8H,EAAE,EAAEnG,SAAS,KAAK,CAAE;oBAACoG,OAAO,EAAE,GAAI;oBAAAtE,QAAA,eACtCnC,OAAA,CAAC9B,GAAG;sBAAAiE,QAAA,eACFnC,OAAA,CAACT,WAAW;wBAAA0E,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACZ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CACP,EAEA/D,SAAS,KAAK,CAAC,iBACdL,OAAA,CAACtB,IAAI;oBAAC8H,EAAE,EAAEnG,SAAS,KAAK,CAAE;oBAACoG,OAAO,EAAE,GAAI;oBAAAtE,QAAA,eACtCnC,OAAA,CAAC9B,GAAG;sBAAAiE,QAAA,eACFnC,OAAA,CAACR,YAAY;wBAAAyE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACb;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CACP,EAEA/D,SAAS,KAAK,CAAC,iBACdL,OAAA,CAACtB,IAAI;oBAAC8H,EAAE,EAAEnG,SAAS,KAAK,CAAE;oBAACoG,OAAO,EAAE,GAAI;oBAAAtE,QAAA,eACtCnC,OAAA,CAAC9B,GAAG;sBAAAiE,QAAA,eACFnC,OAAA,CAACL,cAAc;wBAAAsE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACf;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CACP,EAEA/D,SAAS,KAAK,CAAC,iBACdL,OAAA,CAACtB,IAAI;oBAAC8H,EAAE,EAAEnG,SAAS,KAAK,CAAE;oBAACoG,OAAO,EAAE,GAAI;oBAAAtE,QAAA,eACtCnC,OAAA,CAAC9B,GAAG;sBAAAiE,QAAA,eACFnC,OAAA,CAACJ,gBAAgB;wBAAAqE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjB;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CACP;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACK;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,EAGN,CAAC1D,WAAW,IAAIC,UAAU,kBACzBX,OAAA,CAACtB,IAAI;cAAC8H,EAAE,EAAE,IAAK;cAACC,OAAO,EAAE,GAAI;cAAAtE,QAAA,eAC3BnC,OAAA,CAAC9B,GAAG;gBAAAiE,QAAA,eACFnC,OAAA,CAACP,UAAU;kBACT4B,UAAU,EAAEX,WAAY;kBACxBgG,gBAAgB,EAAEtF,oBAAqB;kBACvCuF,aAAa,EAAEjF;gBAAkB;kBAAAuC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CACP;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC,eAGPpE,OAAA,CAAC/B,IAAI;YAAC4H,IAAI;YAACjB,EAAE,EAAE,EAAG;YAACkB,EAAE,EAAE,CAAE;YAAA3D,QAAA,gBAEvBnC,OAAA,CAAC9B,GAAG;cAACoE,EAAE,EAAE,CAAE;cAAAH,QAAA,eACTnC,OAAA,CAACN,WAAW;gBACVkH,UAAU,EAAElG,WAAY;gBACxBmG,WAAW,EAAE;cAAK;gBAAA5C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAGNpE,OAAA,CAAC5B,IAAI;cACHuD,EAAE,EAAE;gBACFW,EAAE,EAAE,CAAC;gBACLU,UAAU,EAAGI,KAAK,IAAKA,KAAK,CAACC,OAAO,CAACC,IAAI,KAAK,MAAM,GAChD,qHAAqH,GACrH,qHAAqH;gBACzHC,cAAc,EAAE,2BAA2B;gBAC3CO,MAAM,EAAE,oCAAoC;gBAC5CrB,YAAY,EAAE,CAAC;gBACfF,QAAQ,EAAE,UAAU;gBACpBG,QAAQ,EAAE,QAAQ;gBAClBmB,SAAS,EAAE,qCAAqC;gBAChD,WAAW,EAAE;kBACXlB,OAAO,EAAE,IAAI;kBACbJ,QAAQ,EAAE,UAAU;kBACpBK,GAAG,EAAE,CAAC;kBACNC,IAAI,EAAE,CAAC;kBACPC,KAAK,EAAE,CAAC;kBACRa,MAAM,EAAE,KAAK;kBACbX,UAAU,EAAE;gBACd;cACF,CAAE;cAAAb,QAAA,eAEFnC,OAAA,CAAC3B,WAAW;gBAACsD,EAAE,EAAE;kBAAEoE,CAAC,EAAE;gBAAE,CAAE;gBAAA5D,QAAA,gBACxBnC,OAAA,CAAC9B,GAAG;kBAAC4D,OAAO,EAAC,MAAM;kBAACG,UAAU,EAAC,QAAQ;kBAAC8C,GAAG,EAAE,CAAE;kBAACzC,EAAE,EAAE,CAAE;kBAAAH,QAAA,gBACpDnC,OAAA,CAAClB,YAAY;oBAACkF,KAAK,EAAC,SAAS;oBAACrC,EAAE,EAAE;sBAAEoC,QAAQ,EAAE;oBAAG;kBAAE;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACtDpE,OAAA,CAAC7B,UAAU;oBAACkG,OAAO,EAAC,IAAI;oBAACG,UAAU,EAAC,MAAM;oBAAArC,QAAA,EAAC;kBAAuB;oBAAA8B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5E,CAAC,eAENpE,OAAA,CAAC9B,GAAG;kBAACoG,SAAS,EAAC,IAAI;kBAAC3C,EAAE,EAAE;oBAAEmF,EAAE,EAAE,CAAC;oBAAEC,CAAC,EAAE,CAAC;oBAAE,MAAM,EAAE;sBAAEzE,EAAE,EAAE;oBAAI;kBAAE,CAAE;kBAAAH,QAAA,gBAC3DnC,OAAA,CAAC7B,UAAU;oBAACmG,SAAS,EAAC,IAAI;oBAACD,OAAO,EAAC,OAAO;oBAAC1C,EAAE,EAAE;sBAAEmD,UAAU,EAAE;oBAAI,CAAE;oBAAA3C,QAAA,EAAC;kBAEpE;oBAAA8B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACbpE,OAAA,CAAC7B,UAAU;oBAACmG,SAAS,EAAC,IAAI;oBAACD,OAAO,EAAC,OAAO;oBAAC1C,EAAE,EAAE;sBAAEmD,UAAU,EAAE;oBAAI,CAAE;oBAAA3C,QAAA,EAAC;kBAEpE;oBAAA8B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACbpE,OAAA,CAAC7B,UAAU;oBAACmG,SAAS,EAAC,IAAI;oBAACD,OAAO,EAAC,OAAO;oBAAC1C,EAAE,EAAE;sBAAEmD,UAAU,EAAE;oBAAI,CAAE;oBAAA3C,QAAA,EAAC;kBAEpE;oBAAA8B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACbpE,OAAA,CAAC7B,UAAU;oBAACmG,SAAS,EAAC,IAAI;oBAACD,OAAO,EAAC,OAAO;oBAAC1C,EAAE,EAAE;sBAAEmD,UAAU,EAAE;oBAAI,CAAE;oBAAA3C,QAAA,EAAC;kBAEpE;oBAAA8B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACbpE,OAAA,CAAC7B,UAAU;oBAACmG,SAAS,EAAC,IAAI;oBAACD,OAAO,EAAC,OAAO;oBAAC1C,EAAE,EAAE;sBAAEmD,UAAU,EAAE;oBAAI,CAAE;oBAAA3C,QAAA,EAAC;kBAEpE;oBAAA8B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACbpE,OAAA,CAAC7B,UAAU;oBAACmG,SAAS,EAAC,IAAI;oBAACD,OAAO,EAAC,OAAO;oBAAC1C,EAAE,EAAE;sBAAEmD,UAAU,EAAE;oBAAI,CAAE;oBAAA3C,QAAA,EAAC;kBAEpE;oBAAA8B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACK;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAGPpE,OAAA,CAAC5B,IAAI;cACHuD,EAAE,EAAE;gBACFqB,UAAU,EAAGI,KAAK,IAAKA,KAAK,CAACC,OAAO,CAACC,IAAI,KAAK,MAAM,GAChD,oFAAoF,GACpF,oFAAoF;gBACxFC,cAAc,EAAE,2BAA2B;gBAC3CO,MAAM,EAAE,kCAAkC;gBAC1CrB,YAAY,EAAE,CAAC;gBACfF,QAAQ,EAAE,UAAU;gBACpBG,QAAQ,EAAE,QAAQ;gBAClBmB,SAAS,EAAE,mCAAmC;gBAC9C,WAAW,EAAE;kBACXlB,OAAO,EAAE,IAAI;kBACbJ,QAAQ,EAAE,UAAU;kBACpBK,GAAG,EAAE,CAAC;kBACNC,IAAI,EAAE,CAAC;kBACPC,KAAK,EAAE,CAAC;kBACRa,MAAM,EAAE,KAAK;kBACbX,UAAU,EAAE;gBACd;cACF,CAAE;cAAAb,QAAA,eAEFnC,OAAA,CAAC3B,WAAW;gBAACsD,EAAE,EAAE;kBAAEoE,CAAC,EAAE;gBAAE,CAAE;gBAAA5D,QAAA,gBACxBnC,OAAA,CAAC9B,GAAG;kBAAC4D,OAAO,EAAC,MAAM;kBAACG,UAAU,EAAC,QAAQ;kBAAC8C,GAAG,EAAE,CAAE;kBAACzC,EAAE,EAAE,CAAE;kBAAAH,QAAA,gBACpDnC,OAAA,CAACZ,cAAc;oBAAC4E,KAAK,EAAC,SAAS;oBAACrC,EAAE,EAAE;sBAAEoC,QAAQ,EAAE;oBAAG;kBAAE;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACxDpE,OAAA,CAAC7B,UAAU;oBAACkG,OAAO,EAAC,IAAI;oBAACG,UAAU,EAAC,MAAM;oBAAArC,QAAA,EAAC;kBAAiB;oBAAA8B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtE,CAAC,eAENpE,OAAA,CAAC9B,GAAG;kBAAAiE,QAAA,gBACFnC,OAAA,CAAC9B,GAAG;oBAAC4D,OAAO,EAAC,MAAM;oBAACG,UAAU,EAAC,QAAQ;oBAAC8C,GAAG,EAAE,CAAE;oBAACzC,EAAE,EAAE,CAAE;oBAAAH,QAAA,gBACpDnC,OAAA,CAAChB,QAAQ;sBAACgF,KAAK,EAAC;oBAAS;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC5BpE,OAAA,CAAC7B,UAAU;sBAACkG,OAAO,EAAC,OAAO;sBAACG,UAAU,EAAC,KAAK;sBAAArC,QAAA,EAAC;oBAE7C;sBAAA8B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC,eAENpE,OAAA,CAAC9B,GAAG;oBAAC4D,OAAO,EAAC,MAAM;oBAACG,UAAU,EAAC,QAAQ;oBAAC8C,GAAG,EAAE,CAAE;oBAACzC,EAAE,EAAE,CAAE;oBAAAH,QAAA,gBACpDnC,OAAA,CAACd,QAAQ;sBAAC8E,KAAK,EAAC;oBAAS;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC5BpE,OAAA,CAAC7B,UAAU;sBAACkG,OAAO,EAAC,OAAO;sBAACG,UAAU,EAAC,KAAK;sBAAArC,QAAA,EAAC;oBAE7C;sBAAA8B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC,eAENpE,OAAA,CAAC9B,GAAG;oBAAC4D,OAAO,EAAC,MAAM;oBAACG,UAAU,EAAC,QAAQ;oBAAC8C,GAAG,EAAE,CAAE;oBAACzC,EAAE,EAAE,CAAE;oBAAAH,QAAA,gBACpDnC,OAAA,CAAClB,YAAY;sBAACkF,KAAK,EAAC;oBAAS;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAChCpE,OAAA,CAAC7B,UAAU;sBAACkG,OAAO,EAAC,OAAO;sBAACG,UAAU,EAAC,KAAK;sBAAArC,QAAA,EAAC;oBAE7C;sBAAA8B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC,eAENpE,OAAA,CAAC9B,GAAG;oBAAC4D,OAAO,EAAC,MAAM;oBAACG,UAAU,EAAC,QAAQ;oBAAC8C,GAAG,EAAE,CAAE;oBAAA5C,QAAA,gBAC7CnC,OAAA,CAACZ,cAAc;sBAAC4E,KAAK,EAAC;oBAAS;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAClCpE,OAAA,CAAC7B,UAAU;sBAACkG,OAAO,EAAC,OAAO;sBAACG,UAAU,EAAC,KAAK;sBAAArC,QAAA,EAAC;oBAE7C;sBAAA8B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACK;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC,EAGXvD,aAAa,CAACsE,GAAG,CAAE5E,YAAY,iBAC9BP,OAAA,CAACvB,QAAQ;MAEPuI,IAAI,EAAE,IAAK;MACXC,gBAAgB,EAAE,IAAK;MACvBC,OAAO,EAAEA,CAAA,KAAMhG,uBAAuB,CAACX,YAAY,CAAC4G,EAAE,CAAE;MACxDC,YAAY,EAAE;QAAEC,QAAQ,EAAE,QAAQ;QAAEC,UAAU,EAAE;MAAQ,CAAE;MAAAnF,QAAA,eAE1DnC,OAAA,CAACxB,KAAK;QACJ0I,OAAO,EAAEA,CAAA,KAAMhG,uBAAuB,CAACX,YAAY,CAAC4G,EAAE,CAAE;QACxDI,QAAQ,EAAEhH,YAAY,CAACiB,IAAK;QAC5B6C,OAAO,EAAC,QAAQ;QAChB1C,EAAE,EAAE;UACFC,KAAK,EAAE,MAAM;UACba,YAAY,EAAE,CAAC;UACfoB,SAAS,EAAE;QACb,CAAE;QAAA1B,QAAA,gBAEFnC,OAAA,CAAC7B,UAAU;UAACkG,OAAO,EAAC,WAAW;UAACG,UAAU,EAAC,MAAM;UAAArC,QAAA,EAC9C5B,YAAY,CAACiH;QAAK;UAAAvD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,EACZ7D,YAAY,CAACkB,OAAO,iBACnBzB,OAAA,CAAC7B,UAAU;UAACkG,OAAO,EAAC,OAAO;UAAAlC,QAAA,EACxB5B,YAAY,CAACkB;QAAO;UAAAwC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CACb;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC,GAxBH7D,YAAY,CAAC4G,EAAE;MAAAlD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAyBZ,CACX,CAAC,EAGD7D,YAAY,iBACXP,OAAA,CAACvB,QAAQ;MACPuI,IAAI,EAAE,IAAK;MACXC,gBAAgB,EAAE,IAAK;MACvBC,OAAO,EAAEA,CAAA,KAAM1G,eAAe,CAAC,IAAI,CAAE;MACrC4G,YAAY,EAAE;QAAEC,QAAQ,EAAE,QAAQ;QAAEC,UAAU,EAAE;MAAS,CAAE;MAAAnF,QAAA,eAE3DnC,OAAA,CAACxB,KAAK;QACJ0I,OAAO,EAAEA,CAAA,KAAM1G,eAAe,CAAC,IAAI,CAAE;QACrC+G,QAAQ,EAAEhH,YAAY,CAACiB,IAAK;QAC5B6C,OAAO,EAAC,QAAQ;QAChB1C,EAAE,EAAE;UACFc,YAAY,EAAE,CAAC;UACfoB,SAAS,EAAE;QACb,CAAE;QAAA1B,QAAA,EAED5B,YAAY,CAACkB;MAAO;QAAAwC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CACX;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;EAAA,QAxjBkB/E,WAAW,EAQxBC,OAAO;AAAA,EAgjBZ,CAAC;EAAA,QAxjBiBD,WAAW,EAQxBC,OAAO;AAAA,EAgjBX;AAACmI,GAAA,GA3jBGxH,QAAQ;AA6jBdA,QAAQ,CAACyH,WAAW,GAAG,UAAU;AAEjC,eAAezH,QAAQ;AAAC,IAAAG,EAAA,EAAAqH,GAAA;AAAAE,YAAA,CAAAvH,EAAA;AAAAuH,YAAA,CAAAF,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}