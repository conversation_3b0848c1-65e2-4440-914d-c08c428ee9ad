"use client";

import createSvgIcon from "./utils/createSvgIcon.js";
import { jsx as _jsx } from "react/jsx-runtime";
export default createSvgIcon([/*#__PURE__*/_jsx("circle", {
  cx: "20",
  cy: "12",
  r: "2"
}, "0"), /*#__PURE__*/_jsx("circle", {
  cx: "4",
  cy: "12",
  r: "2"
}, "1"), /*#__PURE__*/_jsx("circle", {
  cx: "12",
  cy: "20",
  r: "2"
}, "2"), /*#__PURE__*/_jsx("path", {
  d: "m7.89 14.65-2.94 2.93c-.39.39-.39 1.02 0 1.41s1.02.39 1.41 0l2.94-2.93c.39-.38.39-1.02 0-1.41a.996.996 0 0 0-1.41 0M6.41 4.94a.996.996 0 0 0-1.41 0c-.39.39-.39 1.02 0 1.41l2.93 2.94c.39.39 1.02.39 1.42 0 .38-.39.38-1.02-.01-1.41zm9.71 9.71c-.39-.39-1.02-.39-1.42 0-.39.39-.39 1.02 0 1.41L17.64 19c.39.39 1.02.39 1.41 0s.39-1.02 0-1.41zm-.06-5.32 2.99-2.98c.39-.4.39-1.03 0-1.42a.996.996 0 0 0-1.41 0l-2.99 2.98c-.39.39-.39 1.02 0 1.42.39.39 1.02.39 1.41 0"
}, "3"), /*#__PURE__*/_jsx("circle", {
  cx: "12",
  cy: "4",
  r: "2"
}, "4")], 'StreamRounded');