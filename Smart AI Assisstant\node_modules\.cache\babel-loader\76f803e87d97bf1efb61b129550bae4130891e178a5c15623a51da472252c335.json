{"ast": null, "code": "import axios from 'axios';\n\n// Create axios instance for auth API\nconst authAPI = axios.create({\n  baseURL: process.env.REACT_APP_API_BASE || 'http://localhost:5000/api',\n  timeout: 10000,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// Token management\nconst TOKEN_KEY = 'ai_security_guard_token';\nconst USER_KEY = 'ai_security_guard_user';\n\n/**\n * Authentication Service\n * Handles all authentication-related operations\n */\nclass AuthService {\n  constructor() {\n    this.token = localStorage.getItem(TOKEN_KEY);\n    this.user = this.getStoredUser();\n    this.setupInterceptors();\n  }\n\n  /**\n   * Setup axios interceptors for automatic token attachment\n   */\n  setupInterceptors() {\n    // Request interceptor to add token\n    authAPI.interceptors.request.use(config => {\n      const token = this.getToken();\n      if (token) {\n        config.headers.Authorization = `Bearer ${token}`;\n      }\n      return config;\n    }, error => Promise.reject(error));\n\n    // Response interceptor to handle token expiration\n    authAPI.interceptors.response.use(response => response, error => {\n      var _error$response;\n      if (((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status) === 401) {\n        this.logout();\n        window.location.href = '/';\n      }\n      return Promise.reject(error);\n    });\n  }\n\n  /**\n   * Get stored user from localStorage\n   */\n  getStoredUser() {\n    try {\n      const userStr = localStorage.getItem(USER_KEY);\n      return userStr ? JSON.parse(userStr) : null;\n    } catch (error) {\n      console.error('Error parsing stored user:', error);\n      localStorage.removeItem(USER_KEY);\n      return null;\n    }\n  }\n\n  /**\n   * Store token and user data\n   */\n  storeAuthData(token, user) {\n    localStorage.setItem(TOKEN_KEY, token);\n    localStorage.setItem(USER_KEY, JSON.stringify(user));\n    this.token = token;\n    this.user = user;\n  }\n\n  /**\n   * Clear stored auth data\n   */\n  clearAuthData() {\n    localStorage.removeItem(TOKEN_KEY);\n    localStorage.removeItem(USER_KEY);\n    this.token = null;\n    this.user = null;\n  }\n\n  /**\n   * Get current token\n   */\n  getToken() {\n    return this.token || localStorage.getItem(TOKEN_KEY);\n  }\n\n  /**\n   * Get current user\n   */\n  getCurrentUser() {\n    return this.user || this.getStoredUser();\n  }\n\n  /**\n   * Check if user is authenticated\n   */\n  isAuthenticated() {\n    return !!this.getToken() && !!this.getCurrentUser();\n  }\n\n  /**\n   * Register new user\n   */\n  async signup(userData) {\n    try {\n      const response = await authAPI.post('/auth/signup', userData);\n      if (response.data.success) {\n        const {\n          user,\n          token\n        } = response.data.data;\n        this.storeAuthData(token, user);\n        return {\n          success: true,\n          user,\n          token\n        };\n      }\n      return {\n        success: false,\n        error: response.data.error\n      };\n    } catch (error) {\n      var _error$response2, _error$response2$data, _error$response3, _error$response3$data;\n      console.error('Signup error:', error);\n      return {\n        success: false,\n        error: ((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.error) || 'Registration failed. Please try again.',\n        details: (_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : (_error$response3$data = _error$response3.data) === null || _error$response3$data === void 0 ? void 0 : _error$response3$data.details\n      };\n    }\n  }\n\n  /**\n   * Login user\n   */\n  async login(credentials) {\n    try {\n      const response = await authAPI.post('/auth/login', credentials);\n      if (response.data.success) {\n        const {\n          user,\n          token\n        } = response.data.data;\n        this.storeAuthData(token, user);\n        return {\n          success: true,\n          user,\n          token\n        };\n      }\n      return {\n        success: false,\n        error: response.data.error\n      };\n    } catch (error) {\n      var _error$response4, _error$response4$data;\n      console.error('Login error:', error);\n      return {\n        success: false,\n        error: ((_error$response4 = error.response) === null || _error$response4 === void 0 ? void 0 : (_error$response4$data = _error$response4.data) === null || _error$response4$data === void 0 ? void 0 : _error$response4$data.error) || 'Login failed. Please try again.'\n      };\n    }\n  }\n\n  /**\n   * Logout user\n   */\n  async logout() {\n    try {\n      if (this.isAuthenticated()) {\n        await authAPI.post('/auth/logout');\n      }\n    } catch (error) {\n      console.error('Logout error:', error);\n    } finally {\n      this.clearAuthData();\n    }\n  }\n\n  /**\n   * Get current user profile from server\n   */\n  async getProfile() {\n    try {\n      const response = await authAPI.get('/auth/me');\n      if (response.data.success) {\n        const user = response.data.data.user;\n        // Update stored user data\n        localStorage.setItem(USER_KEY, JSON.stringify(user));\n        this.user = user;\n        return {\n          success: true,\n          user\n        };\n      }\n      return {\n        success: false,\n        error: response.data.error\n      };\n    } catch (error) {\n      var _error$response5, _error$response5$data;\n      console.error('Get profile error:', error);\n      return {\n        success: false,\n        error: ((_error$response5 = error.response) === null || _error$response5 === void 0 ? void 0 : (_error$response5$data = _error$response5.data) === null || _error$response5$data === void 0 ? void 0 : _error$response5$data.error) || 'Failed to get profile.'\n      };\n    }\n  }\n\n  /**\n   * Validate password strength\n   */\n  validatePassword(password) {\n    const minLength = 8;\n    const hasUpperCase = /[A-Z]/.test(password);\n    const hasLowerCase = /[a-z]/.test(password);\n    const hasNumbers = /\\d/.test(password);\n    const hasSpecialChar = /[@$!%*?&]/.test(password);\n    const errors = [];\n    if (password.length < minLength) {\n      errors.push(`Password must be at least ${minLength} characters long`);\n    }\n    if (!hasUpperCase) {\n      errors.push('Password must contain at least one uppercase letter');\n    }\n    if (!hasLowerCase) {\n      errors.push('Password must contain at least one lowercase letter');\n    }\n    if (!hasNumbers) {\n      errors.push('Password must contain at least one number');\n    }\n    if (!hasSpecialChar) {\n      errors.push('Password must contain at least one special character (@$!%*?&)');\n    }\n    return {\n      isValid: errors.length === 0,\n      errors\n    };\n  }\n\n  /**\n   * Validate email format\n   */\n  validateEmail(email) {\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    return emailRegex.test(email);\n  }\n}\n\n// Create and export singleton instance\nconst authService = new AuthService();\nexport default authService;", "map": {"version": 3, "names": ["axios", "authAPI", "create", "baseURL", "process", "env", "REACT_APP_API_BASE", "timeout", "headers", "TOKEN_KEY", "USER_KEY", "AuthService", "constructor", "token", "localStorage", "getItem", "user", "getStoredUser", "setupInterceptors", "interceptors", "request", "use", "config", "getToken", "Authorization", "error", "Promise", "reject", "response", "_error$response", "status", "logout", "window", "location", "href", "userStr", "JSON", "parse", "console", "removeItem", "storeAuthData", "setItem", "stringify", "clearAuthData", "getCurrentUser", "isAuthenticated", "signup", "userData", "post", "data", "success", "_error$response2", "_error$response2$data", "_error$response3", "_error$response3$data", "details", "login", "credentials", "_error$response4", "_error$response4$data", "getProfile", "get", "_error$response5", "_error$response5$data", "validatePassword", "password", "<PERSON><PERSON><PERSON><PERSON>", "hasUpperCase", "test", "hasLowerCase", "hasNumbers", "hasSpecialChar", "errors", "length", "push", "<PERSON><PERSON><PERSON><PERSON>", "validateEmail", "email", "emailRegex", "authService"], "sources": ["E:/PythonProjects/React.js/Smart AI Assisstant/src/services/auth.js"], "sourcesContent": ["import axios from 'axios';\n\n// Create axios instance for auth API\nconst authAPI = axios.create({\n  baseURL: process.env.REACT_APP_API_BASE || 'http://localhost:5000/api',\n  timeout: 10000,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// Token management\nconst TOKEN_KEY = 'ai_security_guard_token';\nconst USER_KEY = 'ai_security_guard_user';\n\n/**\n * Authentication Service\n * Handles all authentication-related operations\n */\nclass AuthService {\n  constructor() {\n    this.token = localStorage.getItem(TOKEN_KEY);\n    this.user = this.getStoredUser();\n    this.setupInterceptors();\n  }\n\n  /**\n   * Setup axios interceptors for automatic token attachment\n   */\n  setupInterceptors() {\n    // Request interceptor to add token\n    authAPI.interceptors.request.use(\n      (config) => {\n        const token = this.getToken();\n        if (token) {\n          config.headers.Authorization = `Bearer ${token}`;\n        }\n        return config;\n      },\n      (error) => Promise.reject(error)\n    );\n\n    // Response interceptor to handle token expiration\n    authAPI.interceptors.response.use(\n      (response) => response,\n      (error) => {\n        if (error.response?.status === 401) {\n          this.logout();\n          window.location.href = '/';\n        }\n        return Promise.reject(error);\n      }\n    );\n  }\n\n  /**\n   * Get stored user from localStorage\n   */\n  getStoredUser() {\n    try {\n      const userStr = localStorage.getItem(USER_KEY);\n      return userStr ? JSON.parse(userStr) : null;\n    } catch (error) {\n      console.error('Error parsing stored user:', error);\n      localStorage.removeItem(USER_KEY);\n      return null;\n    }\n  }\n\n  /**\n   * Store token and user data\n   */\n  storeAuthData(token, user) {\n    localStorage.setItem(TOKEN_KEY, token);\n    localStorage.setItem(USER_KEY, JSON.stringify(user));\n    this.token = token;\n    this.user = user;\n  }\n\n  /**\n   * Clear stored auth data\n   */\n  clearAuthData() {\n    localStorage.removeItem(TOKEN_KEY);\n    localStorage.removeItem(USER_KEY);\n    this.token = null;\n    this.user = null;\n  }\n\n  /**\n   * Get current token\n   */\n  getToken() {\n    return this.token || localStorage.getItem(TOKEN_KEY);\n  }\n\n  /**\n   * Get current user\n   */\n  getCurrentUser() {\n    return this.user || this.getStoredUser();\n  }\n\n  /**\n   * Check if user is authenticated\n   */\n  isAuthenticated() {\n    return !!this.getToken() && !!this.getCurrentUser();\n  }\n\n  /**\n   * Register new user\n   */\n  async signup(userData) {\n    try {\n      const response = await authAPI.post('/auth/signup', userData);\n      \n      if (response.data.success) {\n        const { user, token } = response.data.data;\n        this.storeAuthData(token, user);\n        return { success: true, user, token };\n      }\n      \n      return { success: false, error: response.data.error };\n    } catch (error) {\n      console.error('Signup error:', error);\n      return {\n        success: false,\n        error: error.response?.data?.error || 'Registration failed. Please try again.',\n        details: error.response?.data?.details,\n      };\n    }\n  }\n\n  /**\n   * Login user\n   */\n  async login(credentials) {\n    try {\n      const response = await authAPI.post('/auth/login', credentials);\n      \n      if (response.data.success) {\n        const { user, token } = response.data.data;\n        this.storeAuthData(token, user);\n        return { success: true, user, token };\n      }\n      \n      return { success: false, error: response.data.error };\n    } catch (error) {\n      console.error('Login error:', error);\n      return {\n        success: false,\n        error: error.response?.data?.error || 'Login failed. Please try again.',\n      };\n    }\n  }\n\n  /**\n   * Logout user\n   */\n  async logout() {\n    try {\n      if (this.isAuthenticated()) {\n        await authAPI.post('/auth/logout');\n      }\n    } catch (error) {\n      console.error('Logout error:', error);\n    } finally {\n      this.clearAuthData();\n    }\n  }\n\n  /**\n   * Get current user profile from server\n   */\n  async getProfile() {\n    try {\n      const response = await authAPI.get('/auth/me');\n      \n      if (response.data.success) {\n        const user = response.data.data.user;\n        // Update stored user data\n        localStorage.setItem(USER_KEY, JSON.stringify(user));\n        this.user = user;\n        return { success: true, user };\n      }\n      \n      return { success: false, error: response.data.error };\n    } catch (error) {\n      console.error('Get profile error:', error);\n      return {\n        success: false,\n        error: error.response?.data?.error || 'Failed to get profile.',\n      };\n    }\n  }\n\n  /**\n   * Validate password strength\n   */\n  validatePassword(password) {\n    const minLength = 8;\n    const hasUpperCase = /[A-Z]/.test(password);\n    const hasLowerCase = /[a-z]/.test(password);\n    const hasNumbers = /\\d/.test(password);\n    const hasSpecialChar = /[@$!%*?&]/.test(password);\n\n    const errors = [];\n    \n    if (password.length < minLength) {\n      errors.push(`Password must be at least ${minLength} characters long`);\n    }\n    if (!hasUpperCase) {\n      errors.push('Password must contain at least one uppercase letter');\n    }\n    if (!hasLowerCase) {\n      errors.push('Password must contain at least one lowercase letter');\n    }\n    if (!hasNumbers) {\n      errors.push('Password must contain at least one number');\n    }\n    if (!hasSpecialChar) {\n      errors.push('Password must contain at least one special character (@$!%*?&)');\n    }\n\n    return {\n      isValid: errors.length === 0,\n      errors,\n    };\n  }\n\n  /**\n   * Validate email format\n   */\n  validateEmail(email) {\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    return emailRegex.test(email);\n  }\n}\n\n// Create and export singleton instance\nconst authService = new AuthService();\nexport default authService;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;;AAEzB;AACA,MAAMC,OAAO,GAAGD,KAAK,CAACE,MAAM,CAAC;EAC3BC,OAAO,EAAEC,OAAO,CAACC,GAAG,CAACC,kBAAkB,IAAI,2BAA2B;EACtEC,OAAO,EAAE,KAAK;EACdC,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACA,MAAMC,SAAS,GAAG,yBAAyB;AAC3C,MAAMC,QAAQ,GAAG,wBAAwB;;AAEzC;AACA;AACA;AACA;AACA,MAAMC,WAAW,CAAC;EAChBC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAACN,SAAS,CAAC;IAC5C,IAAI,CAACO,IAAI,GAAG,IAAI,CAACC,aAAa,CAAC,CAAC;IAChC,IAAI,CAACC,iBAAiB,CAAC,CAAC;EAC1B;;EAEA;AACF;AACA;EACEA,iBAAiBA,CAAA,EAAG;IAClB;IACAjB,OAAO,CAACkB,YAAY,CAACC,OAAO,CAACC,GAAG,CAC7BC,MAAM,IAAK;MACV,MAAMT,KAAK,GAAG,IAAI,CAACU,QAAQ,CAAC,CAAC;MAC7B,IAAIV,KAAK,EAAE;QACTS,MAAM,CAACd,OAAO,CAACgB,aAAa,GAAG,UAAUX,KAAK,EAAE;MAClD;MACA,OAAOS,MAAM;IACf,CAAC,EACAG,KAAK,IAAKC,OAAO,CAACC,MAAM,CAACF,KAAK,CACjC,CAAC;;IAED;IACAxB,OAAO,CAACkB,YAAY,CAACS,QAAQ,CAACP,GAAG,CAC9BO,QAAQ,IAAKA,QAAQ,EACrBH,KAAK,IAAK;MAAA,IAAAI,eAAA;MACT,IAAI,EAAAA,eAAA,GAAAJ,KAAK,CAACG,QAAQ,cAAAC,eAAA,uBAAdA,eAAA,CAAgBC,MAAM,MAAK,GAAG,EAAE;QAClC,IAAI,CAACC,MAAM,CAAC,CAAC;QACbC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,GAAG;MAC5B;MACA,OAAOR,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;IAC9B,CACF,CAAC;EACH;;EAEA;AACF;AACA;EACER,aAAaA,CAAA,EAAG;IACd,IAAI;MACF,MAAMkB,OAAO,GAAGrB,YAAY,CAACC,OAAO,CAACL,QAAQ,CAAC;MAC9C,OAAOyB,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACF,OAAO,CAAC,GAAG,IAAI;IAC7C,CAAC,CAAC,OAAOV,KAAK,EAAE;MACda,OAAO,CAACb,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClDX,YAAY,CAACyB,UAAU,CAAC7B,QAAQ,CAAC;MACjC,OAAO,IAAI;IACb;EACF;;EAEA;AACF;AACA;EACE8B,aAAaA,CAAC3B,KAAK,EAAEG,IAAI,EAAE;IACzBF,YAAY,CAAC2B,OAAO,CAAChC,SAAS,EAAEI,KAAK,CAAC;IACtCC,YAAY,CAAC2B,OAAO,CAAC/B,QAAQ,EAAE0B,IAAI,CAACM,SAAS,CAAC1B,IAAI,CAAC,CAAC;IACpD,IAAI,CAACH,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACG,IAAI,GAAGA,IAAI;EAClB;;EAEA;AACF;AACA;EACE2B,aAAaA,CAAA,EAAG;IACd7B,YAAY,CAACyB,UAAU,CAAC9B,SAAS,CAAC;IAClCK,YAAY,CAACyB,UAAU,CAAC7B,QAAQ,CAAC;IACjC,IAAI,CAACG,KAAK,GAAG,IAAI;IACjB,IAAI,CAACG,IAAI,GAAG,IAAI;EAClB;;EAEA;AACF;AACA;EACEO,QAAQA,CAAA,EAAG;IACT,OAAO,IAAI,CAACV,KAAK,IAAIC,YAAY,CAACC,OAAO,CAACN,SAAS,CAAC;EACtD;;EAEA;AACF;AACA;EACEmC,cAAcA,CAAA,EAAG;IACf,OAAO,IAAI,CAAC5B,IAAI,IAAI,IAAI,CAACC,aAAa,CAAC,CAAC;EAC1C;;EAEA;AACF;AACA;EACE4B,eAAeA,CAAA,EAAG;IAChB,OAAO,CAAC,CAAC,IAAI,CAACtB,QAAQ,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAACqB,cAAc,CAAC,CAAC;EACrD;;EAEA;AACF;AACA;EACE,MAAME,MAAMA,CAACC,QAAQ,EAAE;IACrB,IAAI;MACF,MAAMnB,QAAQ,GAAG,MAAM3B,OAAO,CAAC+C,IAAI,CAAC,cAAc,EAAED,QAAQ,CAAC;MAE7D,IAAInB,QAAQ,CAACqB,IAAI,CAACC,OAAO,EAAE;QACzB,MAAM;UAAElC,IAAI;UAAEH;QAAM,CAAC,GAAGe,QAAQ,CAACqB,IAAI,CAACA,IAAI;QAC1C,IAAI,CAACT,aAAa,CAAC3B,KAAK,EAAEG,IAAI,CAAC;QAC/B,OAAO;UAAEkC,OAAO,EAAE,IAAI;UAAElC,IAAI;UAAEH;QAAM,CAAC;MACvC;MAEA,OAAO;QAAEqC,OAAO,EAAE,KAAK;QAAEzB,KAAK,EAAEG,QAAQ,CAACqB,IAAI,CAACxB;MAAM,CAAC;IACvD,CAAC,CAAC,OAAOA,KAAK,EAAE;MAAA,IAAA0B,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,qBAAA;MACdhB,OAAO,CAACb,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;MACrC,OAAO;QACLyB,OAAO,EAAE,KAAK;QACdzB,KAAK,EAAE,EAAA0B,gBAAA,GAAA1B,KAAK,CAACG,QAAQ,cAAAuB,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBF,IAAI,cAAAG,qBAAA,uBAApBA,qBAAA,CAAsB3B,KAAK,KAAI,wCAAwC;QAC9E8B,OAAO,GAAAF,gBAAA,GAAE5B,KAAK,CAACG,QAAQ,cAAAyB,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBJ,IAAI,cAAAK,qBAAA,uBAApBA,qBAAA,CAAsBC;MACjC,CAAC;IACH;EACF;;EAEA;AACF;AACA;EACE,MAAMC,KAAKA,CAACC,WAAW,EAAE;IACvB,IAAI;MACF,MAAM7B,QAAQ,GAAG,MAAM3B,OAAO,CAAC+C,IAAI,CAAC,aAAa,EAAES,WAAW,CAAC;MAE/D,IAAI7B,QAAQ,CAACqB,IAAI,CAACC,OAAO,EAAE;QACzB,MAAM;UAAElC,IAAI;UAAEH;QAAM,CAAC,GAAGe,QAAQ,CAACqB,IAAI,CAACA,IAAI;QAC1C,IAAI,CAACT,aAAa,CAAC3B,KAAK,EAAEG,IAAI,CAAC;QAC/B,OAAO;UAAEkC,OAAO,EAAE,IAAI;UAAElC,IAAI;UAAEH;QAAM,CAAC;MACvC;MAEA,OAAO;QAAEqC,OAAO,EAAE,KAAK;QAAEzB,KAAK,EAAEG,QAAQ,CAACqB,IAAI,CAACxB;MAAM,CAAC;IACvD,CAAC,CAAC,OAAOA,KAAK,EAAE;MAAA,IAAAiC,gBAAA,EAAAC,qBAAA;MACdrB,OAAO,CAACb,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;MACpC,OAAO;QACLyB,OAAO,EAAE,KAAK;QACdzB,KAAK,EAAE,EAAAiC,gBAAA,GAAAjC,KAAK,CAACG,QAAQ,cAAA8B,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBT,IAAI,cAAAU,qBAAA,uBAApBA,qBAAA,CAAsBlC,KAAK,KAAI;MACxC,CAAC;IACH;EACF;;EAEA;AACF;AACA;EACE,MAAMM,MAAMA,CAAA,EAAG;IACb,IAAI;MACF,IAAI,IAAI,CAACc,eAAe,CAAC,CAAC,EAAE;QAC1B,MAAM5C,OAAO,CAAC+C,IAAI,CAAC,cAAc,CAAC;MACpC;IACF,CAAC,CAAC,OAAOvB,KAAK,EAAE;MACda,OAAO,CAACb,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;IACvC,CAAC,SAAS;MACR,IAAI,CAACkB,aAAa,CAAC,CAAC;IACtB;EACF;;EAEA;AACF;AACA;EACE,MAAMiB,UAAUA,CAAA,EAAG;IACjB,IAAI;MACF,MAAMhC,QAAQ,GAAG,MAAM3B,OAAO,CAAC4D,GAAG,CAAC,UAAU,CAAC;MAE9C,IAAIjC,QAAQ,CAACqB,IAAI,CAACC,OAAO,EAAE;QACzB,MAAMlC,IAAI,GAAGY,QAAQ,CAACqB,IAAI,CAACA,IAAI,CAACjC,IAAI;QACpC;QACAF,YAAY,CAAC2B,OAAO,CAAC/B,QAAQ,EAAE0B,IAAI,CAACM,SAAS,CAAC1B,IAAI,CAAC,CAAC;QACpD,IAAI,CAACA,IAAI,GAAGA,IAAI;QAChB,OAAO;UAAEkC,OAAO,EAAE,IAAI;UAAElC;QAAK,CAAC;MAChC;MAEA,OAAO;QAAEkC,OAAO,EAAE,KAAK;QAAEzB,KAAK,EAAEG,QAAQ,CAACqB,IAAI,CAACxB;MAAM,CAAC;IACvD,CAAC,CAAC,OAAOA,KAAK,EAAE;MAAA,IAAAqC,gBAAA,EAAAC,qBAAA;MACdzB,OAAO,CAACb,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1C,OAAO;QACLyB,OAAO,EAAE,KAAK;QACdzB,KAAK,EAAE,EAAAqC,gBAAA,GAAArC,KAAK,CAACG,QAAQ,cAAAkC,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBb,IAAI,cAAAc,qBAAA,uBAApBA,qBAAA,CAAsBtC,KAAK,KAAI;MACxC,CAAC;IACH;EACF;;EAEA;AACF;AACA;EACEuC,gBAAgBA,CAACC,QAAQ,EAAE;IACzB,MAAMC,SAAS,GAAG,CAAC;IACnB,MAAMC,YAAY,GAAG,OAAO,CAACC,IAAI,CAACH,QAAQ,CAAC;IAC3C,MAAMI,YAAY,GAAG,OAAO,CAACD,IAAI,CAACH,QAAQ,CAAC;IAC3C,MAAMK,UAAU,GAAG,IAAI,CAACF,IAAI,CAACH,QAAQ,CAAC;IACtC,MAAMM,cAAc,GAAG,WAAW,CAACH,IAAI,CAACH,QAAQ,CAAC;IAEjD,MAAMO,MAAM,GAAG,EAAE;IAEjB,IAAIP,QAAQ,CAACQ,MAAM,GAAGP,SAAS,EAAE;MAC/BM,MAAM,CAACE,IAAI,CAAC,6BAA6BR,SAAS,kBAAkB,CAAC;IACvE;IACA,IAAI,CAACC,YAAY,EAAE;MACjBK,MAAM,CAACE,IAAI,CAAC,qDAAqD,CAAC;IACpE;IACA,IAAI,CAACL,YAAY,EAAE;MACjBG,MAAM,CAACE,IAAI,CAAC,qDAAqD,CAAC;IACpE;IACA,IAAI,CAACJ,UAAU,EAAE;MACfE,MAAM,CAACE,IAAI,CAAC,2CAA2C,CAAC;IAC1D;IACA,IAAI,CAACH,cAAc,EAAE;MACnBC,MAAM,CAACE,IAAI,CAAC,gEAAgE,CAAC;IAC/E;IAEA,OAAO;MACLC,OAAO,EAAEH,MAAM,CAACC,MAAM,KAAK,CAAC;MAC5BD;IACF,CAAC;EACH;;EAEA;AACF;AACA;EACEI,aAAaA,CAACC,KAAK,EAAE;IACnB,MAAMC,UAAU,GAAG,4BAA4B;IAC/C,OAAOA,UAAU,CAACV,IAAI,CAACS,KAAK,CAAC;EAC/B;AACF;;AAEA;AACA,MAAME,WAAW,GAAG,IAAIpE,WAAW,CAAC,CAAC;AACrC,eAAeoE,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}