{"version": 3, "names": ["_helper<PERSON>lugin<PERSON><PERSON>s", "require", "_pluginSyntaxDecorators", "_helperCreateClassFeaturesPlugin", "_transformerLegacy", "_default", "exports", "default", "declare", "api", "options", "assertVersion", "legacy", "version", "name", "inherits", "syntaxDecorators", "visitor", "legacyVisitor", "createClassFeaturePlugin", "feature", "FEATURES", "decorators", "decoratorVersion", "Error"], "sources": ["../src/index.ts"], "sourcesContent": ["/* eslint-disable @babel/development/plugin-name */\n\nimport { declare } from \"@babel/helper-plugin-utils\";\nimport syntaxDecorators from \"@babel/plugin-syntax-decorators\";\nimport {\n  createClassFeaturePlugin,\n  FEATURES,\n} from \"@babel/helper-create-class-features-plugin\";\nimport legacyVisitor from \"./transformer-legacy.ts\";\nimport type { Options as SyntaxOptions } from \"@babel/plugin-syntax-decorators\";\n\ninterface Options extends SyntaxOptions {\n  /** @deprecated use `constantSuper` assumption instead. Only supported in 2021-12 version. */\n  loose?: boolean;\n}\n\nexport type { Options };\n\nexport default declare((api, options: Options) => {\n  api.assertVersion(REQUIRED_VERSION(7));\n\n  // Options are validated in @babel/plugin-syntax-decorators\n  if (!process.env.BABEL_8_BREAKING) {\n    // eslint-disable-next-line no-var\n    var { legacy } = options;\n  }\n  const { version } = options;\n\n  if (\n    process.env.BABEL_8_BREAKING\n      ? version === \"legacy\"\n      : legacy || version === \"legacy\"\n  ) {\n    return {\n      name: \"proposal-decorators\",\n      inherits: syntaxDecorators,\n      visitor: legacyVisitor,\n    };\n  } else if (\n    !version ||\n    version === \"2018-09\" ||\n    version === \"2021-12\" ||\n    version === \"2022-03\" ||\n    version === \"2023-01\" ||\n    version === \"2023-05\" ||\n    version === \"2023-11\"\n  ) {\n    api.assertVersion(REQUIRED_VERSION(\"^7.0.2\"));\n    return createClassFeaturePlugin({\n      name: \"proposal-decorators\",\n\n      api,\n      feature: FEATURES.decorators,\n      inherits: syntaxDecorators,\n      // @ts-expect-error version must not be \"legacy\" here\n      decoratorVersion: version,\n      // loose: options.loose, Not supported\n    });\n  } else {\n    throw new Error(\n      \"The '.version' option must be one of 'legacy', '2023-11', '2023-05', '2023-01', '2022-03', or '2021-12'.\",\n    );\n  }\n});\n"], "mappings": ";;;;;;AAEA,IAAAA,kBAAA,GAAAC,OAAA;AACA,IAAAC,uBAAA,GAAAD,OAAA;AACA,IAAAE,gCAAA,GAAAF,OAAA;AAIA,IAAAG,kBAAA,GAAAH,OAAA;AAAoD,IAAAI,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAUrC,IAAAC,0BAAO,EAAC,CAACC,GAAG,EAAEC,OAAgB,KAAK;EAChDD,GAAG,CAACE,aAAa,CAAkB,CAAE,CAAC;EAGH;IAEjC,IAAI;MAAEC;IAAO,CAAC,GAAGF,OAAO;EAC1B;EACA,MAAM;IAAEG;EAAQ,CAAC,GAAGH,OAAO;EAE3B,IAGME,MAAM,IAAIC,OAAO,KAAK,QAAQ,EAClC;IACA,OAAO;MACLC,IAAI,EAAE,qBAAqB;MAC3BC,QAAQ,EAAEC,+BAAgB;MAC1BC,OAAO,EAAEC;IACX,CAAC;EACH,CAAC,MAAM,IACL,CAACL,OAAO,IACRA,OAAO,KAAK,SAAS,IACrBA,OAAO,KAAK,SAAS,IACrBA,OAAO,KAAK,SAAS,IACrBA,OAAO,KAAK,SAAS,IACrBA,OAAO,KAAK,SAAS,IACrBA,OAAO,KAAK,SAAS,EACrB;IACAJ,GAAG,CAACE,aAAa,CAAkB,QAAS,CAAC;IAC7C,OAAO,IAAAQ,yDAAwB,EAAC;MAC9BL,IAAI,EAAE,qBAAqB;MAE3BL,GAAG;MACHW,OAAO,EAAEC,yCAAQ,CAACC,UAAU;MAC5BP,QAAQ,EAAEC,+BAAgB;MAE1BO,gBAAgB,EAAEV;IAEpB,CAAC,CAAC;EACJ,CAAC,MAAM;IACL,MAAM,IAAIW,KAAK,CACb,0GACF,CAAC;EACH;AACF,CAAC,CAAC", "ignoreList": []}