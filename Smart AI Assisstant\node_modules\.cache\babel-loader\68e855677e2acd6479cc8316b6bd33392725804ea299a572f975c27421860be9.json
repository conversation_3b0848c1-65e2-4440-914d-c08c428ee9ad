{"ast": null, "code": "var _jsxFileName = \"E:\\\\PythonProjects\\\\React.js\\\\Smart AI Assisstant\\\\src\\\\pages\\\\AboutPage.jsx\";\nimport React from 'react';\nimport { Container, Grid, Box, Typography, Card, CardContent, Avatar, Chip, Button, Stack, Paper, List, ListItem, ListItemIcon, ListItemText } from '@mui/material';\nimport { Security as SecurityIcon, Speed as SpeedIcon, Shield as ShieldIcon, Analytics as AnalyticsIcon, CloudDone as CloudIcon, Verified as VerifiedIcon, CheckCircle as CheckIcon, Star as StarIcon, TrendingUp as TrendingUpIcon, Group as GroupIcon, Email as EmailIcon, Phone as PhoneIcon } from '@mui/icons-material';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AboutPage = /*#__PURE__*/React.memo(_c = () => {\n  const features = [{\n    icon: /*#__PURE__*/_jsxDEV(SecurityIcon, {\n      sx: {\n        fontSize: 40\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 37,\n      columnNumber: 13\n    }, this),\n    title: 'Advanced Threat Detection',\n    description: 'AI-powered scanning engine that identifies malware, phishing, and security threats with 99.9% accuracy.'\n  }, {\n    icon: /*#__PURE__*/_jsxDEV(SpeedIcon, {\n      sx: {\n        fontSize: 40\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 42,\n      columnNumber: 13\n    }, this),\n    title: 'Real-time Analysis',\n    description: 'Lightning-fast scanning with results delivered in seconds, not minutes.'\n  }, {\n    icon: /*#__PURE__*/_jsxDEV(ShieldIcon, {\n      sx: {\n        fontSize: 40\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 47,\n      columnNumber: 13\n    }, this),\n    title: 'Enterprise Security',\n    description: 'SOC 2 compliant with enterprise-grade security and data protection.'\n  }, {\n    icon: /*#__PURE__*/_jsxDEV(AnalyticsIcon, {\n      sx: {\n        fontSize: 40\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 52,\n      columnNumber: 13\n    }, this),\n    title: 'Detailed Reports',\n    description: 'Comprehensive analysis reports with actionable security recommendations.'\n  }, {\n    icon: /*#__PURE__*/_jsxDEV(CloudIcon, {\n      sx: {\n        fontSize: 40\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 57,\n      columnNumber: 13\n    }, this),\n    title: 'Cloud-based',\n    description: 'Scalable cloud infrastructure ensuring 99.9% uptime and global accessibility.'\n  }, {\n    icon: /*#__PURE__*/_jsxDEV(VerifiedIcon, {\n      sx: {\n        fontSize: 40\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 62,\n      columnNumber: 13\n    }, this),\n    title: 'Trusted Platform',\n    description: 'Used by 10,000+ organizations worldwide for critical security operations.'\n  }];\n  const teamMembers = [{\n    name: 'Dr. Sarah Chen',\n    role: 'Chief Security Officer',\n    avatar: '/api/placeholder/150/150',\n    bio: '15+ years in cybersecurity, former NSA researcher'\n  }, {\n    name: 'Michael Rodriguez',\n    role: 'Lead AI Engineer',\n    avatar: '/api/placeholder/150/150',\n    bio: 'PhD in Machine Learning, AI security specialist'\n  }, {\n    name: 'Emily Johnson',\n    role: 'Product Manager',\n    avatar: '/api/placeholder/150/150',\n    bio: 'Product strategy expert with enterprise security focus'\n  }, {\n    name: 'David Kim',\n    role: 'Security Architect',\n    avatar: '/api/placeholder/150/150',\n    bio: 'Cloud security expert, certified ethical hacker'\n  }];\n  const stats = [{\n    number: '10M+',\n    label: 'Files Scanned',\n    icon: /*#__PURE__*/_jsxDEV(SecurityIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 96,\n      columnNumber: 53\n    }, this)\n  }, {\n    number: '50K+',\n    label: 'Threats Blocked',\n    icon: /*#__PURE__*/_jsxDEV(ShieldIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 97,\n      columnNumber: 55\n    }, this)\n  }, {\n    number: '99.9%',\n    label: 'Uptime',\n    icon: /*#__PURE__*/_jsxDEV(TrendingUpIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 98,\n      columnNumber: 47\n    }, this)\n  }, {\n    number: '10K+',\n    label: 'Organizations',\n    icon: /*#__PURE__*/_jsxDEV(GroupIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 99,\n      columnNumber: 53\n    }, this)\n  }];\n  const certifications = ['SOC 2 Type II', 'ISO 27001', 'GDPR Compliant', 'HIPAA Ready', 'FedRAMP Authorized', 'PCI DSS Level 1'];\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      width: '100%',\n      minHeight: '100vh',\n      display: 'flex',\n      flexDirection: 'column',\n      justifyContent: 'center',\n      alignItems: 'center'\n    },\n    children: /*#__PURE__*/_jsxDEV(Container, {\n      maxWidth: \"xl\",\n      sx: {\n        py: 8\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        textAlign: \"center\",\n        mb: 12,\n        sx: {\n          background: theme => theme.palette.mode === 'dark' ? 'linear-gradient(135deg, rgba(25, 118, 210, 0.1) 0%, rgba(156, 39, 176, 0.1) 100%)' : 'linear-gradient(135deg, rgba(25, 118, 210, 0.05) 0%, rgba(156, 39, 176, 0.05) 100%)',\n          borderRadius: 4,\n          py: 8,\n          px: 4,\n          position: 'relative',\n          overflow: 'hidden',\n          '&::before': {\n            content: '\"\"',\n            position: 'absolute',\n            top: 0,\n            left: 0,\n            right: 0,\n            bottom: 0,\n            background: 'linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%)',\n            transform: 'translateX(-100%)',\n            animation: 'shimmer 3s infinite'\n          },\n          '@keyframes shimmer': {\n            '0%': {\n              transform: 'translateX(-100%)'\n            },\n            '100%': {\n              transform: 'translateX(100%)'\n            }\n          }\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n            borderRadius: '50%',\n            width: 120,\n            height: 120,\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            mx: 'auto',\n            mb: 4,\n            boxShadow: '0 20px 40px rgba(102, 126, 234, 0.3)',\n            position: 'relative',\n            '&::after': {\n              content: '\"\"',\n              position: 'absolute',\n              top: -10,\n              left: -10,\n              right: -10,\n              bottom: -10,\n              borderRadius: '50%',\n              border: '2px solid',\n              borderColor: 'primary.main',\n              opacity: 0.3,\n              animation: 'pulse 2s infinite'\n            },\n            '@keyframes pulse': {\n              '0%': {\n                transform: 'scale(1)',\n                opacity: 0.3\n              },\n              '50%': {\n                transform: 'scale(1.1)',\n                opacity: 0.1\n              },\n              '100%': {\n                transform: 'scale(1)',\n                opacity: 0.3\n              }\n            }\n          },\n          children: /*#__PURE__*/_jsxDEV(SecurityIcon, {\n            sx: {\n              fontSize: 60,\n              color: 'white'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 153,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h1\",\n          component: \"h1\",\n          gutterBottom: true,\n          fontWeight: \"bold\",\n          sx: {\n            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n            backgroundClip: 'text',\n            WebkitBackgroundClip: 'text',\n            WebkitTextFillColor: 'transparent',\n            mb: 3\n          },\n          children: \"About AI Security Guard\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 189,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h4\",\n          color: \"text.secondary\",\n          maxWidth: \"900px\",\n          mx: \"auto\",\n          mb: 6,\n          sx: {\n            lineHeight: 1.6,\n            fontWeight: 300\n          },\n          children: \"We're on a mission to make the digital world safer through advanced AI-powered security solutions. Our platform protects organizations worldwide from cyber threats.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 205,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Stack, {\n          direction: {\n            xs: 'column',\n            sm: 'row'\n          },\n          spacing: 3,\n          justifyContent: \"center\",\n          alignItems: \"center\",\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            size: \"large\",\n            startIcon: /*#__PURE__*/_jsxDEV(EmailIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 229,\n              columnNumber: 26\n            }, this),\n            sx: {\n              px: 4,\n              py: 2,\n              borderRadius: 3,\n              fontSize: '1.1rem',\n              minWidth: 200,\n              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n              '&:hover': {\n                background: 'linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)',\n                transform: 'translateY(-2px)',\n                boxShadow: '0 8px 25px rgba(102, 126, 234, 0.4)'\n              }\n            },\n            children: \"Contact Us\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 226,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outlined\",\n            size: \"large\",\n            startIcon: /*#__PURE__*/_jsxDEV(PhoneIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 249,\n              columnNumber: 26\n            }, this),\n            sx: {\n              px: 4,\n              py: 2,\n              borderRadius: 3,\n              fontSize: '1.1rem',\n              minWidth: 200,\n              borderWidth: 2,\n              '&:hover': {\n                borderWidth: 2,\n                transform: 'translateY(-2px)',\n                boxShadow: '0 8px 25px rgba(102, 126, 234, 0.2)'\n              }\n            },\n            children: \"Schedule Demo\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 246,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 220,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 124,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        mb: 12,\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h3\",\n          component: \"h2\",\n          textAlign: \"center\",\n          gutterBottom: true,\n          fontWeight: \"bold\",\n          mb: 6,\n          children: \"Trusted by Industry Leaders\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 271,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 4,\n          justifyContent: \"center\",\n          children: stats.map((stat, index) => /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 6,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              sx: {\n                textAlign: 'center',\n                height: '100%',\n                background: theme => theme.palette.mode === 'dark' ? 'linear-gradient(135deg, rgba(25, 118, 210, 0.1) 0%, rgba(156, 39, 176, 0.1) 100%)' : 'linear-gradient(135deg, rgba(25, 118, 210, 0.05) 0%, rgba(156, 39, 176, 0.05) 100%)',\n                border: theme => `1px solid ${theme.palette.divider}`,\n                transition: 'all 0.3s ease',\n                '&:hover': {\n                  transform: 'translateY(-8px)',\n                  boxShadow: '0 20px 40px rgba(0,0,0,0.1)'\n                }\n              },\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                sx: {\n                  p: 4\n                },\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    color: 'primary.main',\n                    mb: 2,\n                    display: 'flex',\n                    justifyContent: 'center',\n                    alignItems: 'center',\n                    width: 60,\n                    height: 60,\n                    borderRadius: '50%',\n                    background: 'linear-gradient(135deg, rgba(25, 118, 210, 0.1) 0%, rgba(156, 39, 176, 0.1) 100%)',\n                    mx: 'auto'\n                  },\n                  children: stat.icon\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 301,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h2\",\n                  fontWeight: \"bold\",\n                  color: \"primary\",\n                  sx: {\n                    mb: 1\n                  },\n                  children: stat.number\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 317,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  color: \"text.secondary\",\n                  fontWeight: \"500\",\n                  children: stat.label\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 325,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 300,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 285,\n              columnNumber: 17\n            }, this)\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 284,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 282,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 270,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        mb: 12,\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          textAlign: \"center\",\n          mb: 8,\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h3\",\n            component: \"h2\",\n            gutterBottom: true,\n            fontWeight: \"bold\",\n            sx: {\n              mb: 3\n            },\n            children: \"Why Choose AI Security Guard?\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 342,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h5\",\n            color: \"text.secondary\",\n            maxWidth: \"700px\",\n            mx: \"auto\",\n            sx: {\n              lineHeight: 1.6,\n              fontWeight: 300\n            },\n            children: \"Advanced features designed for modern security challenges\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 351,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 341,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 4,\n          justifyContent: \"center\",\n          children: features.map((feature, index) => /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            lg: 4,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              sx: {\n                height: '100%',\n                transition: 'all 0.3s ease',\n                background: theme => theme.palette.mode === 'dark' ? 'linear-gradient(135deg, rgba(30, 30, 30, 0.8) 0%, rgba(50, 50, 50, 0.8) 100%)' : 'linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.9) 100%)',\n                backdropFilter: 'blur(10px)',\n                border: theme => `1px solid ${theme.palette.divider}`,\n                '&:hover': {\n                  transform: 'translateY(-8px) scale(1.02)',\n                  boxShadow: '0 20px 40px rgba(0,0,0,0.15)'\n                }\n              },\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                sx: {\n                  textAlign: 'center',\n                  p: 4\n                },\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    color: 'primary.main',\n                    mb: 3,\n                    display: 'flex',\n                    justifyContent: 'center',\n                    alignItems: 'center',\n                    width: 80,\n                    height: 80,\n                    borderRadius: '50%',\n                    background: 'linear-gradient(135deg, rgba(25, 118, 210, 0.1) 0%, rgba(156, 39, 176, 0.1) 100%)',\n                    mx: 'auto',\n                    position: 'relative',\n                    '&::after': {\n                      content: '\"\"',\n                      position: 'absolute',\n                      top: -5,\n                      left: -5,\n                      right: -5,\n                      bottom: -5,\n                      borderRadius: '50%',\n                      border: '2px solid',\n                      borderColor: 'primary.main',\n                      opacity: 0.2\n                    }\n                  },\n                  children: feature.icon\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 381,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h5\",\n                  gutterBottom: true,\n                  fontWeight: \"600\",\n                  sx: {\n                    mb: 2\n                  },\n                  children: feature.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 410,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body1\",\n                  color: \"text.secondary\",\n                  sx: {\n                    lineHeight: 1.7\n                  },\n                  children: feature.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 418,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 380,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 365,\n              columnNumber: 17\n            }, this)\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 364,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 362,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 340,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        mb: 8,\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h3\",\n          component: \"h2\",\n          textAlign: \"center\",\n          gutterBottom: true,\n          fontWeight: \"bold\",\n          children: \"Meet Our Team\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 434,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          color: \"text.secondary\",\n          textAlign: \"center\",\n          mb: 6,\n          children: \"Security experts dedicated to protecting your digital assets\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 437,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 4,\n          children: teamMembers.map((member, index) => /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              sx: {\n                textAlign: 'center',\n                height: '100%'\n              },\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                sx: {\n                  p: 3\n                },\n                children: [/*#__PURE__*/_jsxDEV(Avatar, {\n                  sx: {\n                    width: 100,\n                    height: 100,\n                    mx: 'auto',\n                    mb: 2,\n                    bgcolor: 'primary.main',\n                    fontSize: '2rem'\n                  },\n                  children: member.name.split(' ').map(n => n[0]).join('')\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 446,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  gutterBottom: true,\n                  fontWeight: \"600\",\n                  children: member.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 458,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"subtitle2\",\n                  color: \"primary\",\n                  gutterBottom: true,\n                  children: member.role\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 461,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: member.bio\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 464,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 445,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 444,\n              columnNumber: 15\n            }, this)\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 443,\n            columnNumber: 13\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 441,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 433,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 4,\n        mb: 8,\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 6,\n          children: /*#__PURE__*/_jsxDEV(Paper, {\n            sx: {\n              p: 4,\n              height: '100%'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h4\",\n              gutterBottom: true,\n              fontWeight: \"bold\",\n              children: \"Security & Compliance\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 478,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              color: \"text.secondary\",\n              mb: 3,\n              children: \"We maintain the highest security standards and compliance certifications to protect your data and ensure regulatory compliance.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 481,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              container: true,\n              spacing: 1,\n              children: certifications.map((cert, index) => /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                children: /*#__PURE__*/_jsxDEV(Chip, {\n                  label: cert,\n                  color: \"primary\",\n                  variant: \"outlined\",\n                  icon: /*#__PURE__*/_jsxDEV(VerifiedIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 492,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 488,\n                  columnNumber: 19\n                }, this)\n              }, index, false, {\n                fileName: _jsxFileName,\n                lineNumber: 487,\n                columnNumber: 17\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 485,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 477,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 476,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 6,\n          children: /*#__PURE__*/_jsxDEV(Paper, {\n            sx: {\n              p: 4,\n              height: '100%'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h4\",\n              gutterBottom: true,\n              fontWeight: \"bold\",\n              children: \"Our Mission\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 502,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              color: \"text.secondary\",\n              mb: 3,\n              children: \"To democratize advanced cybersecurity through AI, making enterprise-grade threat detection accessible to organizations of all sizes.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 505,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(List, {\n              children: [/*#__PURE__*/_jsxDEV(ListItem, {\n                children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                  children: /*#__PURE__*/_jsxDEV(CheckIcon, {\n                    color: \"primary\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 512,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 511,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Protect digital assets from evolving threats\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 514,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 510,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n                children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                  children: /*#__PURE__*/_jsxDEV(CheckIcon, {\n                    color: \"primary\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 518,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 517,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Provide actionable security insights\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 520,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 516,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n                children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                  children: /*#__PURE__*/_jsxDEV(CheckIcon, {\n                    color: \"primary\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 524,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 523,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Enable proactive threat prevention\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 526,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 522,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 509,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 501,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 500,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 475,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 6,\n          textAlign: 'center',\n          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n          color: 'white'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h3\",\n          gutterBottom: true,\n          fontWeight: \"bold\",\n          children: \"Ready to Secure Your Organization?\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 535,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          mb: 4,\n          sx: {\n            opacity: 0.9\n          },\n          children: \"Join thousands of organizations that trust AI Security Guard\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 538,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(Stack, {\n          direction: \"row\",\n          spacing: 2,\n          justifyContent: \"center\",\n          flexWrap: \"wrap\",\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            size: \"large\",\n            sx: {\n              bgcolor: 'white',\n              color: 'primary.main',\n              '&:hover': {\n                bgcolor: 'grey.100'\n              }\n            },\n            children: \"Start Free Trial\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 542,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outlined\",\n            size: \"large\",\n            sx: {\n              borderColor: 'white',\n              color: 'white',\n              '&:hover': {\n                borderColor: 'white',\n                bgcolor: 'rgba(255,255,255,0.1)'\n              }\n            },\n            children: \"Contact Sales\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 553,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 541,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 534,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 122,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 112,\n    columnNumber: 5\n  }, this);\n});\n_c2 = AboutPage;\nAboutPage.displayName = 'AboutPage';\nexport default AboutPage;\nvar _c, _c2;\n$RefreshReg$(_c, \"AboutPage$React.memo\");\n$RefreshReg$(_c2, \"AboutPage\");", "map": {"version": 3, "names": ["React", "Container", "Grid", "Box", "Typography", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Avatar", "Chip", "<PERSON><PERSON>", "<PERSON><PERSON>", "Paper", "List", "ListItem", "ListItemIcon", "ListItemText", "Security", "SecurityIcon", "Speed", "SpeedIcon", "Shield", "ShieldIcon", "Analytics", "AnalyticsIcon", "CloudDone", "CloudIcon", "Verified", "VerifiedIcon", "CheckCircle", "CheckIcon", "Star", "StarIcon", "TrendingUp", "TrendingUpIcon", "Group", "GroupIcon", "Email", "EmailIcon", "Phone", "PhoneIcon", "jsxDEV", "_jsxDEV", "AboutPage", "memo", "_c", "features", "icon", "sx", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "title", "description", "teamMembers", "name", "role", "avatar", "bio", "stats", "number", "label", "certifications", "width", "minHeight", "display", "flexDirection", "justifyContent", "alignItems", "children", "max<PERSON><PERSON><PERSON>", "py", "textAlign", "mb", "background", "theme", "palette", "mode", "borderRadius", "px", "position", "overflow", "content", "top", "left", "right", "bottom", "transform", "animation", "height", "mx", "boxShadow", "border", "borderColor", "opacity", "color", "variant", "component", "gutterBottom", "fontWeight", "backgroundClip", "WebkitBackgroundClip", "WebkitTextFillColor", "lineHeight", "direction", "xs", "sm", "spacing", "size", "startIcon", "min<PERSON><PERSON><PERSON>", "borderWidth", "container", "map", "stat", "index", "item", "md", "divider", "transition", "p", "feature", "lg", "<PERSON><PERSON>ilter", "member", "bgcolor", "split", "n", "join", "cert", "primary", "flexWrap", "_c2", "displayName", "$RefreshReg$"], "sources": ["E:/PythonProjects/React.js/Smart AI Assisstant/src/pages/AboutPage.jsx"], "sourcesContent": ["import React from 'react';\nimport {\n  Container,\n  Grid,\n  Box,\n  Typography,\n  Card,\n  CardContent,\n  Avatar,\n  Chip,\n  Button,\n  Stack,\n  Paper,\n  List,\n  ListItem,\n  ListItemIcon,\n  ListItemText,\n} from '@mui/material';\nimport {\n  Security as SecurityIcon,\n  Speed as SpeedIcon,\n  Shield as ShieldIcon,\n  Analytics as AnalyticsIcon,\n  CloudDone as CloudIcon,\n  Verified as VerifiedIcon,\n  CheckCircle as CheckIcon,\n  Star as StarIcon,\n  TrendingUp as TrendingUpIcon,\n  Group as GroupIcon,\n  Email as EmailIcon,\n  Phone as PhoneIcon,\n} from '@mui/icons-material';\n\nconst AboutPage = React.memo(() => {\n  const features = [\n    {\n      icon: <SecurityIcon sx={{ fontSize: 40 }} />,\n      title: 'Advanced Threat Detection',\n      description: 'AI-powered scanning engine that identifies malware, phishing, and security threats with 99.9% accuracy.',\n    },\n    {\n      icon: <SpeedIcon sx={{ fontSize: 40 }} />,\n      title: 'Real-time Analysis',\n      description: 'Lightning-fast scanning with results delivered in seconds, not minutes.',\n    },\n    {\n      icon: <ShieldIcon sx={{ fontSize: 40 }} />,\n      title: 'Enterprise Security',\n      description: 'SOC 2 compliant with enterprise-grade security and data protection.',\n    },\n    {\n      icon: <AnalyticsIcon sx={{ fontSize: 40 }} />,\n      title: 'Detailed Reports',\n      description: 'Comprehensive analysis reports with actionable security recommendations.',\n    },\n    {\n      icon: <CloudIcon sx={{ fontSize: 40 }} />,\n      title: 'Cloud-based',\n      description: 'Scalable cloud infrastructure ensuring 99.9% uptime and global accessibility.',\n    },\n    {\n      icon: <VerifiedIcon sx={{ fontSize: 40 }} />,\n      title: 'Trusted Platform',\n      description: 'Used by 10,000+ organizations worldwide for critical security operations.',\n    },\n  ];\n\n  const teamMembers = [\n    {\n      name: 'Dr. Sarah Chen',\n      role: 'Chief Security Officer',\n      avatar: '/api/placeholder/150/150',\n      bio: '15+ years in cybersecurity, former NSA researcher',\n    },\n    {\n      name: 'Michael Rodriguez',\n      role: 'Lead AI Engineer',\n      avatar: '/api/placeholder/150/150',\n      bio: 'PhD in Machine Learning, AI security specialist',\n    },\n    {\n      name: 'Emily Johnson',\n      role: 'Product Manager',\n      avatar: '/api/placeholder/150/150',\n      bio: 'Product strategy expert with enterprise security focus',\n    },\n    {\n      name: 'David Kim',\n      role: 'Security Architect',\n      avatar: '/api/placeholder/150/150',\n      bio: 'Cloud security expert, certified ethical hacker',\n    },\n  ];\n\n  const stats = [\n    { number: '10M+', label: 'Files Scanned', icon: <SecurityIcon /> },\n    { number: '50K+', label: 'Threats Blocked', icon: <ShieldIcon /> },\n    { number: '99.9%', label: 'Uptime', icon: <TrendingUpIcon /> },\n    { number: '10K+', label: 'Organizations', icon: <GroupIcon /> },\n  ];\n\n  const certifications = [\n    'SOC 2 Type II',\n    'ISO 27001',\n    'GDPR Compliant',\n    'HIPAA Ready',\n    'FedRAMP Authorized',\n    'PCI DSS Level 1',\n  ];\n\n  return (\n    <Box\n      sx={{\n        width: '100%',\n        minHeight: '100vh',\n        display: 'flex',\n        flexDirection: 'column',\n        justifyContent: 'center',\n        alignItems: 'center',\n      }}\n    >\n      <Container maxWidth=\"xl\" sx={{ py: 8 }}>\n        {/* Hero Section */}\n        <Box\n          textAlign=\"center\"\n          mb={12}\n          sx={{\n            background: (theme) => theme.palette.mode === 'dark'\n              ? 'linear-gradient(135deg, rgba(25, 118, 210, 0.1) 0%, rgba(156, 39, 176, 0.1) 100%)'\n              : 'linear-gradient(135deg, rgba(25, 118, 210, 0.05) 0%, rgba(156, 39, 176, 0.05) 100%)',\n            borderRadius: 4,\n            py: 8,\n            px: 4,\n            position: 'relative',\n            overflow: 'hidden',\n            '&::before': {\n              content: '\"\"',\n              position: 'absolute',\n              top: 0,\n              left: 0,\n              right: 0,\n              bottom: 0,\n              background: 'linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%)',\n              transform: 'translateX(-100%)',\n              animation: 'shimmer 3s infinite',\n            },\n            '@keyframes shimmer': {\n              '0%': { transform: 'translateX(-100%)' },\n              '100%': { transform: 'translateX(100%)' },\n            },\n          }}\n        >\n          <Box\n            sx={{\n              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n              borderRadius: '50%',\n              width: 120,\n              height: 120,\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              mx: 'auto',\n              mb: 4,\n              boxShadow: '0 20px 40px rgba(102, 126, 234, 0.3)',\n              position: 'relative',\n              '&::after': {\n                content: '\"\"',\n                position: 'absolute',\n                top: -10,\n                left: -10,\n                right: -10,\n                bottom: -10,\n                borderRadius: '50%',\n                border: '2px solid',\n                borderColor: 'primary.main',\n                opacity: 0.3,\n                animation: 'pulse 2s infinite',\n              },\n              '@keyframes pulse': {\n                '0%': { transform: 'scale(1)', opacity: 0.3 },\n                '50%': { transform: 'scale(1.1)', opacity: 0.1 },\n                '100%': { transform: 'scale(1)', opacity: 0.3 },\n              },\n            }}\n          >\n            <SecurityIcon sx={{ fontSize: 60, color: 'white' }} />\n          </Box>\n\n          <Typography\n            variant=\"h1\"\n            component=\"h1\"\n            gutterBottom\n            fontWeight=\"bold\"\n            sx={{\n              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n              backgroundClip: 'text',\n              WebkitBackgroundClip: 'text',\n              WebkitTextFillColor: 'transparent',\n              mb: 3,\n            }}\n          >\n            About AI Security Guard\n          </Typography>\n\n          <Typography\n            variant=\"h4\"\n            color=\"text.secondary\"\n            maxWidth=\"900px\"\n            mx=\"auto\"\n            mb={6}\n            sx={{\n              lineHeight: 1.6,\n              fontWeight: 300,\n            }}\n          >\n            We're on a mission to make the digital world safer through advanced AI-powered\n            security solutions. Our platform protects organizations worldwide from cyber threats.\n          </Typography>\n\n          <Stack\n            direction={{ xs: 'column', sm: 'row' }}\n            spacing={3}\n            justifyContent=\"center\"\n            alignItems=\"center\"\n          >\n            <Button\n              variant=\"contained\"\n              size=\"large\"\n              startIcon={<EmailIcon />}\n              sx={{\n                px: 4,\n                py: 2,\n                borderRadius: 3,\n                fontSize: '1.1rem',\n                minWidth: 200,\n                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                '&:hover': {\n                  background: 'linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)',\n                  transform: 'translateY(-2px)',\n                  boxShadow: '0 8px 25px rgba(102, 126, 234, 0.4)',\n                },\n              }}\n            >\n              Contact Us\n            </Button>\n            <Button\n              variant=\"outlined\"\n              size=\"large\"\n              startIcon={<PhoneIcon />}\n              sx={{\n                px: 4,\n                py: 2,\n                borderRadius: 3,\n                fontSize: '1.1rem',\n                minWidth: 200,\n                borderWidth: 2,\n                '&:hover': {\n                  borderWidth: 2,\n                  transform: 'translateY(-2px)',\n                  boxShadow: '0 8px 25px rgba(102, 126, 234, 0.2)',\n                },\n              }}\n            >\n              Schedule Demo\n            </Button>\n          </Stack>\n        </Box>\n\n        {/* Stats Section */}\n        <Box mb={12}>\n          <Typography\n            variant=\"h3\"\n            component=\"h2\"\n            textAlign=\"center\"\n            gutterBottom\n            fontWeight=\"bold\"\n            mb={6}\n          >\n            Trusted by Industry Leaders\n          </Typography>\n\n          <Grid container spacing={4} justifyContent=\"center\">\n            {stats.map((stat, index) => (\n              <Grid item xs={6} md={3} key={index}>\n                <Card\n                  sx={{\n                    textAlign: 'center',\n                    height: '100%',\n                    background: (theme) => theme.palette.mode === 'dark'\n                      ? 'linear-gradient(135deg, rgba(25, 118, 210, 0.1) 0%, rgba(156, 39, 176, 0.1) 100%)'\n                      : 'linear-gradient(135deg, rgba(25, 118, 210, 0.05) 0%, rgba(156, 39, 176, 0.05) 100%)',\n                    border: (theme) => `1px solid ${theme.palette.divider}`,\n                    transition: 'all 0.3s ease',\n                    '&:hover': {\n                      transform: 'translateY(-8px)',\n                      boxShadow: '0 20px 40px rgba(0,0,0,0.1)',\n                    },\n                  }}\n                >\n                  <CardContent sx={{ p: 4 }}>\n                    <Box\n                      sx={{\n                        color: 'primary.main',\n                        mb: 2,\n                        display: 'flex',\n                        justifyContent: 'center',\n                        alignItems: 'center',\n                        width: 60,\n                        height: 60,\n                        borderRadius: '50%',\n                        background: 'linear-gradient(135deg, rgba(25, 118, 210, 0.1) 0%, rgba(156, 39, 176, 0.1) 100%)',\n                        mx: 'auto',\n                      }}\n                    >\n                      {stat.icon}\n                    </Box>\n                    <Typography\n                      variant=\"h2\"\n                      fontWeight=\"bold\"\n                      color=\"primary\"\n                      sx={{ mb: 1 }}\n                    >\n                      {stat.number}\n                    </Typography>\n                    <Typography\n                      variant=\"h6\"\n                      color=\"text.secondary\"\n                      fontWeight=\"500\"\n                    >\n                      {stat.label}\n                    </Typography>\n                  </CardContent>\n                </Card>\n              </Grid>\n            ))}\n          </Grid>\n        </Box>\n\n        {/* Features Section */}\n        <Box mb={12}>\n          <Box textAlign=\"center\" mb={8}>\n            <Typography\n              variant=\"h3\"\n              component=\"h2\"\n              gutterBottom\n              fontWeight=\"bold\"\n              sx={{ mb: 3 }}\n            >\n              Why Choose AI Security Guard?\n            </Typography>\n            <Typography\n              variant=\"h5\"\n              color=\"text.secondary\"\n              maxWidth=\"700px\"\n              mx=\"auto\"\n              sx={{ lineHeight: 1.6, fontWeight: 300 }}\n            >\n              Advanced features designed for modern security challenges\n            </Typography>\n          </Box>\n\n          <Grid container spacing={4} justifyContent=\"center\">\n            {features.map((feature, index) => (\n              <Grid item xs={12} md={6} lg={4} key={index}>\n                <Card\n                  sx={{\n                    height: '100%',\n                    transition: 'all 0.3s ease',\n                    background: (theme) => theme.palette.mode === 'dark'\n                      ? 'linear-gradient(135deg, rgba(30, 30, 30, 0.8) 0%, rgba(50, 50, 50, 0.8) 100%)'\n                      : 'linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.9) 100%)',\n                    backdropFilter: 'blur(10px)',\n                    border: (theme) => `1px solid ${theme.palette.divider}`,\n                    '&:hover': {\n                      transform: 'translateY(-8px) scale(1.02)',\n                      boxShadow: '0 20px 40px rgba(0,0,0,0.15)',\n                    },\n                  }}\n                >\n                  <CardContent sx={{ textAlign: 'center', p: 4 }}>\n                    <Box\n                      sx={{\n                        color: 'primary.main',\n                        mb: 3,\n                        display: 'flex',\n                        justifyContent: 'center',\n                        alignItems: 'center',\n                        width: 80,\n                        height: 80,\n                        borderRadius: '50%',\n                        background: 'linear-gradient(135deg, rgba(25, 118, 210, 0.1) 0%, rgba(156, 39, 176, 0.1) 100%)',\n                        mx: 'auto',\n                        position: 'relative',\n                        '&::after': {\n                          content: '\"\"',\n                          position: 'absolute',\n                          top: -5,\n                          left: -5,\n                          right: -5,\n                          bottom: -5,\n                          borderRadius: '50%',\n                          border: '2px solid',\n                          borderColor: 'primary.main',\n                          opacity: 0.2,\n                        },\n                      }}\n                    >\n                      {feature.icon}\n                    </Box>\n                    <Typography\n                      variant=\"h5\"\n                      gutterBottom\n                      fontWeight=\"600\"\n                      sx={{ mb: 2 }}\n                    >\n                      {feature.title}\n                    </Typography>\n                    <Typography\n                      variant=\"body1\"\n                      color=\"text.secondary\"\n                      sx={{ lineHeight: 1.7 }}\n                    >\n                      {feature.description}\n                    </Typography>\n                  </CardContent>\n                </Card>\n              </Grid>\n            ))}\n          </Grid>\n        </Box>\n\n      {/* Team Section */}\n      <Box mb={8}>\n        <Typography variant=\"h3\" component=\"h2\" textAlign=\"center\" gutterBottom fontWeight=\"bold\">\n          Meet Our Team\n        </Typography>\n        <Typography variant=\"h6\" color=\"text.secondary\" textAlign=\"center\" mb={6}>\n          Security experts dedicated to protecting your digital assets\n        </Typography>\n        \n        <Grid container spacing={4}>\n          {teamMembers.map((member, index) => (\n            <Grid item xs={12} sm={6} md={3} key={index}>\n              <Card sx={{ textAlign: 'center', height: '100%' }}>\n                <CardContent sx={{ p: 3 }}>\n                  <Avatar\n                    sx={{ \n                      width: 100, \n                      height: 100, \n                      mx: 'auto', \n                      mb: 2,\n                      bgcolor: 'primary.main',\n                      fontSize: '2rem'\n                    }}\n                  >\n                    {member.name.split(' ').map(n => n[0]).join('')}\n                  </Avatar>\n                  <Typography variant=\"h6\" gutterBottom fontWeight=\"600\">\n                    {member.name}\n                  </Typography>\n                  <Typography variant=\"subtitle2\" color=\"primary\" gutterBottom>\n                    {member.role}\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    {member.bio}\n                  </Typography>\n                </CardContent>\n              </Card>\n            </Grid>\n          ))}\n        </Grid>\n      </Box>\n\n      {/* Certifications & Compliance */}\n      <Grid container spacing={4} mb={8}>\n        <Grid item xs={12} md={6}>\n          <Paper sx={{ p: 4, height: '100%' }}>\n            <Typography variant=\"h4\" gutterBottom fontWeight=\"bold\">\n              Security & Compliance\n            </Typography>\n            <Typography variant=\"body1\" color=\"text.secondary\" mb={3}>\n              We maintain the highest security standards and compliance certifications \n              to protect your data and ensure regulatory compliance.\n            </Typography>\n            <Grid container spacing={1}>\n              {certifications.map((cert, index) => (\n                <Grid item key={index}>\n                  <Chip\n                    label={cert}\n                    color=\"primary\"\n                    variant=\"outlined\"\n                    icon={<VerifiedIcon />}\n                  />\n                </Grid>\n              ))}\n            </Grid>\n          </Paper>\n        </Grid>\n\n        <Grid item xs={12} md={6}>\n          <Paper sx={{ p: 4, height: '100%' }}>\n            <Typography variant=\"h4\" gutterBottom fontWeight=\"bold\">\n              Our Mission\n            </Typography>\n            <Typography variant=\"body1\" color=\"text.secondary\" mb={3}>\n              To democratize advanced cybersecurity through AI, making enterprise-grade \n              threat detection accessible to organizations of all sizes.\n            </Typography>\n            <List>\n              <ListItem>\n                <ListItemIcon>\n                  <CheckIcon color=\"primary\" />\n                </ListItemIcon>\n                <ListItemText primary=\"Protect digital assets from evolving threats\" />\n              </ListItem>\n              <ListItem>\n                <ListItemIcon>\n                  <CheckIcon color=\"primary\" />\n                </ListItemIcon>\n                <ListItemText primary=\"Provide actionable security insights\" />\n              </ListItem>\n              <ListItem>\n                <ListItemIcon>\n                  <CheckIcon color=\"primary\" />\n                </ListItemIcon>\n                <ListItemText primary=\"Enable proactive threat prevention\" />\n              </ListItem>\n            </List>\n          </Paper>\n        </Grid>\n      </Grid>\n\n      {/* Contact Section */}\n      <Paper sx={{ p: 6, textAlign: 'center', background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)', color: 'white' }}>\n        <Typography variant=\"h3\" gutterBottom fontWeight=\"bold\">\n          Ready to Secure Your Organization?\n        </Typography>\n        <Typography variant=\"h6\" mb={4} sx={{ opacity: 0.9 }}>\n          Join thousands of organizations that trust AI Security Guard\n        </Typography>\n        <Stack direction=\"row\" spacing={2} justifyContent=\"center\" flexWrap=\"wrap\">\n          <Button \n            variant=\"contained\" \n            size=\"large\" \n            sx={{ \n              bgcolor: 'white', \n              color: 'primary.main',\n              '&:hover': { bgcolor: 'grey.100' }\n            }}\n          >\n            Start Free Trial\n          </Button>\n          <Button \n            variant=\"outlined\" \n            size=\"large\"\n            sx={{ \n              borderColor: 'white', \n              color: 'white',\n              '&:hover': { borderColor: 'white', bgcolor: 'rgba(255,255,255,0.1)' }\n            }}\n          >\n            Contact Sales\n          </Button>\n        </Stack>\n      </Paper>\n      </Container>\n    </Box>\n  );\n});\n\nAboutPage.displayName = 'AboutPage';\n\nexport default AboutPage;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SACEC,SAAS,EACTC,IAAI,EACJC,GAAG,EACHC,UAAU,EACVC,IAAI,EACJC,WAAW,EACXC,MAAM,EACNC,IAAI,EACJC,MAAM,EACNC,KAAK,EACLC,KAAK,EACLC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,YAAY,QACP,eAAe;AACtB,SACEC,QAAQ,IAAIC,YAAY,EACxBC,KAAK,IAAIC,SAAS,EAClBC,MAAM,IAAIC,UAAU,EACpBC,SAAS,IAAIC,aAAa,EAC1BC,SAAS,IAAIC,SAAS,EACtBC,QAAQ,IAAIC,YAAY,EACxBC,WAAW,IAAIC,SAAS,EACxBC,IAAI,IAAIC,QAAQ,EAChBC,UAAU,IAAIC,cAAc,EAC5BC,KAAK,IAAIC,SAAS,EAClBC,KAAK,IAAIC,SAAS,EAClBC,KAAK,IAAIC,SAAS,QACb,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7B,MAAMC,SAAS,gBAAG1C,KAAK,CAAC2C,IAAI,CAAAC,EAAA,GAACA,CAAA,KAAM;EACjC,MAAMC,QAAQ,GAAG,CACf;IACEC,IAAI,eAAEL,OAAA,CAACxB,YAAY;MAAC8B,EAAE,EAAE;QAAEC,QAAQ,EAAE;MAAG;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC5CC,KAAK,EAAE,2BAA2B;IAClCC,WAAW,EAAE;EACf,CAAC,EACD;IACER,IAAI,eAAEL,OAAA,CAACtB,SAAS;MAAC4B,EAAE,EAAE;QAAEC,QAAQ,EAAE;MAAG;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACzCC,KAAK,EAAE,oBAAoB;IAC3BC,WAAW,EAAE;EACf,CAAC,EACD;IACER,IAAI,eAAEL,OAAA,CAACpB,UAAU;MAAC0B,EAAE,EAAE;QAAEC,QAAQ,EAAE;MAAG;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC1CC,KAAK,EAAE,qBAAqB;IAC5BC,WAAW,EAAE;EACf,CAAC,EACD;IACER,IAAI,eAAEL,OAAA,CAAClB,aAAa;MAACwB,EAAE,EAAE;QAAEC,QAAQ,EAAE;MAAG;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC7CC,KAAK,EAAE,kBAAkB;IACzBC,WAAW,EAAE;EACf,CAAC,EACD;IACER,IAAI,eAAEL,OAAA,CAAChB,SAAS;MAACsB,EAAE,EAAE;QAAEC,QAAQ,EAAE;MAAG;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACzCC,KAAK,EAAE,aAAa;IACpBC,WAAW,EAAE;EACf,CAAC,EACD;IACER,IAAI,eAAEL,OAAA,CAACd,YAAY;MAACoB,EAAE,EAAE;QAAEC,QAAQ,EAAE;MAAG;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC5CC,KAAK,EAAE,kBAAkB;IACzBC,WAAW,EAAE;EACf,CAAC,CACF;EAED,MAAMC,WAAW,GAAG,CAClB;IACEC,IAAI,EAAE,gBAAgB;IACtBC,IAAI,EAAE,wBAAwB;IAC9BC,MAAM,EAAE,0BAA0B;IAClCC,GAAG,EAAE;EACP,CAAC,EACD;IACEH,IAAI,EAAE,mBAAmB;IACzBC,IAAI,EAAE,kBAAkB;IACxBC,MAAM,EAAE,0BAA0B;IAClCC,GAAG,EAAE;EACP,CAAC,EACD;IACEH,IAAI,EAAE,eAAe;IACrBC,IAAI,EAAE,iBAAiB;IACvBC,MAAM,EAAE,0BAA0B;IAClCC,GAAG,EAAE;EACP,CAAC,EACD;IACEH,IAAI,EAAE,WAAW;IACjBC,IAAI,EAAE,oBAAoB;IAC1BC,MAAM,EAAE,0BAA0B;IAClCC,GAAG,EAAE;EACP,CAAC,CACF;EAED,MAAMC,KAAK,GAAG,CACZ;IAAEC,MAAM,EAAE,MAAM;IAAEC,KAAK,EAAE,eAAe;IAAEhB,IAAI,eAAEL,OAAA,CAACxB,YAAY;MAAAgC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE,CAAC,EAClE;IAAES,MAAM,EAAE,MAAM;IAAEC,KAAK,EAAE,iBAAiB;IAAEhB,IAAI,eAAEL,OAAA,CAACpB,UAAU;MAAA4B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE,CAAC,EAClE;IAAES,MAAM,EAAE,OAAO;IAAEC,KAAK,EAAE,QAAQ;IAAEhB,IAAI,eAAEL,OAAA,CAACR,cAAc;MAAAgB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE,CAAC,EAC9D;IAAES,MAAM,EAAE,MAAM;IAAEC,KAAK,EAAE,eAAe;IAAEhB,IAAI,eAAEL,OAAA,CAACN,SAAS;MAAAc,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE,CAAC,CAChE;EAED,MAAMW,cAAc,GAAG,CACrB,eAAe,EACf,WAAW,EACX,gBAAgB,EAChB,aAAa,EACb,oBAAoB,EACpB,iBAAiB,CAClB;EAED,oBACEtB,OAAA,CAACtC,GAAG;IACF4C,EAAE,EAAE;MACFiB,KAAK,EAAE,MAAM;MACbC,SAAS,EAAE,OAAO;MAClBC,OAAO,EAAE,MAAM;MACfC,aAAa,EAAE,QAAQ;MACvBC,cAAc,EAAE,QAAQ;MACxBC,UAAU,EAAE;IACd,CAAE;IAAAC,QAAA,eAEF7B,OAAA,CAACxC,SAAS;MAACsE,QAAQ,EAAC,IAAI;MAACxB,EAAE,EAAE;QAAEyB,EAAE,EAAE;MAAE,CAAE;MAAAF,QAAA,gBAErC7B,OAAA,CAACtC,GAAG;QACFsE,SAAS,EAAC,QAAQ;QAClBC,EAAE,EAAE,EAAG;QACP3B,EAAE,EAAE;UACF4B,UAAU,EAAGC,KAAK,IAAKA,KAAK,CAACC,OAAO,CAACC,IAAI,KAAK,MAAM,GAChD,mFAAmF,GACnF,qFAAqF;UACzFC,YAAY,EAAE,CAAC;UACfP,EAAE,EAAE,CAAC;UACLQ,EAAE,EAAE,CAAC;UACLC,QAAQ,EAAE,UAAU;UACpBC,QAAQ,EAAE,QAAQ;UAClB,WAAW,EAAE;YACXC,OAAO,EAAE,IAAI;YACbF,QAAQ,EAAE,UAAU;YACpBG,GAAG,EAAE,CAAC;YACNC,IAAI,EAAE,CAAC;YACPC,KAAK,EAAE,CAAC;YACRC,MAAM,EAAE,CAAC;YACTZ,UAAU,EAAE,qFAAqF;YACjGa,SAAS,EAAE,mBAAmB;YAC9BC,SAAS,EAAE;UACb,CAAC;UACD,oBAAoB,EAAE;YACpB,IAAI,EAAE;cAAED,SAAS,EAAE;YAAoB,CAAC;YACxC,MAAM,EAAE;cAAEA,SAAS,EAAE;YAAmB;UAC1C;QACF,CAAE;QAAAlB,QAAA,gBAEF7B,OAAA,CAACtC,GAAG;UACF4C,EAAE,EAAE;YACF4B,UAAU,EAAE,mDAAmD;YAC/DI,YAAY,EAAE,KAAK;YACnBf,KAAK,EAAE,GAAG;YACV0B,MAAM,EAAE,GAAG;YACXxB,OAAO,EAAE,MAAM;YACfG,UAAU,EAAE,QAAQ;YACpBD,cAAc,EAAE,QAAQ;YACxBuB,EAAE,EAAE,MAAM;YACVjB,EAAE,EAAE,CAAC;YACLkB,SAAS,EAAE,sCAAsC;YACjDX,QAAQ,EAAE,UAAU;YACpB,UAAU,EAAE;cACVE,OAAO,EAAE,IAAI;cACbF,QAAQ,EAAE,UAAU;cACpBG,GAAG,EAAE,CAAC,EAAE;cACRC,IAAI,EAAE,CAAC,EAAE;cACTC,KAAK,EAAE,CAAC,EAAE;cACVC,MAAM,EAAE,CAAC,EAAE;cACXR,YAAY,EAAE,KAAK;cACnBc,MAAM,EAAE,WAAW;cACnBC,WAAW,EAAE,cAAc;cAC3BC,OAAO,EAAE,GAAG;cACZN,SAAS,EAAE;YACb,CAAC;YACD,kBAAkB,EAAE;cAClB,IAAI,EAAE;gBAAED,SAAS,EAAE,UAAU;gBAAEO,OAAO,EAAE;cAAI,CAAC;cAC7C,KAAK,EAAE;gBAAEP,SAAS,EAAE,YAAY;gBAAEO,OAAO,EAAE;cAAI,CAAC;cAChD,MAAM,EAAE;gBAAEP,SAAS,EAAE,UAAU;gBAAEO,OAAO,EAAE;cAAI;YAChD;UACF,CAAE;UAAAzB,QAAA,eAEF7B,OAAA,CAACxB,YAAY;YAAC8B,EAAE,EAAE;cAAEC,QAAQ,EAAE,EAAE;cAAEgD,KAAK,EAAE;YAAQ;UAAE;YAAA/C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnD,CAAC,eAENX,OAAA,CAACrC,UAAU;UACT6F,OAAO,EAAC,IAAI;UACZC,SAAS,EAAC,IAAI;UACdC,YAAY;UACZC,UAAU,EAAC,MAAM;UACjBrD,EAAE,EAAE;YACF4B,UAAU,EAAE,mDAAmD;YAC/D0B,cAAc,EAAE,MAAM;YACtBC,oBAAoB,EAAE,MAAM;YAC5BC,mBAAmB,EAAE,aAAa;YAClC7B,EAAE,EAAE;UACN,CAAE;UAAAJ,QAAA,EACH;QAED;UAAArB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAEbX,OAAA,CAACrC,UAAU;UACT6F,OAAO,EAAC,IAAI;UACZD,KAAK,EAAC,gBAAgB;UACtBzB,QAAQ,EAAC,OAAO;UAChBoB,EAAE,EAAC,MAAM;UACTjB,EAAE,EAAE,CAAE;UACN3B,EAAE,EAAE;YACFyD,UAAU,EAAE,GAAG;YACfJ,UAAU,EAAE;UACd,CAAE;UAAA9B,QAAA,EACH;QAGD;UAAArB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAEbX,OAAA,CAAC/B,KAAK;UACJ+F,SAAS,EAAE;YAAEC,EAAE,EAAE,QAAQ;YAAEC,EAAE,EAAE;UAAM,CAAE;UACvCC,OAAO,EAAE,CAAE;UACXxC,cAAc,EAAC,QAAQ;UACvBC,UAAU,EAAC,QAAQ;UAAAC,QAAA,gBAEnB7B,OAAA,CAAChC,MAAM;YACLwF,OAAO,EAAC,WAAW;YACnBY,IAAI,EAAC,OAAO;YACZC,SAAS,eAAErE,OAAA,CAACJ,SAAS;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACzBL,EAAE,EAAE;cACFiC,EAAE,EAAE,CAAC;cACLR,EAAE,EAAE,CAAC;cACLO,YAAY,EAAE,CAAC;cACf/B,QAAQ,EAAE,QAAQ;cAClB+D,QAAQ,EAAE,GAAG;cACbpC,UAAU,EAAE,mDAAmD;cAC/D,SAAS,EAAE;gBACTA,UAAU,EAAE,mDAAmD;gBAC/Da,SAAS,EAAE,kBAAkB;gBAC7BI,SAAS,EAAE;cACb;YACF,CAAE;YAAAtB,QAAA,EACH;UAED;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTX,OAAA,CAAChC,MAAM;YACLwF,OAAO,EAAC,UAAU;YAClBY,IAAI,EAAC,OAAO;YACZC,SAAS,eAAErE,OAAA,CAACF,SAAS;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACzBL,EAAE,EAAE;cACFiC,EAAE,EAAE,CAAC;cACLR,EAAE,EAAE,CAAC;cACLO,YAAY,EAAE,CAAC;cACf/B,QAAQ,EAAE,QAAQ;cAClB+D,QAAQ,EAAE,GAAG;cACbC,WAAW,EAAE,CAAC;cACd,SAAS,EAAE;gBACTA,WAAW,EAAE,CAAC;gBACdxB,SAAS,EAAE,kBAAkB;gBAC7BI,SAAS,EAAE;cACb;YACF,CAAE;YAAAtB,QAAA,EACH;UAED;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAGNX,OAAA,CAACtC,GAAG;QAACuE,EAAE,EAAE,EAAG;QAAAJ,QAAA,gBACV7B,OAAA,CAACrC,UAAU;UACT6F,OAAO,EAAC,IAAI;UACZC,SAAS,EAAC,IAAI;UACdzB,SAAS,EAAC,QAAQ;UAClB0B,YAAY;UACZC,UAAU,EAAC,MAAM;UACjB1B,EAAE,EAAE,CAAE;UAAAJ,QAAA,EACP;QAED;UAAArB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAEbX,OAAA,CAACvC,IAAI;UAAC+G,SAAS;UAACL,OAAO,EAAE,CAAE;UAACxC,cAAc,EAAC,QAAQ;UAAAE,QAAA,EAChDV,KAAK,CAACsD,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBACrB3E,OAAA,CAACvC,IAAI;YAACmH,IAAI;YAACX,EAAE,EAAE,CAAE;YAACY,EAAE,EAAE,CAAE;YAAAhD,QAAA,eACtB7B,OAAA,CAACpC,IAAI;cACH0C,EAAE,EAAE;gBACF0B,SAAS,EAAE,QAAQ;gBACnBiB,MAAM,EAAE,MAAM;gBACdf,UAAU,EAAGC,KAAK,IAAKA,KAAK,CAACC,OAAO,CAACC,IAAI,KAAK,MAAM,GAChD,mFAAmF,GACnF,qFAAqF;gBACzFe,MAAM,EAAGjB,KAAK,IAAK,aAAaA,KAAK,CAACC,OAAO,CAAC0C,OAAO,EAAE;gBACvDC,UAAU,EAAE,eAAe;gBAC3B,SAAS,EAAE;kBACThC,SAAS,EAAE,kBAAkB;kBAC7BI,SAAS,EAAE;gBACb;cACF,CAAE;cAAAtB,QAAA,eAEF7B,OAAA,CAACnC,WAAW;gBAACyC,EAAE,EAAE;kBAAE0E,CAAC,EAAE;gBAAE,CAAE;gBAAAnD,QAAA,gBACxB7B,OAAA,CAACtC,GAAG;kBACF4C,EAAE,EAAE;oBACFiD,KAAK,EAAE,cAAc;oBACrBtB,EAAE,EAAE,CAAC;oBACLR,OAAO,EAAE,MAAM;oBACfE,cAAc,EAAE,QAAQ;oBACxBC,UAAU,EAAE,QAAQ;oBACpBL,KAAK,EAAE,EAAE;oBACT0B,MAAM,EAAE,EAAE;oBACVX,YAAY,EAAE,KAAK;oBACnBJ,UAAU,EAAE,mFAAmF;oBAC/FgB,EAAE,EAAE;kBACN,CAAE;kBAAArB,QAAA,EAED6C,IAAI,CAACrE;gBAAI;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP,CAAC,eACNX,OAAA,CAACrC,UAAU;kBACT6F,OAAO,EAAC,IAAI;kBACZG,UAAU,EAAC,MAAM;kBACjBJ,KAAK,EAAC,SAAS;kBACfjD,EAAE,EAAE;oBAAE2B,EAAE,EAAE;kBAAE,CAAE;kBAAAJ,QAAA,EAEb6C,IAAI,CAACtD;gBAAM;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC,eACbX,OAAA,CAACrC,UAAU;kBACT6F,OAAO,EAAC,IAAI;kBACZD,KAAK,EAAC,gBAAgB;kBACtBI,UAAU,EAAC,KAAK;kBAAA9B,QAAA,EAEf6C,IAAI,CAACrD;gBAAK;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC,GAjDqBgE,KAAK;YAAAnE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAkD7B,CACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGNX,OAAA,CAACtC,GAAG;QAACuE,EAAE,EAAE,EAAG;QAAAJ,QAAA,gBACV7B,OAAA,CAACtC,GAAG;UAACsE,SAAS,EAAC,QAAQ;UAACC,EAAE,EAAE,CAAE;UAAAJ,QAAA,gBAC5B7B,OAAA,CAACrC,UAAU;YACT6F,OAAO,EAAC,IAAI;YACZC,SAAS,EAAC,IAAI;YACdC,YAAY;YACZC,UAAU,EAAC,MAAM;YACjBrD,EAAE,EAAE;cAAE2B,EAAE,EAAE;YAAE,CAAE;YAAAJ,QAAA,EACf;UAED;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbX,OAAA,CAACrC,UAAU;YACT6F,OAAO,EAAC,IAAI;YACZD,KAAK,EAAC,gBAAgB;YACtBzB,QAAQ,EAAC,OAAO;YAChBoB,EAAE,EAAC,MAAM;YACT5C,EAAE,EAAE;cAAEyD,UAAU,EAAE,GAAG;cAAEJ,UAAU,EAAE;YAAI,CAAE;YAAA9B,QAAA,EAC1C;UAED;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAENX,OAAA,CAACvC,IAAI;UAAC+G,SAAS;UAACL,OAAO,EAAE,CAAE;UAACxC,cAAc,EAAC,QAAQ;UAAAE,QAAA,EAChDzB,QAAQ,CAACqE,GAAG,CAAC,CAACQ,OAAO,EAAEN,KAAK,kBAC3B3E,OAAA,CAACvC,IAAI;YAACmH,IAAI;YAACX,EAAE,EAAE,EAAG;YAACY,EAAE,EAAE,CAAE;YAACK,EAAE,EAAE,CAAE;YAAArD,QAAA,eAC9B7B,OAAA,CAACpC,IAAI;cACH0C,EAAE,EAAE;gBACF2C,MAAM,EAAE,MAAM;gBACd8B,UAAU,EAAE,eAAe;gBAC3B7C,UAAU,EAAGC,KAAK,IAAKA,KAAK,CAACC,OAAO,CAACC,IAAI,KAAK,MAAM,GAChD,+EAA+E,GAC/E,qFAAqF;gBACzF8C,cAAc,EAAE,YAAY;gBAC5B/B,MAAM,EAAGjB,KAAK,IAAK,aAAaA,KAAK,CAACC,OAAO,CAAC0C,OAAO,EAAE;gBACvD,SAAS,EAAE;kBACT/B,SAAS,EAAE,8BAA8B;kBACzCI,SAAS,EAAE;gBACb;cACF,CAAE;cAAAtB,QAAA,eAEF7B,OAAA,CAACnC,WAAW;gBAACyC,EAAE,EAAE;kBAAE0B,SAAS,EAAE,QAAQ;kBAAEgD,CAAC,EAAE;gBAAE,CAAE;gBAAAnD,QAAA,gBAC7C7B,OAAA,CAACtC,GAAG;kBACF4C,EAAE,EAAE;oBACFiD,KAAK,EAAE,cAAc;oBACrBtB,EAAE,EAAE,CAAC;oBACLR,OAAO,EAAE,MAAM;oBACfE,cAAc,EAAE,QAAQ;oBACxBC,UAAU,EAAE,QAAQ;oBACpBL,KAAK,EAAE,EAAE;oBACT0B,MAAM,EAAE,EAAE;oBACVX,YAAY,EAAE,KAAK;oBACnBJ,UAAU,EAAE,mFAAmF;oBAC/FgB,EAAE,EAAE,MAAM;oBACVV,QAAQ,EAAE,UAAU;oBACpB,UAAU,EAAE;sBACVE,OAAO,EAAE,IAAI;sBACbF,QAAQ,EAAE,UAAU;sBACpBG,GAAG,EAAE,CAAC,CAAC;sBACPC,IAAI,EAAE,CAAC,CAAC;sBACRC,KAAK,EAAE,CAAC,CAAC;sBACTC,MAAM,EAAE,CAAC,CAAC;sBACVR,YAAY,EAAE,KAAK;sBACnBc,MAAM,EAAE,WAAW;sBACnBC,WAAW,EAAE,cAAc;sBAC3BC,OAAO,EAAE;oBACX;kBACF,CAAE;kBAAAzB,QAAA,EAEDoD,OAAO,CAAC5E;gBAAI;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,eACNX,OAAA,CAACrC,UAAU;kBACT6F,OAAO,EAAC,IAAI;kBACZE,YAAY;kBACZC,UAAU,EAAC,KAAK;kBAChBrD,EAAE,EAAE;oBAAE2B,EAAE,EAAE;kBAAE,CAAE;kBAAAJ,QAAA,EAEboD,OAAO,CAACrE;gBAAK;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACbX,OAAA,CAACrC,UAAU;kBACT6F,OAAO,EAAC,OAAO;kBACfD,KAAK,EAAC,gBAAgB;kBACtBjD,EAAE,EAAE;oBAAEyD,UAAU,EAAE;kBAAI,CAAE;kBAAAlC,QAAA,EAEvBoD,OAAO,CAACpE;gBAAW;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC,GA9D6BgE,KAAK;YAAAnE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA+DrC,CACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGRX,OAAA,CAACtC,GAAG;QAACuE,EAAE,EAAE,CAAE;QAAAJ,QAAA,gBACT7B,OAAA,CAACrC,UAAU;UAAC6F,OAAO,EAAC,IAAI;UAACC,SAAS,EAAC,IAAI;UAACzB,SAAS,EAAC,QAAQ;UAAC0B,YAAY;UAACC,UAAU,EAAC,MAAM;UAAA9B,QAAA,EAAC;QAE1F;UAAArB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbX,OAAA,CAACrC,UAAU;UAAC6F,OAAO,EAAC,IAAI;UAACD,KAAK,EAAC,gBAAgB;UAACvB,SAAS,EAAC,QAAQ;UAACC,EAAE,EAAE,CAAE;UAAAJ,QAAA,EAAC;QAE1E;UAAArB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAEbX,OAAA,CAACvC,IAAI;UAAC+G,SAAS;UAACL,OAAO,EAAE,CAAE;UAAAtC,QAAA,EACxBf,WAAW,CAAC2D,GAAG,CAAC,CAACW,MAAM,EAAET,KAAK,kBAC7B3E,OAAA,CAACvC,IAAI;YAACmH,IAAI;YAACX,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAACW,EAAE,EAAE,CAAE;YAAAhD,QAAA,eAC9B7B,OAAA,CAACpC,IAAI;cAAC0C,EAAE,EAAE;gBAAE0B,SAAS,EAAE,QAAQ;gBAAEiB,MAAM,EAAE;cAAO,CAAE;cAAApB,QAAA,eAChD7B,OAAA,CAACnC,WAAW;gBAACyC,EAAE,EAAE;kBAAE0E,CAAC,EAAE;gBAAE,CAAE;gBAAAnD,QAAA,gBACxB7B,OAAA,CAAClC,MAAM;kBACLwC,EAAE,EAAE;oBACFiB,KAAK,EAAE,GAAG;oBACV0B,MAAM,EAAE,GAAG;oBACXC,EAAE,EAAE,MAAM;oBACVjB,EAAE,EAAE,CAAC;oBACLoD,OAAO,EAAE,cAAc;oBACvB9E,QAAQ,EAAE;kBACZ,CAAE;kBAAAsB,QAAA,EAEDuD,MAAM,CAACrE,IAAI,CAACuE,KAAK,CAAC,GAAG,CAAC,CAACb,GAAG,CAACc,CAAC,IAAIA,CAAC,CAAC,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,EAAE;gBAAC;kBAAAhF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzC,CAAC,eACTX,OAAA,CAACrC,UAAU;kBAAC6F,OAAO,EAAC,IAAI;kBAACE,YAAY;kBAACC,UAAU,EAAC,KAAK;kBAAA9B,QAAA,EACnDuD,MAAM,CAACrE;gBAAI;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC,eACbX,OAAA,CAACrC,UAAU;kBAAC6F,OAAO,EAAC,WAAW;kBAACD,KAAK,EAAC,SAAS;kBAACG,YAAY;kBAAA7B,QAAA,EACzDuD,MAAM,CAACpE;gBAAI;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC,eACbX,OAAA,CAACrC,UAAU;kBAAC6F,OAAO,EAAC,OAAO;kBAACD,KAAK,EAAC,gBAAgB;kBAAA1B,QAAA,EAC/CuD,MAAM,CAAClE;gBAAG;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC,GAzB6BgE,KAAK;YAAAnE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA0BrC,CACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGNX,OAAA,CAACvC,IAAI;QAAC+G,SAAS;QAACL,OAAO,EAAE,CAAE;QAAClC,EAAE,EAAE,CAAE;QAAAJ,QAAA,gBAChC7B,OAAA,CAACvC,IAAI;UAACmH,IAAI;UAACX,EAAE,EAAE,EAAG;UAACY,EAAE,EAAE,CAAE;UAAAhD,QAAA,eACvB7B,OAAA,CAAC9B,KAAK;YAACoC,EAAE,EAAE;cAAE0E,CAAC,EAAE,CAAC;cAAE/B,MAAM,EAAE;YAAO,CAAE;YAAApB,QAAA,gBAClC7B,OAAA,CAACrC,UAAU;cAAC6F,OAAO,EAAC,IAAI;cAACE,YAAY;cAACC,UAAU,EAAC,MAAM;cAAA9B,QAAA,EAAC;YAExD;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbX,OAAA,CAACrC,UAAU;cAAC6F,OAAO,EAAC,OAAO;cAACD,KAAK,EAAC,gBAAgB;cAACtB,EAAE,EAAE,CAAE;cAAAJ,QAAA,EAAC;YAG1D;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbX,OAAA,CAACvC,IAAI;cAAC+G,SAAS;cAACL,OAAO,EAAE,CAAE;cAAAtC,QAAA,EACxBP,cAAc,CAACmD,GAAG,CAAC,CAACgB,IAAI,EAAEd,KAAK,kBAC9B3E,OAAA,CAACvC,IAAI;gBAACmH,IAAI;gBAAA/C,QAAA,eACR7B,OAAA,CAACjC,IAAI;kBACHsD,KAAK,EAAEoE,IAAK;kBACZlC,KAAK,EAAC,SAAS;kBACfC,OAAO,EAAC,UAAU;kBAClBnD,IAAI,eAAEL,OAAA,CAACd,YAAY;oBAAAsB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAE;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB;cAAC,GANYgE,KAAK;gBAAAnE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAOf,CACP;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAEPX,OAAA,CAACvC,IAAI;UAACmH,IAAI;UAACX,EAAE,EAAE,EAAG;UAACY,EAAE,EAAE,CAAE;UAAAhD,QAAA,eACvB7B,OAAA,CAAC9B,KAAK;YAACoC,EAAE,EAAE;cAAE0E,CAAC,EAAE,CAAC;cAAE/B,MAAM,EAAE;YAAO,CAAE;YAAApB,QAAA,gBAClC7B,OAAA,CAACrC,UAAU;cAAC6F,OAAO,EAAC,IAAI;cAACE,YAAY;cAACC,UAAU,EAAC,MAAM;cAAA9B,QAAA,EAAC;YAExD;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbX,OAAA,CAACrC,UAAU;cAAC6F,OAAO,EAAC,OAAO;cAACD,KAAK,EAAC,gBAAgB;cAACtB,EAAE,EAAE,CAAE;cAAAJ,QAAA,EAAC;YAG1D;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbX,OAAA,CAAC7B,IAAI;cAAA0D,QAAA,gBACH7B,OAAA,CAAC5B,QAAQ;gBAAAyD,QAAA,gBACP7B,OAAA,CAAC3B,YAAY;kBAAAwD,QAAA,eACX7B,OAAA,CAACZ,SAAS;oBAACmE,KAAK,EAAC;kBAAS;oBAAA/C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjB,CAAC,eACfX,OAAA,CAAC1B,YAAY;kBAACoH,OAAO,EAAC;gBAA8C;kBAAAlF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/D,CAAC,eACXX,OAAA,CAAC5B,QAAQ;gBAAAyD,QAAA,gBACP7B,OAAA,CAAC3B,YAAY;kBAAAwD,QAAA,eACX7B,OAAA,CAACZ,SAAS;oBAACmE,KAAK,EAAC;kBAAS;oBAAA/C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjB,CAAC,eACfX,OAAA,CAAC1B,YAAY;kBAACoH,OAAO,EAAC;gBAAsC;kBAAAlF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvD,CAAC,eACXX,OAAA,CAAC5B,QAAQ;gBAAAyD,QAAA,gBACP7B,OAAA,CAAC3B,YAAY;kBAAAwD,QAAA,eACX7B,OAAA,CAACZ,SAAS;oBAACmE,KAAK,EAAC;kBAAS;oBAAA/C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjB,CAAC,eACfX,OAAA,CAAC1B,YAAY;kBAACoH,OAAO,EAAC;gBAAoC;kBAAAlF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGPX,OAAA,CAAC9B,KAAK;QAACoC,EAAE,EAAE;UAAE0E,CAAC,EAAE,CAAC;UAAEhD,SAAS,EAAE,QAAQ;UAAEE,UAAU,EAAE,mDAAmD;UAAEqB,KAAK,EAAE;QAAQ,CAAE;QAAA1B,QAAA,gBACxH7B,OAAA,CAACrC,UAAU;UAAC6F,OAAO,EAAC,IAAI;UAACE,YAAY;UAACC,UAAU,EAAC,MAAM;UAAA9B,QAAA,EAAC;QAExD;UAAArB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbX,OAAA,CAACrC,UAAU;UAAC6F,OAAO,EAAC,IAAI;UAACvB,EAAE,EAAE,CAAE;UAAC3B,EAAE,EAAE;YAAEgD,OAAO,EAAE;UAAI,CAAE;UAAAzB,QAAA,EAAC;QAEtD;UAAArB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbX,OAAA,CAAC/B,KAAK;UAAC+F,SAAS,EAAC,KAAK;UAACG,OAAO,EAAE,CAAE;UAACxC,cAAc,EAAC,QAAQ;UAACgE,QAAQ,EAAC,MAAM;UAAA9D,QAAA,gBACxE7B,OAAA,CAAChC,MAAM;YACLwF,OAAO,EAAC,WAAW;YACnBY,IAAI,EAAC,OAAO;YACZ9D,EAAE,EAAE;cACF+E,OAAO,EAAE,OAAO;cAChB9B,KAAK,EAAE,cAAc;cACrB,SAAS,EAAE;gBAAE8B,OAAO,EAAE;cAAW;YACnC,CAAE;YAAAxD,QAAA,EACH;UAED;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTX,OAAA,CAAChC,MAAM;YACLwF,OAAO,EAAC,UAAU;YAClBY,IAAI,EAAC,OAAO;YACZ9D,EAAE,EAAE;cACF+C,WAAW,EAAE,OAAO;cACpBE,KAAK,EAAE,OAAO;cACd,SAAS,EAAE;gBAAEF,WAAW,EAAE,OAAO;gBAAEgC,OAAO,EAAE;cAAwB;YACtE,CAAE;YAAAxD,QAAA,EACH;UAED;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACT,CAAC;AAEV,CAAC,CAAC;AAACiF,GAAA,GAvhBG3F,SAAS;AAyhBfA,SAAS,CAAC4F,WAAW,GAAG,WAAW;AAEnC,eAAe5F,SAAS;AAAC,IAAAE,EAAA,EAAAyF,GAAA;AAAAE,YAAA,CAAA3F,EAAA;AAAA2F,YAAA,CAAAF,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}