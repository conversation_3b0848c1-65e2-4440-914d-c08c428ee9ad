{"name": "@humanwhocodes/config-array", "version": "0.13.0", "description": "Glob-based configuration matching.", "author": "<PERSON>", "main": "api.js", "files": ["api.js", "LICENSE", "README.md"], "repository": {"type": "git", "url": "git+https://github.com/humanwhocodes/config-array.git"}, "bugs": {"url": "https://github.com/humanwhocodes/config-array/issues"}, "homepage": "https://github.com/humanwhocodes/config-array#readme", "scripts": {"build": "rollup -c", "format": "nitpik", "lint": "eslint *.config.js src/*.js tests/*.js", "lint:fix": "eslint --fix *.config.js src/*.js tests/*.js", "prepublish": "npm run build", "test:coverage": "nyc --include src/*.js npm run test", "test": "mocha -r esm tests/ --recursive"}, "gitHooks": {"pre-commit": "lint-staged"}, "lint-staged": {"*.js": ["eslint --fix --ignore-pattern '!.eslintrc.js'"]}, "keywords": ["configuration", "configarray", "config file"], "license": "Apache-2.0", "engines": {"node": ">=10.10.0"}, "dependencies": {"@humanwhocodes/object-schema": "^2.0.3", "debug": "^4.3.1", "minimatch": "^3.0.5"}, "devDependencies": {"@nitpik/javascript": "0.4.0", "@nitpik/node": "0.0.5", "chai": "4.3.10", "eslint": "8.52.0", "esm": "3.2.25", "lint-staged": "15.0.2", "mocha": "6.2.3", "nyc": "15.1.0", "rollup": "3.28.1", "yorkie": "2.0.0"}}