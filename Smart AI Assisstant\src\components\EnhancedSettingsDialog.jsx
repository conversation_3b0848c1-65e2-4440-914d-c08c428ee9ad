import React, { useState } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  Box,
  Typography,
  IconButton,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemButton,
  Paper,
  Divider,
  Fade,
  useTheme,
  useMediaQuery,
  Drawer,
} from '@mui/material';
import {
  Close as CloseIcon,
  Person as PersonIcon,
  Security as SecurityIcon,
  Notifications as NotificationsIcon,
  Palette as PaletteIcon,
  Language as LanguageIcon,
  Info as InfoIcon,
  Menu as MenuIcon,
} from '@mui/icons-material';
import { useAuth } from '../contexts/AuthContext';
import { useScan } from '../contexts/ScanContext';
import UserProfile from './UserProfile';
import SecuritySettings from './SecuritySettings';
import NotificationSettings from './NotificationSettings';
import AppearanceSettings from './AppearanceSettings';
import AboutSettings from './AboutSettings';
import AuthenticationForms from './AuthenticationForms';

const EnhancedSettingsDialog = ({ open, onClose }) => {
  const [activeSection, setActiveSection] = useState('profile');
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const { isAuthenticated } = useAuth();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  const menuItems = [
    {
      id: 'profile',
      label: 'Profile',
      icon: <PersonIcon />,
      requiresAuth: true,
    },
    {
      id: 'security',
      label: 'Security',
      icon: <SecurityIcon />,
      requiresAuth: true,
    },
    {
      id: 'notifications',
      label: 'Notifications',
      icon: <NotificationsIcon />,
      requiresAuth: true,
    },
    {
      id: 'appearance',
      label: 'Appearance',
      icon: <PaletteIcon />,
      requiresAuth: false,
    },
    {
      id: 'language',
      label: 'Language',
      icon: <LanguageIcon />,
      requiresAuth: false,
    },
    {
      id: 'about',
      label: 'About',
      icon: <InfoIcon />,
      requiresAuth: false,
    },
  ];

  const filteredMenuItems = menuItems.filter(item => 
    !item.requiresAuth || isAuthenticated
  );

  const handleSectionChange = (sectionId) => {
    setActiveSection(sectionId);
    if (isMobile) {
      setMobileMenuOpen(false);
    }
  };

  const renderContent = () => {
    if (!isAuthenticated && ['profile', 'security', 'notifications'].includes(activeSection)) {
      return <AuthenticationForms />;
    }

    switch (activeSection) {
      case 'profile':
        return <UserProfile />;
      case 'security':
        return <SecuritySettings />;
      case 'notifications':
        return <NotificationSettings />;
      case 'appearance':
        return <AppearanceSettings />;
      case 'language':
        return <AppearanceSettings showLanguageOnly />;
      case 'about':
        return <AboutSettings />;
      default:
        return <UserProfile />;
    }
  };

  const sidebarContent = (
    <Box sx={{ width: isMobile ? 280 : 300, height: '100%' }}>
      <Box sx={{ p: 3, borderBottom: '1px solid', borderColor: 'divider' }}>
        <Typography variant="h6" fontWeight="bold">
          Settings
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Manage your account and preferences
        </Typography>
      </Box>
      
      <List sx={{ p: 0 }}>
        {filteredMenuItems.map((item, index) => (
          <ListItem key={item.id} disablePadding>
            <ListItemButton
              selected={activeSection === item.id}
              onClick={() => handleSectionChange(item.id)}
              sx={{
                py: 2,
                px: 3,
                '&.Mui-selected': {
                  background: (theme) => theme.palette.mode === 'dark'
                    ? 'linear-gradient(135deg, rgba(102, 126, 234, 0.2) 0%, rgba(118, 75, 162, 0.2) 100%)'
                    : 'linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%)',
                  borderRight: '3px solid',
                  borderRightColor: 'primary.main',
                  '&:hover': {
                    background: (theme) => theme.palette.mode === 'dark'
                      ? 'linear-gradient(135deg, rgba(102, 126, 234, 0.25) 0%, rgba(118, 75, 162, 0.25) 100%)'
                      : 'linear-gradient(135deg, rgba(102, 126, 234, 0.15) 0%, rgba(118, 75, 162, 0.15) 100%)',
                  },
                },
                '&:hover': {
                  background: (theme) => theme.palette.mode === 'dark'
                    ? 'rgba(102, 126, 234, 0.1)'
                    : 'rgba(102, 126, 234, 0.05)',
                },
                transition: 'all 0.2s ease',
              }}
            >
              <ListItemIcon
                sx={{
                  color: activeSection === item.id ? 'primary.main' : 'text.secondary',
                  minWidth: 40,
                }}
              >
                {item.icon}
              </ListItemIcon>
              <ListItemText
                primary={item.label}
                primaryTypographyProps={{
                  fontWeight: activeSection === item.id ? 600 : 400,
                  color: activeSection === item.id ? 'primary.main' : 'text.primary',
                }}
              />
            </ListItemButton>
          </ListItem>
        ))}
      </List>
    </Box>
  );

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="lg"
      fullWidth
      fullScreen={isMobile}
      PaperProps={{
        sx: {
          borderRadius: isMobile ? 0 : 4,
          minHeight: isMobile ? '100vh' : '80vh',
          maxHeight: isMobile ? '100vh' : '90vh',
          background: (theme) => theme.palette.mode === 'dark'
            ? 'linear-gradient(135deg, rgba(12, 12, 12, 0.98) 0%, rgba(26, 26, 26, 0.98) 50%, rgba(45, 45, 45, 0.98) 100%)'
            : 'linear-gradient(135deg, rgba(248, 250, 252, 0.98) 0%, rgba(241, 245, 249, 0.98) 50%, rgba(226, 232, 240, 0.98) 100%)',
          backdropFilter: 'blur(25px) saturate(180%)',
          border: (theme) => theme.palette.mode === 'dark'
            ? '1px solid rgba(102, 126, 234, 0.2)'
            : '1px solid rgba(102, 126, 234, 0.1)',
          boxShadow: '0 25px 80px rgba(0, 0, 0, 0.3)',
          overflow: 'hidden',
        },
      }}
    >
      {/* Header */}
      <DialogTitle sx={{ p: 0 }}>
        <Box
          display="flex"
          alignItems="center"
          justifyContent="space-between"
          sx={{
            p: 3,
            background: (theme) => theme.palette.mode === 'dark'
              ? 'linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%)'
              : 'linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%)',
            borderBottom: '1px solid',
            borderColor: 'divider',
          }}
        >
          <Box display="flex" alignItems="center" gap={2}>
            {isMobile && (
              <IconButton
                onClick={() => setMobileMenuOpen(true)}
                sx={{ mr: 1 }}
              >
                <MenuIcon />
              </IconButton>
            )}
            
            <Box>
              <Typography variant="h5" fontWeight="bold">
                Settings & Account
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {filteredMenuItems.find(item => item.id === activeSection)?.label || 'Settings'}
              </Typography>
            </Box>
          </Box>
          
          <IconButton
            onClick={onClose}
            sx={{
              background: 'rgba(255, 255, 255, 0.1)',
              backdropFilter: 'blur(10px)',
              '&:hover': {
                background: 'rgba(255, 255, 255, 0.2)',
                transform: 'scale(1.05)',
              },
              transition: 'all 0.2s ease',
            }}
          >
            <CloseIcon />
          </IconButton>
        </Box>
      </DialogTitle>

      <DialogContent sx={{ p: 0, display: 'flex', height: '100%' }}>
        {/* Mobile Drawer */}
        {isMobile && (
          <Drawer
            anchor="left"
            open={mobileMenuOpen}
            onClose={() => setMobileMenuOpen(false)}
            PaperProps={{
              sx: {
                background: (theme) => theme.palette.mode === 'dark'
                  ? 'rgba(26, 26, 26, 0.98)'
                  : 'rgba(248, 250, 252, 0.98)',
                backdropFilter: 'blur(25px)',
              },
            }}
          >
            {sidebarContent}
          </Drawer>
        )}

        {/* Desktop Sidebar */}
        {!isMobile && (
          <Paper
            elevation={0}
            sx={{
              background: (theme) => theme.palette.mode === 'dark'
                ? 'rgba(255, 255, 255, 0.02)'
                : 'rgba(0, 0, 0, 0.02)',
              borderRight: '1px solid',
              borderColor: 'divider',
              borderRadius: 0,
            }}
          >
            {sidebarContent}
          </Paper>
        )}

        {/* Main Content */}
        <Box
          sx={{
            flex: 1,
            p: 4,
            overflow: 'auto',
            background: (theme) => theme.palette.mode === 'dark'
              ? 'rgba(0, 0, 0, 0.02)'
              : 'rgba(255, 255, 255, 0.02)',
          }}
        >
          <Fade in={true} timeout={300}>
            <Box>
              {renderContent()}
            </Box>
          </Fade>
        </Box>
      </DialogContent>
    </Dialog>
  );
};

export default EnhancedSettingsDialog;
