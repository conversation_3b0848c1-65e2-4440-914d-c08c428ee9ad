/**
 * @license W3C
 * https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 *
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/utilities.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 *
 * @see https://github.com/w3c/csswg-drafts/blob/main/css-color-4/utilities.js
 */
declare type color = [number, number, number];
export declare function sRGB_to_luminance(RGB: color): number;
export declare function contrast(RGB1: color, RGB2: color): number;
export declare function sRGB_to_LCH(RGB: color): color;
export declare function P3_to_LCH(RGB: color): color;
export declare function r2020_to_LCH(RGB: color): color;
export declare function LCH_to_sRGB(LCH: color): color;
export declare function LCH_to_P3(LCH: color): color;
export declare function LCH_to_r2020(LCH: color): color;
export declare function hslToRgb(hsl: color): color;
export declare function hueToRgb(t1: number, t2: number, hue: number): number;
export declare function naive_CMYK_to_sRGB(CMYK: [number, number, number, number]): color;
export declare function naive_sRGB_to_CMYK(RGB: color): [number, number, number, number];
export declare function XYZ_to_xy(XYZ: color): [number, number];
export declare function xy_to_uv(xy: [number, number]): [number, number];
export declare function XYZ_to_uv(XYZ: color): [number, number];
export {};
