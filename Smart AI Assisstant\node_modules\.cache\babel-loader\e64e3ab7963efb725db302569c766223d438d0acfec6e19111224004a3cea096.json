{"ast": null, "code": "var _jsxFileName = \"E:\\\\PythonProjects\\\\React.js\\\\Smart AI Assisstant\\\\src\\\\components\\\\NotificationSettings.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Box, Card, CardContent, Typography, List, ListItem, ListItemIcon, ListItemText, ListItemSecondaryAction, Switch, Divider, Chip, Alert, Button, FormControl, InputLabel, Select, MenuItem } from '@mui/material';\nimport { Notifications as NotificationsIcon, Email as EmailIcon, Sms as SmsIcon, Security as SecurityIcon, Assessment as AssessmentIcon, Update as UpdateIcon, Campaign as MarketingIcon } from '@mui/icons-material';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst NotificationSettings = () => {\n  _s();\n  const [alert, setAlert] = useState({\n    show: false,\n    message: '',\n    severity: 'success'\n  });\n  const [notifications, setNotifications] = useState({\n    // Security notifications\n    loginAlerts: true,\n    passwordChanges: true,\n    suspiciousActivity: true,\n    // Scan notifications\n    scanComplete: true,\n    threatDetected: true,\n    scanScheduled: false,\n    // System notifications\n    systemUpdates: true,\n    maintenanceAlerts: true,\n    // Marketing\n    newsletter: false,\n    productUpdates: false,\n    tips: true\n  });\n  const [notificationMethods, setNotificationMethods] = useState({\n    email: true,\n    push: true,\n    sms: false\n  });\n  const [frequency, setFrequency] = useState('immediate');\n  const showAlert = (message, severity = 'success') => {\n    setAlert({\n      show: true,\n      message,\n      severity\n    });\n    setTimeout(() => setAlert({\n      show: false,\n      message: '',\n      severity: 'success'\n    }), 3000);\n  };\n  const handleNotificationChange = key => {\n    setNotifications(prev => ({\n      ...prev,\n      [key]: !prev[key]\n    }));\n    const action = notifications[key] ? 'disabled' : 'enabled';\n    showAlert(`${key.replace(/([A-Z])/g, ' $1').toLowerCase()} notifications ${action}`, 'info');\n  };\n  const handleMethodChange = method => {\n    setNotificationMethods(prev => ({\n      ...prev,\n      [method]: !prev[method]\n    }));\n    const action = notificationMethods[method] ? 'disabled' : 'enabled';\n    showAlert(`${method.toUpperCase()} notifications ${action}`, 'info');\n  };\n  const notificationCategories = [{\n    title: 'Security Notifications',\n    icon: /*#__PURE__*/_jsxDEV(SecurityIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 91,\n      columnNumber: 13\n    }, this),\n    items: [{\n      key: 'loginAlerts',\n      primary: 'Login Alerts',\n      secondary: 'Get notified when someone logs into your account',\n      important: true\n    }, {\n      key: 'passwordChanges',\n      primary: 'Password Changes',\n      secondary: 'Alerts when your password is changed',\n      important: true\n    }, {\n      key: 'suspiciousActivity',\n      primary: 'Suspicious Activity',\n      secondary: 'Notifications about unusual account activity',\n      important: true\n    }]\n  }, {\n    title: 'Scan Notifications',\n    icon: /*#__PURE__*/_jsxDEV(AssessmentIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 115,\n      columnNumber: 13\n    }, this),\n    items: [{\n      key: 'scanComplete',\n      primary: 'Scan Complete',\n      secondary: 'When your security scans finish'\n    }, {\n      key: 'threatDetected',\n      primary: 'Threat Detected',\n      secondary: 'Immediate alerts for detected threats',\n      important: true\n    }, {\n      key: 'scanScheduled',\n      primary: 'Scheduled Scans',\n      secondary: 'Reminders about upcoming scheduled scans'\n    }]\n  }, {\n    title: 'System Notifications',\n    icon: /*#__PURE__*/_jsxDEV(UpdateIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 137,\n      columnNumber: 13\n    }, this),\n    items: [{\n      key: 'systemUpdates',\n      primary: 'System Updates',\n      secondary: 'Important updates and new features'\n    }, {\n      key: 'maintenanceAlerts',\n      primary: 'Maintenance Alerts',\n      secondary: 'Scheduled maintenance notifications'\n    }]\n  }, {\n    title: 'Marketing & Tips',\n    icon: /*#__PURE__*/_jsxDEV(MarketingIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 153,\n      columnNumber: 13\n    }, this),\n    items: [{\n      key: 'newsletter',\n      primary: 'Newsletter',\n      secondary: 'Monthly security tips and updates'\n    }, {\n      key: 'productUpdates',\n      primary: 'Product Updates',\n      secondary: 'New features and product announcements'\n    }, {\n      key: 'tips',\n      primary: 'Security Tips',\n      secondary: 'Helpful security tips and best practices'\n    }]\n  }];\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [alert.show && /*#__PURE__*/_jsxDEV(Alert, {\n      severity: alert.severity,\n      sx: {\n        mb: 3\n      },\n      children: alert.message\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 178,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h4\",\n      gutterBottom: true,\n      fontWeight: \"bold\",\n      children: \"Notification Settings\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 183,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"body1\",\n      color: \"text.secondary\",\n      sx: {\n        mb: 4\n      },\n      children: \"Customize how and when you receive notifications\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 186,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        mb: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          fontWeight: \"600\",\n          sx: {\n            mb: 2\n          },\n          children: \"Notification Methods\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 193,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(List, {\n          children: [/*#__PURE__*/_jsxDEV(ListItem, {\n            children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n              children: /*#__PURE__*/_jsxDEV(EmailIcon, {\n                color: \"primary\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 200,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 199,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: \"Email Notifications\",\n              secondary: \"Receive notifications via email\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 202,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ListItemSecondaryAction, {\n              children: /*#__PURE__*/_jsxDEV(Switch, {\n                checked: notificationMethods.email,\n                onChange: () => handleMethodChange('email')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 207,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 206,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 198,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 214,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n            children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n              children: /*#__PURE__*/_jsxDEV(NotificationsIcon, {\n                color: \"primary\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 218,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 217,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: \"Push Notifications\",\n              secondary: \"Browser push notifications\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 220,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ListItemSecondaryAction, {\n              children: /*#__PURE__*/_jsxDEV(Switch, {\n                checked: notificationMethods.push,\n                onChange: () => handleMethodChange('push')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 225,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 224,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 216,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 232,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n            children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n              children: /*#__PURE__*/_jsxDEV(SmsIcon, {\n                color: \"primary\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 236,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 235,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: \"SMS Notifications\",\n              secondary: \"Text message alerts for critical events\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 238,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ListItemSecondaryAction, {\n              children: /*#__PURE__*/_jsxDEV(Switch, {\n                checked: notificationMethods.sms,\n                onChange: () => handleMethodChange('sms')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 243,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 242,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 234,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 197,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 192,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 191,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        mb: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          fontWeight: \"600\",\n          sx: {\n            mb: 2\n          },\n          children: \"Notification Frequency\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 256,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n          fullWidth: true,\n          sx: {\n            maxWidth: 300\n          },\n          children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n            children: \"Frequency\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 261,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Select, {\n            value: frequency,\n            label: \"Frequency\",\n            onChange: e => setFrequency(e.target.value),\n            children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n              value: \"immediate\",\n              children: \"Immediate\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 267,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              value: \"hourly\",\n              children: \"Hourly Digest\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 268,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              value: \"daily\",\n              children: \"Daily Digest\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 269,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              value: \"weekly\",\n              children: \"Weekly Summary\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 270,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 262,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 260,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 255,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 254,\n      columnNumber: 7\n    }, this), notificationCategories.map((category, index) => /*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        mb: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            mb: 2\n          },\n          children: [/*#__PURE__*/React.cloneElement(category.icon, {\n            sx: {\n              mr: 2,\n              color: 'primary.main'\n            }\n          }), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            fontWeight: \"600\",\n            children: category.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 282,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 280,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(List, {\n          children: category.items.map((item, itemIndex) => /*#__PURE__*/_jsxDEV(React.Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(ListItem, {\n              children: [/*#__PURE__*/_jsxDEV(ListItemText, {\n                primary: /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: 1\n                  },\n                  children: [item.primary, item.important && /*#__PURE__*/_jsxDEV(Chip, {\n                    label: \"Important\",\n                    size: \"small\",\n                    color: \"warning\",\n                    variant: \"outlined\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 296,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 293,\n                  columnNumber: 25\n                }, this),\n                secondary: item.secondary\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 291,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemSecondaryAction, {\n                children: /*#__PURE__*/_jsxDEV(Switch, {\n                  checked: notifications[item.key],\n                  onChange: () => handleNotificationChange(item.key)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 308,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 307,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 290,\n              columnNumber: 19\n            }, this), itemIndex < category.items.length - 1 && /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 314,\n              columnNumber: 61\n            }, this)]\n          }, item.key, true, {\n            fileName: _jsxFileName,\n            lineNumber: 289,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 287,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 279,\n        columnNumber: 11\n      }, this)\n    }, category.title, false, {\n      fileName: _jsxFileName,\n      lineNumber: 278,\n      columnNumber: 9\n    }, this)), /*#__PURE__*/_jsxDEV(Card, {\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          fontWeight: \"600\",\n          sx: {\n            mb: 2\n          },\n          children: \"Quick Actions\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 325,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            gap: 2,\n            flexWrap: 'wrap'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outlined\",\n            onClick: () => {\n              const allEnabled = Object.fromEntries(Object.keys(notifications).map(key => [key, true]));\n              setNotifications(allEnabled);\n              showAlert('All notifications enabled', 'success');\n            },\n            sx: {\n              borderRadius: 3\n            },\n            children: \"Enable All\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 330,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outlined\",\n            onClick: () => {\n              const essentialOnly = {\n                ...Object.fromEntries(Object.keys(notifications).map(key => [key, false])),\n                loginAlerts: true,\n                passwordChanges: true,\n                suspiciousActivity: true,\n                threatDetected: true\n              };\n              setNotifications(essentialOnly);\n              showAlert('Only essential notifications enabled', 'info');\n            },\n            sx: {\n              borderRadius: 3\n            },\n            children: \"Essential Only\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 344,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outlined\",\n            color: \"error\",\n            onClick: () => {\n              const allDisabled = Object.fromEntries(Object.keys(notifications).map(key => [key, false]));\n              setNotifications(allDisabled);\n              showAlert('All notifications disabled', 'warning');\n            },\n            sx: {\n              borderRadius: 3\n            },\n            children: \"Disable All\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 362,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 329,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 324,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 323,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 175,\n    columnNumber: 5\n  }, this);\n};\n_s(NotificationSettings, \"se81A+BYrIA3TKO+3YrKr6MeOyo=\");\n_c = NotificationSettings;\nexport default NotificationSettings;\nvar _c;\n$RefreshReg$(_c, \"NotificationSettings\");", "map": {"version": 3, "names": ["React", "useState", "Box", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Typography", "List", "ListItem", "ListItemIcon", "ListItemText", "ListItemSecondaryAction", "Switch", "Divider", "Chip", "<PERSON><PERSON>", "<PERSON><PERSON>", "FormControl", "InputLabel", "Select", "MenuItem", "Notifications", "NotificationsIcon", "Email", "EmailIcon", "Sms", "SmsIcon", "Security", "SecurityIcon", "Assessment", "AssessmentIcon", "Update", "UpdateIcon", "Campaign", "MarketingIcon", "jsxDEV", "_jsxDEV", "NotificationSettings", "_s", "alert", "<PERSON><PERSON><PERSON><PERSON>", "show", "message", "severity", "notifications", "setNotifications", "loginAlerts", "passwordChanges", "suspiciousActivity", "scanComplete", "threatDetected", "scanScheduled", "systemUpdates", "maintenanceAlerts", "newsletter", "productUpdates", "tips", "notificationMethods", "setNotificationMethods", "email", "push", "sms", "frequency", "setFrequency", "show<PERSON><PERSON><PERSON>", "setTimeout", "handleNotificationChange", "key", "prev", "action", "replace", "toLowerCase", "handleMethodChange", "method", "toUpperCase", "notificationCategories", "title", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "items", "primary", "secondary", "important", "children", "sx", "mb", "variant", "gutterBottom", "fontWeight", "color", "checked", "onChange", "fullWidth", "max<PERSON><PERSON><PERSON>", "value", "label", "e", "target", "map", "category", "index", "display", "alignItems", "cloneElement", "mr", "item", "itemIndex", "Fragment", "gap", "size", "length", "flexWrap", "onClick", "allEnabled", "Object", "fromEntries", "keys", "borderRadius", "essentialOnly", "allDisabled", "_c", "$RefreshReg$"], "sources": ["E:/PythonProjects/React.js/Smart AI Assisstant/src/components/NotificationSettings.jsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  Box,\n  Card,\n  CardContent,\n  Typography,\n  List,\n  ListItem,\n  ListItemIcon,\n  ListItemText,\n  ListItemSecondaryAction,\n  Switch,\n  Divider,\n  Chip,\n  Alert,\n  Button,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n} from '@mui/material';\nimport {\n  Notifications as NotificationsIcon,\n  Email as EmailIcon,\n  Sms as SmsIcon,\n  Security as SecurityIcon,\n  Assessment as AssessmentIcon,\n  Update as UpdateIcon,\n  Campaign as MarketingIcon,\n} from '@mui/icons-material';\n\nconst NotificationSettings = () => {\n  const [alert, setAlert] = useState({ show: false, message: '', severity: 'success' });\n  const [notifications, setNotifications] = useState({\n    // Security notifications\n    loginAlerts: true,\n    passwordChanges: true,\n    suspiciousActivity: true,\n    \n    // Scan notifications\n    scanComplete: true,\n    threatDetected: true,\n    scanScheduled: false,\n    \n    // System notifications\n    systemUpdates: true,\n    maintenanceAlerts: true,\n    \n    // Marketing\n    newsletter: false,\n    productUpdates: false,\n    tips: true,\n  });\n\n  const [notificationMethods, setNotificationMethods] = useState({\n    email: true,\n    push: true,\n    sms: false,\n  });\n\n  const [frequency, setFrequency] = useState('immediate');\n\n  const showAlert = (message, severity = 'success') => {\n    setAlert({ show: true, message, severity });\n    setTimeout(() => setAlert({ show: false, message: '', severity: 'success' }), 3000);\n  };\n\n  const handleNotificationChange = (key) => {\n    setNotifications(prev => ({\n      ...prev,\n      [key]: !prev[key],\n    }));\n    \n    const action = notifications[key] ? 'disabled' : 'enabled';\n    showAlert(`${key.replace(/([A-Z])/g, ' $1').toLowerCase()} notifications ${action}`, 'info');\n  };\n\n  const handleMethodChange = (method) => {\n    setNotificationMethods(prev => ({\n      ...prev,\n      [method]: !prev[method],\n    }));\n    \n    const action = notificationMethods[method] ? 'disabled' : 'enabled';\n    showAlert(`${method.toUpperCase()} notifications ${action}`, 'info');\n  };\n\n  const notificationCategories = [\n    {\n      title: 'Security Notifications',\n      icon: <SecurityIcon />,\n      items: [\n        {\n          key: 'loginAlerts',\n          primary: 'Login Alerts',\n          secondary: 'Get notified when someone logs into your account',\n          important: true,\n        },\n        {\n          key: 'passwordChanges',\n          primary: 'Password Changes',\n          secondary: 'Alerts when your password is changed',\n          important: true,\n        },\n        {\n          key: 'suspiciousActivity',\n          primary: 'Suspicious Activity',\n          secondary: 'Notifications about unusual account activity',\n          important: true,\n        },\n      ],\n    },\n    {\n      title: 'Scan Notifications',\n      icon: <AssessmentIcon />,\n      items: [\n        {\n          key: 'scanComplete',\n          primary: 'Scan Complete',\n          secondary: 'When your security scans finish',\n        },\n        {\n          key: 'threatDetected',\n          primary: 'Threat Detected',\n          secondary: 'Immediate alerts for detected threats',\n          important: true,\n        },\n        {\n          key: 'scanScheduled',\n          primary: 'Scheduled Scans',\n          secondary: 'Reminders about upcoming scheduled scans',\n        },\n      ],\n    },\n    {\n      title: 'System Notifications',\n      icon: <UpdateIcon />,\n      items: [\n        {\n          key: 'systemUpdates',\n          primary: 'System Updates',\n          secondary: 'Important updates and new features',\n        },\n        {\n          key: 'maintenanceAlerts',\n          primary: 'Maintenance Alerts',\n          secondary: 'Scheduled maintenance notifications',\n        },\n      ],\n    },\n    {\n      title: 'Marketing & Tips',\n      icon: <MarketingIcon />,\n      items: [\n        {\n          key: 'newsletter',\n          primary: 'Newsletter',\n          secondary: 'Monthly security tips and updates',\n        },\n        {\n          key: 'productUpdates',\n          primary: 'Product Updates',\n          secondary: 'New features and product announcements',\n        },\n        {\n          key: 'tips',\n          primary: 'Security Tips',\n          secondary: 'Helpful security tips and best practices',\n        },\n      ],\n    },\n  ];\n\n  return (\n    <Box>\n      {/* Alert */}\n      {alert.show && (\n        <Alert severity={alert.severity} sx={{ mb: 3 }}>\n          {alert.message}\n        </Alert>\n      )}\n\n      <Typography variant=\"h4\" gutterBottom fontWeight=\"bold\">\n        Notification Settings\n      </Typography>\n      <Typography variant=\"body1\" color=\"text.secondary\" sx={{ mb: 4 }}>\n        Customize how and when you receive notifications\n      </Typography>\n\n      {/* Notification Methods */}\n      <Card sx={{ mb: 3 }}>\n        <CardContent>\n          <Typography variant=\"h6\" fontWeight=\"600\" sx={{ mb: 2 }}>\n            Notification Methods\n          </Typography>\n          \n          <List>\n            <ListItem>\n              <ListItemIcon>\n                <EmailIcon color=\"primary\" />\n              </ListItemIcon>\n              <ListItemText\n                primary=\"Email Notifications\"\n                secondary=\"Receive notifications via email\"\n              />\n              <ListItemSecondaryAction>\n                <Switch\n                  checked={notificationMethods.email}\n                  onChange={() => handleMethodChange('email')}\n                />\n              </ListItemSecondaryAction>\n            </ListItem>\n            \n            <Divider />\n            \n            <ListItem>\n              <ListItemIcon>\n                <NotificationsIcon color=\"primary\" />\n              </ListItemIcon>\n              <ListItemText\n                primary=\"Push Notifications\"\n                secondary=\"Browser push notifications\"\n              />\n              <ListItemSecondaryAction>\n                <Switch\n                  checked={notificationMethods.push}\n                  onChange={() => handleMethodChange('push')}\n                />\n              </ListItemSecondaryAction>\n            </ListItem>\n            \n            <Divider />\n            \n            <ListItem>\n              <ListItemIcon>\n                <SmsIcon color=\"primary\" />\n              </ListItemIcon>\n              <ListItemText\n                primary=\"SMS Notifications\"\n                secondary=\"Text message alerts for critical events\"\n              />\n              <ListItemSecondaryAction>\n                <Switch\n                  checked={notificationMethods.sms}\n                  onChange={() => handleMethodChange('sms')}\n                />\n              </ListItemSecondaryAction>\n            </ListItem>\n          </List>\n        </CardContent>\n      </Card>\n\n      {/* Notification Frequency */}\n      <Card sx={{ mb: 3 }}>\n        <CardContent>\n          <Typography variant=\"h6\" fontWeight=\"600\" sx={{ mb: 2 }}>\n            Notification Frequency\n          </Typography>\n          \n          <FormControl fullWidth sx={{ maxWidth: 300 }}>\n            <InputLabel>Frequency</InputLabel>\n            <Select\n              value={frequency}\n              label=\"Frequency\"\n              onChange={(e) => setFrequency(e.target.value)}\n            >\n              <MenuItem value=\"immediate\">Immediate</MenuItem>\n              <MenuItem value=\"hourly\">Hourly Digest</MenuItem>\n              <MenuItem value=\"daily\">Daily Digest</MenuItem>\n              <MenuItem value=\"weekly\">Weekly Summary</MenuItem>\n            </Select>\n          </FormControl>\n        </CardContent>\n      </Card>\n\n      {/* Notification Categories */}\n      {notificationCategories.map((category, index) => (\n        <Card key={category.title} sx={{ mb: 3 }}>\n          <CardContent>\n            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>\n              {React.cloneElement(category.icon, { sx: { mr: 2, color: 'primary.main' } })}\n              <Typography variant=\"h6\" fontWeight=\"600\">\n                {category.title}\n              </Typography>\n            </Box>\n            \n            <List>\n              {category.items.map((item, itemIndex) => (\n                <React.Fragment key={item.key}>\n                  <ListItem>\n                    <ListItemText\n                      primary={\n                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n                          {item.primary}\n                          {item.important && (\n                            <Chip\n                              label=\"Important\"\n                              size=\"small\"\n                              color=\"warning\"\n                              variant=\"outlined\"\n                            />\n                          )}\n                        </Box>\n                      }\n                      secondary={item.secondary}\n                    />\n                    <ListItemSecondaryAction>\n                      <Switch\n                        checked={notifications[item.key]}\n                        onChange={() => handleNotificationChange(item.key)}\n                      />\n                    </ListItemSecondaryAction>\n                  </ListItem>\n                  {itemIndex < category.items.length - 1 && <Divider />}\n                </React.Fragment>\n              ))}\n            </List>\n          </CardContent>\n        </Card>\n      ))}\n\n      {/* Quick Actions */}\n      <Card>\n        <CardContent>\n          <Typography variant=\"h6\" fontWeight=\"600\" sx={{ mb: 2 }}>\n            Quick Actions\n          </Typography>\n          \n          <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>\n            <Button\n              variant=\"outlined\"\n              onClick={() => {\n                const allEnabled = Object.fromEntries(\n                  Object.keys(notifications).map(key => [key, true])\n                );\n                setNotifications(allEnabled);\n                showAlert('All notifications enabled', 'success');\n              }}\n              sx={{ borderRadius: 3 }}\n            >\n              Enable All\n            </Button>\n            \n            <Button\n              variant=\"outlined\"\n              onClick={() => {\n                const essentialOnly = {\n                  ...Object.fromEntries(Object.keys(notifications).map(key => [key, false])),\n                  loginAlerts: true,\n                  passwordChanges: true,\n                  suspiciousActivity: true,\n                  threatDetected: true,\n                };\n                setNotifications(essentialOnly);\n                showAlert('Only essential notifications enabled', 'info');\n              }}\n              sx={{ borderRadius: 3 }}\n            >\n              Essential Only\n            </Button>\n            \n            <Button\n              variant=\"outlined\"\n              color=\"error\"\n              onClick={() => {\n                const allDisabled = Object.fromEntries(\n                  Object.keys(notifications).map(key => [key, false])\n                );\n                setNotifications(allDisabled);\n                showAlert('All notifications disabled', 'warning');\n              }}\n              sx={{ borderRadius: 3 }}\n            >\n              Disable All\n            </Button>\n          </Box>\n        </CardContent>\n      </Card>\n    </Box>\n  );\n};\n\nexport default NotificationSettings;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,GAAG,EACHC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,YAAY,EACZC,uBAAuB,EACvBC,MAAM,EACNC,OAAO,EACPC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,QACH,eAAe;AACtB,SACEC,aAAa,IAAIC,iBAAiB,EAClCC,KAAK,IAAIC,SAAS,EAClBC,GAAG,IAAIC,OAAO,EACdC,QAAQ,IAAIC,YAAY,EACxBC,UAAU,IAAIC,cAAc,EAC5BC,MAAM,IAAIC,UAAU,EACpBC,QAAQ,IAAIC,aAAa,QACpB,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7B,MAAMC,oBAAoB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjC,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGtC,QAAQ,CAAC;IAAEuC,IAAI,EAAE,KAAK;IAAEC,OAAO,EAAE,EAAE;IAAEC,QAAQ,EAAE;EAAU,CAAC,CAAC;EACrF,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAG3C,QAAQ,CAAC;IACjD;IACA4C,WAAW,EAAE,IAAI;IACjBC,eAAe,EAAE,IAAI;IACrBC,kBAAkB,EAAE,IAAI;IAExB;IACAC,YAAY,EAAE,IAAI;IAClBC,cAAc,EAAE,IAAI;IACpBC,aAAa,EAAE,KAAK;IAEpB;IACAC,aAAa,EAAE,IAAI;IACnBC,iBAAiB,EAAE,IAAI;IAEvB;IACAC,UAAU,EAAE,KAAK;IACjBC,cAAc,EAAE,KAAK;IACrBC,IAAI,EAAE;EACR,CAAC,CAAC;EAEF,MAAM,CAACC,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGxD,QAAQ,CAAC;IAC7DyD,KAAK,EAAE,IAAI;IACXC,IAAI,EAAE,IAAI;IACVC,GAAG,EAAE;EACP,CAAC,CAAC;EAEF,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAG7D,QAAQ,CAAC,WAAW,CAAC;EAEvD,MAAM8D,SAAS,GAAGA,CAACtB,OAAO,EAAEC,QAAQ,GAAG,SAAS,KAAK;IACnDH,QAAQ,CAAC;MAAEC,IAAI,EAAE,IAAI;MAAEC,OAAO;MAAEC;IAAS,CAAC,CAAC;IAC3CsB,UAAU,CAAC,MAAMzB,QAAQ,CAAC;MAAEC,IAAI,EAAE,KAAK;MAAEC,OAAO,EAAE,EAAE;MAAEC,QAAQ,EAAE;IAAU,CAAC,CAAC,EAAE,IAAI,CAAC;EACrF,CAAC;EAED,MAAMuB,wBAAwB,GAAIC,GAAG,IAAK;IACxCtB,gBAAgB,CAACuB,IAAI,KAAK;MACxB,GAAGA,IAAI;MACP,CAACD,GAAG,GAAG,CAACC,IAAI,CAACD,GAAG;IAClB,CAAC,CAAC,CAAC;IAEH,MAAME,MAAM,GAAGzB,aAAa,CAACuB,GAAG,CAAC,GAAG,UAAU,GAAG,SAAS;IAC1DH,SAAS,CAAC,GAAGG,GAAG,CAACG,OAAO,CAAC,UAAU,EAAE,KAAK,CAAC,CAACC,WAAW,CAAC,CAAC,kBAAkBF,MAAM,EAAE,EAAE,MAAM,CAAC;EAC9F,CAAC;EAED,MAAMG,kBAAkB,GAAIC,MAAM,IAAK;IACrCf,sBAAsB,CAACU,IAAI,KAAK;MAC9B,GAAGA,IAAI;MACP,CAACK,MAAM,GAAG,CAACL,IAAI,CAACK,MAAM;IACxB,CAAC,CAAC,CAAC;IAEH,MAAMJ,MAAM,GAAGZ,mBAAmB,CAACgB,MAAM,CAAC,GAAG,UAAU,GAAG,SAAS;IACnET,SAAS,CAAC,GAAGS,MAAM,CAACC,WAAW,CAAC,CAAC,kBAAkBL,MAAM,EAAE,EAAE,MAAM,CAAC;EACtE,CAAC;EAED,MAAMM,sBAAsB,GAAG,CAC7B;IACEC,KAAK,EAAE,wBAAwB;IAC/BC,IAAI,eAAEzC,OAAA,CAACR,YAAY;MAAAkD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBC,KAAK,EAAE,CACL;MACEf,GAAG,EAAE,aAAa;MAClBgB,OAAO,EAAE,cAAc;MACvBC,SAAS,EAAE,kDAAkD;MAC7DC,SAAS,EAAE;IACb,CAAC,EACD;MACElB,GAAG,EAAE,iBAAiB;MACtBgB,OAAO,EAAE,kBAAkB;MAC3BC,SAAS,EAAE,sCAAsC;MACjDC,SAAS,EAAE;IACb,CAAC,EACD;MACElB,GAAG,EAAE,oBAAoB;MACzBgB,OAAO,EAAE,qBAAqB;MAC9BC,SAAS,EAAE,8CAA8C;MACzDC,SAAS,EAAE;IACb,CAAC;EAEL,CAAC,EACD;IACET,KAAK,EAAE,oBAAoB;IAC3BC,IAAI,eAAEzC,OAAA,CAACN,cAAc;MAAAgD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACxBC,KAAK,EAAE,CACL;MACEf,GAAG,EAAE,cAAc;MACnBgB,OAAO,EAAE,eAAe;MACxBC,SAAS,EAAE;IACb,CAAC,EACD;MACEjB,GAAG,EAAE,gBAAgB;MACrBgB,OAAO,EAAE,iBAAiB;MAC1BC,SAAS,EAAE,uCAAuC;MAClDC,SAAS,EAAE;IACb,CAAC,EACD;MACElB,GAAG,EAAE,eAAe;MACpBgB,OAAO,EAAE,iBAAiB;MAC1BC,SAAS,EAAE;IACb,CAAC;EAEL,CAAC,EACD;IACER,KAAK,EAAE,sBAAsB;IAC7BC,IAAI,eAAEzC,OAAA,CAACJ,UAAU;MAAA8C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACpBC,KAAK,EAAE,CACL;MACEf,GAAG,EAAE,eAAe;MACpBgB,OAAO,EAAE,gBAAgB;MACzBC,SAAS,EAAE;IACb,CAAC,EACD;MACEjB,GAAG,EAAE,mBAAmB;MACxBgB,OAAO,EAAE,oBAAoB;MAC7BC,SAAS,EAAE;IACb,CAAC;EAEL,CAAC,EACD;IACER,KAAK,EAAE,kBAAkB;IACzBC,IAAI,eAAEzC,OAAA,CAACF,aAAa;MAAA4C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACvBC,KAAK,EAAE,CACL;MACEf,GAAG,EAAE,YAAY;MACjBgB,OAAO,EAAE,YAAY;MACrBC,SAAS,EAAE;IACb,CAAC,EACD;MACEjB,GAAG,EAAE,gBAAgB;MACrBgB,OAAO,EAAE,iBAAiB;MAC1BC,SAAS,EAAE;IACb,CAAC,EACD;MACEjB,GAAG,EAAE,MAAM;MACXgB,OAAO,EAAE,eAAe;MACxBC,SAAS,EAAE;IACb,CAAC;EAEL,CAAC,CACF;EAED,oBACEhD,OAAA,CAACjC,GAAG;IAAAmF,QAAA,GAED/C,KAAK,CAACE,IAAI,iBACTL,OAAA,CAACrB,KAAK;MAAC4B,QAAQ,EAAEJ,KAAK,CAACI,QAAS;MAAC4C,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAF,QAAA,EAC5C/C,KAAK,CAACG;IAAO;MAAAoC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACT,CACR,eAED7C,OAAA,CAAC9B,UAAU;MAACmF,OAAO,EAAC,IAAI;MAACC,YAAY;MAACC,UAAU,EAAC,MAAM;MAAAL,QAAA,EAAC;IAExD;MAAAR,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eACb7C,OAAA,CAAC9B,UAAU;MAACmF,OAAO,EAAC,OAAO;MAACG,KAAK,EAAC,gBAAgB;MAACL,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAF,QAAA,EAAC;IAElE;MAAAR,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eAGb7C,OAAA,CAAChC,IAAI;MAACmF,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAF,QAAA,eAClBlD,OAAA,CAAC/B,WAAW;QAAAiF,QAAA,gBACVlD,OAAA,CAAC9B,UAAU;UAACmF,OAAO,EAAC,IAAI;UAACE,UAAU,EAAC,KAAK;UAACJ,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAAF,QAAA,EAAC;QAEzD;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAEb7C,OAAA,CAAC7B,IAAI;UAAA+E,QAAA,gBACHlD,OAAA,CAAC5B,QAAQ;YAAA8E,QAAA,gBACPlD,OAAA,CAAC3B,YAAY;cAAA6E,QAAA,eACXlD,OAAA,CAACZ,SAAS;gBAACoE,KAAK,EAAC;cAAS;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB,CAAC,eACf7C,OAAA,CAAC1B,YAAY;cACXyE,OAAO,EAAC,qBAAqB;cAC7BC,SAAS,EAAC;YAAiC;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5C,CAAC,eACF7C,OAAA,CAACzB,uBAAuB;cAAA2E,QAAA,eACtBlD,OAAA,CAACxB,MAAM;gBACLiF,OAAO,EAAEpC,mBAAmB,CAACE,KAAM;gBACnCmC,QAAQ,EAAEA,CAAA,KAAMtB,kBAAkB,CAAC,OAAO;cAAE;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACqB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB,CAAC,eAEX7C,OAAA,CAACvB,OAAO;YAAAiE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAEX7C,OAAA,CAAC5B,QAAQ;YAAA8E,QAAA,gBACPlD,OAAA,CAAC3B,YAAY;cAAA6E,QAAA,eACXlD,OAAA,CAACd,iBAAiB;gBAACsE,KAAK,EAAC;cAAS;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB,CAAC,eACf7C,OAAA,CAAC1B,YAAY;cACXyE,OAAO,EAAC,oBAAoB;cAC5BC,SAAS,EAAC;YAA4B;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC,CAAC,eACF7C,OAAA,CAACzB,uBAAuB;cAAA2E,QAAA,eACtBlD,OAAA,CAACxB,MAAM;gBACLiF,OAAO,EAAEpC,mBAAmB,CAACG,IAAK;gBAClCkC,QAAQ,EAAEA,CAAA,KAAMtB,kBAAkB,CAAC,MAAM;cAAE;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACqB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB,CAAC,eAEX7C,OAAA,CAACvB,OAAO;YAAAiE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAEX7C,OAAA,CAAC5B,QAAQ;YAAA8E,QAAA,gBACPlD,OAAA,CAAC3B,YAAY;cAAA6E,QAAA,eACXlD,OAAA,CAACV,OAAO;gBAACkE,KAAK,EAAC;cAAS;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC,eACf7C,OAAA,CAAC1B,YAAY;cACXyE,OAAO,EAAC,mBAAmB;cAC3BC,SAAS,EAAC;YAAyC;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD,CAAC,eACF7C,OAAA,CAACzB,uBAAuB;cAAA2E,QAAA,eACtBlD,OAAA,CAACxB,MAAM;gBACLiF,OAAO,EAAEpC,mBAAmB,CAACI,GAAI;gBACjCiC,QAAQ,EAAEA,CAAA,KAAMtB,kBAAkB,CAAC,KAAK;cAAE;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACqB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGP7C,OAAA,CAAChC,IAAI;MAACmF,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAF,QAAA,eAClBlD,OAAA,CAAC/B,WAAW;QAAAiF,QAAA,gBACVlD,OAAA,CAAC9B,UAAU;UAACmF,OAAO,EAAC,IAAI;UAACE,UAAU,EAAC,KAAK;UAACJ,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAAF,QAAA,EAAC;QAEzD;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAEb7C,OAAA,CAACnB,WAAW;UAAC8E,SAAS;UAACR,EAAE,EAAE;YAAES,QAAQ,EAAE;UAAI,CAAE;UAAAV,QAAA,gBAC3ClD,OAAA,CAAClB,UAAU;YAAAoE,QAAA,EAAC;UAAS;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAClC7C,OAAA,CAACjB,MAAM;YACL8E,KAAK,EAAEnC,SAAU;YACjBoC,KAAK,EAAC,WAAW;YACjBJ,QAAQ,EAAGK,CAAC,IAAKpC,YAAY,CAACoC,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;YAAAX,QAAA,gBAE9ClD,OAAA,CAAChB,QAAQ;cAAC6E,KAAK,EAAC,WAAW;cAAAX,QAAA,EAAC;YAAS;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eAChD7C,OAAA,CAAChB,QAAQ;cAAC6E,KAAK,EAAC,QAAQ;cAAAX,QAAA,EAAC;YAAa;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACjD7C,OAAA,CAAChB,QAAQ;cAAC6E,KAAK,EAAC,OAAO;cAAAX,QAAA,EAAC;YAAY;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eAC/C7C,OAAA,CAAChB,QAAQ;cAAC6E,KAAK,EAAC,QAAQ;cAAAX,QAAA,EAAC;YAAc;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,EAGNN,sBAAsB,CAAC0B,GAAG,CAAC,CAACC,QAAQ,EAAEC,KAAK,kBAC1CnE,OAAA,CAAChC,IAAI;MAAsBmF,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAF,QAAA,eACvClD,OAAA,CAAC/B,WAAW;QAAAiF,QAAA,gBACVlD,OAAA,CAACjC,GAAG;UAACoF,EAAE,EAAE;YAAEiB,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAEjB,EAAE,EAAE;UAAE,CAAE;UAAAF,QAAA,gBACvDrF,KAAK,CAACyG,YAAY,CAACJ,QAAQ,CAACzB,IAAI,EAAE;YAAEU,EAAE,EAAE;cAAEoB,EAAE,EAAE,CAAC;cAAEf,KAAK,EAAE;YAAe;UAAE,CAAC,CAAC,eAC5ExD,OAAA,CAAC9B,UAAU;YAACmF,OAAO,EAAC,IAAI;YAACE,UAAU,EAAC,KAAK;YAAAL,QAAA,EACtCgB,QAAQ,CAAC1B;UAAK;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAEN7C,OAAA,CAAC7B,IAAI;UAAA+E,QAAA,EACFgB,QAAQ,CAACpB,KAAK,CAACmB,GAAG,CAAC,CAACO,IAAI,EAAEC,SAAS,kBAClCzE,OAAA,CAACnC,KAAK,CAAC6G,QAAQ;YAAAxB,QAAA,gBACblD,OAAA,CAAC5B,QAAQ;cAAA8E,QAAA,gBACPlD,OAAA,CAAC1B,YAAY;gBACXyE,OAAO,eACL/C,OAAA,CAACjC,GAAG;kBAACoF,EAAE,EAAE;oBAAEiB,OAAO,EAAE,MAAM;oBAAEC,UAAU,EAAE,QAAQ;oBAAEM,GAAG,EAAE;kBAAE,CAAE;kBAAAzB,QAAA,GACxDsB,IAAI,CAACzB,OAAO,EACZyB,IAAI,CAACvB,SAAS,iBACbjD,OAAA,CAACtB,IAAI;oBACHoF,KAAK,EAAC,WAAW;oBACjBc,IAAI,EAAC,OAAO;oBACZpB,KAAK,EAAC,SAAS;oBACfH,OAAO,EAAC;kBAAU;oBAAAX,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnB,CACF;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CACN;gBACDG,SAAS,EAAEwB,IAAI,CAACxB;cAAU;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B,CAAC,eACF7C,OAAA,CAACzB,uBAAuB;gBAAA2E,QAAA,eACtBlD,OAAA,CAACxB,MAAM;kBACLiF,OAAO,EAAEjD,aAAa,CAACgE,IAAI,CAACzC,GAAG,CAAE;kBACjC2B,QAAQ,EAAEA,CAAA,KAAM5B,wBAAwB,CAAC0C,IAAI,CAACzC,GAAG;gBAAE;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACqB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClB,CAAC,EACV4B,SAAS,GAAGP,QAAQ,CAACpB,KAAK,CAAC+B,MAAM,GAAG,CAAC,iBAAI7E,OAAA,CAACvB,OAAO;cAAAiE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA,GAzBlC2B,IAAI,CAACzC,GAAG;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA0Bb,CACjB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC,GAxCLqB,QAAQ,CAAC1B,KAAK;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAyCnB,CACP,CAAC,eAGF7C,OAAA,CAAChC,IAAI;MAAAkF,QAAA,eACHlD,OAAA,CAAC/B,WAAW;QAAAiF,QAAA,gBACVlD,OAAA,CAAC9B,UAAU;UAACmF,OAAO,EAAC,IAAI;UAACE,UAAU,EAAC,KAAK;UAACJ,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAAF,QAAA,EAAC;QAEzD;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAEb7C,OAAA,CAACjC,GAAG;UAACoF,EAAE,EAAE;YAAEiB,OAAO,EAAE,MAAM;YAAEO,GAAG,EAAE,CAAC;YAAEG,QAAQ,EAAE;UAAO,CAAE;UAAA5B,QAAA,gBACrDlD,OAAA,CAACpB,MAAM;YACLyE,OAAO,EAAC,UAAU;YAClB0B,OAAO,EAAEA,CAAA,KAAM;cACb,MAAMC,UAAU,GAAGC,MAAM,CAACC,WAAW,CACnCD,MAAM,CAACE,IAAI,CAAC3E,aAAa,CAAC,CAACyD,GAAG,CAAClC,GAAG,IAAI,CAACA,GAAG,EAAE,IAAI,CAAC,CACnD,CAAC;cACDtB,gBAAgB,CAACuE,UAAU,CAAC;cAC5BpD,SAAS,CAAC,2BAA2B,EAAE,SAAS,CAAC;YACnD,CAAE;YACFuB,EAAE,EAAE;cAAEiC,YAAY,EAAE;YAAE,CAAE;YAAAlC,QAAA,EACzB;UAED;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAET7C,OAAA,CAACpB,MAAM;YACLyE,OAAO,EAAC,UAAU;YAClB0B,OAAO,EAAEA,CAAA,KAAM;cACb,MAAMM,aAAa,GAAG;gBACpB,GAAGJ,MAAM,CAACC,WAAW,CAACD,MAAM,CAACE,IAAI,CAAC3E,aAAa,CAAC,CAACyD,GAAG,CAAClC,GAAG,IAAI,CAACA,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC;gBAC1ErB,WAAW,EAAE,IAAI;gBACjBC,eAAe,EAAE,IAAI;gBACrBC,kBAAkB,EAAE,IAAI;gBACxBE,cAAc,EAAE;cAClB,CAAC;cACDL,gBAAgB,CAAC4E,aAAa,CAAC;cAC/BzD,SAAS,CAAC,sCAAsC,EAAE,MAAM,CAAC;YAC3D,CAAE;YACFuB,EAAE,EAAE;cAAEiC,YAAY,EAAE;YAAE,CAAE;YAAAlC,QAAA,EACzB;UAED;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAET7C,OAAA,CAACpB,MAAM;YACLyE,OAAO,EAAC,UAAU;YAClBG,KAAK,EAAC,OAAO;YACbuB,OAAO,EAAEA,CAAA,KAAM;cACb,MAAMO,WAAW,GAAGL,MAAM,CAACC,WAAW,CACpCD,MAAM,CAACE,IAAI,CAAC3E,aAAa,CAAC,CAACyD,GAAG,CAAClC,GAAG,IAAI,CAACA,GAAG,EAAE,KAAK,CAAC,CACpD,CAAC;cACDtB,gBAAgB,CAAC6E,WAAW,CAAC;cAC7B1D,SAAS,CAAC,4BAA4B,EAAE,SAAS,CAAC;YACpD,CAAE;YACFuB,EAAE,EAAE;cAAEiC,YAAY,EAAE;YAAE,CAAE;YAAAlC,QAAA,EACzB;UAED;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAAC3C,EAAA,CA7VID,oBAAoB;AAAAsF,EAAA,GAApBtF,oBAAoB;AA+V1B,eAAeA,oBAAoB;AAAC,IAAAsF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}