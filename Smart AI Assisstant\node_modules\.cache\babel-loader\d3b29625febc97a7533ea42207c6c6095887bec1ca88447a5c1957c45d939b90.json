{"ast": null, "code": "var _jsxFileName = \"E:\\\\PythonProjects\\\\React.js\\\\Smart AI Assisstant\\\\src\\\\pages\\\\SmartFeaturesPage.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Container, Box, Typography, Tabs, Tab, Fade, Grid, Card, CardContent, Avatar, Chip } from '@mui/material';\nimport { Psychology as AIIcon, Dashboard as DashboardIcon, AutoMode as AutoIcon, Notifications as NotificationsIcon, Timeline as TimelineIcon, Security as SecurityIcon } from '@mui/icons-material';\nimport SmartDashboard from '../components/SmartDashboard';\nimport ThreatPrediction from '../components/ThreatPrediction';\nimport AutoScanSystem from '../components/AutoScanSystem';\nimport SmartNotifications from '../components/SmartNotifications';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SmartFeaturesPage = /*#__PURE__*/_s(/*#__PURE__*/React.memo(_c = _s(() => {\n  _s();\n  const [activeTab, setActiveTab] = useState(0);\n  const handleTabChange = (event, newValue) => {\n    setActiveTab(newValue);\n  };\n  const features = [{\n    id: 0,\n    title: 'Smart Dashboard',\n    description: 'Real-time security analytics and insights',\n    icon: /*#__PURE__*/_jsxDEV(DashboardIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 40,\n      columnNumber: 13\n    }, this),\n    color: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n    component: /*#__PURE__*/_jsxDEV(SmartDashboard, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 42,\n      columnNumber: 18\n    }, this)\n  }, {\n    id: 1,\n    title: 'AI Threat Prediction',\n    description: 'Machine learning powered threat forecasting',\n    icon: /*#__PURE__*/_jsxDEV(AIIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 48,\n      columnNumber: 13\n    }, this),\n    color: 'linear-gradient(135deg, #9c27b0 0%, #e91e63 100%)',\n    component: /*#__PURE__*/_jsxDEV(ThreatPrediction, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 50,\n      columnNumber: 18\n    }, this)\n  }, {\n    id: 2,\n    title: 'Auto-Scan System',\n    description: 'Intelligent automated security scanning',\n    icon: /*#__PURE__*/_jsxDEV(AutoIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 56,\n      columnNumber: 13\n    }, this),\n    color: 'linear-gradient(135deg, #4caf50 0%, #8bc34a 100%)',\n    component: /*#__PURE__*/_jsxDEV(AutoScanSystem, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 58,\n      columnNumber: 18\n    }, this)\n  }, {\n    id: 3,\n    title: 'Smart Notifications',\n    description: 'AI-powered security alerts and insights',\n    icon: /*#__PURE__*/_jsxDEV(NotificationsIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 64,\n      columnNumber: 13\n    }, this),\n    color: 'linear-gradient(135deg, #2196f3 0%, #9c27b0 100%)',\n    component: /*#__PURE__*/_jsxDEV(SmartNotifications, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 66,\n      columnNumber: 18\n    }, this)\n  }];\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      width: '100%',\n      minHeight: '100vh',\n      display: 'flex',\n      flexDirection: 'column',\n      justifyContent: 'center',\n      alignItems: 'center',\n      py: 4,\n      background: 'linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 50%, #121212 100%)',\n      position: 'relative',\n      '&::before': {\n        content: '\"\"',\n        position: 'absolute',\n        top: 0,\n        left: 0,\n        right: 0,\n        bottom: 0,\n        backgroundImage: `\n            radial-gradient(circle at 25% 25%, rgba(102, 126, 234, 0.1) 0%, transparent 50%),\n            radial-gradient(circle at 75% 75%, rgba(118, 75, 162, 0.1) 0%, transparent 50%),\n            url(\"data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23667eea' fill-opacity='0.02'%3E%3Ccircle cx='30' cy='30' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E\")\n          `,\n        animation: 'particleFloat 25s linear infinite',\n        zIndex: -1\n      },\n      '@keyframes particleFloat': {\n        '0%': {\n          transform: 'translateY(0px) translateX(0px)'\n        },\n        '33%': {\n          transform: 'translateY(-15px) translateX(10px)'\n        },\n        '66%': {\n          transform: 'translateY(10px) translateX(-10px)'\n        },\n        '100%': {\n          transform: 'translateY(0px) translateX(0px)'\n        }\n      }\n    },\n    children: /*#__PURE__*/_jsxDEV(Container, {\n      maxWidth: \"xl\",\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        textAlign: \"center\",\n        mb: 8,\n        sx: {\n          position: 'relative',\n          py: 6,\n          '&::before': {\n            content: '\"\"',\n            position: 'absolute',\n            top: 0,\n            left: 0,\n            right: 0,\n            bottom: 0,\n            background: 'linear-gradient(45deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 25%, rgba(244, 67, 54, 0.05) 50%, rgba(76, 175, 80, 0.05) 75%, rgba(102, 126, 234, 0.05) 100%)',\n            backgroundSize: '400% 400%',\n            animation: 'heroGradient 12s ease infinite',\n            borderRadius: 4,\n            zIndex: -1\n          },\n          '@keyframes heroGradient': {\n            '0%': {\n              backgroundPosition: '0% 50%'\n            },\n            '50%': {\n              backgroundPosition: '100% 50%'\n            },\n            '100%': {\n              backgroundPosition: '0% 50%'\n            }\n          },\n          '@keyframes gradientText': {\n            '0%': {\n              backgroundPosition: '0% 50%'\n            },\n            '50%': {\n              backgroundPosition: '100% 50%'\n            },\n            '100%': {\n              backgroundPosition: '0% 50%'\n            }\n          }\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n            borderRadius: '50%',\n            width: 120,\n            height: 120,\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            mx: 'auto',\n            mb: 4,\n            boxShadow: '0 20px 40px rgba(102, 126, 234, 0.3)',\n            position: 'relative',\n            '&::after': {\n              content: '\"\"',\n              position: 'absolute',\n              top: -10,\n              left: -10,\n              right: -10,\n              bottom: -10,\n              borderRadius: '50%',\n              border: '3px solid',\n              borderColor: 'primary.main',\n              opacity: 0.3,\n              animation: 'pulse 2s infinite'\n            },\n            '@keyframes pulse': {\n              '0%': {\n                transform: 'scale(1)',\n                opacity: 0.3\n              },\n              '50%': {\n                transform: 'scale(1.1)',\n                opacity: 0.1\n              },\n              '100%': {\n                transform: 'scale(1)',\n                opacity: 0.3\n              }\n            }\n          },\n          children: /*#__PURE__*/_jsxDEV(AIIcon, {\n            sx: {\n              fontSize: 60,\n              color: 'white'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 171,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 138,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h1\",\n          component: \"h1\",\n          gutterBottom: true,\n          fontWeight: \"bold\",\n          sx: {\n            background: 'linear-gradient(45deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%)',\n            backgroundSize: '400% 400%',\n            backgroundClip: 'text',\n            WebkitBackgroundClip: 'text',\n            WebkitTextFillColor: 'transparent',\n            animation: 'gradientText 8s ease infinite',\n            mb: 3\n          },\n          children: \"Smart AI Features\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 174,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h4\",\n          color: \"text.secondary\",\n          maxWidth: \"900px\",\n          mx: \"auto\",\n          sx: {\n            lineHeight: 1.6,\n            fontWeight: 300,\n            mb: 6,\n            fontSize: '1.5rem'\n          },\n          children: \"Advanced artificial intelligence capabilities for next-generation cybersecurity\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 192,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'center',\n            gap: 2,\n            flexWrap: 'wrap'\n          },\n          children: ['Real-time Analytics', 'Predictive Intelligence', 'Automated Response', 'Smart Alerts'].map((feature, index) => /*#__PURE__*/_jsxDEV(Chip, {\n            label: feature,\n            variant: \"outlined\",\n            sx: {\n              borderColor: index === 0 ? '#667eea' : index === 1 ? '#9c27b0' : index === 2 ? '#4caf50' : '#2196f3',\n              color: index === 0 ? '#667eea' : index === 1 ? '#9c27b0' : index === 2 ? '#4caf50' : '#2196f3',\n              fontWeight: 600,\n              fontSize: '0.9rem',\n              transition: 'all 0.3s ease',\n              '&:hover': {\n                background: index === 0 ? 'rgba(102, 126, 234, 0.1)' : index === 1 ? 'rgba(156, 39, 176, 0.1)' : index === 2 ? 'rgba(76, 175, 80, 0.1)' : 'rgba(33, 150, 243, 0.1)',\n                transform: 'translateY(-2px) scale(1.05)',\n                boxShadow: '0 4px 12px rgba(0,0,0,0.15)'\n              }\n            }\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 216,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 207,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 107,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 4,\n        mb: 6,\n        children: features.map((feature, index) => /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            onClick: () => setActiveTab(feature.id),\n            sx: {\n              cursor: 'pointer',\n              height: '100%',\n              background: 'linear-gradient(135deg, rgba(12, 12, 12, 0.95) 0%, rgba(26, 26, 26, 0.95) 50%, rgba(45, 45, 45, 0.95) 100%)',\n              backdropFilter: 'blur(25px) saturate(180%)',\n              border: activeTab === feature.id ? '2px solid rgba(102, 126, 234, 0.6)' : '1px solid rgba(255, 255, 255, 0.1)',\n              borderRadius: 4,\n              transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',\n              position: 'relative',\n              overflow: 'hidden',\n              boxShadow: activeTab === feature.id ? '0 20px 60px rgba(102, 126, 234, 0.3)' : '0 8px 32px rgba(0, 0, 0, 0.2)',\n              '&:hover': {\n                transform: 'translateY(-12px) scale(1.05)',\n                boxShadow: '0 30px 80px rgba(102, 126, 234, 0.4)',\n                border: '2px solid rgba(102, 126, 234, 0.8)'\n              },\n              '&::before': {\n                content: '\"\"',\n                position: 'absolute',\n                top: 0,\n                left: 0,\n                right: 0,\n                height: '4px',\n                background: feature.color,\n                backgroundSize: '200% 200%',\n                animation: activeTab === feature.id ? 'gradientShift 3s ease infinite' : 'none'\n              },\n              '@keyframes gradientShift': {\n                '0%': {\n                  backgroundPosition: '0% 50%'\n                },\n                '50%': {\n                  backgroundPosition: '100% 50%'\n                },\n                '100%': {\n                  backgroundPosition: '0% 50%'\n                }\n              }\n            },\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              sx: {\n                p: 3,\n                textAlign: 'center'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Avatar, {\n                sx: {\n                  background: feature.color,\n                  width: 64,\n                  height: 64,\n                  mx: 'auto',\n                  mb: 2,\n                  boxShadow: '0 8px 25px rgba(0,0,0,0.2)'\n                },\n                children: /*#__PURE__*/React.cloneElement(feature.icon, {\n                  sx: {\n                    fontSize: 32\n                  }\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 288,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                fontWeight: \"bold\",\n                gutterBottom: true,\n                children: feature.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 300,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                children: feature.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 303,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 287,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 247,\n            columnNumber: 15\n          }, this)\n        }, feature.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 246,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 244,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Card, {\n        sx: {\n          background: theme => theme.palette.mode === 'dark' ? 'linear-gradient(135deg, rgba(30, 30, 30, 0.95) 0%, rgba(50, 50, 50, 0.95) 100%)' : 'linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%)',\n          backdropFilter: 'blur(20px)',\n          border: theme => `1px solid ${theme.palette.divider}`\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            borderBottom: 1,\n            borderColor: 'divider'\n          },\n          children: /*#__PURE__*/_jsxDEV(Tabs, {\n            value: activeTab,\n            onChange: handleTabChange,\n            variant: \"fullWidth\",\n            textColor: \"primary\",\n            indicatorColor: \"primary\",\n            sx: {\n              '& .MuiTab-root': {\n                minHeight: 72,\n                fontSize: '1rem',\n                fontWeight: 600,\n                textTransform: 'none'\n              }\n            },\n            children: features.map(feature => /*#__PURE__*/_jsxDEV(Tab, {\n              icon: /*#__PURE__*/React.cloneElement(feature.icon, {\n                sx: {\n                  fontSize: 28\n                }\n              }),\n              label: feature.title,\n              iconPosition: \"start\"\n            }, feature.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 339,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 323,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 322,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(CardContent, {\n          sx: {\n            p: 0\n          },\n          children: features.map(feature => /*#__PURE__*/_jsxDEV(Box, {\n            children: activeTab === feature.id && /*#__PURE__*/_jsxDEV(Fade, {\n              in: activeTab === feature.id,\n              timeout: 300,\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                children: feature.component\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 354,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 353,\n              columnNumber: 19\n            }, this)\n          }, feature.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 351,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 349,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 313,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 105,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 71,\n    columnNumber: 5\n  }, this);\n}, \"9RTnV4k8xQ8Z7mmUeyiKoXdA+A8=\")), \"9RTnV4k8xQ8Z7mmUeyiKoXdA+A8=\");\n_c2 = SmartFeaturesPage;\nSmartFeaturesPage.displayName = 'SmartFeaturesPage';\nexport default SmartFeaturesPage;\nvar _c, _c2;\n$RefreshReg$(_c, \"SmartFeaturesPage$React.memo\");\n$RefreshReg$(_c2, \"SmartFeaturesPage\");", "map": {"version": 3, "names": ["React", "useState", "Container", "Box", "Typography", "Tabs", "Tab", "Fade", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Avatar", "Chip", "Psychology", "AIIcon", "Dashboard", "DashboardIcon", "AutoMode", "AutoIcon", "Notifications", "NotificationsIcon", "Timeline", "TimelineIcon", "Security", "SecurityIcon", "SmartDashboard", "ThreatPrediction", "AutoScanSystem", "SmartNotifications", "jsxDEV", "_jsxDEV", "SmartFeaturesPage", "_s", "memo", "_c", "activeTab", "setActiveTab", "handleTabChange", "event", "newValue", "features", "id", "title", "description", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "color", "component", "sx", "width", "minHeight", "display", "flexDirection", "justifyContent", "alignItems", "py", "background", "position", "content", "top", "left", "right", "bottom", "backgroundImage", "animation", "zIndex", "transform", "children", "max<PERSON><PERSON><PERSON>", "textAlign", "mb", "backgroundSize", "borderRadius", "backgroundPosition", "height", "mx", "boxShadow", "border", "borderColor", "opacity", "fontSize", "variant", "gutterBottom", "fontWeight", "backgroundClip", "WebkitBackgroundClip", "WebkitTextFillColor", "lineHeight", "gap", "flexWrap", "map", "feature", "index", "label", "transition", "container", "spacing", "item", "xs", "sm", "md", "onClick", "cursor", "<PERSON><PERSON>ilter", "overflow", "p", "cloneElement", "theme", "palette", "mode", "divider", "borderBottom", "value", "onChange", "textColor", "indicatorColor", "textTransform", "iconPosition", "in", "timeout", "_c2", "displayName", "$RefreshReg$"], "sources": ["E:/PythonProjects/React.js/Smart AI Assisstant/src/pages/SmartFeaturesPage.jsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  Container,\n  <PERSON>,\n  Typography,\n  Tabs,\n  Tab,\n  Fade,\n  Grid,\n  Card,\n  CardContent,\n  Avatar,\n  Chip,\n} from '@mui/material';\nimport {\n  Psychology as AIIcon,\n  Dashboard as DashboardIcon,\n  AutoMode as AutoIcon,\n  Notifications as NotificationsIcon,\n  Timeline as TimelineIcon,\n  Security as SecurityIcon,\n} from '@mui/icons-material';\nimport SmartDashboard from '../components/SmartDashboard';\nimport ThreatPrediction from '../components/ThreatPrediction';\nimport AutoScanSystem from '../components/AutoScanSystem';\nimport SmartNotifications from '../components/SmartNotifications';\n\nconst SmartFeaturesPage = React.memo(() => {\n  const [activeTab, setActiveTab] = useState(0);\n\n  const handleTabChange = (event, newValue) => {\n    setActiveTab(newValue);\n  };\n\n  const features = [\n    {\n      id: 0,\n      title: 'Smart Dashboard',\n      description: 'Real-time security analytics and insights',\n      icon: <DashboardIcon />,\n      color: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n      component: <SmartDashboard />,\n    },\n    {\n      id: 1,\n      title: 'AI Threat Prediction',\n      description: 'Machine learning powered threat forecasting',\n      icon: <AIIcon />,\n      color: 'linear-gradient(135deg, #9c27b0 0%, #e91e63 100%)',\n      component: <ThreatPrediction />,\n    },\n    {\n      id: 2,\n      title: 'Auto-Scan System',\n      description: 'Intelligent automated security scanning',\n      icon: <AutoIcon />,\n      color: 'linear-gradient(135deg, #4caf50 0%, #8bc34a 100%)',\n      component: <AutoScanSystem />,\n    },\n    {\n      id: 3,\n      title: 'Smart Notifications',\n      description: 'AI-powered security alerts and insights',\n      icon: <NotificationsIcon />,\n      color: 'linear-gradient(135deg, #2196f3 0%, #9c27b0 100%)',\n      component: <SmartNotifications />,\n    },\n  ];\n\n  return (\n    <Box\n      sx={{\n        width: '100%',\n        minHeight: '100vh',\n        display: 'flex',\n        flexDirection: 'column',\n        justifyContent: 'center',\n        alignItems: 'center',\n        py: 4,\n        background: 'linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 50%, #121212 100%)',\n        position: 'relative',\n        '&::before': {\n          content: '\"\"',\n          position: 'absolute',\n          top: 0,\n          left: 0,\n          right: 0,\n          bottom: 0,\n          backgroundImage: `\n            radial-gradient(circle at 25% 25%, rgba(102, 126, 234, 0.1) 0%, transparent 50%),\n            radial-gradient(circle at 75% 75%, rgba(118, 75, 162, 0.1) 0%, transparent 50%),\n            url(\"data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23667eea' fill-opacity='0.02'%3E%3Ccircle cx='30' cy='30' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E\")\n          `,\n          animation: 'particleFloat 25s linear infinite',\n          zIndex: -1,\n        },\n        '@keyframes particleFloat': {\n          '0%': { transform: 'translateY(0px) translateX(0px)' },\n          '33%': { transform: 'translateY(-15px) translateX(10px)' },\n          '66%': { transform: 'translateY(10px) translateX(-10px)' },\n          '100%': { transform: 'translateY(0px) translateX(0px)' },\n        },\n      }}\n    >\n      <Container maxWidth=\"xl\">\n        {/* Hero Section */}\n        <Box \n          textAlign=\"center\" \n          mb={8}\n          sx={{\n            position: 'relative',\n            py: 6,\n            '&::before': {\n              content: '\"\"',\n              position: 'absolute',\n              top: 0,\n              left: 0,\n              right: 0,\n              bottom: 0,\n              background: 'linear-gradient(45deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 25%, rgba(244, 67, 54, 0.05) 50%, rgba(76, 175, 80, 0.05) 75%, rgba(102, 126, 234, 0.05) 100%)',\n              backgroundSize: '400% 400%',\n              animation: 'heroGradient 12s ease infinite',\n              borderRadius: 4,\n              zIndex: -1,\n            },\n            '@keyframes heroGradient': {\n              '0%': { backgroundPosition: '0% 50%' },\n              '50%': { backgroundPosition: '100% 50%' },\n              '100%': { backgroundPosition: '0% 50%' },\n            },\n            '@keyframes gradientText': {\n              '0%': { backgroundPosition: '0% 50%' },\n              '50%': { backgroundPosition: '100% 50%' },\n              '100%': { backgroundPosition: '0% 50%' },\n            },\n          }}\n        >\n          <Box\n            sx={{\n              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n              borderRadius: '50%',\n              width: 120,\n              height: 120,\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              mx: 'auto',\n              mb: 4,\n              boxShadow: '0 20px 40px rgba(102, 126, 234, 0.3)',\n              position: 'relative',\n              '&::after': {\n                content: '\"\"',\n                position: 'absolute',\n                top: -10,\n                left: -10,\n                right: -10,\n                bottom: -10,\n                borderRadius: '50%',\n                border: '3px solid',\n                borderColor: 'primary.main',\n                opacity: 0.3,\n                animation: 'pulse 2s infinite',\n              },\n              '@keyframes pulse': {\n                '0%': { transform: 'scale(1)', opacity: 0.3 },\n                '50%': { transform: 'scale(1.1)', opacity: 0.1 },\n                '100%': { transform: 'scale(1)', opacity: 0.3 },\n              },\n            }}\n          >\n            <AIIcon sx={{ fontSize: 60, color: 'white' }} />\n          </Box>\n          \n          <Typography \n            variant=\"h1\" \n            component=\"h1\" \n            gutterBottom \n            fontWeight=\"bold\"\n            sx={{\n              background: 'linear-gradient(45deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%)',\n              backgroundSize: '400% 400%',\n              backgroundClip: 'text',\n              WebkitBackgroundClip: 'text',\n              WebkitTextFillColor: 'transparent',\n              animation: 'gradientText 8s ease infinite',\n              mb: 3,\n            }}\n          >\n            Smart AI Features\n          </Typography>\n          \n          <Typography \n            variant=\"h4\" \n            color=\"text.secondary\" \n            maxWidth=\"900px\" \n            mx=\"auto\"\n            sx={{ \n              lineHeight: 1.6, \n              fontWeight: 300,\n              mb: 6,\n              fontSize: '1.5rem',\n            }}\n          >\n            Advanced artificial intelligence capabilities for next-generation cybersecurity\n          </Typography>\n          \n          <Box\n            sx={{\n              display: 'flex',\n              justifyContent: 'center',\n              gap: 2,\n              flexWrap: 'wrap',\n            }}\n          >\n            {['Real-time Analytics', 'Predictive Intelligence', 'Automated Response', 'Smart Alerts'].map((feature, index) => (\n              <Chip\n                key={index}\n                label={feature}\n                variant=\"outlined\"\n                sx={{\n                  borderColor: index === 0 ? '#667eea' :\n                              index === 1 ? '#9c27b0' :\n                              index === 2 ? '#4caf50' : '#2196f3',\n                  color: index === 0 ? '#667eea' :\n                         index === 1 ? '#9c27b0' :\n                         index === 2 ? '#4caf50' : '#2196f3',\n                  fontWeight: 600,\n                  fontSize: '0.9rem',\n                  transition: 'all 0.3s ease',\n                  '&:hover': {\n                    background: index === 0 ? 'rgba(102, 126, 234, 0.1)' :\n                               index === 1 ? 'rgba(156, 39, 176, 0.1)' :\n                               index === 2 ? 'rgba(76, 175, 80, 0.1)' : 'rgba(33, 150, 243, 0.1)',\n                    transform: 'translateY(-2px) scale(1.05)',\n                    boxShadow: '0 4px 12px rgba(0,0,0,0.15)',\n                  },\n                }}\n              />\n            ))}\n          </Box>\n        </Box>\n\n        {/* Feature Cards Overview */}\n        <Grid container spacing={4} mb={6}>\n          {features.map((feature, index) => (\n            <Grid item xs={12} sm={6} md={3} key={feature.id}>\n              <Card\n                onClick={() => setActiveTab(feature.id)}\n                sx={{\n                  cursor: 'pointer',\n                  height: '100%',\n                  background: 'linear-gradient(135deg, rgba(12, 12, 12, 0.95) 0%, rgba(26, 26, 26, 0.95) 50%, rgba(45, 45, 45, 0.95) 100%)',\n                  backdropFilter: 'blur(25px) saturate(180%)',\n                  border: activeTab === feature.id\n                    ? '2px solid rgba(102, 126, 234, 0.6)'\n                    : '1px solid rgba(255, 255, 255, 0.1)',\n                  borderRadius: 4,\n                  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',\n                  position: 'relative',\n                  overflow: 'hidden',\n                  boxShadow: activeTab === feature.id\n                    ? '0 20px 60px rgba(102, 126, 234, 0.3)'\n                    : '0 8px 32px rgba(0, 0, 0, 0.2)',\n                  '&:hover': {\n                    transform: 'translateY(-12px) scale(1.05)',\n                    boxShadow: '0 30px 80px rgba(102, 126, 234, 0.4)',\n                    border: '2px solid rgba(102, 126, 234, 0.8)',\n                  },\n                  '&::before': {\n                    content: '\"\"',\n                    position: 'absolute',\n                    top: 0,\n                    left: 0,\n                    right: 0,\n                    height: '4px',\n                    background: feature.color,\n                    backgroundSize: '200% 200%',\n                    animation: activeTab === feature.id ? 'gradientShift 3s ease infinite' : 'none',\n                  },\n                  '@keyframes gradientShift': {\n                    '0%': { backgroundPosition: '0% 50%' },\n                    '50%': { backgroundPosition: '100% 50%' },\n                    '100%': { backgroundPosition: '0% 50%' },\n                  },\n                }}\n              >\n                <CardContent sx={{ p: 3, textAlign: 'center' }}>\n                  <Avatar\n                    sx={{\n                      background: feature.color,\n                      width: 64,\n                      height: 64,\n                      mx: 'auto',\n                      mb: 2,\n                      boxShadow: '0 8px 25px rgba(0,0,0,0.2)',\n                    }}\n                  >\n                    {React.cloneElement(feature.icon, { sx: { fontSize: 32 } })}\n                  </Avatar>\n                  <Typography variant=\"h6\" fontWeight=\"bold\" gutterBottom>\n                    {feature.title}\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    {feature.description}\n                  </Typography>\n                </CardContent>\n              </Card>\n            </Grid>\n          ))}\n        </Grid>\n\n        {/* Feature Tabs */}\n        <Card\n          sx={{\n            background: (theme) => theme.palette.mode === 'dark'\n              ? 'linear-gradient(135deg, rgba(30, 30, 30, 0.95) 0%, rgba(50, 50, 50, 0.95) 100%)'\n              : 'linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%)',\n            backdropFilter: 'blur(20px)',\n            border: (theme) => `1px solid ${theme.palette.divider}`,\n          }}\n        >\n          <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>\n            <Tabs\n              value={activeTab}\n              onChange={handleTabChange}\n              variant=\"fullWidth\"\n              textColor=\"primary\"\n              indicatorColor=\"primary\"\n              sx={{\n                '& .MuiTab-root': {\n                  minHeight: 72,\n                  fontSize: '1rem',\n                  fontWeight: 600,\n                  textTransform: 'none',\n                },\n              }}\n            >\n              {features.map((feature) => (\n                <Tab\n                  key={feature.id}\n                  icon={React.cloneElement(feature.icon, { sx: { fontSize: 28 } })}\n                  label={feature.title}\n                  iconPosition=\"start\"\n                />\n              ))}\n            </Tabs>\n          </Box>\n\n          <CardContent sx={{ p: 0 }}>\n            {features.map((feature) => (\n              <Box key={feature.id}>\n                {activeTab === feature.id && (\n                  <Fade in={activeTab === feature.id} timeout={300}>\n                    <Box>\n                      {feature.component}\n                    </Box>\n                  </Fade>\n                )}\n              </Box>\n            ))}\n          </CardContent>\n        </Card>\n      </Container>\n    </Box>\n  );\n});\n\nSmartFeaturesPage.displayName = 'SmartFeaturesPage';\n\nexport default SmartFeaturesPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,SAAS,EACTC,GAAG,EACHC,UAAU,EACVC,IAAI,EACJC,GAAG,EACHC,IAAI,EACJC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,MAAM,EACNC,IAAI,QACC,eAAe;AACtB,SACEC,UAAU,IAAIC,MAAM,EACpBC,SAAS,IAAIC,aAAa,EAC1BC,QAAQ,IAAIC,QAAQ,EACpBC,aAAa,IAAIC,iBAAiB,EAClCC,QAAQ,IAAIC,YAAY,EACxBC,QAAQ,IAAIC,YAAY,QACnB,qBAAqB;AAC5B,OAAOC,cAAc,MAAM,8BAA8B;AACzD,OAAOC,gBAAgB,MAAM,gCAAgC;AAC7D,OAAOC,cAAc,MAAM,8BAA8B;AACzD,OAAOC,kBAAkB,MAAM,kCAAkC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElE,MAAMC,iBAAiB,gBAAAC,EAAA,cAAGhC,KAAK,CAACiC,IAAI,CAAAC,EAAA,GAAAF,EAAA,CAAC,MAAM;EAAAA,EAAA;EACzC,MAAM,CAACG,SAAS,EAAEC,YAAY,CAAC,GAAGnC,QAAQ,CAAC,CAAC,CAAC;EAE7C,MAAMoC,eAAe,GAAGA,CAACC,KAAK,EAAEC,QAAQ,KAAK;IAC3CH,YAAY,CAACG,QAAQ,CAAC;EACxB,CAAC;EAED,MAAMC,QAAQ,GAAG,CACf;IACEC,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,iBAAiB;IACxBC,WAAW,EAAE,2CAA2C;IACxDC,IAAI,eAAEd,OAAA,CAACd,aAAa;MAAA6B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACvBC,KAAK,EAAE,mDAAmD;IAC1DC,SAAS,eAAEpB,OAAA,CAACL,cAAc;MAAAoB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAC9B,CAAC,EACD;IACEP,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,sBAAsB;IAC7BC,WAAW,EAAE,6CAA6C;IAC1DC,IAAI,eAAEd,OAAA,CAAChB,MAAM;MAAA+B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAChBC,KAAK,EAAE,mDAAmD;IAC1DC,SAAS,eAAEpB,OAAA,CAACJ,gBAAgB;MAAAmB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAChC,CAAC,EACD;IACEP,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,kBAAkB;IACzBC,WAAW,EAAE,yCAAyC;IACtDC,IAAI,eAAEd,OAAA,CAACZ,QAAQ;MAAA2B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAClBC,KAAK,EAAE,mDAAmD;IAC1DC,SAAS,eAAEpB,OAAA,CAACH,cAAc;MAAAkB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAC9B,CAAC,EACD;IACEP,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,qBAAqB;IAC5BC,WAAW,EAAE,yCAAyC;IACtDC,IAAI,eAAEd,OAAA,CAACV,iBAAiB;MAAAyB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC3BC,KAAK,EAAE,mDAAmD;IAC1DC,SAAS,eAAEpB,OAAA,CAACF,kBAAkB;MAAAiB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAClC,CAAC,CACF;EAED,oBACElB,OAAA,CAAC3B,GAAG;IACFgD,EAAE,EAAE;MACFC,KAAK,EAAE,MAAM;MACbC,SAAS,EAAE,OAAO;MAClBC,OAAO,EAAE,MAAM;MACfC,aAAa,EAAE,QAAQ;MACvBC,cAAc,EAAE,QAAQ;MACxBC,UAAU,EAAE,QAAQ;MACpBC,EAAE,EAAE,CAAC;MACLC,UAAU,EAAE,gEAAgE;MAC5EC,QAAQ,EAAE,UAAU;MACpB,WAAW,EAAE;QACXC,OAAO,EAAE,IAAI;QACbD,QAAQ,EAAE,UAAU;QACpBE,GAAG,EAAE,CAAC;QACNC,IAAI,EAAE,CAAC;QACPC,KAAK,EAAE,CAAC;QACRC,MAAM,EAAE,CAAC;QACTC,eAAe,EAAE;AAC3B;AACA;AACA;AACA,WAAW;QACDC,SAAS,EAAE,mCAAmC;QAC9CC,MAAM,EAAE,CAAC;MACX,CAAC;MACD,0BAA0B,EAAE;QAC1B,IAAI,EAAE;UAAEC,SAAS,EAAE;QAAkC,CAAC;QACtD,KAAK,EAAE;UAAEA,SAAS,EAAE;QAAqC,CAAC;QAC1D,KAAK,EAAE;UAAEA,SAAS,EAAE;QAAqC,CAAC;QAC1D,MAAM,EAAE;UAAEA,SAAS,EAAE;QAAkC;MACzD;IACF,CAAE;IAAAC,QAAA,eAEFxC,OAAA,CAAC5B,SAAS;MAACqE,QAAQ,EAAC,IAAI;MAAAD,QAAA,gBAEtBxC,OAAA,CAAC3B,GAAG;QACFqE,SAAS,EAAC,QAAQ;QAClBC,EAAE,EAAE,CAAE;QACNtB,EAAE,EAAE;UACFS,QAAQ,EAAE,UAAU;UACpBF,EAAE,EAAE,CAAC;UACL,WAAW,EAAE;YACXG,OAAO,EAAE,IAAI;YACbD,QAAQ,EAAE,UAAU;YACpBE,GAAG,EAAE,CAAC;YACNC,IAAI,EAAE,CAAC;YACPC,KAAK,EAAE,CAAC;YACRC,MAAM,EAAE,CAAC;YACTN,UAAU,EAAE,8KAA8K;YAC1Le,cAAc,EAAE,WAAW;YAC3BP,SAAS,EAAE,gCAAgC;YAC3CQ,YAAY,EAAE,CAAC;YACfP,MAAM,EAAE,CAAC;UACX,CAAC;UACD,yBAAyB,EAAE;YACzB,IAAI,EAAE;cAAEQ,kBAAkB,EAAE;YAAS,CAAC;YACtC,KAAK,EAAE;cAAEA,kBAAkB,EAAE;YAAW,CAAC;YACzC,MAAM,EAAE;cAAEA,kBAAkB,EAAE;YAAS;UACzC,CAAC;UACD,yBAAyB,EAAE;YACzB,IAAI,EAAE;cAAEA,kBAAkB,EAAE;YAAS,CAAC;YACtC,KAAK,EAAE;cAAEA,kBAAkB,EAAE;YAAW,CAAC;YACzC,MAAM,EAAE;cAAEA,kBAAkB,EAAE;YAAS;UACzC;QACF,CAAE;QAAAN,QAAA,gBAEFxC,OAAA,CAAC3B,GAAG;UACFgD,EAAE,EAAE;YACFQ,UAAU,EAAE,mDAAmD;YAC/DgB,YAAY,EAAE,KAAK;YACnBvB,KAAK,EAAE,GAAG;YACVyB,MAAM,EAAE,GAAG;YACXvB,OAAO,EAAE,MAAM;YACfG,UAAU,EAAE,QAAQ;YACpBD,cAAc,EAAE,QAAQ;YACxBsB,EAAE,EAAE,MAAM;YACVL,EAAE,EAAE,CAAC;YACLM,SAAS,EAAE,sCAAsC;YACjDnB,QAAQ,EAAE,UAAU;YACpB,UAAU,EAAE;cACVC,OAAO,EAAE,IAAI;cACbD,QAAQ,EAAE,UAAU;cACpBE,GAAG,EAAE,CAAC,EAAE;cACRC,IAAI,EAAE,CAAC,EAAE;cACTC,KAAK,EAAE,CAAC,EAAE;cACVC,MAAM,EAAE,CAAC,EAAE;cACXU,YAAY,EAAE,KAAK;cACnBK,MAAM,EAAE,WAAW;cACnBC,WAAW,EAAE,cAAc;cAC3BC,OAAO,EAAE,GAAG;cACZf,SAAS,EAAE;YACb,CAAC;YACD,kBAAkB,EAAE;cAClB,IAAI,EAAE;gBAAEE,SAAS,EAAE,UAAU;gBAAEa,OAAO,EAAE;cAAI,CAAC;cAC7C,KAAK,EAAE;gBAAEb,SAAS,EAAE,YAAY;gBAAEa,OAAO,EAAE;cAAI,CAAC;cAChD,MAAM,EAAE;gBAAEb,SAAS,EAAE,UAAU;gBAAEa,OAAO,EAAE;cAAI;YAChD;UACF,CAAE;UAAAZ,QAAA,eAEFxC,OAAA,CAAChB,MAAM;YAACqC,EAAE,EAAE;cAAEgC,QAAQ,EAAE,EAAE;cAAElC,KAAK,EAAE;YAAQ;UAAE;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C,CAAC,eAENlB,OAAA,CAAC1B,UAAU;UACTgF,OAAO,EAAC,IAAI;UACZlC,SAAS,EAAC,IAAI;UACdmC,YAAY;UACZC,UAAU,EAAC,MAAM;UACjBnC,EAAE,EAAE;YACFQ,UAAU,EAAE,yFAAyF;YACrGe,cAAc,EAAE,WAAW;YAC3Ba,cAAc,EAAE,MAAM;YACtBC,oBAAoB,EAAE,MAAM;YAC5BC,mBAAmB,EAAE,aAAa;YAClCtB,SAAS,EAAE,+BAA+B;YAC1CM,EAAE,EAAE;UACN,CAAE;UAAAH,QAAA,EACH;QAED;UAAAzB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAEblB,OAAA,CAAC1B,UAAU;UACTgF,OAAO,EAAC,IAAI;UACZnC,KAAK,EAAC,gBAAgB;UACtBsB,QAAQ,EAAC,OAAO;UAChBO,EAAE,EAAC,MAAM;UACT3B,EAAE,EAAE;YACFuC,UAAU,EAAE,GAAG;YACfJ,UAAU,EAAE,GAAG;YACfb,EAAE,EAAE,CAAC;YACLU,QAAQ,EAAE;UACZ,CAAE;UAAAb,QAAA,EACH;QAED;UAAAzB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAEblB,OAAA,CAAC3B,GAAG;UACFgD,EAAE,EAAE;YACFG,OAAO,EAAE,MAAM;YACfE,cAAc,EAAE,QAAQ;YACxBmC,GAAG,EAAE,CAAC;YACNC,QAAQ,EAAE;UACZ,CAAE;UAAAtB,QAAA,EAED,CAAC,qBAAqB,EAAE,yBAAyB,EAAE,oBAAoB,EAAE,cAAc,CAAC,CAACuB,GAAG,CAAC,CAACC,OAAO,EAAEC,KAAK,kBAC3GjE,OAAA,CAAClB,IAAI;YAEHoF,KAAK,EAAEF,OAAQ;YACfV,OAAO,EAAC,UAAU;YAClBjC,EAAE,EAAE;cACF8B,WAAW,EAAEc,KAAK,KAAK,CAAC,GAAG,SAAS,GACxBA,KAAK,KAAK,CAAC,GAAG,SAAS,GACvBA,KAAK,KAAK,CAAC,GAAG,SAAS,GAAG,SAAS;cAC/C9C,KAAK,EAAE8C,KAAK,KAAK,CAAC,GAAG,SAAS,GACvBA,KAAK,KAAK,CAAC,GAAG,SAAS,GACvBA,KAAK,KAAK,CAAC,GAAG,SAAS,GAAG,SAAS;cAC1CT,UAAU,EAAE,GAAG;cACfH,QAAQ,EAAE,QAAQ;cAClBc,UAAU,EAAE,eAAe;cAC3B,SAAS,EAAE;gBACTtC,UAAU,EAAEoC,KAAK,KAAK,CAAC,GAAG,0BAA0B,GACzCA,KAAK,KAAK,CAAC,GAAG,yBAAyB,GACvCA,KAAK,KAAK,CAAC,GAAG,wBAAwB,GAAG,yBAAyB;gBAC7E1B,SAAS,EAAE,8BAA8B;gBACzCU,SAAS,EAAE;cACb;YACF;UAAE,GApBGgB,KAAK;YAAAlD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAqBX,CACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNlB,OAAA,CAACtB,IAAI;QAAC0F,SAAS;QAACC,OAAO,EAAE,CAAE;QAAC1B,EAAE,EAAE,CAAE;QAAAH,QAAA,EAC/B9B,QAAQ,CAACqD,GAAG,CAAC,CAACC,OAAO,EAAEC,KAAK,kBAC3BjE,OAAA,CAACtB,IAAI;UAAC4F,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAjC,QAAA,eAC9BxC,OAAA,CAACrB,IAAI;YACH+F,OAAO,EAAEA,CAAA,KAAMpE,YAAY,CAAC0D,OAAO,CAACrD,EAAE,CAAE;YACxCU,EAAE,EAAE;cACFsD,MAAM,EAAE,SAAS;cACjB5B,MAAM,EAAE,MAAM;cACdlB,UAAU,EAAE,6GAA6G;cACzH+C,cAAc,EAAE,2BAA2B;cAC3C1B,MAAM,EAAE7C,SAAS,KAAK2D,OAAO,CAACrD,EAAE,GAC5B,oCAAoC,GACpC,oCAAoC;cACxCkC,YAAY,EAAE,CAAC;cACfsB,UAAU,EAAE,uCAAuC;cACnDrC,QAAQ,EAAE,UAAU;cACpB+C,QAAQ,EAAE,QAAQ;cAClB5B,SAAS,EAAE5C,SAAS,KAAK2D,OAAO,CAACrD,EAAE,GAC/B,sCAAsC,GACtC,+BAA+B;cACnC,SAAS,EAAE;gBACT4B,SAAS,EAAE,+BAA+B;gBAC1CU,SAAS,EAAE,sCAAsC;gBACjDC,MAAM,EAAE;cACV,CAAC;cACD,WAAW,EAAE;gBACXnB,OAAO,EAAE,IAAI;gBACbD,QAAQ,EAAE,UAAU;gBACpBE,GAAG,EAAE,CAAC;gBACNC,IAAI,EAAE,CAAC;gBACPC,KAAK,EAAE,CAAC;gBACRa,MAAM,EAAE,KAAK;gBACblB,UAAU,EAAEmC,OAAO,CAAC7C,KAAK;gBACzByB,cAAc,EAAE,WAAW;gBAC3BP,SAAS,EAAEhC,SAAS,KAAK2D,OAAO,CAACrD,EAAE,GAAG,gCAAgC,GAAG;cAC3E,CAAC;cACD,0BAA0B,EAAE;gBAC1B,IAAI,EAAE;kBAAEmC,kBAAkB,EAAE;gBAAS,CAAC;gBACtC,KAAK,EAAE;kBAAEA,kBAAkB,EAAE;gBAAW,CAAC;gBACzC,MAAM,EAAE;kBAAEA,kBAAkB,EAAE;gBAAS;cACzC;YACF,CAAE;YAAAN,QAAA,eAEFxC,OAAA,CAACpB,WAAW;cAACyC,EAAE,EAAE;gBAAEyD,CAAC,EAAE,CAAC;gBAAEpC,SAAS,EAAE;cAAS,CAAE;cAAAF,QAAA,gBAC7CxC,OAAA,CAACnB,MAAM;gBACLwC,EAAE,EAAE;kBACFQ,UAAU,EAAEmC,OAAO,CAAC7C,KAAK;kBACzBG,KAAK,EAAE,EAAE;kBACTyB,MAAM,EAAE,EAAE;kBACVC,EAAE,EAAE,MAAM;kBACVL,EAAE,EAAE,CAAC;kBACLM,SAAS,EAAE;gBACb,CAAE;gBAAAT,QAAA,eAEDtE,KAAK,CAAC6G,YAAY,CAACf,OAAO,CAAClD,IAAI,EAAE;kBAAEO,EAAE,EAAE;oBAAEgC,QAAQ,EAAE;kBAAG;gBAAE,CAAC;cAAC;gBAAAtC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrD,CAAC,eACTlB,OAAA,CAAC1B,UAAU;gBAACgF,OAAO,EAAC,IAAI;gBAACE,UAAU,EAAC,MAAM;gBAACD,YAAY;gBAAAf,QAAA,EACpDwB,OAAO,CAACpD;cAAK;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACblB,OAAA,CAAC1B,UAAU;gBAACgF,OAAO,EAAC,OAAO;gBAACnC,KAAK,EAAC,gBAAgB;gBAAAqB,QAAA,EAC/CwB,OAAO,CAACnD;cAAW;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC,GA7D6B8C,OAAO,CAACrD,EAAE;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA8D1C,CACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGPlB,OAAA,CAACrB,IAAI;QACH0C,EAAE,EAAE;UACFQ,UAAU,EAAGmD,KAAK,IAAKA,KAAK,CAACC,OAAO,CAACC,IAAI,KAAK,MAAM,GAChD,iFAAiF,GACjF,uFAAuF;UAC3FN,cAAc,EAAE,YAAY;UAC5B1B,MAAM,EAAG8B,KAAK,IAAK,aAAaA,KAAK,CAACC,OAAO,CAACE,OAAO;QACvD,CAAE;QAAA3C,QAAA,gBAEFxC,OAAA,CAAC3B,GAAG;UAACgD,EAAE,EAAE;YAAE+D,YAAY,EAAE,CAAC;YAAEjC,WAAW,EAAE;UAAU,CAAE;UAAAX,QAAA,eACnDxC,OAAA,CAACzB,IAAI;YACH8G,KAAK,EAAEhF,SAAU;YACjBiF,QAAQ,EAAE/E,eAAgB;YAC1B+C,OAAO,EAAC,WAAW;YACnBiC,SAAS,EAAC,SAAS;YACnBC,cAAc,EAAC,SAAS;YACxBnE,EAAE,EAAE;cACF,gBAAgB,EAAE;gBAChBE,SAAS,EAAE,EAAE;gBACb8B,QAAQ,EAAE,MAAM;gBAChBG,UAAU,EAAE,GAAG;gBACfiC,aAAa,EAAE;cACjB;YACF,CAAE;YAAAjD,QAAA,EAED9B,QAAQ,CAACqD,GAAG,CAAEC,OAAO,iBACpBhE,OAAA,CAACxB,GAAG;cAEFsC,IAAI,eAAE5C,KAAK,CAAC6G,YAAY,CAACf,OAAO,CAAClD,IAAI,EAAE;gBAAEO,EAAE,EAAE;kBAAEgC,QAAQ,EAAE;gBAAG;cAAE,CAAC,CAAE;cACjEa,KAAK,EAAEF,OAAO,CAACpD,KAAM;cACrB8E,YAAY,EAAC;YAAO,GAHf1B,OAAO,CAACrD,EAAE;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAIhB,CACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAENlB,OAAA,CAACpB,WAAW;UAACyC,EAAE,EAAE;YAAEyD,CAAC,EAAE;UAAE,CAAE;UAAAtC,QAAA,EACvB9B,QAAQ,CAACqD,GAAG,CAAEC,OAAO,iBACpBhE,OAAA,CAAC3B,GAAG;YAAAmE,QAAA,EACDnC,SAAS,KAAK2D,OAAO,CAACrD,EAAE,iBACvBX,OAAA,CAACvB,IAAI;cAACkH,EAAE,EAAEtF,SAAS,KAAK2D,OAAO,CAACrD,EAAG;cAACiF,OAAO,EAAE,GAAI;cAAApD,QAAA,eAC/CxC,OAAA,CAAC3B,GAAG;gBAAAmE,QAAA,EACDwB,OAAO,CAAC5C;cAAS;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UACP,GAPO8C,OAAO,CAACrD,EAAE;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAQf,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACS,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACT,CAAC;AAEV,CAAC,kCAAC;AAAC2E,GAAA,GAlVG5F,iBAAiB;AAoVvBA,iBAAiB,CAAC6F,WAAW,GAAG,mBAAmB;AAEnD,eAAe7F,iBAAiB;AAAC,IAAAG,EAAA,EAAAyF,GAAA;AAAAE,YAAA,CAAA3F,EAAA;AAAA2F,YAAA,CAAAF,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}