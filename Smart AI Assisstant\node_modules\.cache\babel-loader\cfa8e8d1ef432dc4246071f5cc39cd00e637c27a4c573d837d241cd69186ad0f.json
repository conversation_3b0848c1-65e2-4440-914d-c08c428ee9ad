{"ast": null, "code": "var _jsxFileName = \"E:\\\\PythonProjects\\\\React.js\\\\Smart AI Assisstant\\\\src\\\\components\\\\AuthenticationForms.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Box, Card, CardContent, Typography, TextField, Button, IconButton, Alert, CircularProgress, Tabs, Tab, Fade } from '@mui/material';\nimport { Visibility as VisibilityIcon, VisibilityOff as VisibilityOffIcon, Email as EmailIcon, Lock as LockIcon, Person as PersonIcon } from '@mui/icons-material';\nimport { useAuth } from '../contexts/AuthContext';\nimport authService from '../services/auth';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuthenticationForms = () => {\n  _s();\n  const [activeTab, setActiveTab] = useState(0); // 0 = login, 1 = signup\n  const [showPassword, setShowPassword] = useState(false);\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [formErrors, setFormErrors] = useState({});\n  const [alert, setAlert] = useState({\n    show: false,\n    message: '',\n    severity: 'success'\n  });\n  const [loginForm, setLoginForm] = useState({\n    email: '',\n    password: ''\n  });\n  const [signupForm, setSignupForm] = useState({\n    username: '',\n    email: '',\n    password: '',\n    confirmPassword: ''\n  });\n  const {\n    login,\n    signup,\n    error,\n    clearError\n  } = useAuth();\n  const showAlert = (message, severity = 'success') => {\n    setAlert({\n      show: true,\n      message,\n      severity\n    });\n    setTimeout(() => setAlert({\n      show: false,\n      message: '',\n      severity: 'success'\n    }), 5000);\n  };\n  const validateLoginForm = () => {\n    const errors = {};\n    if (!loginForm.email) {\n      errors.email = 'Email is required';\n    } else if (!authService.validateEmail(loginForm.email)) {\n      errors.email = 'Please enter a valid email address';\n    }\n    if (!loginForm.password) {\n      errors.password = 'Password is required';\n    }\n    setFormErrors(errors);\n    return Object.keys(errors).length === 0;\n  };\n  const validateSignupForm = () => {\n    const errors = {};\n    if (!signupForm.username) {\n      errors.username = 'Username is required';\n    } else if (signupForm.username.length < 3) {\n      errors.username = 'Username must be at least 3 characters long';\n    } else if (!/^[a-zA-Z0-9_]+$/.test(signupForm.username)) {\n      errors.username = 'Username can only contain letters, numbers, and underscores';\n    }\n    if (!signupForm.email) {\n      errors.email = 'Email is required';\n    } else if (!authService.validateEmail(signupForm.email)) {\n      errors.email = 'Please enter a valid email address';\n    }\n    if (!signupForm.password) {\n      errors.password = 'Password is required';\n    } else {\n      const passwordValidation = authService.validatePassword(signupForm.password);\n      if (!passwordValidation.isValid) {\n        errors.password = passwordValidation.errors[0];\n      }\n    }\n    if (!signupForm.confirmPassword) {\n      errors.confirmPassword = 'Please confirm your password';\n    } else if (signupForm.password !== signupForm.confirmPassword) {\n      errors.confirmPassword = 'Passwords do not match';\n    }\n    setFormErrors(errors);\n    return Object.keys(errors).length === 0;\n  };\n  const handleLogin = async e => {\n    e.preventDefault();\n    if (!validateLoginForm()) return;\n    setIsSubmitting(true);\n    clearError();\n    try {\n      const result = await login(loginForm);\n      if (result.success) {\n        showAlert('Login successful! Welcome back.', 'success');\n        setLoginForm({\n          email: '',\n          password: ''\n        });\n      } else {\n        showAlert(result.error || 'Login failed. Please try again.', 'error');\n      }\n    } catch (error) {\n      showAlert('An unexpected error occurred. Please try again.', 'error');\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n  const handleSignup = async e => {\n    e.preventDefault();\n    if (!validateSignupForm()) return;\n    setIsSubmitting(true);\n    clearError();\n    try {\n      const result = await signup({\n        username: signupForm.username,\n        email: signupForm.email,\n        password: signupForm.password,\n        confirmPassword: signupForm.confirmPassword\n      });\n      if (result.success) {\n        showAlert('Account created successfully! Welcome to AI Security Guard.', 'success');\n        setSignupForm({\n          username: '',\n          email: '',\n          password: '',\n          confirmPassword: ''\n        });\n      } else {\n        showAlert(result.error || 'Registration failed. Please try again.', 'error');\n      }\n    } catch (error) {\n      showAlert('An unexpected error occurred. Please try again.', 'error');\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n  const handleTabChange = (event, newValue) => {\n    setActiveTab(newValue);\n    setFormErrors({});\n    clearError();\n    setAlert({\n      show: false,\n      message: '',\n      severity: 'success'\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      maxWidth: 500,\n      mx: 'auto'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Fade, {\n      in: alert.show,\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        severity: alert.severity,\n        sx: {\n          mb: 3\n        },\n        children: alert.message\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 165,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 164,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"error\",\n      sx: {\n        mb: 3\n      },\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 171,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        background: theme => theme.palette.mode === 'dark' ? 'linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%)' : 'linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%)',\n        border: '1px solid rgba(102, 126, 234, 0.2)',\n        borderRadius: 4\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          borderBottom: 1,\n          borderColor: 'divider'\n        },\n        children: /*#__PURE__*/_jsxDEV(Tabs, {\n          value: activeTab,\n          onChange: handleTabChange,\n          variant: \"fullWidth\",\n          textColor: \"primary\",\n          indicatorColor: \"primary\",\n          sx: {\n            '& .MuiTab-root': {\n              minHeight: 60,\n              fontSize: '1rem',\n              fontWeight: 600,\n              textTransform: 'none',\n              transition: 'all 0.3s ease',\n              '&:hover': {\n                background: theme => theme.palette.mode === 'dark' ? 'rgba(102, 126, 234, 0.1)' : 'rgba(102, 126, 234, 0.05)'\n              },\n              '&.Mui-selected': {\n                background: theme => theme.palette.mode === 'dark' ? 'linear-gradient(135deg, rgba(102, 126, 234, 0.2) 0%, rgba(118, 75, 162, 0.2) 100%)' : 'linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%)'\n              }\n            },\n            '& .MuiTabs-indicator': {\n              height: 3,\n              borderRadius: '3px 3px 0 0',\n              background: 'linear-gradient(90deg, #667eea 0%, #764ba2 100%)'\n            }\n          },\n          children: [/*#__PURE__*/_jsxDEV(Tab, {\n            label: \"Sign In\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Tab, {\n            label: \"Create Account\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 219,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 187,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 186,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(CardContent, {\n        sx: {\n          p: 4\n        },\n        children: [activeTab === 0 && /*#__PURE__*/_jsxDEV(Box, {\n          component: \"form\",\n          onSubmit: handleLogin,\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h5\",\n            gutterBottom: true,\n            textAlign: \"center\",\n            fontWeight: \"bold\",\n            children: \"Welcome Back\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 227,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            textAlign: \"center\",\n            sx: {\n              mb: 3\n            },\n            children: \"Sign in to access your AI Security Guard account\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 230,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"Email\",\n            type: \"email\",\n            value: loginForm.email,\n            onChange: e => {\n              setLoginForm({\n                ...loginForm,\n                email: e.target.value\n              });\n              if (formErrors.email) {\n                setFormErrors({\n                  ...formErrors,\n                  email: ''\n                });\n              }\n            },\n            margin: \"normal\",\n            variant: \"outlined\",\n            autoComplete: \"email\",\n            error: !!formErrors.email,\n            helperText: formErrors.email,\n            disabled: isSubmitting,\n            sx: {\n              '& .MuiOutlinedInput-root': {\n                borderRadius: 3\n              }\n            },\n            InputProps: {\n              startAdornment: /*#__PURE__*/_jsxDEV(EmailIcon, {\n                sx: {\n                  mr: 1,\n                  color: 'text.secondary'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 257,\n                columnNumber: 35\n              }, this)\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 234,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"Password\",\n            type: showPassword ? 'text' : 'password',\n            value: loginForm.password,\n            onChange: e => {\n              setLoginForm({\n                ...loginForm,\n                password: e.target.value\n              });\n              if (formErrors.password) {\n                setFormErrors({\n                  ...formErrors,\n                  password: ''\n                });\n              }\n            },\n            margin: \"normal\",\n            variant: \"outlined\",\n            autoComplete: \"current-password\",\n            error: !!formErrors.password,\n            helperText: formErrors.password,\n            disabled: isSubmitting,\n            sx: {\n              '& .MuiOutlinedInput-root': {\n                borderRadius: 3\n              }\n            },\n            InputProps: {\n              startAdornment: /*#__PURE__*/_jsxDEV(LockIcon, {\n                sx: {\n                  mr: 1,\n                  color: 'text.secondary'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 284,\n                columnNumber: 35\n              }, this),\n              endAdornment: /*#__PURE__*/_jsxDEV(IconButton, {\n                onClick: () => setShowPassword(!showPassword),\n                children: showPassword ? /*#__PURE__*/_jsxDEV(VisibilityOffIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 287,\n                  columnNumber: 39\n                }, this) : /*#__PURE__*/_jsxDEV(VisibilityIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 287,\n                  columnNumber: 63\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 286,\n                columnNumber: 21\n              }, this)\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 261,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            type: \"submit\",\n            fullWidth: true,\n            variant: \"contained\",\n            disabled: isSubmitting,\n            sx: {\n              mt: 3,\n              mb: 2,\n              borderRadius: 3,\n              py: 1.5,\n              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n              '&:hover': {\n                background: 'linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)'\n              }\n            },\n            children: isSubmitting ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 24,\n              color: \"inherit\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 310,\n              columnNumber: 19\n            }, this) : 'Sign In'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 293,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 226,\n          columnNumber: 13\n        }, this), activeTab === 1 && /*#__PURE__*/_jsxDEV(Box, {\n          component: \"form\",\n          onSubmit: handleSignup,\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h5\",\n            gutterBottom: true,\n            textAlign: \"center\",\n            fontWeight: \"bold\",\n            children: \"Create Account\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 321,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            textAlign: \"center\",\n            sx: {\n              mb: 3\n            },\n            children: \"Join AI Security Guard to protect your digital assets\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 324,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"Username\",\n            value: signupForm.username,\n            onChange: e => {\n              setSignupForm({\n                ...signupForm,\n                username: e.target.value\n              });\n              if (formErrors.username) {\n                setFormErrors({\n                  ...formErrors,\n                  username: ''\n                });\n              }\n            },\n            margin: \"normal\",\n            variant: \"outlined\",\n            autoComplete: \"username\",\n            error: !!formErrors.username,\n            helperText: formErrors.username,\n            disabled: isSubmitting,\n            sx: {\n              '& .MuiOutlinedInput-root': {\n                borderRadius: 3\n              }\n            },\n            InputProps: {\n              startAdornment: /*#__PURE__*/_jsxDEV(PersonIcon, {\n                sx: {\n                  mr: 1,\n                  color: 'text.secondary'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 350,\n                columnNumber: 35\n              }, this)\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 328,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"Email\",\n            type: \"email\",\n            value: signupForm.email,\n            onChange: e => {\n              setSignupForm({\n                ...signupForm,\n                email: e.target.value\n              });\n              if (formErrors.email) {\n                setFormErrors({\n                  ...formErrors,\n                  email: ''\n                });\n              }\n            },\n            margin: \"normal\",\n            variant: \"outlined\",\n            autoComplete: \"email\",\n            error: !!formErrors.email,\n            helperText: formErrors.email,\n            disabled: isSubmitting,\n            sx: {\n              '& .MuiOutlinedInput-root': {\n                borderRadius: 3\n              }\n            },\n            InputProps: {\n              startAdornment: /*#__PURE__*/_jsxDEV(EmailIcon, {\n                sx: {\n                  mr: 1,\n                  color: 'text.secondary'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 377,\n                columnNumber: 35\n              }, this)\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 354,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"Password\",\n            type: showPassword ? 'text' : 'password',\n            value: signupForm.password,\n            onChange: e => {\n              setSignupForm({\n                ...signupForm,\n                password: e.target.value\n              });\n              if (formErrors.password) {\n                setFormErrors({\n                  ...formErrors,\n                  password: ''\n                });\n              }\n            },\n            margin: \"normal\",\n            variant: \"outlined\",\n            autoComplete: \"new-password\",\n            error: !!formErrors.password,\n            helperText: formErrors.password,\n            disabled: isSubmitting,\n            sx: {\n              '& .MuiOutlinedInput-root': {\n                borderRadius: 3\n              }\n            },\n            InputProps: {\n              startAdornment: /*#__PURE__*/_jsxDEV(LockIcon, {\n                sx: {\n                  mr: 1,\n                  color: 'text.secondary'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 404,\n                columnNumber: 35\n              }, this),\n              endAdornment: /*#__PURE__*/_jsxDEV(IconButton, {\n                onClick: () => setShowPassword(!showPassword),\n                children: showPassword ? /*#__PURE__*/_jsxDEV(VisibilityOffIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 407,\n                  columnNumber: 39\n                }, this) : /*#__PURE__*/_jsxDEV(VisibilityIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 407,\n                  columnNumber: 63\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 406,\n                columnNumber: 21\n              }, this)\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 381,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"Confirm Password\",\n            type: \"password\",\n            value: signupForm.confirmPassword,\n            onChange: e => {\n              setSignupForm({\n                ...signupForm,\n                confirmPassword: e.target.value\n              });\n              if (formErrors.confirmPassword) {\n                setFormErrors({\n                  ...formErrors,\n                  confirmPassword: ''\n                });\n              }\n            },\n            margin: \"normal\",\n            variant: \"outlined\",\n            autoComplete: \"new-password\",\n            error: !!formErrors.confirmPassword,\n            helperText: formErrors.confirmPassword,\n            disabled: isSubmitting,\n            sx: {\n              '& .MuiOutlinedInput-root': {\n                borderRadius: 3\n              }\n            },\n            InputProps: {\n              startAdornment: /*#__PURE__*/_jsxDEV(LockIcon, {\n                sx: {\n                  mr: 1,\n                  color: 'text.secondary'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 436,\n                columnNumber: 35\n              }, this)\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 413,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            type: \"submit\",\n            fullWidth: true,\n            variant: \"contained\",\n            disabled: isSubmitting,\n            sx: {\n              mt: 3,\n              mb: 2,\n              borderRadius: 3,\n              py: 1.5,\n              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n              '&:hover': {\n                background: 'linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)'\n              }\n            },\n            children: isSubmitting ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 24,\n              color: \"inherit\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 457,\n              columnNumber: 19\n            }, this) : 'Create Account'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 440,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 320,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 223,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 176,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 162,\n    columnNumber: 5\n  }, this);\n};\n_s(AuthenticationForms, \"lmKUadvZyxfzE8/2gfHBGLcYRTs=\", false, function () {\n  return [useAuth];\n});\n_c = AuthenticationForms;\nexport default AuthenticationForms;\nvar _c;\n$RefreshReg$(_c, \"AuthenticationForms\");", "map": {"version": 3, "names": ["React", "useState", "Box", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Typography", "TextField", "<PERSON><PERSON>", "IconButton", "<PERSON><PERSON>", "CircularProgress", "Tabs", "Tab", "Fade", "Visibility", "VisibilityIcon", "VisibilityOff", "VisibilityOffIcon", "Email", "EmailIcon", "Lock", "LockIcon", "Person", "PersonIcon", "useAuth", "authService", "jsxDEV", "_jsxDEV", "AuthenticationForms", "_s", "activeTab", "setActiveTab", "showPassword", "setShowPassword", "isSubmitting", "setIsSubmitting", "formErrors", "setFormErrors", "alert", "<PERSON><PERSON><PERSON><PERSON>", "show", "message", "severity", "loginForm", "setLoginForm", "email", "password", "signupForm", "setSignupForm", "username", "confirmPassword", "login", "signup", "error", "clearError", "show<PERSON><PERSON><PERSON>", "setTimeout", "validateLoginForm", "errors", "validateEmail", "Object", "keys", "length", "validateSignupForm", "test", "passwordValidation", "validatePassword", "<PERSON><PERSON><PERSON><PERSON>", "handleLogin", "e", "preventDefault", "result", "success", "handleSignup", "handleTabChange", "event", "newValue", "sx", "max<PERSON><PERSON><PERSON>", "mx", "children", "in", "mb", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "background", "theme", "palette", "mode", "border", "borderRadius", "borderBottom", "borderColor", "value", "onChange", "variant", "textColor", "indicatorColor", "minHeight", "fontSize", "fontWeight", "textTransform", "transition", "height", "label", "p", "component", "onSubmit", "gutterBottom", "textAlign", "color", "fullWidth", "type", "target", "margin", "autoComplete", "helperText", "disabled", "InputProps", "startAdornment", "mr", "endAdornment", "onClick", "mt", "py", "size", "_c", "$RefreshReg$"], "sources": ["E:/PythonProjects/React.js/Smart AI Assisstant/src/components/AuthenticationForms.jsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  <PERSON>,\n  <PERSON>,\n  CardContent,\n  <PERSON>po<PERSON>,\n  TextField,\n  Button,\n  IconButton,\n  Alert,\n  CircularProgress,\n  <PERSON><PERSON>,\n  Tab,\n  Fade,\n} from '@mui/material';\nimport {\n  Visibility as VisibilityIcon,\n  VisibilityOff as VisibilityOffIcon,\n  Email as EmailIcon,\n  Lock as LockIcon,\n  Person as PersonIcon,\n} from '@mui/icons-material';\nimport { useAuth } from '../contexts/AuthContext';\nimport authService from '../services/auth';\n\nconst AuthenticationForms = () => {\n  const [activeTab, setActiveTab] = useState(0); // 0 = login, 1 = signup\n  const [showPassword, setShowPassword] = useState(false);\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [formErrors, setFormErrors] = useState({});\n  const [alert, setAlert] = useState({ show: false, message: '', severity: 'success' });\n\n  const [loginForm, setLoginForm] = useState({ email: '', password: '' });\n  const [signupForm, setSignupForm] = useState({\n    username: '',\n    email: '',\n    password: '',\n    confirmPassword: '',\n  });\n\n  const { login, signup, error, clearError } = useAuth();\n\n  const showAlert = (message, severity = 'success') => {\n    setAlert({ show: true, message, severity });\n    setTimeout(() => setAlert({ show: false, message: '', severity: 'success' }), 5000);\n  };\n\n  const validateLoginForm = () => {\n    const errors = {};\n    \n    if (!loginForm.email) {\n      errors.email = 'Email is required';\n    } else if (!authService.validateEmail(loginForm.email)) {\n      errors.email = 'Please enter a valid email address';\n    }\n    \n    if (!loginForm.password) {\n      errors.password = 'Password is required';\n    }\n    \n    setFormErrors(errors);\n    return Object.keys(errors).length === 0;\n  };\n\n  const validateSignupForm = () => {\n    const errors = {};\n    \n    if (!signupForm.username) {\n      errors.username = 'Username is required';\n    } else if (signupForm.username.length < 3) {\n      errors.username = 'Username must be at least 3 characters long';\n    } else if (!/^[a-zA-Z0-9_]+$/.test(signupForm.username)) {\n      errors.username = 'Username can only contain letters, numbers, and underscores';\n    }\n    \n    if (!signupForm.email) {\n      errors.email = 'Email is required';\n    } else if (!authService.validateEmail(signupForm.email)) {\n      errors.email = 'Please enter a valid email address';\n    }\n    \n    if (!signupForm.password) {\n      errors.password = 'Password is required';\n    } else {\n      const passwordValidation = authService.validatePassword(signupForm.password);\n      if (!passwordValidation.isValid) {\n        errors.password = passwordValidation.errors[0];\n      }\n    }\n    \n    if (!signupForm.confirmPassword) {\n      errors.confirmPassword = 'Please confirm your password';\n    } else if (signupForm.password !== signupForm.confirmPassword) {\n      errors.confirmPassword = 'Passwords do not match';\n    }\n    \n    setFormErrors(errors);\n    return Object.keys(errors).length === 0;\n  };\n\n  const handleLogin = async (e) => {\n    e.preventDefault();\n    \n    if (!validateLoginForm()) return;\n    \n    setIsSubmitting(true);\n    clearError();\n    \n    try {\n      const result = await login(loginForm);\n      \n      if (result.success) {\n        showAlert('Login successful! Welcome back.', 'success');\n        setLoginForm({ email: '', password: '' });\n      } else {\n        showAlert(result.error || 'Login failed. Please try again.', 'error');\n      }\n    } catch (error) {\n      showAlert('An unexpected error occurred. Please try again.', 'error');\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  const handleSignup = async (e) => {\n    e.preventDefault();\n    \n    if (!validateSignupForm()) return;\n    \n    setIsSubmitting(true);\n    clearError();\n    \n    try {\n      const result = await signup({\n        username: signupForm.username,\n        email: signupForm.email,\n        password: signupForm.password,\n        confirmPassword: signupForm.confirmPassword,\n      });\n      \n      if (result.success) {\n        showAlert('Account created successfully! Welcome to AI Security Guard.', 'success');\n        setSignupForm({ username: '', email: '', password: '', confirmPassword: '' });\n      } else {\n        showAlert(result.error || 'Registration failed. Please try again.', 'error');\n      }\n    } catch (error) {\n      showAlert('An unexpected error occurred. Please try again.', 'error');\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  const handleTabChange = (event, newValue) => {\n    setActiveTab(newValue);\n    setFormErrors({});\n    clearError();\n    setAlert({ show: false, message: '', severity: 'success' });\n  };\n\n  return (\n    <Box sx={{ maxWidth: 500, mx: 'auto' }}>\n      {/* Alert Messages */}\n      <Fade in={alert.show}>\n        <Alert severity={alert.severity} sx={{ mb: 3 }}>\n          {alert.message}\n        </Alert>\n      </Fade>\n\n      {error && (\n        <Alert severity=\"error\" sx={{ mb: 3 }}>\n          {error}\n        </Alert>\n      )}\n\n      <Card\n        sx={{\n          background: (theme) => theme.palette.mode === 'dark'\n            ? 'linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%)'\n            : 'linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%)',\n          border: '1px solid rgba(102, 126, 234, 0.2)',\n          borderRadius: 4,\n        }}\n      >\n        {/* Tabs */}\n        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>\n          <Tabs\n            value={activeTab}\n            onChange={handleTabChange}\n            variant=\"fullWidth\"\n            textColor=\"primary\"\n            indicatorColor=\"primary\"\n            sx={{\n              '& .MuiTab-root': {\n                minHeight: 60,\n                fontSize: '1rem',\n                fontWeight: 600,\n                textTransform: 'none',\n                transition: 'all 0.3s ease',\n                '&:hover': {\n                  background: (theme) => theme.palette.mode === 'dark'\n                    ? 'rgba(102, 126, 234, 0.1)'\n                    : 'rgba(102, 126, 234, 0.05)',\n                },\n                '&.Mui-selected': {\n                  background: (theme) => theme.palette.mode === 'dark'\n                    ? 'linear-gradient(135deg, rgba(102, 126, 234, 0.2) 0%, rgba(118, 75, 162, 0.2) 100%)'\n                    : 'linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%)',\n                },\n              },\n              '& .MuiTabs-indicator': {\n                height: 3,\n                borderRadius: '3px 3px 0 0',\n                background: 'linear-gradient(90deg, #667eea 0%, #764ba2 100%)',\n              },\n            }}\n          >\n            <Tab label=\"Sign In\" />\n            <Tab label=\"Create Account\" />\n          </Tabs>\n        </Box>\n\n        <CardContent sx={{ p: 4 }}>\n          {/* Login Form */}\n          {activeTab === 0 && (\n            <Box component=\"form\" onSubmit={handleLogin}>\n              <Typography variant=\"h5\" gutterBottom textAlign=\"center\" fontWeight=\"bold\">\n                Welcome Back\n              </Typography>\n              <Typography variant=\"body2\" color=\"text.secondary\" textAlign=\"center\" sx={{ mb: 3 }}>\n                Sign in to access your AI Security Guard account\n              </Typography>\n\n              <TextField\n                fullWidth\n                label=\"Email\"\n                type=\"email\"\n                value={loginForm.email}\n                onChange={(e) => {\n                  setLoginForm({ ...loginForm, email: e.target.value });\n                  if (formErrors.email) {\n                    setFormErrors({ ...formErrors, email: '' });\n                  }\n                }}\n                margin=\"normal\"\n                variant=\"outlined\"\n                autoComplete=\"email\"\n                error={!!formErrors.email}\n                helperText={formErrors.email}\n                disabled={isSubmitting}\n                sx={{\n                  '& .MuiOutlinedInput-root': {\n                    borderRadius: 3,\n                  },\n                }}\n                InputProps={{\n                  startAdornment: <EmailIcon sx={{ mr: 1, color: 'text.secondary' }} />,\n                }}\n              />\n\n              <TextField\n                fullWidth\n                label=\"Password\"\n                type={showPassword ? 'text' : 'password'}\n                value={loginForm.password}\n                onChange={(e) => {\n                  setLoginForm({ ...loginForm, password: e.target.value });\n                  if (formErrors.password) {\n                    setFormErrors({ ...formErrors, password: '' });\n                  }\n                }}\n                margin=\"normal\"\n                variant=\"outlined\"\n                autoComplete=\"current-password\"\n                error={!!formErrors.password}\n                helperText={formErrors.password}\n                disabled={isSubmitting}\n                sx={{\n                  '& .MuiOutlinedInput-root': {\n                    borderRadius: 3,\n                  },\n                }}\n                InputProps={{\n                  startAdornment: <LockIcon sx={{ mr: 1, color: 'text.secondary' }} />,\n                  endAdornment: (\n                    <IconButton onClick={() => setShowPassword(!showPassword)}>\n                      {showPassword ? <VisibilityOffIcon /> : <VisibilityIcon />}\n                    </IconButton>\n                  ),\n                }}\n              />\n\n              <Button\n                type=\"submit\"\n                fullWidth\n                variant=\"contained\"\n                disabled={isSubmitting}\n                sx={{\n                  mt: 3,\n                  mb: 2,\n                  borderRadius: 3,\n                  py: 1.5,\n                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                  '&:hover': {\n                    background: 'linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)',\n                  },\n                }}\n              >\n                {isSubmitting ? (\n                  <CircularProgress size={24} color=\"inherit\" />\n                ) : (\n                  'Sign In'\n                )}\n              </Button>\n            </Box>\n          )}\n\n          {/* Signup Form */}\n          {activeTab === 1 && (\n            <Box component=\"form\" onSubmit={handleSignup}>\n              <Typography variant=\"h5\" gutterBottom textAlign=\"center\" fontWeight=\"bold\">\n                Create Account\n              </Typography>\n              <Typography variant=\"body2\" color=\"text.secondary\" textAlign=\"center\" sx={{ mb: 3 }}>\n                Join AI Security Guard to protect your digital assets\n              </Typography>\n\n              <TextField\n                fullWidth\n                label=\"Username\"\n                value={signupForm.username}\n                onChange={(e) => {\n                  setSignupForm({ ...signupForm, username: e.target.value });\n                  if (formErrors.username) {\n                    setFormErrors({ ...formErrors, username: '' });\n                  }\n                }}\n                margin=\"normal\"\n                variant=\"outlined\"\n                autoComplete=\"username\"\n                error={!!formErrors.username}\n                helperText={formErrors.username}\n                disabled={isSubmitting}\n                sx={{\n                  '& .MuiOutlinedInput-root': {\n                    borderRadius: 3,\n                  },\n                }}\n                InputProps={{\n                  startAdornment: <PersonIcon sx={{ mr: 1, color: 'text.secondary' }} />,\n                }}\n              />\n\n              <TextField\n                fullWidth\n                label=\"Email\"\n                type=\"email\"\n                value={signupForm.email}\n                onChange={(e) => {\n                  setSignupForm({ ...signupForm, email: e.target.value });\n                  if (formErrors.email) {\n                    setFormErrors({ ...formErrors, email: '' });\n                  }\n                }}\n                margin=\"normal\"\n                variant=\"outlined\"\n                autoComplete=\"email\"\n                error={!!formErrors.email}\n                helperText={formErrors.email}\n                disabled={isSubmitting}\n                sx={{\n                  '& .MuiOutlinedInput-root': {\n                    borderRadius: 3,\n                  },\n                }}\n                InputProps={{\n                  startAdornment: <EmailIcon sx={{ mr: 1, color: 'text.secondary' }} />,\n                }}\n              />\n\n              <TextField\n                fullWidth\n                label=\"Password\"\n                type={showPassword ? 'text' : 'password'}\n                value={signupForm.password}\n                onChange={(e) => {\n                  setSignupForm({ ...signupForm, password: e.target.value });\n                  if (formErrors.password) {\n                    setFormErrors({ ...formErrors, password: '' });\n                  }\n                }}\n                margin=\"normal\"\n                variant=\"outlined\"\n                autoComplete=\"new-password\"\n                error={!!formErrors.password}\n                helperText={formErrors.password}\n                disabled={isSubmitting}\n                sx={{\n                  '& .MuiOutlinedInput-root': {\n                    borderRadius: 3,\n                  },\n                }}\n                InputProps={{\n                  startAdornment: <LockIcon sx={{ mr: 1, color: 'text.secondary' }} />,\n                  endAdornment: (\n                    <IconButton onClick={() => setShowPassword(!showPassword)}>\n                      {showPassword ? <VisibilityOffIcon /> : <VisibilityIcon />}\n                    </IconButton>\n                  ),\n                }}\n              />\n\n              <TextField\n                fullWidth\n                label=\"Confirm Password\"\n                type=\"password\"\n                value={signupForm.confirmPassword}\n                onChange={(e) => {\n                  setSignupForm({ ...signupForm, confirmPassword: e.target.value });\n                  if (formErrors.confirmPassword) {\n                    setFormErrors({ ...formErrors, confirmPassword: '' });\n                  }\n                }}\n                margin=\"normal\"\n                variant=\"outlined\"\n                autoComplete=\"new-password\"\n                error={!!formErrors.confirmPassword}\n                helperText={formErrors.confirmPassword}\n                disabled={isSubmitting}\n                sx={{\n                  '& .MuiOutlinedInput-root': {\n                    borderRadius: 3,\n                  },\n                }}\n                InputProps={{\n                  startAdornment: <LockIcon sx={{ mr: 1, color: 'text.secondary' }} />,\n                }}\n              />\n\n              <Button\n                type=\"submit\"\n                fullWidth\n                variant=\"contained\"\n                disabled={isSubmitting}\n                sx={{\n                  mt: 3,\n                  mb: 2,\n                  borderRadius: 3,\n                  py: 1.5,\n                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                  '&:hover': {\n                    background: 'linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)',\n                  },\n                }}\n              >\n                {isSubmitting ? (\n                  <CircularProgress size={24} color=\"inherit\" />\n                ) : (\n                  'Create Account'\n                )}\n              </Button>\n            </Box>\n          )}\n        </CardContent>\n      </Card>\n    </Box>\n  );\n};\n\nexport default AuthenticationForms;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,GAAG,EACHC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,SAAS,EACTC,MAAM,EACNC,UAAU,EACVC,KAAK,EACLC,gBAAgB,EAChBC,IAAI,EACJC,GAAG,EACHC,IAAI,QACC,eAAe;AACtB,SACEC,UAAU,IAAIC,cAAc,EAC5BC,aAAa,IAAIC,iBAAiB,EAClCC,KAAK,IAAIC,SAAS,EAClBC,IAAI,IAAIC,QAAQ,EAChBC,MAAM,IAAIC,UAAU,QACf,qBAAqB;AAC5B,SAASC,OAAO,QAAQ,yBAAyB;AACjD,OAAOC,WAAW,MAAM,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3C,MAAMC,mBAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAG9B,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;EAC/C,MAAM,CAAC+B,YAAY,EAAEC,eAAe,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACiC,YAAY,EAAEC,eAAe,CAAC,GAAGlC,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACmC,UAAU,EAAEC,aAAa,CAAC,GAAGpC,QAAQ,CAAC,CAAC,CAAC,CAAC;EAChD,MAAM,CAACqC,KAAK,EAAEC,QAAQ,CAAC,GAAGtC,QAAQ,CAAC;IAAEuC,IAAI,EAAE,KAAK;IAAEC,OAAO,EAAE,EAAE;IAAEC,QAAQ,EAAE;EAAU,CAAC,CAAC;EAErF,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAG3C,QAAQ,CAAC;IAAE4C,KAAK,EAAE,EAAE;IAAEC,QAAQ,EAAE;EAAG,CAAC,CAAC;EACvE,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAG/C,QAAQ,CAAC;IAC3CgD,QAAQ,EAAE,EAAE;IACZJ,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZI,eAAe,EAAE;EACnB,CAAC,CAAC;EAEF,MAAM;IAAEC,KAAK;IAAEC,MAAM;IAAEC,KAAK;IAAEC;EAAW,CAAC,GAAG9B,OAAO,CAAC,CAAC;EAEtD,MAAM+B,SAAS,GAAGA,CAACd,OAAO,EAAEC,QAAQ,GAAG,SAAS,KAAK;IACnDH,QAAQ,CAAC;MAAEC,IAAI,EAAE,IAAI;MAAEC,OAAO;MAAEC;IAAS,CAAC,CAAC;IAC3Cc,UAAU,CAAC,MAAMjB,QAAQ,CAAC;MAAEC,IAAI,EAAE,KAAK;MAAEC,OAAO,EAAE,EAAE;MAAEC,QAAQ,EAAE;IAAU,CAAC,CAAC,EAAE,IAAI,CAAC;EACrF,CAAC;EAED,MAAMe,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,MAAMC,MAAM,GAAG,CAAC,CAAC;IAEjB,IAAI,CAACf,SAAS,CAACE,KAAK,EAAE;MACpBa,MAAM,CAACb,KAAK,GAAG,mBAAmB;IACpC,CAAC,MAAM,IAAI,CAACpB,WAAW,CAACkC,aAAa,CAAChB,SAAS,CAACE,KAAK,CAAC,EAAE;MACtDa,MAAM,CAACb,KAAK,GAAG,oCAAoC;IACrD;IAEA,IAAI,CAACF,SAAS,CAACG,QAAQ,EAAE;MACvBY,MAAM,CAACZ,QAAQ,GAAG,sBAAsB;IAC1C;IAEAT,aAAa,CAACqB,MAAM,CAAC;IACrB,OAAOE,MAAM,CAACC,IAAI,CAACH,MAAM,CAAC,CAACI,MAAM,KAAK,CAAC;EACzC,CAAC;EAED,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,MAAML,MAAM,GAAG,CAAC,CAAC;IAEjB,IAAI,CAACX,UAAU,CAACE,QAAQ,EAAE;MACxBS,MAAM,CAACT,QAAQ,GAAG,sBAAsB;IAC1C,CAAC,MAAM,IAAIF,UAAU,CAACE,QAAQ,CAACa,MAAM,GAAG,CAAC,EAAE;MACzCJ,MAAM,CAACT,QAAQ,GAAG,6CAA6C;IACjE,CAAC,MAAM,IAAI,CAAC,iBAAiB,CAACe,IAAI,CAACjB,UAAU,CAACE,QAAQ,CAAC,EAAE;MACvDS,MAAM,CAACT,QAAQ,GAAG,6DAA6D;IACjF;IAEA,IAAI,CAACF,UAAU,CAACF,KAAK,EAAE;MACrBa,MAAM,CAACb,KAAK,GAAG,mBAAmB;IACpC,CAAC,MAAM,IAAI,CAACpB,WAAW,CAACkC,aAAa,CAACZ,UAAU,CAACF,KAAK,CAAC,EAAE;MACvDa,MAAM,CAACb,KAAK,GAAG,oCAAoC;IACrD;IAEA,IAAI,CAACE,UAAU,CAACD,QAAQ,EAAE;MACxBY,MAAM,CAACZ,QAAQ,GAAG,sBAAsB;IAC1C,CAAC,MAAM;MACL,MAAMmB,kBAAkB,GAAGxC,WAAW,CAACyC,gBAAgB,CAACnB,UAAU,CAACD,QAAQ,CAAC;MAC5E,IAAI,CAACmB,kBAAkB,CAACE,OAAO,EAAE;QAC/BT,MAAM,CAACZ,QAAQ,GAAGmB,kBAAkB,CAACP,MAAM,CAAC,CAAC,CAAC;MAChD;IACF;IAEA,IAAI,CAACX,UAAU,CAACG,eAAe,EAAE;MAC/BQ,MAAM,CAACR,eAAe,GAAG,8BAA8B;IACzD,CAAC,MAAM,IAAIH,UAAU,CAACD,QAAQ,KAAKC,UAAU,CAACG,eAAe,EAAE;MAC7DQ,MAAM,CAACR,eAAe,GAAG,wBAAwB;IACnD;IAEAb,aAAa,CAACqB,MAAM,CAAC;IACrB,OAAOE,MAAM,CAACC,IAAI,CAACH,MAAM,CAAC,CAACI,MAAM,KAAK,CAAC;EACzC,CAAC;EAED,MAAMM,WAAW,GAAG,MAAOC,CAAC,IAAK;IAC/BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAElB,IAAI,CAACb,iBAAiB,CAAC,CAAC,EAAE;IAE1BtB,eAAe,CAAC,IAAI,CAAC;IACrBmB,UAAU,CAAC,CAAC;IAEZ,IAAI;MACF,MAAMiB,MAAM,GAAG,MAAMpB,KAAK,CAACR,SAAS,CAAC;MAErC,IAAI4B,MAAM,CAACC,OAAO,EAAE;QAClBjB,SAAS,CAAC,iCAAiC,EAAE,SAAS,CAAC;QACvDX,YAAY,CAAC;UAAEC,KAAK,EAAE,EAAE;UAAEC,QAAQ,EAAE;QAAG,CAAC,CAAC;MAC3C,CAAC,MAAM;QACLS,SAAS,CAACgB,MAAM,CAAClB,KAAK,IAAI,iCAAiC,EAAE,OAAO,CAAC;MACvE;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdE,SAAS,CAAC,iDAAiD,EAAE,OAAO,CAAC;IACvE,CAAC,SAAS;MACRpB,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;EAED,MAAMsC,YAAY,GAAG,MAAOJ,CAAC,IAAK;IAChCA,CAAC,CAACC,cAAc,CAAC,CAAC;IAElB,IAAI,CAACP,kBAAkB,CAAC,CAAC,EAAE;IAE3B5B,eAAe,CAAC,IAAI,CAAC;IACrBmB,UAAU,CAAC,CAAC;IAEZ,IAAI;MACF,MAAMiB,MAAM,GAAG,MAAMnB,MAAM,CAAC;QAC1BH,QAAQ,EAAEF,UAAU,CAACE,QAAQ;QAC7BJ,KAAK,EAAEE,UAAU,CAACF,KAAK;QACvBC,QAAQ,EAAEC,UAAU,CAACD,QAAQ;QAC7BI,eAAe,EAAEH,UAAU,CAACG;MAC9B,CAAC,CAAC;MAEF,IAAIqB,MAAM,CAACC,OAAO,EAAE;QAClBjB,SAAS,CAAC,6DAA6D,EAAE,SAAS,CAAC;QACnFP,aAAa,CAAC;UAAEC,QAAQ,EAAE,EAAE;UAAEJ,KAAK,EAAE,EAAE;UAAEC,QAAQ,EAAE,EAAE;UAAEI,eAAe,EAAE;QAAG,CAAC,CAAC;MAC/E,CAAC,MAAM;QACLK,SAAS,CAACgB,MAAM,CAAClB,KAAK,IAAI,wCAAwC,EAAE,OAAO,CAAC;MAC9E;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdE,SAAS,CAAC,iDAAiD,EAAE,OAAO,CAAC;IACvE,CAAC,SAAS;MACRpB,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;EAED,MAAMuC,eAAe,GAAGA,CAACC,KAAK,EAAEC,QAAQ,KAAK;IAC3C7C,YAAY,CAAC6C,QAAQ,CAAC;IACtBvC,aAAa,CAAC,CAAC,CAAC,CAAC;IACjBiB,UAAU,CAAC,CAAC;IACZf,QAAQ,CAAC;MAAEC,IAAI,EAAE,KAAK;MAAEC,OAAO,EAAE,EAAE;MAAEC,QAAQ,EAAE;IAAU,CAAC,CAAC;EAC7D,CAAC;EAED,oBACEf,OAAA,CAACzB,GAAG;IAAC2E,EAAE,EAAE;MAAEC,QAAQ,EAAE,GAAG;MAAEC,EAAE,EAAE;IAAO,CAAE;IAAAC,QAAA,gBAErCrD,OAAA,CAACd,IAAI;MAACoE,EAAE,EAAE3C,KAAK,CAACE,IAAK;MAAAwC,QAAA,eACnBrD,OAAA,CAAClB,KAAK;QAACiC,QAAQ,EAAEJ,KAAK,CAACI,QAAS;QAACmC,EAAE,EAAE;UAAEK,EAAE,EAAE;QAAE,CAAE;QAAAF,QAAA,EAC5C1C,KAAK,CAACG;MAAO;QAAA0C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,EAENjC,KAAK,iBACJ1B,OAAA,CAAClB,KAAK;MAACiC,QAAQ,EAAC,OAAO;MAACmC,EAAE,EAAE;QAAEK,EAAE,EAAE;MAAE,CAAE;MAAAF,QAAA,EACnC3B;IAAK;MAAA8B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CACR,eAED3D,OAAA,CAACxB,IAAI;MACH0E,EAAE,EAAE;QACFU,UAAU,EAAGC,KAAK,IAAKA,KAAK,CAACC,OAAO,CAACC,IAAI,KAAK,MAAM,GAChD,oFAAoF,GACpF,sFAAsF;QAC1FC,MAAM,EAAE,oCAAoC;QAC5CC,YAAY,EAAE;MAChB,CAAE;MAAAZ,QAAA,gBAGFrD,OAAA,CAACzB,GAAG;QAAC2E,EAAE,EAAE;UAAEgB,YAAY,EAAE,CAAC;UAAEC,WAAW,EAAE;QAAU,CAAE;QAAAd,QAAA,eACnDrD,OAAA,CAAChB,IAAI;UACHoF,KAAK,EAAEjE,SAAU;UACjBkE,QAAQ,EAAEtB,eAAgB;UAC1BuB,OAAO,EAAC,WAAW;UACnBC,SAAS,EAAC,SAAS;UACnBC,cAAc,EAAC,SAAS;UACxBtB,EAAE,EAAE;YACF,gBAAgB,EAAE;cAChBuB,SAAS,EAAE,EAAE;cACbC,QAAQ,EAAE,MAAM;cAChBC,UAAU,EAAE,GAAG;cACfC,aAAa,EAAE,MAAM;cACrBC,UAAU,EAAE,eAAe;cAC3B,SAAS,EAAE;gBACTjB,UAAU,EAAGC,KAAK,IAAKA,KAAK,CAACC,OAAO,CAACC,IAAI,KAAK,MAAM,GAChD,0BAA0B,GAC1B;cACN,CAAC;cACD,gBAAgB,EAAE;gBAChBH,UAAU,EAAGC,KAAK,IAAKA,KAAK,CAACC,OAAO,CAACC,IAAI,KAAK,MAAM,GAChD,oFAAoF,GACpF;cACN;YACF,CAAC;YACD,sBAAsB,EAAE;cACtBe,MAAM,EAAE,CAAC;cACTb,YAAY,EAAE,aAAa;cAC3BL,UAAU,EAAE;YACd;UACF,CAAE;UAAAP,QAAA,gBAEFrD,OAAA,CAACf,GAAG;YAAC8F,KAAK,EAAC;UAAS;YAAAvB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACvB3D,OAAA,CAACf,GAAG;YAAC8F,KAAK,EAAC;UAAgB;YAAAvB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAEN3D,OAAA,CAACvB,WAAW;QAACyE,EAAE,EAAE;UAAE8B,CAAC,EAAE;QAAE,CAAE;QAAA3B,QAAA,GAEvBlD,SAAS,KAAK,CAAC,iBACdH,OAAA,CAACzB,GAAG;UAAC0G,SAAS,EAAC,MAAM;UAACC,QAAQ,EAAEzC,WAAY;UAAAY,QAAA,gBAC1CrD,OAAA,CAACtB,UAAU;YAAC4F,OAAO,EAAC,IAAI;YAACa,YAAY;YAACC,SAAS,EAAC,QAAQ;YAACT,UAAU,EAAC,MAAM;YAAAtB,QAAA,EAAC;UAE3E;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb3D,OAAA,CAACtB,UAAU;YAAC4F,OAAO,EAAC,OAAO;YAACe,KAAK,EAAC,gBAAgB;YAACD,SAAS,EAAC,QAAQ;YAAClC,EAAE,EAAE;cAAEK,EAAE,EAAE;YAAE,CAAE;YAAAF,QAAA,EAAC;UAErF;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAEb3D,OAAA,CAACrB,SAAS;YACR2G,SAAS;YACTP,KAAK,EAAC,OAAO;YACbQ,IAAI,EAAC,OAAO;YACZnB,KAAK,EAAEpD,SAAS,CAACE,KAAM;YACvBmD,QAAQ,EAAG3B,CAAC,IAAK;cACfzB,YAAY,CAAC;gBAAE,GAAGD,SAAS;gBAAEE,KAAK,EAAEwB,CAAC,CAAC8C,MAAM,CAACpB;cAAM,CAAC,CAAC;cACrD,IAAI3D,UAAU,CAACS,KAAK,EAAE;gBACpBR,aAAa,CAAC;kBAAE,GAAGD,UAAU;kBAAES,KAAK,EAAE;gBAAG,CAAC,CAAC;cAC7C;YACF,CAAE;YACFuE,MAAM,EAAC,QAAQ;YACfnB,OAAO,EAAC,UAAU;YAClBoB,YAAY,EAAC,OAAO;YACpBhE,KAAK,EAAE,CAAC,CAACjB,UAAU,CAACS,KAAM;YAC1ByE,UAAU,EAAElF,UAAU,CAACS,KAAM;YAC7B0E,QAAQ,EAAErF,YAAa;YACvB2C,EAAE,EAAE;cACF,0BAA0B,EAAE;gBAC1Be,YAAY,EAAE;cAChB;YACF,CAAE;YACF4B,UAAU,EAAE;cACVC,cAAc,eAAE9F,OAAA,CAACR,SAAS;gBAAC0D,EAAE,EAAE;kBAAE6C,EAAE,EAAE,CAAC;kBAAEV,KAAK,EAAE;gBAAiB;cAAE;gBAAA7B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YACtE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEF3D,OAAA,CAACrB,SAAS;YACR2G,SAAS;YACTP,KAAK,EAAC,UAAU;YAChBQ,IAAI,EAAElF,YAAY,GAAG,MAAM,GAAG,UAAW;YACzC+D,KAAK,EAAEpD,SAAS,CAACG,QAAS;YAC1BkD,QAAQ,EAAG3B,CAAC,IAAK;cACfzB,YAAY,CAAC;gBAAE,GAAGD,SAAS;gBAAEG,QAAQ,EAAEuB,CAAC,CAAC8C,MAAM,CAACpB;cAAM,CAAC,CAAC;cACxD,IAAI3D,UAAU,CAACU,QAAQ,EAAE;gBACvBT,aAAa,CAAC;kBAAE,GAAGD,UAAU;kBAAEU,QAAQ,EAAE;gBAAG,CAAC,CAAC;cAChD;YACF,CAAE;YACFsE,MAAM,EAAC,QAAQ;YACfnB,OAAO,EAAC,UAAU;YAClBoB,YAAY,EAAC,kBAAkB;YAC/BhE,KAAK,EAAE,CAAC,CAACjB,UAAU,CAACU,QAAS;YAC7BwE,UAAU,EAAElF,UAAU,CAACU,QAAS;YAChCyE,QAAQ,EAAErF,YAAa;YACvB2C,EAAE,EAAE;cACF,0BAA0B,EAAE;gBAC1Be,YAAY,EAAE;cAChB;YACF,CAAE;YACF4B,UAAU,EAAE;cACVC,cAAc,eAAE9F,OAAA,CAACN,QAAQ;gBAACwD,EAAE,EAAE;kBAAE6C,EAAE,EAAE,CAAC;kBAAEV,KAAK,EAAE;gBAAiB;cAAE;gBAAA7B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;cACpEqC,YAAY,eACVhG,OAAA,CAACnB,UAAU;gBAACoH,OAAO,EAAEA,CAAA,KAAM3F,eAAe,CAAC,CAACD,YAAY,CAAE;gBAAAgD,QAAA,EACvDhD,YAAY,gBAAGL,OAAA,CAACV,iBAAiB;kBAAAkE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAAG3D,OAAA,CAACZ,cAAc;kBAAAoE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChD;YAEhB;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEF3D,OAAA,CAACpB,MAAM;YACL2G,IAAI,EAAC,QAAQ;YACbD,SAAS;YACThB,OAAO,EAAC,WAAW;YACnBsB,QAAQ,EAAErF,YAAa;YACvB2C,EAAE,EAAE;cACFgD,EAAE,EAAE,CAAC;cACL3C,EAAE,EAAE,CAAC;cACLU,YAAY,EAAE,CAAC;cACfkC,EAAE,EAAE,GAAG;cACPvC,UAAU,EAAE,mDAAmD;cAC/D,SAAS,EAAE;gBACTA,UAAU,EAAE;cACd;YACF,CAAE;YAAAP,QAAA,EAED9C,YAAY,gBACXP,OAAA,CAACjB,gBAAgB;cAACqH,IAAI,EAAE,EAAG;cAACf,KAAK,EAAC;YAAS;cAAA7B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,GAE9C;UACD;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CACN,EAGAxD,SAAS,KAAK,CAAC,iBACdH,OAAA,CAACzB,GAAG;UAAC0G,SAAS,EAAC,MAAM;UAACC,QAAQ,EAAEpC,YAAa;UAAAO,QAAA,gBAC3CrD,OAAA,CAACtB,UAAU;YAAC4F,OAAO,EAAC,IAAI;YAACa,YAAY;YAACC,SAAS,EAAC,QAAQ;YAACT,UAAU,EAAC,MAAM;YAAAtB,QAAA,EAAC;UAE3E;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb3D,OAAA,CAACtB,UAAU;YAAC4F,OAAO,EAAC,OAAO;YAACe,KAAK,EAAC,gBAAgB;YAACD,SAAS,EAAC,QAAQ;YAAClC,EAAE,EAAE;cAAEK,EAAE,EAAE;YAAE,CAAE;YAAAF,QAAA,EAAC;UAErF;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAEb3D,OAAA,CAACrB,SAAS;YACR2G,SAAS;YACTP,KAAK,EAAC,UAAU;YAChBX,KAAK,EAAEhD,UAAU,CAACE,QAAS;YAC3B+C,QAAQ,EAAG3B,CAAC,IAAK;cACfrB,aAAa,CAAC;gBAAE,GAAGD,UAAU;gBAAEE,QAAQ,EAAEoB,CAAC,CAAC8C,MAAM,CAACpB;cAAM,CAAC,CAAC;cAC1D,IAAI3D,UAAU,CAACa,QAAQ,EAAE;gBACvBZ,aAAa,CAAC;kBAAE,GAAGD,UAAU;kBAAEa,QAAQ,EAAE;gBAAG,CAAC,CAAC;cAChD;YACF,CAAE;YACFmE,MAAM,EAAC,QAAQ;YACfnB,OAAO,EAAC,UAAU;YAClBoB,YAAY,EAAC,UAAU;YACvBhE,KAAK,EAAE,CAAC,CAACjB,UAAU,CAACa,QAAS;YAC7BqE,UAAU,EAAElF,UAAU,CAACa,QAAS;YAChCsE,QAAQ,EAAErF,YAAa;YACvB2C,EAAE,EAAE;cACF,0BAA0B,EAAE;gBAC1Be,YAAY,EAAE;cAChB;YACF,CAAE;YACF4B,UAAU,EAAE;cACVC,cAAc,eAAE9F,OAAA,CAACJ,UAAU;gBAACsD,EAAE,EAAE;kBAAE6C,EAAE,EAAE,CAAC;kBAAEV,KAAK,EAAE;gBAAiB;cAAE;gBAAA7B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YACvE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEF3D,OAAA,CAACrB,SAAS;YACR2G,SAAS;YACTP,KAAK,EAAC,OAAO;YACbQ,IAAI,EAAC,OAAO;YACZnB,KAAK,EAAEhD,UAAU,CAACF,KAAM;YACxBmD,QAAQ,EAAG3B,CAAC,IAAK;cACfrB,aAAa,CAAC;gBAAE,GAAGD,UAAU;gBAAEF,KAAK,EAAEwB,CAAC,CAAC8C,MAAM,CAACpB;cAAM,CAAC,CAAC;cACvD,IAAI3D,UAAU,CAACS,KAAK,EAAE;gBACpBR,aAAa,CAAC;kBAAE,GAAGD,UAAU;kBAAES,KAAK,EAAE;gBAAG,CAAC,CAAC;cAC7C;YACF,CAAE;YACFuE,MAAM,EAAC,QAAQ;YACfnB,OAAO,EAAC,UAAU;YAClBoB,YAAY,EAAC,OAAO;YACpBhE,KAAK,EAAE,CAAC,CAACjB,UAAU,CAACS,KAAM;YAC1ByE,UAAU,EAAElF,UAAU,CAACS,KAAM;YAC7B0E,QAAQ,EAAErF,YAAa;YACvB2C,EAAE,EAAE;cACF,0BAA0B,EAAE;gBAC1Be,YAAY,EAAE;cAChB;YACF,CAAE;YACF4B,UAAU,EAAE;cACVC,cAAc,eAAE9F,OAAA,CAACR,SAAS;gBAAC0D,EAAE,EAAE;kBAAE6C,EAAE,EAAE,CAAC;kBAAEV,KAAK,EAAE;gBAAiB;cAAE;gBAAA7B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YACtE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEF3D,OAAA,CAACrB,SAAS;YACR2G,SAAS;YACTP,KAAK,EAAC,UAAU;YAChBQ,IAAI,EAAElF,YAAY,GAAG,MAAM,GAAG,UAAW;YACzC+D,KAAK,EAAEhD,UAAU,CAACD,QAAS;YAC3BkD,QAAQ,EAAG3B,CAAC,IAAK;cACfrB,aAAa,CAAC;gBAAE,GAAGD,UAAU;gBAAED,QAAQ,EAAEuB,CAAC,CAAC8C,MAAM,CAACpB;cAAM,CAAC,CAAC;cAC1D,IAAI3D,UAAU,CAACU,QAAQ,EAAE;gBACvBT,aAAa,CAAC;kBAAE,GAAGD,UAAU;kBAAEU,QAAQ,EAAE;gBAAG,CAAC,CAAC;cAChD;YACF,CAAE;YACFsE,MAAM,EAAC,QAAQ;YACfnB,OAAO,EAAC,UAAU;YAClBoB,YAAY,EAAC,cAAc;YAC3BhE,KAAK,EAAE,CAAC,CAACjB,UAAU,CAACU,QAAS;YAC7BwE,UAAU,EAAElF,UAAU,CAACU,QAAS;YAChCyE,QAAQ,EAAErF,YAAa;YACvB2C,EAAE,EAAE;cACF,0BAA0B,EAAE;gBAC1Be,YAAY,EAAE;cAChB;YACF,CAAE;YACF4B,UAAU,EAAE;cACVC,cAAc,eAAE9F,OAAA,CAACN,QAAQ;gBAACwD,EAAE,EAAE;kBAAE6C,EAAE,EAAE,CAAC;kBAAEV,KAAK,EAAE;gBAAiB;cAAE;gBAAA7B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;cACpEqC,YAAY,eACVhG,OAAA,CAACnB,UAAU;gBAACoH,OAAO,EAAEA,CAAA,KAAM3F,eAAe,CAAC,CAACD,YAAY,CAAE;gBAAAgD,QAAA,EACvDhD,YAAY,gBAAGL,OAAA,CAACV,iBAAiB;kBAAAkE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAAG3D,OAAA,CAACZ,cAAc;kBAAAoE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChD;YAEhB;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEF3D,OAAA,CAACrB,SAAS;YACR2G,SAAS;YACTP,KAAK,EAAC,kBAAkB;YACxBQ,IAAI,EAAC,UAAU;YACfnB,KAAK,EAAEhD,UAAU,CAACG,eAAgB;YAClC8C,QAAQ,EAAG3B,CAAC,IAAK;cACfrB,aAAa,CAAC;gBAAE,GAAGD,UAAU;gBAAEG,eAAe,EAAEmB,CAAC,CAAC8C,MAAM,CAACpB;cAAM,CAAC,CAAC;cACjE,IAAI3D,UAAU,CAACc,eAAe,EAAE;gBAC9Bb,aAAa,CAAC;kBAAE,GAAGD,UAAU;kBAAEc,eAAe,EAAE;gBAAG,CAAC,CAAC;cACvD;YACF,CAAE;YACFkE,MAAM,EAAC,QAAQ;YACfnB,OAAO,EAAC,UAAU;YAClBoB,YAAY,EAAC,cAAc;YAC3BhE,KAAK,EAAE,CAAC,CAACjB,UAAU,CAACc,eAAgB;YACpCoE,UAAU,EAAElF,UAAU,CAACc,eAAgB;YACvCqE,QAAQ,EAAErF,YAAa;YACvB2C,EAAE,EAAE;cACF,0BAA0B,EAAE;gBAC1Be,YAAY,EAAE;cAChB;YACF,CAAE;YACF4B,UAAU,EAAE;cACVC,cAAc,eAAE9F,OAAA,CAACN,QAAQ;gBAACwD,EAAE,EAAE;kBAAE6C,EAAE,EAAE,CAAC;kBAAEV,KAAK,EAAE;gBAAiB;cAAE;gBAAA7B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YACrE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEF3D,OAAA,CAACpB,MAAM;YACL2G,IAAI,EAAC,QAAQ;YACbD,SAAS;YACThB,OAAO,EAAC,WAAW;YACnBsB,QAAQ,EAAErF,YAAa;YACvB2C,EAAE,EAAE;cACFgD,EAAE,EAAE,CAAC;cACL3C,EAAE,EAAE,CAAC;cACLU,YAAY,EAAE,CAAC;cACfkC,EAAE,EAAE,GAAG;cACPvC,UAAU,EAAE,mDAAmD;cAC/D,SAAS,EAAE;gBACTA,UAAU,EAAE;cACd;YACF,CAAE;YAAAP,QAAA,EAED9C,YAAY,gBACXP,OAAA,CAACjB,gBAAgB;cAACqH,IAAI,EAAE,EAAG;cAACf,KAAK,EAAC;YAAS;cAAA7B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,GAE9C;UACD;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACU,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAACzD,EAAA,CA1bID,mBAAmB;EAAA,QAesBJ,OAAO;AAAA;AAAAwG,EAAA,GAfhDpG,mBAAmB;AA4bzB,eAAeA,mBAAmB;AAAC,IAAAoG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}