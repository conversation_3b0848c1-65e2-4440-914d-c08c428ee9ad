{"name": "@babel/plugin-proposal-numeric-separator", "version": "7.18.6", "description": "Remove numeric separators from Decimal, Binary, Hex and Octal literals", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-proposal-numeric-separator"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-numeric-separator", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^7.18.6", "@babel/plugin-syntax-numeric-separator": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.18.6", "@babel/helper-plugin-test-runner": "^7.18.6", "@babel/traverse": "^7.18.6", "@babel/types": "^7.18.6"}, "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "commonjs"}