{"ast": null, "code": "var _jsxFileName = \"E:\\\\PythonProjects\\\\React.js\\\\Smart AI Assisstant\\\\src\\\\components\\\\AppearanceSettings.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Box, Card, CardContent, Typography, Button, ButtonGroup, Grid, FormControl, InputLabel, Select, MenuItem, Slider, Switch, List, ListItem, ListItemIcon, ListItemText, ListItemSecondaryAction, Alert, Chip } from '@mui/material';\nimport { Palette as PaletteIcon, Language as LanguageIcon, TextFields as TextFieldsIcon, Brightness4 as DarkModeIcon, Brightness7 as LightModeIcon, BrightnessAuto as AutoModeIcon, Zoom as ZoomIcon, Animation as AnimationIcon } from '@mui/icons-material';\nimport { useScan } from '../contexts/ScanContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AppearanceSettings = ({\n  showLanguageOnly = false\n}) => {\n  _s();\n  var _languages$find2;\n  const {\n    darkMode,\n    language,\n    toggleDarkMode,\n    setLanguage\n  } = useScan();\n  const [alert, setAlert] = useState({\n    show: false,\n    message: '',\n    severity: 'success'\n  });\n\n  // Local appearance settings\n  const [appearanceSettings, setAppearanceSettings] = useState({\n    fontSize: 16,\n    animations: true,\n    compactMode: false,\n    highContrast: false,\n    reducedMotion: false\n  });\n  const showAlert = (message, severity = 'success') => {\n    setAlert({\n      show: true,\n      message,\n      severity\n    });\n    setTimeout(() => setAlert({\n      show: false,\n      message: '',\n      severity: 'success'\n    }), 3000);\n  };\n  const languages = [{\n    code: 'en',\n    label: '🇺🇸 English',\n    name: 'English'\n  }, {\n    code: 'ar',\n    label: '🇸🇦 العربية',\n    name: 'Arabic'\n  }, {\n    code: 'fr',\n    label: '🇫🇷 Français',\n    name: 'French'\n  }, {\n    code: 'es',\n    label: '🇪🇸 Español',\n    name: 'Spanish'\n  }, {\n    code: 'de',\n    label: '🇩🇪 Deutsch',\n    name: 'German'\n  }, {\n    code: 'zh',\n    label: '🇨🇳 中文',\n    name: 'Chinese'\n  }, {\n    code: 'ja',\n    label: '🇯🇵 日本語',\n    name: 'Japanese'\n  }, {\n    code: 'ko',\n    label: '🇰🇷 한국어',\n    name: 'Korean'\n  }];\n  const fontSizes = [{\n    value: 12,\n    label: 'Small'\n  }, {\n    value: 14,\n    label: 'Medium'\n  }, {\n    value: 16,\n    label: 'Large'\n  }, {\n    value: 18,\n    label: 'Extra Large'\n  }, {\n    value: 20,\n    label: 'Huge'\n  }];\n  const handleLanguageChange = langCode => {\n    var _languages$find;\n    setLanguage(langCode);\n    const langName = ((_languages$find = languages.find(l => l.code === langCode)) === null || _languages$find === void 0 ? void 0 : _languages$find.name) || 'Unknown';\n    showAlert(`Language changed to ${langName}`, 'success');\n  };\n  const handleThemeChange = () => {\n    toggleDarkMode();\n    showAlert(`Switched to ${!darkMode ? 'dark' : 'light'} mode`, 'success');\n  };\n  const handleAppearanceSettingChange = (setting, value) => {\n    setAppearanceSettings(prev => ({\n      ...prev,\n      [setting]: value\n    }));\n    const settingName = setting.replace(/([A-Z])/g, ' $1').toLowerCase();\n    showAlert(`${settingName} ${typeof value === 'boolean' ? value ? 'enabled' : 'disabled' : 'updated'}`, 'info');\n  };\n  if (showLanguageOnly) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      children: [alert.show && /*#__PURE__*/_jsxDEV(Alert, {\n        severity: alert.severity,\n        sx: {\n          mb: 3\n        },\n        children: alert.message\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 98,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h4\",\n        gutterBottom: true,\n        fontWeight: \"bold\",\n        children: \"Language Settings\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 103,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body1\",\n        color: \"text.secondary\",\n        sx: {\n          mb: 4\n        },\n        children: \"Choose your preferred language for the interface\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 106,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Card, {\n        children: /*#__PURE__*/_jsxDEV(CardContent, {\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              mb: 3\n            },\n            children: [/*#__PURE__*/_jsxDEV(LanguageIcon, {\n              sx: {\n                mr: 2,\n                color: 'primary.main'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 113,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              fontWeight: \"600\",\n              children: \"Interface Language\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 114,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 112,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            container: true,\n            spacing: 2,\n            children: languages.map(lang => /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              md: 4,\n              children: /*#__PURE__*/_jsxDEV(Button, {\n                variant: language === lang.code ? 'contained' : 'outlined',\n                onClick: () => handleLanguageChange(lang.code),\n                fullWidth: true,\n                sx: {\n                  borderRadius: 3,\n                  py: 1.5,\n                  justifyContent: 'flex-start',\n                  background: language === lang.code ? 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' : 'transparent',\n                  '&:hover': {\n                    background: language === lang.code ? 'linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)' : 'rgba(102, 126, 234, 0.1)'\n                  }\n                },\n                children: lang.label\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 122,\n                columnNumber: 19\n              }, this)\n            }, lang.code, false, {\n              fileName: _jsxFileName,\n              lineNumber: 121,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 119,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 111,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 110,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 96,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [alert.show && /*#__PURE__*/_jsxDEV(Alert, {\n      severity: alert.severity,\n      sx: {\n        mb: 3\n      },\n      children: alert.message\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 155,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h4\",\n      gutterBottom: true,\n      fontWeight: \"bold\",\n      children: \"Appearance Settings\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 160,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"body1\",\n      color: \"text.secondary\",\n      sx: {\n        mb: 4\n      },\n      children: \"Customize the look and feel of your interface\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 163,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        mb: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            mb: 3\n          },\n          children: [/*#__PURE__*/_jsxDEV(PaletteIcon, {\n            sx: {\n              mr: 2,\n              color: 'primary.main'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 171,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            fontWeight: \"600\",\n            children: \"Theme\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 172,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 170,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ButtonGroup, {\n          variant: \"outlined\",\n          fullWidth: true,\n          sx: {\n            mb: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            variant: !darkMode ? 'contained' : 'outlined',\n            startIcon: /*#__PURE__*/_jsxDEV(LightModeIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 180,\n              columnNumber: 26\n            }, this),\n            onClick: !darkMode ? undefined : handleThemeChange,\n            sx: {\n              background: !darkMode ? 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)' : 'transparent',\n              '&:hover': {\n                background: !darkMode ? 'linear-gradient(135deg, #e081e8 0%, #e34759 100%)' : 'rgba(240, 147, 251, 0.1)'\n              }\n            },\n            children: \"Light Mode\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 178,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: darkMode ? 'contained' : 'outlined',\n            startIcon: /*#__PURE__*/_jsxDEV(DarkModeIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 193,\n              columnNumber: 26\n            }, this),\n            onClick: darkMode ? undefined : handleThemeChange,\n            sx: {\n              background: darkMode ? 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' : 'transparent',\n              '&:hover': {\n                background: darkMode ? 'linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)' : 'rgba(102, 126, 234, 0.1)'\n              }\n            },\n            children: \"Dark Mode\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 191,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 177,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          color: \"text.secondary\",\n          children: [\"Current theme: \", darkMode ? 'Dark Mode' : 'Light Mode']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 206,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 169,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 168,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        mb: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            mb: 3\n          },\n          children: [/*#__PURE__*/_jsxDEV(LanguageIcon, {\n            sx: {\n              mr: 2,\n              color: 'primary.main'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 216,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            fontWeight: \"600\",\n            children: \"Language\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 217,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 215,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n          fullWidth: true,\n          sx: {\n            mb: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n            children: \"Interface Language\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 223,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Select, {\n            value: language,\n            label: \"Interface Language\",\n            onChange: e => handleLanguageChange(e.target.value),\n            children: languages.map(lang => /*#__PURE__*/_jsxDEV(MenuItem, {\n              value: lang.code,\n              children: lang.label\n            }, lang.code, false, {\n              fileName: _jsxFileName,\n              lineNumber: 230,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 224,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 222,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          color: \"text.secondary\",\n          children: [\"Current language: \", ((_languages$find2 = languages.find(l => l.code === language)) === null || _languages$find2 === void 0 ? void 0 : _languages$find2.name) || 'English']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 237,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 214,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 213,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        mb: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            mb: 3\n          },\n          children: [/*#__PURE__*/_jsxDEV(TextFieldsIcon, {\n            sx: {\n              mr: 2,\n              color: 'primary.main'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 247,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            fontWeight: \"600\",\n            children: \"Typography\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 248,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 246,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          color: \"text.secondary\",\n          sx: {\n            mb: 2\n          },\n          children: [\"Font Size: \", appearanceSettings.fontSize, \"px\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 253,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Slider, {\n          value: appearanceSettings.fontSize,\n          onChange: (e, value) => handleAppearanceSettingChange('fontSize', value),\n          min: 12,\n          max: 24,\n          step: 2,\n          marks: fontSizes,\n          valueLabelDisplay: \"auto\",\n          sx: {\n            mb: 2\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 257,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body1\",\n          sx: {\n            fontSize: `${appearanceSettings.fontSize}px`,\n            p: 2,\n            border: '1px solid',\n            borderColor: 'divider',\n            borderRadius: 2,\n            background: 'rgba(102, 126, 234, 0.05)'\n          },\n          children: \"This is a preview of how text will appear with your selected font size.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 268,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 245,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 244,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        mb: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          fontWeight: \"600\",\n          sx: {\n            mb: 2\n          },\n          children: \"Accessibility\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 287,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(List, {\n          children: [/*#__PURE__*/_jsxDEV(ListItem, {\n            children: [/*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: \"High Contrast\",\n              secondary: \"Increase contrast for better visibility\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 293,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ListItemSecondaryAction, {\n              children: /*#__PURE__*/_jsxDEV(Switch, {\n                checked: appearanceSettings.highContrast,\n                onChange: e => handleAppearanceSettingChange('highContrast', e.target.checked)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 298,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 297,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 292,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n            children: [/*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: \"Reduced Motion\",\n              secondary: \"Minimize animations and transitions\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 306,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ListItemSecondaryAction, {\n              children: /*#__PURE__*/_jsxDEV(Switch, {\n                checked: appearanceSettings.reducedMotion,\n                onChange: e => handleAppearanceSettingChange('reducedMotion', e.target.checked)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 311,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 310,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 305,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n            children: [/*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: \"Compact Mode\",\n              secondary: \"Reduce spacing for more content\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 319,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ListItemSecondaryAction, {\n              children: /*#__PURE__*/_jsxDEV(Switch, {\n                checked: appearanceSettings.compactMode,\n                onChange: e => handleAppearanceSettingChange('compactMode', e.target.checked)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 324,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 323,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 318,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 291,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 286,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 285,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            mb: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(AnimationIcon, {\n            sx: {\n              mr: 2,\n              color: 'primary.main'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 338,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            fontWeight: \"600\",\n            children: \"Animations\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 339,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 337,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'space-between'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              fontWeight: \"500\",\n              children: \"Enable Animations\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 346,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: \"Smooth transitions and visual effects\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 349,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 345,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Switch, {\n            checked: appearanceSettings.animations,\n            onChange: e => handleAppearanceSettingChange('animations', e.target.checked)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 353,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 344,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 336,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 335,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 152,\n    columnNumber: 5\n  }, this);\n};\n_s(AppearanceSettings, \"KY8ulC/hBYbVZIj0DBwMYgBIldQ=\", false, function () {\n  return [useScan];\n});\n_c = AppearanceSettings;\nexport default AppearanceSettings;\nvar _c;\n$RefreshReg$(_c, \"AppearanceSettings\");", "map": {"version": 3, "names": ["React", "useState", "Box", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Typography", "<PERSON><PERSON>", "ButtonGroup", "Grid", "FormControl", "InputLabel", "Select", "MenuItem", "Slide<PERSON>", "Switch", "List", "ListItem", "ListItemIcon", "ListItemText", "ListItemSecondaryAction", "<PERSON><PERSON>", "Chip", "Palette", "PaletteIcon", "Language", "LanguageIcon", "TextFields", "TextFieldsIcon", "Brightness4", "DarkModeIcon", "Brightness7", "LightModeIcon", "BrightnessAuto", "AutoModeIcon", "Zoom", "ZoomIcon", "Animation", "AnimationIcon", "useScan", "jsxDEV", "_jsxDEV", "AppearanceSettings", "showLanguageOnly", "_s", "_languages$find2", "darkMode", "language", "toggleDarkMode", "setLanguage", "alert", "<PERSON><PERSON><PERSON><PERSON>", "show", "message", "severity", "appearanceSettings", "setAppearanceSettings", "fontSize", "animations", "compactMode", "highContrast", "reducedMotion", "show<PERSON><PERSON><PERSON>", "setTimeout", "languages", "code", "label", "name", "fontSizes", "value", "handleLanguageChange", "langCode", "_languages$find", "langName", "find", "l", "handleThemeChange", "handleAppearanceSettingChange", "setting", "prev", "<PERSON><PERSON><PERSON>", "replace", "toLowerCase", "children", "sx", "mb", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "gutterBottom", "fontWeight", "color", "display", "alignItems", "mr", "container", "spacing", "map", "lang", "item", "xs", "sm", "md", "onClick", "fullWidth", "borderRadius", "py", "justifyContent", "background", "startIcon", "undefined", "onChange", "e", "target", "min", "max", "step", "marks", "valueLabelDisplay", "p", "border", "borderColor", "primary", "secondary", "checked", "_c", "$RefreshReg$"], "sources": ["E:/PythonProjects/React.js/Smart AI Assisstant/src/components/AppearanceSettings.jsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  Box,\n  Card,\n  CardContent,\n  Typography,\n  Button,\n  ButtonGroup,\n  Grid,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  Slider,\n  Switch,\n  List,\n  ListItem,\n  ListItemIcon,\n  ListItemText,\n  ListItemSecondaryAction,\n  Alert,\n  Chip,\n} from '@mui/material';\nimport {\n  Palette as PaletteIcon,\n  Language as LanguageIcon,\n  TextFields as TextFieldsIcon,\n  Brightness4 as DarkModeIcon,\n  Brightness7 as LightModeIcon,\n  BrightnessAuto as AutoModeIcon,\n  Zoom as ZoomIcon,\n  Animation as AnimationIcon,\n} from '@mui/icons-material';\nimport { useScan } from '../contexts/ScanContext';\n\nconst AppearanceSettings = ({ showLanguageOnly = false }) => {\n  const { darkMode, language, toggleDarkMode, setLanguage } = useScan();\n  const [alert, setAlert] = useState({ show: false, message: '', severity: 'success' });\n  \n  // Local appearance settings\n  const [appearanceSettings, setAppearanceSettings] = useState({\n    fontSize: 16,\n    animations: true,\n    compactMode: false,\n    highContrast: false,\n    reducedMotion: false,\n  });\n\n  const showAlert = (message, severity = 'success') => {\n    setAlert({ show: true, message, severity });\n    setTimeout(() => setAlert({ show: false, message: '', severity: 'success' }), 3000);\n  };\n\n  const languages = [\n    { code: 'en', label: '🇺🇸 English', name: 'English' },\n    { code: 'ar', label: '🇸🇦 العربية', name: 'Arabic' },\n    { code: 'fr', label: '🇫🇷 Français', name: 'French' },\n    { code: 'es', label: '🇪🇸 Español', name: 'Spanish' },\n    { code: 'de', label: '🇩🇪 Deutsch', name: 'German' },\n    { code: 'zh', label: '🇨🇳 中文', name: 'Chinese' },\n    { code: 'ja', label: '🇯🇵 日本語', name: 'Japanese' },\n    { code: 'ko', label: '🇰🇷 한국어', name: 'Korean' },\n  ];\n\n  const fontSizes = [\n    { value: 12, label: 'Small' },\n    { value: 14, label: 'Medium' },\n    { value: 16, label: 'Large' },\n    { value: 18, label: 'Extra Large' },\n    { value: 20, label: 'Huge' },\n  ];\n\n  const handleLanguageChange = (langCode) => {\n    setLanguage(langCode);\n    const langName = languages.find(l => l.code === langCode)?.name || 'Unknown';\n    showAlert(`Language changed to ${langName}`, 'success');\n  };\n\n  const handleThemeChange = () => {\n    toggleDarkMode();\n    showAlert(`Switched to ${!darkMode ? 'dark' : 'light'} mode`, 'success');\n  };\n\n  const handleAppearanceSettingChange = (setting, value) => {\n    setAppearanceSettings(prev => ({\n      ...prev,\n      [setting]: value,\n    }));\n    \n    const settingName = setting.replace(/([A-Z])/g, ' $1').toLowerCase();\n    showAlert(`${settingName} ${typeof value === 'boolean' ? (value ? 'enabled' : 'disabled') : 'updated'}`, 'info');\n  };\n\n  if (showLanguageOnly) {\n    return (\n      <Box>\n        {alert.show && (\n          <Alert severity={alert.severity} sx={{ mb: 3 }}>\n            {alert.message}\n          </Alert>\n        )}\n\n        <Typography variant=\"h4\" gutterBottom fontWeight=\"bold\">\n          Language Settings\n        </Typography>\n        <Typography variant=\"body1\" color=\"text.secondary\" sx={{ mb: 4 }}>\n          Choose your preferred language for the interface\n        </Typography>\n\n        <Card>\n          <CardContent>\n            <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>\n              <LanguageIcon sx={{ mr: 2, color: 'primary.main' }} />\n              <Typography variant=\"h6\" fontWeight=\"600\">\n                Interface Language\n              </Typography>\n            </Box>\n            \n            <Grid container spacing={2}>\n              {languages.map((lang) => (\n                <Grid item xs={12} sm={6} md={4} key={lang.code}>\n                  <Button\n                    variant={language === lang.code ? 'contained' : 'outlined'}\n                    onClick={() => handleLanguageChange(lang.code)}\n                    fullWidth\n                    sx={{\n                      borderRadius: 3,\n                      py: 1.5,\n                      justifyContent: 'flex-start',\n                      background: language === lang.code\n                        ? 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'\n                        : 'transparent',\n                      '&:hover': {\n                        background: language === lang.code\n                          ? 'linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)'\n                          : 'rgba(102, 126, 234, 0.1)',\n                      },\n                    }}\n                  >\n                    {lang.label}\n                  </Button>\n                </Grid>\n              ))}\n            </Grid>\n          </CardContent>\n        </Card>\n      </Box>\n    );\n  }\n\n  return (\n    <Box>\n      {/* Alert */}\n      {alert.show && (\n        <Alert severity={alert.severity} sx={{ mb: 3 }}>\n          {alert.message}\n        </Alert>\n      )}\n\n      <Typography variant=\"h4\" gutterBottom fontWeight=\"bold\">\n        Appearance Settings\n      </Typography>\n      <Typography variant=\"body1\" color=\"text.secondary\" sx={{ mb: 4 }}>\n        Customize the look and feel of your interface\n      </Typography>\n\n      {/* Theme Settings */}\n      <Card sx={{ mb: 3 }}>\n        <CardContent>\n          <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>\n            <PaletteIcon sx={{ mr: 2, color: 'primary.main' }} />\n            <Typography variant=\"h6\" fontWeight=\"600\">\n              Theme\n            </Typography>\n          </Box>\n          \n          <ButtonGroup variant=\"outlined\" fullWidth sx={{ mb: 2 }}>\n            <Button\n              variant={!darkMode ? 'contained' : 'outlined'}\n              startIcon={<LightModeIcon />}\n              onClick={!darkMode ? undefined : handleThemeChange}\n              sx={{\n                background: !darkMode ? 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)' : 'transparent',\n                '&:hover': {\n                  background: !darkMode ? 'linear-gradient(135deg, #e081e8 0%, #e34759 100%)' : 'rgba(240, 147, 251, 0.1)',\n                },\n              }}\n            >\n              Light Mode\n            </Button>\n            <Button\n              variant={darkMode ? 'contained' : 'outlined'}\n              startIcon={<DarkModeIcon />}\n              onClick={darkMode ? undefined : handleThemeChange}\n              sx={{\n                background: darkMode ? 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' : 'transparent',\n                '&:hover': {\n                  background: darkMode ? 'linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)' : 'rgba(102, 126, 234, 0.1)',\n                },\n              }}\n            >\n              Dark Mode\n            </Button>\n          </ButtonGroup>\n          \n          <Typography variant=\"body2\" color=\"text.secondary\">\n            Current theme: {darkMode ? 'Dark Mode' : 'Light Mode'}\n          </Typography>\n        </CardContent>\n      </Card>\n\n      {/* Language Settings */}\n      <Card sx={{ mb: 3 }}>\n        <CardContent>\n          <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>\n            <LanguageIcon sx={{ mr: 2, color: 'primary.main' }} />\n            <Typography variant=\"h6\" fontWeight=\"600\">\n              Language\n            </Typography>\n          </Box>\n          \n          <FormControl fullWidth sx={{ mb: 2 }}>\n            <InputLabel>Interface Language</InputLabel>\n            <Select\n              value={language}\n              label=\"Interface Language\"\n              onChange={(e) => handleLanguageChange(e.target.value)}\n            >\n              {languages.map((lang) => (\n                <MenuItem key={lang.code} value={lang.code}>\n                  {lang.label}\n                </MenuItem>\n              ))}\n            </Select>\n          </FormControl>\n          \n          <Typography variant=\"body2\" color=\"text.secondary\">\n            Current language: {languages.find(l => l.code === language)?.name || 'English'}\n          </Typography>\n        </CardContent>\n      </Card>\n\n      {/* Typography Settings */}\n      <Card sx={{ mb: 3 }}>\n        <CardContent>\n          <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>\n            <TextFieldsIcon sx={{ mr: 2, color: 'primary.main' }} />\n            <Typography variant=\"h6\" fontWeight=\"600\">\n              Typography\n            </Typography>\n          </Box>\n          \n          <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 2 }}>\n            Font Size: {appearanceSettings.fontSize}px\n          </Typography>\n          \n          <Slider\n            value={appearanceSettings.fontSize}\n            onChange={(e, value) => handleAppearanceSettingChange('fontSize', value)}\n            min={12}\n            max={24}\n            step={2}\n            marks={fontSizes}\n            valueLabelDisplay=\"auto\"\n            sx={{ mb: 2 }}\n          />\n          \n          <Typography \n            variant=\"body1\" \n            sx={{ \n              fontSize: `${appearanceSettings.fontSize}px`,\n              p: 2,\n              border: '1px solid',\n              borderColor: 'divider',\n              borderRadius: 2,\n              background: 'rgba(102, 126, 234, 0.05)',\n            }}\n          >\n            This is a preview of how text will appear with your selected font size.\n          </Typography>\n        </CardContent>\n      </Card>\n\n      {/* Accessibility Settings */}\n      <Card sx={{ mb: 3 }}>\n        <CardContent>\n          <Typography variant=\"h6\" fontWeight=\"600\" sx={{ mb: 2 }}>\n            Accessibility\n          </Typography>\n          \n          <List>\n            <ListItem>\n              <ListItemText\n                primary=\"High Contrast\"\n                secondary=\"Increase contrast for better visibility\"\n              />\n              <ListItemSecondaryAction>\n                <Switch\n                  checked={appearanceSettings.highContrast}\n                  onChange={(e) => handleAppearanceSettingChange('highContrast', e.target.checked)}\n                />\n              </ListItemSecondaryAction>\n            </ListItem>\n            \n            <ListItem>\n              <ListItemText\n                primary=\"Reduced Motion\"\n                secondary=\"Minimize animations and transitions\"\n              />\n              <ListItemSecondaryAction>\n                <Switch\n                  checked={appearanceSettings.reducedMotion}\n                  onChange={(e) => handleAppearanceSettingChange('reducedMotion', e.target.checked)}\n                />\n              </ListItemSecondaryAction>\n            </ListItem>\n            \n            <ListItem>\n              <ListItemText\n                primary=\"Compact Mode\"\n                secondary=\"Reduce spacing for more content\"\n              />\n              <ListItemSecondaryAction>\n                <Switch\n                  checked={appearanceSettings.compactMode}\n                  onChange={(e) => handleAppearanceSettingChange('compactMode', e.target.checked)}\n                />\n              </ListItemSecondaryAction>\n            </ListItem>\n          </List>\n        </CardContent>\n      </Card>\n\n      {/* Animation Settings */}\n      <Card>\n        <CardContent>\n          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>\n            <AnimationIcon sx={{ mr: 2, color: 'primary.main' }} />\n            <Typography variant=\"h6\" fontWeight=\"600\">\n              Animations\n            </Typography>\n          </Box>\n          \n          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n            <Box>\n              <Typography variant=\"body1\" fontWeight=\"500\">\n                Enable Animations\n              </Typography>\n              <Typography variant=\"body2\" color=\"text.secondary\">\n                Smooth transitions and visual effects\n              </Typography>\n            </Box>\n            <Switch\n              checked={appearanceSettings.animations}\n              onChange={(e) => handleAppearanceSettingChange('animations', e.target.checked)}\n            />\n          </Box>\n        </CardContent>\n      </Card>\n    </Box>\n  );\n};\n\nexport default AppearanceSettings;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,GAAG,EACHC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,WAAW,EACXC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,MAAM,EACNC,MAAM,EACNC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,YAAY,EACZC,uBAAuB,EACvBC,KAAK,EACLC,IAAI,QACC,eAAe;AACtB,SACEC,OAAO,IAAIC,WAAW,EACtBC,QAAQ,IAAIC,YAAY,EACxBC,UAAU,IAAIC,cAAc,EAC5BC,WAAW,IAAIC,YAAY,EAC3BC,WAAW,IAAIC,aAAa,EAC5BC,cAAc,IAAIC,YAAY,EAC9BC,IAAI,IAAIC,QAAQ,EAChBC,SAAS,IAAIC,aAAa,QACrB,qBAAqB;AAC5B,SAASC,OAAO,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElD,MAAMC,kBAAkB,GAAGA,CAAC;EAAEC,gBAAgB,GAAG;AAAM,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,gBAAA;EAC3D,MAAM;IAAEC,QAAQ;IAAEC,QAAQ;IAAEC,cAAc;IAAEC;EAAY,CAAC,GAAGV,OAAO,CAAC,CAAC;EACrE,MAAM,CAACW,KAAK,EAAEC,QAAQ,CAAC,GAAGjD,QAAQ,CAAC;IAAEkD,IAAI,EAAE,KAAK;IAAEC,OAAO,EAAE,EAAE;IAAEC,QAAQ,EAAE;EAAU,CAAC,CAAC;;EAErF;EACA,MAAM,CAACC,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGtD,QAAQ,CAAC;IAC3DuD,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,IAAI;IAChBC,WAAW,EAAE,KAAK;IAClBC,YAAY,EAAE,KAAK;IACnBC,aAAa,EAAE;EACjB,CAAC,CAAC;EAEF,MAAMC,SAAS,GAAGA,CAACT,OAAO,EAAEC,QAAQ,GAAG,SAAS,KAAK;IACnDH,QAAQ,CAAC;MAAEC,IAAI,EAAE,IAAI;MAAEC,OAAO;MAAEC;IAAS,CAAC,CAAC;IAC3CS,UAAU,CAAC,MAAMZ,QAAQ,CAAC;MAAEC,IAAI,EAAE,KAAK;MAAEC,OAAO,EAAE,EAAE;MAAEC,QAAQ,EAAE;IAAU,CAAC,CAAC,EAAE,IAAI,CAAC;EACrF,CAAC;EAED,MAAMU,SAAS,GAAG,CAChB;IAAEC,IAAI,EAAE,IAAI;IAAEC,KAAK,EAAE,cAAc;IAAEC,IAAI,EAAE;EAAU,CAAC,EACtD;IAAEF,IAAI,EAAE,IAAI;IAAEC,KAAK,EAAE,cAAc;IAAEC,IAAI,EAAE;EAAS,CAAC,EACrD;IAAEF,IAAI,EAAE,IAAI;IAAEC,KAAK,EAAE,eAAe;IAAEC,IAAI,EAAE;EAAS,CAAC,EACtD;IAAEF,IAAI,EAAE,IAAI;IAAEC,KAAK,EAAE,cAAc;IAAEC,IAAI,EAAE;EAAU,CAAC,EACtD;IAAEF,IAAI,EAAE,IAAI;IAAEC,KAAK,EAAE,cAAc;IAAEC,IAAI,EAAE;EAAS,CAAC,EACrD;IAAEF,IAAI,EAAE,IAAI;IAAEC,KAAK,EAAE,SAAS;IAAEC,IAAI,EAAE;EAAU,CAAC,EACjD;IAAEF,IAAI,EAAE,IAAI;IAAEC,KAAK,EAAE,UAAU;IAAEC,IAAI,EAAE;EAAW,CAAC,EACnD;IAAEF,IAAI,EAAE,IAAI;IAAEC,KAAK,EAAE,UAAU;IAAEC,IAAI,EAAE;EAAS,CAAC,CAClD;EAED,MAAMC,SAAS,GAAG,CAChB;IAAEC,KAAK,EAAE,EAAE;IAAEH,KAAK,EAAE;EAAQ,CAAC,EAC7B;IAAEG,KAAK,EAAE,EAAE;IAAEH,KAAK,EAAE;EAAS,CAAC,EAC9B;IAAEG,KAAK,EAAE,EAAE;IAAEH,KAAK,EAAE;EAAQ,CAAC,EAC7B;IAAEG,KAAK,EAAE,EAAE;IAAEH,KAAK,EAAE;EAAc,CAAC,EACnC;IAAEG,KAAK,EAAE,EAAE;IAAEH,KAAK,EAAE;EAAO,CAAC,CAC7B;EAED,MAAMI,oBAAoB,GAAIC,QAAQ,IAAK;IAAA,IAAAC,eAAA;IACzCvB,WAAW,CAACsB,QAAQ,CAAC;IACrB,MAAME,QAAQ,GAAG,EAAAD,eAAA,GAAAR,SAAS,CAACU,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACV,IAAI,KAAKM,QAAQ,CAAC,cAAAC,eAAA,uBAAxCA,eAAA,CAA0CL,IAAI,KAAI,SAAS;IAC5EL,SAAS,CAAC,uBAAuBW,QAAQ,EAAE,EAAE,SAAS,CAAC;EACzD,CAAC;EAED,MAAMG,iBAAiB,GAAGA,CAAA,KAAM;IAC9B5B,cAAc,CAAC,CAAC;IAChBc,SAAS,CAAC,eAAe,CAAChB,QAAQ,GAAG,MAAM,GAAG,OAAO,OAAO,EAAE,SAAS,CAAC;EAC1E,CAAC;EAED,MAAM+B,6BAA6B,GAAGA,CAACC,OAAO,EAAET,KAAK,KAAK;IACxDb,qBAAqB,CAACuB,IAAI,KAAK;MAC7B,GAAGA,IAAI;MACP,CAACD,OAAO,GAAGT;IACb,CAAC,CAAC,CAAC;IAEH,MAAMW,WAAW,GAAGF,OAAO,CAACG,OAAO,CAAC,UAAU,EAAE,KAAK,CAAC,CAACC,WAAW,CAAC,CAAC;IACpEpB,SAAS,CAAC,GAAGkB,WAAW,IAAI,OAAOX,KAAK,KAAK,SAAS,GAAIA,KAAK,GAAG,SAAS,GAAG,UAAU,GAAI,SAAS,EAAE,EAAE,MAAM,CAAC;EAClH,CAAC;EAED,IAAI1B,gBAAgB,EAAE;IACpB,oBACEF,OAAA,CAACtC,GAAG;MAAAgF,QAAA,GACDjC,KAAK,CAACE,IAAI,iBACTX,OAAA,CAACpB,KAAK;QAACiC,QAAQ,EAAEJ,KAAK,CAACI,QAAS;QAAC8B,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAF,QAAA,EAC5CjC,KAAK,CAACG;MAAO;QAAAiC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CACR,eAEDhD,OAAA,CAACnC,UAAU;QAACoF,OAAO,EAAC,IAAI;QAACC,YAAY;QAACC,UAAU,EAAC,MAAM;QAAAT,QAAA,EAAC;MAExD;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbhD,OAAA,CAACnC,UAAU;QAACoF,OAAO,EAAC,OAAO;QAACG,KAAK,EAAC,gBAAgB;QAACT,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAF,QAAA,EAAC;MAElE;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAEbhD,OAAA,CAACrC,IAAI;QAAA+E,QAAA,eACH1C,OAAA,CAACpC,WAAW;UAAA8E,QAAA,gBACV1C,OAAA,CAACtC,GAAG;YAACiF,EAAE,EAAE;cAAEU,OAAO,EAAE,MAAM;cAAEC,UAAU,EAAE,QAAQ;cAAEV,EAAE,EAAE;YAAE,CAAE;YAAAF,QAAA,gBACxD1C,OAAA,CAACf,YAAY;cAAC0D,EAAE,EAAE;gBAAEY,EAAE,EAAE,CAAC;gBAAEH,KAAK,EAAE;cAAe;YAAE;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACtDhD,OAAA,CAACnC,UAAU;cAACoF,OAAO,EAAC,IAAI;cAACE,UAAU,EAAC,KAAK;cAAAT,QAAA,EAAC;YAE1C;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAENhD,OAAA,CAAChC,IAAI;YAACwF,SAAS;YAACC,OAAO,EAAE,CAAE;YAAAf,QAAA,EACxBnB,SAAS,CAACmC,GAAG,CAAEC,IAAI,iBAClB3D,OAAA,CAAChC,IAAI;cAAC4F,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAACC,EAAE,EAAE,CAAE;cAAArB,QAAA,eAC9B1C,OAAA,CAAClC,MAAM;gBACLmF,OAAO,EAAE3C,QAAQ,KAAKqD,IAAI,CAACnC,IAAI,GAAG,WAAW,GAAG,UAAW;gBAC3DwC,OAAO,EAAEA,CAAA,KAAMnC,oBAAoB,CAAC8B,IAAI,CAACnC,IAAI,CAAE;gBAC/CyC,SAAS;gBACTtB,EAAE,EAAE;kBACFuB,YAAY,EAAE,CAAC;kBACfC,EAAE,EAAE,GAAG;kBACPC,cAAc,EAAE,YAAY;kBAC5BC,UAAU,EAAE/D,QAAQ,KAAKqD,IAAI,CAACnC,IAAI,GAC9B,mDAAmD,GACnD,aAAa;kBACjB,SAAS,EAAE;oBACT6C,UAAU,EAAE/D,QAAQ,KAAKqD,IAAI,CAACnC,IAAI,GAC9B,mDAAmD,GACnD;kBACN;gBACF,CAAE;gBAAAkB,QAAA,EAEDiB,IAAI,CAAClC;cAAK;gBAAAoB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL;YAAC,GApB2BW,IAAI,CAACnC,IAAI;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAqBzC,CACP;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAEV;EAEA,oBACEhD,OAAA,CAACtC,GAAG;IAAAgF,QAAA,GAEDjC,KAAK,CAACE,IAAI,iBACTX,OAAA,CAACpB,KAAK;MAACiC,QAAQ,EAAEJ,KAAK,CAACI,QAAS;MAAC8B,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAF,QAAA,EAC5CjC,KAAK,CAACG;IAAO;MAAAiC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACT,CACR,eAEDhD,OAAA,CAACnC,UAAU;MAACoF,OAAO,EAAC,IAAI;MAACC,YAAY;MAACC,UAAU,EAAC,MAAM;MAAAT,QAAA,EAAC;IAExD;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eACbhD,OAAA,CAACnC,UAAU;MAACoF,OAAO,EAAC,OAAO;MAACG,KAAK,EAAC,gBAAgB;MAACT,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAF,QAAA,EAAC;IAElE;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eAGbhD,OAAA,CAACrC,IAAI;MAACgF,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAF,QAAA,eAClB1C,OAAA,CAACpC,WAAW;QAAA8E,QAAA,gBACV1C,OAAA,CAACtC,GAAG;UAACiF,EAAE,EAAE;YAAEU,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAEV,EAAE,EAAE;UAAE,CAAE;UAAAF,QAAA,gBACxD1C,OAAA,CAACjB,WAAW;YAAC4D,EAAE,EAAE;cAAEY,EAAE,EAAE,CAAC;cAAEH,KAAK,EAAE;YAAe;UAAE;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACrDhD,OAAA,CAACnC,UAAU;YAACoF,OAAO,EAAC,IAAI;YAACE,UAAU,EAAC,KAAK;YAAAT,QAAA,EAAC;UAE1C;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAENhD,OAAA,CAACjC,WAAW;UAACkF,OAAO,EAAC,UAAU;UAACgB,SAAS;UAACtB,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAAF,QAAA,gBACtD1C,OAAA,CAAClC,MAAM;YACLmF,OAAO,EAAE,CAAC5C,QAAQ,GAAG,WAAW,GAAG,UAAW;YAC9CiE,SAAS,eAAEtE,OAAA,CAACT,aAAa;cAAAsD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC7BgB,OAAO,EAAE,CAAC3D,QAAQ,GAAGkE,SAAS,GAAGpC,iBAAkB;YACnDQ,EAAE,EAAE;cACF0B,UAAU,EAAE,CAAChE,QAAQ,GAAG,mDAAmD,GAAG,aAAa;cAC3F,SAAS,EAAE;gBACTgE,UAAU,EAAE,CAAChE,QAAQ,GAAG,mDAAmD,GAAG;cAChF;YACF,CAAE;YAAAqC,QAAA,EACH;UAED;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACThD,OAAA,CAAClC,MAAM;YACLmF,OAAO,EAAE5C,QAAQ,GAAG,WAAW,GAAG,UAAW;YAC7CiE,SAAS,eAAEtE,OAAA,CAACX,YAAY;cAAAwD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC5BgB,OAAO,EAAE3D,QAAQ,GAAGkE,SAAS,GAAGpC,iBAAkB;YAClDQ,EAAE,EAAE;cACF0B,UAAU,EAAEhE,QAAQ,GAAG,mDAAmD,GAAG,aAAa;cAC1F,SAAS,EAAE;gBACTgE,UAAU,EAAEhE,QAAQ,GAAG,mDAAmD,GAAG;cAC/E;YACF,CAAE;YAAAqC,QAAA,EACH;UAED;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAEdhD,OAAA,CAACnC,UAAU;UAACoF,OAAO,EAAC,OAAO;UAACG,KAAK,EAAC,gBAAgB;UAAAV,QAAA,GAAC,iBAClC,EAACrC,QAAQ,GAAG,WAAW,GAAG,YAAY;QAAA;UAAAwC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGPhD,OAAA,CAACrC,IAAI;MAACgF,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAF,QAAA,eAClB1C,OAAA,CAACpC,WAAW;QAAA8E,QAAA,gBACV1C,OAAA,CAACtC,GAAG;UAACiF,EAAE,EAAE;YAAEU,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAEV,EAAE,EAAE;UAAE,CAAE;UAAAF,QAAA,gBACxD1C,OAAA,CAACf,YAAY;YAAC0D,EAAE,EAAE;cAAEY,EAAE,EAAE,CAAC;cAAEH,KAAK,EAAE;YAAe;UAAE;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACtDhD,OAAA,CAACnC,UAAU;YAACoF,OAAO,EAAC,IAAI;YAACE,UAAU,EAAC,KAAK;YAAAT,QAAA,EAAC;UAE1C;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAENhD,OAAA,CAAC/B,WAAW;UAACgG,SAAS;UAACtB,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAAF,QAAA,gBACnC1C,OAAA,CAAC9B,UAAU;YAAAwE,QAAA,EAAC;UAAkB;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC3ChD,OAAA,CAAC7B,MAAM;YACLyD,KAAK,EAAEtB,QAAS;YAChBmB,KAAK,EAAC,oBAAoB;YAC1B+C,QAAQ,EAAGC,CAAC,IAAK5C,oBAAoB,CAAC4C,CAAC,CAACC,MAAM,CAAC9C,KAAK,CAAE;YAAAc,QAAA,EAErDnB,SAAS,CAACmC,GAAG,CAAEC,IAAI,iBAClB3D,OAAA,CAAC5B,QAAQ;cAAiBwD,KAAK,EAAE+B,IAAI,CAACnC,IAAK;cAAAkB,QAAA,EACxCiB,IAAI,CAAClC;YAAK,GADEkC,IAAI,CAACnC,IAAI;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEd,CACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAEdhD,OAAA,CAACnC,UAAU;UAACoF,OAAO,EAAC,OAAO;UAACG,KAAK,EAAC,gBAAgB;UAAAV,QAAA,GAAC,oBAC/B,EAAC,EAAAtC,gBAAA,GAAAmB,SAAS,CAACU,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACV,IAAI,KAAKlB,QAAQ,CAAC,cAAAF,gBAAA,uBAAxCA,gBAAA,CAA0CsB,IAAI,KAAI,SAAS;QAAA;UAAAmB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGPhD,OAAA,CAACrC,IAAI;MAACgF,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAF,QAAA,eAClB1C,OAAA,CAACpC,WAAW;QAAA8E,QAAA,gBACV1C,OAAA,CAACtC,GAAG;UAACiF,EAAE,EAAE;YAAEU,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAEV,EAAE,EAAE;UAAE,CAAE;UAAAF,QAAA,gBACxD1C,OAAA,CAACb,cAAc;YAACwD,EAAE,EAAE;cAAEY,EAAE,EAAE,CAAC;cAAEH,KAAK,EAAE;YAAe;UAAE;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACxDhD,OAAA,CAACnC,UAAU;YAACoF,OAAO,EAAC,IAAI;YAACE,UAAU,EAAC,KAAK;YAAAT,QAAA,EAAC;UAE1C;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAENhD,OAAA,CAACnC,UAAU;UAACoF,OAAO,EAAC,OAAO;UAACG,KAAK,EAAC,gBAAgB;UAACT,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAAF,QAAA,GAAC,aACrD,EAAC5B,kBAAkB,CAACE,QAAQ,EAAC,IAC1C;QAAA;UAAA6B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAEbhD,OAAA,CAAC3B,MAAM;UACLuD,KAAK,EAAEd,kBAAkB,CAACE,QAAS;UACnCwD,QAAQ,EAAEA,CAACC,CAAC,EAAE7C,KAAK,KAAKQ,6BAA6B,CAAC,UAAU,EAAER,KAAK,CAAE;UACzE+C,GAAG,EAAE,EAAG;UACRC,GAAG,EAAE,EAAG;UACRC,IAAI,EAAE,CAAE;UACRC,KAAK,EAAEnD,SAAU;UACjBoD,iBAAiB,EAAC,MAAM;UACxBpC,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAE;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf,CAAC,eAEFhD,OAAA,CAACnC,UAAU;UACToF,OAAO,EAAC,OAAO;UACfN,EAAE,EAAE;YACF3B,QAAQ,EAAE,GAAGF,kBAAkB,CAACE,QAAQ,IAAI;YAC5CgE,CAAC,EAAE,CAAC;YACJC,MAAM,EAAE,WAAW;YACnBC,WAAW,EAAE,SAAS;YACtBhB,YAAY,EAAE,CAAC;YACfG,UAAU,EAAE;UACd,CAAE;UAAA3B,QAAA,EACH;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGPhD,OAAA,CAACrC,IAAI;MAACgF,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAF,QAAA,eAClB1C,OAAA,CAACpC,WAAW;QAAA8E,QAAA,gBACV1C,OAAA,CAACnC,UAAU;UAACoF,OAAO,EAAC,IAAI;UAACE,UAAU,EAAC,KAAK;UAACR,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAAF,QAAA,EAAC;QAEzD;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAEbhD,OAAA,CAACzB,IAAI;UAAAmE,QAAA,gBACH1C,OAAA,CAACxB,QAAQ;YAAAkE,QAAA,gBACP1C,OAAA,CAACtB,YAAY;cACXyG,OAAO,EAAC,eAAe;cACvBC,SAAS,EAAC;YAAyC;cAAAvC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD,CAAC,eACFhD,OAAA,CAACrB,uBAAuB;cAAA+D,QAAA,eACtB1C,OAAA,CAAC1B,MAAM;gBACL+G,OAAO,EAAEvE,kBAAkB,CAACK,YAAa;gBACzCqD,QAAQ,EAAGC,CAAC,IAAKrC,6BAA6B,CAAC,cAAc,EAAEqC,CAAC,CAACC,MAAM,CAACW,OAAO;cAAE;gBAAAxC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACqB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB,CAAC,eAEXhD,OAAA,CAACxB,QAAQ;YAAAkE,QAAA,gBACP1C,OAAA,CAACtB,YAAY;cACXyG,OAAO,EAAC,gBAAgB;cACxBC,SAAS,EAAC;YAAqC;cAAAvC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD,CAAC,eACFhD,OAAA,CAACrB,uBAAuB;cAAA+D,QAAA,eACtB1C,OAAA,CAAC1B,MAAM;gBACL+G,OAAO,EAAEvE,kBAAkB,CAACM,aAAc;gBAC1CoD,QAAQ,EAAGC,CAAC,IAAKrC,6BAA6B,CAAC,eAAe,EAAEqC,CAAC,CAACC,MAAM,CAACW,OAAO;cAAE;gBAAAxC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACqB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB,CAAC,eAEXhD,OAAA,CAACxB,QAAQ;YAAAkE,QAAA,gBACP1C,OAAA,CAACtB,YAAY;cACXyG,OAAO,EAAC,cAAc;cACtBC,SAAS,EAAC;YAAiC;cAAAvC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5C,CAAC,eACFhD,OAAA,CAACrB,uBAAuB;cAAA+D,QAAA,eACtB1C,OAAA,CAAC1B,MAAM;gBACL+G,OAAO,EAAEvE,kBAAkB,CAACI,WAAY;gBACxCsD,QAAQ,EAAGC,CAAC,IAAKrC,6BAA6B,CAAC,aAAa,EAAEqC,CAAC,CAACC,MAAM,CAACW,OAAO;cAAE;gBAAAxC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACqB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGPhD,OAAA,CAACrC,IAAI;MAAA+E,QAAA,eACH1C,OAAA,CAACpC,WAAW;QAAA8E,QAAA,gBACV1C,OAAA,CAACtC,GAAG;UAACiF,EAAE,EAAE;YAAEU,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAEV,EAAE,EAAE;UAAE,CAAE;UAAAF,QAAA,gBACxD1C,OAAA,CAACH,aAAa;YAAC8C,EAAE,EAAE;cAAEY,EAAE,EAAE,CAAC;cAAEH,KAAK,EAAE;YAAe;UAAE;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACvDhD,OAAA,CAACnC,UAAU;YAACoF,OAAO,EAAC,IAAI;YAACE,UAAU,EAAC,KAAK;YAAAT,QAAA,EAAC;UAE1C;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAENhD,OAAA,CAACtC,GAAG;UAACiF,EAAE,EAAE;YAAEU,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAEc,cAAc,EAAE;UAAgB,CAAE;UAAA1B,QAAA,gBAClF1C,OAAA,CAACtC,GAAG;YAAAgF,QAAA,gBACF1C,OAAA,CAACnC,UAAU;cAACoF,OAAO,EAAC,OAAO;cAACE,UAAU,EAAC,KAAK;cAAAT,QAAA,EAAC;YAE7C;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbhD,OAAA,CAACnC,UAAU;cAACoF,OAAO,EAAC,OAAO;cAACG,KAAK,EAAC,gBAAgB;cAAAV,QAAA,EAAC;YAEnD;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACNhD,OAAA,CAAC1B,MAAM;YACL+G,OAAO,EAAEvE,kBAAkB,CAACG,UAAW;YACvCuD,QAAQ,EAAGC,CAAC,IAAKrC,6BAA6B,CAAC,YAAY,EAAEqC,CAAC,CAACC,MAAM,CAACW,OAAO;UAAE;YAAAxC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAAC7C,EAAA,CAtUIF,kBAAkB;EAAA,QACsCH,OAAO;AAAA;AAAAwF,EAAA,GAD/DrF,kBAAkB;AAwUxB,eAAeA,kBAAkB;AAAC,IAAAqF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}