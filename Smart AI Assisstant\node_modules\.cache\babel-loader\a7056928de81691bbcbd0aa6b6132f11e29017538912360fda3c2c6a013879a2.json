{"ast": null, "code": "var _jsxFileName = \"E:\\\\PythonProjects\\\\React.js\\\\Smart AI Assisstant\\\\src\\\\components\\\\SmartFAB.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Fab, SpeedDial, SpeedDialAction, SpeedDialIcon, Tooltip, Badge, Box, Zoom, Backdrop } from '@mui/material';\nimport { Psychology as AIIcon, Dashboard as DashboardIcon, AutoMode as AutoIcon, Notifications as NotificationsIcon, Timeline as TimelineIcon, Security as SecurityIcon, Warning as WarningIcon, Shield as ShieldIcon } from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst SmartFAB = /*#__PURE__*/_s(/*#__PURE__*/React.memo(_c = _s(() => {\n  _s();\n  const [open, setOpen] = useState(false);\n  const [alertCount, setAlertCount] = useState(3);\n  const [isVisible, setIsVisible] = useState(true);\n  const navigate = useNavigate();\n\n  // Auto-hide on scroll\n  useEffect(() => {\n    let lastScrollY = window.scrollY;\n    const handleScroll = () => {\n      const currentScrollY = window.scrollY;\n      setIsVisible(currentScrollY < lastScrollY || currentScrollY < 100);\n      lastScrollY = currentScrollY;\n    };\n    window.addEventListener('scroll', handleScroll, {\n      passive: true\n    });\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  // Simulate real-time alert updates\n  useEffect(() => {\n    const interval = setInterval(() => {\n      if (Math.random() < 0.2) {\n        // 20% chance every 5 seconds\n        setAlertCount(prev => prev + 1);\n      }\n    }, 5000);\n    return () => clearInterval(interval);\n  }, []);\n  const actions = [{\n    icon: /*#__PURE__*/_jsxDEV(DashboardIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 58,\n      columnNumber: 13\n    }, this),\n    name: 'Smart Dashboard',\n    color: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n    action: () => navigate('/smart-features')\n  }, {\n    icon: /*#__PURE__*/_jsxDEV(TimelineIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 64,\n      columnNumber: 13\n    }, this),\n    name: 'Threat Prediction',\n    color: 'linear-gradient(135deg, #9c27b0 0%, #e91e63 100%)',\n    action: () => navigate('/smart-features')\n  }, {\n    icon: /*#__PURE__*/_jsxDEV(AutoIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 70,\n      columnNumber: 13\n    }, this),\n    name: 'Auto-Scan',\n    color: 'linear-gradient(135deg, #4caf50 0%, #8bc34a 100%)',\n    action: () => navigate('/smart-features')\n  }, {\n    icon: /*#__PURE__*/_jsxDEV(Badge, {\n      badgeContent: alertCount,\n      color: \"error\",\n      max: 99,\n      children: /*#__PURE__*/_jsxDEV(NotificationsIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 78,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 77,\n      columnNumber: 9\n    }, this),\n    name: 'Smart Alerts',\n    color: 'linear-gradient(135deg, #2196f3 0%, #9c27b0 100%)',\n    action: () => navigate('/smart-features')\n  }];\n  const handleOpen = () => setOpen(true);\n  const handleClose = () => setOpen(false);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(Backdrop, {\n      open: open,\n      sx: {\n        zIndex: 1200\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 92,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Zoom, {\n      in: isVisible,\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          position: 'fixed',\n          bottom: 24,\n          right: 24,\n          zIndex: 1300\n        },\n        children: /*#__PURE__*/_jsxDEV(SpeedDial, {\n          ariaLabel: \"Smart AI Features\",\n          icon: /*#__PURE__*/_jsxDEV(Badge, {\n            badgeContent: alertCount,\n            color: \"error\",\n            max: 99,\n            children: /*#__PURE__*/_jsxDEV(SpeedDialIcon, {\n              icon: /*#__PURE__*/_jsxDEV(AIIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 107,\n                columnNumber: 25\n              }, this),\n              openIcon: /*#__PURE__*/_jsxDEV(SecurityIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 108,\n                columnNumber: 29\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 106,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 105,\n            columnNumber: 15\n          }, this),\n          onClose: handleClose,\n          onOpen: handleOpen,\n          open: open,\n          direction: \"up\",\n          sx: {\n            '& .MuiSpeedDial-fab': {\n              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n              color: 'white',\n              width: 68,\n              height: 68,\n              boxShadow: '0 12px 40px rgba(102, 126, 234, 0.4)',\n              backdropFilter: 'blur(10px)',\n              border: '2px solid rgba(255, 255, 255, 0.1)',\n              transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',\n              '&:hover': {\n                background: 'linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)',\n                transform: 'scale(1.15) rotate(5deg)',\n                boxShadow: '0 20px 60px rgba(102, 126, 234, 0.6)'\n              },\n              '&::before': {\n                content: '\"\"',\n                position: 'absolute',\n                top: -4,\n                left: -4,\n                right: -4,\n                bottom: -4,\n                borderRadius: '50%',\n                background: 'linear-gradient(45deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%)',\n                backgroundSize: '400% 400%',\n                animation: 'neuralPulse 4s ease infinite',\n                zIndex: -1,\n                opacity: 0.8\n              },\n              '&::after': {\n                content: '\"\"',\n                position: 'absolute',\n                top: -8,\n                left: -8,\n                right: -8,\n                bottom: -8,\n                borderRadius: '50%',\n                border: '2px solid rgba(102, 126, 234, 0.3)',\n                animation: 'quantumRipple 3s infinite',\n                zIndex: -2\n              },\n              '@keyframes neuralPulse': {\n                '0%': {\n                  backgroundPosition: '0% 50%'\n                },\n                '50%': {\n                  backgroundPosition: '100% 50%'\n                },\n                '100%': {\n                  backgroundPosition: '0% 50%'\n                }\n              },\n              '@keyframes quantumRipple': {\n                '0%': {\n                  transform: 'scale(1)',\n                  opacity: 0.3\n                },\n                '50%': {\n                  transform: 'scale(1.2)',\n                  opacity: 0.1\n                },\n                '100%': {\n                  transform: 'scale(1)',\n                  opacity: 0.3\n                }\n              }\n            }\n          },\n          children: actions.map((action, index) => /*#__PURE__*/_jsxDEV(SpeedDialAction, {\n            icon: action.icon,\n            tooltipTitle: action.name,\n            tooltipOpen: true,\n            onClick: () => {\n              action.action();\n              handleClose();\n            },\n            sx: {\n              '& .MuiSpeedDialAction-fab': {\n                background: action.color,\n                color: 'white',\n                width: 48,\n                height: 48,\n                boxShadow: '0 4px 15px rgba(0,0,0,0.2)',\n                '&:hover': {\n                  transform: 'scale(1.1)',\n                  boxShadow: '0 6px 20px rgba(0,0,0,0.3)'\n                }\n              },\n              '& .MuiSpeedDialAction-staticTooltipLabel': {\n                background: theme => theme.palette.mode === 'dark' ? 'rgba(30, 30, 30, 0.95)' : 'rgba(255, 255, 255, 0.95)',\n                backdropFilter: 'blur(10px)',\n                border: theme => `1px solid ${theme.palette.divider}`,\n                borderRadius: 2,\n                fontSize: '0.875rem',\n                fontWeight: 600,\n                px: 2,\n                py: 1,\n                boxShadow: '0 4px 12px rgba(0,0,0,0.15)'\n              }\n            },\n            FabProps: {\n              style: {\n                animationDelay: `${index * 50}ms`\n              }\n            }\n          }, action.name, false, {\n            fileName: _jsxFileName,\n            lineNumber: 171,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 102,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 94,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 93,\n      columnNumber: 7\n    }, this), alertCount > 5 && /*#__PURE__*/_jsxDEV(Zoom, {\n      in: !open && isVisible,\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          position: 'fixed',\n          bottom: 100,\n          right: 24,\n          zIndex: 1200\n        },\n        children: /*#__PURE__*/_jsxDEV(Tooltip, {\n          title: \"High Priority Threats Detected!\",\n          placement: \"left\",\n          children: /*#__PURE__*/_jsxDEV(Fab, {\n            size: \"medium\",\n            onClick: () => navigate('/smart-features'),\n            sx: {\n              background: 'linear-gradient(135deg, #f44336 0%, #ff5722 100%)',\n              color: 'white',\n              animation: 'pulse 2s infinite',\n              boxShadow: '0 4px 15px rgba(244, 67, 54, 0.4)',\n              '&:hover': {\n                background: 'linear-gradient(135deg, #d32f2f 0%, #f4511e 100%)',\n                transform: 'scale(1.1)'\n              },\n              '@keyframes pulse': {\n                '0%': {\n                  transform: 'scale(1)',\n                  boxShadow: '0 4px 15px rgba(244, 67, 54, 0.4)'\n                },\n                '50%': {\n                  transform: 'scale(1.05)',\n                  boxShadow: '0 8px 25px rgba(244, 67, 54, 0.6)'\n                },\n                '100%': {\n                  transform: 'scale(1)',\n                  boxShadow: '0 4px 15px rgba(244, 67, 54, 0.4)'\n                }\n              }\n            },\n            children: /*#__PURE__*/_jsxDEV(Badge, {\n              badgeContent: alertCount,\n              color: \"error\",\n              max: 99,\n              children: /*#__PURE__*/_jsxDEV(WarningIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 258,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 257,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 229,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 228,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 220,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 219,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Zoom, {\n      in: !open && isVisible,\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          position: 'fixed',\n          bottom: 24,\n          left: 24,\n          zIndex: 1200\n        },\n        children: /*#__PURE__*/_jsxDEV(Tooltip, {\n          title: \"AI Protection Active\",\n          placement: \"right\",\n          children: /*#__PURE__*/_jsxDEV(Fab, {\n            size: \"small\",\n            sx: {\n              background: 'linear-gradient(135deg, #4caf50 0%, #8bc34a 100%)',\n              color: 'white',\n              width: 48,\n              height: 48,\n              boxShadow: '0 4px 15px rgba(76, 175, 80, 0.3)',\n              '&::before': {\n                content: '\"\"',\n                position: 'absolute',\n                top: -2,\n                left: -2,\n                right: -2,\n                bottom: -2,\n                borderRadius: '50%',\n                border: '2px solid #4caf50',\n                opacity: 0.6,\n                animation: 'ripple 2s infinite'\n              },\n              '@keyframes ripple': {\n                '0%': {\n                  transform: 'scale(1)',\n                  opacity: 0.6\n                },\n                '100%': {\n                  transform: 'scale(1.4)',\n                  opacity: 0\n                }\n              }\n            },\n            children: /*#__PURE__*/_jsxDEV(ShieldIcon, {\n              fontSize: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 309,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 277,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 276,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 268,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 267,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n}, \"odnwGnWZyj5jImeIu+IroQaw4hU=\", false, function () {\n  return [useNavigate];\n})), \"odnwGnWZyj5jImeIu+IroQaw4hU=\", false, function () {\n  return [useNavigate];\n});\n_c2 = SmartFAB;\nSmartFAB.displayName = 'SmartFAB';\nexport default SmartFAB;\nvar _c, _c2;\n$RefreshReg$(_c, \"SmartFAB$React.memo\");\n$RefreshReg$(_c2, \"SmartFAB\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Fab", "SpeedDial", "SpeedDialAction", "SpeedDialIcon", "<PERSON><PERSON><PERSON>", "Badge", "Box", "Zoom", "Backdrop", "Psychology", "AIIcon", "Dashboard", "DashboardIcon", "AutoMode", "AutoIcon", "Notifications", "NotificationsIcon", "Timeline", "TimelineIcon", "Security", "SecurityIcon", "Warning", "WarningIcon", "Shield", "ShieldIcon", "useNavigate", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "SmartFAB", "_s", "memo", "_c", "open", "<PERSON><PERSON><PERSON>", "alertCount", "set<PERSON><PERSON>tCount", "isVisible", "setIsVisible", "navigate", "lastScrollY", "window", "scrollY", "handleScroll", "currentScrollY", "addEventListener", "passive", "removeEventListener", "interval", "setInterval", "Math", "random", "prev", "clearInterval", "actions", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "name", "color", "action", "badgeContent", "max", "children", "handleOpen", "handleClose", "sx", "zIndex", "in", "position", "bottom", "right", "aria<PERSON><PERSON><PERSON>", "openIcon", "onClose", "onOpen", "direction", "background", "width", "height", "boxShadow", "<PERSON><PERSON>ilter", "border", "transition", "transform", "content", "top", "left", "borderRadius", "backgroundSize", "animation", "opacity", "backgroundPosition", "map", "index", "tooltipTitle", "tooltipOpen", "onClick", "theme", "palette", "mode", "divider", "fontSize", "fontWeight", "px", "py", "FabProps", "style", "animationDelay", "title", "placement", "size", "_c2", "displayName", "$RefreshReg$"], "sources": ["E:/PythonProjects/React.js/Smart AI Assisstant/src/components/SmartFAB.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Fab,\n  SpeedDial,\n  SpeedDialAction,\n  SpeedDialIcon,\n  Tooltip,\n  Badge,\n  Box,\n  Zoom,\n  Backdrop,\n} from '@mui/material';\nimport {\n  Psychology as AIIcon,\n  Dashboard as DashboardIcon,\n  AutoMode as AutoIcon,\n  Notifications as NotificationsIcon,\n  Timeline as TimelineIcon,\n  Security as SecurityIcon,\n  Warning as WarningIcon,\n  Shield as ShieldIcon,\n} from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\n\nconst SmartFAB = React.memo(() => {\n  const [open, setOpen] = useState(false);\n  const [alertCount, setAlertCount] = useState(3);\n  const [isVisible, setIsVisible] = useState(true);\n  const navigate = useNavigate();\n\n  // Auto-hide on scroll\n  useEffect(() => {\n    let lastScrollY = window.scrollY;\n    \n    const handleScroll = () => {\n      const currentScrollY = window.scrollY;\n      setIsVisible(currentScrollY < lastScrollY || currentScrollY < 100);\n      lastScrollY = currentScrollY;\n    };\n\n    window.addEventListener('scroll', handleScroll, { passive: true });\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  // Simulate real-time alert updates\n  useEffect(() => {\n    const interval = setInterval(() => {\n      if (Math.random() < 0.2) { // 20% chance every 5 seconds\n        setAlertCount(prev => prev + 1);\n      }\n    }, 5000);\n\n    return () => clearInterval(interval);\n  }, []);\n\n  const actions = [\n    {\n      icon: <DashboardIcon />,\n      name: 'Smart Dashboard',\n      color: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n      action: () => navigate('/smart-features'),\n    },\n    {\n      icon: <TimelineIcon />,\n      name: 'Threat Prediction',\n      color: 'linear-gradient(135deg, #9c27b0 0%, #e91e63 100%)',\n      action: () => navigate('/smart-features'),\n    },\n    {\n      icon: <AutoIcon />,\n      name: 'Auto-Scan',\n      color: 'linear-gradient(135deg, #4caf50 0%, #8bc34a 100%)',\n      action: () => navigate('/smart-features'),\n    },\n    {\n      icon: (\n        <Badge badgeContent={alertCount} color=\"error\" max={99}>\n          <NotificationsIcon />\n        </Badge>\n      ),\n      name: 'Smart Alerts',\n      color: 'linear-gradient(135deg, #2196f3 0%, #9c27b0 100%)',\n      action: () => navigate('/smart-features'),\n    },\n  ];\n\n  const handleOpen = () => setOpen(true);\n  const handleClose = () => setOpen(false);\n\n  return (\n    <>\n      <Backdrop open={open} sx={{ zIndex: 1200 }} />\n      <Zoom in={isVisible}>\n        <Box\n          sx={{\n            position: 'fixed',\n            bottom: 24,\n            right: 24,\n            zIndex: 1300,\n          }}\n        >\n          <SpeedDial\n            ariaLabel=\"Smart AI Features\"\n            icon={\n              <Badge badgeContent={alertCount} color=\"error\" max={99}>\n                <SpeedDialIcon\n                  icon={<AIIcon />}\n                  openIcon={<SecurityIcon />}\n                />\n              </Badge>\n            }\n            onClose={handleClose}\n            onOpen={handleOpen}\n            open={open}\n            direction=\"up\"\n            sx={{\n              '& .MuiSpeedDial-fab': {\n                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                color: 'white',\n                width: 68,\n                height: 68,\n                boxShadow: '0 12px 40px rgba(102, 126, 234, 0.4)',\n                backdropFilter: 'blur(10px)',\n                border: '2px solid rgba(255, 255, 255, 0.1)',\n                transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',\n                '&:hover': {\n                  background: 'linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)',\n                  transform: 'scale(1.15) rotate(5deg)',\n                  boxShadow: '0 20px 60px rgba(102, 126, 234, 0.6)',\n                },\n                '&::before': {\n                  content: '\"\"',\n                  position: 'absolute',\n                  top: -4,\n                  left: -4,\n                  right: -4,\n                  bottom: -4,\n                  borderRadius: '50%',\n                  background: 'linear-gradient(45deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%)',\n                  backgroundSize: '400% 400%',\n                  animation: 'neuralPulse 4s ease infinite',\n                  zIndex: -1,\n                  opacity: 0.8,\n                },\n                '&::after': {\n                  content: '\"\"',\n                  position: 'absolute',\n                  top: -8,\n                  left: -8,\n                  right: -8,\n                  bottom: -8,\n                  borderRadius: '50%',\n                  border: '2px solid rgba(102, 126, 234, 0.3)',\n                  animation: 'quantumRipple 3s infinite',\n                  zIndex: -2,\n                },\n                '@keyframes neuralPulse': {\n                  '0%': { backgroundPosition: '0% 50%' },\n                  '50%': { backgroundPosition: '100% 50%' },\n                  '100%': { backgroundPosition: '0% 50%' },\n                },\n                '@keyframes quantumRipple': {\n                  '0%': { transform: 'scale(1)', opacity: 0.3 },\n                  '50%': { transform: 'scale(1.2)', opacity: 0.1 },\n                  '100%': { transform: 'scale(1)', opacity: 0.3 },\n                },\n              },\n            }}\n          >\n            {actions.map((action, index) => (\n              <SpeedDialAction\n                key={action.name}\n                icon={action.icon}\n                tooltipTitle={action.name}\n                tooltipOpen\n                onClick={() => {\n                  action.action();\n                  handleClose();\n                }}\n                sx={{\n                  '& .MuiSpeedDialAction-fab': {\n                    background: action.color,\n                    color: 'white',\n                    width: 48,\n                    height: 48,\n                    boxShadow: '0 4px 15px rgba(0,0,0,0.2)',\n                    '&:hover': {\n                      transform: 'scale(1.1)',\n                      boxShadow: '0 6px 20px rgba(0,0,0,0.3)',\n                    },\n                  },\n                  '& .MuiSpeedDialAction-staticTooltipLabel': {\n                    background: (theme) => theme.palette.mode === 'dark' \n                      ? 'rgba(30, 30, 30, 0.95)' \n                      : 'rgba(255, 255, 255, 0.95)',\n                    backdropFilter: 'blur(10px)',\n                    border: (theme) => `1px solid ${theme.palette.divider}`,\n                    borderRadius: 2,\n                    fontSize: '0.875rem',\n                    fontWeight: 600,\n                    px: 2,\n                    py: 1,\n                    boxShadow: '0 4px 12px rgba(0,0,0,0.15)',\n                  },\n                }}\n                FabProps={{\n                  style: {\n                    animationDelay: `${index * 50}ms`,\n                  },\n                }}\n              />\n            ))}\n          </SpeedDial>\n        </Box>\n      </Zoom>\n\n      {/* Quick Threat Alert */}\n      {alertCount > 5 && (\n        <Zoom in={!open && isVisible}>\n          <Box\n            sx={{\n              position: 'fixed',\n              bottom: 100,\n              right: 24,\n              zIndex: 1200,\n            }}\n          >\n            <Tooltip title=\"High Priority Threats Detected!\" placement=\"left\">\n              <Fab\n                size=\"medium\"\n                onClick={() => navigate('/smart-features')}\n                sx={{\n                  background: 'linear-gradient(135deg, #f44336 0%, #ff5722 100%)',\n                  color: 'white',\n                  animation: 'pulse 2s infinite',\n                  boxShadow: '0 4px 15px rgba(244, 67, 54, 0.4)',\n                  '&:hover': {\n                    background: 'linear-gradient(135deg, #d32f2f 0%, #f4511e 100%)',\n                    transform: 'scale(1.1)',\n                  },\n                  '@keyframes pulse': {\n                    '0%': { \n                      transform: 'scale(1)',\n                      boxShadow: '0 4px 15px rgba(244, 67, 54, 0.4)',\n                    },\n                    '50%': { \n                      transform: 'scale(1.05)',\n                      boxShadow: '0 8px 25px rgba(244, 67, 54, 0.6)',\n                    },\n                    '100%': { \n                      transform: 'scale(1)',\n                      boxShadow: '0 4px 15px rgba(244, 67, 54, 0.4)',\n                    },\n                  },\n                }}\n              >\n                <Badge badgeContent={alertCount} color=\"error\" max={99}>\n                  <WarningIcon />\n                </Badge>\n              </Fab>\n            </Tooltip>\n          </Box>\n        </Zoom>\n      )}\n\n      {/* AI Status Indicator */}\n      <Zoom in={!open && isVisible}>\n        <Box\n          sx={{\n            position: 'fixed',\n            bottom: 24,\n            left: 24,\n            zIndex: 1200,\n          }}\n        >\n          <Tooltip title=\"AI Protection Active\" placement=\"right\">\n            <Fab\n              size=\"small\"\n              sx={{\n                background: 'linear-gradient(135deg, #4caf50 0%, #8bc34a 100%)',\n                color: 'white',\n                width: 48,\n                height: 48,\n                boxShadow: '0 4px 15px rgba(76, 175, 80, 0.3)',\n                '&::before': {\n                  content: '\"\"',\n                  position: 'absolute',\n                  top: -2,\n                  left: -2,\n                  right: -2,\n                  bottom: -2,\n                  borderRadius: '50%',\n                  border: '2px solid #4caf50',\n                  opacity: 0.6,\n                  animation: 'ripple 2s infinite',\n                },\n                '@keyframes ripple': {\n                  '0%': { \n                    transform: 'scale(1)',\n                    opacity: 0.6,\n                  },\n                  '100%': { \n                    transform: 'scale(1.4)',\n                    opacity: 0,\n                  },\n                },\n              }}\n            >\n              <ShieldIcon fontSize=\"small\" />\n            </Fab>\n          </Tooltip>\n        </Box>\n      </Zoom>\n    </>\n  );\n});\n\nSmartFAB.displayName = 'SmartFAB';\n\nexport default SmartFAB;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,SAAS,EACTC,eAAe,EACfC,aAAa,EACbC,OAAO,EACPC,KAAK,EACLC,GAAG,EACHC,IAAI,EACJC,QAAQ,QACH,eAAe;AACtB,SACEC,UAAU,IAAIC,MAAM,EACpBC,SAAS,IAAIC,aAAa,EAC1BC,QAAQ,IAAIC,QAAQ,EACpBC,aAAa,IAAIC,iBAAiB,EAClCC,QAAQ,IAAIC,YAAY,EACxBC,QAAQ,IAAIC,YAAY,EACxBC,OAAO,IAAIC,WAAW,EACtBC,MAAM,IAAIC,UAAU,QACf,qBAAqB;AAC5B,SAASC,WAAW,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE/C,MAAMC,QAAQ,gBAAAC,EAAA,cAAGlC,KAAK,CAACmC,IAAI,CAAAC,EAAA,GAAAF,EAAA,CAAC,MAAM;EAAAA,EAAA;EAChC,MAAM,CAACG,IAAI,EAAEC,OAAO,CAAC,GAAGrC,QAAQ,CAAC,KAAK,CAAC;EACvC,MAAM,CAACsC,UAAU,EAAEC,aAAa,CAAC,GAAGvC,QAAQ,CAAC,CAAC,CAAC;EAC/C,MAAM,CAACwC,SAAS,EAAEC,YAAY,CAAC,GAAGzC,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM0C,QAAQ,GAAGf,WAAW,CAAC,CAAC;;EAE9B;EACA1B,SAAS,CAAC,MAAM;IACd,IAAI0C,WAAW,GAAGC,MAAM,CAACC,OAAO;IAEhC,MAAMC,YAAY,GAAGA,CAAA,KAAM;MACzB,MAAMC,cAAc,GAAGH,MAAM,CAACC,OAAO;MACrCJ,YAAY,CAACM,cAAc,GAAGJ,WAAW,IAAII,cAAc,GAAG,GAAG,CAAC;MAClEJ,WAAW,GAAGI,cAAc;IAC9B,CAAC;IAEDH,MAAM,CAACI,gBAAgB,CAAC,QAAQ,EAAEF,YAAY,EAAE;MAAEG,OAAO,EAAE;IAAK,CAAC,CAAC;IAClE,OAAO,MAAML,MAAM,CAACM,mBAAmB,CAAC,QAAQ,EAAEJ,YAAY,CAAC;EACjE,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA7C,SAAS,CAAC,MAAM;IACd,MAAMkD,QAAQ,GAAGC,WAAW,CAAC,MAAM;MACjC,IAAIC,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,EAAE;QAAE;QACzBf,aAAa,CAACgB,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;MACjC;IACF,CAAC,EAAE,IAAI,CAAC;IAER,OAAO,MAAMC,aAAa,CAACL,QAAQ,CAAC;EACtC,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMM,OAAO,GAAG,CACd;IACEC,IAAI,eAAE7B,OAAA,CAACf,aAAa;MAAA6C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACvBC,IAAI,EAAE,iBAAiB;IACvBC,KAAK,EAAE,mDAAmD;IAC1DC,MAAM,EAAEA,CAAA,KAAMvB,QAAQ,CAAC,iBAAiB;EAC1C,CAAC,EACD;IACEgB,IAAI,eAAE7B,OAAA,CAACT,YAAY;MAAAuC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBC,IAAI,EAAE,mBAAmB;IACzBC,KAAK,EAAE,mDAAmD;IAC1DC,MAAM,EAAEA,CAAA,KAAMvB,QAAQ,CAAC,iBAAiB;EAC1C,CAAC,EACD;IACEgB,IAAI,eAAE7B,OAAA,CAACb,QAAQ;MAAA2C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAClBC,IAAI,EAAE,WAAW;IACjBC,KAAK,EAAE,mDAAmD;IAC1DC,MAAM,EAAEA,CAAA,KAAMvB,QAAQ,CAAC,iBAAiB;EAC1C,CAAC,EACD;IACEgB,IAAI,eACF7B,OAAA,CAACtB,KAAK;MAAC2D,YAAY,EAAE5B,UAAW;MAAC0B,KAAK,EAAC,OAAO;MAACG,GAAG,EAAE,EAAG;MAAAC,QAAA,eACrDvC,OAAA,CAACX,iBAAiB;QAAAyC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChB,CACR;IACDC,IAAI,EAAE,cAAc;IACpBC,KAAK,EAAE,mDAAmD;IAC1DC,MAAM,EAAEA,CAAA,KAAMvB,QAAQ,CAAC,iBAAiB;EAC1C,CAAC,CACF;EAED,MAAM2B,UAAU,GAAGA,CAAA,KAAMhC,OAAO,CAAC,IAAI,CAAC;EACtC,MAAMiC,WAAW,GAAGA,CAAA,KAAMjC,OAAO,CAAC,KAAK,CAAC;EAExC,oBACER,OAAA,CAAAE,SAAA;IAAAqC,QAAA,gBACEvC,OAAA,CAACnB,QAAQ;MAAC0B,IAAI,EAAEA,IAAK;MAACmC,EAAE,EAAE;QAAEC,MAAM,EAAE;MAAK;IAAE;MAAAb,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAC9CjC,OAAA,CAACpB,IAAI;MAACgE,EAAE,EAAEjC,SAAU;MAAA4B,QAAA,eAClBvC,OAAA,CAACrB,GAAG;QACF+D,EAAE,EAAE;UACFG,QAAQ,EAAE,OAAO;UACjBC,MAAM,EAAE,EAAE;UACVC,KAAK,EAAE,EAAE;UACTJ,MAAM,EAAE;QACV,CAAE;QAAAJ,QAAA,eAEFvC,OAAA,CAAC1B,SAAS;UACR0E,SAAS,EAAC,mBAAmB;UAC7BnB,IAAI,eACF7B,OAAA,CAACtB,KAAK;YAAC2D,YAAY,EAAE5B,UAAW;YAAC0B,KAAK,EAAC,OAAO;YAACG,GAAG,EAAE,EAAG;YAAAC,QAAA,eACrDvC,OAAA,CAACxB,aAAa;cACZqD,IAAI,eAAE7B,OAAA,CAACjB,MAAM;gBAAA+C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACjBgB,QAAQ,eAAEjD,OAAA,CAACP,YAAY;gBAAAqC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CACR;UACDiB,OAAO,EAAET,WAAY;UACrBU,MAAM,EAAEX,UAAW;UACnBjC,IAAI,EAAEA,IAAK;UACX6C,SAAS,EAAC,IAAI;UACdV,EAAE,EAAE;YACF,qBAAqB,EAAE;cACrBW,UAAU,EAAE,mDAAmD;cAC/DlB,KAAK,EAAE,OAAO;cACdmB,KAAK,EAAE,EAAE;cACTC,MAAM,EAAE,EAAE;cACVC,SAAS,EAAE,sCAAsC;cACjDC,cAAc,EAAE,YAAY;cAC5BC,MAAM,EAAE,oCAAoC;cAC5CC,UAAU,EAAE,uCAAuC;cACnD,SAAS,EAAE;gBACTN,UAAU,EAAE,mDAAmD;gBAC/DO,SAAS,EAAE,0BAA0B;gBACrCJ,SAAS,EAAE;cACb,CAAC;cACD,WAAW,EAAE;gBACXK,OAAO,EAAE,IAAI;gBACbhB,QAAQ,EAAE,UAAU;gBACpBiB,GAAG,EAAE,CAAC,CAAC;gBACPC,IAAI,EAAE,CAAC,CAAC;gBACRhB,KAAK,EAAE,CAAC,CAAC;gBACTD,MAAM,EAAE,CAAC,CAAC;gBACVkB,YAAY,EAAE,KAAK;gBACnBX,UAAU,EAAE,yFAAyF;gBACrGY,cAAc,EAAE,WAAW;gBAC3BC,SAAS,EAAE,8BAA8B;gBACzCvB,MAAM,EAAE,CAAC,CAAC;gBACVwB,OAAO,EAAE;cACX,CAAC;cACD,UAAU,EAAE;gBACVN,OAAO,EAAE,IAAI;gBACbhB,QAAQ,EAAE,UAAU;gBACpBiB,GAAG,EAAE,CAAC,CAAC;gBACPC,IAAI,EAAE,CAAC,CAAC;gBACRhB,KAAK,EAAE,CAAC,CAAC;gBACTD,MAAM,EAAE,CAAC,CAAC;gBACVkB,YAAY,EAAE,KAAK;gBACnBN,MAAM,EAAE,oCAAoC;gBAC5CQ,SAAS,EAAE,2BAA2B;gBACtCvB,MAAM,EAAE,CAAC;cACX,CAAC;cACD,wBAAwB,EAAE;gBACxB,IAAI,EAAE;kBAAEyB,kBAAkB,EAAE;gBAAS,CAAC;gBACtC,KAAK,EAAE;kBAAEA,kBAAkB,EAAE;gBAAW,CAAC;gBACzC,MAAM,EAAE;kBAAEA,kBAAkB,EAAE;gBAAS;cACzC,CAAC;cACD,0BAA0B,EAAE;gBAC1B,IAAI,EAAE;kBAAER,SAAS,EAAE,UAAU;kBAAEO,OAAO,EAAE;gBAAI,CAAC;gBAC7C,KAAK,EAAE;kBAAEP,SAAS,EAAE,YAAY;kBAAEO,OAAO,EAAE;gBAAI,CAAC;gBAChD,MAAM,EAAE;kBAAEP,SAAS,EAAE,UAAU;kBAAEO,OAAO,EAAE;gBAAI;cAChD;YACF;UACF,CAAE;UAAA5B,QAAA,EAEDX,OAAO,CAACyC,GAAG,CAAC,CAACjC,MAAM,EAAEkC,KAAK,kBACzBtE,OAAA,CAACzB,eAAe;YAEdsD,IAAI,EAAEO,MAAM,CAACP,IAAK;YAClB0C,YAAY,EAAEnC,MAAM,CAACF,IAAK;YAC1BsC,WAAW;YACXC,OAAO,EAAEA,CAAA,KAAM;cACbrC,MAAM,CAACA,MAAM,CAAC,CAAC;cACfK,WAAW,CAAC,CAAC;YACf,CAAE;YACFC,EAAE,EAAE;cACF,2BAA2B,EAAE;gBAC3BW,UAAU,EAAEjB,MAAM,CAACD,KAAK;gBACxBA,KAAK,EAAE,OAAO;gBACdmB,KAAK,EAAE,EAAE;gBACTC,MAAM,EAAE,EAAE;gBACVC,SAAS,EAAE,4BAA4B;gBACvC,SAAS,EAAE;kBACTI,SAAS,EAAE,YAAY;kBACvBJ,SAAS,EAAE;gBACb;cACF,CAAC;cACD,0CAA0C,EAAE;gBAC1CH,UAAU,EAAGqB,KAAK,IAAKA,KAAK,CAACC,OAAO,CAACC,IAAI,KAAK,MAAM,GAChD,wBAAwB,GACxB,2BAA2B;gBAC/BnB,cAAc,EAAE,YAAY;gBAC5BC,MAAM,EAAGgB,KAAK,IAAK,aAAaA,KAAK,CAACC,OAAO,CAACE,OAAO,EAAE;gBACvDb,YAAY,EAAE,CAAC;gBACfc,QAAQ,EAAE,UAAU;gBACpBC,UAAU,EAAE,GAAG;gBACfC,EAAE,EAAE,CAAC;gBACLC,EAAE,EAAE,CAAC;gBACLzB,SAAS,EAAE;cACb;YACF,CAAE;YACF0B,QAAQ,EAAE;cACRC,KAAK,EAAE;gBACLC,cAAc,EAAE,GAAGd,KAAK,GAAG,EAAE;cAC/B;YACF;UAAE,GAtCGlC,MAAM,CAACF,IAAI;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAuCjB,CACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,EAGNxB,UAAU,GAAG,CAAC,iBACbT,OAAA,CAACpB,IAAI;MAACgE,EAAE,EAAE,CAACrC,IAAI,IAAII,SAAU;MAAA4B,QAAA,eAC3BvC,OAAA,CAACrB,GAAG;QACF+D,EAAE,EAAE;UACFG,QAAQ,EAAE,OAAO;UACjBC,MAAM,EAAE,GAAG;UACXC,KAAK,EAAE,EAAE;UACTJ,MAAM,EAAE;QACV,CAAE;QAAAJ,QAAA,eAEFvC,OAAA,CAACvB,OAAO;UAAC4G,KAAK,EAAC,iCAAiC;UAACC,SAAS,EAAC,MAAM;UAAA/C,QAAA,eAC/DvC,OAAA,CAAC3B,GAAG;YACFkH,IAAI,EAAC,QAAQ;YACbd,OAAO,EAAEA,CAAA,KAAM5D,QAAQ,CAAC,iBAAiB,CAAE;YAC3C6B,EAAE,EAAE;cACFW,UAAU,EAAE,mDAAmD;cAC/DlB,KAAK,EAAE,OAAO;cACd+B,SAAS,EAAE,mBAAmB;cAC9BV,SAAS,EAAE,mCAAmC;cAC9C,SAAS,EAAE;gBACTH,UAAU,EAAE,mDAAmD;gBAC/DO,SAAS,EAAE;cACb,CAAC;cACD,kBAAkB,EAAE;gBAClB,IAAI,EAAE;kBACJA,SAAS,EAAE,UAAU;kBACrBJ,SAAS,EAAE;gBACb,CAAC;gBACD,KAAK,EAAE;kBACLI,SAAS,EAAE,aAAa;kBACxBJ,SAAS,EAAE;gBACb,CAAC;gBACD,MAAM,EAAE;kBACNI,SAAS,EAAE,UAAU;kBACrBJ,SAAS,EAAE;gBACb;cACF;YACF,CAAE;YAAAjB,QAAA,eAEFvC,OAAA,CAACtB,KAAK;cAAC2D,YAAY,EAAE5B,UAAW;cAAC0B,KAAK,EAAC,OAAO;cAACG,GAAG,EAAE,EAAG;cAAAC,QAAA,eACrDvC,OAAA,CAACL,WAAW;gBAAAmC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CACP,eAGDjC,OAAA,CAACpB,IAAI;MAACgE,EAAE,EAAE,CAACrC,IAAI,IAAII,SAAU;MAAA4B,QAAA,eAC3BvC,OAAA,CAACrB,GAAG;QACF+D,EAAE,EAAE;UACFG,QAAQ,EAAE,OAAO;UACjBC,MAAM,EAAE,EAAE;UACViB,IAAI,EAAE,EAAE;UACRpB,MAAM,EAAE;QACV,CAAE;QAAAJ,QAAA,eAEFvC,OAAA,CAACvB,OAAO;UAAC4G,KAAK,EAAC,sBAAsB;UAACC,SAAS,EAAC,OAAO;UAAA/C,QAAA,eACrDvC,OAAA,CAAC3B,GAAG;YACFkH,IAAI,EAAC,OAAO;YACZ7C,EAAE,EAAE;cACFW,UAAU,EAAE,mDAAmD;cAC/DlB,KAAK,EAAE,OAAO;cACdmB,KAAK,EAAE,EAAE;cACTC,MAAM,EAAE,EAAE;cACVC,SAAS,EAAE,mCAAmC;cAC9C,WAAW,EAAE;gBACXK,OAAO,EAAE,IAAI;gBACbhB,QAAQ,EAAE,UAAU;gBACpBiB,GAAG,EAAE,CAAC,CAAC;gBACPC,IAAI,EAAE,CAAC,CAAC;gBACRhB,KAAK,EAAE,CAAC,CAAC;gBACTD,MAAM,EAAE,CAAC,CAAC;gBACVkB,YAAY,EAAE,KAAK;gBACnBN,MAAM,EAAE,mBAAmB;gBAC3BS,OAAO,EAAE,GAAG;gBACZD,SAAS,EAAE;cACb,CAAC;cACD,mBAAmB,EAAE;gBACnB,IAAI,EAAE;kBACJN,SAAS,EAAE,UAAU;kBACrBO,OAAO,EAAE;gBACX,CAAC;gBACD,MAAM,EAAE;kBACNP,SAAS,EAAE,YAAY;kBACvBO,OAAO,EAAE;gBACX;cACF;YACF,CAAE;YAAA5B,QAAA,eAEFvC,OAAA,CAACH,UAAU;cAACiF,QAAQ,EAAC;YAAO;cAAAhD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA,eACP,CAAC;AAEP,CAAC;EAAA,QA/RkBnC,WAAW;AAAA,EA+R7B,CAAC;EAAA,QA/RiBA,WAAW;AAAA,EA+R5B;AAAC0F,GAAA,GAnSGrF,QAAQ;AAqSdA,QAAQ,CAACsF,WAAW,GAAG,UAAU;AAEjC,eAAetF,QAAQ;AAAC,IAAAG,EAAA,EAAAkF,GAAA;AAAAE,YAAA,CAAApF,EAAA;AAAAoF,YAAA,CAAAF,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}