{"ast": null, "code": "var _jsxFileName = \"E:\\\\PythonProjects\\\\React.js\\\\Smart AI Assisstant\\\\src\\\\components\\\\AboutSettings.jsx\";\nimport React from 'react';\nimport { Box, Card, CardContent, Typography, Button, List, ListItem, ListItemIcon, ListItemText, Chip, Divider, Grid, Link } from '@mui/material';\nimport { Security as SecurityIcon, Check as CheckIcon, Info as InfoIcon, Update as UpdateIcon, Support as SupportIcon, Code as CodeIcon, GitHub as GitHubIcon, Email as EmailIcon, Description as DocumentationIcon } from '@mui/icons-material';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AboutSettings = () => {\n  const features = ['Real-time URL threat detection', 'Advanced file malware scanning', 'AI-powered threat analysis', 'Comprehensive security reports', 'Multi-language support', 'Dark/Light theme support', 'User authentication & profiles', 'Notification management'];\n  const techStack = [{\n    name: 'React 18',\n    description: 'Modern UI framework'\n  }, {\n    name: 'Material-UI v5',\n    description: 'Component library'\n  }, {\n    name: 'Node.js & Express',\n    description: 'Backend server'\n  }, {\n    name: 'Supabase',\n    description: 'Database & authentication'\n  }, {\n    name: 'JWT',\n    description: 'Secure authentication'\n  }, {\n    name: 'bcrypt',\n    description: 'Password hashing'\n  }];\n  const supportLinks = [{\n    title: 'Documentation',\n    description: 'Complete user guide and API documentation',\n    icon: /*#__PURE__*/_jsxDEV(DocumentationIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 54,\n      columnNumber: 13\n    }, this),\n    action: 'View Docs'\n  }, {\n    title: 'GitHub Repository',\n    description: 'Source code and issue tracking',\n    icon: /*#__PURE__*/_jsxDEV(GitHubIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 60,\n      columnNumber: 13\n    }, this),\n    action: 'View on GitHub'\n  }, {\n    title: 'Email Support',\n    description: 'Get help from our support team',\n    icon: /*#__PURE__*/_jsxDEV(EmailIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 66,\n      columnNumber: 13\n    }, this),\n    action: 'Contact Support'\n  }];\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h4\",\n      gutterBottom: true,\n      fontWeight: \"bold\",\n      children: \"About AI Security Guard\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 73,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"body1\",\n      color: \"text.secondary\",\n      sx: {\n        mb: 4\n      },\n      children: \"Advanced AI-powered security scanning platform\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 76,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        mb: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        sx: {\n          textAlign: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n            borderRadius: '50%',\n            width: 100,\n            height: 100,\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            mx: 'auto',\n            mb: 3,\n            boxShadow: '0 8px 25px rgba(102, 126, 234, 0.3)'\n          },\n          children: /*#__PURE__*/_jsxDEV(SecurityIcon, {\n            sx: {\n              color: 'white',\n              fontSize: 50\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 97,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 83,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h4\",\n          gutterBottom: true,\n          fontWeight: \"bold\",\n          children: \"AI Security Guard\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 100,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'center',\n            gap: 1,\n            mb: 3\n          },\n          children: [/*#__PURE__*/_jsxDEV(Chip, {\n            label: \"Version 1.0.0\",\n            color: \"primary\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 105,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Chip, {\n            label: \"Stable\",\n            color: \"success\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 106,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Chip, {\n            label: \"Open Source\",\n            color: \"info\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 107,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 104,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body1\",\n          sx: {\n            mb: 3,\n            lineHeight: 1.7,\n            maxWidth: 600,\n            mx: 'auto'\n          },\n          children: \"Advanced AI-powered security scanning platform providing comprehensive threat detection and analysis for URLs and files. Built with cutting-edge technology to protect your digital assets with real-time monitoring and intelligent threat assessment.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 110,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            gap: 2,\n            justifyContent: 'center',\n            flexWrap: 'wrap'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            startIcon: /*#__PURE__*/_jsxDEV(UpdateIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 120,\n              columnNumber: 26\n            }, this),\n            sx: {\n              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n              '&:hover': {\n                background: 'linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)'\n              }\n            },\n            children: \"Check for Updates\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 118,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outlined\",\n            startIcon: /*#__PURE__*/_jsxDEV(InfoIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 132,\n              columnNumber: 26\n            }, this),\n            children: \"Release Notes\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 130,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 117,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 82,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 81,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        mb: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          fontWeight: \"bold\",\n          children: \"Key Features\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 143,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 1,\n          children: features.map((feature, index) => /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                py: 0.5\n              },\n              children: [/*#__PURE__*/_jsxDEV(CheckIcon, {\n                sx: {\n                  color: 'success.main',\n                  mr: 1,\n                  fontSize: 20\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 151,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: feature\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 152,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 150,\n              columnNumber: 17\n            }, this)\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 149,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 142,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 141,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        mb: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            mb: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(CodeIcon, {\n            sx: {\n              mr: 2,\n              color: 'primary.main'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 166,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            fontWeight: \"bold\",\n            children: \"Technology Stack\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 167,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 165,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          children: techStack.map((tech, index) => /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            md: 4,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              variant: \"outlined\",\n              sx: {\n                height: '100%'\n              },\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                sx: {\n                  p: 2\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"subtitle1\",\n                  fontWeight: \"600\",\n                  gutterBottom: true,\n                  children: tech.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 177,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: tech.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 180,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 176,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 175,\n              columnNumber: 17\n            }, this)\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 174,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 172,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 164,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 163,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        mb: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            mb: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(SupportIcon, {\n            sx: {\n              mr: 2,\n              color: 'primary.main'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            fontWeight: \"bold\",\n            children: \"Support & Resources\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 196,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 194,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(List, {\n          children: supportLinks.map((link, index) => /*#__PURE__*/_jsxDEV(React.Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(ListItem, {\n              sx: {\n                px: 0\n              },\n              children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                children: /*#__PURE__*/React.cloneElement(link.icon, {\n                  color: 'primary'\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 205,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                primary: link.title,\n                secondary: link.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 208,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"outlined\",\n                size: \"small\",\n                sx: {\n                  borderRadius: 3\n                },\n                children: link.action\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 212,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 204,\n              columnNumber: 17\n            }, this), index < supportLinks.length - 1 && /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 220,\n              columnNumber: 53\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 203,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 201,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 193,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 192,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        mb: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          fontWeight: \"bold\",\n          children: \"System Information\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 230,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: \"Version\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 236,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              fontWeight: \"500\",\n              children: \"1.0.0\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 239,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 235,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: \"Build Date\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 245,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              fontWeight: \"500\",\n              children: \"January 2025\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 248,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 244,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: \"Environment\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 254,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              fontWeight: \"500\",\n              children: process.env.NODE_ENV || 'Development'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 257,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 253,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: \"Browser\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 263,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              fontWeight: \"500\",\n              children: navigator.userAgent.split(' ')[0] || 'Unknown'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 266,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 262,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 234,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 229,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 228,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          fontWeight: \"bold\",\n          children: \"Legal & Credits\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 277,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          color: \"text.secondary\",\n          sx: {\n            mb: 2\n          },\n          children: \"\\xA9 2025 AI Security Guard. All rights reserved.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 281,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          color: \"text.secondary\",\n          sx: {\n            mb: 2\n          },\n          children: \"Built with \\u2764\\uFE0F for digital security and privacy protection.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 285,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            gap: 2,\n            flexWrap: 'wrap'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Link, {\n            href: \"#\",\n            variant: \"body2\",\n            underline: \"hover\",\n            children: \"Privacy Policy\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 290,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            href: \"#\",\n            variant: \"body2\",\n            underline: \"hover\",\n            children: \"Terms of Service\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 293,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            href: \"#\",\n            variant: \"body2\",\n            underline: \"hover\",\n            children: \"License\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 296,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            href: \"#\",\n            variant: \"body2\",\n            underline: \"hover\",\n            children: \"Acknowledgments\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 299,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 289,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 276,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 275,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 72,\n    columnNumber: 5\n  }, this);\n};\n_c = AboutSettings;\nexport default AboutSettings;\nvar _c;\n$RefreshReg$(_c, \"AboutSettings\");", "map": {"version": 3, "names": ["React", "Box", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Typography", "<PERSON><PERSON>", "List", "ListItem", "ListItemIcon", "ListItemText", "Chip", "Divider", "Grid", "Link", "Security", "SecurityIcon", "Check", "CheckIcon", "Info", "InfoIcon", "Update", "UpdateIcon", "Support", "SupportIcon", "Code", "CodeIcon", "GitHub", "GitHubIcon", "Email", "EmailIcon", "Description", "DocumentationIcon", "jsxDEV", "_jsxDEV", "AboutSettings", "features", "techStack", "name", "description", "supportLinks", "title", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "action", "children", "variant", "gutterBottom", "fontWeight", "color", "sx", "mb", "textAlign", "background", "borderRadius", "width", "height", "display", "alignItems", "justifyContent", "mx", "boxShadow", "fontSize", "gap", "label", "lineHeight", "max<PERSON><PERSON><PERSON>", "flexWrap", "startIcon", "container", "spacing", "map", "feature", "index", "item", "xs", "sm", "py", "mr", "tech", "md", "p", "link", "Fragment", "px", "cloneElement", "primary", "secondary", "size", "length", "process", "env", "NODE_ENV", "navigator", "userAgent", "split", "href", "underline", "_c", "$RefreshReg$"], "sources": ["E:/PythonProjects/React.js/Smart AI Assisstant/src/components/AboutSettings.jsx"], "sourcesContent": ["import React from 'react';\nimport {\n  <PERSON>,\n  <PERSON>,\n  Card<PERSON>ontent,\n  <PERSON><PERSON><PERSON>,\n  Button,\n  List,\n  ListItem,\n  ListItemIcon,\n  ListItemText,\n  Chip,\n  Divider,\n  Grid,\n  Link,\n} from '@mui/material';\nimport {\n  Security as SecurityIcon,\n  Check as CheckIcon,\n  Info as InfoIcon,\n  Update as UpdateIcon,\n  Support as SupportIcon,\n  Code as CodeIcon,\n  GitHub as GitHubIcon,\n  Email as EmailIcon,\n  Description as DocumentationIcon,\n} from '@mui/icons-material';\n\nconst AboutSettings = () => {\n  const features = [\n    'Real-time URL threat detection',\n    'Advanced file malware scanning',\n    'AI-powered threat analysis',\n    'Comprehensive security reports',\n    'Multi-language support',\n    'Dark/Light theme support',\n    'User authentication & profiles',\n    'Notification management',\n  ];\n\n  const techStack = [\n    { name: 'React 18', description: 'Modern UI framework' },\n    { name: 'Material-UI v5', description: 'Component library' },\n    { name: 'Node.js & Express', description: 'Backend server' },\n    { name: 'Supabase', description: 'Database & authentication' },\n    { name: 'JWT', description: 'Secure authentication' },\n    { name: 'bcrypt', description: 'Password hashing' },\n  ];\n\n  const supportLinks = [\n    {\n      title: 'Documentation',\n      description: 'Complete user guide and API documentation',\n      icon: <DocumentationIcon />,\n      action: 'View Docs',\n    },\n    {\n      title: 'GitHub Repository',\n      description: 'Source code and issue tracking',\n      icon: <GitHubIcon />,\n      action: 'View on GitHub',\n    },\n    {\n      title: 'Email Support',\n      description: 'Get help from our support team',\n      icon: <EmailIcon />,\n      action: 'Contact Support',\n    },\n  ];\n\n  return (\n    <Box>\n      <Typography variant=\"h4\" gutterBottom fontWeight=\"bold\">\n        About AI Security Guard\n      </Typography>\n      <Typography variant=\"body1\" color=\"text.secondary\" sx={{ mb: 4 }}>\n        Advanced AI-powered security scanning platform\n      </Typography>\n\n      {/* App Info */}\n      <Card sx={{ mb: 3 }}>\n        <CardContent sx={{ textAlign: 'center' }}>\n          <Box\n            sx={{\n              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n              borderRadius: '50%',\n              width: 100,\n              height: 100,\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              mx: 'auto',\n              mb: 3,\n              boxShadow: '0 8px 25px rgba(102, 126, 234, 0.3)',\n            }}\n          >\n            <SecurityIcon sx={{ color: 'white', fontSize: 50 }} />\n          </Box>\n\n          <Typography variant=\"h4\" gutterBottom fontWeight=\"bold\">\n            AI Security Guard\n          </Typography>\n\n          <Box sx={{ display: 'flex', justifyContent: 'center', gap: 1, mb: 3 }}>\n            <Chip label=\"Version 1.0.0\" color=\"primary\" />\n            <Chip label=\"Stable\" color=\"success\" />\n            <Chip label=\"Open Source\" color=\"info\" />\n          </Box>\n\n          <Typography variant=\"body1\" sx={{ mb: 3, lineHeight: 1.7, maxWidth: 600, mx: 'auto' }}>\n            Advanced AI-powered security scanning platform providing comprehensive\n            threat detection and analysis for URLs and files. Built with cutting-edge\n            technology to protect your digital assets with real-time monitoring and\n            intelligent threat assessment.\n          </Typography>\n\n          <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center', flexWrap: 'wrap' }}>\n            <Button\n              variant=\"contained\"\n              startIcon={<UpdateIcon />}\n              sx={{\n                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                '&:hover': {\n                  background: 'linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)',\n                },\n              }}\n            >\n              Check for Updates\n            </Button>\n            <Button\n              variant=\"outlined\"\n              startIcon={<InfoIcon />}\n            >\n              Release Notes\n            </Button>\n          </Box>\n        </CardContent>\n      </Card>\n\n      {/* Key Features */}\n      <Card sx={{ mb: 3 }}>\n        <CardContent>\n          <Typography variant=\"h6\" gutterBottom fontWeight=\"bold\">\n            Key Features\n          </Typography>\n          \n          <Grid container spacing={1}>\n            {features.map((feature, index) => (\n              <Grid item xs={12} sm={6} key={index}>\n                <Box sx={{ display: 'flex', alignItems: 'center', py: 0.5 }}>\n                  <CheckIcon sx={{ color: 'success.main', mr: 1, fontSize: 20 }} />\n                  <Typography variant=\"body2\">\n                    {feature}\n                  </Typography>\n                </Box>\n              </Grid>\n            ))}\n          </Grid>\n        </CardContent>\n      </Card>\n\n      {/* Technology Stack */}\n      <Card sx={{ mb: 3 }}>\n        <CardContent>\n          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>\n            <CodeIcon sx={{ mr: 2, color: 'primary.main' }} />\n            <Typography variant=\"h6\" fontWeight=\"bold\">\n              Technology Stack\n            </Typography>\n          </Box>\n          \n          <Grid container spacing={2}>\n            {techStack.map((tech, index) => (\n              <Grid item xs={12} sm={6} md={4} key={index}>\n                <Card variant=\"outlined\" sx={{ height: '100%' }}>\n                  <CardContent sx={{ p: 2 }}>\n                    <Typography variant=\"subtitle1\" fontWeight=\"600\" gutterBottom>\n                      {tech.name}\n                    </Typography>\n                    <Typography variant=\"body2\" color=\"text.secondary\">\n                      {tech.description}\n                    </Typography>\n                  </CardContent>\n                </Card>\n              </Grid>\n            ))}\n          </Grid>\n        </CardContent>\n      </Card>\n\n      {/* Support & Resources */}\n      <Card sx={{ mb: 3 }}>\n        <CardContent>\n          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>\n            <SupportIcon sx={{ mr: 2, color: 'primary.main' }} />\n            <Typography variant=\"h6\" fontWeight=\"bold\">\n              Support & Resources\n            </Typography>\n          </Box>\n          \n          <List>\n            {supportLinks.map((link, index) => (\n              <React.Fragment key={index}>\n                <ListItem sx={{ px: 0 }}>\n                  <ListItemIcon>\n                    {React.cloneElement(link.icon, { color: 'primary' })}\n                  </ListItemIcon>\n                  <ListItemText\n                    primary={link.title}\n                    secondary={link.description}\n                  />\n                  <Button\n                    variant=\"outlined\"\n                    size=\"small\"\n                    sx={{ borderRadius: 3 }}\n                  >\n                    {link.action}\n                  </Button>\n                </ListItem>\n                {index < supportLinks.length - 1 && <Divider />}\n              </React.Fragment>\n            ))}\n          </List>\n        </CardContent>\n      </Card>\n\n      {/* System Information */}\n      <Card sx={{ mb: 3 }}>\n        <CardContent>\n          <Typography variant=\"h6\" gutterBottom fontWeight=\"bold\">\n            System Information\n          </Typography>\n          \n          <Grid container spacing={2}>\n            <Grid item xs={12} sm={6}>\n              <Typography variant=\"body2\" color=\"text.secondary\">\n                Version\n              </Typography>\n              <Typography variant=\"body1\" fontWeight=\"500\">\n                1.0.0\n              </Typography>\n            </Grid>\n            \n            <Grid item xs={12} sm={6}>\n              <Typography variant=\"body2\" color=\"text.secondary\">\n                Build Date\n              </Typography>\n              <Typography variant=\"body1\" fontWeight=\"500\">\n                January 2025\n              </Typography>\n            </Grid>\n            \n            <Grid item xs={12} sm={6}>\n              <Typography variant=\"body2\" color=\"text.secondary\">\n                Environment\n              </Typography>\n              <Typography variant=\"body1\" fontWeight=\"500\">\n                {process.env.NODE_ENV || 'Development'}\n              </Typography>\n            </Grid>\n            \n            <Grid item xs={12} sm={6}>\n              <Typography variant=\"body2\" color=\"text.secondary\">\n                Browser\n              </Typography>\n              <Typography variant=\"body1\" fontWeight=\"500\">\n                {navigator.userAgent.split(' ')[0] || 'Unknown'}\n              </Typography>\n            </Grid>\n          </Grid>\n        </CardContent>\n      </Card>\n\n      {/* Legal & Credits */}\n      <Card>\n        <CardContent>\n          <Typography variant=\"h6\" gutterBottom fontWeight=\"bold\">\n            Legal & Credits\n          </Typography>\n          \n          <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 2 }}>\n            © 2025 AI Security Guard. All rights reserved.\n          </Typography>\n          \n          <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 2 }}>\n            Built with ❤️ for digital security and privacy protection.\n          </Typography>\n          \n          <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>\n            <Link href=\"#\" variant=\"body2\" underline=\"hover\">\n              Privacy Policy\n            </Link>\n            <Link href=\"#\" variant=\"body2\" underline=\"hover\">\n              Terms of Service\n            </Link>\n            <Link href=\"#\" variant=\"body2\" underline=\"hover\">\n              License\n            </Link>\n            <Link href=\"#\" variant=\"body2\" underline=\"hover\">\n              Acknowledgments\n            </Link>\n          </Box>\n        </CardContent>\n      </Card>\n    </Box>\n  );\n};\n\nexport default AboutSettings;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SACEC,GAAG,EACHC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,YAAY,EACZC,IAAI,EACJC,OAAO,EACPC,IAAI,EACJC,IAAI,QACC,eAAe;AACtB,SACEC,QAAQ,IAAIC,YAAY,EACxBC,KAAK,IAAIC,SAAS,EAClBC,IAAI,IAAIC,QAAQ,EAChBC,MAAM,IAAIC,UAAU,EACpBC,OAAO,IAAIC,WAAW,EACtBC,IAAI,IAAIC,QAAQ,EAChBC,MAAM,IAAIC,UAAU,EACpBC,KAAK,IAAIC,SAAS,EAClBC,WAAW,IAAIC,iBAAiB,QAC3B,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7B,MAAMC,aAAa,GAAGA,CAAA,KAAM;EAC1B,MAAMC,QAAQ,GAAG,CACf,gCAAgC,EAChC,gCAAgC,EAChC,4BAA4B,EAC5B,gCAAgC,EAChC,wBAAwB,EACxB,0BAA0B,EAC1B,gCAAgC,EAChC,yBAAyB,CAC1B;EAED,MAAMC,SAAS,GAAG,CAChB;IAAEC,IAAI,EAAE,UAAU;IAAEC,WAAW,EAAE;EAAsB,CAAC,EACxD;IAAED,IAAI,EAAE,gBAAgB;IAAEC,WAAW,EAAE;EAAoB,CAAC,EAC5D;IAAED,IAAI,EAAE,mBAAmB;IAAEC,WAAW,EAAE;EAAiB,CAAC,EAC5D;IAAED,IAAI,EAAE,UAAU;IAAEC,WAAW,EAAE;EAA4B,CAAC,EAC9D;IAAED,IAAI,EAAE,KAAK;IAAEC,WAAW,EAAE;EAAwB,CAAC,EACrD;IAAED,IAAI,EAAE,QAAQ;IAAEC,WAAW,EAAE;EAAmB,CAAC,CACpD;EAED,MAAMC,YAAY,GAAG,CACnB;IACEC,KAAK,EAAE,eAAe;IACtBF,WAAW,EAAE,2CAA2C;IACxDG,IAAI,eAAER,OAAA,CAACF,iBAAiB;MAAAW,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC3BC,MAAM,EAAE;EACV,CAAC,EACD;IACEN,KAAK,EAAE,mBAAmB;IAC1BF,WAAW,EAAE,gCAAgC;IAC7CG,IAAI,eAAER,OAAA,CAACN,UAAU;MAAAe,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACpBC,MAAM,EAAE;EACV,CAAC,EACD;IACEN,KAAK,EAAE,eAAe;IACtBF,WAAW,EAAE,gCAAgC;IAC7CG,IAAI,eAAER,OAAA,CAACJ,SAAS;MAAAa,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACnBC,MAAM,EAAE;EACV,CAAC,CACF;EAED,oBACEb,OAAA,CAAChC,GAAG;IAAA8C,QAAA,gBACFd,OAAA,CAAC7B,UAAU;MAAC4C,OAAO,EAAC,IAAI;MAACC,YAAY;MAACC,UAAU,EAAC,MAAM;MAAAH,QAAA,EAAC;IAExD;MAAAL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eACbZ,OAAA,CAAC7B,UAAU;MAAC4C,OAAO,EAAC,OAAO;MAACG,KAAK,EAAC,gBAAgB;MAACC,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAN,QAAA,EAAC;IAElE;MAAAL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eAGbZ,OAAA,CAAC/B,IAAI;MAACkD,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAN,QAAA,eAClBd,OAAA,CAAC9B,WAAW;QAACiD,EAAE,EAAE;UAAEE,SAAS,EAAE;QAAS,CAAE;QAAAP,QAAA,gBACvCd,OAAA,CAAChC,GAAG;UACFmD,EAAE,EAAE;YACFG,UAAU,EAAE,mDAAmD;YAC/DC,YAAY,EAAE,KAAK;YACnBC,KAAK,EAAE,GAAG;YACVC,MAAM,EAAE,GAAG;YACXC,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpBC,cAAc,EAAE,QAAQ;YACxBC,EAAE,EAAE,MAAM;YACVT,EAAE,EAAE,CAAC;YACLU,SAAS,EAAE;UACb,CAAE;UAAAhB,QAAA,eAEFd,OAAA,CAAClB,YAAY;YAACqC,EAAE,EAAE;cAAED,KAAK,EAAE,OAAO;cAAEa,QAAQ,EAAE;YAAG;UAAE;YAAAtB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnD,CAAC,eAENZ,OAAA,CAAC7B,UAAU;UAAC4C,OAAO,EAAC,IAAI;UAACC,YAAY;UAACC,UAAU,EAAC,MAAM;UAAAH,QAAA,EAAC;QAExD;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAEbZ,OAAA,CAAChC,GAAG;UAACmD,EAAE,EAAE;YAAEO,OAAO,EAAE,MAAM;YAAEE,cAAc,EAAE,QAAQ;YAAEI,GAAG,EAAE,CAAC;YAAEZ,EAAE,EAAE;UAAE,CAAE;UAAAN,QAAA,gBACpEd,OAAA,CAACvB,IAAI;YAACwD,KAAK,EAAC,eAAe;YAACf,KAAK,EAAC;UAAS;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC9CZ,OAAA,CAACvB,IAAI;YAACwD,KAAK,EAAC,QAAQ;YAACf,KAAK,EAAC;UAAS;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACvCZ,OAAA,CAACvB,IAAI;YAACwD,KAAK,EAAC,aAAa;YAACf,KAAK,EAAC;UAAM;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CAAC,eAENZ,OAAA,CAAC7B,UAAU;UAAC4C,OAAO,EAAC,OAAO;UAACI,EAAE,EAAE;YAAEC,EAAE,EAAE,CAAC;YAAEc,UAAU,EAAE,GAAG;YAAEC,QAAQ,EAAE,GAAG;YAAEN,EAAE,EAAE;UAAO,CAAE;UAAAf,QAAA,EAAC;QAKvF;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAEbZ,OAAA,CAAChC,GAAG;UAACmD,EAAE,EAAE;YAAEO,OAAO,EAAE,MAAM;YAAEM,GAAG,EAAE,CAAC;YAAEJ,cAAc,EAAE,QAAQ;YAAEQ,QAAQ,EAAE;UAAO,CAAE;UAAAtB,QAAA,gBAC/Ed,OAAA,CAAC5B,MAAM;YACL2C,OAAO,EAAC,WAAW;YACnBsB,SAAS,eAAErC,OAAA,CAACZ,UAAU;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC1BO,EAAE,EAAE;cACFG,UAAU,EAAE,mDAAmD;cAC/D,SAAS,EAAE;gBACTA,UAAU,EAAE;cACd;YACF,CAAE;YAAAR,QAAA,EACH;UAED;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTZ,OAAA,CAAC5B,MAAM;YACL2C,OAAO,EAAC,UAAU;YAClBsB,SAAS,eAAErC,OAAA,CAACd,QAAQ;cAAAuB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAE,QAAA,EACzB;UAED;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGPZ,OAAA,CAAC/B,IAAI;MAACkD,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAN,QAAA,eAClBd,OAAA,CAAC9B,WAAW;QAAA4C,QAAA,gBACVd,OAAA,CAAC7B,UAAU;UAAC4C,OAAO,EAAC,IAAI;UAACC,YAAY;UAACC,UAAU,EAAC,MAAM;UAAAH,QAAA,EAAC;QAExD;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAEbZ,OAAA,CAACrB,IAAI;UAAC2D,SAAS;UAACC,OAAO,EAAE,CAAE;UAAAzB,QAAA,EACxBZ,QAAQ,CAACsC,GAAG,CAAC,CAACC,OAAO,EAAEC,KAAK,kBAC3B1C,OAAA,CAACrB,IAAI;YAACgE,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA/B,QAAA,eACvBd,OAAA,CAAChC,GAAG;cAACmD,EAAE,EAAE;gBAAEO,OAAO,EAAE,MAAM;gBAAEC,UAAU,EAAE,QAAQ;gBAAEmB,EAAE,EAAE;cAAI,CAAE;cAAAhC,QAAA,gBAC1Dd,OAAA,CAAChB,SAAS;gBAACmC,EAAE,EAAE;kBAAED,KAAK,EAAE,cAAc;kBAAE6B,EAAE,EAAE,CAAC;kBAAEhB,QAAQ,EAAE;gBAAG;cAAE;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACjEZ,OAAA,CAAC7B,UAAU;gBAAC4C,OAAO,EAAC,OAAO;gBAAAD,QAAA,EACxB2B;cAAO;gBAAAhC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC,GANuB8B,KAAK;YAAAjC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAO9B,CACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGPZ,OAAA,CAAC/B,IAAI;MAACkD,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAN,QAAA,eAClBd,OAAA,CAAC9B,WAAW;QAAA4C,QAAA,gBACVd,OAAA,CAAChC,GAAG;UAACmD,EAAE,EAAE;YAAEO,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAEP,EAAE,EAAE;UAAE,CAAE;UAAAN,QAAA,gBACxDd,OAAA,CAACR,QAAQ;YAAC2B,EAAE,EAAE;cAAE4B,EAAE,EAAE,CAAC;cAAE7B,KAAK,EAAE;YAAe;UAAE;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAClDZ,OAAA,CAAC7B,UAAU;YAAC4C,OAAO,EAAC,IAAI;YAACE,UAAU,EAAC,MAAM;YAAAH,QAAA,EAAC;UAE3C;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAENZ,OAAA,CAACrB,IAAI;UAAC2D,SAAS;UAACC,OAAO,EAAE,CAAE;UAAAzB,QAAA,EACxBX,SAAS,CAACqC,GAAG,CAAC,CAACQ,IAAI,EAAEN,KAAK,kBACzB1C,OAAA,CAACrB,IAAI;YAACgE,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAACI,EAAE,EAAE,CAAE;YAAAnC,QAAA,eAC9Bd,OAAA,CAAC/B,IAAI;cAAC8C,OAAO,EAAC,UAAU;cAACI,EAAE,EAAE;gBAAEM,MAAM,EAAE;cAAO,CAAE;cAAAX,QAAA,eAC9Cd,OAAA,CAAC9B,WAAW;gBAACiD,EAAE,EAAE;kBAAE+B,CAAC,EAAE;gBAAE,CAAE;gBAAApC,QAAA,gBACxBd,OAAA,CAAC7B,UAAU;kBAAC4C,OAAO,EAAC,WAAW;kBAACE,UAAU,EAAC,KAAK;kBAACD,YAAY;kBAAAF,QAAA,EAC1DkC,IAAI,CAAC5C;gBAAI;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC,eACbZ,OAAA,CAAC7B,UAAU;kBAAC4C,OAAO,EAAC,OAAO;kBAACG,KAAK,EAAC,gBAAgB;kBAAAJ,QAAA,EAC/CkC,IAAI,CAAC3C;gBAAW;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC,GAV6B8B,KAAK;YAAAjC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAWrC,CACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGPZ,OAAA,CAAC/B,IAAI;MAACkD,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAN,QAAA,eAClBd,OAAA,CAAC9B,WAAW;QAAA4C,QAAA,gBACVd,OAAA,CAAChC,GAAG;UAACmD,EAAE,EAAE;YAAEO,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAEP,EAAE,EAAE;UAAE,CAAE;UAAAN,QAAA,gBACxDd,OAAA,CAACV,WAAW;YAAC6B,EAAE,EAAE;cAAE4B,EAAE,EAAE,CAAC;cAAE7B,KAAK,EAAE;YAAe;UAAE;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACrDZ,OAAA,CAAC7B,UAAU;YAAC4C,OAAO,EAAC,IAAI;YAACE,UAAU,EAAC,MAAM;YAAAH,QAAA,EAAC;UAE3C;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAENZ,OAAA,CAAC3B,IAAI;UAAAyC,QAAA,EACFR,YAAY,CAACkC,GAAG,CAAC,CAACW,IAAI,EAAET,KAAK,kBAC5B1C,OAAA,CAACjC,KAAK,CAACqF,QAAQ;YAAAtC,QAAA,gBACbd,OAAA,CAAC1B,QAAQ;cAAC6C,EAAE,EAAE;gBAAEkC,EAAE,EAAE;cAAE,CAAE;cAAAvC,QAAA,gBACtBd,OAAA,CAACzB,YAAY;gBAAAuC,QAAA,eACV/C,KAAK,CAACuF,YAAY,CAACH,IAAI,CAAC3C,IAAI,EAAE;kBAAEU,KAAK,EAAE;gBAAU,CAAC;cAAC;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC,CAAC,eACfZ,OAAA,CAACxB,YAAY;gBACX+E,OAAO,EAAEJ,IAAI,CAAC5C,KAAM;gBACpBiD,SAAS,EAAEL,IAAI,CAAC9C;cAAY;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B,CAAC,eACFZ,OAAA,CAAC5B,MAAM;gBACL2C,OAAO,EAAC,UAAU;gBAClB0C,IAAI,EAAC,OAAO;gBACZtC,EAAE,EAAE;kBAAEI,YAAY,EAAE;gBAAE,CAAE;gBAAAT,QAAA,EAEvBqC,IAAI,CAACtC;cAAM;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,EACV8B,KAAK,GAAGpC,YAAY,CAACoD,MAAM,GAAG,CAAC,iBAAI1D,OAAA,CAACtB,OAAO;cAAA+B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA,GAjB5B8B,KAAK;YAAAjC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAkBV,CACjB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGPZ,OAAA,CAAC/B,IAAI;MAACkD,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAN,QAAA,eAClBd,OAAA,CAAC9B,WAAW;QAAA4C,QAAA,gBACVd,OAAA,CAAC7B,UAAU;UAAC4C,OAAO,EAAC,IAAI;UAACC,YAAY;UAACC,UAAU,EAAC,MAAM;UAAAH,QAAA,EAAC;QAExD;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAEbZ,OAAA,CAACrB,IAAI;UAAC2D,SAAS;UAACC,OAAO,EAAE,CAAE;UAAAzB,QAAA,gBACzBd,OAAA,CAACrB,IAAI;YAACgE,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA/B,QAAA,gBACvBd,OAAA,CAAC7B,UAAU;cAAC4C,OAAO,EAAC,OAAO;cAACG,KAAK,EAAC,gBAAgB;cAAAJ,QAAA,EAAC;YAEnD;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbZ,OAAA,CAAC7B,UAAU;cAAC4C,OAAO,EAAC,OAAO;cAACE,UAAU,EAAC,KAAK;cAAAH,QAAA,EAAC;YAE7C;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eAEPZ,OAAA,CAACrB,IAAI;YAACgE,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA/B,QAAA,gBACvBd,OAAA,CAAC7B,UAAU;cAAC4C,OAAO,EAAC,OAAO;cAACG,KAAK,EAAC,gBAAgB;cAAAJ,QAAA,EAAC;YAEnD;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbZ,OAAA,CAAC7B,UAAU;cAAC4C,OAAO,EAAC,OAAO;cAACE,UAAU,EAAC,KAAK;cAAAH,QAAA,EAAC;YAE7C;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eAEPZ,OAAA,CAACrB,IAAI;YAACgE,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA/B,QAAA,gBACvBd,OAAA,CAAC7B,UAAU;cAAC4C,OAAO,EAAC,OAAO;cAACG,KAAK,EAAC,gBAAgB;cAAAJ,QAAA,EAAC;YAEnD;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbZ,OAAA,CAAC7B,UAAU;cAAC4C,OAAO,EAAC,OAAO;cAACE,UAAU,EAAC,KAAK;cAAAH,QAAA,EACzC6C,OAAO,CAACC,GAAG,CAACC,QAAQ,IAAI;YAAa;cAAApD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eAEPZ,OAAA,CAACrB,IAAI;YAACgE,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA/B,QAAA,gBACvBd,OAAA,CAAC7B,UAAU;cAAC4C,OAAO,EAAC,OAAO;cAACG,KAAK,EAAC,gBAAgB;cAAAJ,QAAA,EAAC;YAEnD;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbZ,OAAA,CAAC7B,UAAU;cAAC4C,OAAO,EAAC,OAAO;cAACE,UAAU,EAAC,KAAK;cAAAH,QAAA,EACzCgD,SAAS,CAACC,SAAS,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI;YAAS;cAAAvD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGPZ,OAAA,CAAC/B,IAAI;MAAA6C,QAAA,eACHd,OAAA,CAAC9B,WAAW;QAAA4C,QAAA,gBACVd,OAAA,CAAC7B,UAAU;UAAC4C,OAAO,EAAC,IAAI;UAACC,YAAY;UAACC,UAAU,EAAC,MAAM;UAAAH,QAAA,EAAC;QAExD;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAEbZ,OAAA,CAAC7B,UAAU;UAAC4C,OAAO,EAAC,OAAO;UAACG,KAAK,EAAC,gBAAgB;UAACC,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAAN,QAAA,EAAC;QAElE;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAEbZ,OAAA,CAAC7B,UAAU;UAAC4C,OAAO,EAAC,OAAO;UAACG,KAAK,EAAC,gBAAgB;UAACC,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAAN,QAAA,EAAC;QAElE;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAEbZ,OAAA,CAAChC,GAAG;UAACmD,EAAE,EAAE;YAAEO,OAAO,EAAE,MAAM;YAAEM,GAAG,EAAE,CAAC;YAAEI,QAAQ,EAAE;UAAO,CAAE;UAAAtB,QAAA,gBACrDd,OAAA,CAACpB,IAAI;YAACqF,IAAI,EAAC,GAAG;YAAClD,OAAO,EAAC,OAAO;YAACmD,SAAS,EAAC,OAAO;YAAApD,QAAA,EAAC;UAEjD;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACPZ,OAAA,CAACpB,IAAI;YAACqF,IAAI,EAAC,GAAG;YAAClD,OAAO,EAAC,OAAO;YAACmD,SAAS,EAAC,OAAO;YAAApD,QAAA,EAAC;UAEjD;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACPZ,OAAA,CAACpB,IAAI;YAACqF,IAAI,EAAC,GAAG;YAAClD,OAAO,EAAC,OAAO;YAACmD,SAAS,EAAC,OAAO;YAAApD,QAAA,EAAC;UAEjD;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACPZ,OAAA,CAACpB,IAAI;YAACqF,IAAI,EAAC,GAAG;YAAClD,OAAO,EAAC,OAAO;YAACmD,SAAS,EAAC,OAAO;YAAApD,QAAA,EAAC;UAEjD;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAACuD,EAAA,GAtRIlE,aAAa;AAwRnB,eAAeA,aAAa;AAAC,IAAAkE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}