{"ast": null, "code": "var _jsxFileName = \"E:\\\\PythonProjects\\\\React.js\\\\Smart AI Assisstant\\\\src\\\\pages\\\\HomePage.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Container, Grid, Box, Typography, Card, CardContent, Tabs, Tab, Alert, Snackbar, Fade, Button, Chip } from '@mui/material';\nimport { Security as SecurityIcon, Link as LinkIcon, InsertDriveFile as FileIcon, Assessment as AssessmentIcon } from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport { useScan } from '../contexts/ScanContext';\nimport LinkScanner from '../components/LinkScanner';\nimport FileUploader from '../components/FileUploader';\nimport ResultView from '../components/ResultView';\nimport SmartDashboard from '../components/SmartDashboard';\nimport ThreatPrediction from '../components/ThreatPrediction';\nimport SmartNotifications from '../components/SmartNotifications';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst HomePage = /*#__PURE__*/_s(/*#__PURE__*/React.memo(_c = _s(() => {\n  _s();\n  const [activeTab, setActiveTab] = useState(0);\n  const [notification, setNotification] = useState(null);\n  const navigate = useNavigate();\n  const {\n    currentScan,\n    isScanning,\n    threatLevel,\n    notifications,\n    removeNotification\n  } = useScan();\n\n  // Function to scroll to scanner section and set active tab\n  const handleStartScan = () => {\n    setActiveTab(0);\n    // Smooth scroll to scanner section\n    setTimeout(() => {\n      const scannerElement = document.getElementById('scanner-section');\n      if (scannerElement) {\n        scannerElement.scrollIntoView({\n          behavior: 'smooth',\n          block: 'start'\n        });\n      }\n    }, 100);\n  };\n  const handleTabChange = (event, newValue) => {\n    setActiveTab(newValue);\n  };\n  const handleCloseNotification = notificationId => {\n    removeNotification(notificationId);\n  };\n  const handleDownloadReport = scanResult => {\n    // This would integrate with a PDF generation service\n    console.log('Downloading report for:', scanResult);\n    setNotification({\n      type: 'info',\n      message: 'Report download feature will be available soon.'\n    });\n  };\n  const handleShareResult = scanResult => {\n    // This would integrate with sharing functionality\n    console.log('Sharing result:', scanResult);\n    setNotification({\n      type: 'info',\n      message: 'Result sharing feature will be available soon.'\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    className: \"cyber-defense-bg-enhanced\",\n    sx: {\n      width: '100%',\n      minHeight: '100vh',\n      display: 'flex',\n      flexDirection: 'column',\n      justifyContent: 'center',\n      alignItems: 'center',\n      py: 4,\n      position: 'relative',\n      // Optimized text contrast overlay\n      '&::before': {\n        content: '\"\"',\n        position: 'absolute',\n        top: 0,\n        left: 0,\n        right: 0,\n        bottom: 0,\n        background: 'radial-gradient(ellipse at center, transparent 0%, rgba(15, 15, 15, 0.2) 60%, rgba(15, 15, 15, 0.4) 100%)',\n        zIndex: 1,\n        pointerEvents: 'none',\n        willChange: 'transform'\n      },\n      '@keyframes particleFloat': {\n        '0%': {\n          transform: 'translateY(0px) translateX(0px) rotate(0deg)'\n        },\n        '25%': {\n          transform: 'translateY(-15px) translateX(10px) rotate(90deg)'\n        },\n        '50%': {\n          transform: 'translateY(0px) translateX(-5px) rotate(180deg)'\n        },\n        '75%': {\n          transform: 'translateY(10px) translateX(-10px) rotate(270deg)'\n        },\n        '100%': {\n          transform: 'translateY(0px) translateX(0px) rotate(360deg)'\n        }\n      }\n    },\n    children: [/*#__PURE__*/_jsxDEV(Container, {\n      maxWidth: \"xl\",\n      sx: {\n        position: 'relative',\n        zIndex: 2\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        textAlign: \"center\",\n        mb: 8,\n        sx: {\n          position: 'relative',\n          py: 8,\n          px: 4,\n          borderRadius: 4,\n          overflow: 'hidden',\n          '&::before': {\n            content: '\"\"',\n            position: 'absolute',\n            top: 0,\n            left: 0,\n            right: 0,\n            bottom: 0,\n            background: 'linear-gradient(45deg, rgba(102, 126, 234, 0.08) 0%, rgba(118, 75, 162, 0.08) 25%, rgba(240, 147, 251, 0.08) 50%, rgba(79, 172, 254, 0.08) 75%, rgba(102, 126, 234, 0.08) 100%)',\n            backgroundSize: '400% 400%',\n            animation: 'cyberWaves 15s ease infinite',\n            zIndex: -2\n          },\n          '&::after': {\n            content: '\"\"',\n            position: 'absolute',\n            top: 0,\n            left: 0,\n            right: 0,\n            bottom: 0,\n            background: theme => theme.palette.mode === 'dark' ? 'linear-gradient(135deg, rgba(12, 12, 12, 0.4) 0%, rgba(26, 26, 26, 0.4) 50%, rgba(45, 45, 45, 0.4) 100%)' : 'linear-gradient(135deg, rgba(248, 250, 252, 0.6) 0%, rgba(241, 245, 249, 0.6) 50%, rgba(226, 232, 240, 0.6) 100%)',\n            backdropFilter: 'blur(20px) saturate(180%)',\n            zIndex: -1\n          },\n          '@keyframes cyberWaves': {\n            '0%': {\n              backgroundPosition: '0% 50%'\n            },\n            '50%': {\n              backgroundPosition: '100% 50%'\n            },\n            '100%': {\n              backgroundPosition: '0% 50%'\n            }\n          },\n          '@keyframes neuralPulse': {\n            '0%': {\n              backgroundPosition: '0% 50%'\n            },\n            '50%': {\n              backgroundPosition: '100% 50%'\n            },\n            '100%': {\n              backgroundPosition: '0% 50%'\n            }\n          },\n          '@keyframes quantumGlow': {\n            '0%': {\n              transform: 'scale(1)',\n              opacity: 0.3\n            },\n            '50%': {\n              transform: 'scale(1.05)',\n              opacity: 0.1\n            },\n            '100%': {\n              transform: 'scale(1)',\n              opacity: 0.3\n            }\n          }\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n            borderRadius: '50%',\n            width: 140,\n            height: 140,\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            mx: 'auto',\n            mb: 4,\n            boxShadow: '0 20px 60px rgba(102, 126, 234, 0.4)',\n            position: 'relative',\n            '&::before': {\n              content: '\"\"',\n              position: 'absolute',\n              top: -8,\n              left: -8,\n              right: -8,\n              bottom: -8,\n              borderRadius: '50%',\n              background: 'linear-gradient(45deg, #667eea, #764ba2, #f093fb, #f5576c)',\n              backgroundSize: '400% 400%',\n              animation: 'neuralPulse 3s ease infinite',\n              opacity: 0.7,\n              zIndex: -1\n            },\n            '&::after': {\n              content: '\"\"',\n              position: 'absolute',\n              top: -15,\n              left: -15,\n              right: -15,\n              bottom: -15,\n              borderRadius: '50%',\n              border: '3px solid rgba(102, 126, 234, 0.3)',\n              animation: 'quantumGlow 2s infinite'\n            }\n          },\n          children: /*#__PURE__*/_jsxDEV(SecurityIcon, {\n            sx: {\n              fontSize: 70,\n              color: 'white'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 211,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 171,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h1\",\n          component: \"h1\",\n          gutterBottom: true,\n          fontWeight: \"bold\",\n          sx: {\n            background: 'linear-gradient(45deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%)',\n            backgroundSize: '400% 400%',\n            backgroundClip: 'text',\n            WebkitBackgroundClip: 'text',\n            WebkitTextFillColor: 'transparent',\n            animation: 'neuralPulse 8s ease infinite',\n            mb: 3,\n            fontSize: {\n              xs: '2.5rem',\n              md: '4rem'\n            },\n            textShadow: '0 4px 20px rgba(102, 126, 234, 0.8), 0 2px 10px rgba(118, 75, 162, 0.6), 0 1px 5px rgba(240, 147, 251, 0.4)',\n            filter: 'drop-shadow(0 6px 12px rgba(102, 126, 234, 0.7)) drop-shadow(0 3px 6px rgba(255, 255, 255, 0.3))',\n            position: 'relative',\n            '&::before': {\n              content: '\"AI Security Guard\"',\n              position: 'absolute',\n              top: 0,\n              left: 0,\n              right: 0,\n              bottom: 0,\n              background: 'linear-gradient(45deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.9) 25%, rgba(255, 255, 255, 0.95) 50%, rgba(255, 255, 255, 0.9) 75%, rgba(255, 255, 255, 0.95) 100%)',\n              backgroundSize: '400% 400%',\n              backgroundClip: 'text',\n              WebkitBackgroundClip: 'text',\n              WebkitTextFillColor: 'transparent',\n              animation: 'neuralPulse 8s ease infinite',\n              zIndex: -1,\n              filter: 'blur(0.5px)',\n              opacity: 0.7\n            },\n            '&::after': {\n              content: '\"AI Security Guard\"',\n              position: 'absolute',\n              top: 0,\n              left: 0,\n              right: 0,\n              bottom: 0,\n              background: 'linear-gradient(45deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%)',\n              backgroundSize: '400% 400%',\n              backgroundClip: 'text',\n              WebkitBackgroundClip: 'text',\n              WebkitTextFillColor: 'transparent',\n              animation: 'neuralPulse 8s ease infinite',\n              zIndex: 1,\n              opacity: 0.9\n            }\n          },\n          children: \"AI Security Guard\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 215,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h4\",\n          color: \"text.secondary\",\n          maxWidth: \"900px\",\n          mx: \"auto\",\n          sx: {\n            mb: 6,\n            lineHeight: 1.6,\n            fontWeight: 400,\n            fontSize: {\n              xs: '1.2rem',\n              md: '1.5rem'\n            },\n            textShadow: '0 4px 12px rgba(0, 0, 0, 0.8), 0 2px 6px rgba(0, 0, 0, 0.6)',\n            color: 'rgba(255, 255, 255, 0.95)'\n          },\n          children: \"Next-generation cybersecurity powered by advanced artificial intelligence. Protect your digital assets with real-time threat detection and predictive analytics.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 270,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'center',\n            gap: 3,\n            flexWrap: 'wrap',\n            mb: 6\n          },\n          children: [{\n            label: 'Real-time Analytics',\n            gradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'\n          }, {\n            label: 'AI Threat Prediction',\n            gradient: 'linear-gradient(135deg, #9c27b0 0%, #e91e63 100%)'\n          }, {\n            label: 'Automated Response',\n            gradient: 'linear-gradient(135deg, #4caf50 0%, #8bc34a 100%)'\n          }, {\n            label: 'Smart Notifications',\n            gradient: 'linear-gradient(135deg, #2196f3 0%, #9c27b0 100%)'\n          }].map((feature, index) => /*#__PURE__*/_jsxDEV(Chip, {\n            label: feature.label,\n            variant: \"outlined\",\n            sx: {\n              borderColor: 'transparent',\n              background: feature.gradient,\n              color: 'white',\n              fontWeight: 600,\n              fontSize: '1rem',\n              px: 3,\n              py: 2,\n              height: 'auto',\n              borderRadius: 3,\n              transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',\n              boxShadow: '0 8px 25px rgba(0,0,0,0.4), 0 4px 12px rgba(102, 126, 234, 0.2)',\n              backdropFilter: 'blur(10px)',\n              border: '1px solid rgba(255, 255, 255, 0.1)',\n              textShadow: '0 2px 8px rgba(0, 0, 0, 0.8)',\n              '&:hover': {\n                transform: 'translateY(-6px) scale(1.08)',\n                boxShadow: '0 16px 40px rgba(0,0,0,0.5), 0 8px 20px rgba(102, 126, 234, 0.4)',\n                filter: 'brightness(1.1)'\n              }\n            }\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 303,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 288,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          display: \"flex\",\n          justifyContent: \"center\",\n          gap: 3,\n          flexWrap: \"wrap\",\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            size: \"large\",\n            onClick: handleStartScan // Navigate to URL Security Scanner tab\n            ,\n            sx: {\n              px: 6,\n              py: 2,\n              borderRadius: 3,\n              fontSize: '1.1rem',\n              fontWeight: 600,\n              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n              boxShadow: '0 12px 35px rgba(102, 126, 234, 0.5), 0 6px 15px rgba(0, 0, 0, 0.4)',\n              backdropFilter: 'blur(10px)',\n              border: '1px solid rgba(255, 255, 255, 0.1)',\n              textShadow: '0 2px 8px rgba(0, 0, 0, 0.6)',\n              '&:hover': {\n                background: 'linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)',\n                transform: 'translateY(-4px) scale(1.05)',\n                boxShadow: '0 20px 50px rgba(102, 126, 234, 0.6), 0 10px 25px rgba(0, 0, 0, 0.5)',\n                filter: 'brightness(1.1)'\n              }\n            },\n            children: \"Start Security Scan\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 334,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outlined\",\n            size: \"large\",\n            onClick: () => navigate('/smart-features'),\n            sx: {\n              px: 6,\n              py: 2,\n              borderRadius: 3,\n              fontSize: '1.1rem',\n              fontWeight: 600,\n              borderColor: 'rgba(255, 255, 255, 0.6)',\n              color: 'white',\n              borderWidth: 2,\n              backdropFilter: 'blur(10px)',\n              background: 'rgba(255, 255, 255, 0.05)',\n              textShadow: '0 2px 8px rgba(0, 0, 0, 0.6)',\n              '&:hover': {\n                borderWidth: 2,\n                borderColor: '#667eea',\n                background: 'linear-gradient(135deg, rgba(102, 126, 234, 0.2) 0%, rgba(118, 75, 162, 0.2) 100%)',\n                transform: 'translateY(-4px) scale(1.05)',\n                boxShadow: '0 16px 40px rgba(102, 126, 234, 0.3), 0 8px 20px rgba(0, 0, 0, 0.4)',\n                filter: 'brightness(1.2)'\n              }\n            },\n            children: \"Explore AI Features\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 359,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 333,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 119,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        width: \"100%\",\n        maxWidth: \"1200px\",\n        mx: \"auto\",\n        id: \"scanner-section\",\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Card, {\n            sx: {\n              mb: 6,\n              background: 'linear-gradient(135deg, rgba(12, 12, 12, 0.95) 0%, rgba(26, 26, 26, 0.95) 50%, rgba(45, 45, 45, 0.95) 100%)',\n              backdropFilter: 'blur(25px) saturate(180%)',\n              border: '1px solid rgba(255, 255, 255, 0.1)',\n              borderRadius: 4,\n              overflow: 'hidden',\n              position: 'relative',\n              boxShadow: '0 20px 60px rgba(102, 126, 234, 0.2)',\n              transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',\n              '&:hover': {\n                transform: 'translateY(-4px)',\n                boxShadow: '0 30px 80px rgba(102, 126, 234, 0.3)'\n              },\n              '&::before': {\n                content: '\"\"',\n                position: 'absolute',\n                top: 0,\n                left: 0,\n                right: 0,\n                height: '4px',\n                background: 'linear-gradient(90deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%)',\n                backgroundSize: '200% 200%',\n                animation: 'gradientShift 4s ease infinite'\n              },\n              '@keyframes gradientShift': {\n                '0%': {\n                  backgroundPosition: '0% 50%'\n                },\n                '50%': {\n                  backgroundPosition: '100% 50%'\n                },\n                '100%': {\n                  backgroundPosition: '0% 50%'\n                }\n              }\n            },\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              sx: {\n                p: 6\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  borderBottom: 1,\n                  borderColor: 'divider',\n                  mb: 4\n                },\n                children: /*#__PURE__*/_jsxDEV(Tabs, {\n                  value: activeTab,\n                  onChange: handleTabChange,\n                  variant: \"fullWidth\",\n                  textColor: \"primary\",\n                  indicatorColor: \"primary\",\n                  sx: {\n                    '& .MuiTab-root': {\n                      minHeight: 72,\n                      fontSize: '1rem',\n                      fontWeight: 600,\n                      textTransform: 'none'\n                    }\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Tab, {\n                    icon: /*#__PURE__*/_jsxDEV(LinkIcon, {\n                      sx: {\n                        fontSize: 28\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 446,\n                      columnNumber: 31\n                    }, this),\n                    label: \"URL Security Scanner\",\n                    iconPosition: \"start\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 445,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Tab, {\n                    icon: /*#__PURE__*/_jsxDEV(FileIcon, {\n                      sx: {\n                        fontSize: 28\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 451,\n                      columnNumber: 31\n                    }, this),\n                    label: \"File Security Scanner\",\n                    iconPosition: \"start\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 450,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Tab, {\n                    icon: /*#__PURE__*/_jsxDEV(SecurityIcon, {\n                      sx: {\n                        fontSize: 28\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 456,\n                      columnNumber: 31\n                    }, this),\n                    label: \"Smart Dashboard\",\n                    iconPosition: \"start\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 455,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Tab, {\n                    icon: /*#__PURE__*/_jsxDEV(AssessmentIcon, {\n                      sx: {\n                        fontSize: 28\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 461,\n                      columnNumber: 31\n                    }, this),\n                    label: \"Threat Prediction\",\n                    iconPosition: \"start\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 460,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 430,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 429,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                children: [activeTab === 0 && /*#__PURE__*/_jsxDEV(Fade, {\n                  in: activeTab === 0,\n                  timeout: 300,\n                  children: /*#__PURE__*/_jsxDEV(Box, {\n                    children: /*#__PURE__*/_jsxDEV(LinkScanner, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 473,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 472,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 471,\n                  columnNumber: 23\n                }, this), activeTab === 1 && /*#__PURE__*/_jsxDEV(Fade, {\n                  in: activeTab === 1,\n                  timeout: 300,\n                  children: /*#__PURE__*/_jsxDEV(Box, {\n                    children: /*#__PURE__*/_jsxDEV(FileUploader, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 481,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 480,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 479,\n                  columnNumber: 23\n                }, this), activeTab === 2 && /*#__PURE__*/_jsxDEV(Fade, {\n                  in: activeTab === 2,\n                  timeout: 300,\n                  children: /*#__PURE__*/_jsxDEV(Box, {\n                    children: /*#__PURE__*/_jsxDEV(SmartDashboard, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 489,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 488,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 487,\n                  columnNumber: 23\n                }, this), activeTab === 3 && /*#__PURE__*/_jsxDEV(Fade, {\n                  in: activeTab === 3,\n                  timeout: 300,\n                  children: /*#__PURE__*/_jsxDEV(Box, {\n                    children: /*#__PURE__*/_jsxDEV(ThreatPrediction, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 497,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 496,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 495,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 469,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 427,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 394,\n            columnNumber: 13\n          }, this), (currentScan || isScanning) && /*#__PURE__*/_jsxDEV(Fade, {\n            in: true,\n            timeout: 500,\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              children: /*#__PURE__*/_jsxDEV(ResultView, {\n                scanResult: currentScan,\n                onDownloadReport: handleDownloadReport,\n                onShareResult: handleShareResult\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 509,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 508,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 507,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 393,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 391,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 117,\n      columnNumber: 7\n    }, this), notifications.map(notification => /*#__PURE__*/_jsxDEV(Snackbar, {\n      open: true,\n      autoHideDuration: 6000,\n      onClose: () => handleCloseNotification(notification.id),\n      anchorOrigin: {\n        vertical: 'bottom',\n        horizontal: 'right'\n      },\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        onClose: () => handleCloseNotification(notification.id),\n        severity: notification.type,\n        variant: \"filled\",\n        sx: {\n          width: '100%',\n          borderRadius: 2,\n          boxShadow: '0 8px 32px rgba(0,0,0,0.12)'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle2\",\n          fontWeight: \"bold\",\n          children: notification.title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 546,\n          columnNumber: 13\n        }, this), notification.message && /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          children: notification.message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 550,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 536,\n        columnNumber: 11\n      }, this)\n    }, notification.id, false, {\n      fileName: _jsxFileName,\n      lineNumber: 529,\n      columnNumber: 9\n    }, this)), notification && /*#__PURE__*/_jsxDEV(Snackbar, {\n      open: true,\n      autoHideDuration: 4000,\n      onClose: () => setNotification(null),\n      anchorOrigin: {\n        vertical: 'bottom',\n        horizontal: 'center'\n      },\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        onClose: () => setNotification(null),\n        severity: notification.type,\n        variant: \"filled\",\n        sx: {\n          borderRadius: 2,\n          boxShadow: '0 8px 32px rgba(0,0,0,0.12)'\n        },\n        children: notification.message\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 566,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 560,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 84,\n    columnNumber: 5\n  }, this);\n}, \"UfI/CENxLvYOx40AaSKFcfyM5pQ=\", false, function () {\n  return [useNavigate, useScan];\n})), \"UfI/CENxLvYOx40AaSKFcfyM5pQ=\", false, function () {\n  return [useNavigate, useScan];\n});\n_c2 = HomePage;\nHomePage.displayName = 'HomePage';\nexport default HomePage;\nvar _c, _c2;\n$RefreshReg$(_c, \"HomePage$React.memo\");\n$RefreshReg$(_c2, \"HomePage\");", "map": {"version": 3, "names": ["React", "useState", "Container", "Grid", "Box", "Typography", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Tabs", "Tab", "<PERSON><PERSON>", "Snackbar", "Fade", "<PERSON><PERSON>", "Chip", "Security", "SecurityIcon", "Link", "LinkIcon", "InsertDriveFile", "FileIcon", "Assessment", "AssessmentIcon", "useNavigate", "useScan", "LinkScanner", "FileUploader", "ResultView", "SmartDashboard", "ThreatPrediction", "SmartNotifications", "jsxDEV", "_jsxDEV", "HomePage", "_s", "memo", "_c", "activeTab", "setActiveTab", "notification", "setNotification", "navigate", "currentScan", "isScanning", "threatLevel", "notifications", "removeNotification", "handleStartScan", "setTimeout", "scannerElement", "document", "getElementById", "scrollIntoView", "behavior", "block", "handleTabChange", "event", "newValue", "handleCloseNotification", "notificationId", "handleDownloadReport", "scanResult", "console", "log", "type", "message", "handleShareResult", "className", "sx", "width", "minHeight", "display", "flexDirection", "justifyContent", "alignItems", "py", "position", "content", "top", "left", "right", "bottom", "background", "zIndex", "pointerEvents", "<PERSON><PERSON><PERSON><PERSON>", "transform", "children", "max<PERSON><PERSON><PERSON>", "textAlign", "mb", "px", "borderRadius", "overflow", "backgroundSize", "animation", "theme", "palette", "mode", "<PERSON><PERSON>ilter", "backgroundPosition", "opacity", "height", "mx", "boxShadow", "border", "fontSize", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "component", "gutterBottom", "fontWeight", "backgroundClip", "WebkitBackgroundClip", "WebkitTextFillColor", "xs", "md", "textShadow", "filter", "lineHeight", "gap", "flexWrap", "label", "gradient", "map", "feature", "index", "borderColor", "transition", "size", "onClick", "borderWidth", "id", "p", "borderBottom", "value", "onChange", "textColor", "indicatorColor", "textTransform", "icon", "iconPosition", "in", "timeout", "onDownloadReport", "onShareResult", "open", "autoHideDuration", "onClose", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "severity", "title", "_c2", "displayName", "$RefreshReg$"], "sources": ["E:/PythonProjects/React.js/Smart AI Assisstant/src/pages/HomePage.jsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  Container,\n  Grid,\n  Box,\n  Typography,\n  Card,\n  CardContent,\n  Tabs,\n  Tab,\n  Alert,\n  <PERSON>nackbar,\n  Fade,\n  Button,\n  Chip,\n} from '@mui/material';\nimport {\n  Security as SecurityIcon,\n  Link as LinkIcon,\n  InsertDriveFile as FileIcon,\n  Assessment as AssessmentIcon,\n} from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport { useScan } from '../contexts/ScanContext';\nimport LinkScanner from '../components/LinkScanner';\nimport FileUploader from '../components/FileUploader';\nimport ResultView from '../components/ResultView';\nimport SmartDashboard from '../components/SmartDashboard';\nimport ThreatPrediction from '../components/ThreatPrediction';\nimport SmartNotifications from '../components/SmartNotifications';\n\nconst HomePage = React.memo(() => {\n  const [activeTab, setActiveTab] = useState(0);\n  const [notification, setNotification] = useState(null);\n  const navigate = useNavigate();\n\n  const {\n    currentScan,\n    isScanning,\n    threatLevel,\n    notifications,\n    removeNotification\n  } = useScan();\n\n  // Function to scroll to scanner section and set active tab\n  const handleStartScan = () => {\n    setActiveTab(0);\n    // Smooth scroll to scanner section\n    setTimeout(() => {\n      const scannerElement = document.getElementById('scanner-section');\n      if (scannerElement) {\n        scannerElement.scrollIntoView({ behavior: 'smooth', block: 'start' });\n      }\n    }, 100);\n  };\n\n  const handleTabChange = (event, newValue) => {\n    setActiveTab(newValue);\n  };\n\n  const handleCloseNotification = (notificationId) => {\n    removeNotification(notificationId);\n  };\n\n  const handleDownloadReport = (scanResult) => {\n    // This would integrate with a PDF generation service\n    console.log('Downloading report for:', scanResult);\n    setNotification({\n      type: 'info',\n      message: 'Report download feature will be available soon.',\n    });\n  };\n\n  const handleShareResult = (scanResult) => {\n    // This would integrate with sharing functionality\n    console.log('Sharing result:', scanResult);\n    setNotification({\n      type: 'info',\n      message: 'Result sharing feature will be available soon.',\n    });\n  };\n\n  return (\n    <Box\n      className=\"cyber-defense-bg-enhanced\"\n      sx={{\n        width: '100%',\n        minHeight: '100vh',\n        display: 'flex',\n        flexDirection: 'column',\n        justifyContent: 'center',\n        alignItems: 'center',\n        py: 4,\n        position: 'relative',\n        // Optimized text contrast overlay\n        '&::before': {\n          content: '\"\"',\n          position: 'absolute',\n          top: 0,\n          left: 0,\n          right: 0,\n          bottom: 0,\n          background: 'radial-gradient(ellipse at center, transparent 0%, rgba(15, 15, 15, 0.2) 60%, rgba(15, 15, 15, 0.4) 100%)',\n          zIndex: 1,\n          pointerEvents: 'none',\n          willChange: 'transform',\n        },\n        '@keyframes particleFloat': {\n          '0%': { transform: 'translateY(0px) translateX(0px) rotate(0deg)' },\n          '25%': { transform: 'translateY(-15px) translateX(10px) rotate(90deg)' },\n          '50%': { transform: 'translateY(0px) translateX(-5px) rotate(180deg)' },\n          '75%': { transform: 'translateY(10px) translateX(-10px) rotate(270deg)' },\n          '100%': { transform: 'translateY(0px) translateX(0px) rotate(360deg)' },\n        },\n      }}\n    >\n      <Container maxWidth=\"xl\" sx={{ position: 'relative', zIndex: 2 }}>\n        {/* Professional Hero Section - Quantum Shield */}\n        <Box\n          textAlign=\"center\"\n          mb={8}\n          sx={{\n            position: 'relative',\n            py: 8,\n            px: 4,\n            borderRadius: 4,\n            overflow: 'hidden',\n            '&::before': {\n              content: '\"\"',\n              position: 'absolute',\n              top: 0,\n              left: 0,\n              right: 0,\n              bottom: 0,\n              background: 'linear-gradient(45deg, rgba(102, 126, 234, 0.08) 0%, rgba(118, 75, 162, 0.08) 25%, rgba(240, 147, 251, 0.08) 50%, rgba(79, 172, 254, 0.08) 75%, rgba(102, 126, 234, 0.08) 100%)',\n              backgroundSize: '400% 400%',\n              animation: 'cyberWaves 15s ease infinite',\n              zIndex: -2,\n            },\n            '&::after': {\n              content: '\"\"',\n              position: 'absolute',\n              top: 0,\n              left: 0,\n              right: 0,\n              bottom: 0,\n              background: (theme) => theme.palette.mode === 'dark'\n                ? 'linear-gradient(135deg, rgba(12, 12, 12, 0.4) 0%, rgba(26, 26, 26, 0.4) 50%, rgba(45, 45, 45, 0.4) 100%)'\n                : 'linear-gradient(135deg, rgba(248, 250, 252, 0.6) 0%, rgba(241, 245, 249, 0.6) 50%, rgba(226, 232, 240, 0.6) 100%)',\n              backdropFilter: 'blur(20px) saturate(180%)',\n              zIndex: -1,\n            },\n            '@keyframes cyberWaves': {\n              '0%': { backgroundPosition: '0% 50%' },\n              '50%': { backgroundPosition: '100% 50%' },\n              '100%': { backgroundPosition: '0% 50%' },\n            },\n            '@keyframes neuralPulse': {\n              '0%': { backgroundPosition: '0% 50%' },\n              '50%': { backgroundPosition: '100% 50%' },\n              '100%': { backgroundPosition: '0% 50%' },\n            },\n            '@keyframes quantumGlow': {\n              '0%': { transform: 'scale(1)', opacity: 0.3 },\n              '50%': { transform: 'scale(1.05)', opacity: 0.1 },\n              '100%': { transform: 'scale(1)', opacity: 0.3 },\n            },\n          }}\n        >\n          {/* Quantum Shield Icon */}\n          <Box\n            sx={{\n              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n              borderRadius: '50%',\n              width: 140,\n              height: 140,\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              mx: 'auto',\n              mb: 4,\n              boxShadow: '0 20px 60px rgba(102, 126, 234, 0.4)',\n              position: 'relative',\n              '&::before': {\n                content: '\"\"',\n                position: 'absolute',\n                top: -8,\n                left: -8,\n                right: -8,\n                bottom: -8,\n                borderRadius: '50%',\n                background: 'linear-gradient(45deg, #667eea, #764ba2, #f093fb, #f5576c)',\n                backgroundSize: '400% 400%',\n                animation: 'neuralPulse 3s ease infinite',\n                opacity: 0.7,\n                zIndex: -1,\n              },\n              '&::after': {\n                content: '\"\"',\n                position: 'absolute',\n                top: -15,\n                left: -15,\n                right: -15,\n                bottom: -15,\n                borderRadius: '50%',\n                border: '3px solid rgba(102, 126, 234, 0.3)',\n                animation: 'quantumGlow 2s infinite',\n              },\n            }}\n          >\n            <SecurityIcon sx={{ fontSize: 70, color: 'white' }} />\n          </Box>\n\n          {/* Neural Pulse Title */}\n          <Typography\n            variant=\"h1\"\n            component=\"h1\"\n            gutterBottom\n            fontWeight=\"bold\"\n            sx={{\n              background: 'linear-gradient(45deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%)',\n              backgroundSize: '400% 400%',\n              backgroundClip: 'text',\n              WebkitBackgroundClip: 'text',\n              WebkitTextFillColor: 'transparent',\n              animation: 'neuralPulse 8s ease infinite',\n              mb: 3,\n              fontSize: { xs: '2.5rem', md: '4rem' },\n              textShadow: '0 4px 20px rgba(102, 126, 234, 0.8), 0 2px 10px rgba(118, 75, 162, 0.6), 0 1px 5px rgba(240, 147, 251, 0.4)',\n              filter: 'drop-shadow(0 6px 12px rgba(102, 126, 234, 0.7)) drop-shadow(0 3px 6px rgba(255, 255, 255, 0.3))',\n              position: 'relative',\n              '&::before': {\n                content: '\"AI Security Guard\"',\n                position: 'absolute',\n                top: 0,\n                left: 0,\n                right: 0,\n                bottom: 0,\n                background: 'linear-gradient(45deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.9) 25%, rgba(255, 255, 255, 0.95) 50%, rgba(255, 255, 255, 0.9) 75%, rgba(255, 255, 255, 0.95) 100%)',\n                backgroundSize: '400% 400%',\n                backgroundClip: 'text',\n                WebkitBackgroundClip: 'text',\n                WebkitTextFillColor: 'transparent',\n                animation: 'neuralPulse 8s ease infinite',\n                zIndex: -1,\n                filter: 'blur(0.5px)',\n                opacity: 0.7,\n              },\n              '&::after': {\n                content: '\"AI Security Guard\"',\n                position: 'absolute',\n                top: 0,\n                left: 0,\n                right: 0,\n                bottom: 0,\n                background: 'linear-gradient(45deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%)',\n                backgroundSize: '400% 400%',\n                backgroundClip: 'text',\n                WebkitBackgroundClip: 'text',\n                WebkitTextFillColor: 'transparent',\n                animation: 'neuralPulse 8s ease infinite',\n                zIndex: 1,\n                opacity: 0.9,\n              },\n            }}\n          >\n            AI Security Guard\n          </Typography>\n\n          <Typography\n            variant=\"h4\"\n            color=\"text.secondary\"\n            maxWidth=\"900px\"\n            mx=\"auto\"\n            sx={{\n              mb: 6,\n              lineHeight: 1.6,\n              fontWeight: 400,\n              fontSize: { xs: '1.2rem', md: '1.5rem' },\n              textShadow: '0 4px 12px rgba(0, 0, 0, 0.8), 0 2px 6px rgba(0, 0, 0, 0.6)',\n              color: 'rgba(255, 255, 255, 0.95)',\n            }}\n          >\n            Next-generation cybersecurity powered by advanced artificial intelligence.\n            Protect your digital assets with real-time threat detection and predictive analytics.\n          </Typography>\n          {/* Professional Feature Chips */}\n          <Box\n            sx={{\n              display: 'flex',\n              justifyContent: 'center',\n              gap: 3,\n              flexWrap: 'wrap',\n              mb: 6,\n            }}\n          >\n            {[\n              { label: 'Real-time Analytics', gradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' },\n              { label: 'AI Threat Prediction', gradient: 'linear-gradient(135deg, #9c27b0 0%, #e91e63 100%)' },\n              { label: 'Automated Response', gradient: 'linear-gradient(135deg, #4caf50 0%, #8bc34a 100%)' },\n              { label: 'Smart Notifications', gradient: 'linear-gradient(135deg, #2196f3 0%, #9c27b0 100%)' },\n            ].map((feature, index) => (\n              <Chip\n                key={index}\n                label={feature.label}\n                variant=\"outlined\"\n                sx={{\n                  borderColor: 'transparent',\n                  background: feature.gradient,\n                  color: 'white',\n                  fontWeight: 600,\n                  fontSize: '1rem',\n                  px: 3,\n                  py: 2,\n                  height: 'auto',\n                  borderRadius: 3,\n                  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',\n                  boxShadow: '0 8px 25px rgba(0,0,0,0.4), 0 4px 12px rgba(102, 126, 234, 0.2)',\n                  backdropFilter: 'blur(10px)',\n                  border: '1px solid rgba(255, 255, 255, 0.1)',\n                  textShadow: '0 2px 8px rgba(0, 0, 0, 0.8)',\n                  '&:hover': {\n                    transform: 'translateY(-6px) scale(1.08)',\n                    boxShadow: '0 16px 40px rgba(0,0,0,0.5), 0 8px 20px rgba(102, 126, 234, 0.4)',\n                    filter: 'brightness(1.1)',\n                  },\n                }}\n              />\n            ))}\n          </Box>\n\n          {/* CTA Buttons */}\n          <Box display=\"flex\" justifyContent=\"center\" gap={3} flexWrap=\"wrap\">\n            <Button\n              variant=\"contained\"\n              size=\"large\"\n              onClick={handleStartScan} // Navigate to URL Security Scanner tab\n              sx={{\n                px: 6,\n                py: 2,\n                borderRadius: 3,\n                fontSize: '1.1rem',\n                fontWeight: 600,\n                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                boxShadow: '0 12px 35px rgba(102, 126, 234, 0.5), 0 6px 15px rgba(0, 0, 0, 0.4)',\n                backdropFilter: 'blur(10px)',\n                border: '1px solid rgba(255, 255, 255, 0.1)',\n                textShadow: '0 2px 8px rgba(0, 0, 0, 0.6)',\n                '&:hover': {\n                  background: 'linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)',\n                  transform: 'translateY(-4px) scale(1.05)',\n                  boxShadow: '0 20px 50px rgba(102, 126, 234, 0.6), 0 10px 25px rgba(0, 0, 0, 0.5)',\n                  filter: 'brightness(1.1)',\n                },\n              }}\n            >\n              Start Security Scan\n            </Button>\n            <Button\n              variant=\"outlined\"\n              size=\"large\"\n              onClick={() => navigate('/smart-features')}\n              sx={{\n                px: 6,\n                py: 2,\n                borderRadius: 3,\n                fontSize: '1.1rem',\n                fontWeight: 600,\n                borderColor: 'rgba(255, 255, 255, 0.6)',\n                color: 'white',\n                borderWidth: 2,\n                backdropFilter: 'blur(10px)',\n                background: 'rgba(255, 255, 255, 0.05)',\n                textShadow: '0 2px 8px rgba(0, 0, 0, 0.6)',\n                '&:hover': {\n                  borderWidth: 2,\n                  borderColor: '#667eea',\n                  background: 'linear-gradient(135deg, rgba(102, 126, 234, 0.2) 0%, rgba(118, 75, 162, 0.2) 100%)',\n                  transform: 'translateY(-4px) scale(1.05)',\n                  boxShadow: '0 16px 40px rgba(102, 126, 234, 0.3), 0 8px 20px rgba(0, 0, 0, 0.4)',\n                  filter: 'brightness(1.2)',\n                },\n              }}\n            >\n              Explore AI Features\n            </Button>\n          </Box>\n        </Box>\n\n        {/* Full Width Professional Scanner Section */}\n        <Box width=\"100%\" maxWidth=\"1200px\" mx=\"auto\" id=\"scanner-section\">\n          {/* Main Scanner Card - Full Width */}\n          <Box>\n            <Card\n              sx={{\n                mb: 6,\n                background: 'linear-gradient(135deg, rgba(12, 12, 12, 0.95) 0%, rgba(26, 26, 26, 0.95) 50%, rgba(45, 45, 45, 0.95) 100%)',\n                backdropFilter: 'blur(25px) saturate(180%)',\n                border: '1px solid rgba(255, 255, 255, 0.1)',\n                borderRadius: 4,\n                overflow: 'hidden',\n                position: 'relative',\n                boxShadow: '0 20px 60px rgba(102, 126, 234, 0.2)',\n                transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',\n                '&:hover': {\n                  transform: 'translateY(-4px)',\n                  boxShadow: '0 30px 80px rgba(102, 126, 234, 0.3)',\n                },\n                '&::before': {\n                  content: '\"\"',\n                  position: 'absolute',\n                  top: 0,\n                  left: 0,\n                  right: 0,\n                  height: '4px',\n                  background: 'linear-gradient(90deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%)',\n                  backgroundSize: '200% 200%',\n                  animation: 'gradientShift 4s ease infinite',\n                },\n                '@keyframes gradientShift': {\n                  '0%': { backgroundPosition: '0% 50%' },\n                  '50%': { backgroundPosition: '100% 50%' },\n                  '100%': { backgroundPosition: '0% 50%' },\n                },\n              }}\n            >\n              <CardContent sx={{ p: 6 }}>\n                  {/* Tabs for different scan types */}\n                  <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 4 }}>\n                    <Tabs\n                      value={activeTab}\n                      onChange={handleTabChange}\n                      variant=\"fullWidth\"\n                      textColor=\"primary\"\n                      indicatorColor=\"primary\"\n                      sx={{\n                        '& .MuiTab-root': {\n                          minHeight: 72,\n                          fontSize: '1rem',\n                          fontWeight: 600,\n                          textTransform: 'none',\n                        },\n                      }}\n                    >\n                      <Tab\n                        icon={<LinkIcon sx={{ fontSize: 28 }} />}\n                        label=\"URL Security Scanner\"\n                        iconPosition=\"start\"\n                      />\n                      <Tab\n                        icon={<FileIcon sx={{ fontSize: 28 }} />}\n                        label=\"File Security Scanner\"\n                        iconPosition=\"start\"\n                      />\n                      <Tab\n                        icon={<SecurityIcon sx={{ fontSize: 28 }} />}\n                        label=\"Smart Dashboard\"\n                        iconPosition=\"start\"\n                      />\n                      <Tab\n                        icon={<AssessmentIcon sx={{ fontSize: 28 }} />}\n                        label=\"Threat Prediction\"\n                        iconPosition=\"start\"\n                      />\n                    </Tabs>\n                  </Box>\n\n                  {/* Tab Panels */}\n                  <Box>\n                    {activeTab === 0 && (\n                      <Fade in={activeTab === 0} timeout={300}>\n                        <Box>\n                          <LinkScanner />\n                        </Box>\n                      </Fade>\n                    )}\n\n                    {activeTab === 1 && (\n                      <Fade in={activeTab === 1} timeout={300}>\n                        <Box>\n                          <FileUploader />\n                        </Box>\n                      </Fade>\n                    )}\n\n                    {activeTab === 2 && (\n                      <Fade in={activeTab === 2} timeout={300}>\n                        <Box>\n                          <SmartDashboard />\n                        </Box>\n                      </Fade>\n                    )}\n\n                    {activeTab === 3 && (\n                      <Fade in={activeTab === 3} timeout={300}>\n                        <Box>\n                          <ThreatPrediction />\n                        </Box>\n                      </Fade>\n                    )}\n                  </Box>\n                </CardContent>\n              </Card>\n\n              {/* Results Section */}\n              {(currentScan || isScanning) && (\n                <Fade in={true} timeout={500}>\n                  <Box>\n                    <ResultView\n                      scanResult={currentScan}\n                      onDownloadReport={handleDownloadReport}\n                      onShareResult={handleShareResult}\n                    />\n                  </Box>\n                </Fade>\n              )}\n\n\n\n\n\n\n            </Box>\n        </Box>\n      </Container>\n\n      {/* Notifications */}\n      {notifications.map((notification) => (\n        <Snackbar\n          key={notification.id}\n          open={true}\n          autoHideDuration={6000}\n          onClose={() => handleCloseNotification(notification.id)}\n          anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}\n        >\n          <Alert\n            onClose={() => handleCloseNotification(notification.id)}\n            severity={notification.type}\n            variant=\"filled\"\n            sx={{\n              width: '100%',\n              borderRadius: 2,\n              boxShadow: '0 8px 32px rgba(0,0,0,0.12)',\n            }}\n          >\n            <Typography variant=\"subtitle2\" fontWeight=\"bold\">\n              {notification.title}\n            </Typography>\n            {notification.message && (\n              <Typography variant=\"body2\">\n                {notification.message}\n              </Typography>\n            )}\n          </Alert>\n        </Snackbar>\n      ))}\n\n      {/* Custom notification */}\n      {notification && (\n        <Snackbar\n          open={true}\n          autoHideDuration={4000}\n          onClose={() => setNotification(null)}\n          anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}\n        >\n          <Alert\n            onClose={() => setNotification(null)}\n            severity={notification.type}\n            variant=\"filled\"\n            sx={{\n              borderRadius: 2,\n              boxShadow: '0 8px 32px rgba(0,0,0,0.12)',\n            }}\n          >\n            {notification.message}\n          </Alert>\n        </Snackbar>\n      )}\n    </Box>\n  );\n});\n\nHomePage.displayName = 'HomePage';\n\nexport default HomePage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,SAAS,EACTC,IAAI,EACJC,GAAG,EACHC,UAAU,EACVC,IAAI,EACJC,WAAW,EACXC,IAAI,EACJC,GAAG,EACHC,KAAK,EACLC,QAAQ,EACRC,IAAI,EACJC,MAAM,EACNC,IAAI,QACC,eAAe;AACtB,SACEC,QAAQ,IAAIC,YAAY,EACxBC,IAAI,IAAIC,QAAQ,EAChBC,eAAe,IAAIC,QAAQ,EAC3BC,UAAU,IAAIC,cAAc,QACvB,qBAAqB;AAC5B,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,OAAO,QAAQ,yBAAyB;AACjD,OAAOC,WAAW,MAAM,2BAA2B;AACnD,OAAOC,YAAY,MAAM,4BAA4B;AACrD,OAAOC,UAAU,MAAM,0BAA0B;AACjD,OAAOC,cAAc,MAAM,8BAA8B;AACzD,OAAOC,gBAAgB,MAAM,gCAAgC;AAC7D,OAAOC,kBAAkB,MAAM,kCAAkC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElE,MAAMC,QAAQ,gBAAAC,EAAA,cAAGlC,KAAK,CAACmC,IAAI,CAAAC,EAAA,GAAAF,EAAA,CAAC,MAAM;EAAAA,EAAA;EAChC,MAAM,CAACG,SAAS,EAAEC,YAAY,CAAC,GAAGrC,QAAQ,CAAC,CAAC,CAAC;EAC7C,MAAM,CAACsC,YAAY,EAAEC,eAAe,CAAC,GAAGvC,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAMwC,QAAQ,GAAGlB,WAAW,CAAC,CAAC;EAE9B,MAAM;IACJmB,WAAW;IACXC,UAAU;IACVC,WAAW;IACXC,aAAa;IACbC;EACF,CAAC,GAAGtB,OAAO,CAAC,CAAC;;EAEb;EACA,MAAMuB,eAAe,GAAGA,CAAA,KAAM;IAC5BT,YAAY,CAAC,CAAC,CAAC;IACf;IACAU,UAAU,CAAC,MAAM;MACf,MAAMC,cAAc,GAAGC,QAAQ,CAACC,cAAc,CAAC,iBAAiB,CAAC;MACjE,IAAIF,cAAc,EAAE;QAClBA,cAAc,CAACG,cAAc,CAAC;UAAEC,QAAQ,EAAE,QAAQ;UAAEC,KAAK,EAAE;QAAQ,CAAC,CAAC;MACvE;IACF,CAAC,EAAE,GAAG,CAAC;EACT,CAAC;EAED,MAAMC,eAAe,GAAGA,CAACC,KAAK,EAAEC,QAAQ,KAAK;IAC3CnB,YAAY,CAACmB,QAAQ,CAAC;EACxB,CAAC;EAED,MAAMC,uBAAuB,GAAIC,cAAc,IAAK;IAClDb,kBAAkB,CAACa,cAAc,CAAC;EACpC,CAAC;EAED,MAAMC,oBAAoB,GAAIC,UAAU,IAAK;IAC3C;IACAC,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEF,UAAU,CAAC;IAClDrB,eAAe,CAAC;MACdwB,IAAI,EAAE,MAAM;MACZC,OAAO,EAAE;IACX,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,iBAAiB,GAAIL,UAAU,IAAK;IACxC;IACAC,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEF,UAAU,CAAC;IAC1CrB,eAAe,CAAC;MACdwB,IAAI,EAAE,MAAM;MACZC,OAAO,EAAE;IACX,CAAC,CAAC;EACJ,CAAC;EAED,oBACEjC,OAAA,CAAC5B,GAAG;IACF+D,SAAS,EAAC,2BAA2B;IACrCC,EAAE,EAAE;MACFC,KAAK,EAAE,MAAM;MACbC,SAAS,EAAE,OAAO;MAClBC,OAAO,EAAE,MAAM;MACfC,aAAa,EAAE,QAAQ;MACvBC,cAAc,EAAE,QAAQ;MACxBC,UAAU,EAAE,QAAQ;MACpBC,EAAE,EAAE,CAAC;MACLC,QAAQ,EAAE,UAAU;MACpB;MACA,WAAW,EAAE;QACXC,OAAO,EAAE,IAAI;QACbD,QAAQ,EAAE,UAAU;QACpBE,GAAG,EAAE,CAAC;QACNC,IAAI,EAAE,CAAC;QACPC,KAAK,EAAE,CAAC;QACRC,MAAM,EAAE,CAAC;QACTC,UAAU,EAAE,2GAA2G;QACvHC,MAAM,EAAE,CAAC;QACTC,aAAa,EAAE,MAAM;QACrBC,UAAU,EAAE;MACd,CAAC;MACD,0BAA0B,EAAE;QAC1B,IAAI,EAAE;UAAEC,SAAS,EAAE;QAA+C,CAAC;QACnE,KAAK,EAAE;UAAEA,SAAS,EAAE;QAAmD,CAAC;QACxE,KAAK,EAAE;UAAEA,SAAS,EAAE;QAAkD,CAAC;QACvE,KAAK,EAAE;UAAEA,SAAS,EAAE;QAAoD,CAAC;QACzE,MAAM,EAAE;UAAEA,SAAS,EAAE;QAAiD;MACxE;IACF,CAAE;IAAAC,QAAA,gBAEFvD,OAAA,CAAC9B,SAAS;MAACsF,QAAQ,EAAC,IAAI;MAACpB,EAAE,EAAE;QAAEQ,QAAQ,EAAE,UAAU;QAAEO,MAAM,EAAE;MAAE,CAAE;MAAAI,QAAA,gBAE/DvD,OAAA,CAAC5B,GAAG;QACFqF,SAAS,EAAC,QAAQ;QAClBC,EAAE,EAAE,CAAE;QACNtB,EAAE,EAAE;UACFQ,QAAQ,EAAE,UAAU;UACpBD,EAAE,EAAE,CAAC;UACLgB,EAAE,EAAE,CAAC;UACLC,YAAY,EAAE,CAAC;UACfC,QAAQ,EAAE,QAAQ;UAClB,WAAW,EAAE;YACXhB,OAAO,EAAE,IAAI;YACbD,QAAQ,EAAE,UAAU;YACpBE,GAAG,EAAE,CAAC;YACNC,IAAI,EAAE,CAAC;YACPC,KAAK,EAAE,CAAC;YACRC,MAAM,EAAE,CAAC;YACTC,UAAU,EAAE,iLAAiL;YAC7LY,cAAc,EAAE,WAAW;YAC3BC,SAAS,EAAE,8BAA8B;YACzCZ,MAAM,EAAE,CAAC;UACX,CAAC;UACD,UAAU,EAAE;YACVN,OAAO,EAAE,IAAI;YACbD,QAAQ,EAAE,UAAU;YACpBE,GAAG,EAAE,CAAC;YACNC,IAAI,EAAE,CAAC;YACPC,KAAK,EAAE,CAAC;YACRC,MAAM,EAAE,CAAC;YACTC,UAAU,EAAGc,KAAK,IAAKA,KAAK,CAACC,OAAO,CAACC,IAAI,KAAK,MAAM,GAChD,0GAA0G,GAC1G,mHAAmH;YACvHC,cAAc,EAAE,2BAA2B;YAC3ChB,MAAM,EAAE,CAAC;UACX,CAAC;UACD,uBAAuB,EAAE;YACvB,IAAI,EAAE;cAAEiB,kBAAkB,EAAE;YAAS,CAAC;YACtC,KAAK,EAAE;cAAEA,kBAAkB,EAAE;YAAW,CAAC;YACzC,MAAM,EAAE;cAAEA,kBAAkB,EAAE;YAAS;UACzC,CAAC;UACD,wBAAwB,EAAE;YACxB,IAAI,EAAE;cAAEA,kBAAkB,EAAE;YAAS,CAAC;YACtC,KAAK,EAAE;cAAEA,kBAAkB,EAAE;YAAW,CAAC;YACzC,MAAM,EAAE;cAAEA,kBAAkB,EAAE;YAAS;UACzC,CAAC;UACD,wBAAwB,EAAE;YACxB,IAAI,EAAE;cAAEd,SAAS,EAAE,UAAU;cAAEe,OAAO,EAAE;YAAI,CAAC;YAC7C,KAAK,EAAE;cAAEf,SAAS,EAAE,aAAa;cAAEe,OAAO,EAAE;YAAI,CAAC;YACjD,MAAM,EAAE;cAAEf,SAAS,EAAE,UAAU;cAAEe,OAAO,EAAE;YAAI;UAChD;QACF,CAAE;QAAAd,QAAA,gBAGFvD,OAAA,CAAC5B,GAAG;UACFgE,EAAE,EAAE;YACFc,UAAU,EAAE,mDAAmD;YAC/DU,YAAY,EAAE,KAAK;YACnBvB,KAAK,EAAE,GAAG;YACViC,MAAM,EAAE,GAAG;YACX/B,OAAO,EAAE,MAAM;YACfG,UAAU,EAAE,QAAQ;YACpBD,cAAc,EAAE,QAAQ;YACxB8B,EAAE,EAAE,MAAM;YACVb,EAAE,EAAE,CAAC;YACLc,SAAS,EAAE,sCAAsC;YACjD5B,QAAQ,EAAE,UAAU;YACpB,WAAW,EAAE;cACXC,OAAO,EAAE,IAAI;cACbD,QAAQ,EAAE,UAAU;cACpBE,GAAG,EAAE,CAAC,CAAC;cACPC,IAAI,EAAE,CAAC,CAAC;cACRC,KAAK,EAAE,CAAC,CAAC;cACTC,MAAM,EAAE,CAAC,CAAC;cACVW,YAAY,EAAE,KAAK;cACnBV,UAAU,EAAE,4DAA4D;cACxEY,cAAc,EAAE,WAAW;cAC3BC,SAAS,EAAE,8BAA8B;cACzCM,OAAO,EAAE,GAAG;cACZlB,MAAM,EAAE,CAAC;YACX,CAAC;YACD,UAAU,EAAE;cACVN,OAAO,EAAE,IAAI;cACbD,QAAQ,EAAE,UAAU;cACpBE,GAAG,EAAE,CAAC,EAAE;cACRC,IAAI,EAAE,CAAC,EAAE;cACTC,KAAK,EAAE,CAAC,EAAE;cACVC,MAAM,EAAE,CAAC,EAAE;cACXW,YAAY,EAAE,KAAK;cACnBa,MAAM,EAAE,oCAAoC;cAC5CV,SAAS,EAAE;YACb;UACF,CAAE;UAAAR,QAAA,eAEFvD,OAAA,CAAChB,YAAY;YAACoD,EAAE,EAAE;cAAEsC,QAAQ,EAAE,EAAE;cAAEC,KAAK,EAAE;YAAQ;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnD,CAAC,eAGN/E,OAAA,CAAC3B,UAAU;UACT2G,OAAO,EAAC,IAAI;UACZC,SAAS,EAAC,IAAI;UACdC,YAAY;UACZC,UAAU,EAAC,MAAM;UACjB/C,EAAE,EAAE;YACFc,UAAU,EAAE,yFAAyF;YACrGY,cAAc,EAAE,WAAW;YAC3BsB,cAAc,EAAE,MAAM;YACtBC,oBAAoB,EAAE,MAAM;YAC5BC,mBAAmB,EAAE,aAAa;YAClCvB,SAAS,EAAE,8BAA8B;YACzCL,EAAE,EAAE,CAAC;YACLgB,QAAQ,EAAE;cAAEa,EAAE,EAAE,QAAQ;cAAEC,EAAE,EAAE;YAAO,CAAC;YACtCC,UAAU,EAAE,6GAA6G;YACzHC,MAAM,EAAE,kGAAkG;YAC1G9C,QAAQ,EAAE,UAAU;YACpB,WAAW,EAAE;cACXC,OAAO,EAAE,qBAAqB;cAC9BD,QAAQ,EAAE,UAAU;cACpBE,GAAG,EAAE,CAAC;cACNC,IAAI,EAAE,CAAC;cACPC,KAAK,EAAE,CAAC;cACRC,MAAM,EAAE,CAAC;cACTC,UAAU,EAAE,iLAAiL;cAC7LY,cAAc,EAAE,WAAW;cAC3BsB,cAAc,EAAE,MAAM;cACtBC,oBAAoB,EAAE,MAAM;cAC5BC,mBAAmB,EAAE,aAAa;cAClCvB,SAAS,EAAE,8BAA8B;cACzCZ,MAAM,EAAE,CAAC,CAAC;cACVuC,MAAM,EAAE,aAAa;cACrBrB,OAAO,EAAE;YACX,CAAC;YACD,UAAU,EAAE;cACVxB,OAAO,EAAE,qBAAqB;cAC9BD,QAAQ,EAAE,UAAU;cACpBE,GAAG,EAAE,CAAC;cACNC,IAAI,EAAE,CAAC;cACPC,KAAK,EAAE,CAAC;cACRC,MAAM,EAAE,CAAC;cACTC,UAAU,EAAE,yFAAyF;cACrGY,cAAc,EAAE,WAAW;cAC3BsB,cAAc,EAAE,MAAM;cACtBC,oBAAoB,EAAE,MAAM;cAC5BC,mBAAmB,EAAE,aAAa;cAClCvB,SAAS,EAAE,8BAA8B;cACzCZ,MAAM,EAAE,CAAC;cACTkB,OAAO,EAAE;YACX;UACF,CAAE;UAAAd,QAAA,EACH;QAED;UAAAqB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAEb/E,OAAA,CAAC3B,UAAU;UACT2G,OAAO,EAAC,IAAI;UACZL,KAAK,EAAC,gBAAgB;UACtBnB,QAAQ,EAAC,OAAO;UAChBe,EAAE,EAAC,MAAM;UACTnC,EAAE,EAAE;YACFsB,EAAE,EAAE,CAAC;YACLiC,UAAU,EAAE,GAAG;YACfR,UAAU,EAAE,GAAG;YACfT,QAAQ,EAAE;cAAEa,EAAE,EAAE,QAAQ;cAAEC,EAAE,EAAE;YAAS,CAAC;YACxCC,UAAU,EAAE,6DAA6D;YACzEd,KAAK,EAAE;UACT,CAAE;UAAApB,QAAA,EACH;QAGD;UAAAqB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAEb/E,OAAA,CAAC5B,GAAG;UACFgE,EAAE,EAAE;YACFG,OAAO,EAAE,MAAM;YACfE,cAAc,EAAE,QAAQ;YACxBmD,GAAG,EAAE,CAAC;YACNC,QAAQ,EAAE,MAAM;YAChBnC,EAAE,EAAE;UACN,CAAE;UAAAH,QAAA,EAED,CACC;YAAEuC,KAAK,EAAE,qBAAqB;YAAEC,QAAQ,EAAE;UAAoD,CAAC,EAC/F;YAAED,KAAK,EAAE,sBAAsB;YAAEC,QAAQ,EAAE;UAAoD,CAAC,EAChG;YAAED,KAAK,EAAE,oBAAoB;YAAEC,QAAQ,EAAE;UAAoD,CAAC,EAC9F;YAAED,KAAK,EAAE,qBAAqB;YAAEC,QAAQ,EAAE;UAAoD,CAAC,CAChG,CAACC,GAAG,CAAC,CAACC,OAAO,EAAEC,KAAK,kBACnBlG,OAAA,CAAClB,IAAI;YAEHgH,KAAK,EAAEG,OAAO,CAACH,KAAM;YACrBd,OAAO,EAAC,UAAU;YAClB5C,EAAE,EAAE;cACF+D,WAAW,EAAE,aAAa;cAC1BjD,UAAU,EAAE+C,OAAO,CAACF,QAAQ;cAC5BpB,KAAK,EAAE,OAAO;cACdQ,UAAU,EAAE,GAAG;cACfT,QAAQ,EAAE,MAAM;cAChBf,EAAE,EAAE,CAAC;cACLhB,EAAE,EAAE,CAAC;cACL2B,MAAM,EAAE,MAAM;cACdV,YAAY,EAAE,CAAC;cACfwC,UAAU,EAAE,uCAAuC;cACnD5B,SAAS,EAAE,iEAAiE;cAC5EL,cAAc,EAAE,YAAY;cAC5BM,MAAM,EAAE,oCAAoC;cAC5CgB,UAAU,EAAE,8BAA8B;cAC1C,SAAS,EAAE;gBACTnC,SAAS,EAAE,8BAA8B;gBACzCkB,SAAS,EAAE,kEAAkE;gBAC7EkB,MAAM,EAAE;cACV;YACF;UAAE,GAvBGQ,KAAK;YAAAtB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAwBX,CACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGN/E,OAAA,CAAC5B,GAAG;UAACmE,OAAO,EAAC,MAAM;UAACE,cAAc,EAAC,QAAQ;UAACmD,GAAG,EAAE,CAAE;UAACC,QAAQ,EAAC,MAAM;UAAAtC,QAAA,gBACjEvD,OAAA,CAACnB,MAAM;YACLmG,OAAO,EAAC,WAAW;YACnBqB,IAAI,EAAC,OAAO;YACZC,OAAO,EAAEvF,eAAgB,CAAC;YAAA;YAC1BqB,EAAE,EAAE;cACFuB,EAAE,EAAE,CAAC;cACLhB,EAAE,EAAE,CAAC;cACLiB,YAAY,EAAE,CAAC;cACfc,QAAQ,EAAE,QAAQ;cAClBS,UAAU,EAAE,GAAG;cACfjC,UAAU,EAAE,mDAAmD;cAC/DsB,SAAS,EAAE,qEAAqE;cAChFL,cAAc,EAAE,YAAY;cAC5BM,MAAM,EAAE,oCAAoC;cAC5CgB,UAAU,EAAE,8BAA8B;cAC1C,SAAS,EAAE;gBACTvC,UAAU,EAAE,mDAAmD;gBAC/DI,SAAS,EAAE,8BAA8B;gBACzCkB,SAAS,EAAE,sEAAsE;gBACjFkB,MAAM,EAAE;cACV;YACF,CAAE;YAAAnC,QAAA,EACH;UAED;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT/E,OAAA,CAACnB,MAAM;YACLmG,OAAO,EAAC,UAAU;YAClBqB,IAAI,EAAC,OAAO;YACZC,OAAO,EAAEA,CAAA,KAAM7F,QAAQ,CAAC,iBAAiB,CAAE;YAC3C2B,EAAE,EAAE;cACFuB,EAAE,EAAE,CAAC;cACLhB,EAAE,EAAE,CAAC;cACLiB,YAAY,EAAE,CAAC;cACfc,QAAQ,EAAE,QAAQ;cAClBS,UAAU,EAAE,GAAG;cACfgB,WAAW,EAAE,0BAA0B;cACvCxB,KAAK,EAAE,OAAO;cACd4B,WAAW,EAAE,CAAC;cACdpC,cAAc,EAAE,YAAY;cAC5BjB,UAAU,EAAE,2BAA2B;cACvCuC,UAAU,EAAE,8BAA8B;cAC1C,SAAS,EAAE;gBACTc,WAAW,EAAE,CAAC;gBACdJ,WAAW,EAAE,SAAS;gBACtBjD,UAAU,EAAE,oFAAoF;gBAChGI,SAAS,EAAE,8BAA8B;gBACzCkB,SAAS,EAAE,qEAAqE;gBAChFkB,MAAM,EAAE;cACV;YACF,CAAE;YAAAnC,QAAA,EACH;UAED;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN/E,OAAA,CAAC5B,GAAG;QAACiE,KAAK,EAAC,MAAM;QAACmB,QAAQ,EAAC,QAAQ;QAACe,EAAE,EAAC,MAAM;QAACiC,EAAE,EAAC,iBAAiB;QAAAjD,QAAA,eAEhEvD,OAAA,CAAC5B,GAAG;UAAAmF,QAAA,gBACFvD,OAAA,CAAC1B,IAAI;YACH8D,EAAE,EAAE;cACFsB,EAAE,EAAE,CAAC;cACLR,UAAU,EAAE,6GAA6G;cACzHiB,cAAc,EAAE,2BAA2B;cAC3CM,MAAM,EAAE,oCAAoC;cAC5Cb,YAAY,EAAE,CAAC;cACfC,QAAQ,EAAE,QAAQ;cAClBjB,QAAQ,EAAE,UAAU;cACpB4B,SAAS,EAAE,sCAAsC;cACjD4B,UAAU,EAAE,uCAAuC;cACnD,SAAS,EAAE;gBACT9C,SAAS,EAAE,kBAAkB;gBAC7BkB,SAAS,EAAE;cACb,CAAC;cACD,WAAW,EAAE;gBACX3B,OAAO,EAAE,IAAI;gBACbD,QAAQ,EAAE,UAAU;gBACpBE,GAAG,EAAE,CAAC;gBACNC,IAAI,EAAE,CAAC;gBACPC,KAAK,EAAE,CAAC;gBACRsB,MAAM,EAAE,KAAK;gBACbpB,UAAU,EAAE,yFAAyF;gBACrGY,cAAc,EAAE,WAAW;gBAC3BC,SAAS,EAAE;cACb,CAAC;cACD,0BAA0B,EAAE;gBAC1B,IAAI,EAAE;kBAAEK,kBAAkB,EAAE;gBAAS,CAAC;gBACtC,KAAK,EAAE;kBAAEA,kBAAkB,EAAE;gBAAW,CAAC;gBACzC,MAAM,EAAE;kBAAEA,kBAAkB,EAAE;gBAAS;cACzC;YACF,CAAE;YAAAb,QAAA,eAEFvD,OAAA,CAACzB,WAAW;cAAC6D,EAAE,EAAE;gBAAEqE,CAAC,EAAE;cAAE,CAAE;cAAAlD,QAAA,gBAEtBvD,OAAA,CAAC5B,GAAG;gBAACgE,EAAE,EAAE;kBAAEsE,YAAY,EAAE,CAAC;kBAAEP,WAAW,EAAE,SAAS;kBAAEzC,EAAE,EAAE;gBAAE,CAAE;gBAAAH,QAAA,eAC1DvD,OAAA,CAACxB,IAAI;kBACHmI,KAAK,EAAEtG,SAAU;kBACjBuG,QAAQ,EAAErF,eAAgB;kBAC1ByD,OAAO,EAAC,WAAW;kBACnB6B,SAAS,EAAC,SAAS;kBACnBC,cAAc,EAAC,SAAS;kBACxB1E,EAAE,EAAE;oBACF,gBAAgB,EAAE;sBAChBE,SAAS,EAAE,EAAE;sBACboC,QAAQ,EAAE,MAAM;sBAChBS,UAAU,EAAE,GAAG;sBACf4B,aAAa,EAAE;oBACjB;kBACF,CAAE;kBAAAxD,QAAA,gBAEFvD,OAAA,CAACvB,GAAG;oBACFuI,IAAI,eAAEhH,OAAA,CAACd,QAAQ;sBAACkD,EAAE,EAAE;wBAAEsC,QAAQ,EAAE;sBAAG;oBAAE;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAE;oBACzCe,KAAK,EAAC,sBAAsB;oBAC5BmB,YAAY,EAAC;kBAAO;oBAAArC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrB,CAAC,eACF/E,OAAA,CAACvB,GAAG;oBACFuI,IAAI,eAAEhH,OAAA,CAACZ,QAAQ;sBAACgD,EAAE,EAAE;wBAAEsC,QAAQ,EAAE;sBAAG;oBAAE;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAE;oBACzCe,KAAK,EAAC,uBAAuB;oBAC7BmB,YAAY,EAAC;kBAAO;oBAAArC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrB,CAAC,eACF/E,OAAA,CAACvB,GAAG;oBACFuI,IAAI,eAAEhH,OAAA,CAAChB,YAAY;sBAACoD,EAAE,EAAE;wBAAEsC,QAAQ,EAAE;sBAAG;oBAAE;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAE;oBAC7Ce,KAAK,EAAC,iBAAiB;oBACvBmB,YAAY,EAAC;kBAAO;oBAAArC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrB,CAAC,eACF/E,OAAA,CAACvB,GAAG;oBACFuI,IAAI,eAAEhH,OAAA,CAACV,cAAc;sBAAC8C,EAAE,EAAE;wBAAEsC,QAAQ,EAAE;sBAAG;oBAAE;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAE;oBAC/Ce,KAAK,EAAC,mBAAmB;oBACzBmB,YAAY,EAAC;kBAAO;oBAAArC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eAGN/E,OAAA,CAAC5B,GAAG;gBAAAmF,QAAA,GACDlD,SAAS,KAAK,CAAC,iBACdL,OAAA,CAACpB,IAAI;kBAACsI,EAAE,EAAE7G,SAAS,KAAK,CAAE;kBAAC8G,OAAO,EAAE,GAAI;kBAAA5D,QAAA,eACtCvD,OAAA,CAAC5B,GAAG;oBAAAmF,QAAA,eACFvD,OAAA,CAACP,WAAW;sBAAAmF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACZ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CACP,EAEA1E,SAAS,KAAK,CAAC,iBACdL,OAAA,CAACpB,IAAI;kBAACsI,EAAE,EAAE7G,SAAS,KAAK,CAAE;kBAAC8G,OAAO,EAAE,GAAI;kBAAA5D,QAAA,eACtCvD,OAAA,CAAC5B,GAAG;oBAAAmF,QAAA,eACFvD,OAAA,CAACN,YAAY;sBAAAkF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACb;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CACP,EAEA1E,SAAS,KAAK,CAAC,iBACdL,OAAA,CAACpB,IAAI;kBAACsI,EAAE,EAAE7G,SAAS,KAAK,CAAE;kBAAC8G,OAAO,EAAE,GAAI;kBAAA5D,QAAA,eACtCvD,OAAA,CAAC5B,GAAG;oBAAAmF,QAAA,eACFvD,OAAA,CAACJ,cAAc;sBAAAgF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CACP,EAEA1E,SAAS,KAAK,CAAC,iBACdL,OAAA,CAACpB,IAAI;kBAACsI,EAAE,EAAE7G,SAAS,KAAK,CAAE;kBAAC8G,OAAO,EAAE,GAAI;kBAAA5D,QAAA,eACtCvD,OAAA,CAAC5B,GAAG;oBAAAmF,QAAA,eACFvD,OAAA,CAACH,gBAAgB;sBAAA+E,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CACP;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,EAGN,CAACrE,WAAW,IAAIC,UAAU,kBACzBX,OAAA,CAACpB,IAAI;YAACsI,EAAE,EAAE,IAAK;YAACC,OAAO,EAAE,GAAI;YAAA5D,QAAA,eAC3BvD,OAAA,CAAC5B,GAAG;cAAAmF,QAAA,eACFvD,OAAA,CAACL,UAAU;gBACTkC,UAAU,EAAEnB,WAAY;gBACxB0G,gBAAgB,EAAExF,oBAAqB;gBACvCyF,aAAa,EAAEnF;cAAkB;gBAAA0C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CACP;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAOE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC,EAGXlE,aAAa,CAACmF,GAAG,CAAEzF,YAAY,iBAC9BP,OAAA,CAACrB,QAAQ;MAEP2I,IAAI,EAAE,IAAK;MACXC,gBAAgB,EAAE,IAAK;MACvBC,OAAO,EAAEA,CAAA,KAAM9F,uBAAuB,CAACnB,YAAY,CAACiG,EAAE,CAAE;MACxDiB,YAAY,EAAE;QAAEC,QAAQ,EAAE,QAAQ;QAAEC,UAAU,EAAE;MAAQ,CAAE;MAAApE,QAAA,eAE1DvD,OAAA,CAACtB,KAAK;QACJ8I,OAAO,EAAEA,CAAA,KAAM9F,uBAAuB,CAACnB,YAAY,CAACiG,EAAE,CAAE;QACxDoB,QAAQ,EAAErH,YAAY,CAACyB,IAAK;QAC5BgD,OAAO,EAAC,QAAQ;QAChB5C,EAAE,EAAE;UACFC,KAAK,EAAE,MAAM;UACbuB,YAAY,EAAE,CAAC;UACfY,SAAS,EAAE;QACb,CAAE;QAAAjB,QAAA,gBAEFvD,OAAA,CAAC3B,UAAU;UAAC2G,OAAO,EAAC,WAAW;UAACG,UAAU,EAAC,MAAM;UAAA5B,QAAA,EAC9ChD,YAAY,CAACsH;QAAK;UAAAjD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,EACZxE,YAAY,CAAC0B,OAAO,iBACnBjC,OAAA,CAAC3B,UAAU;UAAC2G,OAAO,EAAC,OAAO;UAAAzB,QAAA,EACxBhD,YAAY,CAAC0B;QAAO;UAAA2C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CACb;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC,GAxBHxE,YAAY,CAACiG,EAAE;MAAA5B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAyBZ,CACX,CAAC,EAGDxE,YAAY,iBACXP,OAAA,CAACrB,QAAQ;MACP2I,IAAI,EAAE,IAAK;MACXC,gBAAgB,EAAE,IAAK;MACvBC,OAAO,EAAEA,CAAA,KAAMhH,eAAe,CAAC,IAAI,CAAE;MACrCiH,YAAY,EAAE;QAAEC,QAAQ,EAAE,QAAQ;QAAEC,UAAU,EAAE;MAAS,CAAE;MAAApE,QAAA,eAE3DvD,OAAA,CAACtB,KAAK;QACJ8I,OAAO,EAAEA,CAAA,KAAMhH,eAAe,CAAC,IAAI,CAAE;QACrCoH,QAAQ,EAAErH,YAAY,CAACyB,IAAK;QAC5BgD,OAAO,EAAC,QAAQ;QAChB5C,EAAE,EAAE;UACFwB,YAAY,EAAE,CAAC;UACfY,SAAS,EAAE;QACb,CAAE;QAAAjB,QAAA,EAEDhD,YAAY,CAAC0B;MAAO;QAAA2C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CACX;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;EAAA,QAliBkBxF,WAAW,EAQxBC,OAAO;AAAA,EA0hBZ,CAAC;EAAA,QAliBiBD,WAAW,EAQxBC,OAAO;AAAA,EA0hBX;AAACsI,GAAA,GAriBG7H,QAAQ;AAuiBdA,QAAQ,CAAC8H,WAAW,GAAG,UAAU;AAEjC,eAAe9H,QAAQ;AAAC,IAAAG,EAAA,EAAA0H,GAAA;AAAAE,YAAA,CAAA5H,EAAA;AAAA4H,YAAA,CAAAF,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}