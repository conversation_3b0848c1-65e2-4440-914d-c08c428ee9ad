{"name": "express-rate-limit", "version": "8.0.1", "description": "Basic IP rate-limiting middleware for Express. Use to limit repeated requests to public APIs and/or endpoints such as password reset.", "author": {"name": "<PERSON>", "url": "http://nfriedly.com/"}, "license": "MIT", "homepage": "https://github.com/express-rate-limit/express-rate-limit", "repository": {"type": "git", "url": "git+https://github.com/express-rate-limit/express-rate-limit.git"}, "funding": "https://github.com/sponsors/express-rate-limit", "keywords": ["express-rate-limit", "express", "rate", "limit", "ratelimit", "rate-limit", "middleware", "ip", "auth", "authorization", "security", "brute", "force", "bruteforce", "brute-force", "attack"], "type": "module", "exports": {".": {"import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}}}, "main": "./dist/index.cjs", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "files": ["dist/", "tsconfig.json"], "engines": {"node": ">= 16"}, "scripts": {"clean": "del-cli dist/ coverage/ *.log *.tmp *.bak *.tgz", "build:cjs": "esbuild --packages=external --platform=node --bundle --target=es2022 --format=cjs --outfile=dist/index.cjs --footer:js=\"module.exports = Object.assign(rateLimit, module.exports);\" source/index.ts", "build:esm": "esbuild --packages=external --platform=node --bundle --target=es2022 --format=esm --outfile=dist/index.mjs source/index.ts", "build:types": "dts-bundle-generator --out-file=dist/index.d.ts source/index.ts && cp dist/index.d.ts dist/index.d.cts && cp dist/index.d.ts dist/index.d.mts", "compile": "run-s clean build:*", "docs": "cd docs && mintlify dev", "lint:code": "biome check", "lint:docs": "prettier --check docs/ *.md", "lint": "run-s lint:*", "format:code": "biome check --write", "format:docs": "prettier --write docs/ *.md", "format": "run-s format:*", "test:lib": "jest", "test:ext": "cd test/external/ && bash run-all-tests", "test": "run-s lint test:lib", "pre-commit": "lint-staged", "prepare": "run-s compile && husky"}, "peerDependencies": {"express": ">= 4.11"}, "devDependencies": {"@biomejs/biome": "2.1.1", "@express-rate-limit/prettier": "1.1.1", "@express-rate-limit/tsconfig": "1.0.2", "@jest/globals": "30.0.4", "@types/express": "5.0.3", "@types/jest": "30.0.0", "@types/node": "24.0.14", "@types/supertest": "6.0.3", "del-cli": "6.0.0", "dts-bundle-generator": "8.0.1", "esbuild": "0.25.6", "express": "5.1.0", "husky": "9.1.7", "jest": "30.0.4", "lint-staged": "16.1.2", "mintlify": "4.2.15", "npm-run-all": "4.1.5", "prettier": "3.6.2", "ratelimit-header-parser": "0.1.0", "supertest": "7.1.3", "ts-jest": "29.4.0", "ts-node": "10.9.2", "typescript": "5.8.3"}, "prettier": "@express-rate-limit/prettier", "lint-staged": {"*.{js,ts,json}": "biome check --write", "*.{md,yaml}": "prettier --write"}, "dependencies": {"ip-address": "10.0.1"}}