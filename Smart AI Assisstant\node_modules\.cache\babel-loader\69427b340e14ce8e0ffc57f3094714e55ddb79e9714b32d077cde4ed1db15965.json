{"ast": null, "code": "var _jsxFileName = \"E:\\\\PythonProjects\\\\React.js\\\\Smart AI Assisstant\\\\src\\\\components\\\\NotificationDetailDialog.jsx\";\nimport React from 'react';\nimport { Dialog, DialogTitle, DialogContent, DialogActions, Button, Typography, Box, Chip, IconButton, Divider, List, ListItem, ListItemIcon, ListItemText, Card, CardContent } from '@mui/material';\nimport { Close as CloseIcon, Security as SecurityIcon, Warning as WarningIcon, Info as InfoIcon, CheckCircle as CheckCircleIcon, Error as ErrorIcon, Schedule as ScheduleIcon, Person as PersonIcon, Computer as ComputerIcon, LocationOn as LocationIcon } from '@mui/icons-material';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst NotificationDetailDialog = ({\n  open,\n  onClose,\n  notification\n}) => {\n  if (!notification) return null;\n  const getTypeIcon = type => {\n    switch (type) {\n      case 'success':\n        return /*#__PURE__*/_jsxDEV(CheckCircleIcon, {\n          sx: {\n            color: '#4caf50'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 16\n        }, this);\n      case 'warning':\n        return /*#__PURE__*/_jsxDEV(WarningIcon, {\n          sx: {\n            color: '#ff9800'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 41,\n          columnNumber: 16\n        }, this);\n      case 'error':\n        return /*#__PURE__*/_jsxDEV(ErrorIcon, {\n          sx: {\n            color: '#f44336'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 43,\n          columnNumber: 16\n        }, this);\n      case 'security':\n        return /*#__PURE__*/_jsxDEV(SecurityIcon, {\n          sx: {\n            color: '#2196f3'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 45,\n          columnNumber: 16\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(InfoIcon, {\n          sx: {\n            color: '#2196f3'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 47,\n          columnNumber: 16\n        }, this);\n    }\n  };\n  const getTypeColor = type => {\n    switch (type) {\n      case 'success':\n        return '#4caf50';\n      case 'warning':\n        return '#ff9800';\n      case 'error':\n        return '#f44336';\n      case 'security':\n        return '#2196f3';\n      default:\n        return '#2196f3';\n    }\n  };\n  const mockDetailedInfo = {\n    timestamp: notification.timestamp,\n    source: 'AI Security Guard System',\n    severity: notification.type === 'error' ? 'High' : notification.type === 'warning' ? 'Medium' : 'Low',\n    category: notification.type === 'security' ? 'Security Alert' : 'System Notification',\n    affectedSystems: ['Web Scanner', 'Threat Detection Engine'],\n    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',\n    ipAddress: '*************',\n    location: 'New York, USA',\n    actions: ['View detailed report', 'Mark as resolved', 'Configure alerts', 'Contact support']\n  };\n  return /*#__PURE__*/_jsxDEV(Dialog, {\n    open: open,\n    onClose: onClose,\n    maxWidth: \"md\",\n    fullWidth: true,\n    PaperProps: {\n      sx: {\n        borderRadius: 4,\n        background: theme => theme.palette.mode === 'dark' ? 'linear-gradient(135deg, rgba(12, 12, 12, 0.98) 0%, rgba(26, 26, 26, 0.98) 100%)' : 'linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(248, 250, 252, 0.98) 100%)',\n        backdropFilter: 'blur(25px)',\n        border: '1px solid rgba(102, 126, 234, 0.2)',\n        boxShadow: '0 25px 80px rgba(0, 0, 0, 0.3)'\n      }\n    },\n    children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n      sx: {\n        p: 0\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        display: \"flex\",\n        alignItems: \"center\",\n        justifyContent: \"space-between\",\n        sx: {\n          p: 3,\n          background: `linear-gradient(135deg, ${getTypeColor(notification.type)}20 0%, ${getTypeColor(notification.type)}10 100%)`,\n          borderBottom: '1px solid',\n          borderColor: 'divider'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          display: \"flex\",\n          alignItems: \"center\",\n          gap: 2,\n          children: [getTypeIcon(notification.type), /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h5\",\n              fontWeight: \"bold\",\n              children: \"Notification Details\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 116,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: mockDetailedInfo.category\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 119,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 115,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 113,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: onClose,\n          sx: {\n            background: 'rgba(255, 255, 255, 0.1)',\n            backdropFilter: 'blur(10px)',\n            '&:hover': {\n              background: 'rgba(255, 255, 255, 0.2)'\n            }\n          },\n          children: /*#__PURE__*/_jsxDEV(CloseIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 135,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 125,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 102,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 101,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n      sx: {\n        p: 0\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          p: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Card, {\n          sx: {\n            mb: 3,\n            border: `1px solid ${getTypeColor(notification.type)}40`\n          },\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              display: \"flex\",\n              alignItems: \"center\",\n              gap: 2,\n              mb: 2,\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                fontWeight: \"bold\",\n                children: notification.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 146,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                label: notification.type.toUpperCase(),\n                size: \"small\",\n                sx: {\n                  background: getTypeColor(notification.type),\n                  color: 'white',\n                  fontWeight: 'bold'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 149,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                label: mockDetailedInfo.severity,\n                size: \"small\",\n                color: mockDetailedInfo.severity === 'High' ? 'error' : mockDetailedInfo.severity === 'Medium' ? 'warning' : 'success',\n                variant: \"outlined\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 158,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 145,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              sx: {\n                mb: 2,\n                lineHeight: 1.6\n              },\n              children: notification.message\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 166,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: \"This notification was generated by our AI-powered security monitoring system to alert you about important events and potential security concerns.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 170,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 144,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 143,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          fontWeight: \"bold\",\n          sx: {\n            mb: 2\n          },\n          children: \"Detailed Information\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 178,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(List, {\n          sx: {\n            mb: 3\n          },\n          children: [/*#__PURE__*/_jsxDEV(ListItem, {\n            children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n              children: /*#__PURE__*/_jsxDEV(ScheduleIcon, {\n                color: \"primary\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 185,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 184,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: \"Timestamp\",\n              secondary: new Date(notification.timestamp).toLocaleString()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 187,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 183,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n            children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n              children: /*#__PURE__*/_jsxDEV(ComputerIcon, {\n                color: \"primary\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 195,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 194,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: \"Source\",\n              secondary: mockDetailedInfo.source\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 197,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 193,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n            children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n              children: /*#__PURE__*/_jsxDEV(PersonIcon, {\n                color: \"primary\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 205,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 204,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: \"User Agent\",\n              secondary: mockDetailedInfo.userAgent\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 207,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 203,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n            children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n              children: /*#__PURE__*/_jsxDEV(LocationIcon, {\n                color: \"primary\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 215,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 214,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: \"Location\",\n              secondary: `${mockDetailedInfo.ipAddress} - ${mockDetailedInfo.location}`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 217,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 182,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Divider, {\n          sx: {\n            my: 3\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 224,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          fontWeight: \"bold\",\n          sx: {\n            mb: 2\n          },\n          children: \"Affected Systems\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 227,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          display: \"flex\",\n          gap: 1,\n          flexWrap: \"wrap\",\n          mb: 3,\n          children: mockDetailedInfo.affectedSystems.map((system, index) => /*#__PURE__*/_jsxDEV(Chip, {\n            label: system,\n            variant: \"outlined\",\n            color: \"primary\",\n            size: \"small\"\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 233,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 231,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Divider, {\n          sx: {\n            my: 3\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 243,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          fontWeight: \"bold\",\n          sx: {\n            mb: 2\n          },\n          children: \"Recommended Actions\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 246,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          display: \"flex\",\n          gap: 2,\n          flexWrap: \"wrap\",\n          children: mockDetailedInfo.actions.map((action, index) => /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outlined\",\n            size: \"small\",\n            sx: {\n              borderRadius: 3,\n              borderColor: getTypeColor(notification.type),\n              color: getTypeColor(notification.type),\n              '&:hover': {\n                background: `${getTypeColor(notification.type)}10`,\n                borderColor: getTypeColor(notification.type)\n              }\n            },\n            children: action\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 252,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 250,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 141,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 140,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n      sx: {\n        p: 3,\n        borderTop: '1px solid',\n        borderColor: 'divider'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        onClick: onClose,\n        variant: \"outlined\",\n        sx: {\n          borderRadius: 3\n        },\n        children: \"Close\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 274,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        sx: {\n          borderRadius: 3,\n          background: `linear-gradient(135deg, ${getTypeColor(notification.type)} 0%, ${getTypeColor(notification.type)}CC 100%)`,\n          '&:hover': {\n            background: `linear-gradient(135deg, ${getTypeColor(notification.type)}DD 0%, ${getTypeColor(notification.type)}AA 100%)`\n          }\n        },\n        children: \"Mark as Resolved\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 281,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 273,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 84,\n    columnNumber: 5\n  }, this);\n};\n_c = NotificationDetailDialog;\nexport default NotificationDetailDialog;\nvar _c;\n$RefreshReg$(_c, \"NotificationDetailDialog\");", "map": {"version": 3, "names": ["React", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "<PERSON><PERSON>", "Typography", "Box", "Chip", "IconButton", "Divider", "List", "ListItem", "ListItemIcon", "ListItemText", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Close", "CloseIcon", "Security", "SecurityIcon", "Warning", "WarningIcon", "Info", "InfoIcon", "CheckCircle", "CheckCircleIcon", "Error", "ErrorIcon", "Schedule", "ScheduleIcon", "Person", "PersonIcon", "Computer", "ComputerIcon", "LocationOn", "LocationIcon", "jsxDEV", "_jsxDEV", "NotificationDetailDialog", "open", "onClose", "notification", "getTypeIcon", "type", "sx", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "getTypeColor", "mockDetailedInfo", "timestamp", "source", "severity", "category", "affectedSystems", "userAgent", "ip<PERSON><PERSON><PERSON>", "location", "actions", "max<PERSON><PERSON><PERSON>", "fullWidth", "PaperProps", "borderRadius", "background", "theme", "palette", "mode", "<PERSON><PERSON>ilter", "border", "boxShadow", "children", "p", "display", "alignItems", "justifyContent", "borderBottom", "borderColor", "gap", "variant", "fontWeight", "onClick", "mb", "title", "label", "toUpperCase", "size", "lineHeight", "message", "primary", "secondary", "Date", "toLocaleString", "my", "flexWrap", "map", "system", "index", "action", "borderTop", "_c", "$RefreshReg$"], "sources": ["E:/PythonProjects/React.js/Smart AI Assisstant/src/components/NotificationDetailDialog.jsx"], "sourcesContent": ["import React from 'react';\nimport {\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  Button,\n  Typography,\n  Box,\n  Chip,\n  IconButton,\n  Divider,\n  List,\n  ListItem,\n  ListItemIcon,\n  ListItemText,\n  Card,\n  CardContent,\n} from '@mui/material';\nimport {\n  Close as CloseIcon,\n  Security as SecurityIcon,\n  Warning as WarningIcon,\n  Info as InfoIcon,\n  CheckCircle as CheckCircleIcon,\n  Error as ErrorIcon,\n  Schedule as ScheduleIcon,\n  Person as PersonIcon,\n  Computer as ComputerIcon,\n  LocationOn as LocationIcon,\n} from '@mui/icons-material';\n\nconst NotificationDetailDialog = ({ open, onClose, notification }) => {\n  if (!notification) return null;\n\n  const getTypeIcon = (type) => {\n    switch (type) {\n      case 'success':\n        return <CheckCircleIcon sx={{ color: '#4caf50' }} />;\n      case 'warning':\n        return <WarningIcon sx={{ color: '#ff9800' }} />;\n      case 'error':\n        return <ErrorIcon sx={{ color: '#f44336' }} />;\n      case 'security':\n        return <SecurityIcon sx={{ color: '#2196f3' }} />;\n      default:\n        return <InfoIcon sx={{ color: '#2196f3' }} />;\n    }\n  };\n\n  const getTypeColor = (type) => {\n    switch (type) {\n      case 'success':\n        return '#4caf50';\n      case 'warning':\n        return '#ff9800';\n      case 'error':\n        return '#f44336';\n      case 'security':\n        return '#2196f3';\n      default:\n        return '#2196f3';\n    }\n  };\n\n  const mockDetailedInfo = {\n    timestamp: notification.timestamp,\n    source: 'AI Security Guard System',\n    severity: notification.type === 'error' ? 'High' : notification.type === 'warning' ? 'Medium' : 'Low',\n    category: notification.type === 'security' ? 'Security Alert' : 'System Notification',\n    affectedSystems: ['Web Scanner', 'Threat Detection Engine'],\n    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',\n    ipAddress: '*************',\n    location: 'New York, USA',\n    actions: [\n      'View detailed report',\n      'Mark as resolved',\n      'Configure alerts',\n      'Contact support'\n    ]\n  };\n\n  return (\n    <Dialog\n      open={open}\n      onClose={onClose}\n      maxWidth=\"md\"\n      fullWidth\n      PaperProps={{\n        sx: {\n          borderRadius: 4,\n          background: (theme) => theme.palette.mode === 'dark'\n            ? 'linear-gradient(135deg, rgba(12, 12, 12, 0.98) 0%, rgba(26, 26, 26, 0.98) 100%)'\n            : 'linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(248, 250, 252, 0.98) 100%)',\n          backdropFilter: 'blur(25px)',\n          border: '1px solid rgba(102, 126, 234, 0.2)',\n          boxShadow: '0 25px 80px rgba(0, 0, 0, 0.3)',\n        },\n      }}\n    >\n      <DialogTitle sx={{ p: 0 }}>\n        <Box\n          display=\"flex\"\n          alignItems=\"center\"\n          justifyContent=\"space-between\"\n          sx={{\n            p: 3,\n            background: `linear-gradient(135deg, ${getTypeColor(notification.type)}20 0%, ${getTypeColor(notification.type)}10 100%)`,\n            borderBottom: '1px solid',\n            borderColor: 'divider',\n          }}\n        >\n          <Box display=\"flex\" alignItems=\"center\" gap={2}>\n            {getTypeIcon(notification.type)}\n            <Box>\n              <Typography variant=\"h5\" fontWeight=\"bold\">\n                Notification Details\n              </Typography>\n              <Typography variant=\"body2\" color=\"text.secondary\">\n                {mockDetailedInfo.category}\n              </Typography>\n            </Box>\n          </Box>\n          \n          <IconButton\n            onClick={onClose}\n            sx={{\n              background: 'rgba(255, 255, 255, 0.1)',\n              backdropFilter: 'blur(10px)',\n              '&:hover': {\n                background: 'rgba(255, 255, 255, 0.2)',\n              },\n            }}\n          >\n            <CloseIcon />\n          </IconButton>\n        </Box>\n      </DialogTitle>\n\n      <DialogContent sx={{ p: 0 }}>\n        <Box sx={{ p: 3 }}>\n          {/* Main Message */}\n          <Card sx={{ mb: 3, border: `1px solid ${getTypeColor(notification.type)}40` }}>\n            <CardContent>\n              <Box display=\"flex\" alignItems=\"center\" gap={2} mb={2}>\n                <Typography variant=\"h6\" fontWeight=\"bold\">\n                  {notification.title}\n                </Typography>\n                <Chip\n                  label={notification.type.toUpperCase()}\n                  size=\"small\"\n                  sx={{\n                    background: getTypeColor(notification.type),\n                    color: 'white',\n                    fontWeight: 'bold',\n                  }}\n                />\n                <Chip\n                  label={mockDetailedInfo.severity}\n                  size=\"small\"\n                  color={mockDetailedInfo.severity === 'High' ? 'error' : mockDetailedInfo.severity === 'Medium' ? 'warning' : 'success'}\n                  variant=\"outlined\"\n                />\n              </Box>\n              \n              <Typography variant=\"body1\" sx={{ mb: 2, lineHeight: 1.6 }}>\n                {notification.message}\n              </Typography>\n              \n              <Typography variant=\"body2\" color=\"text.secondary\">\n                This notification was generated by our AI-powered security monitoring system \n                to alert you about important events and potential security concerns.\n              </Typography>\n            </CardContent>\n          </Card>\n\n          {/* Detailed Information */}\n          <Typography variant=\"h6\" fontWeight=\"bold\" sx={{ mb: 2 }}>\n            Detailed Information\n          </Typography>\n          \n          <List sx={{ mb: 3 }}>\n            <ListItem>\n              <ListItemIcon>\n                <ScheduleIcon color=\"primary\" />\n              </ListItemIcon>\n              <ListItemText\n                primary=\"Timestamp\"\n                secondary={new Date(notification.timestamp).toLocaleString()}\n              />\n            </ListItem>\n            \n            <ListItem>\n              <ListItemIcon>\n                <ComputerIcon color=\"primary\" />\n              </ListItemIcon>\n              <ListItemText\n                primary=\"Source\"\n                secondary={mockDetailedInfo.source}\n              />\n            </ListItem>\n            \n            <ListItem>\n              <ListItemIcon>\n                <PersonIcon color=\"primary\" />\n              </ListItemIcon>\n              <ListItemText\n                primary=\"User Agent\"\n                secondary={mockDetailedInfo.userAgent}\n              />\n            </ListItem>\n            \n            <ListItem>\n              <ListItemIcon>\n                <LocationIcon color=\"primary\" />\n              </ListItemIcon>\n              <ListItemText\n                primary=\"Location\"\n                secondary={`${mockDetailedInfo.ipAddress} - ${mockDetailedInfo.location}`}\n              />\n            </ListItem>\n          </List>\n\n          <Divider sx={{ my: 3 }} />\n\n          {/* Affected Systems */}\n          <Typography variant=\"h6\" fontWeight=\"bold\" sx={{ mb: 2 }}>\n            Affected Systems\n          </Typography>\n          \n          <Box display=\"flex\" gap={1} flexWrap=\"wrap\" mb={3}>\n            {mockDetailedInfo.affectedSystems.map((system, index) => (\n              <Chip\n                key={index}\n                label={system}\n                variant=\"outlined\"\n                color=\"primary\"\n                size=\"small\"\n              />\n            ))}\n          </Box>\n\n          <Divider sx={{ my: 3 }} />\n\n          {/* Recommended Actions */}\n          <Typography variant=\"h6\" fontWeight=\"bold\" sx={{ mb: 2 }}>\n            Recommended Actions\n          </Typography>\n          \n          <Box display=\"flex\" gap={2} flexWrap=\"wrap\">\n            {mockDetailedInfo.actions.map((action, index) => (\n              <Button\n                key={index}\n                variant=\"outlined\"\n                size=\"small\"\n                sx={{\n                  borderRadius: 3,\n                  borderColor: getTypeColor(notification.type),\n                  color: getTypeColor(notification.type),\n                  '&:hover': {\n                    background: `${getTypeColor(notification.type)}10`,\n                    borderColor: getTypeColor(notification.type),\n                  },\n                }}\n              >\n                {action}\n              </Button>\n            ))}\n          </Box>\n        </Box>\n      </DialogContent>\n\n      <DialogActions sx={{ p: 3, borderTop: '1px solid', borderColor: 'divider' }}>\n        <Button\n          onClick={onClose}\n          variant=\"outlined\"\n          sx={{ borderRadius: 3 }}\n        >\n          Close\n        </Button>\n        <Button\n          variant=\"contained\"\n          sx={{\n            borderRadius: 3,\n            background: `linear-gradient(135deg, ${getTypeColor(notification.type)} 0%, ${getTypeColor(notification.type)}CC 100%)`,\n            '&:hover': {\n              background: `linear-gradient(135deg, ${getTypeColor(notification.type)}DD 0%, ${getTypeColor(notification.type)}AA 100%)`,\n            },\n          }}\n        >\n          Mark as Resolved\n        </Button>\n      </DialogActions>\n    </Dialog>\n  );\n};\n\nexport default NotificationDetailDialog;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SACEC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,MAAM,EACNC,UAAU,EACVC,GAAG,EACHC,IAAI,EACJC,UAAU,EACVC,OAAO,EACPC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,YAAY,EACZC,IAAI,EACJC,WAAW,QACN,eAAe;AACtB,SACEC,KAAK,IAAIC,SAAS,EAClBC,QAAQ,IAAIC,YAAY,EACxBC,OAAO,IAAIC,WAAW,EACtBC,IAAI,IAAIC,QAAQ,EAChBC,WAAW,IAAIC,eAAe,EAC9BC,KAAK,IAAIC,SAAS,EAClBC,QAAQ,IAAIC,YAAY,EACxBC,MAAM,IAAIC,UAAU,EACpBC,QAAQ,IAAIC,YAAY,EACxBC,UAAU,IAAIC,YAAY,QACrB,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7B,MAAMC,wBAAwB,GAAGA,CAAC;EAAEC,IAAI;EAAEC,OAAO;EAAEC;AAAa,CAAC,KAAK;EACpE,IAAI,CAACA,YAAY,EAAE,OAAO,IAAI;EAE9B,MAAMC,WAAW,GAAIC,IAAI,IAAK;IAC5B,QAAQA,IAAI;MACV,KAAK,SAAS;QACZ,oBAAON,OAAA,CAACZ,eAAe;UAACmB,EAAE,EAAE;YAAEC,KAAK,EAAE;UAAU;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACtD,KAAK,SAAS;QACZ,oBAAOZ,OAAA,CAAChB,WAAW;UAACuB,EAAE,EAAE;YAAEC,KAAK,EAAE;UAAU;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAClD,KAAK,OAAO;QACV,oBAAOZ,OAAA,CAACV,SAAS;UAACiB,EAAE,EAAE;YAAEC,KAAK,EAAE;UAAU;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAChD,KAAK,UAAU;QACb,oBAAOZ,OAAA,CAAClB,YAAY;UAACyB,EAAE,EAAE;YAAEC,KAAK,EAAE;UAAU;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACnD;QACE,oBAAOZ,OAAA,CAACd,QAAQ;UAACqB,EAAE,EAAE;YAAEC,KAAK,EAAE;UAAU;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;IACjD;EACF,CAAC;EAED,MAAMC,YAAY,GAAIP,IAAI,IAAK;IAC7B,QAAQA,IAAI;MACV,KAAK,SAAS;QACZ,OAAO,SAAS;MAClB,KAAK,SAAS;QACZ,OAAO,SAAS;MAClB,KAAK,OAAO;QACV,OAAO,SAAS;MAClB,KAAK,UAAU;QACb,OAAO,SAAS;MAClB;QACE,OAAO,SAAS;IACpB;EACF,CAAC;EAED,MAAMQ,gBAAgB,GAAG;IACvBC,SAAS,EAAEX,YAAY,CAACW,SAAS;IACjCC,MAAM,EAAE,0BAA0B;IAClCC,QAAQ,EAAEb,YAAY,CAACE,IAAI,KAAK,OAAO,GAAG,MAAM,GAAGF,YAAY,CAACE,IAAI,KAAK,SAAS,GAAG,QAAQ,GAAG,KAAK;IACrGY,QAAQ,EAAEd,YAAY,CAACE,IAAI,KAAK,UAAU,GAAG,gBAAgB,GAAG,qBAAqB;IACrFa,eAAe,EAAE,CAAC,aAAa,EAAE,yBAAyB,CAAC;IAC3DC,SAAS,EAAE,8DAA8D;IACzEC,SAAS,EAAE,eAAe;IAC1BC,QAAQ,EAAE,eAAe;IACzBC,OAAO,EAAE,CACP,sBAAsB,EACtB,kBAAkB,EAClB,kBAAkB,EAClB,iBAAiB;EAErB,CAAC;EAED,oBACEvB,OAAA,CAACrC,MAAM;IACLuC,IAAI,EAAEA,IAAK;IACXC,OAAO,EAAEA,OAAQ;IACjBqB,QAAQ,EAAC,IAAI;IACbC,SAAS;IACTC,UAAU,EAAE;MACVnB,EAAE,EAAE;QACFoB,YAAY,EAAE,CAAC;QACfC,UAAU,EAAGC,KAAK,IAAKA,KAAK,CAACC,OAAO,CAACC,IAAI,KAAK,MAAM,GAChD,iFAAiF,GACjF,uFAAuF;QAC3FC,cAAc,EAAE,YAAY;QAC5BC,MAAM,EAAE,oCAAoC;QAC5CC,SAAS,EAAE;MACb;IACF,CAAE;IAAAC,QAAA,gBAEFnC,OAAA,CAACpC,WAAW;MAAC2C,EAAE,EAAE;QAAE6B,CAAC,EAAE;MAAE,CAAE;MAAAD,QAAA,eACxBnC,OAAA,CAAC/B,GAAG;QACFoE,OAAO,EAAC,MAAM;QACdC,UAAU,EAAC,QAAQ;QACnBC,cAAc,EAAC,eAAe;QAC9BhC,EAAE,EAAE;UACF6B,CAAC,EAAE,CAAC;UACJR,UAAU,EAAE,2BAA2Bf,YAAY,CAACT,YAAY,CAACE,IAAI,CAAC,UAAUO,YAAY,CAACT,YAAY,CAACE,IAAI,CAAC,UAAU;UACzHkC,YAAY,EAAE,WAAW;UACzBC,WAAW,EAAE;QACf,CAAE;QAAAN,QAAA,gBAEFnC,OAAA,CAAC/B,GAAG;UAACoE,OAAO,EAAC,MAAM;UAACC,UAAU,EAAC,QAAQ;UAACI,GAAG,EAAE,CAAE;UAAAP,QAAA,GAC5C9B,WAAW,CAACD,YAAY,CAACE,IAAI,CAAC,eAC/BN,OAAA,CAAC/B,GAAG;YAAAkE,QAAA,gBACFnC,OAAA,CAAChC,UAAU;cAAC2E,OAAO,EAAC,IAAI;cAACC,UAAU,EAAC,MAAM;cAAAT,QAAA,EAAC;YAE3C;cAAA1B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbZ,OAAA,CAAChC,UAAU;cAAC2E,OAAO,EAAC,OAAO;cAACnC,KAAK,EAAC,gBAAgB;cAAA2B,QAAA,EAC/CrB,gBAAgB,CAACI;YAAQ;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENZ,OAAA,CAAC7B,UAAU;UACT0E,OAAO,EAAE1C,OAAQ;UACjBI,EAAE,EAAE;YACFqB,UAAU,EAAE,0BAA0B;YACtCI,cAAc,EAAE,YAAY;YAC5B,SAAS,EAAE;cACTJ,UAAU,EAAE;YACd;UACF,CAAE;UAAAO,QAAA,eAEFnC,OAAA,CAACpB,SAAS;YAAA6B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CAAC,eAEdZ,OAAA,CAACnC,aAAa;MAAC0C,EAAE,EAAE;QAAE6B,CAAC,EAAE;MAAE,CAAE;MAAAD,QAAA,eAC1BnC,OAAA,CAAC/B,GAAG;QAACsC,EAAE,EAAE;UAAE6B,CAAC,EAAE;QAAE,CAAE;QAAAD,QAAA,gBAEhBnC,OAAA,CAACvB,IAAI;UAAC8B,EAAE,EAAE;YAAEuC,EAAE,EAAE,CAAC;YAAEb,MAAM,EAAE,aAAapB,YAAY,CAACT,YAAY,CAACE,IAAI,CAAC;UAAK,CAAE;UAAA6B,QAAA,eAC5EnC,OAAA,CAACtB,WAAW;YAAAyD,QAAA,gBACVnC,OAAA,CAAC/B,GAAG;cAACoE,OAAO,EAAC,MAAM;cAACC,UAAU,EAAC,QAAQ;cAACI,GAAG,EAAE,CAAE;cAACI,EAAE,EAAE,CAAE;cAAAX,QAAA,gBACpDnC,OAAA,CAAChC,UAAU;gBAAC2E,OAAO,EAAC,IAAI;gBAACC,UAAU,EAAC,MAAM;gBAAAT,QAAA,EACvC/B,YAAY,CAAC2C;cAAK;gBAAAtC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eACbZ,OAAA,CAAC9B,IAAI;gBACH8E,KAAK,EAAE5C,YAAY,CAACE,IAAI,CAAC2C,WAAW,CAAC,CAAE;gBACvCC,IAAI,EAAC,OAAO;gBACZ3C,EAAE,EAAE;kBACFqB,UAAU,EAAEf,YAAY,CAACT,YAAY,CAACE,IAAI,CAAC;kBAC3CE,KAAK,EAAE,OAAO;kBACdoC,UAAU,EAAE;gBACd;cAAE;gBAAAnC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACFZ,OAAA,CAAC9B,IAAI;gBACH8E,KAAK,EAAElC,gBAAgB,CAACG,QAAS;gBACjCiC,IAAI,EAAC,OAAO;gBACZ1C,KAAK,EAAEM,gBAAgB,CAACG,QAAQ,KAAK,MAAM,GAAG,OAAO,GAAGH,gBAAgB,CAACG,QAAQ,KAAK,QAAQ,GAAG,SAAS,GAAG,SAAU;gBACvH0B,OAAO,EAAC;cAAU;gBAAAlC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENZ,OAAA,CAAChC,UAAU;cAAC2E,OAAO,EAAC,OAAO;cAACpC,EAAE,EAAE;gBAAEuC,EAAE,EAAE,CAAC;gBAAEK,UAAU,EAAE;cAAI,CAAE;cAAAhB,QAAA,EACxD/B,YAAY,CAACgD;YAAO;cAAA3C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX,CAAC,eAEbZ,OAAA,CAAChC,UAAU;cAAC2E,OAAO,EAAC,OAAO;cAACnC,KAAK,EAAC,gBAAgB;cAAA2B,QAAA,EAAC;YAGnD;cAAA1B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAGPZ,OAAA,CAAChC,UAAU;UAAC2E,OAAO,EAAC,IAAI;UAACC,UAAU,EAAC,MAAM;UAACrC,EAAE,EAAE;YAAEuC,EAAE,EAAE;UAAE,CAAE;UAAAX,QAAA,EAAC;QAE1D;UAAA1B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAEbZ,OAAA,CAAC3B,IAAI;UAACkC,EAAE,EAAE;YAAEuC,EAAE,EAAE;UAAE,CAAE;UAAAX,QAAA,gBAClBnC,OAAA,CAAC1B,QAAQ;YAAA6D,QAAA,gBACPnC,OAAA,CAACzB,YAAY;cAAA4D,QAAA,eACXnC,OAAA,CAACR,YAAY;gBAACgB,KAAK,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpB,CAAC,eACfZ,OAAA,CAACxB,YAAY;cACX6E,OAAO,EAAC,WAAW;cACnBC,SAAS,EAAE,IAAIC,IAAI,CAACnD,YAAY,CAACW,SAAS,CAAC,CAACyC,cAAc,CAAC;YAAE;cAAA/C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC,eAEXZ,OAAA,CAAC1B,QAAQ;YAAA6D,QAAA,gBACPnC,OAAA,CAACzB,YAAY;cAAA4D,QAAA,eACXnC,OAAA,CAACJ,YAAY;gBAACY,KAAK,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpB,CAAC,eACfZ,OAAA,CAACxB,YAAY;cACX6E,OAAO,EAAC,QAAQ;cAChBC,SAAS,EAAExC,gBAAgB,CAACE;YAAO;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC,eAEXZ,OAAA,CAAC1B,QAAQ;YAAA6D,QAAA,gBACPnC,OAAA,CAACzB,YAAY;cAAA4D,QAAA,eACXnC,OAAA,CAACN,UAAU;gBAACc,KAAK,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClB,CAAC,eACfZ,OAAA,CAACxB,YAAY;cACX6E,OAAO,EAAC,YAAY;cACpBC,SAAS,EAAExC,gBAAgB,CAACM;YAAU;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC,eAEXZ,OAAA,CAAC1B,QAAQ;YAAA6D,QAAA,gBACPnC,OAAA,CAACzB,YAAY;cAAA4D,QAAA,eACXnC,OAAA,CAACF,YAAY;gBAACU,KAAK,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpB,CAAC,eACfZ,OAAA,CAACxB,YAAY;cACX6E,OAAO,EAAC,UAAU;cAClBC,SAAS,EAAE,GAAGxC,gBAAgB,CAACO,SAAS,MAAMP,gBAAgB,CAACQ,QAAQ;YAAG;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3E,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC,eAEPZ,OAAA,CAAC5B,OAAO;UAACmC,EAAE,EAAE;YAAEkD,EAAE,EAAE;UAAE;QAAE;UAAAhD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAG1BZ,OAAA,CAAChC,UAAU;UAAC2E,OAAO,EAAC,IAAI;UAACC,UAAU,EAAC,MAAM;UAACrC,EAAE,EAAE;YAAEuC,EAAE,EAAE;UAAE,CAAE;UAAAX,QAAA,EAAC;QAE1D;UAAA1B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAEbZ,OAAA,CAAC/B,GAAG;UAACoE,OAAO,EAAC,MAAM;UAACK,GAAG,EAAE,CAAE;UAACgB,QAAQ,EAAC,MAAM;UAACZ,EAAE,EAAE,CAAE;UAAAX,QAAA,EAC/CrB,gBAAgB,CAACK,eAAe,CAACwC,GAAG,CAAC,CAACC,MAAM,EAAEC,KAAK,kBAClD7D,OAAA,CAAC9B,IAAI;YAEH8E,KAAK,EAAEY,MAAO;YACdjB,OAAO,EAAC,UAAU;YAClBnC,KAAK,EAAC,SAAS;YACf0C,IAAI,EAAC;UAAO,GAJPW,KAAK;YAAApD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAKX,CACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENZ,OAAA,CAAC5B,OAAO;UAACmC,EAAE,EAAE;YAAEkD,EAAE,EAAE;UAAE;QAAE;UAAAhD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAG1BZ,OAAA,CAAChC,UAAU;UAAC2E,OAAO,EAAC,IAAI;UAACC,UAAU,EAAC,MAAM;UAACrC,EAAE,EAAE;YAAEuC,EAAE,EAAE;UAAE,CAAE;UAAAX,QAAA,EAAC;QAE1D;UAAA1B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAEbZ,OAAA,CAAC/B,GAAG;UAACoE,OAAO,EAAC,MAAM;UAACK,GAAG,EAAE,CAAE;UAACgB,QAAQ,EAAC,MAAM;UAAAvB,QAAA,EACxCrB,gBAAgB,CAACS,OAAO,CAACoC,GAAG,CAAC,CAACG,MAAM,EAAED,KAAK,kBAC1C7D,OAAA,CAACjC,MAAM;YAEL4E,OAAO,EAAC,UAAU;YAClBO,IAAI,EAAC,OAAO;YACZ3C,EAAE,EAAE;cACFoB,YAAY,EAAE,CAAC;cACfc,WAAW,EAAE5B,YAAY,CAACT,YAAY,CAACE,IAAI,CAAC;cAC5CE,KAAK,EAAEK,YAAY,CAACT,YAAY,CAACE,IAAI,CAAC;cACtC,SAAS,EAAE;gBACTsB,UAAU,EAAE,GAAGf,YAAY,CAACT,YAAY,CAACE,IAAI,CAAC,IAAI;gBAClDmC,WAAW,EAAE5B,YAAY,CAACT,YAAY,CAACE,IAAI;cAC7C;YACF,CAAE;YAAA6B,QAAA,EAED2B;UAAM,GAbFD,KAAK;YAAApD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAcJ,CACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACO,CAAC,eAEhBZ,OAAA,CAAClC,aAAa;MAACyC,EAAE,EAAE;QAAE6B,CAAC,EAAE,CAAC;QAAE2B,SAAS,EAAE,WAAW;QAAEtB,WAAW,EAAE;MAAU,CAAE;MAAAN,QAAA,gBAC1EnC,OAAA,CAACjC,MAAM;QACL8E,OAAO,EAAE1C,OAAQ;QACjBwC,OAAO,EAAC,UAAU;QAClBpC,EAAE,EAAE;UAAEoB,YAAY,EAAE;QAAE,CAAE;QAAAQ,QAAA,EACzB;MAED;QAAA1B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTZ,OAAA,CAACjC,MAAM;QACL4E,OAAO,EAAC,WAAW;QACnBpC,EAAE,EAAE;UACFoB,YAAY,EAAE,CAAC;UACfC,UAAU,EAAE,2BAA2Bf,YAAY,CAACT,YAAY,CAACE,IAAI,CAAC,QAAQO,YAAY,CAACT,YAAY,CAACE,IAAI,CAAC,UAAU;UACvH,SAAS,EAAE;YACTsB,UAAU,EAAE,2BAA2Bf,YAAY,CAACT,YAAY,CAACE,IAAI,CAAC,UAAUO,YAAY,CAACT,YAAY,CAACE,IAAI,CAAC;UACjH;QACF,CAAE;QAAA6B,QAAA,EACH;MAED;QAAA1B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEb,CAAC;AAACoD,EAAA,GAvQI/D,wBAAwB;AAyQ9B,eAAeA,wBAAwB;AAAC,IAAA+D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}