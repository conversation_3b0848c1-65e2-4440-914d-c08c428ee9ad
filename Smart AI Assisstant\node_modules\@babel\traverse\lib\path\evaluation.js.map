{"version": 3, "names": ["VALID_OBJECT_CALLEES", "VALID_IDENTIFIER_CALLEES", "INVALID_METHODS", "isValidObjectCallee", "val", "includes", "isValidIdentifierCallee", "isInvalidMethod", "<PERSON><PERSON><PERSON><PERSON>", "res", "evaluate", "confident", "value", "de<PERSON>t", "path", "state", "deoptPath", "Globals", "Map", "undefined", "Infinity", "NaN", "evaluateCached", "node", "seen", "has", "existing", "get", "resolved", "item", "set", "_evaluate", "isSequenceExpression", "exprs", "length", "isStringLiteral", "isNumericLiteral", "isBooleanLiteral", "is<PERSON>ull<PERSON>iteral", "isTemplateLiteral", "evaluateQuasis", "quasis", "isTaggedTemplateExpression", "isMemberExpression", "object", "name", "property", "isIdentifier", "scope", "getBinding", "quasi", "isConditionalExpression", "testResult", "isExpressionWrapper", "parentPath", "isCallExpression", "callee", "isLiteral", "type", "key", "computed", "isReferencedIdentifier", "binding", "constantViolations", "start", "end", "bindingPathScope", "kind", "hasUnsafeBlock", "isBlockStatement", "parent", "_scope$path$parentPat", "hasValue", "resolve", "references", "isUnaryExpression", "prefix", "operator", "argument", "isFunction", "isClass", "arg", "isArrayExpression", "arr", "elems", "elem", "elemValue", "push", "isObjectExpression", "obj", "props", "prop", "isObjectMethod", "isSpreadElement", "keyP<PERSON>", "valuePath", "isLogicalExpression", "wasConfident", "left", "leftConfident", "right", "rightConfident", "isBinaryExpression", "Math", "pow", "context", "func", "global", "hasOwnProperty", "call", "args", "map", "apply", "raw", "str", "i", "cooked", "expr", "String"], "sources": ["../../src/path/evaluation.ts"], "sourcesContent": ["import type NodePath from \"./index.ts\";\nimport type * as t from \"@babel/types\";\n\n// This file contains Babe<PERSON> metainterpreter that can evaluate static code.\n\nconst VALID_OBJECT_CALLEES = [\"Number\", \"String\", \"Math\"] as const;\nconst VALID_IDENTIFIER_CALLEES = [\n  \"isFinite\",\n  \"isNaN\",\n  \"parseFloat\",\n  \"parseInt\",\n  \"decodeURI\",\n  \"decodeURIComponent\",\n  \"encodeURI\",\n  \"encodeURIComponent\",\n  process.env.BABEL_8_BREAKING ? \"btoa\" : null,\n  process.env.BABEL_8_BREAKING ? \"atob\" : null,\n] as const;\n\nconst INVALID_METHODS = [\"random\"] as const;\n\nfunction isValidObjectCallee(\n  val: string,\n): val is (typeof VALID_OBJECT_CALLEES)[number] {\n  return VALID_OBJECT_CALLEES.includes(\n    // @ts-expect-error val is a string\n    val,\n  );\n}\n\nfunction isValidIdentifierCallee(\n  val: string,\n): val is (typeof VALID_IDENTIFIER_CALLEES)[number] {\n  return VALID_IDENTIFIER_CALLEES.includes(\n    // @ts-expect-error val is a string\n    val,\n  );\n}\n\nfunction isInvalidMethod(val: string): val is (typeof INVALID_METHODS)[number] {\n  return INVALID_METHODS.includes(\n    // @ts-expect-error val is a string\n    val,\n  );\n}\n\n/**\n * Walk the input `node` and statically evaluate if it's truthy.\n *\n * Returning `true` when we're sure that the expression will evaluate to a\n * truthy value, `false` if we're sure that it will evaluate to a falsy\n * value and `undefined` if we aren't sure. Because of this please do not\n * rely on coercion when using this method and check with === if it's false.\n *\n * For example do:\n *\n *   if (t.evaluateTruthy(node) === false) falsyLogic();\n *\n * **AND NOT**\n *\n *   if (!t.evaluateTruthy(node)) falsyLogic();\n *\n */\n\nexport function evaluateTruthy(this: NodePath): boolean {\n  const res = this.evaluate();\n  if (res.confident) return !!res.value;\n}\n\ntype State = {\n  confident: boolean;\n  deoptPath: NodePath | null;\n  seen: Map<t.Node, Result>;\n};\n\ntype Result = {\n  resolved: boolean;\n  value?: any;\n};\n/**\n * Deopts the evaluation\n */\nfunction deopt(path: NodePath, state: State) {\n  if (!state.confident) return;\n  state.deoptPath = path;\n  state.confident = false;\n}\n\nconst Globals = new Map([\n  [\"undefined\", undefined],\n  [\"Infinity\", Infinity],\n  [\"NaN\", NaN],\n]);\n\n/**\n * We wrap the _evaluate method so we can track `seen` nodes, we push an item\n * to the map before we actually evaluate it so we can deopt on self recursive\n * nodes such as:\n *\n *   var g = a ? 1 : 2,\n *       a = g * this.foo\n */\nfunction evaluateCached(path: NodePath, state: State): any {\n  const { node } = path;\n  const { seen } = state;\n\n  if (seen.has(node)) {\n    const existing = seen.get(node);\n    if (existing.resolved) {\n      return existing.value;\n    } else {\n      deopt(path, state);\n      return;\n    }\n  } else {\n    const item: Result = { resolved: false };\n    seen.set(node, item);\n\n    const val = _evaluate(path, state);\n    if (state.confident) {\n      item.resolved = true;\n      item.value = val;\n    }\n    return val;\n  }\n}\n\nfunction _evaluate(path: NodePath, state: State): any {\n  if (!state.confident) return;\n\n  if (path.isSequenceExpression()) {\n    const exprs = path.get(\"expressions\");\n    return evaluateCached(exprs[exprs.length - 1], state);\n  }\n\n  if (\n    path.isStringLiteral() ||\n    path.isNumericLiteral() ||\n    path.isBooleanLiteral()\n  ) {\n    return path.node.value;\n  }\n\n  if (path.isNullLiteral()) {\n    return null;\n  }\n\n  if (path.isTemplateLiteral()) {\n    return evaluateQuasis(path, path.node.quasis, state);\n  }\n\n  if (\n    path.isTaggedTemplateExpression() &&\n    path.get(\"tag\").isMemberExpression()\n  ) {\n    const object = path.get(\"tag.object\") as NodePath;\n    const {\n      // @ts-expect-error todo(flow->ts): possible bug, object is can be any expression and so name might be undefined\n      node: { name },\n    } = object;\n    const property = path.get(\"tag.property\") as NodePath;\n\n    if (\n      object.isIdentifier() &&\n      name === \"String\" &&\n      // todo(flow->ts): was changed from getBinding(name, true)\n      //  should this be hasBinding(name, true) as the binding is never used later?\n      !path.scope.getBinding(name) &&\n      property.isIdentifier() &&\n      property.node.name === \"raw\"\n    ) {\n      return evaluateQuasis(path, path.node.quasi.quasis, state, true);\n    }\n  }\n\n  if (path.isConditionalExpression()) {\n    const testResult = evaluateCached(path.get(\"test\"), state);\n    if (!state.confident) return;\n    if (testResult) {\n      return evaluateCached(path.get(\"consequent\"), state);\n    } else {\n      return evaluateCached(path.get(\"alternate\"), state);\n    }\n  }\n\n  if (path.isExpressionWrapper()) {\n    // TypeCastExpression, ExpressionStatement etc\n    return evaluateCached(path.get(\"expression\"), state);\n  }\n\n  // \"foo\".length, \"foo\"[0]\n  if (\n    path.isMemberExpression() &&\n    !path.parentPath.isCallExpression({ callee: path.node })\n  ) {\n    const property = path.get(\"property\");\n    const object = path.get(\"object\");\n\n    if (object.isLiteral()) {\n      // @ts-expect-error todo(flow->ts): instead of typeof - would it be better to check type of ast node?\n      const value = object.node.value;\n      const type = typeof value;\n\n      let key = null;\n      if (path.node.computed) {\n        key = evaluateCached(property, state);\n        if (!state.confident) return;\n      } else if (property.isIdentifier()) {\n        key = property.node.name;\n      }\n      if (\n        (type === \"number\" || type === \"string\") &&\n        key != null &&\n        (typeof key === \"number\" || typeof key === \"string\")\n      ) {\n        return value[key];\n      }\n    }\n  }\n\n  if (path.isReferencedIdentifier()) {\n    const binding = path.scope.getBinding(path.node.name);\n\n    if (binding) {\n      if (\n        binding.constantViolations.length > 0 ||\n        path.node.start < binding.path.node.end\n      ) {\n        deopt(binding.path, state);\n        return;\n      }\n      const bindingPathScope = binding.path.scope;\n      if (binding.kind === \"var\" && bindingPathScope !== binding.scope) {\n        let hasUnsafeBlock =\n          !bindingPathScope.path.parentPath.isBlockStatement();\n        for (let scope = bindingPathScope.parent; scope; scope = scope.parent) {\n          if (scope === path.scope) {\n            if (hasUnsafeBlock) {\n              deopt(binding.path, state);\n              return;\n            }\n            break;\n          }\n          if (scope.path.parentPath?.isBlockStatement()) {\n            hasUnsafeBlock = true;\n          }\n        }\n      }\n      if (binding.hasValue) {\n        return binding.value;\n      }\n    }\n\n    const name = path.node.name;\n    if (Globals.has(name)) {\n      if (!binding) {\n        return Globals.get(name);\n      }\n      deopt(binding.path, state);\n      return;\n    }\n\n    const resolved = path.resolve();\n    if (resolved === path) {\n      deopt(path, state);\n      return;\n    }\n    const value = evaluateCached(resolved, state);\n    if (typeof value === \"object\" && value !== null && binding.references > 1) {\n      deopt(resolved, state);\n      return;\n    }\n    return value;\n  }\n\n  if (path.isUnaryExpression({ prefix: true })) {\n    if (path.node.operator === \"void\") {\n      // we don't need to evaluate the argument to know what this will return\n      return undefined;\n    }\n\n    const argument = path.get(\"argument\");\n    if (\n      path.node.operator === \"typeof\" &&\n      (argument.isFunction() || argument.isClass())\n    ) {\n      return \"function\";\n    }\n\n    const arg = evaluateCached(argument, state);\n    if (!state.confident) return;\n    switch (path.node.operator) {\n      case \"!\":\n        return !arg;\n      case \"+\":\n        return +arg;\n      case \"-\":\n        return -arg;\n      case \"~\":\n        return ~arg;\n      case \"typeof\":\n        return typeof arg;\n    }\n  }\n\n  if (path.isArrayExpression()) {\n    const arr = [];\n    const elems: Array<NodePath> = path.get(\"elements\");\n    for (const elem of elems) {\n      const elemValue = elem.evaluate();\n\n      if (elemValue.confident) {\n        arr.push(elemValue.value);\n      } else {\n        deopt(elemValue.deopt, state);\n        return;\n      }\n    }\n    return arr;\n  }\n\n  if (path.isObjectExpression()) {\n    const obj = {};\n    const props = path.get(\"properties\");\n    for (const prop of props) {\n      if (prop.isObjectMethod() || prop.isSpreadElement()) {\n        deopt(prop, state);\n        return;\n      }\n      const keyPath = prop.get(\"key\");\n      let key;\n      if (prop.node.computed) {\n        key = keyPath.evaluate();\n        if (!key.confident) {\n          deopt(key.deopt, state);\n          return;\n        }\n        key = key.value;\n      } else if (keyPath.isIdentifier()) {\n        key = keyPath.node.name;\n      } else {\n        key = (\n          keyPath.node as t.StringLiteral | t.NumericLiteral | t.BigIntLiteral\n        ).value;\n      }\n      const valuePath = prop.get(\"value\");\n      let value = valuePath.evaluate();\n      if (!value.confident) {\n        deopt(value.deopt, state);\n        return;\n      }\n      value = value.value;\n      // @ts-expect-error key is any type\n      obj[key] = value;\n    }\n    return obj;\n  }\n\n  if (path.isLogicalExpression()) {\n    // If we are confident that the left side of an && is false, or the left\n    // side of an || is true, we can be confident about the entire expression\n    const wasConfident = state.confident;\n    const left = evaluateCached(path.get(\"left\"), state);\n    const leftConfident = state.confident;\n    state.confident = wasConfident;\n    const right = evaluateCached(path.get(\"right\"), state);\n    const rightConfident = state.confident;\n\n    switch (path.node.operator) {\n      case \"||\":\n        // TODO consider having a \"truthy type\" that doesn't bail on\n        // left uncertainty but can still evaluate to truthy.\n        state.confident = leftConfident && (!!left || rightConfident);\n        if (!state.confident) return;\n\n        return left || right;\n      case \"&&\":\n        state.confident = leftConfident && (!left || rightConfident);\n        if (!state.confident) return;\n\n        return left && right;\n      case \"??\":\n        state.confident = leftConfident && (left != null || rightConfident);\n        if (!state.confident) return;\n\n        return left ?? right;\n    }\n  }\n\n  if (path.isBinaryExpression()) {\n    const left = evaluateCached(path.get(\"left\"), state);\n    if (!state.confident) return;\n    const right = evaluateCached(path.get(\"right\"), state);\n    if (!state.confident) return;\n\n    switch (path.node.operator) {\n      case \"-\":\n        return left - right;\n      case \"+\":\n        return left + right;\n      case \"/\":\n        return left / right;\n      case \"*\":\n        return left * right;\n      case \"%\":\n        return left % right;\n      case \"**\":\n        return left ** right;\n      case \"<\":\n        return left < right;\n      case \">\":\n        return left > right;\n      case \"<=\":\n        return left <= right;\n      case \">=\":\n        return left >= right;\n      case \"==\":\n        return left == right; // eslint-disable-line eqeqeq\n      case \"!=\":\n        return left != right; // eslint-disable-line eqeqeq\n      case \"===\":\n        return left === right;\n      case \"!==\":\n        return left !== right;\n      case \"|\":\n        return left | right;\n      case \"&\":\n        return left & right;\n      case \"^\":\n        return left ^ right;\n      case \"<<\":\n        return left << right;\n      case \">>\":\n        return left >> right;\n      case \">>>\":\n        return left >>> right;\n    }\n  }\n\n  if (path.isCallExpression()) {\n    const callee = path.get(\"callee\");\n    let context;\n    let func;\n\n    // Number(1);\n    if (\n      callee.isIdentifier() &&\n      !path.scope.getBinding(callee.node.name) &&\n      (isValidObjectCallee(callee.node.name) ||\n        isValidIdentifierCallee(callee.node.name))\n    ) {\n      func = global[callee.node.name];\n    }\n\n    if (callee.isMemberExpression()) {\n      const object = callee.get(\"object\");\n      const property = callee.get(\"property\");\n\n      // Math.min(1, 2)\n      if (\n        object.isIdentifier() &&\n        property.isIdentifier() &&\n        isValidObjectCallee(object.node.name) &&\n        !isInvalidMethod(property.node.name)\n      ) {\n        context = global[object.node.name];\n        const key = property.node.name;\n        if (Object.hasOwn(context, key)) {\n          func = context[key as keyof typeof context];\n        }\n      }\n\n      // \"abc\".charCodeAt(4)\n      if (object.isLiteral() && property.isIdentifier()) {\n        // @ts-expect-error todo(flow->ts): consider checking ast node type instead of value type (StringLiteral and NumberLiteral)\n        const type = typeof object.node.value;\n        if (type === \"string\" || type === \"number\") {\n          // @ts-expect-error todo(flow->ts): consider checking ast node type instead of value type\n          context = object.node.value;\n          func = context[property.node.name];\n        }\n      }\n    }\n\n    if (func) {\n      const args = path.get(\"arguments\").map(arg => evaluateCached(arg, state));\n      if (!state.confident) return;\n\n      return func.apply(context, args);\n    }\n  }\n\n  deopt(path, state);\n}\n\nfunction evaluateQuasis(\n  path: NodePath<t.TaggedTemplateExpression | t.TemplateLiteral>,\n  quasis: Array<any>,\n  state: State,\n  raw = false,\n) {\n  let str = \"\";\n\n  let i = 0;\n  const exprs: Array<NodePath<t.Node>> = path.isTemplateLiteral()\n    ? path.get(\"expressions\")\n    : path.get(\"quasi.expressions\");\n\n  for (const elem of quasis) {\n    // not confident, evaluated an expression we don't like\n    if (!state.confident) break;\n\n    // add on element\n    str += raw ? elem.value.raw : elem.value.cooked;\n\n    // add on interpolated expression if it's present\n    const expr = exprs[i++];\n    if (expr) str += String(evaluateCached(expr, state));\n  }\n\n  if (!state.confident) return;\n  return str;\n}\n\n/**\n * Walk the input `node` and statically evaluate it.\n *\n * Returns an object in the form `{ confident, value, deopt }`. `confident`\n * indicates whether or not we had to drop out of evaluating the expression\n * because of hitting an unknown node that we couldn't confidently find the\n * value of, in which case `deopt` is the path of said node.\n *\n * Example:\n *\n *   t.evaluate(parse(\"5 + 5\")) // { confident: true, value: 10 }\n *   t.evaluate(parse(\"!true\")) // { confident: true, value: false }\n *   t.evaluate(parse(\"foo + foo\")) // { confident: false, value: undefined, deopt: NodePath }\n *\n */\n\nexport function evaluate(this: NodePath): {\n  confident: boolean;\n  value: any;\n  deopt?: NodePath;\n} {\n  const state: State = {\n    confident: true,\n    deoptPath: null,\n    seen: new Map(),\n  };\n  let value = evaluateCached(this, state);\n  if (!state.confident) value = undefined;\n\n  return {\n    confident: state.confident,\n    deopt: state.deoptPath,\n    value: value,\n  };\n}\n"], "mappings": ";;;;;;;AAKA,MAAMA,oBAAoB,GAAG,CAAC,QAAQ,EAAE,QAAQ,EAAE,MAAM,CAAU;AAClE,MAAMC,wBAAwB,GAAG,CAC/B,UAAU,EACV,OAAO,EACP,YAAY,EACZ,UAAU,EACV,WAAW,EACX,oBAAoB,EACpB,WAAW,EACX,oBAAoB,EACoB,IAAI,EACJ,IAAI,CACpC;AAEV,MAAMC,eAAe,GAAG,CAAC,QAAQ,CAAU;AAE3C,SAASC,mBAAmBA,CAC1BC,GAAW,EACmC;EAC9C,OAAOJ,oBAAoB,CAACK,QAAQ,CAElCD,GACF,CAAC;AACH;AAEA,SAASE,uBAAuBA,CAC9BF,GAAW,EACuC;EAClD,OAAOH,wBAAwB,CAACI,QAAQ,CAEtCD,GACF,CAAC;AACH;AAEA,SAASG,eAAeA,CAACH,GAAW,EAA2C;EAC7E,OAAOF,eAAe,CAACG,QAAQ,CAE7BD,GACF,CAAC;AACH;AAoBO,SAASI,cAAcA,CAAA,EAA0B;EACtD,MAAMC,GAAG,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC;EAC3B,IAAID,GAAG,CAACE,SAAS,EAAE,OAAO,CAAC,CAACF,GAAG,CAACG,KAAK;AACvC;AAeA,SAASC,KAAKA,CAACC,IAAc,EAAEC,KAAY,EAAE;EAC3C,IAAI,CAACA,KAAK,CAACJ,SAAS,EAAE;EACtBI,KAAK,CAACC,SAAS,GAAGF,IAAI;EACtBC,KAAK,CAACJ,SAAS,GAAG,KAAK;AACzB;AAEA,MAAMM,OAAO,GAAG,IAAIC,GAAG,CAAC,CACtB,CAAC,WAAW,EAAEC,SAAS,CAAC,EACxB,CAAC,UAAU,EAAEC,QAAQ,CAAC,EACtB,CAAC,KAAK,EAAEC,GAAG,CAAC,CACb,CAAC;AAUF,SAASC,cAAcA,CAACR,IAAc,EAAEC,KAAY,EAAO;EACzD,MAAM;IAAEQ;EAAK,CAAC,GAAGT,IAAI;EACrB,MAAM;IAAEU;EAAK,CAAC,GAAGT,KAAK;EAEtB,IAAIS,IAAI,CAACC,GAAG,CAACF,IAAI,CAAC,EAAE;IAClB,MAAMG,QAAQ,GAAGF,IAAI,CAACG,GAAG,CAACJ,IAAI,CAAC;IAC/B,IAAIG,QAAQ,CAACE,QAAQ,EAAE;MACrB,OAAOF,QAAQ,CAACd,KAAK;IACvB,CAAC,MAAM;MACLC,KAAK,CAACC,IAAI,EAAEC,KAAK,CAAC;MAClB;IACF;EACF,CAAC,MAAM;IACL,MAAMc,IAAY,GAAG;MAAED,QAAQ,EAAE;IAAM,CAAC;IACxCJ,IAAI,CAACM,GAAG,CAACP,IAAI,EAAEM,IAAI,CAAC;IAEpB,MAAMzB,GAAG,GAAG2B,SAAS,CAACjB,IAAI,EAAEC,KAAK,CAAC;IAClC,IAAIA,KAAK,CAACJ,SAAS,EAAE;MACnBkB,IAAI,CAACD,QAAQ,GAAG,IAAI;MACpBC,IAAI,CAACjB,KAAK,GAAGR,GAAG;IAClB;IACA,OAAOA,GAAG;EACZ;AACF;AAEA,SAAS2B,SAASA,CAACjB,IAAc,EAAEC,KAAY,EAAO;EACpD,IAAI,CAACA,KAAK,CAACJ,SAAS,EAAE;EAEtB,IAAIG,IAAI,CAACkB,oBAAoB,CAAC,CAAC,EAAE;IAC/B,MAAMC,KAAK,GAAGnB,IAAI,CAACa,GAAG,CAAC,aAAa,CAAC;IACrC,OAAOL,cAAc,CAACW,KAAK,CAACA,KAAK,CAACC,MAAM,GAAG,CAAC,CAAC,EAAEnB,KAAK,CAAC;EACvD;EAEA,IACED,IAAI,CAACqB,eAAe,CAAC,CAAC,IACtBrB,IAAI,CAACsB,gBAAgB,CAAC,CAAC,IACvBtB,IAAI,CAACuB,gBAAgB,CAAC,CAAC,EACvB;IACA,OAAOvB,IAAI,CAACS,IAAI,CAACX,KAAK;EACxB;EAEA,IAAIE,IAAI,CAACwB,aAAa,CAAC,CAAC,EAAE;IACxB,OAAO,IAAI;EACb;EAEA,IAAIxB,IAAI,CAACyB,iBAAiB,CAAC,CAAC,EAAE;IAC5B,OAAOC,cAAc,CAAC1B,IAAI,EAAEA,IAAI,CAACS,IAAI,CAACkB,MAAM,EAAE1B,KAAK,CAAC;EACtD;EAEA,IACED,IAAI,CAAC4B,0BAA0B,CAAC,CAAC,IACjC5B,IAAI,CAACa,GAAG,CAAC,KAAK,CAAC,CAACgB,kBAAkB,CAAC,CAAC,EACpC;IACA,MAAMC,MAAM,GAAG9B,IAAI,CAACa,GAAG,CAAC,YAAY,CAAa;IACjD,MAAM;MAEJJ,IAAI,EAAE;QAAEsB;MAAK;IACf,CAAC,GAAGD,MAAM;IACV,MAAME,QAAQ,GAAGhC,IAAI,CAACa,GAAG,CAAC,cAAc,CAAa;IAErD,IACEiB,MAAM,CAACG,YAAY,CAAC,CAAC,IACrBF,IAAI,KAAK,QAAQ,IAGjB,CAAC/B,IAAI,CAACkC,KAAK,CAACC,UAAU,CAACJ,IAAI,CAAC,IAC5BC,QAAQ,CAACC,YAAY,CAAC,CAAC,IACvBD,QAAQ,CAACvB,IAAI,CAACsB,IAAI,KAAK,KAAK,EAC5B;MACA,OAAOL,cAAc,CAAC1B,IAAI,EAAEA,IAAI,CAACS,IAAI,CAAC2B,KAAK,CAACT,MAAM,EAAE1B,KAAK,EAAE,IAAI,CAAC;IAClE;EACF;EAEA,IAAID,IAAI,CAACqC,uBAAuB,CAAC,CAAC,EAAE;IAClC,MAAMC,UAAU,GAAG9B,cAAc,CAACR,IAAI,CAACa,GAAG,CAAC,MAAM,CAAC,EAAEZ,KAAK,CAAC;IAC1D,IAAI,CAACA,KAAK,CAACJ,SAAS,EAAE;IACtB,IAAIyC,UAAU,EAAE;MACd,OAAO9B,cAAc,CAACR,IAAI,CAACa,GAAG,CAAC,YAAY,CAAC,EAAEZ,KAAK,CAAC;IACtD,CAAC,MAAM;MACL,OAAOO,cAAc,CAACR,IAAI,CAACa,GAAG,CAAC,WAAW,CAAC,EAAEZ,KAAK,CAAC;IACrD;EACF;EAEA,IAAID,IAAI,CAACuC,mBAAmB,CAAC,CAAC,EAAE;IAE9B,OAAO/B,cAAc,CAACR,IAAI,CAACa,GAAG,CAAC,YAAY,CAAC,EAAEZ,KAAK,CAAC;EACtD;EAGA,IACED,IAAI,CAAC6B,kBAAkB,CAAC,CAAC,IACzB,CAAC7B,IAAI,CAACwC,UAAU,CAACC,gBAAgB,CAAC;IAAEC,MAAM,EAAE1C,IAAI,CAACS;EAAK,CAAC,CAAC,EACxD;IACA,MAAMuB,QAAQ,GAAGhC,IAAI,CAACa,GAAG,CAAC,UAAU,CAAC;IACrC,MAAMiB,MAAM,GAAG9B,IAAI,CAACa,GAAG,CAAC,QAAQ,CAAC;IAEjC,IAAIiB,MAAM,CAACa,SAAS,CAAC,CAAC,EAAE;MAEtB,MAAM7C,KAAK,GAAGgC,MAAM,CAACrB,IAAI,CAACX,KAAK;MAC/B,MAAM8C,IAAI,GAAG,OAAO9C,KAAK;MAEzB,IAAI+C,GAAG,GAAG,IAAI;MACd,IAAI7C,IAAI,CAACS,IAAI,CAACqC,QAAQ,EAAE;QACtBD,GAAG,GAAGrC,cAAc,CAACwB,QAAQ,EAAE/B,KAAK,CAAC;QACrC,IAAI,CAACA,KAAK,CAACJ,SAAS,EAAE;MACxB,CAAC,MAAM,IAAImC,QAAQ,CAACC,YAAY,CAAC,CAAC,EAAE;QAClCY,GAAG,GAAGb,QAAQ,CAACvB,IAAI,CAACsB,IAAI;MAC1B;MACA,IACE,CAACa,IAAI,KAAK,QAAQ,IAAIA,IAAI,KAAK,QAAQ,KACvCC,GAAG,IAAI,IAAI,KACV,OAAOA,GAAG,KAAK,QAAQ,IAAI,OAAOA,GAAG,KAAK,QAAQ,CAAC,EACpD;QACA,OAAO/C,KAAK,CAAC+C,GAAG,CAAC;MACnB;IACF;EACF;EAEA,IAAI7C,IAAI,CAAC+C,sBAAsB,CAAC,CAAC,EAAE;IACjC,MAAMC,OAAO,GAAGhD,IAAI,CAACkC,KAAK,CAACC,UAAU,CAACnC,IAAI,CAACS,IAAI,CAACsB,IAAI,CAAC;IAErD,IAAIiB,OAAO,EAAE;MACX,IACEA,OAAO,CAACC,kBAAkB,CAAC7B,MAAM,GAAG,CAAC,IACrCpB,IAAI,CAACS,IAAI,CAACyC,KAAK,GAAGF,OAAO,CAAChD,IAAI,CAACS,IAAI,CAAC0C,GAAG,EACvC;QACApD,KAAK,CAACiD,OAAO,CAAChD,IAAI,EAAEC,KAAK,CAAC;QAC1B;MACF;MACA,MAAMmD,gBAAgB,GAAGJ,OAAO,CAAChD,IAAI,CAACkC,KAAK;MAC3C,IAAIc,OAAO,CAACK,IAAI,KAAK,KAAK,IAAID,gBAAgB,KAAKJ,OAAO,CAACd,KAAK,EAAE;QAChE,IAAIoB,cAAc,GAChB,CAACF,gBAAgB,CAACpD,IAAI,CAACwC,UAAU,CAACe,gBAAgB,CAAC,CAAC;QACtD,KAAK,IAAIrB,KAAK,GAAGkB,gBAAgB,CAACI,MAAM,EAAEtB,KAAK,EAAEA,KAAK,GAAGA,KAAK,CAACsB,MAAM,EAAE;UAAA,IAAAC,qBAAA;UACrE,IAAIvB,KAAK,KAAKlC,IAAI,CAACkC,KAAK,EAAE;YACxB,IAAIoB,cAAc,EAAE;cAClBvD,KAAK,CAACiD,OAAO,CAAChD,IAAI,EAAEC,KAAK,CAAC;cAC1B;YACF;YACA;UACF;UACA,KAAAwD,qBAAA,GAAIvB,KAAK,CAAClC,IAAI,CAACwC,UAAU,aAArBiB,qBAAA,CAAuBF,gBAAgB,CAAC,CAAC,EAAE;YAC7CD,cAAc,GAAG,IAAI;UACvB;QACF;MACF;MACA,IAAIN,OAAO,CAACU,QAAQ,EAAE;QACpB,OAAOV,OAAO,CAAClD,KAAK;MACtB;IACF;IAEA,MAAMiC,IAAI,GAAG/B,IAAI,CAACS,IAAI,CAACsB,IAAI;IAC3B,IAAI5B,OAAO,CAACQ,GAAG,CAACoB,IAAI,CAAC,EAAE;MACrB,IAAI,CAACiB,OAAO,EAAE;QACZ,OAAO7C,OAAO,CAACU,GAAG,CAACkB,IAAI,CAAC;MAC1B;MACAhC,KAAK,CAACiD,OAAO,CAAChD,IAAI,EAAEC,KAAK,CAAC;MAC1B;IACF;IAEA,MAAMa,QAAQ,GAAGd,IAAI,CAAC2D,OAAO,CAAC,CAAC;IAC/B,IAAI7C,QAAQ,KAAKd,IAAI,EAAE;MACrBD,KAAK,CAACC,IAAI,EAAEC,KAAK,CAAC;MAClB;IACF;IACA,MAAMH,KAAK,GAAGU,cAAc,CAACM,QAAQ,EAAEb,KAAK,CAAC;IAC7C,IAAI,OAAOH,KAAK,KAAK,QAAQ,IAAIA,KAAK,KAAK,IAAI,IAAIkD,OAAO,CAACY,UAAU,GAAG,CAAC,EAAE;MACzE7D,KAAK,CAACe,QAAQ,EAAEb,KAAK,CAAC;MACtB;IACF;IACA,OAAOH,KAAK;EACd;EAEA,IAAIE,IAAI,CAAC6D,iBAAiB,CAAC;IAAEC,MAAM,EAAE;EAAK,CAAC,CAAC,EAAE;IAC5C,IAAI9D,IAAI,CAACS,IAAI,CAACsD,QAAQ,KAAK,MAAM,EAAE;MAEjC,OAAO1D,SAAS;IAClB;IAEA,MAAM2D,QAAQ,GAAGhE,IAAI,CAACa,GAAG,CAAC,UAAU,CAAC;IACrC,IACEb,IAAI,CAACS,IAAI,CAACsD,QAAQ,KAAK,QAAQ,KAC9BC,QAAQ,CAACC,UAAU,CAAC,CAAC,IAAID,QAAQ,CAACE,OAAO,CAAC,CAAC,CAAC,EAC7C;MACA,OAAO,UAAU;IACnB;IAEA,MAAMC,GAAG,GAAG3D,cAAc,CAACwD,QAAQ,EAAE/D,KAAK,CAAC;IAC3C,IAAI,CAACA,KAAK,CAACJ,SAAS,EAAE;IACtB,QAAQG,IAAI,CAACS,IAAI,CAACsD,QAAQ;MACxB,KAAK,GAAG;QACN,OAAO,CAACI,GAAG;MACb,KAAK,GAAG;QACN,OAAO,CAACA,GAAG;MACb,KAAK,GAAG;QACN,OAAO,CAACA,GAAG;MACb,KAAK,GAAG;QACN,OAAO,CAACA,GAAG;MACb,KAAK,QAAQ;QACX,OAAO,OAAOA,GAAG;IACrB;EACF;EAEA,IAAInE,IAAI,CAACoE,iBAAiB,CAAC,CAAC,EAAE;IAC5B,MAAMC,GAAG,GAAG,EAAE;IACd,MAAMC,KAAsB,GAAGtE,IAAI,CAACa,GAAG,CAAC,UAAU,CAAC;IACnD,KAAK,MAAM0D,IAAI,IAAID,KAAK,EAAE;MACxB,MAAME,SAAS,GAAGD,IAAI,CAAC3E,QAAQ,CAAC,CAAC;MAEjC,IAAI4E,SAAS,CAAC3E,SAAS,EAAE;QACvBwE,GAAG,CAACI,IAAI,CAACD,SAAS,CAAC1E,KAAK,CAAC;MAC3B,CAAC,MAAM;QACLC,KAAK,CAACyE,SAAS,CAACzE,KAAK,EAAEE,KAAK,CAAC;QAC7B;MACF;IACF;IACA,OAAOoE,GAAG;EACZ;EAEA,IAAIrE,IAAI,CAAC0E,kBAAkB,CAAC,CAAC,EAAE;IAC7B,MAAMC,GAAG,GAAG,CAAC,CAAC;IACd,MAAMC,KAAK,GAAG5E,IAAI,CAACa,GAAG,CAAC,YAAY,CAAC;IACpC,KAAK,MAAMgE,IAAI,IAAID,KAAK,EAAE;MACxB,IAAIC,IAAI,CAACC,cAAc,CAAC,CAAC,IAAID,IAAI,CAACE,eAAe,CAAC,CAAC,EAAE;QACnDhF,KAAK,CAAC8E,IAAI,EAAE5E,KAAK,CAAC;QAClB;MACF;MACA,MAAM+E,OAAO,GAAGH,IAAI,CAAChE,GAAG,CAAC,KAAK,CAAC;MAC/B,IAAIgC,GAAG;MACP,IAAIgC,IAAI,CAACpE,IAAI,CAACqC,QAAQ,EAAE;QACtBD,GAAG,GAAGmC,OAAO,CAACpF,QAAQ,CAAC,CAAC;QACxB,IAAI,CAACiD,GAAG,CAAChD,SAAS,EAAE;UAClBE,KAAK,CAAC8C,GAAG,CAAC9C,KAAK,EAAEE,KAAK,CAAC;UACvB;QACF;QACA4C,GAAG,GAAGA,GAAG,CAAC/C,KAAK;MACjB,CAAC,MAAM,IAAIkF,OAAO,CAAC/C,YAAY,CAAC,CAAC,EAAE;QACjCY,GAAG,GAAGmC,OAAO,CAACvE,IAAI,CAACsB,IAAI;MACzB,CAAC,MAAM;QACLc,GAAG,GACDmC,OAAO,CAACvE,IAAI,CACZX,KAAK;MACT;MACA,MAAMmF,SAAS,GAAGJ,IAAI,CAAChE,GAAG,CAAC,OAAO,CAAC;MACnC,IAAIf,KAAK,GAAGmF,SAAS,CAACrF,QAAQ,CAAC,CAAC;MAChC,IAAI,CAACE,KAAK,CAACD,SAAS,EAAE;QACpBE,KAAK,CAACD,KAAK,CAACC,KAAK,EAAEE,KAAK,CAAC;QACzB;MACF;MACAH,KAAK,GAAGA,KAAK,CAACA,KAAK;MAEnB6E,GAAG,CAAC9B,GAAG,CAAC,GAAG/C,KAAK;IAClB;IACA,OAAO6E,GAAG;EACZ;EAEA,IAAI3E,IAAI,CAACkF,mBAAmB,CAAC,CAAC,EAAE;IAG9B,MAAMC,YAAY,GAAGlF,KAAK,CAACJ,SAAS;IACpC,MAAMuF,IAAI,GAAG5E,cAAc,CAACR,IAAI,CAACa,GAAG,CAAC,MAAM,CAAC,EAAEZ,KAAK,CAAC;IACpD,MAAMoF,aAAa,GAAGpF,KAAK,CAACJ,SAAS;IACrCI,KAAK,CAACJ,SAAS,GAAGsF,YAAY;IAC9B,MAAMG,KAAK,GAAG9E,cAAc,CAACR,IAAI,CAACa,GAAG,CAAC,OAAO,CAAC,EAAEZ,KAAK,CAAC;IACtD,MAAMsF,cAAc,GAAGtF,KAAK,CAACJ,SAAS;IAEtC,QAAQG,IAAI,CAACS,IAAI,CAACsD,QAAQ;MACxB,KAAK,IAAI;QAGP9D,KAAK,CAACJ,SAAS,GAAGwF,aAAa,KAAK,CAAC,CAACD,IAAI,IAAIG,cAAc,CAAC;QAC7D,IAAI,CAACtF,KAAK,CAACJ,SAAS,EAAE;QAEtB,OAAOuF,IAAI,IAAIE,KAAK;MACtB,KAAK,IAAI;QACPrF,KAAK,CAACJ,SAAS,GAAGwF,aAAa,KAAK,CAACD,IAAI,IAAIG,cAAc,CAAC;QAC5D,IAAI,CAACtF,KAAK,CAACJ,SAAS,EAAE;QAEtB,OAAOuF,IAAI,IAAIE,KAAK;MACtB,KAAK,IAAI;QACPrF,KAAK,CAACJ,SAAS,GAAGwF,aAAa,KAAKD,IAAI,IAAI,IAAI,IAAIG,cAAc,CAAC;QACnE,IAAI,CAACtF,KAAK,CAACJ,SAAS,EAAE;QAEtB,OAAOuF,IAAI,WAAJA,IAAI,GAAIE,KAAK;IACxB;EACF;EAEA,IAAItF,IAAI,CAACwF,kBAAkB,CAAC,CAAC,EAAE;IAC7B,MAAMJ,IAAI,GAAG5E,cAAc,CAACR,IAAI,CAACa,GAAG,CAAC,MAAM,CAAC,EAAEZ,KAAK,CAAC;IACpD,IAAI,CAACA,KAAK,CAACJ,SAAS,EAAE;IACtB,MAAMyF,KAAK,GAAG9E,cAAc,CAACR,IAAI,CAACa,GAAG,CAAC,OAAO,CAAC,EAAEZ,KAAK,CAAC;IACtD,IAAI,CAACA,KAAK,CAACJ,SAAS,EAAE;IAEtB,QAAQG,IAAI,CAACS,IAAI,CAACsD,QAAQ;MACxB,KAAK,GAAG;QACN,OAAOqB,IAAI,GAAGE,KAAK;MACrB,KAAK,GAAG;QACN,OAAOF,IAAI,GAAGE,KAAK;MACrB,KAAK,GAAG;QACN,OAAOF,IAAI,GAAGE,KAAK;MACrB,KAAK,GAAG;QACN,OAAOF,IAAI,GAAGE,KAAK;MACrB,KAAK,GAAG;QACN,OAAOF,IAAI,GAAGE,KAAK;MACrB,KAAK,IAAI;QACP,OAAAG,IAAA,CAAAC,GAAA,CAAON,IAAI,EAAIE,KAAK;MACtB,KAAK,GAAG;QACN,OAAOF,IAAI,GAAGE,KAAK;MACrB,KAAK,GAAG;QACN,OAAOF,IAAI,GAAGE,KAAK;MACrB,KAAK,IAAI;QACP,OAAOF,IAAI,IAAIE,KAAK;MACtB,KAAK,IAAI;QACP,OAAOF,IAAI,IAAIE,KAAK;MACtB,KAAK,IAAI;QACP,OAAOF,IAAI,IAAIE,KAAK;MACtB,KAAK,IAAI;QACP,OAAOF,IAAI,IAAIE,KAAK;MACtB,KAAK,KAAK;QACR,OAAOF,IAAI,KAAKE,KAAK;MACvB,KAAK,KAAK;QACR,OAAOF,IAAI,KAAKE,KAAK;MACvB,KAAK,GAAG;QACN,OAAOF,IAAI,GAAGE,KAAK;MACrB,KAAK,GAAG;QACN,OAAOF,IAAI,GAAGE,KAAK;MACrB,KAAK,GAAG;QACN,OAAOF,IAAI,GAAGE,KAAK;MACrB,KAAK,IAAI;QACP,OAAOF,IAAI,IAAIE,KAAK;MACtB,KAAK,IAAI;QACP,OAAOF,IAAI,IAAIE,KAAK;MACtB,KAAK,KAAK;QACR,OAAOF,IAAI,KAAKE,KAAK;IACzB;EACF;EAEA,IAAItF,IAAI,CAACyC,gBAAgB,CAAC,CAAC,EAAE;IAC3B,MAAMC,MAAM,GAAG1C,IAAI,CAACa,GAAG,CAAC,QAAQ,CAAC;IACjC,IAAI8E,OAAO;IACX,IAAIC,IAAI;IAGR,IACElD,MAAM,CAACT,YAAY,CAAC,CAAC,IACrB,CAACjC,IAAI,CAACkC,KAAK,CAACC,UAAU,CAACO,MAAM,CAACjC,IAAI,CAACsB,IAAI,CAAC,KACvC1C,mBAAmB,CAACqD,MAAM,CAACjC,IAAI,CAACsB,IAAI,CAAC,IACpCvC,uBAAuB,CAACkD,MAAM,CAACjC,IAAI,CAACsB,IAAI,CAAC,CAAC,EAC5C;MACA6D,IAAI,GAAGC,MAAM,CAACnD,MAAM,CAACjC,IAAI,CAACsB,IAAI,CAAC;IACjC;IAEA,IAAIW,MAAM,CAACb,kBAAkB,CAAC,CAAC,EAAE;MAC/B,MAAMC,MAAM,GAAGY,MAAM,CAAC7B,GAAG,CAAC,QAAQ,CAAC;MACnC,MAAMmB,QAAQ,GAAGU,MAAM,CAAC7B,GAAG,CAAC,UAAU,CAAC;MAGvC,IACEiB,MAAM,CAACG,YAAY,CAAC,CAAC,IACrBD,QAAQ,CAACC,YAAY,CAAC,CAAC,IACvB5C,mBAAmB,CAACyC,MAAM,CAACrB,IAAI,CAACsB,IAAI,CAAC,IACrC,CAACtC,eAAe,CAACuC,QAAQ,CAACvB,IAAI,CAACsB,IAAI,CAAC,EACpC;QACA4D,OAAO,GAAGE,MAAM,CAAC/D,MAAM,CAACrB,IAAI,CAACsB,IAAI,CAAC;QAClC,MAAMc,GAAG,GAAGb,QAAQ,CAACvB,IAAI,CAACsB,IAAI;QAC9B,IAAI+D,cAAA,CAAAC,IAAA,CAAcJ,OAAO,EAAE9C,GAAG,CAAC,EAAE;UAC/B+C,IAAI,GAAGD,OAAO,CAAC9C,GAAG,CAAyB;QAC7C;MACF;MAGA,IAAIf,MAAM,CAACa,SAAS,CAAC,CAAC,IAAIX,QAAQ,CAACC,YAAY,CAAC,CAAC,EAAE;QAEjD,MAAMW,IAAI,GAAG,OAAOd,MAAM,CAACrB,IAAI,CAACX,KAAK;QACrC,IAAI8C,IAAI,KAAK,QAAQ,IAAIA,IAAI,KAAK,QAAQ,EAAE;UAE1C+C,OAAO,GAAG7D,MAAM,CAACrB,IAAI,CAACX,KAAK;UAC3B8F,IAAI,GAAGD,OAAO,CAAC3D,QAAQ,CAACvB,IAAI,CAACsB,IAAI,CAAC;QACpC;MACF;IACF;IAEA,IAAI6D,IAAI,EAAE;MACR,MAAMI,IAAI,GAAGhG,IAAI,CAACa,GAAG,CAAC,WAAW,CAAC,CAACoF,GAAG,CAAC9B,GAAG,IAAI3D,cAAc,CAAC2D,GAAG,EAAElE,KAAK,CAAC,CAAC;MACzE,IAAI,CAACA,KAAK,CAACJ,SAAS,EAAE;MAEtB,OAAO+F,IAAI,CAACM,KAAK,CAACP,OAAO,EAAEK,IAAI,CAAC;IAClC;EACF;EAEAjG,KAAK,CAACC,IAAI,EAAEC,KAAK,CAAC;AACpB;AAEA,SAASyB,cAAcA,CACrB1B,IAA8D,EAC9D2B,MAAkB,EAClB1B,KAAY,EACZkG,GAAG,GAAG,KAAK,EACX;EACA,IAAIC,GAAG,GAAG,EAAE;EAEZ,IAAIC,CAAC,GAAG,CAAC;EACT,MAAMlF,KAA8B,GAAGnB,IAAI,CAACyB,iBAAiB,CAAC,CAAC,GAC3DzB,IAAI,CAACa,GAAG,CAAC,aAAa,CAAC,GACvBb,IAAI,CAACa,GAAG,CAAC,mBAAmB,CAAC;EAEjC,KAAK,MAAM0D,IAAI,IAAI5C,MAAM,EAAE;IAEzB,IAAI,CAAC1B,KAAK,CAACJ,SAAS,EAAE;IAGtBuG,GAAG,IAAID,GAAG,GAAG5B,IAAI,CAACzE,KAAK,CAACqG,GAAG,GAAG5B,IAAI,CAACzE,KAAK,CAACwG,MAAM;IAG/C,MAAMC,IAAI,GAAGpF,KAAK,CAACkF,CAAC,EAAE,CAAC;IACvB,IAAIE,IAAI,EAAEH,GAAG,IAAII,MAAM,CAAChG,cAAc,CAAC+F,IAAI,EAAEtG,KAAK,CAAC,CAAC;EACtD;EAEA,IAAI,CAACA,KAAK,CAACJ,SAAS,EAAE;EACtB,OAAOuG,GAAG;AACZ;AAkBO,SAASxG,QAAQA,CAAA,EAItB;EACA,MAAMK,KAAY,GAAG;IACnBJ,SAAS,EAAE,IAAI;IACfK,SAAS,EAAE,IAAI;IACfQ,IAAI,EAAE,IAAIN,GAAG,CAAC;EAChB,CAAC;EACD,IAAIN,KAAK,GAAGU,cAAc,CAAC,IAAI,EAAEP,KAAK,CAAC;EACvC,IAAI,CAACA,KAAK,CAACJ,SAAS,EAAEC,KAAK,GAAGO,SAAS;EAEvC,OAAO;IACLR,SAAS,EAAEI,KAAK,CAACJ,SAAS;IAC1BE,KAAK,EAAEE,KAAK,CAACC,SAAS;IACtBJ,KAAK,EAAEA;EACT,CAAC;AACH", "ignoreList": []}