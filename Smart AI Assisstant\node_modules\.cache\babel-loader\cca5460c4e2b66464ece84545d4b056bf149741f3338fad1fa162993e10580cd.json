{"ast": null, "code": "var _jsxFileName = \"E:\\\\PythonProjects\\\\React.js\\\\Smart AI Assisstant\\\\src\\\\components\\\\SmartDashboard.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Grid, Card, CardContent, Typography, LinearProgress, Chip, Avatar, List, ListItem, ListItemIcon, ListItemText, IconButton, Tooltip, Badge, Divider } from '@mui/material';\nimport { Security as SecurityIcon, TrendingUp as TrendingUpIcon, Warning as WarningIcon, Shield as ShieldIcon, Speed as SpeedIcon, Notifications as NotificationsIcon, Timeline as TimelineIcon, Psychology as AIIcon, AutoFixHigh as AutoFixIcon, Insights as InsightsIcon } from '@mui/icons-material';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SmartDashboard = /*#__PURE__*/_s(/*#__PURE__*/React.memo(_c = _s(() => {\n  _s();\n  const [realTimeData, setRealTimeData] = useState({\n    threatLevel: 15,\n    scansToday: 247,\n    threatsBlocked: 12,\n    systemHealth: 98,\n    aiConfidence: 94,\n    activeScans: 3\n  });\n  const [smartInsights, setSmartInsights] = useState([{\n    type: 'warning',\n    title: 'Unusual Activity Detected',\n    description: 'Increased malware attempts from Eastern Europe region',\n    confidence: 87,\n    timestamp: new Date(Date.now() - 1000 * 60 * 15)\n  }, {\n    type: 'success',\n    title: 'AI Model Updated',\n    description: 'Threat detection accuracy improved by 12%',\n    confidence: 95,\n    timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2)\n  }, {\n    type: 'info',\n    title: 'Security Trend Analysis',\n    description: 'Phishing attempts decreased by 23% this week',\n    confidence: 91,\n    timestamp: new Date(Date.now() - 1000 * 60 * 60 * 4)\n  }]);\n  const [threatMap, setThreatMap] = useState([{\n    region: 'North America',\n    threats: 45,\n    trend: 'down'\n  }, {\n    region: 'Europe',\n    threats: 67,\n    trend: 'up'\n  }, {\n    region: 'Asia Pacific',\n    threats: 89,\n    trend: 'stable'\n  }, {\n    region: 'South America',\n    threats: 23,\n    trend: 'down'\n  }]);\n\n  // Simulate real-time updates\n  useEffect(() => {\n    const interval = setInterval(() => {\n      setRealTimeData(prev => ({\n        ...prev,\n        scansToday: prev.scansToday + Math.floor(Math.random() * 3),\n        threatLevel: Math.max(5, Math.min(100, prev.threatLevel + (Math.random() - 0.5) * 10)),\n        systemHealth: Math.max(85, Math.min(100, prev.systemHealth + (Math.random() - 0.5) * 2)),\n        aiConfidence: Math.max(80, Math.min(100, prev.aiConfidence + (Math.random() - 0.5) * 3))\n      }));\n    }, 5000);\n    return () => clearInterval(interval);\n  }, []);\n  const getThreatLevelColor = level => {\n    if (level < 30) return 'success';\n    if (level < 70) return 'warning';\n    return 'error';\n  };\n  const getInsightIcon = type => {\n    switch (type) {\n      case 'warning':\n        return /*#__PURE__*/_jsxDEV(WarningIcon, {\n          color: \"warning\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 97,\n          columnNumber: 30\n        }, this);\n      case 'success':\n        return /*#__PURE__*/_jsxDEV(ShieldIcon, {\n          color: \"success\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 98,\n          columnNumber: 30\n        }, this);\n      case 'info':\n        return /*#__PURE__*/_jsxDEV(InsightsIcon, {\n          color: \"info\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 99,\n          columnNumber: 27\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(SecurityIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 100,\n          columnNumber: 23\n        }, this);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      p: 4,\n      background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.02) 100%)',\n      borderRadius: 4,\n      backdropFilter: 'blur(20px)',\n      border: '1px solid rgba(255, 255, 255, 0.1)'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        mb: 4,\n        pb: 2,\n        borderBottom: '1px solid rgba(102, 126, 234, 0.2)'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h4\",\n          fontWeight: \"bold\",\n          sx: {\n            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n            backgroundClip: 'text',\n            WebkitBackgroundClip: 'text',\n            WebkitTextFillColor: 'transparent',\n            mb: 1\n          },\n          children: \"Smart Dashboard\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 126,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          color: \"text.secondary\",\n          children: \"Real-time security monitoring and analytics\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 139,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 125,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Chip, {\n        icon: /*#__PURE__*/_jsxDEV(AIIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 144,\n          columnNumber: 17\n        }, this),\n        label: \"AI Active\",\n        color: \"success\",\n        variant: \"outlined\",\n        sx: {\n          background: 'linear-gradient(135deg, rgba(76, 175, 80, 0.1) 0%, rgba(139, 195, 74, 0.1) 100%)',\n          border: '1px solid rgba(76, 175, 80, 0.3)'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 143,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 115,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'grid',\n        gridTemplateColumns: {\n          xs: '1fr',\n          sm: 'repeat(2, 1fr)',\n          md: 'repeat(4, 1fr)'\n        },\n        gap: 3,\n        mb: 4\n      },\n      children: [/*#__PURE__*/_jsxDEV(Card, {\n        sx: {\n          background: theme => theme.palette.mode === 'dark' ? 'linear-gradient(135deg, rgba(12, 12, 12, 0.95) 0%, rgba(26, 26, 26, 0.95) 50%, rgba(45, 45, 45, 0.95) 100%)' : 'linear-gradient(135deg, rgba(248, 250, 252, 0.95) 0%, rgba(241, 245, 249, 0.95) 50%, rgba(226, 232, 240, 0.95) 100%)',\n          backdropFilter: 'blur(25px) saturate(180%)',\n          border: '1px solid rgba(102, 126, 234, 0.2)',\n          borderRadius: 3,\n          position: 'relative',\n          overflow: 'hidden',\n          boxShadow: '0 8px 32px rgba(102, 126, 234, 0.15)',\n          transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',\n          '&:hover': {\n            transform: 'translateY(-4px) scale(1.02)',\n            boxShadow: '0 16px 48px rgba(102, 126, 234, 0.25)'\n          },\n          '&::before': {\n            content: '\"\"',\n            position: 'absolute',\n            top: 0,\n            left: 0,\n            right: 0,\n            height: '4px',\n            background: 'linear-gradient(90deg, #667eea 0%, #764ba2 100%)'\n          }\n        },\n        children: /*#__PURE__*/_jsxDEV(CardContent, {\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            display: \"flex\",\n            alignItems: \"center\",\n            justifyContent: \"space-between\",\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h3\",\n                fontWeight: \"bold\",\n                color: \"primary\",\n                children: realTimeData.scansToday\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 198,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                children: \"Scans Today\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 201,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 197,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Avatar, {\n              sx: {\n                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                width: 56,\n                height: 56\n              },\n              children: /*#__PURE__*/_jsxDEV(SpeedIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 212,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 205,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 196,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 195,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 168,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Card, {\n        sx: {\n          background: theme => theme.palette.mode === 'dark' ? 'linear-gradient(135deg, rgba(26, 26, 46, 0.95) 0%, rgba(22, 33, 62, 0.95) 50%, rgba(15, 52, 96, 0.95) 100%)' : 'linear-gradient(135deg, rgba(248, 250, 252, 0.95) 0%, rgba(241, 245, 249, 0.95) 50%, rgba(226, 232, 240, 0.95) 100%)',\n          backdropFilter: 'blur(25px) saturate(180%)',\n          border: '1px solid rgba(233, 69, 96, 0.2)',\n          borderRadius: 3,\n          position: 'relative',\n          overflow: 'hidden',\n          boxShadow: '0 8px 32px rgba(233, 69, 96, 0.15)',\n          transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',\n          '&:hover': {\n            transform: 'translateY(-4px) scale(1.02)',\n            boxShadow: '0 16px 48px rgba(233, 69, 96, 0.25)'\n          },\n          '&::before': {\n            content: '\"\"',\n            position: 'absolute',\n            top: 0,\n            left: 0,\n            right: 0,\n            height: '4px',\n            background: 'linear-gradient(90deg, #e94560 0%, #f27121 100%)'\n          }\n        },\n        children: /*#__PURE__*/_jsxDEV(CardContent, {\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            display: \"flex\",\n            alignItems: \"center\",\n            justifyContent: \"space-between\",\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h3\",\n                fontWeight: \"bold\",\n                color: \"error\",\n                children: realTimeData.threatsBlocked\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 248,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                children: \"Threats Blocked\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 251,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 247,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Avatar, {\n              sx: {\n                background: 'linear-gradient(135deg, #f44336 0%, #ff5722 100%)',\n                width: 56,\n                height: 56\n              },\n              children: /*#__PURE__*/_jsxDEV(ShieldIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 262,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 255,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 246,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 245,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 218,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Card, {\n        sx: {\n          background: 'linear-gradient(135deg, rgba(76, 175, 80, 0.1) 0%, rgba(139, 195, 74, 0.1) 100%)',\n          border: '1px solid rgba(76, 175, 80, 0.2)',\n          borderRadius: 3,\n          position: 'relative',\n          overflow: 'hidden',\n          transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',\n          '&:hover': {\n            transform: 'translateY(-4px) scale(1.02)',\n            boxShadow: '0 16px 48px rgba(76, 175, 80, 0.25)'\n          },\n          '&::before': {\n            content: '\"\"',\n            position: 'absolute',\n            top: 0,\n            left: 0,\n            right: 0,\n            height: '4px',\n            background: 'linear-gradient(90deg, #4caf50 0%, #8bc34a 100%)'\n          }\n        },\n        children: /*#__PURE__*/_jsxDEV(CardContent, {\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            display: \"flex\",\n            alignItems: \"center\",\n            justifyContent: \"space-between\",\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h3\",\n                fontWeight: \"bold\",\n                color: \"success\",\n                children: [realTimeData.systemHealth, \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 294,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                children: \"System Health\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 297,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 293,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Avatar, {\n              sx: {\n                background: 'linear-gradient(135deg, #4caf50 0%, #8bc34a 100%)',\n                width: 56,\n                height: 56\n              },\n              children: /*#__PURE__*/_jsxDEV(TrendingUpIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 308,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 301,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 292,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(LinearProgress, {\n            variant: \"determinate\",\n            value: realTimeData.systemHealth,\n            sx: {\n              mt: 1,\n              height: 6,\n              borderRadius: 3\n            },\n            color: \"success\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 311,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 291,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 268,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Card, {\n        sx: {\n          background: 'linear-gradient(135deg, rgba(156, 39, 176, 0.1) 0%, rgba(233, 30, 99, 0.1) 100%)',\n          border: '1px solid rgba(156, 39, 176, 0.2)',\n          borderRadius: 3,\n          position: 'relative',\n          overflow: 'hidden',\n          transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',\n          '&:hover': {\n            transform: 'translateY(-4px) scale(1.02)',\n            boxShadow: '0 16px 48px rgba(156, 39, 176, 0.25)'\n          },\n          '&::before': {\n            content: '\"\"',\n            position: 'absolute',\n            top: 0,\n            left: 0,\n            right: 0,\n            height: '4px',\n            background: 'linear-gradient(90deg, #9c27b0 0%, #e91e63 100%)'\n          }\n        },\n        children: /*#__PURE__*/_jsxDEV(CardContent, {\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            display: \"flex\",\n            alignItems: \"center\",\n            justifyContent: \"space-between\",\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h3\",\n                fontWeight: \"bold\",\n                color: \"secondary\",\n                children: [realTimeData.aiConfidence, \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 346,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                children: \"AI Confidence\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 349,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 345,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Avatar, {\n              sx: {\n                background: 'linear-gradient(135deg, #9c27b0 0%, #e91e63 100%)',\n                width: 56,\n                height: 56\n              },\n              children: /*#__PURE__*/_jsxDEV(AIIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 360,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 353,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 344,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(LinearProgress, {\n            variant: \"determinate\",\n            value: realTimeData.aiConfidence,\n            sx: {\n              mt: 1,\n              height: 6,\n              borderRadius: 3\n            },\n            color: \"secondary\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 363,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 343,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 320,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 156,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'grid',\n        gridTemplateColumns: {\n          xs: '1fr',\n          md: '2fr 1fr'\n        },\n        gap: 3,\n        mb: 4\n      },\n      children: [/*#__PURE__*/_jsxDEV(Card, {\n        sx: {\n          background: theme => theme.palette.mode === 'dark' ? 'linear-gradient(135deg, rgba(30, 30, 30, 0.95) 0%, rgba(50, 50, 50, 0.95) 100%)' : 'linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%)',\n          backdropFilter: 'blur(20px)',\n          border: theme => `1px solid ${theme.palette.divider}`,\n          borderRadius: 3,\n          height: 'fit-content'\n        },\n        children: /*#__PURE__*/_jsxDEV(CardContent, {\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            display: \"flex\",\n            alignItems: \"center\",\n            gap: 2,\n            mb: 3,\n            children: [/*#__PURE__*/_jsxDEV(Avatar, {\n              sx: {\n                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                width: 40,\n                height: 40\n              },\n              children: /*#__PURE__*/_jsxDEV(InsightsIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 405,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 398,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h5\",\n              fontWeight: \"bold\",\n              children: \"Smart Security Insights\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 407,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Badge, {\n              badgeContent: smartInsights.length,\n              color: \"primary\",\n              children: /*#__PURE__*/_jsxDEV(NotificationsIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 411,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 410,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 397,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(List, {\n            children: smartInsights.map((insight, index) => /*#__PURE__*/_jsxDEV(React.Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(ListItem, {\n                sx: {\n                  borderRadius: 2,\n                  mb: 1,\n                  background: theme => theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.02)',\n                  '&:hover': {\n                    background: theme => theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.08)' : 'rgba(0, 0, 0, 0.04)'\n                  }\n                },\n                children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                  children: getInsightIcon(insight.type)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 432,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: /*#__PURE__*/_jsxDEV(Box, {\n                    display: \"flex\",\n                    alignItems: \"center\",\n                    gap: 2,\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"subtitle1\",\n                      fontWeight: \"600\",\n                      children: insight.title\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 438,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                      label: `${insight.confidence}% confidence`,\n                      size: \"small\",\n                      color: insight.confidence > 90 ? 'success' : 'warning',\n                      variant: \"outlined\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 441,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 437,\n                    columnNumber: 27\n                  }, this),\n                  secondary: /*#__PURE__*/_jsxDEV(Box, {\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      color: \"text.secondary\",\n                      sx: {\n                        mb: 1\n                      },\n                      children: insight.description\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 451,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"caption\",\n                      color: \"text.secondary\",\n                      children: insight.timestamp.toLocaleString()\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 454,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 450,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 435,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                  title: \"Auto-resolve\",\n                  children: /*#__PURE__*/_jsxDEV(IconButton, {\n                    size: \"small\",\n                    children: /*#__PURE__*/_jsxDEV(AutoFixIcon, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 462,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 461,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 460,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 418,\n                columnNumber: 21\n              }, this), index < smartInsights.length - 1 && /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 466,\n                columnNumber: 58\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 417,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 415,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 396,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 385,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Card, {\n        sx: {\n          background: theme => theme.palette.mode === 'dark' ? 'linear-gradient(135deg, rgba(30, 30, 30, 0.95) 0%, rgba(50, 50, 50, 0.95) 100%)' : 'linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%)',\n          backdropFilter: 'blur(20px)',\n          border: theme => `1px solid ${theme.palette.divider}`,\n          borderRadius: 3,\n          height: 'fit-content'\n        },\n        children: /*#__PURE__*/_jsxDEV(CardContent, {\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            display: \"flex\",\n            alignItems: \"center\",\n            gap: 2,\n            mb: 3,\n            children: [/*#__PURE__*/_jsxDEV(Avatar, {\n              sx: {\n                background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',\n                width: 40,\n                height: 40\n              },\n              children: /*#__PURE__*/_jsxDEV(TimelineIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 493,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 486,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              fontWeight: \"bold\",\n              children: \"Global Threat Map\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 495,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 485,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(List, {\n            dense: true,\n            children: threatMap.map((region, index) => /*#__PURE__*/_jsxDEV(ListItem, {\n              sx: {\n                px: 0\n              },\n              children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                primary: /*#__PURE__*/_jsxDEV(Box, {\n                  display: \"flex\",\n                  alignItems: \"center\",\n                  justifyContent: \"space-between\",\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    fontWeight: \"600\",\n                    children: region.region\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 506,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    display: \"flex\",\n                    alignItems: \"center\",\n                    gap: 1,\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      color: \"text.secondary\",\n                      children: region.threats\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 510,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                      label: region.trend,\n                      size: \"small\",\n                      color: region.trend === 'up' ? 'error' : region.trend === 'down' ? 'success' : 'default',\n                      variant: \"outlined\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 513,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 509,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 505,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 503,\n                columnNumber: 19\n              }, this)\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 502,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 500,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 484,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 473,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 374,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 105,\n    columnNumber: 5\n  }, this);\n}, \"ZP1fjcwMW3PjPomMalqrrp6Az0k=\")), \"ZP1fjcwMW3PjPomMalqrrp6Az0k=\");\n_c2 = SmartDashboard;\nSmartDashboard.displayName = 'SmartDashboard';\nexport default SmartDashboard;\nvar _c, _c2;\n$RefreshReg$(_c, \"SmartDashboard$React.memo\");\n$RefreshReg$(_c2, \"SmartDashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Typography", "LinearProgress", "Chip", "Avatar", "List", "ListItem", "ListItemIcon", "ListItemText", "IconButton", "<PERSON><PERSON><PERSON>", "Badge", "Divider", "Security", "SecurityIcon", "TrendingUp", "TrendingUpIcon", "Warning", "WarningIcon", "Shield", "ShieldIcon", "Speed", "SpeedIcon", "Notifications", "NotificationsIcon", "Timeline", "TimelineIcon", "Psychology", "AIIcon", "AutoFixHigh", "AutoFixIcon", "Insights", "InsightsIcon", "jsxDEV", "_jsxDEV", "SmartDashboard", "_s", "memo", "_c", "realTimeData", "setRealTimeData", "threatLevel", "scansToday", "threatsBlocked", "systemHealth", "aiConfidence", "activeScans", "smartInsights", "setSmartInsights", "type", "title", "description", "confidence", "timestamp", "Date", "now", "threatMap", "setThreatMap", "region", "threats", "trend", "interval", "setInterval", "prev", "Math", "floor", "random", "max", "min", "clearInterval", "getThreatLevelColor", "level", "getInsightIcon", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "sx", "p", "background", "borderRadius", "<PERSON><PERSON>ilter", "border", "children", "display", "justifyContent", "alignItems", "mb", "pb", "borderBottom", "variant", "fontWeight", "backgroundClip", "WebkitBackgroundClip", "WebkitTextFillColor", "icon", "label", "gridTemplateColumns", "xs", "sm", "md", "gap", "theme", "palette", "mode", "position", "overflow", "boxShadow", "transition", "transform", "content", "top", "left", "right", "height", "width", "value", "mt", "divider", "badgeContent", "length", "map", "insight", "index", "Fragment", "primary", "size", "secondary", "toLocaleString", "dense", "px", "_c2", "displayName", "$RefreshReg$"], "sources": ["E:/PythonProjects/React.js/Smart AI Assisstant/src/components/SmartDashboard.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Grid,\n  Card,\n  CardContent,\n  Typography,\n  LinearProgress,\n  Chip,\n  Avatar,\n  List,\n  ListItem,\n  ListItemIcon,\n  ListItemText,\n  IconButton,\n  Tooltip,\n  Badge,\n  Divider,\n} from '@mui/material';\nimport {\n  Security as SecurityIcon,\n  TrendingUp as TrendingUpIcon,\n  Warning as WarningIcon,\n  Shield as ShieldIcon,\n  Speed as SpeedIcon,\n  Notifications as NotificationsIcon,\n  Timeline as TimelineIcon,\n  Psychology as AIIcon,\n  AutoFixHigh as AutoFixIcon,\n  Insights as InsightsIcon,\n} from '@mui/icons-material';\n\nconst SmartDashboard = React.memo(() => {\n  const [realTimeData, setRealTimeData] = useState({\n    threatLevel: 15,\n    scansToday: 247,\n    threatsBlocked: 12,\n    systemHealth: 98,\n    aiConfidence: 94,\n    activeScans: 3,\n  });\n\n  const [smartInsights, setSmartInsights] = useState([\n    {\n      type: 'warning',\n      title: 'Unusual Activity Detected',\n      description: 'Increased malware attempts from Eastern Europe region',\n      confidence: 87,\n      timestamp: new Date(Date.now() - 1000 * 60 * 15),\n    },\n    {\n      type: 'success',\n      title: 'AI Model Updated',\n      description: 'Threat detection accuracy improved by 12%',\n      confidence: 95,\n      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2),\n    },\n    {\n      type: 'info',\n      title: 'Security Trend Analysis',\n      description: 'Phishing attempts decreased by 23% this week',\n      confidence: 91,\n      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 4),\n    },\n  ]);\n\n  const [threatMap, setThreatMap] = useState([\n    { region: 'North America', threats: 45, trend: 'down' },\n    { region: 'Europe', threats: 67, trend: 'up' },\n    { region: 'Asia Pacific', threats: 89, trend: 'stable' },\n    { region: 'South America', threats: 23, trend: 'down' },\n  ]);\n\n  // Simulate real-time updates\n  useEffect(() => {\n    const interval = setInterval(() => {\n      setRealTimeData(prev => ({\n        ...prev,\n        scansToday: prev.scansToday + Math.floor(Math.random() * 3),\n        threatLevel: Math.max(5, Math.min(100, prev.threatLevel + (Math.random() - 0.5) * 10)),\n        systemHealth: Math.max(85, Math.min(100, prev.systemHealth + (Math.random() - 0.5) * 2)),\n        aiConfidence: Math.max(80, Math.min(100, prev.aiConfidence + (Math.random() - 0.5) * 3)),\n      }));\n    }, 5000);\n\n    return () => clearInterval(interval);\n  }, []);\n\n  const getThreatLevelColor = (level) => {\n    if (level < 30) return 'success';\n    if (level < 70) return 'warning';\n    return 'error';\n  };\n\n  const getInsightIcon = (type) => {\n    switch (type) {\n      case 'warning': return <WarningIcon color=\"warning\" />;\n      case 'success': return <ShieldIcon color=\"success\" />;\n      case 'info': return <InsightsIcon color=\"info\" />;\n      default: return <SecurityIcon />;\n    }\n  };\n\n  return (\n    <Box\n      sx={{\n        p: 4,\n        background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.02) 100%)',\n        borderRadius: 4,\n        backdropFilter: 'blur(20px)',\n        border: '1px solid rgba(255, 255, 255, 0.1)',\n      }}\n    >\n      {/* Dashboard Header */}\n      <Box\n        sx={{\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center',\n          mb: 4,\n          pb: 2,\n          borderBottom: '1px solid rgba(102, 126, 234, 0.2)',\n        }}\n      >\n        <Box>\n          <Typography\n            variant=\"h4\"\n            fontWeight=\"bold\"\n            sx={{\n              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n              backgroundClip: 'text',\n              WebkitBackgroundClip: 'text',\n              WebkitTextFillColor: 'transparent',\n              mb: 1,\n            }}\n          >\n            Smart Dashboard\n          </Typography>\n          <Typography variant=\"body2\" color=\"text.secondary\">\n            Real-time security monitoring and analytics\n          </Typography>\n        </Box>\n        <Chip\n          icon={<AIIcon />}\n          label=\"AI Active\"\n          color=\"success\"\n          variant=\"outlined\"\n          sx={{\n            background: 'linear-gradient(135deg, rgba(76, 175, 80, 0.1) 0%, rgba(139, 195, 74, 0.1) 100%)',\n            border: '1px solid rgba(76, 175, 80, 0.3)',\n          }}\n        />\n      </Box>\n\n      {/* Real-time Metrics Grid */}\n      <Box\n        sx={{\n          display: 'grid',\n          gridTemplateColumns: {\n            xs: '1fr',\n            sm: 'repeat(2, 1fr)',\n            md: 'repeat(4, 1fr)',\n          },\n          gap: 3,\n          mb: 4,\n        }}\n      >\n        <Card\n          sx={{\n            background: (theme) => theme.palette.mode === 'dark'\n              ? 'linear-gradient(135deg, rgba(12, 12, 12, 0.95) 0%, rgba(26, 26, 26, 0.95) 50%, rgba(45, 45, 45, 0.95) 100%)'\n              : 'linear-gradient(135deg, rgba(248, 250, 252, 0.95) 0%, rgba(241, 245, 249, 0.95) 50%, rgba(226, 232, 240, 0.95) 100%)',\n            backdropFilter: 'blur(25px) saturate(180%)',\n            border: '1px solid rgba(102, 126, 234, 0.2)',\n            borderRadius: 3,\n            position: 'relative',\n            overflow: 'hidden',\n            boxShadow: '0 8px 32px rgba(102, 126, 234, 0.15)',\n            transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',\n            '&:hover': {\n              transform: 'translateY(-4px) scale(1.02)',\n                boxShadow: '0 16px 48px rgba(102, 126, 234, 0.25)',\n            },\n            '&::before': {\n              content: '\"\"',\n              position: 'absolute',\n              top: 0,\n              left: 0,\n              right: 0,\n              height: '4px',\n              background: 'linear-gradient(90deg, #667eea 0%, #764ba2 100%)',\n            },\n          }}\n        >\n          <CardContent>\n            <Box display=\"flex\" alignItems=\"center\" justifyContent=\"space-between\">\n              <Box>\n                <Typography variant=\"h3\" fontWeight=\"bold\" color=\"primary\">\n                  {realTimeData.scansToday}\n                </Typography>\n                <Typography variant=\"body2\" color=\"text.secondary\">\n                  Scans Today\n                </Typography>\n              </Box>\n              <Avatar\n                sx={{\n                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                  width: 56,\n                  height: 56,\n                }}\n              >\n                <SpeedIcon />\n              </Avatar>\n            </Box>\n          </CardContent>\n        </Card>\n\n        <Card\n          sx={{\n            background: (theme) => theme.palette.mode === 'dark'\n              ? 'linear-gradient(135deg, rgba(26, 26, 46, 0.95) 0%, rgba(22, 33, 62, 0.95) 50%, rgba(15, 52, 96, 0.95) 100%)'\n              : 'linear-gradient(135deg, rgba(248, 250, 252, 0.95) 0%, rgba(241, 245, 249, 0.95) 50%, rgba(226, 232, 240, 0.95) 100%)',\n            backdropFilter: 'blur(25px) saturate(180%)',\n            border: '1px solid rgba(233, 69, 96, 0.2)',\n            borderRadius: 3,\n            position: 'relative',\n            overflow: 'hidden',\n            boxShadow: '0 8px 32px rgba(233, 69, 96, 0.15)',\n            transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',\n            '&:hover': {\n              transform: 'translateY(-4px) scale(1.02)',\n              boxShadow: '0 16px 48px rgba(233, 69, 96, 0.25)',\n            },\n            '&::before': {\n              content: '\"\"',\n              position: 'absolute',\n              top: 0,\n              left: 0,\n              right: 0,\n              height: '4px',\n              background: 'linear-gradient(90deg, #e94560 0%, #f27121 100%)',\n            },\n          }}\n        >\n          <CardContent>\n            <Box display=\"flex\" alignItems=\"center\" justifyContent=\"space-between\">\n              <Box>\n                <Typography variant=\"h3\" fontWeight=\"bold\" color=\"error\">\n                    {realTimeData.threatsBlocked}\n                </Typography>\n                <Typography variant=\"body2\" color=\"text.secondary\">\n                  Threats Blocked\n                </Typography>\n              </Box>\n              <Avatar\n                sx={{\n                  background: 'linear-gradient(135deg, #f44336 0%, #ff5722 100%)',\n                  width: 56,\n                  height: 56,\n                }}\n              >\n                <ShieldIcon />\n              </Avatar>\n            </Box>\n          </CardContent>\n        </Card>\n\n        <Card\n          sx={{\n            background: 'linear-gradient(135deg, rgba(76, 175, 80, 0.1) 0%, rgba(139, 195, 74, 0.1) 100%)',\n            border: '1px solid rgba(76, 175, 80, 0.2)',\n            borderRadius: 3,\n            position: 'relative',\n            overflow: 'hidden',\n            transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',\n            '&:hover': {\n              transform: 'translateY(-4px) scale(1.02)',\n              boxShadow: '0 16px 48px rgba(76, 175, 80, 0.25)',\n            },\n            '&::before': {\n              content: '\"\"',\n              position: 'absolute',\n              top: 0,\n              left: 0,\n              right: 0,\n              height: '4px',\n              background: 'linear-gradient(90deg, #4caf50 0%, #8bc34a 100%)',\n            },\n          }}\n        >\n          <CardContent>\n            <Box display=\"flex\" alignItems=\"center\" justifyContent=\"space-between\">\n              <Box>\n                <Typography variant=\"h3\" fontWeight=\"bold\" color=\"success\">\n                  {realTimeData.systemHealth}%\n                </Typography>\n                <Typography variant=\"body2\" color=\"text.secondary\">\n                  System Health\n                </Typography>\n              </Box>\n              <Avatar\n                sx={{\n                  background: 'linear-gradient(135deg, #4caf50 0%, #8bc34a 100%)',\n                  width: 56,\n                  height: 56,\n                }}\n              >\n                <TrendingUpIcon />\n              </Avatar>\n            </Box>\n            <LinearProgress\n              variant=\"determinate\"\n              value={realTimeData.systemHealth}\n              sx={{ mt: 1, height: 6, borderRadius: 3 }}\n              color=\"success\"\n            />\n          </CardContent>\n        </Card>\n\n        <Card\n          sx={{\n            background: 'linear-gradient(135deg, rgba(156, 39, 176, 0.1) 0%, rgba(233, 30, 99, 0.1) 100%)',\n            border: '1px solid rgba(156, 39, 176, 0.2)',\n            borderRadius: 3,\n            position: 'relative',\n            overflow: 'hidden',\n            transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',\n            '&:hover': {\n              transform: 'translateY(-4px) scale(1.02)',\n              boxShadow: '0 16px 48px rgba(156, 39, 176, 0.25)',\n            },\n            '&::before': {\n              content: '\"\"',\n              position: 'absolute',\n              top: 0,\n              left: 0,\n              right: 0,\n              height: '4px',\n              background: 'linear-gradient(90deg, #9c27b0 0%, #e91e63 100%)',\n            },\n          }}\n        >\n          <CardContent>\n            <Box display=\"flex\" alignItems=\"center\" justifyContent=\"space-between\">\n              <Box>\n                <Typography variant=\"h3\" fontWeight=\"bold\" color=\"secondary\">\n                  {realTimeData.aiConfidence}%\n                </Typography>\n                <Typography variant=\"body2\" color=\"text.secondary\">\n                  AI Confidence\n                </Typography>\n              </Box>\n              <Avatar\n                sx={{\n                  background: 'linear-gradient(135deg, #9c27b0 0%, #e91e63 100%)',\n                  width: 56,\n                  height: 56,\n                }}\n                >\n                  <AIIcon />\n                </Avatar>\n              </Box>\n              <LinearProgress\n                variant=\"determinate\"\n                value={realTimeData.aiConfidence}\n                sx={{ mt: 1, height: 6, borderRadius: 3 }}\n                color=\"secondary\"\n              />\n            </CardContent>\n          </Card>\n      </Box>\n\n      {/* Smart Insights and Threat Map */}\n      <Box\n        sx={{\n          display: 'grid',\n          gridTemplateColumns: {\n            xs: '1fr',\n            md: '2fr 1fr',\n          },\n          gap: 3,\n          mb: 4,\n        }}\n      >\n        <Card\n          sx={{\n            background: (theme) => theme.palette.mode === 'dark'\n              ? 'linear-gradient(135deg, rgba(30, 30, 30, 0.95) 0%, rgba(50, 50, 50, 0.95) 100%)'\n              : 'linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%)',\n            backdropFilter: 'blur(20px)',\n            border: (theme) => `1px solid ${theme.palette.divider}`,\n            borderRadius: 3,\n            height: 'fit-content',\n          }}\n        >\n            <CardContent>\n              <Box display=\"flex\" alignItems=\"center\" gap={2} mb={3}>\n                <Avatar\n                  sx={{\n                    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                    width: 40,\n                    height: 40,\n                  }}\n                >\n                  <InsightsIcon />\n                </Avatar>\n                <Typography variant=\"h5\" fontWeight=\"bold\">\n                  Smart Security Insights\n                </Typography>\n                <Badge badgeContent={smartInsights.length} color=\"primary\">\n                  <NotificationsIcon />\n                </Badge>\n              </Box>\n\n              <List>\n                {smartInsights.map((insight, index) => (\n                  <React.Fragment key={index}>\n                    <ListItem\n                      sx={{\n                        borderRadius: 2,\n                        mb: 1,\n                        background: (theme) => theme.palette.mode === 'dark'\n                          ? 'rgba(255, 255, 255, 0.05)'\n                          : 'rgba(0, 0, 0, 0.02)',\n                        '&:hover': {\n                          background: (theme) => theme.palette.mode === 'dark'\n                            ? 'rgba(255, 255, 255, 0.08)'\n                            : 'rgba(0, 0, 0, 0.04)',\n                        },\n                      }}\n                    >\n                      <ListItemIcon>\n                        {getInsightIcon(insight.type)}\n                      </ListItemIcon>\n                      <ListItemText\n                        primary={\n                          <Box display=\"flex\" alignItems=\"center\" gap={2}>\n                            <Typography variant=\"subtitle1\" fontWeight=\"600\">\n                              {insight.title}\n                            </Typography>\n                            <Chip\n                              label={`${insight.confidence}% confidence`}\n                              size=\"small\"\n                              color={insight.confidence > 90 ? 'success' : 'warning'}\n                              variant=\"outlined\"\n                            />\n                          </Box>\n                        }\n                        secondary={\n                          <Box>\n                            <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 1 }}>\n                              {insight.description}\n                            </Typography>\n                            <Typography variant=\"caption\" color=\"text.secondary\">\n                              {insight.timestamp.toLocaleString()}\n                            </Typography>\n                          </Box>\n                        }\n                      />\n                      <Tooltip title=\"Auto-resolve\">\n                        <IconButton size=\"small\">\n                          <AutoFixIcon />\n                        </IconButton>\n                      </Tooltip>\n                    </ListItem>\n                    {index < smartInsights.length - 1 && <Divider />}\n                  </React.Fragment>\n                ))}\n              </List>\n          </CardContent>\n        </Card>\n\n        <Card\n          sx={{\n            background: (theme) => theme.palette.mode === 'dark'\n              ? 'linear-gradient(135deg, rgba(30, 30, 30, 0.95) 0%, rgba(50, 50, 50, 0.95) 100%)'\n              : 'linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%)',\n            backdropFilter: 'blur(20px)',\n            border: (theme) => `1px solid ${theme.palette.divider}`,\n            borderRadius: 3,\n            height: 'fit-content',\n          }}\n        >\n          <CardContent>\n            <Box display=\"flex\" alignItems=\"center\" gap={2} mb={3}>\n              <Avatar\n                sx={{\n                  background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',\n                  width: 40,\n                  height: 40,\n                }}\n              >\n                <TimelineIcon />\n              </Avatar>\n              <Typography variant=\"h6\" fontWeight=\"bold\">\n                Global Threat Map\n              </Typography>\n            </Box>\n\n            <List dense>\n              {threatMap.map((region, index) => (\n                <ListItem key={index} sx={{ px: 0 }}>\n                  <ListItemText\n                    primary={\n                      <Box display=\"flex\" alignItems=\"center\" justifyContent=\"space-between\">\n                        <Typography variant=\"body2\" fontWeight=\"600\">\n                          {region.region}\n                        </Typography>\n                        <Box display=\"flex\" alignItems=\"center\" gap={1}>\n                          <Typography variant=\"body2\" color=\"text.secondary\">\n                            {region.threats}\n                          </Typography>\n                          <Chip\n                            label={region.trend}\n                            size=\"small\"\n                            color={\n                              region.trend === 'up' ? 'error' :\n                              region.trend === 'down' ? 'success' : 'default'\n                            }\n                            variant=\"outlined\"\n                          />\n                        </Box>\n                      </Box>\n                    }\n                  />\n                </ListItem>\n              ))}\n            </List>\n          </CardContent>\n        </Card>\n      </Box>\n    </Box>\n  );\n});\n\nSmartDashboard.displayName = 'SmartDashboard';\n\nexport default SmartDashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,cAAc,EACdC,IAAI,EACJC,MAAM,EACNC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,YAAY,EACZC,UAAU,EACVC,OAAO,EACPC,KAAK,EACLC,OAAO,QACF,eAAe;AACtB,SACEC,QAAQ,IAAIC,YAAY,EACxBC,UAAU,IAAIC,cAAc,EAC5BC,OAAO,IAAIC,WAAW,EACtBC,MAAM,IAAIC,UAAU,EACpBC,KAAK,IAAIC,SAAS,EAClBC,aAAa,IAAIC,iBAAiB,EAClCC,QAAQ,IAAIC,YAAY,EACxBC,UAAU,IAAIC,MAAM,EACpBC,WAAW,IAAIC,WAAW,EAC1BC,QAAQ,IAAIC,YAAY,QACnB,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7B,MAAMC,cAAc,gBAAAC,EAAA,cAAG1C,KAAK,CAAC2C,IAAI,CAAAC,EAAA,GAAAF,EAAA,CAAC,MAAM;EAAAA,EAAA;EACtC,MAAM,CAACG,YAAY,EAAEC,eAAe,CAAC,GAAG7C,QAAQ,CAAC;IAC/C8C,WAAW,EAAE,EAAE;IACfC,UAAU,EAAE,GAAG;IACfC,cAAc,EAAE,EAAE;IAClBC,YAAY,EAAE,EAAE;IAChBC,YAAY,EAAE,EAAE;IAChBC,WAAW,EAAE;EACf,CAAC,CAAC;EAEF,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGrD,QAAQ,CAAC,CACjD;IACEsD,IAAI,EAAE,SAAS;IACfC,KAAK,EAAE,2BAA2B;IAClCC,WAAW,EAAE,uDAAuD;IACpEC,UAAU,EAAE,EAAE;IACdC,SAAS,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE;EACjD,CAAC,EACD;IACEN,IAAI,EAAE,SAAS;IACfC,KAAK,EAAE,kBAAkB;IACzBC,WAAW,EAAE,2CAA2C;IACxDC,UAAU,EAAE,EAAE;IACdC,SAAS,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACrD,CAAC,EACD;IACEN,IAAI,EAAE,MAAM;IACZC,KAAK,EAAE,yBAAyB;IAChCC,WAAW,EAAE,8CAA8C;IAC3DC,UAAU,EAAE,EAAE;IACdC,SAAS,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACrD,CAAC,CACF,CAAC;EAEF,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAG9D,QAAQ,CAAC,CACzC;IAAE+D,MAAM,EAAE,eAAe;IAAEC,OAAO,EAAE,EAAE;IAAEC,KAAK,EAAE;EAAO,CAAC,EACvD;IAAEF,MAAM,EAAE,QAAQ;IAAEC,OAAO,EAAE,EAAE;IAAEC,KAAK,EAAE;EAAK,CAAC,EAC9C;IAAEF,MAAM,EAAE,cAAc;IAAEC,OAAO,EAAE,EAAE;IAAEC,KAAK,EAAE;EAAS,CAAC,EACxD;IAAEF,MAAM,EAAE,eAAe;IAAEC,OAAO,EAAE,EAAE;IAAEC,KAAK,EAAE;EAAO,CAAC,CACxD,CAAC;;EAEF;EACAhE,SAAS,CAAC,MAAM;IACd,MAAMiE,QAAQ,GAAGC,WAAW,CAAC,MAAM;MACjCtB,eAAe,CAACuB,IAAI,KAAK;QACvB,GAAGA,IAAI;QACPrB,UAAU,EAAEqB,IAAI,CAACrB,UAAU,GAAGsB,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC;QAC3DzB,WAAW,EAAEuB,IAAI,CAACG,GAAG,CAAC,CAAC,EAAEH,IAAI,CAACI,GAAG,CAAC,GAAG,EAAEL,IAAI,CAACtB,WAAW,GAAG,CAACuB,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI,EAAE,CAAC,CAAC;QACtFtB,YAAY,EAAEoB,IAAI,CAACG,GAAG,CAAC,EAAE,EAAEH,IAAI,CAACI,GAAG,CAAC,GAAG,EAAEL,IAAI,CAACnB,YAAY,GAAG,CAACoB,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC;QACxFrB,YAAY,EAAEmB,IAAI,CAACG,GAAG,CAAC,EAAE,EAAEH,IAAI,CAACI,GAAG,CAAC,GAAG,EAAEL,IAAI,CAAClB,YAAY,GAAG,CAACmB,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC,CAAC;MACzF,CAAC,CAAC,CAAC;IACL,CAAC,EAAE,IAAI,CAAC;IAER,OAAO,MAAMG,aAAa,CAACR,QAAQ,CAAC;EACtC,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMS,mBAAmB,GAAIC,KAAK,IAAK;IACrC,IAAIA,KAAK,GAAG,EAAE,EAAE,OAAO,SAAS;IAChC,IAAIA,KAAK,GAAG,EAAE,EAAE,OAAO,SAAS;IAChC,OAAO,OAAO;EAChB,CAAC;EAED,MAAMC,cAAc,GAAIvB,IAAI,IAAK;IAC/B,QAAQA,IAAI;MACV,KAAK,SAAS;QAAE,oBAAOf,OAAA,CAAChB,WAAW;UAACuD,KAAK,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACtD,KAAK,SAAS;QAAE,oBAAO3C,OAAA,CAACd,UAAU;UAACqD,KAAK,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACrD,KAAK,MAAM;QAAE,oBAAO3C,OAAA,CAACF,YAAY;UAACyC,KAAK,EAAC;QAAM;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACjD;QAAS,oBAAO3C,OAAA,CAACpB,YAAY;UAAA4D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;IAClC;EACF,CAAC;EAED,oBACE3C,OAAA,CAACrC,GAAG;IACFiF,EAAE,EAAE;MACFC,CAAC,EAAE,CAAC;MACJC,UAAU,EAAE,uFAAuF;MACnGC,YAAY,EAAE,CAAC;MACfC,cAAc,EAAE,YAAY;MAC5BC,MAAM,EAAE;IACV,CAAE;IAAAC,QAAA,gBAGFlD,OAAA,CAACrC,GAAG;MACFiF,EAAE,EAAE;QACFO,OAAO,EAAE,MAAM;QACfC,cAAc,EAAE,eAAe;QAC/BC,UAAU,EAAE,QAAQ;QACpBC,EAAE,EAAE,CAAC;QACLC,EAAE,EAAE,CAAC;QACLC,YAAY,EAAE;MAChB,CAAE;MAAAN,QAAA,gBAEFlD,OAAA,CAACrC,GAAG;QAAAuF,QAAA,gBACFlD,OAAA,CAACjC,UAAU;UACT0F,OAAO,EAAC,IAAI;UACZC,UAAU,EAAC,MAAM;UACjBd,EAAE,EAAE;YACFE,UAAU,EAAE,mDAAmD;YAC/Da,cAAc,EAAE,MAAM;YACtBC,oBAAoB,EAAE,MAAM;YAC5BC,mBAAmB,EAAE,aAAa;YAClCP,EAAE,EAAE;UACN,CAAE;UAAAJ,QAAA,EACH;QAED;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACb3C,OAAA,CAACjC,UAAU;UAAC0F,OAAO,EAAC,OAAO;UAAClB,KAAK,EAAC,gBAAgB;UAAAW,QAAA,EAAC;QAEnD;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eACN3C,OAAA,CAAC/B,IAAI;QACH6F,IAAI,eAAE9D,OAAA,CAACN,MAAM;UAAA8C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACjBoB,KAAK,EAAC,WAAW;QACjBxB,KAAK,EAAC,SAAS;QACfkB,OAAO,EAAC,UAAU;QAClBb,EAAE,EAAE;UACFE,UAAU,EAAE,kFAAkF;UAC9FG,MAAM,EAAE;QACV;MAAE;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGN3C,OAAA,CAACrC,GAAG;MACFiF,EAAE,EAAE;QACFO,OAAO,EAAE,MAAM;QACfa,mBAAmB,EAAE;UACnBC,EAAE,EAAE,KAAK;UACTC,EAAE,EAAE,gBAAgB;UACpBC,EAAE,EAAE;QACN,CAAC;QACDC,GAAG,EAAE,CAAC;QACNd,EAAE,EAAE;MACN,CAAE;MAAAJ,QAAA,gBAEFlD,OAAA,CAACnC,IAAI;QACH+E,EAAE,EAAE;UACFE,UAAU,EAAGuB,KAAK,IAAKA,KAAK,CAACC,OAAO,CAACC,IAAI,KAAK,MAAM,GAChD,6GAA6G,GAC7G,sHAAsH;UAC1HvB,cAAc,EAAE,2BAA2B;UAC3CC,MAAM,EAAE,oCAAoC;UAC5CF,YAAY,EAAE,CAAC;UACfyB,QAAQ,EAAE,UAAU;UACpBC,QAAQ,EAAE,QAAQ;UAClBC,SAAS,EAAE,sCAAsC;UACjDC,UAAU,EAAE,uCAAuC;UACnD,SAAS,EAAE;YACTC,SAAS,EAAE,8BAA8B;YACvCF,SAAS,EAAE;UACf,CAAC;UACD,WAAW,EAAE;YACXG,OAAO,EAAE,IAAI;YACbL,QAAQ,EAAE,UAAU;YACpBM,GAAG,EAAE,CAAC;YACNC,IAAI,EAAE,CAAC;YACPC,KAAK,EAAE,CAAC;YACRC,MAAM,EAAE,KAAK;YACbnC,UAAU,EAAE;UACd;QACF,CAAE;QAAAI,QAAA,eAEFlD,OAAA,CAAClC,WAAW;UAAAoF,QAAA,eACVlD,OAAA,CAACrC,GAAG;YAACwF,OAAO,EAAC,MAAM;YAACE,UAAU,EAAC,QAAQ;YAACD,cAAc,EAAC,eAAe;YAAAF,QAAA,gBACpElD,OAAA,CAACrC,GAAG;cAAAuF,QAAA,gBACFlD,OAAA,CAACjC,UAAU;gBAAC0F,OAAO,EAAC,IAAI;gBAACC,UAAU,EAAC,MAAM;gBAACnB,KAAK,EAAC,SAAS;gBAAAW,QAAA,EACvD7C,YAAY,CAACG;cAAU;gBAAAgC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACd,CAAC,eACb3C,OAAA,CAACjC,UAAU;gBAAC0F,OAAO,EAAC,OAAO;gBAAClB,KAAK,EAAC,gBAAgB;gBAAAW,QAAA,EAAC;cAEnD;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACN3C,OAAA,CAAC9B,MAAM;cACL0E,EAAE,EAAE;gBACFE,UAAU,EAAE,mDAAmD;gBAC/DoC,KAAK,EAAE,EAAE;gBACTD,MAAM,EAAE;cACV,CAAE;cAAA/B,QAAA,eAEFlD,OAAA,CAACZ,SAAS;gBAAAoD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAEP3C,OAAA,CAACnC,IAAI;QACH+E,EAAE,EAAE;UACFE,UAAU,EAAGuB,KAAK,IAAKA,KAAK,CAACC,OAAO,CAACC,IAAI,KAAK,MAAM,GAChD,6GAA6G,GAC7G,sHAAsH;UAC1HvB,cAAc,EAAE,2BAA2B;UAC3CC,MAAM,EAAE,kCAAkC;UAC1CF,YAAY,EAAE,CAAC;UACfyB,QAAQ,EAAE,UAAU;UACpBC,QAAQ,EAAE,QAAQ;UAClBC,SAAS,EAAE,oCAAoC;UAC/CC,UAAU,EAAE,uCAAuC;UACnD,SAAS,EAAE;YACTC,SAAS,EAAE,8BAA8B;YACzCF,SAAS,EAAE;UACb,CAAC;UACD,WAAW,EAAE;YACXG,OAAO,EAAE,IAAI;YACbL,QAAQ,EAAE,UAAU;YACpBM,GAAG,EAAE,CAAC;YACNC,IAAI,EAAE,CAAC;YACPC,KAAK,EAAE,CAAC;YACRC,MAAM,EAAE,KAAK;YACbnC,UAAU,EAAE;UACd;QACF,CAAE;QAAAI,QAAA,eAEFlD,OAAA,CAAClC,WAAW;UAAAoF,QAAA,eACVlD,OAAA,CAACrC,GAAG;YAACwF,OAAO,EAAC,MAAM;YAACE,UAAU,EAAC,QAAQ;YAACD,cAAc,EAAC,eAAe;YAAAF,QAAA,gBACpElD,OAAA,CAACrC,GAAG;cAAAuF,QAAA,gBACFlD,OAAA,CAACjC,UAAU;gBAAC0F,OAAO,EAAC,IAAI;gBAACC,UAAU,EAAC,MAAM;gBAACnB,KAAK,EAAC,OAAO;gBAAAW,QAAA,EACnD7C,YAAY,CAACI;cAAc;gBAAA+B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB,CAAC,eACb3C,OAAA,CAACjC,UAAU;gBAAC0F,OAAO,EAAC,OAAO;gBAAClB,KAAK,EAAC,gBAAgB;gBAAAW,QAAA,EAAC;cAEnD;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACN3C,OAAA,CAAC9B,MAAM;cACL0E,EAAE,EAAE;gBACFE,UAAU,EAAE,mDAAmD;gBAC/DoC,KAAK,EAAE,EAAE;gBACTD,MAAM,EAAE;cACV,CAAE;cAAA/B,QAAA,eAEFlD,OAAA,CAACd,UAAU;gBAAAsD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAEP3C,OAAA,CAACnC,IAAI;QACH+E,EAAE,EAAE;UACFE,UAAU,EAAE,kFAAkF;UAC9FG,MAAM,EAAE,kCAAkC;UAC1CF,YAAY,EAAE,CAAC;UACfyB,QAAQ,EAAE,UAAU;UACpBC,QAAQ,EAAE,QAAQ;UAClBE,UAAU,EAAE,uCAAuC;UACnD,SAAS,EAAE;YACTC,SAAS,EAAE,8BAA8B;YACzCF,SAAS,EAAE;UACb,CAAC;UACD,WAAW,EAAE;YACXG,OAAO,EAAE,IAAI;YACbL,QAAQ,EAAE,UAAU;YACpBM,GAAG,EAAE,CAAC;YACNC,IAAI,EAAE,CAAC;YACPC,KAAK,EAAE,CAAC;YACRC,MAAM,EAAE,KAAK;YACbnC,UAAU,EAAE;UACd;QACF,CAAE;QAAAI,QAAA,eAEFlD,OAAA,CAAClC,WAAW;UAAAoF,QAAA,gBACVlD,OAAA,CAACrC,GAAG;YAACwF,OAAO,EAAC,MAAM;YAACE,UAAU,EAAC,QAAQ;YAACD,cAAc,EAAC,eAAe;YAAAF,QAAA,gBACpElD,OAAA,CAACrC,GAAG;cAAAuF,QAAA,gBACFlD,OAAA,CAACjC,UAAU;gBAAC0F,OAAO,EAAC,IAAI;gBAACC,UAAU,EAAC,MAAM;gBAACnB,KAAK,EAAC,SAAS;gBAAAW,QAAA,GACvD7C,YAAY,CAACK,YAAY,EAAC,GAC7B;cAAA;gBAAA8B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACb3C,OAAA,CAACjC,UAAU;gBAAC0F,OAAO,EAAC,OAAO;gBAAClB,KAAK,EAAC,gBAAgB;gBAAAW,QAAA,EAAC;cAEnD;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACN3C,OAAA,CAAC9B,MAAM;cACL0E,EAAE,EAAE;gBACFE,UAAU,EAAE,mDAAmD;gBAC/DoC,KAAK,EAAE,EAAE;gBACTD,MAAM,EAAE;cACV,CAAE;cAAA/B,QAAA,eAEFlD,OAAA,CAAClB,cAAc;gBAAA0D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eACN3C,OAAA,CAAChC,cAAc;YACbyF,OAAO,EAAC,aAAa;YACrB0B,KAAK,EAAE9E,YAAY,CAACK,YAAa;YACjCkC,EAAE,EAAE;cAAEwC,EAAE,EAAE,CAAC;cAAEH,MAAM,EAAE,CAAC;cAAElC,YAAY,EAAE;YAAE,CAAE;YAC1CR,KAAK,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACS;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAEP3C,OAAA,CAACnC,IAAI;QACH+E,EAAE,EAAE;UACFE,UAAU,EAAE,kFAAkF;UAC9FG,MAAM,EAAE,mCAAmC;UAC3CF,YAAY,EAAE,CAAC;UACfyB,QAAQ,EAAE,UAAU;UACpBC,QAAQ,EAAE,QAAQ;UAClBE,UAAU,EAAE,uCAAuC;UACnD,SAAS,EAAE;YACTC,SAAS,EAAE,8BAA8B;YACzCF,SAAS,EAAE;UACb,CAAC;UACD,WAAW,EAAE;YACXG,OAAO,EAAE,IAAI;YACbL,QAAQ,EAAE,UAAU;YACpBM,GAAG,EAAE,CAAC;YACNC,IAAI,EAAE,CAAC;YACPC,KAAK,EAAE,CAAC;YACRC,MAAM,EAAE,KAAK;YACbnC,UAAU,EAAE;UACd;QACF,CAAE;QAAAI,QAAA,eAEFlD,OAAA,CAAClC,WAAW;UAAAoF,QAAA,gBACVlD,OAAA,CAACrC,GAAG;YAACwF,OAAO,EAAC,MAAM;YAACE,UAAU,EAAC,QAAQ;YAACD,cAAc,EAAC,eAAe;YAAAF,QAAA,gBACpElD,OAAA,CAACrC,GAAG;cAAAuF,QAAA,gBACFlD,OAAA,CAACjC,UAAU;gBAAC0F,OAAO,EAAC,IAAI;gBAACC,UAAU,EAAC,MAAM;gBAACnB,KAAK,EAAC,WAAW;gBAAAW,QAAA,GACzD7C,YAAY,CAACM,YAAY,EAAC,GAC7B;cAAA;gBAAA6B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACb3C,OAAA,CAACjC,UAAU;gBAAC0F,OAAO,EAAC,OAAO;gBAAClB,KAAK,EAAC,gBAAgB;gBAAAW,QAAA,EAAC;cAEnD;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACN3C,OAAA,CAAC9B,MAAM;cACL0E,EAAE,EAAE;gBACFE,UAAU,EAAE,mDAAmD;gBAC/DoC,KAAK,EAAE,EAAE;gBACTD,MAAM,EAAE;cACV,CAAE;cAAA/B,QAAA,eAEAlD,OAAA,CAACN,MAAM;gBAAA8C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eACN3C,OAAA,CAAChC,cAAc;YACbyF,OAAO,EAAC,aAAa;YACrB0B,KAAK,EAAE9E,YAAY,CAACM,YAAa;YACjCiC,EAAE,EAAE;cAAEwC,EAAE,EAAE,CAAC;cAAEH,MAAM,EAAE,CAAC;cAAElC,YAAY,EAAE;YAAE,CAAE;YAC1CR,KAAK,EAAC;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACS;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGN3C,OAAA,CAACrC,GAAG;MACFiF,EAAE,EAAE;QACFO,OAAO,EAAE,MAAM;QACfa,mBAAmB,EAAE;UACnBC,EAAE,EAAE,KAAK;UACTE,EAAE,EAAE;QACN,CAAC;QACDC,GAAG,EAAE,CAAC;QACNd,EAAE,EAAE;MACN,CAAE;MAAAJ,QAAA,gBAEFlD,OAAA,CAACnC,IAAI;QACH+E,EAAE,EAAE;UACFE,UAAU,EAAGuB,KAAK,IAAKA,KAAK,CAACC,OAAO,CAACC,IAAI,KAAK,MAAM,GAChD,iFAAiF,GACjF,uFAAuF;UAC3FvB,cAAc,EAAE,YAAY;UAC5BC,MAAM,EAAGoB,KAAK,IAAK,aAAaA,KAAK,CAACC,OAAO,CAACe,OAAO,EAAE;UACvDtC,YAAY,EAAE,CAAC;UACfkC,MAAM,EAAE;QACV,CAAE;QAAA/B,QAAA,eAEAlD,OAAA,CAAClC,WAAW;UAAAoF,QAAA,gBACVlD,OAAA,CAACrC,GAAG;YAACwF,OAAO,EAAC,MAAM;YAACE,UAAU,EAAC,QAAQ;YAACe,GAAG,EAAE,CAAE;YAACd,EAAE,EAAE,CAAE;YAAAJ,QAAA,gBACpDlD,OAAA,CAAC9B,MAAM;cACL0E,EAAE,EAAE;gBACFE,UAAU,EAAE,mDAAmD;gBAC/DoC,KAAK,EAAE,EAAE;gBACTD,MAAM,EAAE;cACV,CAAE;cAAA/B,QAAA,eAEFlD,OAAA,CAACF,YAAY;gBAAA0C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACT3C,OAAA,CAACjC,UAAU;cAAC0F,OAAO,EAAC,IAAI;cAACC,UAAU,EAAC,MAAM;cAAAR,QAAA,EAAC;YAE3C;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb3C,OAAA,CAACvB,KAAK;cAAC6G,YAAY,EAAEzE,aAAa,CAAC0E,MAAO;cAAChD,KAAK,EAAC,SAAS;cAAAW,QAAA,eACxDlD,OAAA,CAACV,iBAAiB;gBAAAkD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAEN3C,OAAA,CAAC7B,IAAI;YAAA+E,QAAA,EACFrC,aAAa,CAAC2E,GAAG,CAAC,CAACC,OAAO,EAAEC,KAAK,kBAChC1F,OAAA,CAACxC,KAAK,CAACmI,QAAQ;cAAAzC,QAAA,gBACblD,OAAA,CAAC5B,QAAQ;gBACPwE,EAAE,EAAE;kBACFG,YAAY,EAAE,CAAC;kBACfO,EAAE,EAAE,CAAC;kBACLR,UAAU,EAAGuB,KAAK,IAAKA,KAAK,CAACC,OAAO,CAACC,IAAI,KAAK,MAAM,GAChD,2BAA2B,GAC3B,qBAAqB;kBACzB,SAAS,EAAE;oBACTzB,UAAU,EAAGuB,KAAK,IAAKA,KAAK,CAACC,OAAO,CAACC,IAAI,KAAK,MAAM,GAChD,2BAA2B,GAC3B;kBACN;gBACF,CAAE;gBAAArB,QAAA,gBAEFlD,OAAA,CAAC3B,YAAY;kBAAA6E,QAAA,EACVZ,cAAc,CAACmD,OAAO,CAAC1E,IAAI;gBAAC;kBAAAyB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjB,CAAC,eACf3C,OAAA,CAAC1B,YAAY;kBACXsH,OAAO,eACL5F,OAAA,CAACrC,GAAG;oBAACwF,OAAO,EAAC,MAAM;oBAACE,UAAU,EAAC,QAAQ;oBAACe,GAAG,EAAE,CAAE;oBAAAlB,QAAA,gBAC7ClD,OAAA,CAACjC,UAAU;sBAAC0F,OAAO,EAAC,WAAW;sBAACC,UAAU,EAAC,KAAK;sBAAAR,QAAA,EAC7CuC,OAAO,CAACzE;oBAAK;sBAAAwB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC,eACb3C,OAAA,CAAC/B,IAAI;sBACH8F,KAAK,EAAE,GAAG0B,OAAO,CAACvE,UAAU,cAAe;sBAC3C2E,IAAI,EAAC,OAAO;sBACZtD,KAAK,EAAEkD,OAAO,CAACvE,UAAU,GAAG,EAAE,GAAG,SAAS,GAAG,SAAU;sBACvDuC,OAAO,EAAC;oBAAU;sBAAAjB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnB,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CACN;kBACDmD,SAAS,eACP9F,OAAA,CAACrC,GAAG;oBAAAuF,QAAA,gBACFlD,OAAA,CAACjC,UAAU;sBAAC0F,OAAO,EAAC,OAAO;sBAAClB,KAAK,EAAC,gBAAgB;sBAACK,EAAE,EAAE;wBAAEU,EAAE,EAAE;sBAAE,CAAE;sBAAAJ,QAAA,EAC9DuC,OAAO,CAACxE;oBAAW;sBAAAuB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV,CAAC,eACb3C,OAAA,CAACjC,UAAU;sBAAC0F,OAAO,EAAC,SAAS;sBAAClB,KAAK,EAAC,gBAAgB;sBAAAW,QAAA,EACjDuC,OAAO,CAACtE,SAAS,CAAC4E,cAAc,CAAC;oBAAC;sBAAAvD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzB,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV;gBACN;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC,eACF3C,OAAA,CAACxB,OAAO;kBAACwC,KAAK,EAAC,cAAc;kBAAAkC,QAAA,eAC3BlD,OAAA,CAACzB,UAAU;oBAACsH,IAAI,EAAC,OAAO;oBAAA3C,QAAA,eACtBlD,OAAA,CAACJ,WAAW;sBAAA4C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,EACV+C,KAAK,GAAG7E,aAAa,CAAC0E,MAAM,GAAG,CAAC,iBAAIvF,OAAA,CAACtB,OAAO;gBAAA8D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA,GAjD7B+C,KAAK;cAAAlD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAkDV,CACjB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAEP3C,OAAA,CAACnC,IAAI;QACH+E,EAAE,EAAE;UACFE,UAAU,EAAGuB,KAAK,IAAKA,KAAK,CAACC,OAAO,CAACC,IAAI,KAAK,MAAM,GAChD,iFAAiF,GACjF,uFAAuF;UAC3FvB,cAAc,EAAE,YAAY;UAC5BC,MAAM,EAAGoB,KAAK,IAAK,aAAaA,KAAK,CAACC,OAAO,CAACe,OAAO,EAAE;UACvDtC,YAAY,EAAE,CAAC;UACfkC,MAAM,EAAE;QACV,CAAE;QAAA/B,QAAA,eAEFlD,OAAA,CAAClC,WAAW;UAAAoF,QAAA,gBACVlD,OAAA,CAACrC,GAAG;YAACwF,OAAO,EAAC,MAAM;YAACE,UAAU,EAAC,QAAQ;YAACe,GAAG,EAAE,CAAE;YAACd,EAAE,EAAE,CAAE;YAAAJ,QAAA,gBACpDlD,OAAA,CAAC9B,MAAM;cACL0E,EAAE,EAAE;gBACFE,UAAU,EAAE,mDAAmD;gBAC/DoC,KAAK,EAAE,EAAE;gBACTD,MAAM,EAAE;cACV,CAAE;cAAA/B,QAAA,eAEFlD,OAAA,CAACR,YAAY;gBAAAgD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACT3C,OAAA,CAACjC,UAAU;cAAC0F,OAAO,EAAC,IAAI;cAACC,UAAU,EAAC,MAAM;cAAAR,QAAA,EAAC;YAE3C;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAEN3C,OAAA,CAAC7B,IAAI;YAAC6H,KAAK;YAAA9C,QAAA,EACR5B,SAAS,CAACkE,GAAG,CAAC,CAAChE,MAAM,EAAEkE,KAAK,kBAC3B1F,OAAA,CAAC5B,QAAQ;cAAawE,EAAE,EAAE;gBAAEqD,EAAE,EAAE;cAAE,CAAE;cAAA/C,QAAA,eAClClD,OAAA,CAAC1B,YAAY;gBACXsH,OAAO,eACL5F,OAAA,CAACrC,GAAG;kBAACwF,OAAO,EAAC,MAAM;kBAACE,UAAU,EAAC,QAAQ;kBAACD,cAAc,EAAC,eAAe;kBAAAF,QAAA,gBACpElD,OAAA,CAACjC,UAAU;oBAAC0F,OAAO,EAAC,OAAO;oBAACC,UAAU,EAAC,KAAK;oBAAAR,QAAA,EACzC1B,MAAM,CAACA;kBAAM;oBAAAgB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACb3C,OAAA,CAACrC,GAAG;oBAACwF,OAAO,EAAC,MAAM;oBAACE,UAAU,EAAC,QAAQ;oBAACe,GAAG,EAAE,CAAE;oBAAAlB,QAAA,gBAC7ClD,OAAA,CAACjC,UAAU;sBAAC0F,OAAO,EAAC,OAAO;sBAAClB,KAAK,EAAC,gBAAgB;sBAAAW,QAAA,EAC/C1B,MAAM,CAACC;oBAAO;sBAAAe,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL,CAAC,eACb3C,OAAA,CAAC/B,IAAI;sBACH8F,KAAK,EAAEvC,MAAM,CAACE,KAAM;sBACpBmE,IAAI,EAAC,OAAO;sBACZtD,KAAK,EACHf,MAAM,CAACE,KAAK,KAAK,IAAI,GAAG,OAAO,GAC/BF,MAAM,CAACE,KAAK,KAAK,MAAM,GAAG,SAAS,GAAG,SACvC;sBACD+B,OAAO,EAAC;oBAAU;sBAAAjB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnB,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cACN;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC,GAvBW+C,KAAK;cAAAlD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAwBV,CACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC,kCAAC;AAACuD,GAAA,GArfGjG,cAAc;AAufpBA,cAAc,CAACkG,WAAW,GAAG,gBAAgB;AAE7C,eAAelG,cAAc;AAAC,IAAAG,EAAA,EAAA8F,GAAA;AAAAE,YAAA,CAAAhG,EAAA;AAAAgG,YAAA,CAAAF,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}