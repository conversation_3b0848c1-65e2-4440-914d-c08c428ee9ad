{"ast": null, "code": "var _jsxFileName = \"E:\\\\PythonProjects\\\\React.js\\\\Smart AI Assisstant\\\\src\\\\components\\\\Footer.jsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Box, Container, Grid, Typography, Link, IconButton, Divider, Stack, Chip } from '@mui/material';\nimport { Security as SecurityIcon, Email as EmailIcon, Phone as PhoneIcon, LocationOn as LocationIcon, GitHub as GitHubIcon, LinkedIn as LinkedInIcon, Twitter as TwitterIcon, Facebook as FacebookIcon, Shield as ShieldIcon, Verified as VerifiedIcon, Copyright as CopyrightIcon, Chat as DiscordIcon } from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Footer = /*#__PURE__*/_s(/*#__PURE__*/React.memo(_c = _s(() => {\n  _s();\n  const navigate = useNavigate();\n  const currentYear = new Date().getFullYear();\n  const footerSections = [{\n    title: 'Company',\n    description: 'Showcase who you are and what the business stands for.',\n    links: [{\n      label: 'About Us',\n      path: '/about'\n    }, {\n      label: 'Careers',\n      path: '/about'\n    }, {\n      label: 'Press & Media',\n      path: '/about'\n    }, {\n      label: 'Our Mission',\n      path: '/about'\n    }, {\n      label: 'Partners',\n      path: '/about'\n    }]\n  }, {\n    title: 'Platform',\n    description: 'Help users understand what your platform offers.',\n    links: [{\n      label: 'URL Security Scanner',\n      path: '/'\n    }, {\n      label: 'Smart Security Features',\n      path: '/smart-features'\n    }, {\n      label: 'Auto Vulnerability Scan',\n      path: '/smart-features'\n    }, {\n      label: 'Real-Time Threat Alerts',\n      path: '/smart-features'\n    }, {\n      label: 'Dashboard & Analytics',\n      path: '/smart-features'\n    }]\n  }, {\n    title: 'Resources',\n    description: 'Give users access to help, tools, and documentation.',\n    links: [{\n      label: 'Help Center',\n      path: '/about'\n    }, {\n      label: 'Documentation',\n      path: '/about'\n    }, {\n      label: 'API Reference',\n      path: '/about'\n    }, {\n      label: 'Tutorials',\n      path: '/about'\n    }, {\n      label: 'Community Forum',\n      path: '/about'\n    }]\n  }, {\n    title: 'Legal & Contact',\n    description: 'Build trust and offer easy communication.',\n    links: [{\n      label: 'Privacy Policy',\n      path: '/about'\n    }, {\n      label: 'Terms of Service',\n      path: '/about'\n    }, {\n      label: 'Security Policy',\n      path: '/about'\n    }, {\n      label: 'Contact Us',\n      path: '/about'\n    }],\n    contact: '<EMAIL>'\n  }];\n  const socialLinks = [{\n    icon: /*#__PURE__*/_jsxDEV(GitHubIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 82,\n      columnNumber: 13\n    }, this),\n    label: 'GitHub',\n    url: 'https://github.com',\n    color: '#667eea'\n  }, {\n    icon: /*#__PURE__*/_jsxDEV(LinkedInIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 88,\n      columnNumber: 13\n    }, this),\n    label: 'LinkedIn',\n    url: 'https://linkedin.com',\n    color: '#0077B5'\n  }, {\n    icon: /*#__PURE__*/_jsxDEV(TwitterIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 94,\n      columnNumber: 13\n    }, this),\n    label: 'Twitter',\n    url: 'https://twitter.com',\n    color: '#1DA1F2'\n  }, {\n    icon: /*#__PURE__*/_jsxDEV(DiscordIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 100,\n      columnNumber: 13\n    }, this),\n    label: 'Discord',\n    url: 'https://discord.com',\n    color: '#7289DA'\n  }];\n  const handleLinkClick = path => {\n    navigate(path);\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    component: \"footer\",\n    sx: {\n      background: theme => theme.palette.mode === 'dark' ? 'linear-gradient(135deg, rgba(12, 12, 12, 0.98) 0%, rgba(26, 26, 26, 0.98) 50%, rgba(45, 45, 45, 0.98) 100%)' : 'linear-gradient(135deg, rgba(26, 32, 44, 0.98) 0%, rgba(45, 55, 72, 0.98) 50%, rgba(74, 85, 104, 0.98) 100%)',\n      backdropFilter: 'blur(25px) saturate(180%)',\n      color: 'white',\n      mt: 'auto',\n      py: 8,\n      position: 'relative',\n      overflow: 'hidden',\n      '&::before': {\n        content: '\"\"',\n        position: 'absolute',\n        top: 0,\n        left: 0,\n        right: 0,\n        height: '4px',\n        background: 'linear-gradient(90deg, #667eea 0%, #764ba2 20%, #f093fb 40%, #f5576c 60%, #4facfe 80%, #00f2fe 100%)',\n        backgroundSize: '400% 400%',\n        animation: 'footerGradient 12s ease infinite'\n      },\n      '@keyframes footerGradient': {\n        '0%': {\n          backgroundPosition: '0% 50%'\n        },\n        '50%': {\n          backgroundPosition: '100% 50%'\n        },\n        '100%': {\n          backgroundPosition: '0% 50%'\n        }\n      }\n    },\n    children: /*#__PURE__*/_jsxDEV(Container, {\n      maxWidth: \"xl\",\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 6,\n        sx: {\n          mb: 6\n        },\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          lg: 4,\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              mb: 4\n            },\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              display: \"flex\",\n              alignItems: \"center\",\n              gap: 2,\n              mb: 3,\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                  borderRadius: '50%',\n                  width: 50,\n                  height: 50,\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  boxShadow: '0 8px 25px rgba(102, 126, 234, 0.3)'\n                },\n                children: /*#__PURE__*/_jsxDEV(SecurityIcon, {\n                  sx: {\n                    fontSize: 28,\n                    color: 'white'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 161,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 149,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h4\",\n                fontWeight: \"bold\",\n                children: \"AI Security Guard\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 163,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 148,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              sx: {\n                mb: 4,\n                opacity: 0.9,\n                lineHeight: 1.8,\n                fontSize: '1.1rem'\n              },\n              children: \"Advanced AI-powered security scanning platform providing comprehensive threat detection and analysis for URLs and files. Protecting your digital assets with cutting-edge technology.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 167,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Stack, {\n              direction: \"row\",\n              spacing: 1,\n              flexWrap: \"wrap\",\n              gap: 1,\n              children: [/*#__PURE__*/_jsxDEV(Chip, {\n                icon: /*#__PURE__*/_jsxDEV(ShieldIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 184,\n                  columnNumber: 25\n                }, this),\n                label: \"SOC 2 Compliant\",\n                size: \"small\",\n                sx: {\n                  bgcolor: 'rgba(76, 175, 80, 0.2)',\n                  color: '#4caf50',\n                  border: '1px solid rgba(76, 175, 80, 0.3)'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 183,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                icon: /*#__PURE__*/_jsxDEV(VerifiedIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 194,\n                  columnNumber: 25\n                }, this),\n                label: \"ISO 27001\",\n                size: \"small\",\n                sx: {\n                  bgcolor: 'rgba(33, 150, 243, 0.2)',\n                  color: '#2196f3',\n                  border: '1px solid rgba(33, 150, 243, 0.3)'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 193,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 182,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 147,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              gutterBottom: true,\n              fontWeight: \"600\",\n              children: \"Contact Information\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 208,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Stack, {\n              spacing: 1,\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                display: \"flex\",\n                alignItems: \"center\",\n                gap: 1,\n                children: [/*#__PURE__*/_jsxDEV(EmailIcon, {\n                  sx: {\n                    fontSize: 18,\n                    opacity: 0.7\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 213,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    opacity: 0.9\n                  },\n                  children: \"<EMAIL>\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 214,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 212,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                display: \"flex\",\n                alignItems: \"center\",\n                gap: 1,\n                children: [/*#__PURE__*/_jsxDEV(PhoneIcon, {\n                  sx: {\n                    fontSize: 18,\n                    opacity: 0.7\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 219,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    opacity: 0.9\n                  },\n                  children: \"+****************\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 220,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 218,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                display: \"flex\",\n                alignItems: \"center\",\n                gap: 1,\n                children: [/*#__PURE__*/_jsxDEV(LocationIcon, {\n                  sx: {\n                    fontSize: 18,\n                    opacity: 0.7\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 225,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    opacity: 0.9\n                  },\n                  children: \"San Francisco, CA, USA\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 226,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 224,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 211,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 207,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 146,\n          columnNumber: 11\n        }, this), footerSections.map((section, index) => /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              mb: 3\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              gutterBottom: true,\n              fontWeight: \"600\",\n              sx: {\n                mb: 1,\n                color: 'white'\n              },\n              children: section.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 238,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              sx: {\n                mb: 3,\n                color: 'rgba(255, 255, 255, 0.6)',\n                fontSize: '0.875rem'\n              },\n              children: section.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 241,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Stack, {\n              spacing: 1.5,\n              children: [section.links.map((link, linkIndex) => /*#__PURE__*/_jsxDEV(Link, {\n                component: \"button\",\n                variant: \"body2\",\n                onClick: () => handleLinkClick(link.path),\n                sx: {\n                  color: 'rgba(255, 255, 255, 0.8)',\n                  textDecoration: 'none',\n                  textAlign: 'left',\n                  background: 'none',\n                  border: 'none',\n                  cursor: 'pointer',\n                  padding: 0,\n                  transition: 'all 0.2s ease',\n                  fontSize: '0.9rem',\n                  '&:hover': {\n                    color: '#64b5f6',\n                    textDecoration: 'underline',\n                    transform: 'translateX(4px)'\n                  }\n                },\n                children: link.label\n              }, linkIndex, false, {\n                fileName: _jsxFileName,\n                lineNumber: 246,\n                columnNumber: 21\n              }, this)), section.contact && /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  mt: 2,\n                  pt: 2,\n                  borderTop: '1px solid rgba(255, 255, 255, 0.1)'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    color: 'rgba(255, 255, 255, 0.6)',\n                    mb: 1\n                  },\n                  children: \"Email:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 273,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    color: '#64b5f6',\n                    fontWeight: 500\n                  },\n                  children: section.contact\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 276,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 272,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 244,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 237,\n            columnNumber: 15\n          }, this)\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 236,\n          columnNumber: 13\n        }, this))]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 144,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Divider, {\n        sx: {\n          bgcolor: 'rgba(255, 255, 255, 0.1)',\n          mb: 3\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 287,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        display: \"flex\",\n        justifyContent: \"space-between\",\n        alignItems: \"center\",\n        flexWrap: \"wrap\",\n        gap: 2,\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          display: \"flex\",\n          alignItems: \"center\",\n          gap: 1,\n          children: [/*#__PURE__*/_jsxDEV(CopyrightIcon, {\n            sx: {\n              fontSize: 16,\n              opacity: 0.7\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 299,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            sx: {\n              opacity: 0.8\n            },\n            children: [currentYear, \" AI Security Guard. All rights reserved.\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 300,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 298,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          display: \"flex\",\n          gap: 3,\n          flexWrap: \"wrap\",\n          children: [/*#__PURE__*/_jsxDEV(Link, {\n            component: \"button\",\n            variant: \"body2\",\n            onClick: () => handleLinkClick('/about'),\n            sx: {\n              color: 'rgba(255, 255, 255, 0.8)',\n              textDecoration: 'none',\n              background: 'none',\n              border: 'none',\n              cursor: 'pointer',\n              '&:hover': {\n                color: '#64b5f6'\n              }\n            },\n            children: \"Privacy Policy\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 307,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            component: \"button\",\n            variant: \"body2\",\n            onClick: () => handleLinkClick('/about'),\n            sx: {\n              color: 'rgba(255, 255, 255, 0.8)',\n              textDecoration: 'none',\n              background: 'none',\n              border: 'none',\n              cursor: 'pointer',\n              '&:hover': {\n                color: '#64b5f6'\n              }\n            },\n            children: \"Terms of Service\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 322,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            component: \"button\",\n            variant: \"body2\",\n            onClick: () => handleLinkClick('/about'),\n            sx: {\n              color: 'rgba(255, 255, 255, 0.8)',\n              textDecoration: 'none',\n              background: 'none',\n              border: 'none',\n              cursor: 'pointer',\n              '&:hover': {\n                color: '#64b5f6'\n              }\n            },\n            children: \"Cookie Policy\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 337,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 306,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          display: \"flex\",\n          gap: 1,\n          children: socialLinks.map((social, index) => /*#__PURE__*/_jsxDEV(IconButton, {\n            href: social.url,\n            target: \"_blank\",\n            rel: \"noopener noreferrer\",\n            sx: {\n              color: 'rgba(255, 255, 255, 0.7)',\n              background: 'rgba(255, 255, 255, 0.05)',\n              border: '1px solid rgba(255, 255, 255, 0.1)',\n              borderRadius: 2,\n              width: 48,\n              height: 48,\n              transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',\n              '&:hover': {\n                color: social.color,\n                background: `linear-gradient(135deg, ${social.color}15, ${social.color}25)`,\n                border: `1px solid ${social.color}40`,\n                transform: 'translateY(-4px) scale(1.1)',\n                boxShadow: `0 8px 25px ${social.color}30`\n              }\n            },\n            \"aria-label\": social.label,\n            children: social.icon\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 357,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 355,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 290,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        display: \"flex\",\n        justifyContent: \"center\",\n        alignItems: \"center\",\n        gap: 2,\n        mt: 3,\n        pt: 3,\n        sx: {\n          borderTop: '1px solid rgba(255, 255, 255, 0.1)'\n        },\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"caption\",\n          sx: {\n            opacity: 0.6,\n            textAlign: 'center'\n          },\n          children: \"Trusted by 10,000+ organizations worldwide \\u2022 99.9% uptime \\u2022 24/7 security monitoring\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 396,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 387,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 142,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 112,\n    columnNumber: 5\n  }, this);\n}, \"CzcTeTziyjMsSrAVmHuCCb6+Bfg=\", false, function () {\n  return [useNavigate];\n})), \"CzcTeTziyjMsSrAVmHuCCb6+Bfg=\", false, function () {\n  return [useNavigate];\n});\n_c2 = Footer;\nFooter.displayName = 'Footer';\nexport default Footer;\nvar _c, _c2;\n$RefreshReg$(_c, \"Footer$React.memo\");\n$RefreshReg$(_c2, \"Footer\");", "map": {"version": 3, "names": ["React", "Box", "Container", "Grid", "Typography", "Link", "IconButton", "Divider", "<PERSON><PERSON>", "Chip", "Security", "SecurityIcon", "Email", "EmailIcon", "Phone", "PhoneIcon", "LocationOn", "LocationIcon", "GitHub", "GitHubIcon", "LinkedIn", "LinkedInIcon", "Twitter", "TwitterIcon", "Facebook", "FacebookIcon", "Shield", "ShieldIcon", "Verified", "VerifiedIcon", "Copyright", "CopyrightIcon", "Cha<PERSON>", "DiscordIcon", "useNavigate", "jsxDEV", "_jsxDEV", "Footer", "_s", "memo", "_c", "navigate", "currentYear", "Date", "getFullYear", "footerSections", "title", "description", "links", "label", "path", "contact", "socialLinks", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "url", "color", "handleLinkClick", "component", "sx", "background", "theme", "palette", "mode", "<PERSON><PERSON>ilter", "mt", "py", "position", "overflow", "content", "top", "left", "right", "height", "backgroundSize", "animation", "backgroundPosition", "children", "max<PERSON><PERSON><PERSON>", "container", "spacing", "mb", "item", "xs", "lg", "display", "alignItems", "gap", "borderRadius", "width", "justifyContent", "boxShadow", "fontSize", "variant", "fontWeight", "opacity", "lineHeight", "direction", "flexWrap", "size", "bgcolor", "border", "gutterBottom", "map", "section", "index", "sm", "md", "link", "linkIndex", "onClick", "textDecoration", "textAlign", "cursor", "padding", "transition", "transform", "pt", "borderTop", "social", "href", "target", "rel", "_c2", "displayName", "$RefreshReg$"], "sources": ["E:/PythonProjects/React.js/Smart AI Assisstant/src/components/Footer.jsx"], "sourcesContent": ["import React from 'react';\nimport {\n  <PERSON>,\n  Container,\n  Grid,\n  <PERSON><PERSON><PERSON>,\n  Link,\n  IconButton,\n  Divider,\n  Stack,\n  Chip,\n} from '@mui/material';\nimport {\n  Security as SecurityIcon,\n  Email as EmailIcon,\n  Phone as PhoneIcon,\n  LocationOn as LocationIcon,\n  GitHub as GitHubIcon,\n  LinkedIn as LinkedInIcon,\n  Twitter as <PERSON>Icon,\n  Facebook as FacebookIcon,\n  Shield as ShieldIcon,\n  Verified as VerifiedIcon,\n  Copyright as CopyrightIcon,\n  Chat as DiscordIcon,\n} from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\n\nconst Footer = React.memo(() => {\n  const navigate = useNavigate();\n  const currentYear = new Date().getFullYear();\n\n  const footerSections = [\n    {\n      title: 'Company',\n      description: 'Showcase who you are and what the business stands for.',\n      links: [\n        { label: 'About Us', path: '/about' },\n        { label: 'Careers', path: '/about' },\n        { label: 'Press & Media', path: '/about' },\n        { label: 'Our Mission', path: '/about' },\n        { label: 'Partners', path: '/about' },\n      ],\n    },\n    {\n      title: 'Platform',\n      description: 'Help users understand what your platform offers.',\n      links: [\n        { label: 'URL Security Scanner', path: '/' },\n        { label: 'Smart Security Features', path: '/smart-features' },\n        { label: 'Auto Vulnerability Scan', path: '/smart-features' },\n        { label: 'Real-Time Threat Alerts', path: '/smart-features' },\n        { label: 'Dashboard & Analytics', path: '/smart-features' },\n      ],\n    },\n    {\n      title: 'Resources',\n      description: 'Give users access to help, tools, and documentation.',\n      links: [\n        { label: 'Help Center', path: '/about' },\n        { label: 'Documentation', path: '/about' },\n        { label: 'API Reference', path: '/about' },\n        { label: 'Tutorials', path: '/about' },\n        { label: 'Community Forum', path: '/about' },\n      ],\n    },\n    {\n      title: 'Legal & Contact',\n      description: 'Build trust and offer easy communication.',\n      links: [\n        { label: 'Privacy Policy', path: '/about' },\n        { label: 'Terms of Service', path: '/about' },\n        { label: 'Security Policy', path: '/about' },\n        { label: 'Contact Us', path: '/about' },\n      ],\n      contact: '<EMAIL>',\n    },\n  ];\n\n  const socialLinks = [\n    {\n      icon: <GitHubIcon />,\n      label: 'GitHub',\n      url: 'https://github.com',\n      color: '#667eea'\n    },\n    {\n      icon: <LinkedInIcon />,\n      label: 'LinkedIn',\n      url: 'https://linkedin.com',\n      color: '#0077B5'\n    },\n    {\n      icon: <TwitterIcon />,\n      label: 'Twitter',\n      url: 'https://twitter.com',\n      color: '#1DA1F2'\n    },\n    {\n      icon: <DiscordIcon />,\n      label: 'Discord',\n      url: 'https://discord.com',\n      color: '#7289DA'\n    },\n  ];\n\n  const handleLinkClick = (path) => {\n    navigate(path);\n  };\n\n  return (\n    <Box\n      component=\"footer\"\n      sx={{\n        background: (theme) => theme.palette.mode === 'dark'\n          ? 'linear-gradient(135deg, rgba(12, 12, 12, 0.98) 0%, rgba(26, 26, 26, 0.98) 50%, rgba(45, 45, 45, 0.98) 100%)'\n          : 'linear-gradient(135deg, rgba(26, 32, 44, 0.98) 0%, rgba(45, 55, 72, 0.98) 50%, rgba(74, 85, 104, 0.98) 100%)',\n        backdropFilter: 'blur(25px) saturate(180%)',\n        color: 'white',\n        mt: 'auto',\n        py: 8,\n        position: 'relative',\n        overflow: 'hidden',\n        '&::before': {\n          content: '\"\"',\n          position: 'absolute',\n          top: 0,\n          left: 0,\n          right: 0,\n          height: '4px',\n          background: 'linear-gradient(90deg, #667eea 0%, #764ba2 20%, #f093fb 40%, #f5576c 60%, #4facfe 80%, #00f2fe 100%)',\n          backgroundSize: '400% 400%',\n          animation: 'footerGradient 12s ease infinite',\n        },\n        '@keyframes footerGradient': {\n          '0%': { backgroundPosition: '0% 50%' },\n          '50%': { backgroundPosition: '100% 50%' },\n          '100%': { backgroundPosition: '0% 50%' },\n        },\n      }}\n    >\n      <Container maxWidth=\"xl\">\n        {/* Main Footer Content */}\n        <Grid container spacing={6} sx={{ mb: 6 }}>\n          {/* Company Info */}\n          <Grid item xs={12} lg={4}>\n            <Box sx={{ mb: 4 }}>\n              <Box display=\"flex\" alignItems=\"center\" gap={2} mb={3}>\n                <Box\n                  sx={{\n                    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                    borderRadius: '50%',\n                    width: 50,\n                    height: 50,\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    boxShadow: '0 8px 25px rgba(102, 126, 234, 0.3)',\n                  }}\n                >\n                  <SecurityIcon sx={{ fontSize: 28, color: 'white' }} />\n                </Box>\n                <Typography variant=\"h4\" fontWeight=\"bold\">\n                  AI Security Guard\n                </Typography>\n              </Box>\n              <Typography\n                variant=\"body1\"\n                sx={{\n                  mb: 4,\n                  opacity: 0.9,\n                  lineHeight: 1.8,\n                  fontSize: '1.1rem',\n                }}\n              >\n                Advanced AI-powered security scanning platform providing comprehensive\n                threat detection and analysis for URLs and files. Protecting your\n                digital assets with cutting-edge technology.\n              </Typography>\n              \n              {/* Security Badges */}\n              <Stack direction=\"row\" spacing={1} flexWrap=\"wrap\" gap={1}>\n                <Chip\n                  icon={<ShieldIcon />}\n                  label=\"SOC 2 Compliant\"\n                  size=\"small\"\n                  sx={{ \n                    bgcolor: 'rgba(76, 175, 80, 0.2)', \n                    color: '#4caf50',\n                    border: '1px solid rgba(76, 175, 80, 0.3)'\n                  }}\n                />\n                <Chip\n                  icon={<VerifiedIcon />}\n                  label=\"ISO 27001\"\n                  size=\"small\"\n                  sx={{ \n                    bgcolor: 'rgba(33, 150, 243, 0.2)', \n                    color: '#2196f3',\n                    border: '1px solid rgba(33, 150, 243, 0.3)'\n                  }}\n                />\n              </Stack>\n            </Box>\n\n            {/* Contact Info */}\n            <Box>\n              <Typography variant=\"h6\" gutterBottom fontWeight=\"600\">\n                Contact Information\n              </Typography>\n              <Stack spacing={1}>\n                <Box display=\"flex\" alignItems=\"center\" gap={1}>\n                  <EmailIcon sx={{ fontSize: 18, opacity: 0.7 }} />\n                  <Typography variant=\"body2\" sx={{ opacity: 0.9 }}>\n                    <EMAIL>\n                  </Typography>\n                </Box>\n                <Box display=\"flex\" alignItems=\"center\" gap={1}>\n                  <PhoneIcon sx={{ fontSize: 18, opacity: 0.7 }} />\n                  <Typography variant=\"body2\" sx={{ opacity: 0.9 }}>\n                    +****************\n                  </Typography>\n                </Box>\n                <Box display=\"flex\" alignItems=\"center\" gap={1}>\n                  <LocationIcon sx={{ fontSize: 18, opacity: 0.7 }} />\n                  <Typography variant=\"body2\" sx={{ opacity: 0.9 }}>\n                    San Francisco, CA, USA\n                  </Typography>\n                </Box>\n              </Stack>\n            </Box>\n          </Grid>\n\n          {/* Footer Links */}\n          {footerSections.map((section, index) => (\n            <Grid item xs={12} sm={6} md={3} key={index}>\n              <Box sx={{ mb: 3 }}>\n                <Typography variant=\"h6\" gutterBottom fontWeight=\"600\" sx={{ mb: 1, color: 'white' }}>\n                  {section.title}\n                </Typography>\n                <Typography variant=\"body2\" sx={{ mb: 3, color: 'rgba(255, 255, 255, 0.6)', fontSize: '0.875rem' }}>\n                  {section.description}\n                </Typography>\n                <Stack spacing={1.5}>\n                  {section.links.map((link, linkIndex) => (\n                    <Link\n                      key={linkIndex}\n                      component=\"button\"\n                      variant=\"body2\"\n                      onClick={() => handleLinkClick(link.path)}\n                      sx={{\n                        color: 'rgba(255, 255, 255, 0.8)',\n                        textDecoration: 'none',\n                        textAlign: 'left',\n                        background: 'none',\n                        border: 'none',\n                        cursor: 'pointer',\n                        padding: 0,\n                        transition: 'all 0.2s ease',\n                        fontSize: '0.9rem',\n                        '&:hover': {\n                          color: '#64b5f6',\n                          textDecoration: 'underline',\n                          transform: 'translateX(4px)',\n                        },\n                      }}\n                    >\n                      {link.label}\n                    </Link>\n                  ))}\n                  {section.contact && (\n                    <Box sx={{ mt: 2, pt: 2, borderTop: '1px solid rgba(255, 255, 255, 0.1)' }}>\n                      <Typography variant=\"body2\" sx={{ color: 'rgba(255, 255, 255, 0.6)', mb: 1 }}>\n                        Email:\n                      </Typography>\n                      <Typography variant=\"body2\" sx={{ color: '#64b5f6', fontWeight: 500 }}>\n                        {section.contact}\n                      </Typography>\n                    </Box>\n                  )}\n                </Stack>\n              </Box>\n            </Grid>\n          ))}\n        </Grid>\n\n        <Divider sx={{ bgcolor: 'rgba(255, 255, 255, 0.1)', mb: 3 }} />\n\n        {/* Bottom Footer */}\n        <Box\n          display=\"flex\"\n          justifyContent=\"space-between\"\n          alignItems=\"center\"\n          flexWrap=\"wrap\"\n          gap={2}\n        >\n          {/* Copyright */}\n          <Box display=\"flex\" alignItems=\"center\" gap={1}>\n            <CopyrightIcon sx={{ fontSize: 16, opacity: 0.7 }} />\n            <Typography variant=\"body2\" sx={{ opacity: 0.8 }}>\n              {currentYear} AI Security Guard. All rights reserved.\n            </Typography>\n          </Box>\n\n          {/* Legal Links */}\n          <Box display=\"flex\" gap={3} flexWrap=\"wrap\">\n            <Link\n              component=\"button\"\n              variant=\"body2\"\n              onClick={() => handleLinkClick('/about')}\n              sx={{\n                color: 'rgba(255, 255, 255, 0.8)',\n                textDecoration: 'none',\n                background: 'none',\n                border: 'none',\n                cursor: 'pointer',\n                '&:hover': { color: '#64b5f6' },\n              }}\n            >\n              Privacy Policy\n            </Link>\n            <Link\n              component=\"button\"\n              variant=\"body2\"\n              onClick={() => handleLinkClick('/about')}\n              sx={{\n                color: 'rgba(255, 255, 255, 0.8)',\n                textDecoration: 'none',\n                background: 'none',\n                border: 'none',\n                cursor: 'pointer',\n                '&:hover': { color: '#64b5f6' },\n              }}\n            >\n              Terms of Service\n            </Link>\n            <Link\n              component=\"button\"\n              variant=\"body2\"\n              onClick={() => handleLinkClick('/about')}\n              sx={{\n                color: 'rgba(255, 255, 255, 0.8)',\n                textDecoration: 'none',\n                background: 'none',\n                border: 'none',\n                cursor: 'pointer',\n                '&:hover': { color: '#64b5f6' },\n              }}\n            >\n              Cookie Policy\n            </Link>\n          </Box>\n\n          {/* Social Links */}\n          <Box display=\"flex\" gap={1}>\n            {socialLinks.map((social, index) => (\n              <IconButton\n                key={index}\n                href={social.url}\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                sx={{\n                  color: 'rgba(255, 255, 255, 0.7)',\n                  background: 'rgba(255, 255, 255, 0.05)',\n                  border: '1px solid rgba(255, 255, 255, 0.1)',\n                  borderRadius: 2,\n                  width: 48,\n                  height: 48,\n                  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',\n                  '&:hover': {\n                    color: social.color,\n                    background: `linear-gradient(135deg, ${social.color}15, ${social.color}25)`,\n                    border: `1px solid ${social.color}40`,\n                    transform: 'translateY(-4px) scale(1.1)',\n                    boxShadow: `0 8px 25px ${social.color}30`,\n                  },\n                }}\n                aria-label={social.label}\n              >\n                {social.icon}\n              </IconButton>\n            ))}\n          </Box>\n        </Box>\n\n        {/* Trust Indicators */}\n        <Box\n          display=\"flex\"\n          justifyContent=\"center\"\n          alignItems=\"center\"\n          gap={2}\n          mt={3}\n          pt={3}\n          sx={{ borderTop: '1px solid rgba(255, 255, 255, 0.1)' }}\n        >\n          <Typography variant=\"caption\" sx={{ opacity: 0.6, textAlign: 'center' }}>\n            Trusted by 10,000+ organizations worldwide • 99.9% uptime • 24/7 security monitoring\n          </Typography>\n        </Box>\n      </Container>\n    </Box>\n  );\n});\n\nFooter.displayName = 'Footer';\n\nexport default Footer;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SACEC,GAAG,EACHC,SAAS,EACTC,IAAI,EACJC,UAAU,EACVC,IAAI,EACJC,UAAU,EACVC,OAAO,EACPC,KAAK,EACLC,IAAI,QACC,eAAe;AACtB,SACEC,QAAQ,IAAIC,YAAY,EACxBC,KAAK,IAAIC,SAAS,EAClBC,KAAK,IAAIC,SAAS,EAClBC,UAAU,IAAIC,YAAY,EAC1BC,MAAM,IAAIC,UAAU,EACpBC,QAAQ,IAAIC,YAAY,EACxBC,OAAO,IAAIC,WAAW,EACtBC,QAAQ,IAAIC,YAAY,EACxBC,MAAM,IAAIC,UAAU,EACpBC,QAAQ,IAAIC,YAAY,EACxBC,SAAS,IAAIC,aAAa,EAC1BC,IAAI,IAAIC,WAAW,QACd,qBAAqB;AAC5B,SAASC,WAAW,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/C,MAAMC,MAAM,gBAAAC,EAAA,cAAGtC,KAAK,CAACuC,IAAI,CAAAC,EAAA,GAAAF,EAAA,CAAC,MAAM;EAAAA,EAAA;EAC9B,MAAMG,QAAQ,GAAGP,WAAW,CAAC,CAAC;EAC9B,MAAMQ,WAAW,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;EAE5C,MAAMC,cAAc,GAAG,CACrB;IACEC,KAAK,EAAE,SAAS;IAChBC,WAAW,EAAE,wDAAwD;IACrEC,KAAK,EAAE,CACL;MAAEC,KAAK,EAAE,UAAU;MAAEC,IAAI,EAAE;IAAS,CAAC,EACrC;MAAED,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAS,CAAC,EACpC;MAAED,KAAK,EAAE,eAAe;MAAEC,IAAI,EAAE;IAAS,CAAC,EAC1C;MAAED,KAAK,EAAE,aAAa;MAAEC,IAAI,EAAE;IAAS,CAAC,EACxC;MAAED,KAAK,EAAE,UAAU;MAAEC,IAAI,EAAE;IAAS,CAAC;EAEzC,CAAC,EACD;IACEJ,KAAK,EAAE,UAAU;IACjBC,WAAW,EAAE,kDAAkD;IAC/DC,KAAK,EAAE,CACL;MAAEC,KAAK,EAAE,sBAAsB;MAAEC,IAAI,EAAE;IAAI,CAAC,EAC5C;MAAED,KAAK,EAAE,yBAAyB;MAAEC,IAAI,EAAE;IAAkB,CAAC,EAC7D;MAAED,KAAK,EAAE,yBAAyB;MAAEC,IAAI,EAAE;IAAkB,CAAC,EAC7D;MAAED,KAAK,EAAE,yBAAyB;MAAEC,IAAI,EAAE;IAAkB,CAAC,EAC7D;MAAED,KAAK,EAAE,uBAAuB;MAAEC,IAAI,EAAE;IAAkB,CAAC;EAE/D,CAAC,EACD;IACEJ,KAAK,EAAE,WAAW;IAClBC,WAAW,EAAE,sDAAsD;IACnEC,KAAK,EAAE,CACL;MAAEC,KAAK,EAAE,aAAa;MAAEC,IAAI,EAAE;IAAS,CAAC,EACxC;MAAED,KAAK,EAAE,eAAe;MAAEC,IAAI,EAAE;IAAS,CAAC,EAC1C;MAAED,KAAK,EAAE,eAAe;MAAEC,IAAI,EAAE;IAAS,CAAC,EAC1C;MAAED,KAAK,EAAE,WAAW;MAAEC,IAAI,EAAE;IAAS,CAAC,EACtC;MAAED,KAAK,EAAE,iBAAiB;MAAEC,IAAI,EAAE;IAAS,CAAC;EAEhD,CAAC,EACD;IACEJ,KAAK,EAAE,iBAAiB;IACxBC,WAAW,EAAE,2CAA2C;IACxDC,KAAK,EAAE,CACL;MAAEC,KAAK,EAAE,gBAAgB;MAAEC,IAAI,EAAE;IAAS,CAAC,EAC3C;MAAED,KAAK,EAAE,kBAAkB;MAAEC,IAAI,EAAE;IAAS,CAAC,EAC7C;MAAED,KAAK,EAAE,iBAAiB;MAAEC,IAAI,EAAE;IAAS,CAAC,EAC5C;MAAED,KAAK,EAAE,YAAY;MAAEC,IAAI,EAAE;IAAS,CAAC,CACxC;IACDC,OAAO,EAAE;EACX,CAAC,CACF;EAED,MAAMC,WAAW,GAAG,CAClB;IACEC,IAAI,eAAEjB,OAAA,CAACjB,UAAU;MAAAmC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACpBR,KAAK,EAAE,QAAQ;IACfS,GAAG,EAAE,oBAAoB;IACzBC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,IAAI,eAAEjB,OAAA,CAACf,YAAY;MAAAiC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBR,KAAK,EAAE,UAAU;IACjBS,GAAG,EAAE,sBAAsB;IAC3BC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,IAAI,eAAEjB,OAAA,CAACb,WAAW;MAAA+B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACrBR,KAAK,EAAE,SAAS;IAChBS,GAAG,EAAE,qBAAqB;IAC1BC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,IAAI,eAAEjB,OAAA,CAACH,WAAW;MAAAqB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACrBR,KAAK,EAAE,SAAS;IAChBS,GAAG,EAAE,qBAAqB;IAC1BC,KAAK,EAAE;EACT,CAAC,CACF;EAED,MAAMC,eAAe,GAAIV,IAAI,IAAK;IAChCT,QAAQ,CAACS,IAAI,CAAC;EAChB,CAAC;EAED,oBACEd,OAAA,CAACnC,GAAG;IACF4D,SAAS,EAAC,QAAQ;IAClBC,EAAE,EAAE;MACFC,UAAU,EAAGC,KAAK,IAAKA,KAAK,CAACC,OAAO,CAACC,IAAI,KAAK,MAAM,GAChD,6GAA6G,GAC7G,8GAA8G;MAClHC,cAAc,EAAE,2BAA2B;MAC3CR,KAAK,EAAE,OAAO;MACdS,EAAE,EAAE,MAAM;MACVC,EAAE,EAAE,CAAC;MACLC,QAAQ,EAAE,UAAU;MACpBC,QAAQ,EAAE,QAAQ;MAClB,WAAW,EAAE;QACXC,OAAO,EAAE,IAAI;QACbF,QAAQ,EAAE,UAAU;QACpBG,GAAG,EAAE,CAAC;QACNC,IAAI,EAAE,CAAC;QACPC,KAAK,EAAE,CAAC;QACRC,MAAM,EAAE,KAAK;QACbb,UAAU,EAAE,sGAAsG;QAClHc,cAAc,EAAE,WAAW;QAC3BC,SAAS,EAAE;MACb,CAAC;MACD,2BAA2B,EAAE;QAC3B,IAAI,EAAE;UAAEC,kBAAkB,EAAE;QAAS,CAAC;QACtC,KAAK,EAAE;UAAEA,kBAAkB,EAAE;QAAW,CAAC;QACzC,MAAM,EAAE;UAAEA,kBAAkB,EAAE;QAAS;MACzC;IACF,CAAE;IAAAC,QAAA,eAEF5C,OAAA,CAAClC,SAAS;MAAC+E,QAAQ,EAAC,IAAI;MAAAD,QAAA,gBAEtB5C,OAAA,CAACjC,IAAI;QAAC+E,SAAS;QAACC,OAAO,EAAE,CAAE;QAACrB,EAAE,EAAE;UAAEsB,EAAE,EAAE;QAAE,CAAE;QAAAJ,QAAA,gBAExC5C,OAAA,CAACjC,IAAI;UAACkF,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAP,QAAA,gBACvB5C,OAAA,CAACnC,GAAG;YAAC6D,EAAE,EAAE;cAAEsB,EAAE,EAAE;YAAE,CAAE;YAAAJ,QAAA,gBACjB5C,OAAA,CAACnC,GAAG;cAACuF,OAAO,EAAC,MAAM;cAACC,UAAU,EAAC,QAAQ;cAACC,GAAG,EAAE,CAAE;cAACN,EAAE,EAAE,CAAE;cAAAJ,QAAA,gBACpD5C,OAAA,CAACnC,GAAG;gBACF6D,EAAE,EAAE;kBACFC,UAAU,EAAE,mDAAmD;kBAC/D4B,YAAY,EAAE,KAAK;kBACnBC,KAAK,EAAE,EAAE;kBACThB,MAAM,EAAE,EAAE;kBACVY,OAAO,EAAE,MAAM;kBACfC,UAAU,EAAE,QAAQ;kBACpBI,cAAc,EAAE,QAAQ;kBACxBC,SAAS,EAAE;gBACb,CAAE;gBAAAd,QAAA,eAEF5C,OAAA,CAACzB,YAAY;kBAACmD,EAAE,EAAE;oBAAEiC,QAAQ,EAAE,EAAE;oBAAEpC,KAAK,EAAE;kBAAQ;gBAAE;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnD,CAAC,eACNrB,OAAA,CAAChC,UAAU;gBAAC4F,OAAO,EAAC,IAAI;gBAACC,UAAU,EAAC,MAAM;gBAAAjB,QAAA,EAAC;cAE3C;gBAAA1B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACNrB,OAAA,CAAChC,UAAU;cACT4F,OAAO,EAAC,OAAO;cACflC,EAAE,EAAE;gBACFsB,EAAE,EAAE,CAAC;gBACLc,OAAO,EAAE,GAAG;gBACZC,UAAU,EAAE,GAAG;gBACfJ,QAAQ,EAAE;cACZ,CAAE;cAAAf,QAAA,EACH;YAID;cAAA1B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAGbrB,OAAA,CAAC5B,KAAK;cAAC4F,SAAS,EAAC,KAAK;cAACjB,OAAO,EAAE,CAAE;cAACkB,QAAQ,EAAC,MAAM;cAACX,GAAG,EAAE,CAAE;cAAAV,QAAA,gBACxD5C,OAAA,CAAC3B,IAAI;gBACH4C,IAAI,eAAEjB,OAAA,CAACT,UAAU;kBAAA2B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACrBR,KAAK,EAAC,iBAAiB;gBACvBqD,IAAI,EAAC,OAAO;gBACZxC,EAAE,EAAE;kBACFyC,OAAO,EAAE,wBAAwB;kBACjC5C,KAAK,EAAE,SAAS;kBAChB6C,MAAM,EAAE;gBACV;cAAE;gBAAAlD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACFrB,OAAA,CAAC3B,IAAI;gBACH4C,IAAI,eAAEjB,OAAA,CAACP,YAAY;kBAAAyB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACvBR,KAAK,EAAC,WAAW;gBACjBqD,IAAI,EAAC,OAAO;gBACZxC,EAAE,EAAE;kBACFyC,OAAO,EAAE,yBAAyB;kBAClC5C,KAAK,EAAE,SAAS;kBAChB6C,MAAM,EAAE;gBACV;cAAE;gBAAAlD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAGNrB,OAAA,CAACnC,GAAG;YAAA+E,QAAA,gBACF5C,OAAA,CAAChC,UAAU;cAAC4F,OAAO,EAAC,IAAI;cAACS,YAAY;cAACR,UAAU,EAAC,KAAK;cAAAjB,QAAA,EAAC;YAEvD;cAAA1B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbrB,OAAA,CAAC5B,KAAK;cAAC2E,OAAO,EAAE,CAAE;cAAAH,QAAA,gBAChB5C,OAAA,CAACnC,GAAG;gBAACuF,OAAO,EAAC,MAAM;gBAACC,UAAU,EAAC,QAAQ;gBAACC,GAAG,EAAE,CAAE;gBAAAV,QAAA,gBAC7C5C,OAAA,CAACvB,SAAS;kBAACiD,EAAE,EAAE;oBAAEiC,QAAQ,EAAE,EAAE;oBAAEG,OAAO,EAAE;kBAAI;gBAAE;kBAAA5C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACjDrB,OAAA,CAAChC,UAAU;kBAAC4F,OAAO,EAAC,OAAO;kBAAClC,EAAE,EAAE;oBAAEoC,OAAO,EAAE;kBAAI,CAAE;kBAAAlB,QAAA,EAAC;gBAElD;kBAAA1B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACNrB,OAAA,CAACnC,GAAG;gBAACuF,OAAO,EAAC,MAAM;gBAACC,UAAU,EAAC,QAAQ;gBAACC,GAAG,EAAE,CAAE;gBAAAV,QAAA,gBAC7C5C,OAAA,CAACrB,SAAS;kBAAC+C,EAAE,EAAE;oBAAEiC,QAAQ,EAAE,EAAE;oBAAEG,OAAO,EAAE;kBAAI;gBAAE;kBAAA5C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACjDrB,OAAA,CAAChC,UAAU;kBAAC4F,OAAO,EAAC,OAAO;kBAAClC,EAAE,EAAE;oBAAEoC,OAAO,EAAE;kBAAI,CAAE;kBAAAlB,QAAA,EAAC;gBAElD;kBAAA1B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACNrB,OAAA,CAACnC,GAAG;gBAACuF,OAAO,EAAC,MAAM;gBAACC,UAAU,EAAC,QAAQ;gBAACC,GAAG,EAAE,CAAE;gBAAAV,QAAA,gBAC7C5C,OAAA,CAACnB,YAAY;kBAAC6C,EAAE,EAAE;oBAAEiC,QAAQ,EAAE,EAAE;oBAAEG,OAAO,EAAE;kBAAI;gBAAE;kBAAA5C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACpDrB,OAAA,CAAChC,UAAU;kBAAC4F,OAAO,EAAC,OAAO;kBAAClC,EAAE,EAAE;oBAAEoC,OAAO,EAAE;kBAAI,CAAE;kBAAAlB,QAAA,EAAC;gBAElD;kBAAA1B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,EAGNZ,cAAc,CAAC6D,GAAG,CAAC,CAACC,OAAO,EAAEC,KAAK,kBACjCxE,OAAA,CAACjC,IAAI;UAACkF,IAAI;UAACC,EAAE,EAAE,EAAG;UAACuB,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAA9B,QAAA,eAC9B5C,OAAA,CAACnC,GAAG;YAAC6D,EAAE,EAAE;cAAEsB,EAAE,EAAE;YAAE,CAAE;YAAAJ,QAAA,gBACjB5C,OAAA,CAAChC,UAAU;cAAC4F,OAAO,EAAC,IAAI;cAACS,YAAY;cAACR,UAAU,EAAC,KAAK;cAACnC,EAAE,EAAE;gBAAEsB,EAAE,EAAE,CAAC;gBAAEzB,KAAK,EAAE;cAAQ,CAAE;cAAAqB,QAAA,EAClF2B,OAAO,CAAC7D;YAAK;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACbrB,OAAA,CAAChC,UAAU;cAAC4F,OAAO,EAAC,OAAO;cAAClC,EAAE,EAAE;gBAAEsB,EAAE,EAAE,CAAC;gBAAEzB,KAAK,EAAE,0BAA0B;gBAAEoC,QAAQ,EAAE;cAAW,CAAE;cAAAf,QAAA,EAChG2B,OAAO,CAAC5D;YAAW;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACbrB,OAAA,CAAC5B,KAAK;cAAC2E,OAAO,EAAE,GAAI;cAAAH,QAAA,GACjB2B,OAAO,CAAC3D,KAAK,CAAC0D,GAAG,CAAC,CAACK,IAAI,EAAEC,SAAS,kBACjC5E,OAAA,CAAC/B,IAAI;gBAEHwD,SAAS,EAAC,QAAQ;gBAClBmC,OAAO,EAAC,OAAO;gBACfiB,OAAO,EAAEA,CAAA,KAAMrD,eAAe,CAACmD,IAAI,CAAC7D,IAAI,CAAE;gBAC1CY,EAAE,EAAE;kBACFH,KAAK,EAAE,0BAA0B;kBACjCuD,cAAc,EAAE,MAAM;kBACtBC,SAAS,EAAE,MAAM;kBACjBpD,UAAU,EAAE,MAAM;kBAClByC,MAAM,EAAE,MAAM;kBACdY,MAAM,EAAE,SAAS;kBACjBC,OAAO,EAAE,CAAC;kBACVC,UAAU,EAAE,eAAe;kBAC3BvB,QAAQ,EAAE,QAAQ;kBAClB,SAAS,EAAE;oBACTpC,KAAK,EAAE,SAAS;oBAChBuD,cAAc,EAAE,WAAW;oBAC3BK,SAAS,EAAE;kBACb;gBACF,CAAE;gBAAAvC,QAAA,EAED+B,IAAI,CAAC9D;cAAK,GArBN+D,SAAS;gBAAA1D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAsBV,CACP,CAAC,EACDkD,OAAO,CAACxD,OAAO,iBACdf,OAAA,CAACnC,GAAG;gBAAC6D,EAAE,EAAE;kBAAEM,EAAE,EAAE,CAAC;kBAAEoD,EAAE,EAAE,CAAC;kBAAEC,SAAS,EAAE;gBAAqC,CAAE;gBAAAzC,QAAA,gBACzE5C,OAAA,CAAChC,UAAU;kBAAC4F,OAAO,EAAC,OAAO;kBAAClC,EAAE,EAAE;oBAAEH,KAAK,EAAE,0BAA0B;oBAAEyB,EAAE,EAAE;kBAAE,CAAE;kBAAAJ,QAAA,EAAC;gBAE9E;kBAAA1B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbrB,OAAA,CAAChC,UAAU;kBAAC4F,OAAO,EAAC,OAAO;kBAAClC,EAAE,EAAE;oBAAEH,KAAK,EAAE,SAAS;oBAAEsC,UAAU,EAAE;kBAAI,CAAE;kBAAAjB,QAAA,EACnE2B,OAAO,CAACxD;gBAAO;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC,GA9C8BmD,KAAK;UAAAtD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA+CrC,CACP,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAEPrB,OAAA,CAAC7B,OAAO;QAACuD,EAAE,EAAE;UAAEyC,OAAO,EAAE,0BAA0B;UAAEnB,EAAE,EAAE;QAAE;MAAE;QAAA9B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAG/DrB,OAAA,CAACnC,GAAG;QACFuF,OAAO,EAAC,MAAM;QACdK,cAAc,EAAC,eAAe;QAC9BJ,UAAU,EAAC,QAAQ;QACnBY,QAAQ,EAAC,MAAM;QACfX,GAAG,EAAE,CAAE;QAAAV,QAAA,gBAGP5C,OAAA,CAACnC,GAAG;UAACuF,OAAO,EAAC,MAAM;UAACC,UAAU,EAAC,QAAQ;UAACC,GAAG,EAAE,CAAE;UAAAV,QAAA,gBAC7C5C,OAAA,CAACL,aAAa;YAAC+B,EAAE,EAAE;cAAEiC,QAAQ,EAAE,EAAE;cAAEG,OAAO,EAAE;YAAI;UAAE;YAAA5C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACrDrB,OAAA,CAAChC,UAAU;YAAC4F,OAAO,EAAC,OAAO;YAAClC,EAAE,EAAE;cAAEoC,OAAO,EAAE;YAAI,CAAE;YAAAlB,QAAA,GAC9CtC,WAAW,EAAC,0CACf;UAAA;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAGNrB,OAAA,CAACnC,GAAG;UAACuF,OAAO,EAAC,MAAM;UAACE,GAAG,EAAE,CAAE;UAACW,QAAQ,EAAC,MAAM;UAAArB,QAAA,gBACzC5C,OAAA,CAAC/B,IAAI;YACHwD,SAAS,EAAC,QAAQ;YAClBmC,OAAO,EAAC,OAAO;YACfiB,OAAO,EAAEA,CAAA,KAAMrD,eAAe,CAAC,QAAQ,CAAE;YACzCE,EAAE,EAAE;cACFH,KAAK,EAAE,0BAA0B;cACjCuD,cAAc,EAAE,MAAM;cACtBnD,UAAU,EAAE,MAAM;cAClByC,MAAM,EAAE,MAAM;cACdY,MAAM,EAAE,SAAS;cACjB,SAAS,EAAE;gBAAEzD,KAAK,EAAE;cAAU;YAChC,CAAE;YAAAqB,QAAA,EACH;UAED;YAAA1B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACPrB,OAAA,CAAC/B,IAAI;YACHwD,SAAS,EAAC,QAAQ;YAClBmC,OAAO,EAAC,OAAO;YACfiB,OAAO,EAAEA,CAAA,KAAMrD,eAAe,CAAC,QAAQ,CAAE;YACzCE,EAAE,EAAE;cACFH,KAAK,EAAE,0BAA0B;cACjCuD,cAAc,EAAE,MAAM;cACtBnD,UAAU,EAAE,MAAM;cAClByC,MAAM,EAAE,MAAM;cACdY,MAAM,EAAE,SAAS;cACjB,SAAS,EAAE;gBAAEzD,KAAK,EAAE;cAAU;YAChC,CAAE;YAAAqB,QAAA,EACH;UAED;YAAA1B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACPrB,OAAA,CAAC/B,IAAI;YACHwD,SAAS,EAAC,QAAQ;YAClBmC,OAAO,EAAC,OAAO;YACfiB,OAAO,EAAEA,CAAA,KAAMrD,eAAe,CAAC,QAAQ,CAAE;YACzCE,EAAE,EAAE;cACFH,KAAK,EAAE,0BAA0B;cACjCuD,cAAc,EAAE,MAAM;cACtBnD,UAAU,EAAE,MAAM;cAClByC,MAAM,EAAE,MAAM;cACdY,MAAM,EAAE,SAAS;cACjB,SAAS,EAAE;gBAAEzD,KAAK,EAAE;cAAU;YAChC,CAAE;YAAAqB,QAAA,EACH;UAED;YAAA1B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAGNrB,OAAA,CAACnC,GAAG;UAACuF,OAAO,EAAC,MAAM;UAACE,GAAG,EAAE,CAAE;UAAAV,QAAA,EACxB5B,WAAW,CAACsD,GAAG,CAAC,CAACgB,MAAM,EAAEd,KAAK,kBAC7BxE,OAAA,CAAC9B,UAAU;YAETqH,IAAI,EAAED,MAAM,CAAChE,GAAI;YACjBkE,MAAM,EAAC,QAAQ;YACfC,GAAG,EAAC,qBAAqB;YACzB/D,EAAE,EAAE;cACFH,KAAK,EAAE,0BAA0B;cACjCI,UAAU,EAAE,2BAA2B;cACvCyC,MAAM,EAAE,oCAAoC;cAC5Cb,YAAY,EAAE,CAAC;cACfC,KAAK,EAAE,EAAE;cACThB,MAAM,EAAE,EAAE;cACV0C,UAAU,EAAE,uCAAuC;cACnD,SAAS,EAAE;gBACT3D,KAAK,EAAE+D,MAAM,CAAC/D,KAAK;gBACnBI,UAAU,EAAE,2BAA2B2D,MAAM,CAAC/D,KAAK,OAAO+D,MAAM,CAAC/D,KAAK,KAAK;gBAC3E6C,MAAM,EAAE,aAAakB,MAAM,CAAC/D,KAAK,IAAI;gBACrC4D,SAAS,EAAE,6BAA6B;gBACxCzB,SAAS,EAAE,cAAc4B,MAAM,CAAC/D,KAAK;cACvC;YACF,CAAE;YACF,cAAY+D,MAAM,CAACzE,KAAM;YAAA+B,QAAA,EAExB0C,MAAM,CAACrE;UAAI,GAtBPuD,KAAK;YAAAtD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAuBA,CACb;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNrB,OAAA,CAACnC,GAAG;QACFuF,OAAO,EAAC,MAAM;QACdK,cAAc,EAAC,QAAQ;QACvBJ,UAAU,EAAC,QAAQ;QACnBC,GAAG,EAAE,CAAE;QACPtB,EAAE,EAAE,CAAE;QACNoD,EAAE,EAAE,CAAE;QACN1D,EAAE,EAAE;UAAE2D,SAAS,EAAE;QAAqC,CAAE;QAAAzC,QAAA,eAExD5C,OAAA,CAAChC,UAAU;UAAC4F,OAAO,EAAC,SAAS;UAAClC,EAAE,EAAE;YAAEoC,OAAO,EAAE,GAAG;YAAEiB,SAAS,EAAE;UAAS,CAAE;UAAAnC,QAAA,EAAC;QAEzE;UAAA1B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACT,CAAC;AAEV,CAAC;EAAA,QArXkBvB,WAAW;AAAA,EAqX7B,CAAC;EAAA,QArXiBA,WAAW;AAAA,EAqX5B;AAAC4F,GAAA,GAtXGzF,MAAM;AAwXZA,MAAM,CAAC0F,WAAW,GAAG,QAAQ;AAE7B,eAAe1F,MAAM;AAAC,IAAAG,EAAA,EAAAsF,GAAA;AAAAE,YAAA,CAAAxF,EAAA;AAAAwF,YAAA,CAAAF,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}