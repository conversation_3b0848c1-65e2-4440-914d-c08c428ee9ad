{"ast": null, "code": "import jsPDF from 'jspdf';\nimport html2canvas from 'html2canvas';\n\n// Report generation service\nexport class ReportService {\n  static async generatePDFReport(scanResult, options = {}) {\n    try {\n      const {\n        title = 'Security Scan Report',\n        includeCharts = true,\n        includeRecommendations = true,\n        format = 'a4'\n      } = options;\n      const pdf = new jsPDF({\n        orientation: 'portrait',\n        unit: 'mm',\n        format: format\n      });\n\n      // Set up document properties\n      pdf.setProperties({\n        title: title,\n        subject: 'Security Analysis Report',\n        author: 'AI Security Guard',\n        creator: 'AI Security Guard Platform',\n        keywords: 'security, scan, report, analysis'\n      });\n      let yPosition = 20;\n      const pageWidth = pdf.internal.pageSize.getWidth();\n      const margin = 20;\n      const contentWidth = pageWidth - margin * 2;\n\n      // Header\n      pdf.setFontSize(24);\n      pdf.setFont('helvetica', 'bold');\n      pdf.setTextColor(25, 118, 210);\n      pdf.text(title, margin, yPosition);\n      yPosition += 15;\n\n      // Subtitle\n      pdf.setFontSize(12);\n      pdf.setFont('helvetica', 'normal');\n      pdf.setTextColor(100, 100, 100);\n      pdf.text(`Generated on ${new Date().toLocaleString()}`, margin, yPosition);\n      yPosition += 20;\n\n      // Scan Summary\n      pdf.setFontSize(16);\n      pdf.setFont('helvetica', 'bold');\n      pdf.setTextColor(0, 0, 0);\n      pdf.text('Scan Summary', margin, yPosition);\n      yPosition += 10;\n      pdf.setFontSize(11);\n      pdf.setFont('helvetica', 'normal');\n      const summaryData = [['Target:', scanResult.target || 'N/A'], ['Scan Type:', scanResult.type || 'N/A'], ['Status:', scanResult.isSafe ? 'Safe' : 'Threats Detected'], ['Threat Level:', scanResult.threatLevel || 'Unknown'], ['Scan Date:', new Date(scanResult.timestamp).toLocaleString()], ['Duration:', scanResult.duration || 'N/A']];\n      summaryData.forEach(([label, value]) => {\n        pdf.setFont('helvetica', 'bold');\n        pdf.text(label, margin, yPosition);\n        pdf.setFont('helvetica', 'normal');\n        pdf.text(value, margin + 40, yPosition);\n        yPosition += 7;\n      });\n      yPosition += 10;\n\n      // Threat Analysis\n      if (scanResult.threats && scanResult.threats.length > 0) {\n        pdf.setFontSize(16);\n        pdf.setFont('helvetica', 'bold');\n        pdf.setTextColor(244, 67, 54);\n        pdf.text('Threats Detected', margin, yPosition);\n        yPosition += 10;\n        scanResult.threats.forEach((threat, index) => {\n          pdf.setFontSize(12);\n          pdf.setFont('helvetica', 'bold');\n          pdf.setTextColor(0, 0, 0);\n          pdf.text(`${index + 1}. ${threat.name || 'Unknown Threat'}`, margin, yPosition);\n          yPosition += 7;\n          pdf.setFontSize(10);\n          pdf.setFont('helvetica', 'normal');\n          pdf.setTextColor(100, 100, 100);\n          const description = threat.description || 'No description available';\n          const lines = pdf.splitTextToSize(description, contentWidth - 10);\n          pdf.text(lines, margin + 5, yPosition);\n          yPosition += lines.length * 5 + 5;\n          if (threat.severity) {\n            pdf.setFont('helvetica', 'bold');\n            pdf.text(`Severity: ${threat.severity.toUpperCase()}`, margin + 5, yPosition);\n            yPosition += 7;\n          }\n          yPosition += 5;\n        });\n      }\n\n      // Recommendations\n      if (includeRecommendations && scanResult.recommendations && scanResult.recommendations.length > 0) {\n        yPosition += 10;\n        pdf.setFontSize(16);\n        pdf.setFont('helvetica', 'bold');\n        pdf.setTextColor(76, 175, 80);\n        pdf.text('Security Recommendations', margin, yPosition);\n        yPosition += 10;\n        scanResult.recommendations.forEach((rec, index) => {\n          pdf.setFontSize(11);\n          pdf.setFont('helvetica', 'normal');\n          pdf.setTextColor(0, 0, 0);\n          const recText = typeof rec === 'string' ? rec : rec.title || rec;\n          const lines = pdf.splitTextToSize(`${index + 1}. ${recText}`, contentWidth - 10);\n          pdf.text(lines, margin, yPosition);\n          yPosition += lines.length * 5 + 3;\n        });\n      }\n\n      // Technical Details\n      if (scanResult.details && scanResult.details.length > 0) {\n        yPosition += 15;\n        pdf.setFontSize(16);\n        pdf.setFont('helvetica', 'bold');\n        pdf.setTextColor(0, 0, 0);\n        pdf.text('Technical Details', margin, yPosition);\n        yPosition += 10;\n        scanResult.details.forEach(detail => {\n          pdf.setFontSize(12);\n          pdf.setFont('helvetica', 'bold');\n          pdf.text(detail.title || 'Detail', margin, yPosition);\n          yPosition += 7;\n          pdf.setFontSize(10);\n          pdf.setFont('helvetica', 'normal');\n          pdf.setTextColor(100, 100, 100);\n          const description = detail.description || detail.value || 'No details available';\n          const lines = pdf.splitTextToSize(description, contentWidth - 10);\n          pdf.text(lines, margin + 5, yPosition);\n          yPosition += lines.length * 5 + 8;\n        });\n      }\n\n      // Footer\n      const pageHeight = pdf.internal.pageSize.getHeight();\n      pdf.setFontSize(8);\n      pdf.setFont('helvetica', 'normal');\n      pdf.setTextColor(150, 150, 150);\n      pdf.text('Generated by AI Security Guard - Professional Security Platform', margin, pageHeight - 10);\n      pdf.text(`Report ID: ${Date.now()}`, pageWidth - margin - 50, pageHeight - 10);\n      return pdf;\n    } catch (error) {\n      console.error('Error generating PDF report:', error);\n      throw new Error('Failed to generate PDF report');\n    }\n  }\n  static async downloadReport(scanResult, filename) {\n    try {\n      const pdf = await this.generatePDFReport(scanResult);\n      const reportFilename = filename || `security-report-${Date.now()}.pdf`;\n      pdf.save(reportFilename);\n      return {\n        success: true,\n        filename: reportFilename\n      };\n    } catch (error) {\n      console.error('Error downloading report:', error);\n      return {\n        success: false,\n        error: error.message\n      };\n    }\n  }\n  static async shareReport(scanResult, method = 'link') {\n    try {\n      switch (method) {\n        case 'link':\n          return await this.generateShareableLink(scanResult);\n        case 'email':\n          return await this.shareViaEmail(scanResult);\n        case 'copy':\n          return await this.copyToClipboard(scanResult);\n        default:\n          throw new Error('Unsupported sharing method');\n      }\n    } catch (error) {\n      console.error('Error sharing report:', error);\n      return {\n        success: false,\n        error: error.message\n      };\n    }\n  }\n  static async generateShareableLink(scanResult) {\n    var _scanResult$threats;\n    // In a real application, this would upload the report to a server\n    // and return a shareable link\n    const reportData = {\n      id: Date.now(),\n      timestamp: new Date().toISOString(),\n      summary: {\n        target: scanResult.target,\n        isSafe: scanResult.isSafe,\n        threatLevel: scanResult.threatLevel,\n        threatsCount: ((_scanResult$threats = scanResult.threats) === null || _scanResult$threats === void 0 ? void 0 : _scanResult$threats.length) || 0\n      }\n    };\n\n    // Simulate API call\n    await new Promise(resolve => setTimeout(resolve, 1000));\n    const shareableLink = `https://aisecurityguard.com/reports/${reportData.id}`;\n    return {\n      success: true,\n      link: shareableLink,\n      expiresIn: '7 days'\n    };\n  }\n  static async shareViaEmail(scanResult) {\n    var _scanResult$threats2;\n    const subject = `Security Scan Report - ${scanResult.target}`;\n    const body = `\nSecurity Scan Report\n\nTarget: ${scanResult.target}\nStatus: ${scanResult.isSafe ? 'Safe' : 'Threats Detected'}\nThreat Level: ${scanResult.threatLevel || 'Unknown'}\nScan Date: ${new Date(scanResult.timestamp).toLocaleString()}\n\n${((_scanResult$threats2 = scanResult.threats) === null || _scanResult$threats2 === void 0 ? void 0 : _scanResult$threats2.length) > 0 ? `Threats Found: ${scanResult.threats.length}` : 'No threats detected'}\n\nGenerated by AI Security Guard\nVisit: https://aisecurityguard.com\n    `.trim();\n    const mailtoLink = `mailto:?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;\n    window.open(mailtoLink);\n    return {\n      success: true,\n      method: 'email'\n    };\n  }\n  static async copyToClipboard(scanResult) {\n    var _scanResult$threats3, _scanResult$recommend;\n    const reportText = `\nAI Security Guard - Scan Report\n\nTarget: ${scanResult.target}\nStatus: ${scanResult.isSafe ? 'Safe' : 'Threats Detected'}\nThreat Level: ${scanResult.threatLevel || 'Unknown'}\nScan Date: ${new Date(scanResult.timestamp).toLocaleString()}\nDuration: ${scanResult.duration || 'N/A'}\n\n${((_scanResult$threats3 = scanResult.threats) === null || _scanResult$threats3 === void 0 ? void 0 : _scanResult$threats3.length) > 0 ? `Threats Detected (${scanResult.threats.length}):\\n${scanResult.threats.map((t, i) => `${i + 1}. ${t.name}: ${t.description}`).join('\\n')}` : 'No threats detected'}\n\n${((_scanResult$recommend = scanResult.recommendations) === null || _scanResult$recommend === void 0 ? void 0 : _scanResult$recommend.length) > 0 ? `\\nRecommendations:\\n${scanResult.recommendations.map((r, i) => `${i + 1}. ${typeof r === 'string' ? r : r.title || r}`).join('\\n')}` : ''}\n\nGenerated by AI Security Guard\n    `.trim();\n    try {\n      await navigator.clipboard.writeText(reportText);\n      return {\n        success: true,\n        method: 'clipboard'\n      };\n    } catch (error) {\n      // Fallback for older browsers\n      const textArea = document.createElement('textarea');\n      textArea.value = reportText;\n      document.body.appendChild(textArea);\n      textArea.select();\n      document.execCommand('copy');\n      document.body.removeChild(textArea);\n      return {\n        success: true,\n        method: 'clipboard'\n      };\n    }\n  }\n  static generateReportSummary(scanResult) {\n    var _scanResult$threats4, _scanResult$vulnerabi, _scanResult$recommend2;\n    return {\n      target: scanResult.target,\n      type: scanResult.type,\n      timestamp: scanResult.timestamp,\n      isSafe: scanResult.isSafe,\n      threatLevel: scanResult.threatLevel,\n      threatsCount: ((_scanResult$threats4 = scanResult.threats) === null || _scanResult$threats4 === void 0 ? void 0 : _scanResult$threats4.length) || 0,\n      vulnerabilitiesCount: ((_scanResult$vulnerabi = scanResult.vulnerabilities) === null || _scanResult$vulnerabi === void 0 ? void 0 : _scanResult$vulnerabi.length) || 0,\n      recommendationsCount: ((_scanResult$recommend2 = scanResult.recommendations) === null || _scanResult$recommend2 === void 0 ? void 0 : _scanResult$recommend2.length) || 0,\n      duration: scanResult.duration\n    };\n  }\n}\nexport default ReportService;", "map": {"version": 3, "names": ["jsPDF", "html2canvas", "ReportService", "generatePDFReport", "scanResult", "options", "title", "<PERSON><PERSON><PERSON><PERSON>", "includeRecommendations", "format", "pdf", "orientation", "unit", "setProperties", "subject", "author", "creator", "keywords", "yPosition", "pageWidth", "internal", "pageSize", "getWidth", "margin", "contentWidth", "setFontSize", "setFont", "setTextColor", "text", "Date", "toLocaleString", "summaryData", "target", "type", "isSafe", "threatLevel", "timestamp", "duration", "for<PERSON>ach", "label", "value", "threats", "length", "threat", "index", "name", "description", "lines", "splitTextToSize", "severity", "toUpperCase", "recommendations", "rec", "recText", "details", "detail", "pageHeight", "getHeight", "now", "error", "console", "Error", "downloadReport", "filename", "reportFilename", "save", "success", "message", "shareReport", "method", "generateShareableLink", "shareViaEmail", "copyToClipboard", "_scanResult$threats", "reportData", "id", "toISOString", "summary", "threatsCount", "Promise", "resolve", "setTimeout", "shareableLink", "link", "expiresIn", "_scanResult$threats2", "body", "trim", "mailtoLink", "encodeURIComponent", "window", "open", "_scanResult$threats3", "_scanResult$recommend", "reportText", "map", "t", "i", "join", "r", "navigator", "clipboard", "writeText", "textArea", "document", "createElement", "append<PERSON><PERSON><PERSON>", "select", "execCommand", "<PERSON><PERSON><PERSON><PERSON>", "generateReportSummary", "_scanResult$threats4", "_scanResult$vulnerabi", "_scanResult$recommend2", "vulnerabilitiesCount", "vulnerabilities", "recommendationsCount"], "sources": ["E:/PythonProjects/React.js/Smart AI Assisstant/src/services/reportService.js"], "sourcesContent": ["import jsPDF from 'jspdf';\nimport html2canvas from 'html2canvas';\n\n// Report generation service\nexport class ReportService {\n  static async generatePDFReport(scanResult, options = {}) {\n    try {\n      const {\n        title = 'Security Scan Report',\n        includeCharts = true,\n        includeRecommendations = true,\n        format = 'a4',\n      } = options;\n\n      const pdf = new jsPDF({\n        orientation: 'portrait',\n        unit: 'mm',\n        format: format,\n      });\n\n      // Set up document properties\n      pdf.setProperties({\n        title: title,\n        subject: 'Security Analysis Report',\n        author: 'AI Security Guard',\n        creator: 'AI Security Guard Platform',\n        keywords: 'security, scan, report, analysis',\n      });\n\n      let yPosition = 20;\n      const pageWidth = pdf.internal.pageSize.getWidth();\n      const margin = 20;\n      const contentWidth = pageWidth - (margin * 2);\n\n      // Header\n      pdf.setFontSize(24);\n      pdf.setFont('helvetica', 'bold');\n      pdf.setTextColor(25, 118, 210);\n      pdf.text(title, margin, yPosition);\n      yPosition += 15;\n\n      // Subtitle\n      pdf.setFontSize(12);\n      pdf.setFont('helvetica', 'normal');\n      pdf.setTextColor(100, 100, 100);\n      pdf.text(`Generated on ${new Date().toLocaleString()}`, margin, yPosition);\n      yPosition += 20;\n\n      // Scan Summary\n      pdf.setFontSize(16);\n      pdf.setFont('helvetica', 'bold');\n      pdf.setTextColor(0, 0, 0);\n      pdf.text('Scan Summary', margin, yPosition);\n      yPosition += 10;\n\n      pdf.setFontSize(11);\n      pdf.setFont('helvetica', 'normal');\n      \n      const summaryData = [\n        ['Target:', scanResult.target || 'N/A'],\n        ['Scan Type:', scanResult.type || 'N/A'],\n        ['Status:', scanResult.isSafe ? 'Safe' : 'Threats Detected'],\n        ['Threat Level:', scanResult.threatLevel || 'Unknown'],\n        ['Scan Date:', new Date(scanResult.timestamp).toLocaleString()],\n        ['Duration:', scanResult.duration || 'N/A'],\n      ];\n\n      summaryData.forEach(([label, value]) => {\n        pdf.setFont('helvetica', 'bold');\n        pdf.text(label, margin, yPosition);\n        pdf.setFont('helvetica', 'normal');\n        pdf.text(value, margin + 40, yPosition);\n        yPosition += 7;\n      });\n\n      yPosition += 10;\n\n      // Threat Analysis\n      if (scanResult.threats && scanResult.threats.length > 0) {\n        pdf.setFontSize(16);\n        pdf.setFont('helvetica', 'bold');\n        pdf.setTextColor(244, 67, 54);\n        pdf.text('Threats Detected', margin, yPosition);\n        yPosition += 10;\n\n        scanResult.threats.forEach((threat, index) => {\n          pdf.setFontSize(12);\n          pdf.setFont('helvetica', 'bold');\n          pdf.setTextColor(0, 0, 0);\n          pdf.text(`${index + 1}. ${threat.name || 'Unknown Threat'}`, margin, yPosition);\n          yPosition += 7;\n\n          pdf.setFontSize(10);\n          pdf.setFont('helvetica', 'normal');\n          pdf.setTextColor(100, 100, 100);\n          \n          const description = threat.description || 'No description available';\n          const lines = pdf.splitTextToSize(description, contentWidth - 10);\n          pdf.text(lines, margin + 5, yPosition);\n          yPosition += lines.length * 5 + 5;\n\n          if (threat.severity) {\n            pdf.setFont('helvetica', 'bold');\n            pdf.text(`Severity: ${threat.severity.toUpperCase()}`, margin + 5, yPosition);\n            yPosition += 7;\n          }\n\n          yPosition += 5;\n        });\n      }\n\n      // Recommendations\n      if (includeRecommendations && scanResult.recommendations && scanResult.recommendations.length > 0) {\n        yPosition += 10;\n        pdf.setFontSize(16);\n        pdf.setFont('helvetica', 'bold');\n        pdf.setTextColor(76, 175, 80);\n        pdf.text('Security Recommendations', margin, yPosition);\n        yPosition += 10;\n\n        scanResult.recommendations.forEach((rec, index) => {\n          pdf.setFontSize(11);\n          pdf.setFont('helvetica', 'normal');\n          pdf.setTextColor(0, 0, 0);\n          \n          const recText = typeof rec === 'string' ? rec : rec.title || rec;\n          const lines = pdf.splitTextToSize(`${index + 1}. ${recText}`, contentWidth - 10);\n          pdf.text(lines, margin, yPosition);\n          yPosition += lines.length * 5 + 3;\n        });\n      }\n\n      // Technical Details\n      if (scanResult.details && scanResult.details.length > 0) {\n        yPosition += 15;\n        pdf.setFontSize(16);\n        pdf.setFont('helvetica', 'bold');\n        pdf.setTextColor(0, 0, 0);\n        pdf.text('Technical Details', margin, yPosition);\n        yPosition += 10;\n\n        scanResult.details.forEach((detail) => {\n          pdf.setFontSize(12);\n          pdf.setFont('helvetica', 'bold');\n          pdf.text(detail.title || 'Detail', margin, yPosition);\n          yPosition += 7;\n\n          pdf.setFontSize(10);\n          pdf.setFont('helvetica', 'normal');\n          pdf.setTextColor(100, 100, 100);\n          \n          const description = detail.description || detail.value || 'No details available';\n          const lines = pdf.splitTextToSize(description, contentWidth - 10);\n          pdf.text(lines, margin + 5, yPosition);\n          yPosition += lines.length * 5 + 8;\n        });\n      }\n\n      // Footer\n      const pageHeight = pdf.internal.pageSize.getHeight();\n      pdf.setFontSize(8);\n      pdf.setFont('helvetica', 'normal');\n      pdf.setTextColor(150, 150, 150);\n      pdf.text('Generated by AI Security Guard - Professional Security Platform', margin, pageHeight - 10);\n      pdf.text(`Report ID: ${Date.now()}`, pageWidth - margin - 50, pageHeight - 10);\n\n      return pdf;\n    } catch (error) {\n      console.error('Error generating PDF report:', error);\n      throw new Error('Failed to generate PDF report');\n    }\n  }\n\n  static async downloadReport(scanResult, filename) {\n    try {\n      const pdf = await this.generatePDFReport(scanResult);\n      const reportFilename = filename || `security-report-${Date.now()}.pdf`;\n      pdf.save(reportFilename);\n      return { success: true, filename: reportFilename };\n    } catch (error) {\n      console.error('Error downloading report:', error);\n      return { success: false, error: error.message };\n    }\n  }\n\n  static async shareReport(scanResult, method = 'link') {\n    try {\n      switch (method) {\n        case 'link':\n          return await this.generateShareableLink(scanResult);\n        case 'email':\n          return await this.shareViaEmail(scanResult);\n        case 'copy':\n          return await this.copyToClipboard(scanResult);\n        default:\n          throw new Error('Unsupported sharing method');\n      }\n    } catch (error) {\n      console.error('Error sharing report:', error);\n      return { success: false, error: error.message };\n    }\n  }\n\n  static async generateShareableLink(scanResult) {\n    // In a real application, this would upload the report to a server\n    // and return a shareable link\n    const reportData = {\n      id: Date.now(),\n      timestamp: new Date().toISOString(),\n      summary: {\n        target: scanResult.target,\n        isSafe: scanResult.isSafe,\n        threatLevel: scanResult.threatLevel,\n        threatsCount: scanResult.threats?.length || 0,\n      },\n    };\n\n    // Simulate API call\n    await new Promise(resolve => setTimeout(resolve, 1000));\n\n    const shareableLink = `https://aisecurityguard.com/reports/${reportData.id}`;\n    \n    return {\n      success: true,\n      link: shareableLink,\n      expiresIn: '7 days',\n    };\n  }\n\n  static async shareViaEmail(scanResult) {\n    const subject = `Security Scan Report - ${scanResult.target}`;\n    const body = `\nSecurity Scan Report\n\nTarget: ${scanResult.target}\nStatus: ${scanResult.isSafe ? 'Safe' : 'Threats Detected'}\nThreat Level: ${scanResult.threatLevel || 'Unknown'}\nScan Date: ${new Date(scanResult.timestamp).toLocaleString()}\n\n${scanResult.threats?.length > 0 ? `Threats Found: ${scanResult.threats.length}` : 'No threats detected'}\n\nGenerated by AI Security Guard\nVisit: https://aisecurityguard.com\n    `.trim();\n\n    const mailtoLink = `mailto:?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;\n    window.open(mailtoLink);\n\n    return {\n      success: true,\n      method: 'email',\n    };\n  }\n\n  static async copyToClipboard(scanResult) {\n    const reportText = `\nAI Security Guard - Scan Report\n\nTarget: ${scanResult.target}\nStatus: ${scanResult.isSafe ? 'Safe' : 'Threats Detected'}\nThreat Level: ${scanResult.threatLevel || 'Unknown'}\nScan Date: ${new Date(scanResult.timestamp).toLocaleString()}\nDuration: ${scanResult.duration || 'N/A'}\n\n${scanResult.threats?.length > 0 ? \n  `Threats Detected (${scanResult.threats.length}):\\n${scanResult.threats.map((t, i) => `${i + 1}. ${t.name}: ${t.description}`).join('\\n')}` : \n  'No threats detected'\n}\n\n${scanResult.recommendations?.length > 0 ? \n  `\\nRecommendations:\\n${scanResult.recommendations.map((r, i) => `${i + 1}. ${typeof r === 'string' ? r : r.title || r}`).join('\\n')}` : \n  ''\n}\n\nGenerated by AI Security Guard\n    `.trim();\n\n    try {\n      await navigator.clipboard.writeText(reportText);\n      return {\n        success: true,\n        method: 'clipboard',\n      };\n    } catch (error) {\n      // Fallback for older browsers\n      const textArea = document.createElement('textarea');\n      textArea.value = reportText;\n      document.body.appendChild(textArea);\n      textArea.select();\n      document.execCommand('copy');\n      document.body.removeChild(textArea);\n      \n      return {\n        success: true,\n        method: 'clipboard',\n      };\n    }\n  }\n\n  static generateReportSummary(scanResult) {\n    return {\n      target: scanResult.target,\n      type: scanResult.type,\n      timestamp: scanResult.timestamp,\n      isSafe: scanResult.isSafe,\n      threatLevel: scanResult.threatLevel,\n      threatsCount: scanResult.threats?.length || 0,\n      vulnerabilitiesCount: scanResult.vulnerabilities?.length || 0,\n      recommendationsCount: scanResult.recommendations?.length || 0,\n      duration: scanResult.duration,\n    };\n  }\n}\n\nexport default ReportService;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,WAAW,MAAM,aAAa;;AAErC;AACA,OAAO,MAAMC,aAAa,CAAC;EACzB,aAAaC,iBAAiBA,CAACC,UAAU,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;IACvD,IAAI;MACF,MAAM;QACJC,KAAK,GAAG,sBAAsB;QAC9BC,aAAa,GAAG,IAAI;QACpBC,sBAAsB,GAAG,IAAI;QAC7BC,MAAM,GAAG;MACX,CAAC,GAAGJ,OAAO;MAEX,MAAMK,GAAG,GAAG,IAAIV,KAAK,CAAC;QACpBW,WAAW,EAAE,UAAU;QACvBC,IAAI,EAAE,IAAI;QACVH,MAAM,EAAEA;MACV,CAAC,CAAC;;MAEF;MACAC,GAAG,CAACG,aAAa,CAAC;QAChBP,KAAK,EAAEA,KAAK;QACZQ,OAAO,EAAE,0BAA0B;QACnCC,MAAM,EAAE,mBAAmB;QAC3BC,OAAO,EAAE,4BAA4B;QACrCC,QAAQ,EAAE;MACZ,CAAC,CAAC;MAEF,IAAIC,SAAS,GAAG,EAAE;MAClB,MAAMC,SAAS,GAAGT,GAAG,CAACU,QAAQ,CAACC,QAAQ,CAACC,QAAQ,CAAC,CAAC;MAClD,MAAMC,MAAM,GAAG,EAAE;MACjB,MAAMC,YAAY,GAAGL,SAAS,GAAII,MAAM,GAAG,CAAE;;MAE7C;MACAb,GAAG,CAACe,WAAW,CAAC,EAAE,CAAC;MACnBf,GAAG,CAACgB,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC;MAChChB,GAAG,CAACiB,YAAY,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC;MAC9BjB,GAAG,CAACkB,IAAI,CAACtB,KAAK,EAAEiB,MAAM,EAAEL,SAAS,CAAC;MAClCA,SAAS,IAAI,EAAE;;MAEf;MACAR,GAAG,CAACe,WAAW,CAAC,EAAE,CAAC;MACnBf,GAAG,CAACgB,OAAO,CAAC,WAAW,EAAE,QAAQ,CAAC;MAClChB,GAAG,CAACiB,YAAY,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;MAC/BjB,GAAG,CAACkB,IAAI,CAAC,gBAAgB,IAAIC,IAAI,CAAC,CAAC,CAACC,cAAc,CAAC,CAAC,EAAE,EAAEP,MAAM,EAAEL,SAAS,CAAC;MAC1EA,SAAS,IAAI,EAAE;;MAEf;MACAR,GAAG,CAACe,WAAW,CAAC,EAAE,CAAC;MACnBf,GAAG,CAACgB,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC;MAChChB,GAAG,CAACiB,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MACzBjB,GAAG,CAACkB,IAAI,CAAC,cAAc,EAAEL,MAAM,EAAEL,SAAS,CAAC;MAC3CA,SAAS,IAAI,EAAE;MAEfR,GAAG,CAACe,WAAW,CAAC,EAAE,CAAC;MACnBf,GAAG,CAACgB,OAAO,CAAC,WAAW,EAAE,QAAQ,CAAC;MAElC,MAAMK,WAAW,GAAG,CAClB,CAAC,SAAS,EAAE3B,UAAU,CAAC4B,MAAM,IAAI,KAAK,CAAC,EACvC,CAAC,YAAY,EAAE5B,UAAU,CAAC6B,IAAI,IAAI,KAAK,CAAC,EACxC,CAAC,SAAS,EAAE7B,UAAU,CAAC8B,MAAM,GAAG,MAAM,GAAG,kBAAkB,CAAC,EAC5D,CAAC,eAAe,EAAE9B,UAAU,CAAC+B,WAAW,IAAI,SAAS,CAAC,EACtD,CAAC,YAAY,EAAE,IAAIN,IAAI,CAACzB,UAAU,CAACgC,SAAS,CAAC,CAACN,cAAc,CAAC,CAAC,CAAC,EAC/D,CAAC,WAAW,EAAE1B,UAAU,CAACiC,QAAQ,IAAI,KAAK,CAAC,CAC5C;MAEDN,WAAW,CAACO,OAAO,CAAC,CAAC,CAACC,KAAK,EAAEC,KAAK,CAAC,KAAK;QACtC9B,GAAG,CAACgB,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC;QAChChB,GAAG,CAACkB,IAAI,CAACW,KAAK,EAAEhB,MAAM,EAAEL,SAAS,CAAC;QAClCR,GAAG,CAACgB,OAAO,CAAC,WAAW,EAAE,QAAQ,CAAC;QAClChB,GAAG,CAACkB,IAAI,CAACY,KAAK,EAAEjB,MAAM,GAAG,EAAE,EAAEL,SAAS,CAAC;QACvCA,SAAS,IAAI,CAAC;MAChB,CAAC,CAAC;MAEFA,SAAS,IAAI,EAAE;;MAEf;MACA,IAAId,UAAU,CAACqC,OAAO,IAAIrC,UAAU,CAACqC,OAAO,CAACC,MAAM,GAAG,CAAC,EAAE;QACvDhC,GAAG,CAACe,WAAW,CAAC,EAAE,CAAC;QACnBf,GAAG,CAACgB,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC;QAChChB,GAAG,CAACiB,YAAY,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE,CAAC;QAC7BjB,GAAG,CAACkB,IAAI,CAAC,kBAAkB,EAAEL,MAAM,EAAEL,SAAS,CAAC;QAC/CA,SAAS,IAAI,EAAE;QAEfd,UAAU,CAACqC,OAAO,CAACH,OAAO,CAAC,CAACK,MAAM,EAAEC,KAAK,KAAK;UAC5ClC,GAAG,CAACe,WAAW,CAAC,EAAE,CAAC;UACnBf,GAAG,CAACgB,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC;UAChChB,GAAG,CAACiB,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UACzBjB,GAAG,CAACkB,IAAI,CAAC,GAAGgB,KAAK,GAAG,CAAC,KAAKD,MAAM,CAACE,IAAI,IAAI,gBAAgB,EAAE,EAAEtB,MAAM,EAAEL,SAAS,CAAC;UAC/EA,SAAS,IAAI,CAAC;UAEdR,GAAG,CAACe,WAAW,CAAC,EAAE,CAAC;UACnBf,GAAG,CAACgB,OAAO,CAAC,WAAW,EAAE,QAAQ,CAAC;UAClChB,GAAG,CAACiB,YAAY,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;UAE/B,MAAMmB,WAAW,GAAGH,MAAM,CAACG,WAAW,IAAI,0BAA0B;UACpE,MAAMC,KAAK,GAAGrC,GAAG,CAACsC,eAAe,CAACF,WAAW,EAAEtB,YAAY,GAAG,EAAE,CAAC;UACjEd,GAAG,CAACkB,IAAI,CAACmB,KAAK,EAAExB,MAAM,GAAG,CAAC,EAAEL,SAAS,CAAC;UACtCA,SAAS,IAAI6B,KAAK,CAACL,MAAM,GAAG,CAAC,GAAG,CAAC;UAEjC,IAAIC,MAAM,CAACM,QAAQ,EAAE;YACnBvC,GAAG,CAACgB,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC;YAChChB,GAAG,CAACkB,IAAI,CAAC,aAAae,MAAM,CAACM,QAAQ,CAACC,WAAW,CAAC,CAAC,EAAE,EAAE3B,MAAM,GAAG,CAAC,EAAEL,SAAS,CAAC;YAC7EA,SAAS,IAAI,CAAC;UAChB;UAEAA,SAAS,IAAI,CAAC;QAChB,CAAC,CAAC;MACJ;;MAEA;MACA,IAAIV,sBAAsB,IAAIJ,UAAU,CAAC+C,eAAe,IAAI/C,UAAU,CAAC+C,eAAe,CAACT,MAAM,GAAG,CAAC,EAAE;QACjGxB,SAAS,IAAI,EAAE;QACfR,GAAG,CAACe,WAAW,CAAC,EAAE,CAAC;QACnBf,GAAG,CAACgB,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC;QAChChB,GAAG,CAACiB,YAAY,CAAC,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC;QAC7BjB,GAAG,CAACkB,IAAI,CAAC,0BAA0B,EAAEL,MAAM,EAAEL,SAAS,CAAC;QACvDA,SAAS,IAAI,EAAE;QAEfd,UAAU,CAAC+C,eAAe,CAACb,OAAO,CAAC,CAACc,GAAG,EAAER,KAAK,KAAK;UACjDlC,GAAG,CAACe,WAAW,CAAC,EAAE,CAAC;UACnBf,GAAG,CAACgB,OAAO,CAAC,WAAW,EAAE,QAAQ,CAAC;UAClChB,GAAG,CAACiB,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UAEzB,MAAM0B,OAAO,GAAG,OAAOD,GAAG,KAAK,QAAQ,GAAGA,GAAG,GAAGA,GAAG,CAAC9C,KAAK,IAAI8C,GAAG;UAChE,MAAML,KAAK,GAAGrC,GAAG,CAACsC,eAAe,CAAC,GAAGJ,KAAK,GAAG,CAAC,KAAKS,OAAO,EAAE,EAAE7B,YAAY,GAAG,EAAE,CAAC;UAChFd,GAAG,CAACkB,IAAI,CAACmB,KAAK,EAAExB,MAAM,EAAEL,SAAS,CAAC;UAClCA,SAAS,IAAI6B,KAAK,CAACL,MAAM,GAAG,CAAC,GAAG,CAAC;QACnC,CAAC,CAAC;MACJ;;MAEA;MACA,IAAItC,UAAU,CAACkD,OAAO,IAAIlD,UAAU,CAACkD,OAAO,CAACZ,MAAM,GAAG,CAAC,EAAE;QACvDxB,SAAS,IAAI,EAAE;QACfR,GAAG,CAACe,WAAW,CAAC,EAAE,CAAC;QACnBf,GAAG,CAACgB,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC;QAChChB,GAAG,CAACiB,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACzBjB,GAAG,CAACkB,IAAI,CAAC,mBAAmB,EAAEL,MAAM,EAAEL,SAAS,CAAC;QAChDA,SAAS,IAAI,EAAE;QAEfd,UAAU,CAACkD,OAAO,CAAChB,OAAO,CAAEiB,MAAM,IAAK;UACrC7C,GAAG,CAACe,WAAW,CAAC,EAAE,CAAC;UACnBf,GAAG,CAACgB,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC;UAChChB,GAAG,CAACkB,IAAI,CAAC2B,MAAM,CAACjD,KAAK,IAAI,QAAQ,EAAEiB,MAAM,EAAEL,SAAS,CAAC;UACrDA,SAAS,IAAI,CAAC;UAEdR,GAAG,CAACe,WAAW,CAAC,EAAE,CAAC;UACnBf,GAAG,CAACgB,OAAO,CAAC,WAAW,EAAE,QAAQ,CAAC;UAClChB,GAAG,CAACiB,YAAY,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;UAE/B,MAAMmB,WAAW,GAAGS,MAAM,CAACT,WAAW,IAAIS,MAAM,CAACf,KAAK,IAAI,sBAAsB;UAChF,MAAMO,KAAK,GAAGrC,GAAG,CAACsC,eAAe,CAACF,WAAW,EAAEtB,YAAY,GAAG,EAAE,CAAC;UACjEd,GAAG,CAACkB,IAAI,CAACmB,KAAK,EAAExB,MAAM,GAAG,CAAC,EAAEL,SAAS,CAAC;UACtCA,SAAS,IAAI6B,KAAK,CAACL,MAAM,GAAG,CAAC,GAAG,CAAC;QACnC,CAAC,CAAC;MACJ;;MAEA;MACA,MAAMc,UAAU,GAAG9C,GAAG,CAACU,QAAQ,CAACC,QAAQ,CAACoC,SAAS,CAAC,CAAC;MACpD/C,GAAG,CAACe,WAAW,CAAC,CAAC,CAAC;MAClBf,GAAG,CAACgB,OAAO,CAAC,WAAW,EAAE,QAAQ,CAAC;MAClChB,GAAG,CAACiB,YAAY,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;MAC/BjB,GAAG,CAACkB,IAAI,CAAC,iEAAiE,EAAEL,MAAM,EAAEiC,UAAU,GAAG,EAAE,CAAC;MACpG9C,GAAG,CAACkB,IAAI,CAAC,cAAcC,IAAI,CAAC6B,GAAG,CAAC,CAAC,EAAE,EAAEvC,SAAS,GAAGI,MAAM,GAAG,EAAE,EAAEiC,UAAU,GAAG,EAAE,CAAC;MAE9E,OAAO9C,GAAG;IACZ,CAAC,CAAC,OAAOiD,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD,MAAM,IAAIE,KAAK,CAAC,+BAA+B,CAAC;IAClD;EACF;EAEA,aAAaC,cAAcA,CAAC1D,UAAU,EAAE2D,QAAQ,EAAE;IAChD,IAAI;MACF,MAAMrD,GAAG,GAAG,MAAM,IAAI,CAACP,iBAAiB,CAACC,UAAU,CAAC;MACpD,MAAM4D,cAAc,GAAGD,QAAQ,IAAI,mBAAmBlC,IAAI,CAAC6B,GAAG,CAAC,CAAC,MAAM;MACtEhD,GAAG,CAACuD,IAAI,CAACD,cAAc,CAAC;MACxB,OAAO;QAAEE,OAAO,EAAE,IAAI;QAAEH,QAAQ,EAAEC;MAAe,CAAC;IACpD,CAAC,CAAC,OAAOL,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD,OAAO;QAAEO,OAAO,EAAE,KAAK;QAAEP,KAAK,EAAEA,KAAK,CAACQ;MAAQ,CAAC;IACjD;EACF;EAEA,aAAaC,WAAWA,CAAChE,UAAU,EAAEiE,MAAM,GAAG,MAAM,EAAE;IACpD,IAAI;MACF,QAAQA,MAAM;QACZ,KAAK,MAAM;UACT,OAAO,MAAM,IAAI,CAACC,qBAAqB,CAAClE,UAAU,CAAC;QACrD,KAAK,OAAO;UACV,OAAO,MAAM,IAAI,CAACmE,aAAa,CAACnE,UAAU,CAAC;QAC7C,KAAK,MAAM;UACT,OAAO,MAAM,IAAI,CAACoE,eAAe,CAACpE,UAAU,CAAC;QAC/C;UACE,MAAM,IAAIyD,KAAK,CAAC,4BAA4B,CAAC;MACjD;IACF,CAAC,CAAC,OAAOF,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C,OAAO;QAAEO,OAAO,EAAE,KAAK;QAAEP,KAAK,EAAEA,KAAK,CAACQ;MAAQ,CAAC;IACjD;EACF;EAEA,aAAaG,qBAAqBA,CAAClE,UAAU,EAAE;IAAA,IAAAqE,mBAAA;IAC7C;IACA;IACA,MAAMC,UAAU,GAAG;MACjBC,EAAE,EAAE9C,IAAI,CAAC6B,GAAG,CAAC,CAAC;MACdtB,SAAS,EAAE,IAAIP,IAAI,CAAC,CAAC,CAAC+C,WAAW,CAAC,CAAC;MACnCC,OAAO,EAAE;QACP7C,MAAM,EAAE5B,UAAU,CAAC4B,MAAM;QACzBE,MAAM,EAAE9B,UAAU,CAAC8B,MAAM;QACzBC,WAAW,EAAE/B,UAAU,CAAC+B,WAAW;QACnC2C,YAAY,EAAE,EAAAL,mBAAA,GAAArE,UAAU,CAACqC,OAAO,cAAAgC,mBAAA,uBAAlBA,mBAAA,CAAoB/B,MAAM,KAAI;MAC9C;IACF,CAAC;;IAED;IACA,MAAM,IAAIqC,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;IAEvD,MAAME,aAAa,GAAG,uCAAuCR,UAAU,CAACC,EAAE,EAAE;IAE5E,OAAO;MACLT,OAAO,EAAE,IAAI;MACbiB,IAAI,EAAED,aAAa;MACnBE,SAAS,EAAE;IACb,CAAC;EACH;EAEA,aAAab,aAAaA,CAACnE,UAAU,EAAE;IAAA,IAAAiF,oBAAA;IACrC,MAAMvE,OAAO,GAAG,0BAA0BV,UAAU,CAAC4B,MAAM,EAAE;IAC7D,MAAMsD,IAAI,GAAG;AACjB;AACA;AACA,UAAUlF,UAAU,CAAC4B,MAAM;AAC3B,UAAU5B,UAAU,CAAC8B,MAAM,GAAG,MAAM,GAAG,kBAAkB;AACzD,gBAAgB9B,UAAU,CAAC+B,WAAW,IAAI,SAAS;AACnD,aAAa,IAAIN,IAAI,CAACzB,UAAU,CAACgC,SAAS,CAAC,CAACN,cAAc,CAAC,CAAC;AAC5D;AACA,EAAE,EAAAuD,oBAAA,GAAAjF,UAAU,CAACqC,OAAO,cAAA4C,oBAAA,uBAAlBA,oBAAA,CAAoB3C,MAAM,IAAG,CAAC,GAAG,kBAAkBtC,UAAU,CAACqC,OAAO,CAACC,MAAM,EAAE,GAAG,qBAAqB;AACxG;AACA;AACA;AACA,KAAK,CAAC6C,IAAI,CAAC,CAAC;IAER,MAAMC,UAAU,GAAG,mBAAmBC,kBAAkB,CAAC3E,OAAO,CAAC,SAAS2E,kBAAkB,CAACH,IAAI,CAAC,EAAE;IACpGI,MAAM,CAACC,IAAI,CAACH,UAAU,CAAC;IAEvB,OAAO;MACLtB,OAAO,EAAE,IAAI;MACbG,MAAM,EAAE;IACV,CAAC;EACH;EAEA,aAAaG,eAAeA,CAACpE,UAAU,EAAE;IAAA,IAAAwF,oBAAA,EAAAC,qBAAA;IACvC,MAAMC,UAAU,GAAG;AACvB;AACA;AACA,UAAU1F,UAAU,CAAC4B,MAAM;AAC3B,UAAU5B,UAAU,CAAC8B,MAAM,GAAG,MAAM,GAAG,kBAAkB;AACzD,gBAAgB9B,UAAU,CAAC+B,WAAW,IAAI,SAAS;AACnD,aAAa,IAAIN,IAAI,CAACzB,UAAU,CAACgC,SAAS,CAAC,CAACN,cAAc,CAAC,CAAC;AAC5D,YAAY1B,UAAU,CAACiC,QAAQ,IAAI,KAAK;AACxC;AACA,EAAE,EAAAuD,oBAAA,GAAAxF,UAAU,CAACqC,OAAO,cAAAmD,oBAAA,uBAAlBA,oBAAA,CAAoBlD,MAAM,IAAG,CAAC,GAC9B,qBAAqBtC,UAAU,CAACqC,OAAO,CAACC,MAAM,OAAOtC,UAAU,CAACqC,OAAO,CAACsD,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,GAAGA,CAAC,GAAG,CAAC,KAAKD,CAAC,CAACnD,IAAI,KAAKmD,CAAC,CAAClD,WAAW,EAAE,CAAC,CAACoD,IAAI,CAAC,IAAI,CAAC,EAAE,GAC3I,qBAAqB;AACvB;AACA,EACE,EAAAL,qBAAA,GAAAzF,UAAU,CAAC+C,eAAe,cAAA0C,qBAAA,uBAA1BA,qBAAA,CAA4BnD,MAAM,IAAG,CAAC,GACtC,uBAAuBtC,UAAU,CAAC+C,eAAe,CAAC4C,GAAG,CAAC,CAACI,CAAC,EAAEF,CAAC,KAAK,GAAGA,CAAC,GAAG,CAAC,KAAK,OAAOE,CAAC,KAAK,QAAQ,GAAGA,CAAC,GAAGA,CAAC,CAAC7F,KAAK,IAAI6F,CAAC,EAAE,CAAC,CAACD,IAAI,CAAC,IAAI,CAAC,EAAE,GACrI,EAAE;AACJ;AACA;AACA,KACK,CAACX,IAAI,CAAC,CAAC;IAER,IAAI;MACF,MAAMa,SAAS,CAACC,SAAS,CAACC,SAAS,CAACR,UAAU,CAAC;MAC/C,OAAO;QACL5B,OAAO,EAAE,IAAI;QACbG,MAAM,EAAE;MACV,CAAC;IACH,CAAC,CAAC,OAAOV,KAAK,EAAE;MACd;MACA,MAAM4C,QAAQ,GAAGC,QAAQ,CAACC,aAAa,CAAC,UAAU,CAAC;MACnDF,QAAQ,CAAC/D,KAAK,GAAGsD,UAAU;MAC3BU,QAAQ,CAAClB,IAAI,CAACoB,WAAW,CAACH,QAAQ,CAAC;MACnCA,QAAQ,CAACI,MAAM,CAAC,CAAC;MACjBH,QAAQ,CAACI,WAAW,CAAC,MAAM,CAAC;MAC5BJ,QAAQ,CAAClB,IAAI,CAACuB,WAAW,CAACN,QAAQ,CAAC;MAEnC,OAAO;QACLrC,OAAO,EAAE,IAAI;QACbG,MAAM,EAAE;MACV,CAAC;IACH;EACF;EAEA,OAAOyC,qBAAqBA,CAAC1G,UAAU,EAAE;IAAA,IAAA2G,oBAAA,EAAAC,qBAAA,EAAAC,sBAAA;IACvC,OAAO;MACLjF,MAAM,EAAE5B,UAAU,CAAC4B,MAAM;MACzBC,IAAI,EAAE7B,UAAU,CAAC6B,IAAI;MACrBG,SAAS,EAAEhC,UAAU,CAACgC,SAAS;MAC/BF,MAAM,EAAE9B,UAAU,CAAC8B,MAAM;MACzBC,WAAW,EAAE/B,UAAU,CAAC+B,WAAW;MACnC2C,YAAY,EAAE,EAAAiC,oBAAA,GAAA3G,UAAU,CAACqC,OAAO,cAAAsE,oBAAA,uBAAlBA,oBAAA,CAAoBrE,MAAM,KAAI,CAAC;MAC7CwE,oBAAoB,EAAE,EAAAF,qBAAA,GAAA5G,UAAU,CAAC+G,eAAe,cAAAH,qBAAA,uBAA1BA,qBAAA,CAA4BtE,MAAM,KAAI,CAAC;MAC7D0E,oBAAoB,EAAE,EAAAH,sBAAA,GAAA7G,UAAU,CAAC+C,eAAe,cAAA8D,sBAAA,uBAA1BA,sBAAA,CAA4BvE,MAAM,KAAI,CAAC;MAC7DL,QAAQ,EAAEjC,UAAU,CAACiC;IACvB,CAAC;EACH;AACF;AAEA,eAAenC,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}