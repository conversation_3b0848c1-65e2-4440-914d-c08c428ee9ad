"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = _classPrivateFieldSet;
var _classApplyDescriptorSet = require("classApplyDescriptorSet");
var _classPrivateFieldGet = require("classPrivateFieldGet2");
function _classPrivateFieldSet(receiver, privateMap, value) {
  var descriptor = _classPrivateFieldGet(privateMap, receiver);
  _classApplyDescriptorSet(receiver, descriptor, value);
  return value;
}

//# sourceMappingURL=classPrivateFieldSet.js.map
