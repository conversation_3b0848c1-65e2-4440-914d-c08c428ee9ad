{"ast": null, "code": "var _jsxFileName = \"E:\\\\PythonProjects\\\\React.js\\\\Smart AI Assisstant\\\\src\\\\components\\\\AutoScanSystem.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Card, CardContent, Typography, Switch, FormControlLabel, Grid, Chip, List, ListItem, ListItemIcon, ListItemText, Avatar, IconButton, Tooltip, LinearProgress, Alert, Slider, TextField, Button, Divider } from '@mui/material';\nimport { AutoMode as AutoIcon, Schedule as ScheduleIcon, Speed as SpeedIcon, Security as SecurityIcon, Notifications as NotificationsIcon, Settings as SettingsIcon, PlayArrow as PlayIcon, Pause as PauseIcon, Stop as StopIcon, Refresh as RefreshIcon, Psychology as AIIcon, Shield as ShieldIcon } from '@mui/icons-material';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AutoScanSystem = /*#__PURE__*/_s(/*#__PURE__*/React.memo(_c = _s(() => {\n  _s();\n  const [autoScanEnabled, setAutoScanEnabled] = useState(true);\n  const [scanInterval, setScanInterval] = useState(30); // minutes\n  const [scanIntensity, setScanIntensity] = useState(2); // 1-3 scale\n  const [smartScheduling, setSmartScheduling] = useState(true);\n  const [adaptiveScan, setAdaptiveScan] = useState(true);\n  const [currentScan, setCurrentScan] = useState({\n    isRunning: false,\n    progress: 0,\n    currentTarget: '',\n    threatsFound: 0,\n    estimatedTime: 0\n  });\n  const [scanQueue, setScanQueue] = useState([{\n    type: 'URL',\n    target: 'https://suspicious-site.com',\n    priority: 'high',\n    status: 'pending'\n  }, {\n    type: 'File',\n    target: 'document.pdf',\n    priority: 'medium',\n    status: 'pending'\n  }, {\n    type: 'URL',\n    target: 'https://phishing-attempt.net',\n    priority: 'high',\n    status: 'pending'\n  }, {\n    type: 'File',\n    target: 'software.exe',\n    priority: 'low',\n    status: 'pending'\n  }]);\n  const [scanHistory, setScanHistory] = useState([{\n    timestamp: new Date(Date.now() - 1000 * 60 * 15),\n    type: 'Auto Scan',\n    targets: 12,\n    threats: 2,\n    duration: '3m 45s',\n    status: 'completed'\n  }, {\n    timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2),\n    type: 'Scheduled Scan',\n    targets: 8,\n    threats: 0,\n    duration: '2m 12s',\n    status: 'completed'\n  }, {\n    timestamp: new Date(Date.now() - 1000 * 60 * 60 * 6),\n    type: 'Priority Scan',\n    targets: 3,\n    threats: 1,\n    duration: '1m 33s',\n    status: 'completed'\n  }]);\n  const [aiRecommendations, setAiRecommendations] = useState([{\n    type: 'schedule',\n    message: 'Increase scan frequency during peak hours (9-17)',\n    confidence: 89\n  }, {\n    type: 'priority',\n    message: 'Focus on .exe files - 67% higher threat probability',\n    confidence: 94\n  }, {\n    type: 'optimization',\n    message: 'Reduce scan intensity for trusted domains',\n    confidence: 82\n  }]);\n\n  // Simulate auto-scan progress\n  useEffect(() => {\n    let interval;\n    if (currentScan.isRunning) {\n      interval = setInterval(() => {\n        setCurrentScan(prev => {\n          const newProgress = Math.min(100, prev.progress + Math.random() * 10);\n          if (newProgress >= 100) {\n            return {\n              ...prev,\n              progress: 100,\n              isRunning: false,\n              threatsFound: prev.threatsFound + Math.floor(Math.random() * 3)\n            };\n          }\n          return {\n            ...prev,\n            progress: newProgress,\n            estimatedTime: Math.max(0, prev.estimatedTime - 5)\n          };\n        });\n      }, 1000);\n    }\n    return () => clearInterval(interval);\n  }, [currentScan.isRunning]);\n  const startAutoScan = () => {\n    var _scanQueue$;\n    setCurrentScan({\n      isRunning: true,\n      progress: 0,\n      currentTarget: ((_scanQueue$ = scanQueue[0]) === null || _scanQueue$ === void 0 ? void 0 : _scanQueue$.target) || 'Multiple targets',\n      threatsFound: 0,\n      estimatedTime: 180 // 3 minutes\n    });\n  };\n  const pauseAutoScan = () => {\n    setCurrentScan(prev => ({\n      ...prev,\n      isRunning: false\n    }));\n  };\n  const stopAutoScan = () => {\n    setCurrentScan({\n      isRunning: false,\n      progress: 0,\n      currentTarget: '',\n      threatsFound: 0,\n      estimatedTime: 0\n    });\n  };\n  const getIntensityLabel = value => {\n    switch (value) {\n      case 1:\n        return 'Light';\n      case 2:\n        return 'Balanced';\n      case 3:\n        return 'Intensive';\n      default:\n        return 'Balanced';\n    }\n  };\n  const getPriorityColor = priority => {\n    switch (priority) {\n      case 'high':\n        return 'error';\n      case 'medium':\n        return 'warning';\n      case 'low':\n        return 'success';\n      default:\n        return 'default';\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      p: 3\n    },\n    children: [/*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        mb: 4,\n        background: theme => theme.palette.mode === 'dark' ? 'linear-gradient(135deg, rgba(12, 12, 12, 0.95) 0%, rgba(26, 26, 26, 0.95) 50%, rgba(45, 45, 45, 0.95) 100%)' : 'linear-gradient(135deg, rgba(248, 250, 252, 0.95) 0%, rgba(241, 245, 249, 0.95) 50%, rgba(226, 232, 240, 0.95) 100%)',\n        backdropFilter: 'blur(25px) saturate(180%)',\n        border: '1px solid rgba(139, 92, 246, 0.2)',\n        borderRadius: 4,\n        position: 'relative',\n        overflow: 'hidden',\n        boxShadow: '0 8px 32px rgba(139, 92, 246, 0.2)',\n        transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',\n        '&:hover': {\n          transform: 'translateY(-2px)',\n          boxShadow: '0 16px 48px rgba(139, 92, 246, 0.3)'\n        },\n        '&::before': {\n          content: '\"\"',\n          position: 'absolute',\n          top: 0,\n          left: 0,\n          right: 0,\n          height: '4px',\n          background: 'linear-gradient(90deg, #8b5cf6 0%, #a855f7 50%, #c084fc 100%)',\n          backgroundSize: '200% 200%',\n          animation: 'dataFlow 5s ease infinite'\n        },\n        '@keyframes dataFlow': {\n          '0%': {\n            backgroundPosition: '0% 50%'\n          },\n          '50%': {\n            backgroundPosition: '100% 50%'\n          },\n          '100%': {\n            backgroundPosition: '0% 50%'\n          }\n        }\n      },\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          display: \"flex\",\n          alignItems: \"center\",\n          gap: 2,\n          mb: 3,\n          children: [/*#__PURE__*/_jsxDEV(Avatar, {\n            sx: {\n              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n              width: 56,\n              height: 56\n            },\n            children: /*#__PURE__*/_jsxDEV(AutoIcon, {\n              sx: {\n                fontSize: 28\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 222,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 215,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            flex: 1,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h4\",\n              fontWeight: \"bold\",\n              gutterBottom: true,\n              children: \"Intelligent Auto-Scan System\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 225,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              color: \"text.secondary\",\n              children: \"AI-powered automated security scanning with adaptive scheduling\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 228,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 224,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            display: \"flex\",\n            gap: 1,\n            children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n              title: \"Start Auto Scan\",\n              children: /*#__PURE__*/_jsxDEV(IconButton, {\n                onClick: startAutoScan,\n                disabled: currentScan.isRunning,\n                sx: {\n                  background: 'linear-gradient(135deg, #4caf50 0%, #8bc34a 100%)',\n                  color: 'white',\n                  '&:hover': {\n                    background: 'linear-gradient(135deg, #45a049 0%, #7cb342 100%)'\n                  }\n                },\n                children: /*#__PURE__*/_jsxDEV(PlayIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 245,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 234,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 233,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n              title: \"Pause\",\n              children: /*#__PURE__*/_jsxDEV(IconButton, {\n                onClick: pauseAutoScan,\n                disabled: !currentScan.isRunning,\n                sx: {\n                  background: 'linear-gradient(135deg, #ff9800 0%, #ffc107 100%)',\n                  color: 'white',\n                  '&:hover': {\n                    background: 'linear-gradient(135deg, #f57c00 0%, #ffb300 100%)'\n                  }\n                },\n                children: /*#__PURE__*/_jsxDEV(PauseIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 260,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 249,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 248,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n              title: \"Stop\",\n              children: /*#__PURE__*/_jsxDEV(IconButton, {\n                onClick: stopAutoScan,\n                sx: {\n                  background: 'linear-gradient(135deg, #f44336 0%, #ff5722 100%)',\n                  color: 'white',\n                  '&:hover': {\n                    background: 'linear-gradient(135deg, #d32f2f 0%, #f4511e 100%)'\n                  }\n                },\n                children: /*#__PURE__*/_jsxDEV(StopIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 274,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 264,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 263,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 232,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 214,\n          columnNumber: 11\n        }, this), (currentScan.isRunning || currentScan.progress > 0) && /*#__PURE__*/_jsxDEV(Alert, {\n          severity: currentScan.isRunning ? 'info' : 'success',\n          sx: {\n            mb: 3\n          },\n          icon: currentScan.isRunning ? /*#__PURE__*/_jsxDEV(RefreshIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 285,\n            columnNumber: 45\n          }, this) : /*#__PURE__*/_jsxDEV(ShieldIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 285,\n            columnNumber: 63\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle2\",\n              fontWeight: \"600\",\n              children: currentScan.isRunning ? 'Scanning in Progress' : 'Scan Completed'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 288,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              sx: {\n                mb: 1\n              },\n              children: [\"Target: \", currentScan.currentTarget]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 291,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(LinearProgress, {\n              variant: \"determinate\",\n              value: currentScan.progress,\n              sx: {\n                mb: 1,\n                height: 6,\n                borderRadius: 3\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 294,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              display: \"flex\",\n              justifyContent: \"space-between\",\n              alignItems: \"center\",\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                children: [\"Progress: \", Math.round(currentScan.progress), \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 300,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                children: [\"Threats Found: \", currentScan.threatsFound]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 303,\n                columnNumber: 19\n              }, this), currentScan.isRunning && /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                children: [\"ETA: \", Math.floor(currentScan.estimatedTime / 60), \"m \", currentScan.estimatedTime % 60, \"s\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 307,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 299,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 287,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 282,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 3,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              gutterBottom: true,\n              fontWeight: \"600\",\n              children: \"Auto-Scan Configuration\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 319,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(FormControlLabel, {\n              control: /*#__PURE__*/_jsxDEV(Switch, {\n                checked: autoScanEnabled,\n                onChange: e => setAutoScanEnabled(e.target.checked),\n                color: \"primary\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 325,\n                columnNumber: 19\n              }, this),\n              label: \"Enable Auto-Scan\",\n              sx: {\n                mb: 2\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 323,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(FormControlLabel, {\n              control: /*#__PURE__*/_jsxDEV(Switch, {\n                checked: smartScheduling,\n                onChange: e => setSmartScheduling(e.target.checked),\n                color: \"primary\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 337,\n                columnNumber: 19\n              }, this),\n              label: \"AI Smart Scheduling\",\n              sx: {\n                mb: 2\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 335,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(FormControlLabel, {\n              control: /*#__PURE__*/_jsxDEV(Switch, {\n                checked: adaptiveScan,\n                onChange: e => setAdaptiveScan(e.target.checked),\n                color: \"primary\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 349,\n                columnNumber: 19\n              }, this),\n              label: \"Adaptive Scan Intensity\",\n              sx: {\n                mb: 3\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 347,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              mb: 3,\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                gutterBottom: true,\n                children: [\"Scan Interval: \", scanInterval, \" minutes\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 360,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Slider, {\n                value: scanInterval,\n                onChange: (e, value) => setScanInterval(value),\n                min: 5,\n                max: 120,\n                step: 5,\n                marks: [{\n                  value: 5,\n                  label: '5m'\n                }, {\n                  value: 30,\n                  label: '30m'\n                }, {\n                  value: 60,\n                  label: '1h'\n                }, {\n                  value: 120,\n                  label: '2h'\n                }],\n                color: \"primary\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 363,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 359,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              mb: 3,\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                gutterBottom: true,\n                children: [\"Scan Intensity: \", getIntensityLabel(scanIntensity)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 380,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Slider, {\n                value: scanIntensity,\n                onChange: (e, value) => setScanIntensity(value),\n                min: 1,\n                max: 3,\n                step: 1,\n                marks: [{\n                  value: 1,\n                  label: 'Light'\n                }, {\n                  value: 2,\n                  label: 'Balanced'\n                }, {\n                  value: 3,\n                  label: 'Intensive'\n                }],\n                color: \"primary\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 383,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 379,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 318,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              gutterBottom: true,\n              fontWeight: \"600\",\n              children: \"AI Recommendations\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 400,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(List, {\n              children: aiRecommendations.map((rec, index) => /*#__PURE__*/_jsxDEV(ListItem, {\n                sx: {\n                  background: theme => theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.02)',\n                  borderRadius: 2,\n                  mb: 1\n                },\n                children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                  children: /*#__PURE__*/_jsxDEV(AIIcon, {\n                    color: \"secondary\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 417,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 416,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: rec.message,\n                  secondary: /*#__PURE__*/_jsxDEV(Chip, {\n                    label: `${rec.confidence}% confidence`,\n                    size: \"small\",\n                    color: rec.confidence > 90 ? 'success' : 'warning',\n                    variant: \"outlined\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 422,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 419,\n                  columnNumber: 21\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 406,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 404,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 399,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 317,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 213,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 178,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          sx: {\n            background: theme => theme.palette.mode === 'dark' ? 'linear-gradient(135deg, rgba(30, 30, 30, 0.95) 0%, rgba(50, 50, 50, 0.95) 100%)' : 'linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%)',\n            backdropFilter: 'blur(20px)',\n            border: theme => `1px solid ${theme.palette.divider}`\n          },\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              display: \"flex\",\n              alignItems: \"center\",\n              gap: 2,\n              mb: 3,\n              children: [/*#__PURE__*/_jsxDEV(Avatar, {\n                sx: {\n                  background: 'linear-gradient(135deg, #ff9800 0%, #ffc107 100%)',\n                  width: 48,\n                  height: 48\n                },\n                children: /*#__PURE__*/_jsxDEV(ScheduleIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 459,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 452,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                fontWeight: \"bold\",\n                children: [\"Scan Queue (\", scanQueue.length, \")\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 461,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 451,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(List, {\n              children: scanQueue.map((item, index) => /*#__PURE__*/_jsxDEV(ListItem, {\n                sx: {\n                  background: theme => theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.02)',\n                  borderRadius: 2,\n                  mb: 1\n                },\n                children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                  children: /*#__PURE__*/_jsxDEV(SecurityIcon, {\n                    color: \"primary\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 479,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 478,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: /*#__PURE__*/_jsxDEV(Box, {\n                    display: \"flex\",\n                    alignItems: \"center\",\n                    gap: 2,\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      fontWeight: \"600\",\n                      children: item.target\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 484,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                      label: item.type,\n                      size: \"small\",\n                      variant: \"outlined\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 487,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                      label: item.priority,\n                      size: \"small\",\n                      color: getPriorityColor(item.priority)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 492,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 483,\n                    columnNumber: 25\n                  }, this),\n                  secondary: `Status: ${item.status}`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 481,\n                  columnNumber: 21\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 468,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 466,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 450,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 441,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 440,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          sx: {\n            background: theme => theme.palette.mode === 'dark' ? 'linear-gradient(135deg, rgba(30, 30, 30, 0.95) 0%, rgba(50, 50, 50, 0.95) 100%)' : 'linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%)',\n            backdropFilter: 'blur(20px)',\n            border: theme => `1px solid ${theme.palette.divider}`\n          },\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              display: \"flex\",\n              alignItems: \"center\",\n              gap: 2,\n              mb: 3,\n              children: [/*#__PURE__*/_jsxDEV(Avatar, {\n                sx: {\n                  background: 'linear-gradient(135deg, #4caf50 0%, #8bc34a 100%)',\n                  width: 48,\n                  height: 48\n                },\n                children: /*#__PURE__*/_jsxDEV(SpeedIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 528,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 521,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                fontWeight: \"bold\",\n                children: \"Recent Auto-Scans\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 530,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 520,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(List, {\n              children: scanHistory.map((scan, index) => /*#__PURE__*/_jsxDEV(ListItem, {\n                sx: {\n                  background: theme => theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.02)',\n                  borderRadius: 2,\n                  mb: 1\n                },\n                children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: /*#__PURE__*/_jsxDEV(Box, {\n                    display: \"flex\",\n                    alignItems: \"center\",\n                    justifyContent: \"space-between\",\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      fontWeight: \"600\",\n                      children: scan.type\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 550,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"caption\",\n                      color: \"text.secondary\",\n                      children: scan.timestamp.toLocaleTimeString()\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 553,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 549,\n                    columnNumber: 25\n                  }, this),\n                  secondary: /*#__PURE__*/_jsxDEV(Box, {\n                    display: \"flex\",\n                    alignItems: \"center\",\n                    gap: 2,\n                    mt: 1,\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"caption\",\n                      children: [scan.targets, \" targets\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 560,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"caption\",\n                      color: scan.threats > 0 ? 'error.main' : 'success.main',\n                      children: [scan.threats, \" threats\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 563,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"caption\",\n                      children: scan.duration\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 566,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 559,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 547,\n                  columnNumber: 21\n                }, this)\n              }, index, false, {\n                fileName: _jsxFileName,\n                lineNumber: 537,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 535,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 519,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 510,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 509,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 438,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 176,\n    columnNumber: 5\n  }, this);\n}, \"3sWh9O3UvN+oPCNLQyCwrzbNBAE=\")), \"3sWh9O3UvN+oPCNLQyCwrzbNBAE=\");\n_c2 = AutoScanSystem;\nAutoScanSystem.displayName = 'AutoScanSystem';\nexport default AutoScanSystem;\nvar _c, _c2;\n$RefreshReg$(_c, \"AutoScanSystem$React.memo\");\n$RefreshReg$(_c2, \"AutoScanSystem\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Typography", "Switch", "FormControlLabel", "Grid", "Chip", "List", "ListItem", "ListItemIcon", "ListItemText", "Avatar", "IconButton", "<PERSON><PERSON><PERSON>", "LinearProgress", "<PERSON><PERSON>", "Slide<PERSON>", "TextField", "<PERSON><PERSON>", "Divider", "AutoMode", "AutoIcon", "Schedule", "ScheduleIcon", "Speed", "SpeedIcon", "Security", "SecurityIcon", "Notifications", "NotificationsIcon", "Settings", "SettingsIcon", "PlayArrow", "PlayIcon", "Pause", "PauseIcon", "Stop", "StopIcon", "Refresh", "RefreshIcon", "Psychology", "AIIcon", "Shield", "ShieldIcon", "jsxDEV", "_jsxDEV", "AutoScanSystem", "_s", "memo", "_c", "autoScanEnabled", "setAutoScanEnabled", "scanInterval", "setScanInterval", "scanIntensity", "setScanIntensity", "smartScheduling", "setSmartScheduling", "adaptiveScan", "setAdaptiveScan", "currentScan", "setCurrentScan", "isRunning", "progress", "currentTarget", "threatsFound", "estimatedTime", "scanQueue", "setScanQueue", "type", "target", "priority", "status", "scanHistory", "setScanHistory", "timestamp", "Date", "now", "targets", "threats", "duration", "aiRecommendations", "setAiRecommendations", "message", "confidence", "interval", "setInterval", "prev", "newProgress", "Math", "min", "random", "floor", "max", "clearInterval", "startAutoScan", "_scanQueue$", "pauseAutoScan", "stopAutoScan", "getIntensityLabel", "value", "getPriorityColor", "sx", "p", "children", "mb", "background", "theme", "palette", "mode", "<PERSON><PERSON>ilter", "border", "borderRadius", "position", "overflow", "boxShadow", "transition", "transform", "content", "top", "left", "right", "height", "backgroundSize", "animation", "backgroundPosition", "display", "alignItems", "gap", "width", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "flex", "variant", "fontWeight", "gutterBottom", "color", "title", "onClick", "disabled", "severity", "icon", "justifyContent", "round", "container", "spacing", "item", "xs", "md", "control", "checked", "onChange", "e", "label", "step", "marks", "map", "rec", "index", "primary", "secondary", "size", "divider", "length", "scan", "toLocaleTimeString", "mt", "_c2", "displayName", "$RefreshReg$"], "sources": ["E:/PythonProjects/React.js/Smart AI Assisstant/src/components/AutoScanSystem.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  <PERSON>,\n  Card,\n  CardContent,\n  Typography,\n  Switch,\n  FormControlLabel,\n  Grid,\n  Chip,\n  List,\n  ListItem,\n  ListItemIcon,\n  ListItemText,\n  Avatar,\n  IconButton,\n  Tooltip,\n  LinearProgress,\n  Alert,\n  Slider,\n  TextField,\n  Button,\n  Divider,\n} from '@mui/material';\nimport {\n  AutoMode as AutoIcon,\n  Schedule as ScheduleIcon,\n  Speed as SpeedIcon,\n  Security as SecurityIcon,\n  Notifications as NotificationsIcon,\n  Settings as SettingsIcon,\n  PlayArrow as PlayIcon,\n  Pause as PauseIcon,\n  Stop as StopIcon,\n  Refresh as RefreshIcon,\n  Psychology as AIIcon,\n  Shield as ShieldIcon,\n} from '@mui/icons-material';\n\nconst AutoScanSystem = React.memo(() => {\n  const [autoScanEnabled, setAutoScanEnabled] = useState(true);\n  const [scanInterval, setScanInterval] = useState(30); // minutes\n  const [scanIntensity, setScanIntensity] = useState(2); // 1-3 scale\n  const [smartScheduling, setSmartScheduling] = useState(true);\n  const [adaptiveScan, setAdaptiveScan] = useState(true);\n\n  const [currentScan, setCurrentScan] = useState({\n    isRunning: false,\n    progress: 0,\n    currentTarget: '',\n    threatsFound: 0,\n    estimatedTime: 0,\n  });\n\n  const [scanQueue, setScanQueue] = useState([\n    { type: 'URL', target: 'https://suspicious-site.com', priority: 'high', status: 'pending' },\n    { type: 'File', target: 'document.pdf', priority: 'medium', status: 'pending' },\n    { type: 'URL', target: 'https://phishing-attempt.net', priority: 'high', status: 'pending' },\n    { type: 'File', target: 'software.exe', priority: 'low', status: 'pending' },\n  ]);\n\n  const [scanHistory, setScanHistory] = useState([\n    {\n      timestamp: new Date(Date.now() - 1000 * 60 * 15),\n      type: 'Auto Scan',\n      targets: 12,\n      threats: 2,\n      duration: '3m 45s',\n      status: 'completed',\n    },\n    {\n      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2),\n      type: 'Scheduled Scan',\n      targets: 8,\n      threats: 0,\n      duration: '2m 12s',\n      status: 'completed',\n    },\n    {\n      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 6),\n      type: 'Priority Scan',\n      targets: 3,\n      threats: 1,\n      duration: '1m 33s',\n      status: 'completed',\n    },\n  ]);\n\n  const [aiRecommendations, setAiRecommendations] = useState([\n    {\n      type: 'schedule',\n      message: 'Increase scan frequency during peak hours (9-17)',\n      confidence: 89,\n    },\n    {\n      type: 'priority',\n      message: 'Focus on .exe files - 67% higher threat probability',\n      confidence: 94,\n    },\n    {\n      type: 'optimization',\n      message: 'Reduce scan intensity for trusted domains',\n      confidence: 82,\n    },\n  ]);\n\n  // Simulate auto-scan progress\n  useEffect(() => {\n    let interval;\n    if (currentScan.isRunning) {\n      interval = setInterval(() => {\n        setCurrentScan(prev => {\n          const newProgress = Math.min(100, prev.progress + Math.random() * 10);\n          if (newProgress >= 100) {\n            return {\n              ...prev,\n              progress: 100,\n              isRunning: false,\n              threatsFound: prev.threatsFound + Math.floor(Math.random() * 3),\n            };\n          }\n          return {\n            ...prev,\n            progress: newProgress,\n            estimatedTime: Math.max(0, prev.estimatedTime - 5),\n          };\n        });\n      }, 1000);\n    }\n    return () => clearInterval(interval);\n  }, [currentScan.isRunning]);\n\n  const startAutoScan = () => {\n    setCurrentScan({\n      isRunning: true,\n      progress: 0,\n      currentTarget: scanQueue[0]?.target || 'Multiple targets',\n      threatsFound: 0,\n      estimatedTime: 180, // 3 minutes\n    });\n  };\n\n  const pauseAutoScan = () => {\n    setCurrentScan(prev => ({ ...prev, isRunning: false }));\n  };\n\n  const stopAutoScan = () => {\n    setCurrentScan({\n      isRunning: false,\n      progress: 0,\n      currentTarget: '',\n      threatsFound: 0,\n      estimatedTime: 0,\n    });\n  };\n\n  const getIntensityLabel = (value) => {\n    switch (value) {\n      case 1: return 'Light';\n      case 2: return 'Balanced';\n      case 3: return 'Intensive';\n      default: return 'Balanced';\n    }\n  };\n\n  const getPriorityColor = (priority) => {\n    switch (priority) {\n      case 'high': return 'error';\n      case 'medium': return 'warning';\n      case 'low': return 'success';\n      default: return 'default';\n    }\n  };\n\n  return (\n    <Box sx={{ p: 3 }}>\n      {/* Auto-Scan Controls - Data Flow */}\n      <Card\n        sx={{\n          mb: 4,\n          background: (theme) => theme.palette.mode === 'dark'\n            ? 'linear-gradient(135deg, rgba(12, 12, 12, 0.95) 0%, rgba(26, 26, 26, 0.95) 50%, rgba(45, 45, 45, 0.95) 100%)'\n            : 'linear-gradient(135deg, rgba(248, 250, 252, 0.95) 0%, rgba(241, 245, 249, 0.95) 50%, rgba(226, 232, 240, 0.95) 100%)',\n          backdropFilter: 'blur(25px) saturate(180%)',\n          border: '1px solid rgba(139, 92, 246, 0.2)',\n          borderRadius: 4,\n          position: 'relative',\n          overflow: 'hidden',\n          boxShadow: '0 8px 32px rgba(139, 92, 246, 0.2)',\n          transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',\n          '&:hover': {\n            transform: 'translateY(-2px)',\n            boxShadow: '0 16px 48px rgba(139, 92, 246, 0.3)',\n          },\n          '&::before': {\n            content: '\"\"',\n            position: 'absolute',\n            top: 0,\n            left: 0,\n            right: 0,\n            height: '4px',\n            background: 'linear-gradient(90deg, #8b5cf6 0%, #a855f7 50%, #c084fc 100%)',\n            backgroundSize: '200% 200%',\n            animation: 'dataFlow 5s ease infinite',\n          },\n          '@keyframes dataFlow': {\n            '0%': { backgroundPosition: '0% 50%' },\n            '50%': { backgroundPosition: '100% 50%' },\n            '100%': { backgroundPosition: '0% 50%' },\n          },\n        }}\n      >\n        <CardContent>\n          <Box display=\"flex\" alignItems=\"center\" gap={2} mb={3}>\n            <Avatar\n              sx={{\n                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                width: 56,\n                height: 56,\n              }}\n            >\n              <AutoIcon sx={{ fontSize: 28 }} />\n            </Avatar>\n            <Box flex={1}>\n              <Typography variant=\"h4\" fontWeight=\"bold\" gutterBottom>\n                Intelligent Auto-Scan System\n              </Typography>\n              <Typography variant=\"body1\" color=\"text.secondary\">\n                AI-powered automated security scanning with adaptive scheduling\n              </Typography>\n            </Box>\n            <Box display=\"flex\" gap={1}>\n              <Tooltip title=\"Start Auto Scan\">\n                <IconButton\n                  onClick={startAutoScan}\n                  disabled={currentScan.isRunning}\n                  sx={{\n                    background: 'linear-gradient(135deg, #4caf50 0%, #8bc34a 100%)',\n                    color: 'white',\n                    '&:hover': {\n                      background: 'linear-gradient(135deg, #45a049 0%, #7cb342 100%)',\n                    },\n                  }}\n                >\n                  <PlayIcon />\n                </IconButton>\n              </Tooltip>\n              <Tooltip title=\"Pause\">\n                <IconButton\n                  onClick={pauseAutoScan}\n                  disabled={!currentScan.isRunning}\n                  sx={{\n                    background: 'linear-gradient(135deg, #ff9800 0%, #ffc107 100%)',\n                    color: 'white',\n                    '&:hover': {\n                      background: 'linear-gradient(135deg, #f57c00 0%, #ffb300 100%)',\n                    },\n                  }}\n                >\n                  <PauseIcon />\n                </IconButton>\n              </Tooltip>\n              <Tooltip title=\"Stop\">\n                <IconButton\n                  onClick={stopAutoScan}\n                  sx={{\n                    background: 'linear-gradient(135deg, #f44336 0%, #ff5722 100%)',\n                    color: 'white',\n                    '&:hover': {\n                      background: 'linear-gradient(135deg, #d32f2f 0%, #f4511e 100%)',\n                    },\n                  }}\n                >\n                  <StopIcon />\n                </IconButton>\n              </Tooltip>\n            </Box>\n          </Box>\n\n          {/* Current Scan Status */}\n          {(currentScan.isRunning || currentScan.progress > 0) && (\n            <Alert\n              severity={currentScan.isRunning ? 'info' : 'success'}\n              sx={{ mb: 3 }}\n              icon={currentScan.isRunning ? <RefreshIcon /> : <ShieldIcon />}\n            >\n              <Box>\n                <Typography variant=\"subtitle2\" fontWeight=\"600\">\n                  {currentScan.isRunning ? 'Scanning in Progress' : 'Scan Completed'}\n                </Typography>\n                <Typography variant=\"body2\" sx={{ mb: 1 }}>\n                  Target: {currentScan.currentTarget}\n                </Typography>\n                <LinearProgress\n                  variant=\"determinate\"\n                  value={currentScan.progress}\n                  sx={{ mb: 1, height: 6, borderRadius: 3 }}\n                />\n                <Box display=\"flex\" justifyContent=\"space-between\" alignItems=\"center\">\n                  <Typography variant=\"caption\">\n                    Progress: {Math.round(currentScan.progress)}%\n                  </Typography>\n                  <Typography variant=\"caption\">\n                    Threats Found: {currentScan.threatsFound}\n                  </Typography>\n                  {currentScan.isRunning && (\n                    <Typography variant=\"caption\">\n                      ETA: {Math.floor(currentScan.estimatedTime / 60)}m {currentScan.estimatedTime % 60}s\n                    </Typography>\n                  )}\n                </Box>\n              </Box>\n            </Alert>\n          )}\n\n          {/* Auto-Scan Settings */}\n          <Grid container spacing={3}>\n            <Grid item xs={12} md={6}>\n              <Typography variant=\"h6\" gutterBottom fontWeight=\"600\">\n                Auto-Scan Configuration\n              </Typography>\n              \n              <FormControlLabel\n                control={\n                  <Switch\n                    checked={autoScanEnabled}\n                    onChange={(e) => setAutoScanEnabled(e.target.checked)}\n                    color=\"primary\"\n                  />\n                }\n                label=\"Enable Auto-Scan\"\n                sx={{ mb: 2 }}\n              />\n\n              <FormControlLabel\n                control={\n                  <Switch\n                    checked={smartScheduling}\n                    onChange={(e) => setSmartScheduling(e.target.checked)}\n                    color=\"primary\"\n                  />\n                }\n                label=\"AI Smart Scheduling\"\n                sx={{ mb: 2 }}\n              />\n\n              <FormControlLabel\n                control={\n                  <Switch\n                    checked={adaptiveScan}\n                    onChange={(e) => setAdaptiveScan(e.target.checked)}\n                    color=\"primary\"\n                  />\n                }\n                label=\"Adaptive Scan Intensity\"\n                sx={{ mb: 3 }}\n              />\n\n              <Box mb={3}>\n                <Typography variant=\"body2\" gutterBottom>\n                  Scan Interval: {scanInterval} minutes\n                </Typography>\n                <Slider\n                  value={scanInterval}\n                  onChange={(e, value) => setScanInterval(value)}\n                  min={5}\n                  max={120}\n                  step={5}\n                  marks={[\n                    { value: 5, label: '5m' },\n                    { value: 30, label: '30m' },\n                    { value: 60, label: '1h' },\n                    { value: 120, label: '2h' },\n                  ]}\n                  color=\"primary\"\n                />\n              </Box>\n\n              <Box mb={3}>\n                <Typography variant=\"body2\" gutterBottom>\n                  Scan Intensity: {getIntensityLabel(scanIntensity)}\n                </Typography>\n                <Slider\n                  value={scanIntensity}\n                  onChange={(e, value) => setScanIntensity(value)}\n                  min={1}\n                  max={3}\n                  step={1}\n                  marks={[\n                    { value: 1, label: 'Light' },\n                    { value: 2, label: 'Balanced' },\n                    { value: 3, label: 'Intensive' },\n                  ]}\n                  color=\"primary\"\n                />\n              </Box>\n            </Grid>\n\n            <Grid item xs={12} md={6}>\n              <Typography variant=\"h6\" gutterBottom fontWeight=\"600\">\n                AI Recommendations\n              </Typography>\n              \n              <List>\n                {aiRecommendations.map((rec, index) => (\n                  <ListItem\n                    key={index}\n                    sx={{\n                      background: (theme) => theme.palette.mode === 'dark'\n                        ? 'rgba(255, 255, 255, 0.05)'\n                        : 'rgba(0, 0, 0, 0.02)',\n                      borderRadius: 2,\n                      mb: 1,\n                    }}\n                  >\n                    <ListItemIcon>\n                      <AIIcon color=\"secondary\" />\n                    </ListItemIcon>\n                    <ListItemText\n                      primary={rec.message}\n                      secondary={\n                        <Chip\n                          label={`${rec.confidence}% confidence`}\n                          size=\"small\"\n                          color={rec.confidence > 90 ? 'success' : 'warning'}\n                          variant=\"outlined\"\n                        />\n                      }\n                    />\n                  </ListItem>\n                ))}\n              </List>\n            </Grid>\n          </Grid>\n        </CardContent>\n      </Card>\n\n      <Grid container spacing={3}>\n        {/* Scan Queue */}\n        <Grid item xs={12} md={6}>\n          <Card\n            sx={{\n              background: (theme) => theme.palette.mode === 'dark'\n                ? 'linear-gradient(135deg, rgba(30, 30, 30, 0.95) 0%, rgba(50, 50, 50, 0.95) 100%)'\n                : 'linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%)',\n              backdropFilter: 'blur(20px)',\n              border: (theme) => `1px solid ${theme.palette.divider}`,\n            }}\n          >\n            <CardContent>\n              <Box display=\"flex\" alignItems=\"center\" gap={2} mb={3}>\n                <Avatar\n                  sx={{\n                    background: 'linear-gradient(135deg, #ff9800 0%, #ffc107 100%)',\n                    width: 48,\n                    height: 48,\n                  }}\n                >\n                  <ScheduleIcon />\n                </Avatar>\n                <Typography variant=\"h6\" fontWeight=\"bold\">\n                  Scan Queue ({scanQueue.length})\n                </Typography>\n              </Box>\n\n              <List>\n                {scanQueue.map((item, index) => (\n                  <ListItem\n                    key={index}\n                    sx={{\n                      background: (theme) => theme.palette.mode === 'dark'\n                        ? 'rgba(255, 255, 255, 0.05)'\n                        : 'rgba(0, 0, 0, 0.02)',\n                      borderRadius: 2,\n                      mb: 1,\n                    }}\n                  >\n                    <ListItemIcon>\n                      <SecurityIcon color=\"primary\" />\n                    </ListItemIcon>\n                    <ListItemText\n                      primary={\n                        <Box display=\"flex\" alignItems=\"center\" gap={2}>\n                          <Typography variant=\"body2\" fontWeight=\"600\">\n                            {item.target}\n                          </Typography>\n                          <Chip\n                            label={item.type}\n                            size=\"small\"\n                            variant=\"outlined\"\n                          />\n                          <Chip\n                            label={item.priority}\n                            size=\"small\"\n                            color={getPriorityColor(item.priority)}\n                          />\n                        </Box>\n                      }\n                      secondary={`Status: ${item.status}`}\n                    />\n                  </ListItem>\n                ))}\n              </List>\n            </CardContent>\n          </Card>\n        </Grid>\n\n        {/* Recent Scans */}\n        <Grid item xs={12} md={6}>\n          <Card\n            sx={{\n              background: (theme) => theme.palette.mode === 'dark'\n                ? 'linear-gradient(135deg, rgba(30, 30, 30, 0.95) 0%, rgba(50, 50, 50, 0.95) 100%)'\n                : 'linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%)',\n              backdropFilter: 'blur(20px)',\n              border: (theme) => `1px solid ${theme.palette.divider}`,\n            }}\n          >\n            <CardContent>\n              <Box display=\"flex\" alignItems=\"center\" gap={2} mb={3}>\n                <Avatar\n                  sx={{\n                    background: 'linear-gradient(135deg, #4caf50 0%, #8bc34a 100%)',\n                    width: 48,\n                    height: 48,\n                  }}\n                >\n                  <SpeedIcon />\n                </Avatar>\n                <Typography variant=\"h6\" fontWeight=\"bold\">\n                  Recent Auto-Scans\n                </Typography>\n              </Box>\n\n              <List>\n                {scanHistory.map((scan, index) => (\n                  <ListItem\n                    key={index}\n                    sx={{\n                      background: (theme) => theme.palette.mode === 'dark'\n                        ? 'rgba(255, 255, 255, 0.05)'\n                        : 'rgba(0, 0, 0, 0.02)',\n                      borderRadius: 2,\n                      mb: 1,\n                    }}\n                  >\n                    <ListItemText\n                      primary={\n                        <Box display=\"flex\" alignItems=\"center\" justifyContent=\"space-between\">\n                          <Typography variant=\"body2\" fontWeight=\"600\">\n                            {scan.type}\n                          </Typography>\n                          <Typography variant=\"caption\" color=\"text.secondary\">\n                            {scan.timestamp.toLocaleTimeString()}\n                          </Typography>\n                        </Box>\n                      }\n                      secondary={\n                        <Box display=\"flex\" alignItems=\"center\" gap={2} mt={1}>\n                          <Typography variant=\"caption\">\n                            {scan.targets} targets\n                          </Typography>\n                          <Typography variant=\"caption\" color={scan.threats > 0 ? 'error.main' : 'success.main'}>\n                            {scan.threats} threats\n                          </Typography>\n                          <Typography variant=\"caption\">\n                            {scan.duration}\n                          </Typography>\n                        </Box>\n                      }\n                    />\n                  </ListItem>\n                ))}\n              </List>\n            </CardContent>\n          </Card>\n        </Grid>\n      </Grid>\n    </Box>\n  );\n});\n\nAutoScanSystem.displayName = 'AutoScanSystem';\n\nexport default AutoScanSystem;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,gBAAgB,EAChBC,IAAI,EACJC,IAAI,EACJC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,YAAY,EACZC,MAAM,EACNC,UAAU,EACVC,OAAO,EACPC,cAAc,EACdC,KAAK,EACLC,MAAM,EACNC,SAAS,EACTC,MAAM,EACNC,OAAO,QACF,eAAe;AACtB,SACEC,QAAQ,IAAIC,QAAQ,EACpBC,QAAQ,IAAIC,YAAY,EACxBC,KAAK,IAAIC,SAAS,EAClBC,QAAQ,IAAIC,YAAY,EACxBC,aAAa,IAAIC,iBAAiB,EAClCC,QAAQ,IAAIC,YAAY,EACxBC,SAAS,IAAIC,QAAQ,EACrBC,KAAK,IAAIC,SAAS,EAClBC,IAAI,IAAIC,QAAQ,EAChBC,OAAO,IAAIC,WAAW,EACtBC,UAAU,IAAIC,MAAM,EACpBC,MAAM,IAAIC,UAAU,QACf,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7B,MAAMC,cAAc,gBAAAC,EAAA,cAAGnD,KAAK,CAACoD,IAAI,CAAAC,EAAA,GAAAF,EAAA,CAAC,MAAM;EAAAA,EAAA;EACtC,MAAM,CAACG,eAAe,EAAEC,kBAAkB,CAAC,GAAGtD,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACuD,YAAY,EAAEC,eAAe,CAAC,GAAGxD,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EACtD,MAAM,CAACyD,aAAa,EAAEC,gBAAgB,CAAC,GAAG1D,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;EACvD,MAAM,CAAC2D,eAAe,EAAEC,kBAAkB,CAAC,GAAG5D,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAAC6D,YAAY,EAAEC,eAAe,CAAC,GAAG9D,QAAQ,CAAC,IAAI,CAAC;EAEtD,MAAM,CAAC+D,WAAW,EAAEC,cAAc,CAAC,GAAGhE,QAAQ,CAAC;IAC7CiE,SAAS,EAAE,KAAK;IAChBC,QAAQ,EAAE,CAAC;IACXC,aAAa,EAAE,EAAE;IACjBC,YAAY,EAAE,CAAC;IACfC,aAAa,EAAE;EACjB,CAAC,CAAC;EAEF,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGvE,QAAQ,CAAC,CACzC;IAAEwE,IAAI,EAAE,KAAK;IAAEC,MAAM,EAAE,6BAA6B;IAAEC,QAAQ,EAAE,MAAM;IAAEC,MAAM,EAAE;EAAU,CAAC,EAC3F;IAAEH,IAAI,EAAE,MAAM;IAAEC,MAAM,EAAE,cAAc;IAAEC,QAAQ,EAAE,QAAQ;IAAEC,MAAM,EAAE;EAAU,CAAC,EAC/E;IAAEH,IAAI,EAAE,KAAK;IAAEC,MAAM,EAAE,8BAA8B;IAAEC,QAAQ,EAAE,MAAM;IAAEC,MAAM,EAAE;EAAU,CAAC,EAC5F;IAAEH,IAAI,EAAE,MAAM;IAAEC,MAAM,EAAE,cAAc;IAAEC,QAAQ,EAAE,KAAK;IAAEC,MAAM,EAAE;EAAU,CAAC,CAC7E,CAAC;EAEF,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG7E,QAAQ,CAAC,CAC7C;IACE8E,SAAS,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC;IAChDR,IAAI,EAAE,WAAW;IACjBS,OAAO,EAAE,EAAE;IACXC,OAAO,EAAE,CAAC;IACVC,QAAQ,EAAE,QAAQ;IAClBR,MAAM,EAAE;EACV,CAAC,EACD;IACEG,SAAS,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;IACpDR,IAAI,EAAE,gBAAgB;IACtBS,OAAO,EAAE,CAAC;IACVC,OAAO,EAAE,CAAC;IACVC,QAAQ,EAAE,QAAQ;IAClBR,MAAM,EAAE;EACV,CAAC,EACD;IACEG,SAAS,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;IACpDR,IAAI,EAAE,eAAe;IACrBS,OAAO,EAAE,CAAC;IACVC,OAAO,EAAE,CAAC;IACVC,QAAQ,EAAE,QAAQ;IAClBR,MAAM,EAAE;EACV,CAAC,CACF,CAAC;EAEF,MAAM,CAACS,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGrF,QAAQ,CAAC,CACzD;IACEwE,IAAI,EAAE,UAAU;IAChBc,OAAO,EAAE,kDAAkD;IAC3DC,UAAU,EAAE;EACd,CAAC,EACD;IACEf,IAAI,EAAE,UAAU;IAChBc,OAAO,EAAE,qDAAqD;IAC9DC,UAAU,EAAE;EACd,CAAC,EACD;IACEf,IAAI,EAAE,cAAc;IACpBc,OAAO,EAAE,2CAA2C;IACpDC,UAAU,EAAE;EACd,CAAC,CACF,CAAC;;EAEF;EACAtF,SAAS,CAAC,MAAM;IACd,IAAIuF,QAAQ;IACZ,IAAIzB,WAAW,CAACE,SAAS,EAAE;MACzBuB,QAAQ,GAAGC,WAAW,CAAC,MAAM;QAC3BzB,cAAc,CAAC0B,IAAI,IAAI;UACrB,MAAMC,WAAW,GAAGC,IAAI,CAACC,GAAG,CAAC,GAAG,EAAEH,IAAI,CAACxB,QAAQ,GAAG0B,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC;UACrE,IAAIH,WAAW,IAAI,GAAG,EAAE;YACtB,OAAO;cACL,GAAGD,IAAI;cACPxB,QAAQ,EAAE,GAAG;cACbD,SAAS,EAAE,KAAK;cAChBG,YAAY,EAAEsB,IAAI,CAACtB,YAAY,GAAGwB,IAAI,CAACG,KAAK,CAACH,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,CAAC;YAChE,CAAC;UACH;UACA,OAAO;YACL,GAAGJ,IAAI;YACPxB,QAAQ,EAAEyB,WAAW;YACrBtB,aAAa,EAAEuB,IAAI,CAACI,GAAG,CAAC,CAAC,EAAEN,IAAI,CAACrB,aAAa,GAAG,CAAC;UACnD,CAAC;QACH,CAAC,CAAC;MACJ,CAAC,EAAE,IAAI,CAAC;IACV;IACA,OAAO,MAAM4B,aAAa,CAACT,QAAQ,CAAC;EACtC,CAAC,EAAE,CAACzB,WAAW,CAACE,SAAS,CAAC,CAAC;EAE3B,MAAMiC,aAAa,GAAGA,CAAA,KAAM;IAAA,IAAAC,WAAA;IAC1BnC,cAAc,CAAC;MACbC,SAAS,EAAE,IAAI;MACfC,QAAQ,EAAE,CAAC;MACXC,aAAa,EAAE,EAAAgC,WAAA,GAAA7B,SAAS,CAAC,CAAC,CAAC,cAAA6B,WAAA,uBAAZA,WAAA,CAAc1B,MAAM,KAAI,kBAAkB;MACzDL,YAAY,EAAE,CAAC;MACfC,aAAa,EAAE,GAAG,CAAE;IACtB,CAAC,CAAC;EACJ,CAAC;EAED,MAAM+B,aAAa,GAAGA,CAAA,KAAM;IAC1BpC,cAAc,CAAC0B,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAEzB,SAAS,EAAE;IAAM,CAAC,CAAC,CAAC;EACzD,CAAC;EAED,MAAMoC,YAAY,GAAGA,CAAA,KAAM;IACzBrC,cAAc,CAAC;MACbC,SAAS,EAAE,KAAK;MAChBC,QAAQ,EAAE,CAAC;MACXC,aAAa,EAAE,EAAE;MACjBC,YAAY,EAAE,CAAC;MACfC,aAAa,EAAE;IACjB,CAAC,CAAC;EACJ,CAAC;EAED,MAAMiC,iBAAiB,GAAIC,KAAK,IAAK;IACnC,QAAQA,KAAK;MACX,KAAK,CAAC;QAAE,OAAO,OAAO;MACtB,KAAK,CAAC;QAAE,OAAO,UAAU;MACzB,KAAK,CAAC;QAAE,OAAO,WAAW;MAC1B;QAAS,OAAO,UAAU;IAC5B;EACF,CAAC;EAED,MAAMC,gBAAgB,GAAI9B,QAAQ,IAAK;IACrC,QAAQA,QAAQ;MACd,KAAK,MAAM;QAAE,OAAO,OAAO;MAC3B,KAAK,QAAQ;QAAE,OAAO,SAAS;MAC/B,KAAK,KAAK;QAAE,OAAO,SAAS;MAC5B;QAAS,OAAO,SAAS;IAC3B;EACF,CAAC;EAED,oBACE1B,OAAA,CAAC9C,GAAG;IAACuG,EAAE,EAAE;MAAEC,CAAC,EAAE;IAAE,CAAE;IAAAC,QAAA,gBAEhB3D,OAAA,CAAC7C,IAAI;MACHsG,EAAE,EAAE;QACFG,EAAE,EAAE,CAAC;QACLC,UAAU,EAAGC,KAAK,IAAKA,KAAK,CAACC,OAAO,CAACC,IAAI,KAAK,MAAM,GAChD,6GAA6G,GAC7G,sHAAsH;QAC1HC,cAAc,EAAE,2BAA2B;QAC3CC,MAAM,EAAE,mCAAmC;QAC3CC,YAAY,EAAE,CAAC;QACfC,QAAQ,EAAE,UAAU;QACpBC,QAAQ,EAAE,QAAQ;QAClBC,SAAS,EAAE,oCAAoC;QAC/CC,UAAU,EAAE,uCAAuC;QACnD,SAAS,EAAE;UACTC,SAAS,EAAE,kBAAkB;UAC7BF,SAAS,EAAE;QACb,CAAC;QACD,WAAW,EAAE;UACXG,OAAO,EAAE,IAAI;UACbL,QAAQ,EAAE,UAAU;UACpBM,GAAG,EAAE,CAAC;UACNC,IAAI,EAAE,CAAC;UACPC,KAAK,EAAE,CAAC;UACRC,MAAM,EAAE,KAAK;UACbhB,UAAU,EAAE,+DAA+D;UAC3EiB,cAAc,EAAE,WAAW;UAC3BC,SAAS,EAAE;QACb,CAAC;QACD,qBAAqB,EAAE;UACrB,IAAI,EAAE;YAAEC,kBAAkB,EAAE;UAAS,CAAC;UACtC,KAAK,EAAE;YAAEA,kBAAkB,EAAE;UAAW,CAAC;UACzC,MAAM,EAAE;YAAEA,kBAAkB,EAAE;UAAS;QACzC;MACF,CAAE;MAAArB,QAAA,eAEF3D,OAAA,CAAC5C,WAAW;QAAAuG,QAAA,gBACV3D,OAAA,CAAC9C,GAAG;UAAC+H,OAAO,EAAC,MAAM;UAACC,UAAU,EAAC,QAAQ;UAACC,GAAG,EAAE,CAAE;UAACvB,EAAE,EAAE,CAAE;UAAAD,QAAA,gBACpD3D,OAAA,CAAClC,MAAM;YACL2F,EAAE,EAAE;cACFI,UAAU,EAAE,mDAAmD;cAC/DuB,KAAK,EAAE,EAAE;cACTP,MAAM,EAAE;YACV,CAAE;YAAAlB,QAAA,eAEF3D,OAAA,CAACxB,QAAQ;cAACiF,EAAE,EAAE;gBAAE4B,QAAQ,EAAE;cAAG;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CAAC,eACTzF,OAAA,CAAC9C,GAAG;YAACwI,IAAI,EAAE,CAAE;YAAA/B,QAAA,gBACX3D,OAAA,CAAC3C,UAAU;cAACsI,OAAO,EAAC,IAAI;cAACC,UAAU,EAAC,MAAM;cAACC,YAAY;cAAAlC,QAAA,EAAC;YAExD;cAAA2B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbzF,OAAA,CAAC3C,UAAU;cAACsI,OAAO,EAAC,OAAO;cAACG,KAAK,EAAC,gBAAgB;cAAAnC,QAAA,EAAC;YAEnD;cAAA2B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACNzF,OAAA,CAAC9C,GAAG;YAAC+H,OAAO,EAAC,MAAM;YAACE,GAAG,EAAE,CAAE;YAAAxB,QAAA,gBACzB3D,OAAA,CAAChC,OAAO;cAAC+H,KAAK,EAAC,iBAAiB;cAAApC,QAAA,eAC9B3D,OAAA,CAACjC,UAAU;gBACTiI,OAAO,EAAE9C,aAAc;gBACvB+C,QAAQ,EAAElF,WAAW,CAACE,SAAU;gBAChCwC,EAAE,EAAE;kBACFI,UAAU,EAAE,mDAAmD;kBAC/DiC,KAAK,EAAE,OAAO;kBACd,SAAS,EAAE;oBACTjC,UAAU,EAAE;kBACd;gBACF,CAAE;gBAAAF,QAAA,eAEF3D,OAAA,CAACZ,QAAQ;kBAAAkG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACVzF,OAAA,CAAChC,OAAO;cAAC+H,KAAK,EAAC,OAAO;cAAApC,QAAA,eACpB3D,OAAA,CAACjC,UAAU;gBACTiI,OAAO,EAAE5C,aAAc;gBACvB6C,QAAQ,EAAE,CAAClF,WAAW,CAACE,SAAU;gBACjCwC,EAAE,EAAE;kBACFI,UAAU,EAAE,mDAAmD;kBAC/DiC,KAAK,EAAE,OAAO;kBACd,SAAS,EAAE;oBACTjC,UAAU,EAAE;kBACd;gBACF,CAAE;gBAAAF,QAAA,eAEF3D,OAAA,CAACV,SAAS;kBAAAgG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACVzF,OAAA,CAAChC,OAAO;cAAC+H,KAAK,EAAC,MAAM;cAAApC,QAAA,eACnB3D,OAAA,CAACjC,UAAU;gBACTiI,OAAO,EAAE3C,YAAa;gBACtBI,EAAE,EAAE;kBACFI,UAAU,EAAE,mDAAmD;kBAC/DiC,KAAK,EAAE,OAAO;kBACd,SAAS,EAAE;oBACTjC,UAAU,EAAE;kBACd;gBACF,CAAE;gBAAAF,QAAA,eAEF3D,OAAA,CAACR,QAAQ;kBAAA8F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAGL,CAAC1E,WAAW,CAACE,SAAS,IAAIF,WAAW,CAACG,QAAQ,GAAG,CAAC,kBACjDlB,OAAA,CAAC9B,KAAK;UACJgI,QAAQ,EAAEnF,WAAW,CAACE,SAAS,GAAG,MAAM,GAAG,SAAU;UACrDwC,EAAE,EAAE;YAAEG,EAAE,EAAE;UAAE,CAAE;UACduC,IAAI,EAAEpF,WAAW,CAACE,SAAS,gBAAGjB,OAAA,CAACN,WAAW;YAAA4F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAAGzF,OAAA,CAACF,UAAU;YAAAwF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAA9B,QAAA,eAE/D3D,OAAA,CAAC9C,GAAG;YAAAyG,QAAA,gBACF3D,OAAA,CAAC3C,UAAU;cAACsI,OAAO,EAAC,WAAW;cAACC,UAAU,EAAC,KAAK;cAAAjC,QAAA,EAC7C5C,WAAW,CAACE,SAAS,GAAG,sBAAsB,GAAG;YAAgB;cAAAqE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxD,CAAC,eACbzF,OAAA,CAAC3C,UAAU;cAACsI,OAAO,EAAC,OAAO;cAAClC,EAAE,EAAE;gBAAEG,EAAE,EAAE;cAAE,CAAE;cAAAD,QAAA,GAAC,UACjC,EAAC5C,WAAW,CAACI,aAAa;YAAA;cAAAmE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB,CAAC,eACbzF,OAAA,CAAC/B,cAAc;cACb0H,OAAO,EAAC,aAAa;cACrBpC,KAAK,EAAExC,WAAW,CAACG,QAAS;cAC5BuC,EAAE,EAAE;gBAAEG,EAAE,EAAE,CAAC;gBAAEiB,MAAM,EAAE,CAAC;gBAAEV,YAAY,EAAE;cAAE;YAAE;cAAAmB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC,eACFzF,OAAA,CAAC9C,GAAG;cAAC+H,OAAO,EAAC,MAAM;cAACmB,cAAc,EAAC,eAAe;cAAClB,UAAU,EAAC,QAAQ;cAAAvB,QAAA,gBACpE3D,OAAA,CAAC3C,UAAU;gBAACsI,OAAO,EAAC,SAAS;gBAAAhC,QAAA,GAAC,YAClB,EAACf,IAAI,CAACyD,KAAK,CAACtF,WAAW,CAACG,QAAQ,CAAC,EAAC,GAC9C;cAAA;gBAAAoE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbzF,OAAA,CAAC3C,UAAU;gBAACsI,OAAO,EAAC,SAAS;gBAAAhC,QAAA,GAAC,iBACb,EAAC5C,WAAW,CAACK,YAAY;cAAA;gBAAAkE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CAAC,EACZ1E,WAAW,CAACE,SAAS,iBACpBjB,OAAA,CAAC3C,UAAU;gBAACsI,OAAO,EAAC,SAAS;gBAAAhC,QAAA,GAAC,OACvB,EAACf,IAAI,CAACG,KAAK,CAAChC,WAAW,CAACM,aAAa,GAAG,EAAE,CAAC,EAAC,IAAE,EAACN,WAAW,CAACM,aAAa,GAAG,EAAE,EAAC,GACrF;cAAA;gBAAAiE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CACb;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CACR,eAGDzF,OAAA,CAACxC,IAAI;UAAC8I,SAAS;UAACC,OAAO,EAAE,CAAE;UAAA5C,QAAA,gBACzB3D,OAAA,CAACxC,IAAI;YAACgJ,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA/C,QAAA,gBACvB3D,OAAA,CAAC3C,UAAU;cAACsI,OAAO,EAAC,IAAI;cAACE,YAAY;cAACD,UAAU,EAAC,KAAK;cAAAjC,QAAA,EAAC;YAEvD;cAAA2B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAEbzF,OAAA,CAACzC,gBAAgB;cACfoJ,OAAO,eACL3G,OAAA,CAAC1C,MAAM;gBACLsJ,OAAO,EAAEvG,eAAgB;gBACzBwG,QAAQ,EAAGC,CAAC,IAAKxG,kBAAkB,CAACwG,CAAC,CAACrF,MAAM,CAACmF,OAAO,CAAE;gBACtDd,KAAK,EAAC;cAAS;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChB,CACF;cACDsB,KAAK,EAAC,kBAAkB;cACxBtD,EAAE,EAAE;gBAAEG,EAAE,EAAE;cAAE;YAAE;cAAA0B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC,eAEFzF,OAAA,CAACzC,gBAAgB;cACfoJ,OAAO,eACL3G,OAAA,CAAC1C,MAAM;gBACLsJ,OAAO,EAAEjG,eAAgB;gBACzBkG,QAAQ,EAAGC,CAAC,IAAKlG,kBAAkB,CAACkG,CAAC,CAACrF,MAAM,CAACmF,OAAO,CAAE;gBACtDd,KAAK,EAAC;cAAS;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChB,CACF;cACDsB,KAAK,EAAC,qBAAqB;cAC3BtD,EAAE,EAAE;gBAAEG,EAAE,EAAE;cAAE;YAAE;cAAA0B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC,eAEFzF,OAAA,CAACzC,gBAAgB;cACfoJ,OAAO,eACL3G,OAAA,CAAC1C,MAAM;gBACLsJ,OAAO,EAAE/F,YAAa;gBACtBgG,QAAQ,EAAGC,CAAC,IAAKhG,eAAe,CAACgG,CAAC,CAACrF,MAAM,CAACmF,OAAO,CAAE;gBACnDd,KAAK,EAAC;cAAS;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChB,CACF;cACDsB,KAAK,EAAC,yBAAyB;cAC/BtD,EAAE,EAAE;gBAAEG,EAAE,EAAE;cAAE;YAAE;cAAA0B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC,eAEFzF,OAAA,CAAC9C,GAAG;cAAC0G,EAAE,EAAE,CAAE;cAAAD,QAAA,gBACT3D,OAAA,CAAC3C,UAAU;gBAACsI,OAAO,EAAC,OAAO;gBAACE,YAAY;gBAAAlC,QAAA,GAAC,iBACxB,EAACpD,YAAY,EAAC,UAC/B;cAAA;gBAAA+E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbzF,OAAA,CAAC7B,MAAM;gBACLoF,KAAK,EAAEhD,YAAa;gBACpBsG,QAAQ,EAAEA,CAACC,CAAC,EAAEvD,KAAK,KAAK/C,eAAe,CAAC+C,KAAK,CAAE;gBAC/CV,GAAG,EAAE,CAAE;gBACPG,GAAG,EAAE,GAAI;gBACTgE,IAAI,EAAE,CAAE;gBACRC,KAAK,EAAE,CACL;kBAAE1D,KAAK,EAAE,CAAC;kBAAEwD,KAAK,EAAE;gBAAK,CAAC,EACzB;kBAAExD,KAAK,EAAE,EAAE;kBAAEwD,KAAK,EAAE;gBAAM,CAAC,EAC3B;kBAAExD,KAAK,EAAE,EAAE;kBAAEwD,KAAK,EAAE;gBAAK,CAAC,EAC1B;kBAAExD,KAAK,EAAE,GAAG;kBAAEwD,KAAK,EAAE;gBAAK,CAAC,CAC3B;gBACFjB,KAAK,EAAC;cAAS;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENzF,OAAA,CAAC9C,GAAG;cAAC0G,EAAE,EAAE,CAAE;cAAAD,QAAA,gBACT3D,OAAA,CAAC3C,UAAU;gBAACsI,OAAO,EAAC,OAAO;gBAACE,YAAY;gBAAAlC,QAAA,GAAC,kBACvB,EAACL,iBAAiB,CAAC7C,aAAa,CAAC;cAAA;gBAAA6E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvC,CAAC,eACbzF,OAAA,CAAC7B,MAAM;gBACLoF,KAAK,EAAE9C,aAAc;gBACrBoG,QAAQ,EAAEA,CAACC,CAAC,EAAEvD,KAAK,KAAK7C,gBAAgB,CAAC6C,KAAK,CAAE;gBAChDV,GAAG,EAAE,CAAE;gBACPG,GAAG,EAAE,CAAE;gBACPgE,IAAI,EAAE,CAAE;gBACRC,KAAK,EAAE,CACL;kBAAE1D,KAAK,EAAE,CAAC;kBAAEwD,KAAK,EAAE;gBAAQ,CAAC,EAC5B;kBAAExD,KAAK,EAAE,CAAC;kBAAEwD,KAAK,EAAE;gBAAW,CAAC,EAC/B;kBAAExD,KAAK,EAAE,CAAC;kBAAEwD,KAAK,EAAE;gBAAY,CAAC,CAChC;gBACFjB,KAAK,EAAC;cAAS;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAEPzF,OAAA,CAACxC,IAAI;YAACgJ,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA/C,QAAA,gBACvB3D,OAAA,CAAC3C,UAAU;cAACsI,OAAO,EAAC,IAAI;cAACE,YAAY;cAACD,UAAU,EAAC,KAAK;cAAAjC,QAAA,EAAC;YAEvD;cAAA2B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAEbzF,OAAA,CAACtC,IAAI;cAAAiG,QAAA,EACFvB,iBAAiB,CAAC8E,GAAG,CAAC,CAACC,GAAG,EAAEC,KAAK,kBAChCpH,OAAA,CAACrC,QAAQ;gBAEP8F,EAAE,EAAE;kBACFI,UAAU,EAAGC,KAAK,IAAKA,KAAK,CAACC,OAAO,CAACC,IAAI,KAAK,MAAM,GAChD,2BAA2B,GAC3B,qBAAqB;kBACzBG,YAAY,EAAE,CAAC;kBACfP,EAAE,EAAE;gBACN,CAAE;gBAAAD,QAAA,gBAEF3D,OAAA,CAACpC,YAAY;kBAAA+F,QAAA,eACX3D,OAAA,CAACJ,MAAM;oBAACkG,KAAK,EAAC;kBAAW;oBAAAR,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChB,CAAC,eACfzF,OAAA,CAACnC,YAAY;kBACXwJ,OAAO,EAAEF,GAAG,CAAC7E,OAAQ;kBACrBgF,SAAS,eACPtH,OAAA,CAACvC,IAAI;oBACHsJ,KAAK,EAAE,GAAGI,GAAG,CAAC5E,UAAU,cAAe;oBACvCgF,IAAI,EAAC,OAAO;oBACZzB,KAAK,EAAEqB,GAAG,CAAC5E,UAAU,GAAG,EAAE,GAAG,SAAS,GAAG,SAAU;oBACnDoD,OAAO,EAAC;kBAAU;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnB;gBACF;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC;cAAA,GAtBG2B,KAAK;gBAAA9B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAuBF,CACX;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAEPzF,OAAA,CAACxC,IAAI;MAAC8I,SAAS;MAACC,OAAO,EAAE,CAAE;MAAA5C,QAAA,gBAEzB3D,OAAA,CAACxC,IAAI;QAACgJ,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAA/C,QAAA,eACvB3D,OAAA,CAAC7C,IAAI;UACHsG,EAAE,EAAE;YACFI,UAAU,EAAGC,KAAK,IAAKA,KAAK,CAACC,OAAO,CAACC,IAAI,KAAK,MAAM,GAChD,iFAAiF,GACjF,uFAAuF;YAC3FC,cAAc,EAAE,YAAY;YAC5BC,MAAM,EAAGJ,KAAK,IAAK,aAAaA,KAAK,CAACC,OAAO,CAACyD,OAAO;UACvD,CAAE;UAAA7D,QAAA,eAEF3D,OAAA,CAAC5C,WAAW;YAAAuG,QAAA,gBACV3D,OAAA,CAAC9C,GAAG;cAAC+H,OAAO,EAAC,MAAM;cAACC,UAAU,EAAC,QAAQ;cAACC,GAAG,EAAE,CAAE;cAACvB,EAAE,EAAE,CAAE;cAAAD,QAAA,gBACpD3D,OAAA,CAAClC,MAAM;gBACL2F,EAAE,EAAE;kBACFI,UAAU,EAAE,mDAAmD;kBAC/DuB,KAAK,EAAE,EAAE;kBACTP,MAAM,EAAE;gBACV,CAAE;gBAAAlB,QAAA,eAEF3D,OAAA,CAACtB,YAAY;kBAAA4G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACTzF,OAAA,CAAC3C,UAAU;gBAACsI,OAAO,EAAC,IAAI;gBAACC,UAAU,EAAC,MAAM;gBAAAjC,QAAA,GAAC,cAC7B,EAACrC,SAAS,CAACmG,MAAM,EAAC,GAChC;cAAA;gBAAAnC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAENzF,OAAA,CAACtC,IAAI;cAAAiG,QAAA,EACFrC,SAAS,CAAC4F,GAAG,CAAC,CAACV,IAAI,EAAEY,KAAK,kBACzBpH,OAAA,CAACrC,QAAQ;gBAEP8F,EAAE,EAAE;kBACFI,UAAU,EAAGC,KAAK,IAAKA,KAAK,CAACC,OAAO,CAACC,IAAI,KAAK,MAAM,GAChD,2BAA2B,GAC3B,qBAAqB;kBACzBG,YAAY,EAAE,CAAC;kBACfP,EAAE,EAAE;gBACN,CAAE;gBAAAD,QAAA,gBAEF3D,OAAA,CAACpC,YAAY;kBAAA+F,QAAA,eACX3D,OAAA,CAAClB,YAAY;oBAACgH,KAAK,EAAC;kBAAS;oBAAAR,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpB,CAAC,eACfzF,OAAA,CAACnC,YAAY;kBACXwJ,OAAO,eACLrH,OAAA,CAAC9C,GAAG;oBAAC+H,OAAO,EAAC,MAAM;oBAACC,UAAU,EAAC,QAAQ;oBAACC,GAAG,EAAE,CAAE;oBAAAxB,QAAA,gBAC7C3D,OAAA,CAAC3C,UAAU;sBAACsI,OAAO,EAAC,OAAO;sBAACC,UAAU,EAAC,KAAK;sBAAAjC,QAAA,EACzC6C,IAAI,CAAC/E;oBAAM;sBAAA6D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF,CAAC,eACbzF,OAAA,CAACvC,IAAI;sBACHsJ,KAAK,EAAEP,IAAI,CAAChF,IAAK;sBACjB+F,IAAI,EAAC,OAAO;sBACZ5B,OAAO,EAAC;oBAAU;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnB,CAAC,eACFzF,OAAA,CAACvC,IAAI;sBACHsJ,KAAK,EAAEP,IAAI,CAAC9E,QAAS;sBACrB6F,IAAI,EAAC,OAAO;sBACZzB,KAAK,EAAEtC,gBAAgB,CAACgD,IAAI,CAAC9E,QAAQ;oBAAE;sBAAA4D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CACN;kBACD6B,SAAS,EAAE,WAAWd,IAAI,CAAC7E,MAAM;gBAAG;kBAAA2D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrC,CAAC;cAAA,GA/BG2B,KAAK;gBAAA9B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAgCF,CACX;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGPzF,OAAA,CAACxC,IAAI;QAACgJ,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAA/C,QAAA,eACvB3D,OAAA,CAAC7C,IAAI;UACHsG,EAAE,EAAE;YACFI,UAAU,EAAGC,KAAK,IAAKA,KAAK,CAACC,OAAO,CAACC,IAAI,KAAK,MAAM,GAChD,iFAAiF,GACjF,uFAAuF;YAC3FC,cAAc,EAAE,YAAY;YAC5BC,MAAM,EAAGJ,KAAK,IAAK,aAAaA,KAAK,CAACC,OAAO,CAACyD,OAAO;UACvD,CAAE;UAAA7D,QAAA,eAEF3D,OAAA,CAAC5C,WAAW;YAAAuG,QAAA,gBACV3D,OAAA,CAAC9C,GAAG;cAAC+H,OAAO,EAAC,MAAM;cAACC,UAAU,EAAC,QAAQ;cAACC,GAAG,EAAE,CAAE;cAACvB,EAAE,EAAE,CAAE;cAAAD,QAAA,gBACpD3D,OAAA,CAAClC,MAAM;gBACL2F,EAAE,EAAE;kBACFI,UAAU,EAAE,mDAAmD;kBAC/DuB,KAAK,EAAE,EAAE;kBACTP,MAAM,EAAE;gBACV,CAAE;gBAAAlB,QAAA,eAEF3D,OAAA,CAACpB,SAAS;kBAAA0G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP,CAAC,eACTzF,OAAA,CAAC3C,UAAU;gBAACsI,OAAO,EAAC,IAAI;gBAACC,UAAU,EAAC,MAAM;gBAAAjC,QAAA,EAAC;cAE3C;gBAAA2B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAENzF,OAAA,CAACtC,IAAI;cAAAiG,QAAA,EACF/B,WAAW,CAACsF,GAAG,CAAC,CAACQ,IAAI,EAAEN,KAAK,kBAC3BpH,OAAA,CAACrC,QAAQ;gBAEP8F,EAAE,EAAE;kBACFI,UAAU,EAAGC,KAAK,IAAKA,KAAK,CAACC,OAAO,CAACC,IAAI,KAAK,MAAM,GAChD,2BAA2B,GAC3B,qBAAqB;kBACzBG,YAAY,EAAE,CAAC;kBACfP,EAAE,EAAE;gBACN,CAAE;gBAAAD,QAAA,eAEF3D,OAAA,CAACnC,YAAY;kBACXwJ,OAAO,eACLrH,OAAA,CAAC9C,GAAG;oBAAC+H,OAAO,EAAC,MAAM;oBAACC,UAAU,EAAC,QAAQ;oBAACkB,cAAc,EAAC,eAAe;oBAAAzC,QAAA,gBACpE3D,OAAA,CAAC3C,UAAU;sBAACsI,OAAO,EAAC,OAAO;sBAACC,UAAU,EAAC,KAAK;sBAAAjC,QAAA,EACzC+D,IAAI,CAAClG;oBAAI;sBAAA8D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACA,CAAC,eACbzF,OAAA,CAAC3C,UAAU;sBAACsI,OAAO,EAAC,SAAS;sBAACG,KAAK,EAAC,gBAAgB;sBAAAnC,QAAA,EACjD+D,IAAI,CAAC5F,SAAS,CAAC6F,kBAAkB,CAAC;oBAAC;sBAAArC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1B,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CACN;kBACD6B,SAAS,eACPtH,OAAA,CAAC9C,GAAG;oBAAC+H,OAAO,EAAC,MAAM;oBAACC,UAAU,EAAC,QAAQ;oBAACC,GAAG,EAAE,CAAE;oBAACyC,EAAE,EAAE,CAAE;oBAAAjE,QAAA,gBACpD3D,OAAA,CAAC3C,UAAU;sBAACsI,OAAO,EAAC,SAAS;sBAAAhC,QAAA,GAC1B+D,IAAI,CAACzF,OAAO,EAAC,UAChB;oBAAA;sBAAAqD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACbzF,OAAA,CAAC3C,UAAU;sBAACsI,OAAO,EAAC,SAAS;sBAACG,KAAK,EAAE4B,IAAI,CAACxF,OAAO,GAAG,CAAC,GAAG,YAAY,GAAG,cAAe;sBAAAyB,QAAA,GACnF+D,IAAI,CAACxF,OAAO,EAAC,UAChB;oBAAA;sBAAAoD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACbzF,OAAA,CAAC3C,UAAU;sBAACsI,OAAO,EAAC,SAAS;sBAAAhC,QAAA,EAC1B+D,IAAI,CAACvF;oBAAQ;sBAAAmD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV;gBACN;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF;cAAC,GAjCG2B,KAAK;gBAAA9B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAkCF,CACX;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC,kCAAC;AAACoC,GAAA,GA7hBG5H,cAAc;AA+hBpBA,cAAc,CAAC6H,WAAW,GAAG,gBAAgB;AAE7C,eAAe7H,cAAc;AAAC,IAAAG,EAAA,EAAAyH,GAAA;AAAAE,YAAA,CAAA3H,EAAA;AAAA2H,YAAA,CAAAF,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}