import React, { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import {
  Card,
  CardContent,
  Typography,
  Box,
  Button,
  LinearProgress,
  Alert,
  Chip,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  IconButton,
  Divider,
} from '@mui/material';
import {
  CloudUpload as CloudUploadIcon,
  InsertDriveFile as FileIcon,
  Delete as DeleteIcon,
  Security as SecurityIcon,
  Warning as WarningIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
} from '@mui/icons-material';
import { useScan } from '../contexts/ScanContext';
import { scanFile } from '../services/security';

const FileUploader = React.memo(() => {
  const [files, setFiles] = useState([]);
  const [isScanning, setIsScanning] = useState(false);
  const [scanResults, setScanResults] = useState([]);
  const [uploadProgress, setUploadProgress] = useState(0);
  
  const { startScan, completeScan, addToHistory, addNotification } = useScan();

  // File validation
  const validateFile = useCallback((file) => {
    const maxSize = 50 * 1024 * 1024; // 50MB
    const allowedTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'text/plain',
      'image/jpeg',
      'image/png',
      'image/gif',
      'application/zip',
      'application/x-zip-compressed',
    ];

    if (file.size > maxSize) {
      return { valid: false, error: 'File size exceeds 50MB limit' };
    }

    if (!allowedTypes.includes(file.type)) {
      return { valid: false, error: 'File type not supported' };
    }

    return { valid: true };
  }, []);

  // Handle file drop
  const onDrop = useCallback((acceptedFiles, rejectedFiles) => {
    // Handle rejected files
    rejectedFiles.forEach(({ file, errors }) => {
      addNotification({
        type: 'error',
        title: 'File Rejected',
        message: `${file.name}: ${errors.map(e => e.message).join(', ')}`,
      });
    });

    // Validate and add accepted files
    const validFiles = [];
    acceptedFiles.forEach(file => {
      const validation = validateFile(file);
      if (validation.valid) {
        validFiles.push({
          id: Date.now() + Math.random(),
          file,
          status: 'pending',
          result: null,
        });
      } else {
        addNotification({
          type: 'error',
          title: 'Invalid File',
          message: `${file.name}: ${validation.error}`,
        });
      }
    });

    setFiles(prev => [...prev, ...validFiles]);
  }, [validateFile, addNotification]);

  // Configure dropzone
  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    maxSize: 50 * 1024 * 1024, // 50MB
    multiple: true,
  });

  // Remove file
  const removeFile = useCallback((fileId) => {
    setFiles(prev => prev.filter(f => f.id !== fileId));
    setScanResults(prev => prev.filter(r => r.fileId !== fileId));
  }, []);

  // Scan all files
  const handleScanFiles = useCallback(async () => {
    if (files.length === 0) return;

    setIsScanning(true);
    setUploadProgress(0);
    setScanResults([]);

    try {
      startScan('files', `${files.length} files`);

      const results = [];
      const totalFiles = files.length;

      for (let i = 0; i < files.length; i++) {
        const fileData = files[i];
        
        try {
          // Update progress
          setUploadProgress(((i + 1) / totalFiles) * 100);

          // Scan individual file
          const result = await scanFile(fileData.file);
          
          const scanResult = {
            fileId: fileData.id,
            fileName: fileData.file.name,
            fileSize: fileData.file.size,
            result,
            status: 'completed',
          };

          results.push(scanResult);
          setScanResults(prev => [...prev, scanResult]);

          // Update file status
          setFiles(prev => prev.map(f => 
            f.id === fileData.id 
              ? { ...f, status: 'scanned', result }
              : f
          ));

        } catch (error) {
          const errorResult = {
            fileId: fileData.id,
            fileName: fileData.file.name,
            fileSize: fileData.file.size,
            error: error.message,
            status: 'error',
          };

          results.push(errorResult);
          setScanResults(prev => [...prev, errorResult]);

          // Update file status
          setFiles(prev => prev.map(f => 
            f.id === fileData.id 
              ? { ...f, status: 'error', error: error.message }
              : f
          ));
        }
      }

      // Complete scan
      const overallResult = {
        filesScanned: totalFiles,
        threatsFound: results.filter(r => r.result && !r.result.isSafe).length,
        cleanFiles: results.filter(r => r.result && r.result.isSafe).length,
        errors: results.filter(r => r.error).length,
      };

      completeScan(overallResult);

      // Add to history
      const historyEntry = {
        id: Date.now(),
        type: 'files',
        target: `${totalFiles} files`,
        result: overallResult,
        timestamp: new Date().toISOString(),
        files: results,
      };
      addToHistory(historyEntry);

      // Add notification
      addNotification({
        type: overallResult.threatsFound > 0 ? 'warning' : 'success',
        title: 'File Scan Complete',
        message: `Scanned ${totalFiles} files. ${overallResult.threatsFound} threats found.`,
      });

    } catch (error) {
      addNotification({
        type: 'error',
        title: 'Scan Failed',
        message: error.message || 'Failed to scan files',
      });
    } finally {
      setIsScanning(false);
      setUploadProgress(0);
    }
  }, [files, startScan, completeScan, addToHistory, addNotification]);

  // Format file size
  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Get status icon
  const getStatusIcon = (status, result) => {
    switch (status) {
      case 'pending':
        return <FileIcon />;
      case 'scanned':
        return result?.isSafe ? <CheckCircleIcon color="success" /> : <WarningIcon color="warning" />;
      case 'error':
        return <ErrorIcon color="error" />;
      default:
        return <FileIcon />;
    }
  };

  return (
    <Box
      data-testid="file-scanner"
      sx={{
        background: (theme) => theme.palette.mode === 'dark'
          ? 'linear-gradient(135deg, rgba(30, 30, 30, 0.95) 0%, rgba(50, 50, 50, 0.95) 100%)'
          : 'linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%)',
        backdropFilter: 'blur(20px)',
        borderRadius: 4,
        border: (theme) => `1px solid ${theme.palette.divider}`,
        p: 4,
        position: 'relative',
        overflow: 'hidden',
        '&::before': {
          content: '""',
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          height: '4px',
          background: 'linear-gradient(90deg, #667eea 0%, #764ba2 100%)',
        },
      }}
    >
      {/* Header Section */}
      <Box
        display="flex"
        alignItems="center"
        justifyContent="center"
        mb={4}
        sx={{
          textAlign: 'center',
        }}
      >
        <Box
          sx={{
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            borderRadius: '50%',
            width: 60,
            height: 60,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            mr: 3,
            boxShadow: '0 8px 25px rgba(102, 126, 234, 0.3)',
          }}
        >
          <CloudUploadIcon sx={{ color: 'white', fontSize: 28 }} />
        </Box>
        <Box>
          <Typography variant="h4" component="h2" fontWeight="bold" gutterBottom>
            File Security Scanner
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Advanced malware detection for files and documents
          </Typography>
        </Box>
      </Box>

      {/* Enhanced Dropzone */}
      <Box
        {...getRootProps()}
        sx={{
          border: '3px dashed',
          borderColor: isDragActive ? 'primary.main' : 'grey.300',
          borderRadius: 4,
          p: 6,
          textAlign: 'center',
          cursor: 'pointer',
          background: isDragActive
            ? 'linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%)'
            : (theme) => theme.palette.mode === 'dark'
              ? 'rgba(255, 255, 255, 0.02)'
              : 'rgba(0, 0, 0, 0.02)',
          transition: 'all 0.3s ease',
          mb: 4,
          position: 'relative',
          overflow: 'hidden',
          '&:hover': {
            borderColor: 'primary.main',
            background: 'linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%)',
            transform: 'scale(1.01)',
          },
          '&::before': {
            content: '""',
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: isDragActive
              ? 'linear-gradient(45deg, transparent 30%, rgba(102, 126, 234, 0.1) 50%, transparent 70%)'
              : 'none',
            animation: isDragActive ? 'shimmer 1.5s infinite' : 'none',
          },
          '@keyframes shimmer': {
            '0%': { transform: 'translateX(-100%)' },
            '100%': { transform: 'translateX(100%)' },
          },
        }}
      >
        <input {...getInputProps()} />

        <Box
          sx={{
            background: 'linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%)',
            borderRadius: '50%',
            width: 80,
            height: 80,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            mx: 'auto',
            mb: 3,
            transition: 'transform 0.3s ease',
            transform: isDragActive ? 'scale(1.1)' : 'scale(1)',
          }}
        >
          <CloudUploadIcon sx={{ fontSize: 40, color: 'primary.main' }} />
        </Box>

        <Typography variant="h5" gutterBottom fontWeight="600">
          {isDragActive ? 'Drop files here to scan' : 'Drag & drop files here'}
        </Typography>
        <Typography variant="h6" color="text.secondary" gutterBottom sx={{ mb: 2 }}>
          or click to select files from your device
        </Typography>
        <Typography variant="body1" color="text.secondary" sx={{ mb: 2 }}>
          Supported formats: PDF, DOC, XLS, TXT, Images, ZIP
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Maximum file size: 50MB per file
        </Typography>
      </Box>

        {/* File List */}
        {files.length > 0 && (
          <Box mb={3}>
            <Typography variant="subtitle1" gutterBottom>
              Selected Files ({files.length})
            </Typography>
            <List>
              {files.map((fileData, index) => (
                <React.Fragment key={fileData.id}>
                  <ListItem
                    secondaryAction={
                      <IconButton
                        edge="end"
                        onClick={() => removeFile(fileData.id)}
                        disabled={isScanning}
                      >
                        <DeleteIcon />
                      </IconButton>
                    }
                  >
                    <ListItemIcon>
                      {getStatusIcon(fileData.status, fileData.result)}
                    </ListItemIcon>
                    <ListItemText
                      primary={fileData.file.name}
                      secondary={
                        <Box>
                          <Typography variant="caption" display="block">
                            {formatFileSize(fileData.file.size)}
                          </Typography>
                          {fileData.status === 'error' && (
                            <Typography variant="caption" color="error">
                              Error: {fileData.error}
                            </Typography>
                          )}
                          {fileData.result && (
                            <Box mt={1}>
                              <Chip
                                size="small"
                                label={fileData.result.isSafe ? 'Clean' : 'Threat Detected'}
                                color={fileData.result.isSafe ? 'success' : 'error'}
                                variant="outlined"
                              />
                            </Box>
                          )}
                        </Box>
                      }
                    />
                  </ListItem>
                  {index < files.length - 1 && <Divider />}
                </React.Fragment>
              ))}
            </List>
          </Box>
        )}

        {/* Scan Progress */}
        {isScanning && (
          <Box mb={3}>
            <Typography variant="body2" gutterBottom>
              Scanning files... {Math.round(uploadProgress)}%
            </Typography>
            <LinearProgress variant="determinate" value={uploadProgress} />
          </Box>
        )}

        {/* Scan Button */}
        <Button
          variant="contained"
          color="primary"
          onClick={handleScanFiles}
          disabled={files.length === 0 || isScanning}
          startIcon={<SecurityIcon />}
          fullWidth
          size="large"
        >
          {isScanning ? 'Scanning Files...' : `Scan ${files.length} File${files.length !== 1 ? 's' : ''}`}
        </Button>

        {/* Scan Results Summary */}
        {scanResults.length > 0 && !isScanning && (
          <Box mt={3}>
            <Alert
              severity={scanResults.some(r => r.result && !r.result.isSafe) ? 'warning' : 'success'}
              icon={<SecurityIcon />}
            >
              <Typography variant="subtitle1" fontWeight="bold">
                Scan Complete
              </Typography>
              <Typography variant="body2">
                {scanResults.filter(r => r.result?.isSafe).length} files clean, {' '}
                {scanResults.filter(r => r.result && !r.result.isSafe).length} threats detected, {' '}
                {scanResults.filter(r => r.error).length} errors
              </Typography>
            </Alert>
          </Box>
        )}
    </Box>
  );
});

FileUploader.displayName = 'FileUploader';

export default FileUploader;
