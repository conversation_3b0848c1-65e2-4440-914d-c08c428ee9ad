{"ast": null, "code": "var _jsxFileName = \"E:\\\\PythonProjects\\\\React.js\\\\Smart AI Assisstant\\\\src\\\\components\\\\SecuritySettings.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Box, Card, CardContent, Typography, Button, TextField, IconButton, List, ListItem, ListItemIcon, ListItemText, ListItemSecondaryAction, Switch, Chip, Alert, Dialog, DialogTitle, DialogContent, DialogActions, CircularProgress, Divider } from '@mui/material';\nimport { Security as SecurityIcon, Lock as LockIcon, Key as KeyIcon, Shield as ShieldIcon, Visibility as VisibilityIcon, VisibilityOff as VisibilityOffIcon, Warning as WarningIcon, CheckCircle as CheckCircleIcon } from '@mui/icons-material';\nimport { useAuth } from '../contexts/AuthContext';\nimport authService from '../services/auth';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SecuritySettings = () => {\n  _s();\n  const {\n    user,\n    logout\n  } = useAuth();\n  const [changePasswordOpen, setChangePasswordOpen] = useState(false);\n  const [passwordForm, setPasswordForm] = useState({\n    currentPassword: '',\n    newPassword: '',\n    confirmPassword: ''\n  });\n  const [showPasswords, setShowPasswords] = useState({\n    current: false,\n    new: false,\n    confirm: false\n  });\n  const [isLoading, setIsLoading] = useState(false);\n  const [alert, setAlert] = useState({\n    show: false,\n    message: '',\n    severity: 'success'\n  });\n  const [formErrors, setFormErrors] = useState({});\n\n  // Security settings state\n  const [securitySettings, setSecuritySettings] = useState({\n    twoFactorAuth: false,\n    loginNotifications: true,\n    sessionTimeout: true,\n    deviceTracking: true\n  });\n  const showAlert = (message, severity = 'success') => {\n    setAlert({\n      show: true,\n      message,\n      severity\n    });\n    setTimeout(() => setAlert({\n      show: false,\n      message: '',\n      severity: 'success'\n    }), 5000);\n  };\n  const handlePasswordChange = async () => {\n    // Validate form\n    const errors = {};\n    if (!passwordForm.currentPassword) {\n      errors.currentPassword = 'Current password is required';\n    }\n    if (!passwordForm.newPassword) {\n      errors.newPassword = 'New password is required';\n    } else {\n      const validation = authService.validatePassword(passwordForm.newPassword);\n      if (!validation.isValid) {\n        errors.newPassword = validation.errors[0];\n      }\n    }\n    if (!passwordForm.confirmPassword) {\n      errors.confirmPassword = 'Please confirm your new password';\n    } else if (passwordForm.newPassword !== passwordForm.confirmPassword) {\n      errors.confirmPassword = 'Passwords do not match';\n    }\n    if (passwordForm.currentPassword === passwordForm.newPassword) {\n      errors.newPassword = 'New password must be different from current password';\n    }\n    setFormErrors(errors);\n    if (Object.keys(errors).length > 0) {\n      return;\n    }\n    setIsLoading(true);\n    try {\n      // Simulate API call\n      await new Promise(resolve => setTimeout(resolve, 2000));\n      showAlert('Password changed successfully!', 'success');\n      setChangePasswordOpen(false);\n      setPasswordForm({\n        currentPassword: '',\n        newPassword: '',\n        confirmPassword: ''\n      });\n      setFormErrors({});\n    } catch (error) {\n      showAlert('Failed to change password. Please try again.', 'error');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  const handleSecuritySettingChange = setting => {\n    setSecuritySettings(prev => ({\n      ...prev,\n      [setting]: !prev[setting]\n    }));\n    showAlert(`${setting.replace(/([A-Z])/g, ' $1').toLowerCase()} ${securitySettings[setting] ? 'disabled' : 'enabled'}`, 'info');\n  };\n  const getPasswordStrength = password => {\n    if (!password) return {\n      strength: 0,\n      label: 'No password',\n      color: 'default'\n    };\n    let strength = 0;\n    if (password.length >= 8) strength++;\n    if (/[A-Z]/.test(password)) strength++;\n    if (/[a-z]/.test(password)) strength++;\n    if (/\\d/.test(password)) strength++;\n    if (/[@$!%*?&]/.test(password)) strength++;\n    const levels = [{\n      strength: 0,\n      label: 'Very Weak',\n      color: 'error'\n    }, {\n      strength: 1,\n      label: 'Weak',\n      color: 'error'\n    }, {\n      strength: 2,\n      label: 'Fair',\n      color: 'warning'\n    }, {\n      strength: 3,\n      label: 'Good',\n      color: 'info'\n    }, {\n      strength: 4,\n      label: 'Strong',\n      color: 'success'\n    }, {\n      strength: 5,\n      label: 'Very Strong',\n      color: 'success'\n    }];\n    return levels[strength];\n  };\n  const passwordStrength = getPasswordStrength(passwordForm.newPassword);\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [alert.show && /*#__PURE__*/_jsxDEV(Alert, {\n      severity: alert.severity,\n      sx: {\n        mb: 3\n      },\n      children: alert.message\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 159,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h4\",\n      gutterBottom: true,\n      fontWeight: \"bold\",\n      children: \"Security Settings\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 164,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"body1\",\n      color: \"text.secondary\",\n      sx: {\n        mb: 4\n      },\n      children: \"Manage your account security and privacy settings\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 167,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        mb: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            mb: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(LockIcon, {\n            sx: {\n              mr: 2,\n              color: 'primary.main'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 175,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            fontWeight: \"600\",\n            children: \"Password Security\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 176,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 174,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          color: \"text.secondary\",\n          sx: {\n            mb: 3\n          },\n          children: \"Keep your account secure with a strong password\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 181,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outlined\",\n          startIcon: /*#__PURE__*/_jsxDEV(KeyIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 24\n          }, this),\n          onClick: () => setChangePasswordOpen(true),\n          sx: {\n            borderRadius: 3\n          },\n          children: \"Change Password\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 185,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 173,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 172,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        mb: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            mb: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(ShieldIcon, {\n            sx: {\n              mr: 2,\n              color: 'primary.main'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 200,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            fontWeight: \"600\",\n            children: \"Two-Factor Authentication\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 201,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 199,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          color: \"text.secondary\",\n          sx: {\n            mb: 3\n          },\n          children: \"Add an extra layer of security to your account\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 206,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'space-between'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              fontWeight: \"500\",\n              children: \"SMS Authentication\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 212,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: \"Receive codes via text message\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 215,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 211,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Switch, {\n            checked: securitySettings.twoFactorAuth,\n            onChange: () => handleSecuritySettingChange('twoFactorAuth')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 219,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 210,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 198,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 197,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        mb: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            mb: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(SecurityIcon, {\n            sx: {\n              mr: 2,\n              color: 'primary.main'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 231,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            fontWeight: \"600\",\n            children: \"Security Preferences\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 232,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 230,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(List, {\n          children: [/*#__PURE__*/_jsxDEV(ListItem, {\n            children: [/*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: \"Login Notifications\",\n              secondary: \"Get notified when someone logs into your account\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 239,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ListItemSecondaryAction, {\n              children: /*#__PURE__*/_jsxDEV(Switch, {\n                checked: securitySettings.loginNotifications,\n                onChange: () => handleSecuritySettingChange('loginNotifications')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 244,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 243,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 238,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 251,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n            children: [/*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: \"Session Timeout\",\n              secondary: \"Automatically log out after period of inactivity\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 254,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ListItemSecondaryAction, {\n              children: /*#__PURE__*/_jsxDEV(Switch, {\n                checked: securitySettings.sessionTimeout,\n                onChange: () => handleSecuritySettingChange('sessionTimeout')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 259,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 258,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 253,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 266,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n            children: [/*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: \"Device Tracking\",\n              secondary: \"Keep track of devices that access your account\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 269,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ListItemSecondaryAction, {\n              children: /*#__PURE__*/_jsxDEV(Switch, {\n                checked: securitySettings.deviceTracking,\n                onChange: () => handleSecuritySettingChange('deviceTracking')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 274,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 273,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 268,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 237,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 229,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 228,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          fontWeight: \"600\",\n          sx: {\n            mb: 2\n          },\n          children: \"Account Actions\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 287,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            gap: 2,\n            flexWrap: 'wrap'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outlined\",\n            color: \"error\",\n            onClick: logout,\n            sx: {\n              borderRadius: 3\n            },\n            children: \"Sign Out\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 292,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outlined\",\n            color: \"warning\",\n            sx: {\n              borderRadius: 3\n            },\n            children: \"Deactivate Account\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 301,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 291,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 286,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 285,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: changePasswordOpen,\n      onClose: () => setChangePasswordOpen(false),\n      maxWidth: \"sm\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          fontWeight: \"bold\",\n          children: \"Change Password\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 320,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 319,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: [/*#__PURE__*/_jsxDEV(TextField, {\n          fullWidth: true,\n          label: \"Current Password\",\n          type: showPasswords.current ? 'text' : 'password',\n          value: passwordForm.currentPassword,\n          onChange: e => {\n            setPasswordForm({\n              ...passwordForm,\n              currentPassword: e.target.value\n            });\n            if (formErrors.currentPassword) {\n              setFormErrors({\n                ...formErrors,\n                currentPassword: ''\n              });\n            }\n          },\n          margin: \"normal\",\n          error: !!formErrors.currentPassword,\n          helperText: formErrors.currentPassword,\n          InputProps: {\n            endAdornment: /*#__PURE__*/_jsxDEV(IconButton, {\n              onClick: () => setShowPasswords({\n                ...showPasswords,\n                current: !showPasswords.current\n              }),\n              children: showPasswords.current ? /*#__PURE__*/_jsxDEV(VisibilityOffIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 345,\n                columnNumber: 44\n              }, this) : /*#__PURE__*/_jsxDEV(VisibilityIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 345,\n                columnNumber: 68\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 342,\n              columnNumber: 17\n            }, this)\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 326,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TextField, {\n          fullWidth: true,\n          label: \"New Password\",\n          type: showPasswords.new ? 'text' : 'password',\n          value: passwordForm.newPassword,\n          onChange: e => {\n            setPasswordForm({\n              ...passwordForm,\n              newPassword: e.target.value\n            });\n            if (formErrors.newPassword) {\n              setFormErrors({\n                ...formErrors,\n                newPassword: ''\n              });\n            }\n          },\n          margin: \"normal\",\n          error: !!formErrors.newPassword,\n          helperText: formErrors.newPassword,\n          InputProps: {\n            endAdornment: /*#__PURE__*/_jsxDEV(IconButton, {\n              onClick: () => setShowPasswords({\n                ...showPasswords,\n                new: !showPasswords.new\n              }),\n              children: showPasswords.new ? /*#__PURE__*/_jsxDEV(VisibilityOffIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 370,\n                columnNumber: 40\n              }, this) : /*#__PURE__*/_jsxDEV(VisibilityIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 370,\n                columnNumber: 64\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 367,\n              columnNumber: 17\n            }, this)\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 351,\n          columnNumber: 11\n        }, this), passwordForm.newPassword && /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mt: 1,\n            mb: 2\n          },\n          children: /*#__PURE__*/_jsxDEV(Chip, {\n            label: `Password Strength: ${passwordStrength.label}`,\n            color: passwordStrength.color,\n            size: \"small\",\n            icon: passwordStrength.strength >= 4 ? /*#__PURE__*/_jsxDEV(CheckCircleIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 382,\n              columnNumber: 56\n            }, this) : /*#__PURE__*/_jsxDEV(WarningIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 382,\n              columnNumber: 78\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 378,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 377,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(TextField, {\n          fullWidth: true,\n          label: \"Confirm New Password\",\n          type: showPasswords.confirm ? 'text' : 'password',\n          value: passwordForm.confirmPassword,\n          onChange: e => {\n            setPasswordForm({\n              ...passwordForm,\n              confirmPassword: e.target.value\n            });\n            if (formErrors.confirmPassword) {\n              setFormErrors({\n                ...formErrors,\n                confirmPassword: ''\n              });\n            }\n          },\n          margin: \"normal\",\n          error: !!formErrors.confirmPassword,\n          helperText: formErrors.confirmPassword,\n          InputProps: {\n            endAdornment: /*#__PURE__*/_jsxDEV(IconButton, {\n              onClick: () => setShowPasswords({\n                ...showPasswords,\n                confirm: !showPasswords.confirm\n              }),\n              children: showPasswords.confirm ? /*#__PURE__*/_jsxDEV(VisibilityOffIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 406,\n                columnNumber: 44\n              }, this) : /*#__PURE__*/_jsxDEV(VisibilityIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 406,\n                columnNumber: 68\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 403,\n              columnNumber: 17\n            }, this)\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 387,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 325,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        sx: {\n          p: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => setChangePasswordOpen(false),\n          disabled: isLoading,\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 414,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          onClick: handlePasswordChange,\n          disabled: isLoading,\n          startIcon: isLoading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 424,\n            columnNumber: 36\n          }, this) : /*#__PURE__*/_jsxDEV(LockIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 424,\n            columnNumber: 69\n          }, this),\n          children: isLoading ? 'Changing...' : 'Change Password'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 420,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 413,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 313,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 156,\n    columnNumber: 5\n  }, this);\n};\n_s(SecuritySettings, \"pme7ipegOzO7A0g+fAtMu16I99A=\", false, function () {\n  return [useAuth];\n});\n_c = SecuritySettings;\nexport default SecuritySettings;\nvar _c;\n$RefreshReg$(_c, \"SecuritySettings\");", "map": {"version": 3, "names": ["React", "useState", "Box", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Typography", "<PERSON><PERSON>", "TextField", "IconButton", "List", "ListItem", "ListItemIcon", "ListItemText", "ListItemSecondaryAction", "Switch", "Chip", "<PERSON><PERSON>", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "CircularProgress", "Divider", "Security", "SecurityIcon", "Lock", "LockIcon", "Key", "KeyIcon", "Shield", "ShieldIcon", "Visibility", "VisibilityIcon", "VisibilityOff", "VisibilityOffIcon", "Warning", "WarningIcon", "CheckCircle", "CheckCircleIcon", "useAuth", "authService", "jsxDEV", "_jsxDEV", "SecuritySettings", "_s", "user", "logout", "changePasswordOpen", "setChangePasswordOpen", "passwordForm", "setPasswordForm", "currentPassword", "newPassword", "confirmPassword", "showPasswords", "setShowPasswords", "current", "new", "confirm", "isLoading", "setIsLoading", "alert", "<PERSON><PERSON><PERSON><PERSON>", "show", "message", "severity", "formErrors", "setFormErrors", "securitySettings", "setSecuritySettings", "twoFactorAuth", "loginNotifications", "sessionTimeout", "deviceTracking", "show<PERSON><PERSON><PERSON>", "setTimeout", "handlePasswordChange", "errors", "validation", "validatePassword", "<PERSON><PERSON><PERSON><PERSON>", "Object", "keys", "length", "Promise", "resolve", "error", "handleSecuritySettingChange", "setting", "prev", "replace", "toLowerCase", "getPasswordStrength", "password", "strength", "label", "color", "test", "levels", "passwordStrength", "children", "sx", "mb", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "gutterBottom", "fontWeight", "display", "alignItems", "mr", "startIcon", "onClick", "borderRadius", "justifyContent", "checked", "onChange", "primary", "secondary", "gap", "flexWrap", "open", "onClose", "max<PERSON><PERSON><PERSON>", "fullWidth", "type", "value", "e", "target", "margin", "helperText", "InputProps", "endAdornment", "mt", "size", "icon", "p", "disabled", "_c", "$RefreshReg$"], "sources": ["E:/PythonProjects/React.js/Smart AI Assisstant/src/components/SecuritySettings.jsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  <PERSON>,\n  Card,\n  Card<PERSON>ontent,\n  <PERSON><PERSON><PERSON>,\n  Button,\n  TextField,\n  IconButton,\n  List,\n  ListItem,\n  ListItemIcon,\n  ListItemText,\n  ListItemSecondaryAction,\n  Switch,\n  Chip,\n  Alert,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  CircularProgress,\n  Divider,\n} from '@mui/material';\nimport {\n  Security as SecurityIcon,\n  Lock as LockIcon,\n  Key as KeyIcon,\n  Shield as ShieldIcon,\n  Visibility as VisibilityIcon,\n  VisibilityOff as VisibilityOffIcon,\n  Warning as WarningIcon,\n  CheckCircle as CheckCircleIcon,\n} from '@mui/icons-material';\nimport { useAuth } from '../contexts/AuthContext';\nimport authService from '../services/auth';\n\nconst SecuritySettings = () => {\n  const { user, logout } = useAuth();\n  const [changePasswordOpen, setChangePasswordOpen] = useState(false);\n  const [passwordForm, setPasswordForm] = useState({\n    currentPassword: '',\n    newPassword: '',\n    confirmPassword: '',\n  });\n  const [showPasswords, setShowPasswords] = useState({\n    current: false,\n    new: false,\n    confirm: false,\n  });\n  const [isLoading, setIsLoading] = useState(false);\n  const [alert, setAlert] = useState({ show: false, message: '', severity: 'success' });\n  const [formErrors, setFormErrors] = useState({});\n\n  // Security settings state\n  const [securitySettings, setSecuritySettings] = useState({\n    twoFactorAuth: false,\n    loginNotifications: true,\n    sessionTimeout: true,\n    deviceTracking: true,\n  });\n\n  const showAlert = (message, severity = 'success') => {\n    setAlert({ show: true, message, severity });\n    setTimeout(() => setAlert({ show: false, message: '', severity: 'success' }), 5000);\n  };\n\n  const handlePasswordChange = async () => {\n    // Validate form\n    const errors = {};\n    \n    if (!passwordForm.currentPassword) {\n      errors.currentPassword = 'Current password is required';\n    }\n    \n    if (!passwordForm.newPassword) {\n      errors.newPassword = 'New password is required';\n    } else {\n      const validation = authService.validatePassword(passwordForm.newPassword);\n      if (!validation.isValid) {\n        errors.newPassword = validation.errors[0];\n      }\n    }\n    \n    if (!passwordForm.confirmPassword) {\n      errors.confirmPassword = 'Please confirm your new password';\n    } else if (passwordForm.newPassword !== passwordForm.confirmPassword) {\n      errors.confirmPassword = 'Passwords do not match';\n    }\n    \n    if (passwordForm.currentPassword === passwordForm.newPassword) {\n      errors.newPassword = 'New password must be different from current password';\n    }\n    \n    setFormErrors(errors);\n    \n    if (Object.keys(errors).length > 0) {\n      return;\n    }\n\n    setIsLoading(true);\n    \n    try {\n      // Simulate API call\n      await new Promise(resolve => setTimeout(resolve, 2000));\n      \n      showAlert('Password changed successfully!', 'success');\n      setChangePasswordOpen(false);\n      setPasswordForm({\n        currentPassword: '',\n        newPassword: '',\n        confirmPassword: '',\n      });\n      setFormErrors({});\n    } catch (error) {\n      showAlert('Failed to change password. Please try again.', 'error');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleSecuritySettingChange = (setting) => {\n    setSecuritySettings(prev => ({\n      ...prev,\n      [setting]: !prev[setting],\n    }));\n    \n    showAlert(`${setting.replace(/([A-Z])/g, ' $1').toLowerCase()} ${securitySettings[setting] ? 'disabled' : 'enabled'}`, 'info');\n  };\n\n  const getPasswordStrength = (password) => {\n    if (!password) return { strength: 0, label: 'No password', color: 'default' };\n    \n    let strength = 0;\n    if (password.length >= 8) strength++;\n    if (/[A-Z]/.test(password)) strength++;\n    if (/[a-z]/.test(password)) strength++;\n    if (/\\d/.test(password)) strength++;\n    if (/[@$!%*?&]/.test(password)) strength++;\n    \n    const levels = [\n      { strength: 0, label: 'Very Weak', color: 'error' },\n      { strength: 1, label: 'Weak', color: 'error' },\n      { strength: 2, label: 'Fair', color: 'warning' },\n      { strength: 3, label: 'Good', color: 'info' },\n      { strength: 4, label: 'Strong', color: 'success' },\n      { strength: 5, label: 'Very Strong', color: 'success' },\n    ];\n    \n    return levels[strength];\n  };\n\n  const passwordStrength = getPasswordStrength(passwordForm.newPassword);\n\n  return (\n    <Box>\n      {/* Alert */}\n      {alert.show && (\n        <Alert severity={alert.severity} sx={{ mb: 3 }}>\n          {alert.message}\n        </Alert>\n      )}\n\n      <Typography variant=\"h4\" gutterBottom fontWeight=\"bold\">\n        Security Settings\n      </Typography>\n      <Typography variant=\"body1\" color=\"text.secondary\" sx={{ mb: 4 }}>\n        Manage your account security and privacy settings\n      </Typography>\n\n      {/* Password Security */}\n      <Card sx={{ mb: 3 }}>\n        <CardContent>\n          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>\n            <LockIcon sx={{ mr: 2, color: 'primary.main' }} />\n            <Typography variant=\"h6\" fontWeight=\"600\">\n              Password Security\n            </Typography>\n          </Box>\n          \n          <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 3 }}>\n            Keep your account secure with a strong password\n          </Typography>\n          \n          <Button\n            variant=\"outlined\"\n            startIcon={<KeyIcon />}\n            onClick={() => setChangePasswordOpen(true)}\n            sx={{ borderRadius: 3 }}\n          >\n            Change Password\n          </Button>\n        </CardContent>\n      </Card>\n\n      {/* Two-Factor Authentication */}\n      <Card sx={{ mb: 3 }}>\n        <CardContent>\n          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>\n            <ShieldIcon sx={{ mr: 2, color: 'primary.main' }} />\n            <Typography variant=\"h6\" fontWeight=\"600\">\n              Two-Factor Authentication\n            </Typography>\n          </Box>\n          \n          <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 3 }}>\n            Add an extra layer of security to your account\n          </Typography>\n          \n          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n            <Box>\n              <Typography variant=\"body1\" fontWeight=\"500\">\n                SMS Authentication\n              </Typography>\n              <Typography variant=\"body2\" color=\"text.secondary\">\n                Receive codes via text message\n              </Typography>\n            </Box>\n            <Switch\n              checked={securitySettings.twoFactorAuth}\n              onChange={() => handleSecuritySettingChange('twoFactorAuth')}\n            />\n          </Box>\n        </CardContent>\n      </Card>\n\n      {/* Security Preferences */}\n      <Card sx={{ mb: 3 }}>\n        <CardContent>\n          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>\n            <SecurityIcon sx={{ mr: 2, color: 'primary.main' }} />\n            <Typography variant=\"h6\" fontWeight=\"600\">\n              Security Preferences\n            </Typography>\n          </Box>\n          \n          <List>\n            <ListItem>\n              <ListItemText\n                primary=\"Login Notifications\"\n                secondary=\"Get notified when someone logs into your account\"\n              />\n              <ListItemSecondaryAction>\n                <Switch\n                  checked={securitySettings.loginNotifications}\n                  onChange={() => handleSecuritySettingChange('loginNotifications')}\n                />\n              </ListItemSecondaryAction>\n            </ListItem>\n            \n            <Divider />\n            \n            <ListItem>\n              <ListItemText\n                primary=\"Session Timeout\"\n                secondary=\"Automatically log out after period of inactivity\"\n              />\n              <ListItemSecondaryAction>\n                <Switch\n                  checked={securitySettings.sessionTimeout}\n                  onChange={() => handleSecuritySettingChange('sessionTimeout')}\n                />\n              </ListItemSecondaryAction>\n            </ListItem>\n            \n            <Divider />\n            \n            <ListItem>\n              <ListItemText\n                primary=\"Device Tracking\"\n                secondary=\"Keep track of devices that access your account\"\n              />\n              <ListItemSecondaryAction>\n                <Switch\n                  checked={securitySettings.deviceTracking}\n                  onChange={() => handleSecuritySettingChange('deviceTracking')}\n                />\n              </ListItemSecondaryAction>\n            </ListItem>\n          </List>\n        </CardContent>\n      </Card>\n\n      {/* Account Actions */}\n      <Card>\n        <CardContent>\n          <Typography variant=\"h6\" fontWeight=\"600\" sx={{ mb: 2 }}>\n            Account Actions\n          </Typography>\n          \n          <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>\n            <Button\n              variant=\"outlined\"\n              color=\"error\"\n              onClick={logout}\n              sx={{ borderRadius: 3 }}\n            >\n              Sign Out\n            </Button>\n            \n            <Button\n              variant=\"outlined\"\n              color=\"warning\"\n              sx={{ borderRadius: 3 }}\n            >\n              Deactivate Account\n            </Button>\n          </Box>\n        </CardContent>\n      </Card>\n\n      {/* Change Password Dialog */}\n      <Dialog\n        open={changePasswordOpen}\n        onClose={() => setChangePasswordOpen(false)}\n        maxWidth=\"sm\"\n        fullWidth\n      >\n        <DialogTitle>\n          <Typography variant=\"h6\" fontWeight=\"bold\">\n            Change Password\n          </Typography>\n        </DialogTitle>\n        \n        <DialogContent>\n          <TextField\n            fullWidth\n            label=\"Current Password\"\n            type={showPasswords.current ? 'text' : 'password'}\n            value={passwordForm.currentPassword}\n            onChange={(e) => {\n              setPasswordForm({ ...passwordForm, currentPassword: e.target.value });\n              if (formErrors.currentPassword) {\n                setFormErrors({ ...formErrors, currentPassword: '' });\n              }\n            }}\n            margin=\"normal\"\n            error={!!formErrors.currentPassword}\n            helperText={formErrors.currentPassword}\n            InputProps={{\n              endAdornment: (\n                <IconButton\n                  onClick={() => setShowPasswords({ ...showPasswords, current: !showPasswords.current })}\n                >\n                  {showPasswords.current ? <VisibilityOffIcon /> : <VisibilityIcon />}\n                </IconButton>\n              ),\n            }}\n          />\n          \n          <TextField\n            fullWidth\n            label=\"New Password\"\n            type={showPasswords.new ? 'text' : 'password'}\n            value={passwordForm.newPassword}\n            onChange={(e) => {\n              setPasswordForm({ ...passwordForm, newPassword: e.target.value });\n              if (formErrors.newPassword) {\n                setFormErrors({ ...formErrors, newPassword: '' });\n              }\n            }}\n            margin=\"normal\"\n            error={!!formErrors.newPassword}\n            helperText={formErrors.newPassword}\n            InputProps={{\n              endAdornment: (\n                <IconButton\n                  onClick={() => setShowPasswords({ ...showPasswords, new: !showPasswords.new })}\n                >\n                  {showPasswords.new ? <VisibilityOffIcon /> : <VisibilityIcon />}\n                </IconButton>\n              ),\n            }}\n          />\n          \n          {passwordForm.newPassword && (\n            <Box sx={{ mt: 1, mb: 2 }}>\n              <Chip\n                label={`Password Strength: ${passwordStrength.label}`}\n                color={passwordStrength.color}\n                size=\"small\"\n                icon={passwordStrength.strength >= 4 ? <CheckCircleIcon /> : <WarningIcon />}\n              />\n            </Box>\n          )}\n          \n          <TextField\n            fullWidth\n            label=\"Confirm New Password\"\n            type={showPasswords.confirm ? 'text' : 'password'}\n            value={passwordForm.confirmPassword}\n            onChange={(e) => {\n              setPasswordForm({ ...passwordForm, confirmPassword: e.target.value });\n              if (formErrors.confirmPassword) {\n                setFormErrors({ ...formErrors, confirmPassword: '' });\n              }\n            }}\n            margin=\"normal\"\n            error={!!formErrors.confirmPassword}\n            helperText={formErrors.confirmPassword}\n            InputProps={{\n              endAdornment: (\n                <IconButton\n                  onClick={() => setShowPasswords({ ...showPasswords, confirm: !showPasswords.confirm })}\n                >\n                  {showPasswords.confirm ? <VisibilityOffIcon /> : <VisibilityIcon />}\n                </IconButton>\n              ),\n            }}\n          />\n        </DialogContent>\n        \n        <DialogActions sx={{ p: 3 }}>\n          <Button\n            onClick={() => setChangePasswordOpen(false)}\n            disabled={isLoading}\n          >\n            Cancel\n          </Button>\n          <Button\n            variant=\"contained\"\n            onClick={handlePasswordChange}\n            disabled={isLoading}\n            startIcon={isLoading ? <CircularProgress size={20} /> : <LockIcon />}\n          >\n            {isLoading ? 'Changing...' : 'Change Password'}\n          </Button>\n        </DialogActions>\n      </Dialog>\n    </Box>\n  );\n};\n\nexport default SecuritySettings;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,GAAG,EACHC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,SAAS,EACTC,UAAU,EACVC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,YAAY,EACZC,uBAAuB,EACvBC,MAAM,EACNC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,gBAAgB,EAChBC,OAAO,QACF,eAAe;AACtB,SACEC,QAAQ,IAAIC,YAAY,EACxBC,IAAI,IAAIC,QAAQ,EAChBC,GAAG,IAAIC,OAAO,EACdC,MAAM,IAAIC,UAAU,EACpBC,UAAU,IAAIC,cAAc,EAC5BC,aAAa,IAAIC,iBAAiB,EAClCC,OAAO,IAAIC,WAAW,EACtBC,WAAW,IAAIC,eAAe,QACzB,qBAAqB;AAC5B,SAASC,OAAO,QAAQ,yBAAyB;AACjD,OAAOC,WAAW,MAAM,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3C,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC7B,MAAM;IAAEC,IAAI;IAAEC;EAAO,CAAC,GAAGP,OAAO,CAAC,CAAC;EAClC,MAAM,CAACQ,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG/C,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAACgD,YAAY,EAAEC,eAAe,CAAC,GAAGjD,QAAQ,CAAC;IAC/CkD,eAAe,EAAE,EAAE;IACnBC,WAAW,EAAE,EAAE;IACfC,eAAe,EAAE;EACnB,CAAC,CAAC;EACF,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGtD,QAAQ,CAAC;IACjDuD,OAAO,EAAE,KAAK;IACdC,GAAG,EAAE,KAAK;IACVC,OAAO,EAAE;EACX,CAAC,CAAC;EACF,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAG3D,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC4D,KAAK,EAAEC,QAAQ,CAAC,GAAG7D,QAAQ,CAAC;IAAE8D,IAAI,EAAE,KAAK;IAAEC,OAAO,EAAE,EAAE;IAAEC,QAAQ,EAAE;EAAU,CAAC,CAAC;EACrF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGlE,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAEhD;EACA,MAAM,CAACmE,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGpE,QAAQ,CAAC;IACvDqE,aAAa,EAAE,KAAK;IACpBC,kBAAkB,EAAE,IAAI;IACxBC,cAAc,EAAE,IAAI;IACpBC,cAAc,EAAE;EAClB,CAAC,CAAC;EAEF,MAAMC,SAAS,GAAGA,CAACV,OAAO,EAAEC,QAAQ,GAAG,SAAS,KAAK;IACnDH,QAAQ,CAAC;MAAEC,IAAI,EAAE,IAAI;MAAEC,OAAO;MAAEC;IAAS,CAAC,CAAC;IAC3CU,UAAU,CAAC,MAAMb,QAAQ,CAAC;MAAEC,IAAI,EAAE,KAAK;MAAEC,OAAO,EAAE,EAAE;MAAEC,QAAQ,EAAE;IAAU,CAAC,CAAC,EAAE,IAAI,CAAC;EACrF,CAAC;EAED,MAAMW,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC;IACA,MAAMC,MAAM,GAAG,CAAC,CAAC;IAEjB,IAAI,CAAC5B,YAAY,CAACE,eAAe,EAAE;MACjC0B,MAAM,CAAC1B,eAAe,GAAG,8BAA8B;IACzD;IAEA,IAAI,CAACF,YAAY,CAACG,WAAW,EAAE;MAC7ByB,MAAM,CAACzB,WAAW,GAAG,0BAA0B;IACjD,CAAC,MAAM;MACL,MAAM0B,UAAU,GAAGtC,WAAW,CAACuC,gBAAgB,CAAC9B,YAAY,CAACG,WAAW,CAAC;MACzE,IAAI,CAAC0B,UAAU,CAACE,OAAO,EAAE;QACvBH,MAAM,CAACzB,WAAW,GAAG0B,UAAU,CAACD,MAAM,CAAC,CAAC,CAAC;MAC3C;IACF;IAEA,IAAI,CAAC5B,YAAY,CAACI,eAAe,EAAE;MACjCwB,MAAM,CAACxB,eAAe,GAAG,kCAAkC;IAC7D,CAAC,MAAM,IAAIJ,YAAY,CAACG,WAAW,KAAKH,YAAY,CAACI,eAAe,EAAE;MACpEwB,MAAM,CAACxB,eAAe,GAAG,wBAAwB;IACnD;IAEA,IAAIJ,YAAY,CAACE,eAAe,KAAKF,YAAY,CAACG,WAAW,EAAE;MAC7DyB,MAAM,CAACzB,WAAW,GAAG,sDAAsD;IAC7E;IAEAe,aAAa,CAACU,MAAM,CAAC;IAErB,IAAII,MAAM,CAACC,IAAI,CAACL,MAAM,CAAC,CAACM,MAAM,GAAG,CAAC,EAAE;MAClC;IACF;IAEAvB,YAAY,CAAC,IAAI,CAAC;IAElB,IAAI;MACF;MACA,MAAM,IAAIwB,OAAO,CAACC,OAAO,IAAIV,UAAU,CAACU,OAAO,EAAE,IAAI,CAAC,CAAC;MAEvDX,SAAS,CAAC,gCAAgC,EAAE,SAAS,CAAC;MACtD1B,qBAAqB,CAAC,KAAK,CAAC;MAC5BE,eAAe,CAAC;QACdC,eAAe,EAAE,EAAE;QACnBC,WAAW,EAAE,EAAE;QACfC,eAAe,EAAE;MACnB,CAAC,CAAC;MACFc,aAAa,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,OAAOmB,KAAK,EAAE;MACdZ,SAAS,CAAC,8CAA8C,EAAE,OAAO,CAAC;IACpE,CAAC,SAAS;MACRd,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAM2B,2BAA2B,GAAIC,OAAO,IAAK;IAC/CnB,mBAAmB,CAACoB,IAAI,KAAK;MAC3B,GAAGA,IAAI;MACP,CAACD,OAAO,GAAG,CAACC,IAAI,CAACD,OAAO;IAC1B,CAAC,CAAC,CAAC;IAEHd,SAAS,CAAC,GAAGc,OAAO,CAACE,OAAO,CAAC,UAAU,EAAE,KAAK,CAAC,CAACC,WAAW,CAAC,CAAC,IAAIvB,gBAAgB,CAACoB,OAAO,CAAC,GAAG,UAAU,GAAG,SAAS,EAAE,EAAE,MAAM,CAAC;EAChI,CAAC;EAED,MAAMI,mBAAmB,GAAIC,QAAQ,IAAK;IACxC,IAAI,CAACA,QAAQ,EAAE,OAAO;MAAEC,QAAQ,EAAE,CAAC;MAAEC,KAAK,EAAE,aAAa;MAAEC,KAAK,EAAE;IAAU,CAAC;IAE7E,IAAIF,QAAQ,GAAG,CAAC;IAChB,IAAID,QAAQ,CAACV,MAAM,IAAI,CAAC,EAAEW,QAAQ,EAAE;IACpC,IAAI,OAAO,CAACG,IAAI,CAACJ,QAAQ,CAAC,EAAEC,QAAQ,EAAE;IACtC,IAAI,OAAO,CAACG,IAAI,CAACJ,QAAQ,CAAC,EAAEC,QAAQ,EAAE;IACtC,IAAI,IAAI,CAACG,IAAI,CAACJ,QAAQ,CAAC,EAAEC,QAAQ,EAAE;IACnC,IAAI,WAAW,CAACG,IAAI,CAACJ,QAAQ,CAAC,EAAEC,QAAQ,EAAE;IAE1C,MAAMI,MAAM,GAAG,CACb;MAAEJ,QAAQ,EAAE,CAAC;MAAEC,KAAK,EAAE,WAAW;MAAEC,KAAK,EAAE;IAAQ,CAAC,EACnD;MAAEF,QAAQ,EAAE,CAAC;MAAEC,KAAK,EAAE,MAAM;MAAEC,KAAK,EAAE;IAAQ,CAAC,EAC9C;MAAEF,QAAQ,EAAE,CAAC;MAAEC,KAAK,EAAE,MAAM;MAAEC,KAAK,EAAE;IAAU,CAAC,EAChD;MAAEF,QAAQ,EAAE,CAAC;MAAEC,KAAK,EAAE,MAAM;MAAEC,KAAK,EAAE;IAAO,CAAC,EAC7C;MAAEF,QAAQ,EAAE,CAAC;MAAEC,KAAK,EAAE,QAAQ;MAAEC,KAAK,EAAE;IAAU,CAAC,EAClD;MAAEF,QAAQ,EAAE,CAAC;MAAEC,KAAK,EAAE,aAAa;MAAEC,KAAK,EAAE;IAAU,CAAC,CACxD;IAED,OAAOE,MAAM,CAACJ,QAAQ,CAAC;EACzB,CAAC;EAED,MAAMK,gBAAgB,GAAGP,mBAAmB,CAAC3C,YAAY,CAACG,WAAW,CAAC;EAEtE,oBACEV,OAAA,CAACxC,GAAG;IAAAkG,QAAA,GAEDvC,KAAK,CAACE,IAAI,iBACTrB,OAAA,CAAC1B,KAAK;MAACiD,QAAQ,EAAEJ,KAAK,CAACI,QAAS;MAACoC,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAF,QAAA,EAC5CvC,KAAK,CAACG;IAAO;MAAAuC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACT,CACR,eAEDhE,OAAA,CAACrC,UAAU;MAACsG,OAAO,EAAC,IAAI;MAACC,YAAY;MAACC,UAAU,EAAC,MAAM;MAAAT,QAAA,EAAC;IAExD;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eACbhE,OAAA,CAACrC,UAAU;MAACsG,OAAO,EAAC,OAAO;MAACX,KAAK,EAAC,gBAAgB;MAACK,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAF,QAAA,EAAC;IAElE;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eAGbhE,OAAA,CAACvC,IAAI;MAACkG,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAF,QAAA,eAClB1D,OAAA,CAACtC,WAAW;QAAAgG,QAAA,gBACV1D,OAAA,CAACxC,GAAG;UAACmG,EAAE,EAAE;YAAES,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAET,EAAE,EAAE;UAAE,CAAE;UAAAF,QAAA,gBACxD1D,OAAA,CAAChB,QAAQ;YAAC2E,EAAE,EAAE;cAAEW,EAAE,EAAE,CAAC;cAAEhB,KAAK,EAAE;YAAe;UAAE;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAClDhE,OAAA,CAACrC,UAAU;YAACsG,OAAO,EAAC,IAAI;YAACE,UAAU,EAAC,KAAK;YAAAT,QAAA,EAAC;UAE1C;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAENhE,OAAA,CAACrC,UAAU;UAACsG,OAAO,EAAC,OAAO;UAACX,KAAK,EAAC,gBAAgB;UAACK,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAAF,QAAA,EAAC;QAElE;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAEbhE,OAAA,CAACpC,MAAM;UACLqG,OAAO,EAAC,UAAU;UAClBM,SAAS,eAAEvE,OAAA,CAACd,OAAO;YAAA2E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBQ,OAAO,EAAEA,CAAA,KAAMlE,qBAAqB,CAAC,IAAI,CAAE;UAC3CqD,EAAE,EAAE;YAAEc,YAAY,EAAE;UAAE,CAAE;UAAAf,QAAA,EACzB;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGPhE,OAAA,CAACvC,IAAI;MAACkG,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAF,QAAA,eAClB1D,OAAA,CAACtC,WAAW;QAAAgG,QAAA,gBACV1D,OAAA,CAACxC,GAAG;UAACmG,EAAE,EAAE;YAAES,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAET,EAAE,EAAE;UAAE,CAAE;UAAAF,QAAA,gBACxD1D,OAAA,CAACZ,UAAU;YAACuE,EAAE,EAAE;cAAEW,EAAE,EAAE,CAAC;cAAEhB,KAAK,EAAE;YAAe;UAAE;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACpDhE,OAAA,CAACrC,UAAU;YAACsG,OAAO,EAAC,IAAI;YAACE,UAAU,EAAC,KAAK;YAAAT,QAAA,EAAC;UAE1C;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAENhE,OAAA,CAACrC,UAAU;UAACsG,OAAO,EAAC,OAAO;UAACX,KAAK,EAAC,gBAAgB;UAACK,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAAF,QAAA,EAAC;QAElE;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAEbhE,OAAA,CAACxC,GAAG;UAACmG,EAAE,EAAE;YAAES,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAEK,cAAc,EAAE;UAAgB,CAAE;UAAAhB,QAAA,gBAClF1D,OAAA,CAACxC,GAAG;YAAAkG,QAAA,gBACF1D,OAAA,CAACrC,UAAU;cAACsG,OAAO,EAAC,OAAO;cAACE,UAAU,EAAC,KAAK;cAAAT,QAAA,EAAC;YAE7C;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbhE,OAAA,CAACrC,UAAU;cAACsG,OAAO,EAAC,OAAO;cAACX,KAAK,EAAC,gBAAgB;cAAAI,QAAA,EAAC;YAEnD;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACNhE,OAAA,CAAC5B,MAAM;YACLuG,OAAO,EAAEjD,gBAAgB,CAACE,aAAc;YACxCgD,QAAQ,EAAEA,CAAA,KAAM/B,2BAA2B,CAAC,eAAe;UAAE;YAAAgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9D,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGPhE,OAAA,CAACvC,IAAI;MAACkG,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAF,QAAA,eAClB1D,OAAA,CAACtC,WAAW;QAAAgG,QAAA,gBACV1D,OAAA,CAACxC,GAAG;UAACmG,EAAE,EAAE;YAAES,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAET,EAAE,EAAE;UAAE,CAAE;UAAAF,QAAA,gBACxD1D,OAAA,CAAClB,YAAY;YAAC6E,EAAE,EAAE;cAAEW,EAAE,EAAE,CAAC;cAAEhB,KAAK,EAAE;YAAe;UAAE;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACtDhE,OAAA,CAACrC,UAAU;YAACsG,OAAO,EAAC,IAAI;YAACE,UAAU,EAAC,KAAK;YAAAT,QAAA,EAAC;UAE1C;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAENhE,OAAA,CAACjC,IAAI;UAAA2F,QAAA,gBACH1D,OAAA,CAAChC,QAAQ;YAAA0F,QAAA,gBACP1D,OAAA,CAAC9B,YAAY;cACX2G,OAAO,EAAC,qBAAqB;cAC7BC,SAAS,EAAC;YAAkD;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7D,CAAC,eACFhE,OAAA,CAAC7B,uBAAuB;cAAAuF,QAAA,eACtB1D,OAAA,CAAC5B,MAAM;gBACLuG,OAAO,EAAEjD,gBAAgB,CAACG,kBAAmB;gBAC7C+C,QAAQ,EAAEA,CAAA,KAAM/B,2BAA2B,CAAC,oBAAoB;cAAE;gBAAAgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACqB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB,CAAC,eAEXhE,OAAA,CAACpB,OAAO;YAAAiF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAEXhE,OAAA,CAAChC,QAAQ;YAAA0F,QAAA,gBACP1D,OAAA,CAAC9B,YAAY;cACX2G,OAAO,EAAC,iBAAiB;cACzBC,SAAS,EAAC;YAAkD;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7D,CAAC,eACFhE,OAAA,CAAC7B,uBAAuB;cAAAuF,QAAA,eACtB1D,OAAA,CAAC5B,MAAM;gBACLuG,OAAO,EAAEjD,gBAAgB,CAACI,cAAe;gBACzC8C,QAAQ,EAAEA,CAAA,KAAM/B,2BAA2B,CAAC,gBAAgB;cAAE;gBAAAgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/D;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACqB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB,CAAC,eAEXhE,OAAA,CAACpB,OAAO;YAAAiF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAEXhE,OAAA,CAAChC,QAAQ;YAAA0F,QAAA,gBACP1D,OAAA,CAAC9B,YAAY;cACX2G,OAAO,EAAC,iBAAiB;cACzBC,SAAS,EAAC;YAAgD;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3D,CAAC,eACFhE,OAAA,CAAC7B,uBAAuB;cAAAuF,QAAA,eACtB1D,OAAA,CAAC5B,MAAM;gBACLuG,OAAO,EAAEjD,gBAAgB,CAACK,cAAe;gBACzC6C,QAAQ,EAAEA,CAAA,KAAM/B,2BAA2B,CAAC,gBAAgB;cAAE;gBAAAgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/D;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACqB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGPhE,OAAA,CAACvC,IAAI;MAAAiG,QAAA,eACH1D,OAAA,CAACtC,WAAW;QAAAgG,QAAA,gBACV1D,OAAA,CAACrC,UAAU;UAACsG,OAAO,EAAC,IAAI;UAACE,UAAU,EAAC,KAAK;UAACR,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAAF,QAAA,EAAC;QAEzD;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAEbhE,OAAA,CAACxC,GAAG;UAACmG,EAAE,EAAE;YAAES,OAAO,EAAE,MAAM;YAAEW,GAAG,EAAE,CAAC;YAAEC,QAAQ,EAAE;UAAO,CAAE;UAAAtB,QAAA,gBACrD1D,OAAA,CAACpC,MAAM;YACLqG,OAAO,EAAC,UAAU;YAClBX,KAAK,EAAC,OAAO;YACbkB,OAAO,EAAEpE,MAAO;YAChBuD,EAAE,EAAE;cAAEc,YAAY,EAAE;YAAE,CAAE;YAAAf,QAAA,EACzB;UAED;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAEThE,OAAA,CAACpC,MAAM;YACLqG,OAAO,EAAC,UAAU;YAClBX,KAAK,EAAC,SAAS;YACfK,EAAE,EAAE;cAAEc,YAAY,EAAE;YAAE,CAAE;YAAAf,QAAA,EACzB;UAED;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGPhE,OAAA,CAACzB,MAAM;MACL0G,IAAI,EAAE5E,kBAAmB;MACzB6E,OAAO,EAAEA,CAAA,KAAM5E,qBAAqB,CAAC,KAAK,CAAE;MAC5C6E,QAAQ,EAAC,IAAI;MACbC,SAAS;MAAA1B,QAAA,gBAET1D,OAAA,CAACxB,WAAW;QAAAkF,QAAA,eACV1D,OAAA,CAACrC,UAAU;UAACsG,OAAO,EAAC,IAAI;UAACE,UAAU,EAAC,MAAM;UAAAT,QAAA,EAAC;QAE3C;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAEdhE,OAAA,CAACvB,aAAa;QAAAiF,QAAA,gBACZ1D,OAAA,CAACnC,SAAS;UACRuH,SAAS;UACT/B,KAAK,EAAC,kBAAkB;UACxBgC,IAAI,EAAEzE,aAAa,CAACE,OAAO,GAAG,MAAM,GAAG,UAAW;UAClDwE,KAAK,EAAE/E,YAAY,CAACE,eAAgB;UACpCmE,QAAQ,EAAGW,CAAC,IAAK;YACf/E,eAAe,CAAC;cAAE,GAAGD,YAAY;cAAEE,eAAe,EAAE8E,CAAC,CAACC,MAAM,CAACF;YAAM,CAAC,CAAC;YACrE,IAAI9D,UAAU,CAACf,eAAe,EAAE;cAC9BgB,aAAa,CAAC;gBAAE,GAAGD,UAAU;gBAAEf,eAAe,EAAE;cAAG,CAAC,CAAC;YACvD;UACF,CAAE;UACFgF,MAAM,EAAC,QAAQ;UACf7C,KAAK,EAAE,CAAC,CAACpB,UAAU,CAACf,eAAgB;UACpCiF,UAAU,EAAElE,UAAU,CAACf,eAAgB;UACvCkF,UAAU,EAAE;YACVC,YAAY,eACV5F,OAAA,CAAClC,UAAU;cACT0G,OAAO,EAAEA,CAAA,KAAM3D,gBAAgB,CAAC;gBAAE,GAAGD,aAAa;gBAAEE,OAAO,EAAE,CAACF,aAAa,CAACE;cAAQ,CAAC,CAAE;cAAA4C,QAAA,EAEtF9C,aAAa,CAACE,OAAO,gBAAGd,OAAA,CAACR,iBAAiB;gBAAAqE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAAGhE,OAAA,CAACV,cAAc;gBAAAuE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzD;UAEhB;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEFhE,OAAA,CAACnC,SAAS;UACRuH,SAAS;UACT/B,KAAK,EAAC,cAAc;UACpBgC,IAAI,EAAEzE,aAAa,CAACG,GAAG,GAAG,MAAM,GAAG,UAAW;UAC9CuE,KAAK,EAAE/E,YAAY,CAACG,WAAY;UAChCkE,QAAQ,EAAGW,CAAC,IAAK;YACf/E,eAAe,CAAC;cAAE,GAAGD,YAAY;cAAEG,WAAW,EAAE6E,CAAC,CAACC,MAAM,CAACF;YAAM,CAAC,CAAC;YACjE,IAAI9D,UAAU,CAACd,WAAW,EAAE;cAC1Be,aAAa,CAAC;gBAAE,GAAGD,UAAU;gBAAEd,WAAW,EAAE;cAAG,CAAC,CAAC;YACnD;UACF,CAAE;UACF+E,MAAM,EAAC,QAAQ;UACf7C,KAAK,EAAE,CAAC,CAACpB,UAAU,CAACd,WAAY;UAChCgF,UAAU,EAAElE,UAAU,CAACd,WAAY;UACnCiF,UAAU,EAAE;YACVC,YAAY,eACV5F,OAAA,CAAClC,UAAU;cACT0G,OAAO,EAAEA,CAAA,KAAM3D,gBAAgB,CAAC;gBAAE,GAAGD,aAAa;gBAAEG,GAAG,EAAE,CAACH,aAAa,CAACG;cAAI,CAAC,CAAE;cAAA2C,QAAA,EAE9E9C,aAAa,CAACG,GAAG,gBAAGf,OAAA,CAACR,iBAAiB;gBAAAqE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAAGhE,OAAA,CAACV,cAAc;gBAAAuE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrD;UAEhB;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAEDzD,YAAY,CAACG,WAAW,iBACvBV,OAAA,CAACxC,GAAG;UAACmG,EAAE,EAAE;YAAEkC,EAAE,EAAE,CAAC;YAAEjC,EAAE,EAAE;UAAE,CAAE;UAAAF,QAAA,eACxB1D,OAAA,CAAC3B,IAAI;YACHgF,KAAK,EAAE,sBAAsBI,gBAAgB,CAACJ,KAAK,EAAG;YACtDC,KAAK,EAAEG,gBAAgB,CAACH,KAAM;YAC9BwC,IAAI,EAAC,OAAO;YACZC,IAAI,EAAEtC,gBAAgB,CAACL,QAAQ,IAAI,CAAC,gBAAGpD,OAAA,CAACJ,eAAe;cAAAiE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAGhE,OAAA,CAACN,WAAW;cAAAmE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9E;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN,eAEDhE,OAAA,CAACnC,SAAS;UACRuH,SAAS;UACT/B,KAAK,EAAC,sBAAsB;UAC5BgC,IAAI,EAAEzE,aAAa,CAACI,OAAO,GAAG,MAAM,GAAG,UAAW;UAClDsE,KAAK,EAAE/E,YAAY,CAACI,eAAgB;UACpCiE,QAAQ,EAAGW,CAAC,IAAK;YACf/E,eAAe,CAAC;cAAE,GAAGD,YAAY;cAAEI,eAAe,EAAE4E,CAAC,CAACC,MAAM,CAACF;YAAM,CAAC,CAAC;YACrE,IAAI9D,UAAU,CAACb,eAAe,EAAE;cAC9Bc,aAAa,CAAC;gBAAE,GAAGD,UAAU;gBAAEb,eAAe,EAAE;cAAG,CAAC,CAAC;YACvD;UACF,CAAE;UACF8E,MAAM,EAAC,QAAQ;UACf7C,KAAK,EAAE,CAAC,CAACpB,UAAU,CAACb,eAAgB;UACpC+E,UAAU,EAAElE,UAAU,CAACb,eAAgB;UACvCgF,UAAU,EAAE;YACVC,YAAY,eACV5F,OAAA,CAAClC,UAAU;cACT0G,OAAO,EAAEA,CAAA,KAAM3D,gBAAgB,CAAC;gBAAE,GAAGD,aAAa;gBAAEI,OAAO,EAAE,CAACJ,aAAa,CAACI;cAAQ,CAAC,CAAE;cAAA0C,QAAA,EAEtF9C,aAAa,CAACI,OAAO,gBAAGhB,OAAA,CAACR,iBAAiB;gBAAAqE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAAGhE,OAAA,CAACV,cAAc;gBAAAuE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzD;UAEhB;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACW,CAAC,eAEhBhE,OAAA,CAACtB,aAAa;QAACiF,EAAE,EAAE;UAAEqC,CAAC,EAAE;QAAE,CAAE;QAAAtC,QAAA,gBAC1B1D,OAAA,CAACpC,MAAM;UACL4G,OAAO,EAAEA,CAAA,KAAMlE,qBAAqB,CAAC,KAAK,CAAE;UAC5C2F,QAAQ,EAAEhF,SAAU;UAAAyC,QAAA,EACrB;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACThE,OAAA,CAACpC,MAAM;UACLqG,OAAO,EAAC,WAAW;UACnBO,OAAO,EAAEtC,oBAAqB;UAC9B+D,QAAQ,EAAEhF,SAAU;UACpBsD,SAAS,EAAEtD,SAAS,gBAAGjB,OAAA,CAACrB,gBAAgB;YAACmH,IAAI,EAAE;UAAG;YAAAjC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAAGhE,OAAA,CAAChB,QAAQ;YAAA6E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAN,QAAA,EAEpEzC,SAAS,GAAG,aAAa,GAAG;QAAiB;UAAA4C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAAC9D,EAAA,CA1YID,gBAAgB;EAAA,QACKJ,OAAO;AAAA;AAAAqG,EAAA,GAD5BjG,gBAAgB;AA4YtB,eAAeA,gBAAgB;AAAC,IAAAiG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}