{"ast": null, "code": "var _jsxFileName = \"E:\\\\PythonProjects\\\\React.js\\\\Smart AI Assisstant\\\\src\\\\components\\\\FloatingActionMenu.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Fab, SpeedDial, SpeedDialAction, SpeedDialIcon, Tooltip, Box } from '@mui/material';\nimport { Add as AddIcon, Security as SecurityIcon, Link as LinkIcon, Upload as UploadIcon, History as HistoryIcon, Analytics as AnalyticsIcon, AutoAwesome as AutoIcon } from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst FloatingActionMenu = /*#__PURE__*/_s(/*#__PURE__*/React.memo(_c = _s(() => {\n  _s();\n  const [open, setOpen] = useState(false);\n  const navigate = useNavigate();\n  const actions = [{\n    icon: /*#__PURE__*/_jsxDEV(LinkIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 27,\n      columnNumber: 13\n    }, this),\n    name: 'Quick URL Scan',\n    action: () => {\n      navigate('/');\n      // Scroll to scanner section\n      setTimeout(() => {\n        const scanner = document.querySelector('[data-testid=\"url-scanner\"]');\n        if (scanner) {\n          scanner.scrollIntoView({\n            behavior: 'smooth',\n            block: 'center'\n          });\n        }\n      }, 100);\n    }\n  }, {\n    icon: /*#__PURE__*/_jsxDEV(UploadIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 41,\n      columnNumber: 13\n    }, this),\n    name: 'Quick File Scan',\n    action: () => {\n      navigate('/');\n      // Scroll to file scanner and trigger file input\n      setTimeout(() => {\n        const scanner = document.querySelector('[data-testid=\"file-scanner\"]');\n        if (scanner) {\n          scanner.scrollIntoView({\n            behavior: 'smooth',\n            block: 'center'\n          });\n          // Try to trigger the file input\n          setTimeout(() => {\n            const fileInput = scanner.querySelector('input[type=\"file\"]');\n            if (fileInput) {\n              fileInput.click();\n            }\n          }, 500);\n        }\n      }, 100);\n    }\n  }, {\n    icon: /*#__PURE__*/_jsxDEV(HistoryIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 62,\n      columnNumber: 13\n    }, this),\n    name: 'View History',\n    action: () => navigate('/history')\n  }, {\n    icon: /*#__PURE__*/_jsxDEV(AnalyticsIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 67,\n      columnNumber: 13\n    }, this),\n    name: 'Smart Features',\n    action: () => navigate('/smart-features')\n  }, {\n    icon: /*#__PURE__*/_jsxDEV(AutoIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 72,\n      columnNumber: 13\n    }, this),\n    name: 'Auto Scan',\n    action: () => {\n      navigate('/');\n      // Scroll to auto scan section\n      setTimeout(() => {\n        const autoScan = document.querySelector('[data-testid=\"auto-scan\"]');\n        if (autoScan) {\n          autoScan.scrollIntoView({\n            behavior: 'smooth',\n            block: 'center'\n          });\n        }\n      }, 100);\n    }\n  }];\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      position: 'fixed',\n      bottom: 24,\n      right: 24,\n      zIndex: 1300\n    },\n    children: /*#__PURE__*/_jsxDEV(SpeedDial, {\n      ariaLabel: \"Quick Actions\",\n      sx: {\n        '& .MuiFab-primary': {\n          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n          boxShadow: '0 8px 25px rgba(102, 126, 234, 0.4)',\n          '&:hover': {\n            background: 'linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)',\n            boxShadow: '0 12px 35px rgba(102, 126, 234, 0.5)',\n            transform: 'scale(1.1)'\n          }\n        },\n        '& .MuiSpeedDial-actions': {\n          paddingBottom: 0\n        }\n      },\n      icon: /*#__PURE__*/_jsxDEV(SpeedDialIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 112,\n        columnNumber: 15\n      }, this),\n      onClose: () => setOpen(false),\n      onOpen: () => setOpen(true),\n      open: open,\n      direction: \"up\",\n      children: actions.map(action => /*#__PURE__*/_jsxDEV(SpeedDialAction, {\n        icon: action.icon,\n        tooltipTitle: action.name,\n        tooltipOpen: true,\n        onClick: () => {\n          setOpen(false);\n          action.action();\n        },\n        sx: {\n          '& .MuiFab-primary': {\n            background: 'linear-gradient(135deg, rgba(12, 12, 12, 0.95) 0%, rgba(26, 26, 26, 0.95) 100%)',\n            backdropFilter: 'blur(25px)',\n            border: '1px solid rgba(255, 255, 255, 0.1)',\n            color: 'white',\n            '&:hover': {\n              background: 'linear-gradient(135deg, rgba(102, 126, 234, 0.2) 0%, rgba(118, 75, 162, 0.2) 100%)',\n              transform: 'scale(1.1)'\n            }\n          }\n        }\n      }, action.name, false, {\n        fileName: _jsxFileName,\n        lineNumber: 119,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 96,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 88,\n    columnNumber: 5\n  }, this);\n}, \"YUK7oHXSEMieGazmkICo5Lb28go=\", false, function () {\n  return [useNavigate];\n})), \"YUK7oHXSEMieGazmkICo5Lb28go=\", false, function () {\n  return [useNavigate];\n});\n_c2 = FloatingActionMenu;\nFloatingActionMenu.displayName = 'FloatingActionMenu';\nexport default FloatingActionMenu;\nvar _c, _c2;\n$RefreshReg$(_c, \"FloatingActionMenu$React.memo\");\n$RefreshReg$(_c2, \"FloatingActionMenu\");", "map": {"version": 3, "names": ["React", "useState", "Fab", "SpeedDial", "SpeedDialAction", "SpeedDialIcon", "<PERSON><PERSON><PERSON>", "Box", "Add", "AddIcon", "Security", "SecurityIcon", "Link", "LinkIcon", "Upload", "UploadIcon", "History", "HistoryIcon", "Analytics", "AnalyticsIcon", "AutoAwesome", "AutoIcon", "useNavigate", "jsxDEV", "_jsxDEV", "FloatingActionMenu", "_s", "memo", "_c", "open", "<PERSON><PERSON><PERSON>", "navigate", "actions", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "name", "action", "setTimeout", "scanner", "document", "querySelector", "scrollIntoView", "behavior", "block", "fileInput", "click", "autoScan", "sx", "position", "bottom", "right", "zIndex", "children", "aria<PERSON><PERSON><PERSON>", "background", "boxShadow", "transform", "paddingBottom", "onClose", "onOpen", "direction", "map", "tooltipTitle", "tooltipOpen", "onClick", "<PERSON><PERSON>ilter", "border", "color", "_c2", "displayName", "$RefreshReg$"], "sources": ["E:/PythonProjects/React.js/Smart AI Assisstant/src/components/FloatingActionMenu.jsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  Fab,\n  SpeedDial,\n  SpeedDialAction,\n  SpeedDialIcon,\n  Tooltip,\n  Box,\n} from '@mui/material';\nimport {\n  Add as AddIcon,\n  Security as SecurityIcon,\n  Link as LinkIcon,\n  Upload as UploadIcon,\n  History as HistoryIcon,\n  Analytics as AnalyticsIcon,\n  AutoAwesome as AutoIcon,\n} from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\n\nconst FloatingActionMenu = React.memo(() => {\n  const [open, setOpen] = useState(false);\n  const navigate = useNavigate();\n\n  const actions = [\n    {\n      icon: <LinkIcon />,\n      name: 'Quick URL Scan',\n      action: () => {\n        navigate('/');\n        // Scroll to scanner section\n        setTimeout(() => {\n          const scanner = document.querySelector('[data-testid=\"url-scanner\"]');\n          if (scanner) {\n            scanner.scrollIntoView({ behavior: 'smooth', block: 'center' });\n          }\n        }, 100);\n      },\n    },\n    {\n      icon: <UploadIcon />,\n      name: 'Quick File Scan',\n      action: () => {\n        navigate('/');\n        // Scroll to file scanner and trigger file input\n        setTimeout(() => {\n          const scanner = document.querySelector('[data-testid=\"file-scanner\"]');\n          if (scanner) {\n            scanner.scrollIntoView({ behavior: 'smooth', block: 'center' });\n            // Try to trigger the file input\n            setTimeout(() => {\n              const fileInput = scanner.querySelector('input[type=\"file\"]');\n              if (fileInput) {\n                fileInput.click();\n              }\n            }, 500);\n          }\n        }, 100);\n      },\n    },\n    {\n      icon: <HistoryIcon />,\n      name: 'View History',\n      action: () => navigate('/history'),\n    },\n    {\n      icon: <AnalyticsIcon />,\n      name: 'Smart Features',\n      action: () => navigate('/smart-features'),\n    },\n    {\n      icon: <AutoIcon />,\n      name: 'Auto Scan',\n      action: () => {\n        navigate('/');\n        // Scroll to auto scan section\n        setTimeout(() => {\n          const autoScan = document.querySelector('[data-testid=\"auto-scan\"]');\n          if (autoScan) {\n            autoScan.scrollIntoView({ behavior: 'smooth', block: 'center' });\n          }\n        }, 100);\n      },\n    },\n  ];\n\n  return (\n    <Box\n      sx={{\n        position: 'fixed',\n        bottom: 24,\n        right: 24,\n        zIndex: 1300,\n      }}\n    >\n      <SpeedDial\n        ariaLabel=\"Quick Actions\"\n        sx={{\n          '& .MuiFab-primary': {\n            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n            boxShadow: '0 8px 25px rgba(102, 126, 234, 0.4)',\n            '&:hover': {\n              background: 'linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)',\n              boxShadow: '0 12px 35px rgba(102, 126, 234, 0.5)',\n              transform: 'scale(1.1)',\n            },\n          },\n          '& .MuiSpeedDial-actions': {\n            paddingBottom: 0,\n          },\n        }}\n        icon={<SpeedDialIcon />}\n        onClose={() => setOpen(false)}\n        onOpen={() => setOpen(true)}\n        open={open}\n        direction=\"up\"\n      >\n        {actions.map((action) => (\n          <SpeedDialAction\n            key={action.name}\n            icon={action.icon}\n            tooltipTitle={action.name}\n            tooltipOpen\n            onClick={() => {\n              setOpen(false);\n              action.action();\n            }}\n            sx={{\n              '& .MuiFab-primary': {\n                background: 'linear-gradient(135deg, rgba(12, 12, 12, 0.95) 0%, rgba(26, 26, 26, 0.95) 100%)',\n                backdropFilter: 'blur(25px)',\n                border: '1px solid rgba(255, 255, 255, 0.1)',\n                color: 'white',\n                '&:hover': {\n                  background: 'linear-gradient(135deg, rgba(102, 126, 234, 0.2) 0%, rgba(118, 75, 162, 0.2) 100%)',\n                  transform: 'scale(1.1)',\n                },\n              },\n            }}\n          />\n        ))}\n      </SpeedDial>\n    </Box>\n  );\n});\n\nFloatingActionMenu.displayName = 'FloatingActionMenu';\n\nexport default FloatingActionMenu;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,GAAG,EACHC,SAAS,EACTC,eAAe,EACfC,aAAa,EACbC,OAAO,EACPC,GAAG,QACE,eAAe;AACtB,SACEC,GAAG,IAAIC,OAAO,EACdC,QAAQ,IAAIC,YAAY,EACxBC,IAAI,IAAIC,QAAQ,EAChBC,MAAM,IAAIC,UAAU,EACpBC,OAAO,IAAIC,WAAW,EACtBC,SAAS,IAAIC,aAAa,EAC1BC,WAAW,IAAIC,QAAQ,QAClB,qBAAqB;AAC5B,SAASC,WAAW,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/C,MAAMC,kBAAkB,gBAAAC,EAAA,cAAG1B,KAAK,CAAC2B,IAAI,CAAAC,EAAA,GAAAF,EAAA,CAAC,MAAM;EAAAA,EAAA;EAC1C,MAAM,CAACG,IAAI,EAAEC,OAAO,CAAC,GAAG7B,QAAQ,CAAC,KAAK,CAAC;EACvC,MAAM8B,QAAQ,GAAGT,WAAW,CAAC,CAAC;EAE9B,MAAMU,OAAO,GAAG,CACd;IACEC,IAAI,eAAET,OAAA,CAACX,QAAQ;MAAAqB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAClBC,IAAI,EAAE,gBAAgB;IACtBC,MAAM,EAAEA,CAAA,KAAM;MACZR,QAAQ,CAAC,GAAG,CAAC;MACb;MACAS,UAAU,CAAC,MAAM;QACf,MAAMC,OAAO,GAAGC,QAAQ,CAACC,aAAa,CAAC,6BAA6B,CAAC;QACrE,IAAIF,OAAO,EAAE;UACXA,OAAO,CAACG,cAAc,CAAC;YAAEC,QAAQ,EAAE,QAAQ;YAAEC,KAAK,EAAE;UAAS,CAAC,CAAC;QACjE;MACF,CAAC,EAAE,GAAG,CAAC;IACT;EACF,CAAC,EACD;IACEb,IAAI,eAAET,OAAA,CAACT,UAAU;MAAAmB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACpBC,IAAI,EAAE,iBAAiB;IACvBC,MAAM,EAAEA,CAAA,KAAM;MACZR,QAAQ,CAAC,GAAG,CAAC;MACb;MACAS,UAAU,CAAC,MAAM;QACf,MAAMC,OAAO,GAAGC,QAAQ,CAACC,aAAa,CAAC,8BAA8B,CAAC;QACtE,IAAIF,OAAO,EAAE;UACXA,OAAO,CAACG,cAAc,CAAC;YAAEC,QAAQ,EAAE,QAAQ;YAAEC,KAAK,EAAE;UAAS,CAAC,CAAC;UAC/D;UACAN,UAAU,CAAC,MAAM;YACf,MAAMO,SAAS,GAAGN,OAAO,CAACE,aAAa,CAAC,oBAAoB,CAAC;YAC7D,IAAII,SAAS,EAAE;cACbA,SAAS,CAACC,KAAK,CAAC,CAAC;YACnB;UACF,CAAC,EAAE,GAAG,CAAC;QACT;MACF,CAAC,EAAE,GAAG,CAAC;IACT;EACF,CAAC,EACD;IACEf,IAAI,eAAET,OAAA,CAACP,WAAW;MAAAiB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACrBC,IAAI,EAAE,cAAc;IACpBC,MAAM,EAAEA,CAAA,KAAMR,QAAQ,CAAC,UAAU;EACnC,CAAC,EACD;IACEE,IAAI,eAAET,OAAA,CAACL,aAAa;MAAAe,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACvBC,IAAI,EAAE,gBAAgB;IACtBC,MAAM,EAAEA,CAAA,KAAMR,QAAQ,CAAC,iBAAiB;EAC1C,CAAC,EACD;IACEE,IAAI,eAAET,OAAA,CAACH,QAAQ;MAAAa,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAClBC,IAAI,EAAE,WAAW;IACjBC,MAAM,EAAEA,CAAA,KAAM;MACZR,QAAQ,CAAC,GAAG,CAAC;MACb;MACAS,UAAU,CAAC,MAAM;QACf,MAAMS,QAAQ,GAAGP,QAAQ,CAACC,aAAa,CAAC,2BAA2B,CAAC;QACpE,IAAIM,QAAQ,EAAE;UACZA,QAAQ,CAACL,cAAc,CAAC;YAAEC,QAAQ,EAAE,QAAQ;YAAEC,KAAK,EAAE;UAAS,CAAC,CAAC;QAClE;MACF,CAAC,EAAE,GAAG,CAAC;IACT;EACF,CAAC,CACF;EAED,oBACEtB,OAAA,CAACjB,GAAG;IACF2C,EAAE,EAAE;MACFC,QAAQ,EAAE,OAAO;MACjBC,MAAM,EAAE,EAAE;MACVC,KAAK,EAAE,EAAE;MACTC,MAAM,EAAE;IACV,CAAE;IAAAC,QAAA,eAEF/B,OAAA,CAACrB,SAAS;MACRqD,SAAS,EAAC,eAAe;MACzBN,EAAE,EAAE;QACF,mBAAmB,EAAE;UACnBO,UAAU,EAAE,mDAAmD;UAC/DC,SAAS,EAAE,qCAAqC;UAChD,SAAS,EAAE;YACTD,UAAU,EAAE,mDAAmD;YAC/DC,SAAS,EAAE,sCAAsC;YACjDC,SAAS,EAAE;UACb;QACF,CAAC;QACD,yBAAyB,EAAE;UACzBC,aAAa,EAAE;QACjB;MACF,CAAE;MACF3B,IAAI,eAAET,OAAA,CAACnB,aAAa;QAAA6B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAE;MACxBwB,OAAO,EAAEA,CAAA,KAAM/B,OAAO,CAAC,KAAK,CAAE;MAC9BgC,MAAM,EAAEA,CAAA,KAAMhC,OAAO,CAAC,IAAI,CAAE;MAC5BD,IAAI,EAAEA,IAAK;MACXkC,SAAS,EAAC,IAAI;MAAAR,QAAA,EAEbvB,OAAO,CAACgC,GAAG,CAAEzB,MAAM,iBAClBf,OAAA,CAACpB,eAAe;QAEd6B,IAAI,EAAEM,MAAM,CAACN,IAAK;QAClBgC,YAAY,EAAE1B,MAAM,CAACD,IAAK;QAC1B4B,WAAW;QACXC,OAAO,EAAEA,CAAA,KAAM;UACbrC,OAAO,CAAC,KAAK,CAAC;UACdS,MAAM,CAACA,MAAM,CAAC,CAAC;QACjB,CAAE;QACFW,EAAE,EAAE;UACF,mBAAmB,EAAE;YACnBO,UAAU,EAAE,iFAAiF;YAC7FW,cAAc,EAAE,YAAY;YAC5BC,MAAM,EAAE,oCAAoC;YAC5CC,KAAK,EAAE,OAAO;YACd,SAAS,EAAE;cACTb,UAAU,EAAE,oFAAoF;cAChGE,SAAS,EAAE;YACb;UACF;QACF;MAAE,GAnBGpB,MAAM,CAACD,IAAI;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAoBjB,CACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACO;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACT,CAAC;AAEV,CAAC;EAAA,QA1HkBf,WAAW;AAAA,EA0H7B,CAAC;EAAA,QA1HiBA,WAAW;AAAA,EA0H5B;AAACiD,GAAA,GA5HG9C,kBAAkB;AA8HxBA,kBAAkB,CAAC+C,WAAW,GAAG,oBAAoB;AAErD,eAAe/C,kBAAkB;AAAC,IAAAG,EAAA,EAAA2C,GAAA;AAAAE,YAAA,CAAA7C,EAAA;AAAA6C,YAAA,CAAAF,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}