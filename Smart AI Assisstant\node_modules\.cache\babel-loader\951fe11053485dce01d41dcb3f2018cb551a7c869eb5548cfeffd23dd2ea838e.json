{"ast": null, "code": "var _jsxFileName = \"E:\\\\PythonProjects\\\\React.js\\\\Smart AI Assisstant\\\\src\\\\components\\\\ResultView.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Card, CardContent, Typography, Box, Accordion, AccordionSummary, AccordionDetails, Chip, List, ListItem, ListItemIcon, ListItemText, Button, Divider, Alert, IconButton, Tooltip, Menu, MenuItem, CircularProgress, Snackbar } from '@mui/material';\nimport { ExpandMore as ExpandMoreIcon, Security as SecurityIcon, Warning as WarningIcon, Error as ErrorIcon, CheckCircle as CheckCircleIcon, Info as InfoIcon, Download as DownloadIcon, Share as ShareIcon, Visibility as VisibilityIcon, Email as EmailIcon, Link as LinkIcon, ContentCopy as CopyIcon, PictureAsPdf as PdfIcon } from '@mui/icons-material';\nimport { useScan } from '../contexts/ScanContext';\nimport ThreatMeter from './ThreatMeter';\nimport { ReportService } from '../services/reportService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ResultView = /*#__PURE__*/_s(/*#__PURE__*/React.memo(_c = _s(({\n  scanResult,\n  onDownloadReport,\n  onShareResult\n}) => {\n  _s();\n  const [expanded, setExpanded] = useState('summary');\n  const [downloadAnchor, setDownloadAnchor] = useState(null);\n  const [shareAnchor, setShareAnchor] = useState(null);\n  const [isDownloading, setIsDownloading] = useState(false);\n  const [isSharing, setIsSharing] = useState(false);\n  const [notification, setNotification] = useState(null);\n  const {\n    addNotification\n  } = useScan();\n  if (!scanResult) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        background: theme => theme.palette.mode === 'dark' ? 'linear-gradient(135deg, rgba(30, 30, 30, 0.95) 0%, rgba(50, 50, 50, 0.95) 100%)' : 'linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%)',\n        backdropFilter: 'blur(20px)',\n        borderRadius: 4,\n        border: theme => `1px solid ${theme.palette.divider}`,\n        p: 6,\n        textAlign: 'center'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          background: 'linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%)',\n          borderRadius: '50%',\n          width: 80,\n          height: 80,\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          mx: 'auto',\n          mb: 3\n        },\n        children: /*#__PURE__*/_jsxDEV(SecurityIcon, {\n          sx: {\n            fontSize: 40,\n            color: 'primary.main'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 80,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 67,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h5\",\n        gutterBottom: true,\n        fontWeight: \"600\",\n        children: \"No scan results available\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 82,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body1\",\n        color: \"text.secondary\",\n        children: \"Start a scan to see detailed security analysis results here.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 85,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 55,\n      columnNumber: 7\n    }, this);\n  }\n  const handleAccordionChange = panel => (event, isExpanded) => {\n    setExpanded(isExpanded ? panel : false);\n  };\n  const handleDownloadClick = event => {\n    setDownloadAnchor(event.currentTarget);\n  };\n  const handleShareClick = event => {\n    setShareAnchor(event.currentTarget);\n  };\n  const handleDownloadClose = () => {\n    setDownloadAnchor(null);\n  };\n  const handleShareClose = () => {\n    setShareAnchor(null);\n  };\n  const handleDownloadPDF = async () => {\n    setIsDownloading(true);\n    handleDownloadClose();\n    try {\n      const result = await ReportService.downloadReport(scanResult);\n      if (result.success) {\n        setNotification({\n          type: 'success',\n          message: `Report downloaded successfully: ${result.filename}`\n        });\n      } else {\n        throw new Error(result.error);\n      }\n    } catch (error) {\n      setNotification({\n        type: 'error',\n        message: `Failed to download report: ${error.message}`\n      });\n    } finally {\n      setIsDownloading(false);\n    }\n  };\n  const handleShareLink = async () => {\n    setIsSharing(true);\n    handleShareClose();\n    try {\n      const result = await ReportService.shareReport(scanResult, 'link');\n      if (result.success) {\n        await navigator.clipboard.writeText(result.link);\n        setNotification({\n          type: 'success',\n          message: `Shareable link copied to clipboard! Expires in ${result.expiresIn}`\n        });\n      } else {\n        throw new Error(result.error);\n      }\n    } catch (error) {\n      setNotification({\n        type: 'error',\n        message: `Failed to generate share link: ${error.message}`\n      });\n    } finally {\n      setIsSharing(false);\n    }\n  };\n  const handleShareEmail = async () => {\n    handleShareClose();\n    try {\n      const result = await ReportService.shareReport(scanResult, 'email');\n      if (result.success) {\n        setNotification({\n          type: 'success',\n          message: 'Email client opened with report details'\n        });\n      }\n    } catch (error) {\n      setNotification({\n        type: 'error',\n        message: `Failed to share via email: ${error.message}`\n      });\n    }\n  };\n  const handleCopyReport = async () => {\n    handleShareClose();\n    try {\n      const result = await ReportService.shareReport(scanResult, 'copy');\n      if (result.success) {\n        setNotification({\n          type: 'success',\n          message: 'Report details copied to clipboard'\n        });\n      }\n    } catch (error) {\n      setNotification({\n        type: 'error',\n        message: `Failed to copy report: ${error.message}`\n      });\n    }\n  };\n  const getSeverityIcon = severity => {\n    switch (severity === null || severity === void 0 ? void 0 : severity.toLowerCase()) {\n      case 'critical':\n      case 'high':\n        return /*#__PURE__*/_jsxDEV(ErrorIcon, {\n          color: \"error\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 203,\n          columnNumber: 16\n        }, this);\n      case 'medium':\n        return /*#__PURE__*/_jsxDEV(WarningIcon, {\n          color: \"warning\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 205,\n          columnNumber: 16\n        }, this);\n      case 'low':\n        return /*#__PURE__*/_jsxDEV(InfoIcon, {\n          color: \"info\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 207,\n          columnNumber: 16\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(CheckCircleIcon, {\n          color: \"success\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 209,\n          columnNumber: 16\n        }, this);\n    }\n  };\n  const getSeverityColor = severity => {\n    switch (severity === null || severity === void 0 ? void 0 : severity.toLowerCase()) {\n      case 'critical':\n        return 'error';\n      case 'high':\n        return 'error';\n      case 'medium':\n        return 'warning';\n      case 'low':\n        return 'info';\n      default:\n        return 'success';\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      background: theme => theme.palette.mode === 'dark' ? 'linear-gradient(135deg, rgba(30, 30, 30, 0.95) 0%, rgba(50, 50, 50, 0.95) 100%)' : 'linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%)',\n      backdropFilter: 'blur(20px)',\n      borderRadius: 4,\n      border: theme => `1px solid ${theme.palette.divider}`,\n      p: 4,\n      position: 'relative',\n      overflow: 'hidden',\n      '&::before': {\n        content: '\"\"',\n        position: 'absolute',\n        top: 0,\n        left: 0,\n        right: 0,\n        height: '4px',\n        background: scanResult.isSafe ? 'linear-gradient(90deg, #4caf50 0%, #8bc34a 100%)' : 'linear-gradient(90deg, #f44336 0%, #ff9800 100%)'\n      }\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      display: \"flex\",\n      justifyContent: \"space-between\",\n      alignItems: \"center\",\n      mb: 4,\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        display: \"flex\",\n        alignItems: \"center\",\n        gap: 2,\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            background: scanResult.isSafe ? 'linear-gradient(135deg, rgba(76, 175, 80, 0.1) 0%, rgba(139, 195, 74, 0.1) 100%)' : 'linear-gradient(135deg, rgba(244, 67, 54, 0.1) 0%, rgba(255, 152, 0, 0.1) 100%)',\n            borderRadius: '50%',\n            width: 50,\n            height: 50,\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center'\n          },\n          children: scanResult.isSafe ? /*#__PURE__*/_jsxDEV(CheckCircleIcon, {\n            sx: {\n              color: 'success.main',\n              fontSize: 28\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 270,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(WarningIcon, {\n            sx: {\n              color: 'error.main',\n              fontSize: 28\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 272,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 256,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h4\",\n            component: \"h2\",\n            fontWeight: \"bold\",\n            gutterBottom: true,\n            children: \"Security Analysis Results\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 276,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            color: \"text.secondary\",\n            children: \"Comprehensive threat assessment and recommendations\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 279,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 275,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 255,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        display: \"flex\",\n        gap: 2,\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          startIcon: isDownloading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n            size: 16,\n            color: \"inherit\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 288,\n            columnNumber: 40\n          }, this) : /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 288,\n            columnNumber: 89\n          }, this),\n          onClick: handleDownloadClick,\n          disabled: isDownloading,\n          sx: {\n            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n            '&:hover': {\n              background: 'linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)'\n            }\n          },\n          children: isDownloading ? 'Generating...' : 'Download'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 286,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outlined\",\n          startIcon: isSharing ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n            size: 16\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 302,\n            columnNumber: 36\n          }, this) : /*#__PURE__*/_jsxDEV(ShareIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 302,\n            columnNumber: 69\n          }, this),\n          onClick: handleShareClick,\n          disabled: isSharing,\n          children: \"Share\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 300,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 285,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 254,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      mb: 4,\n      children: /*#__PURE__*/_jsxDEV(ThreatMeter, {\n        threatData: scanResult\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 313,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 312,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Accordion, {\n      expanded: expanded === 'summary',\n      onChange: handleAccordionChange('summary'),\n      children: [/*#__PURE__*/_jsxDEV(AccordionSummary, {\n        expandIcon: /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 321,\n          columnNumber: 41\n        }, this),\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          children: \"Summary\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 322,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 321,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(AccordionDetails, {\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Alert, {\n            severity: scanResult.isSafe ? 'success' : 'warning',\n            sx: {\n              mb: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              fontWeight: \"bold\",\n              children: scanResult.isSafe ? 'No threats detected' : 'Security issues found'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 330,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: scanResult.summary || 'Scan completed successfully'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 333,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 326,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            display: \"flex\",\n            gap: 1,\n            flexWrap: \"wrap\",\n            mb: 2,\n            children: [/*#__PURE__*/_jsxDEV(Chip, {\n              label: `Target: ${scanResult.target || 'Unknown'}`,\n              variant: \"outlined\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 339,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Chip, {\n              label: `Type: ${scanResult.type || 'Unknown'}`,\n              variant: \"outlined\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 343,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Chip, {\n              label: `Duration: ${scanResult.duration || 'N/A'}`,\n              variant: \"outlined\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 347,\n              columnNumber: 17\n            }, this), scanResult.timestamp && /*#__PURE__*/_jsxDEV(Chip, {\n              label: `Scanned: ${new Date(scanResult.timestamp).toLocaleString()}`,\n              variant: \"outlined\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 352,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 338,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 325,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 324,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 317,\n      columnNumber: 9\n    }, this), scanResult.threats && scanResult.threats.length > 0 && /*#__PURE__*/_jsxDEV(Accordion, {\n      expanded: expanded === 'threats',\n      onChange: handleAccordionChange('threats'),\n      children: [/*#__PURE__*/_jsxDEV(AccordionSummary, {\n        expandIcon: /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 368,\n          columnNumber: 43\n        }, this),\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          children: [\"Threats Detected (\", scanResult.threats.length, \")\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 369,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 368,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(AccordionDetails, {\n        children: /*#__PURE__*/_jsxDEV(List, {\n          children: scanResult.threats.map((threat, index) => /*#__PURE__*/_jsxDEV(React.Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(ListItem, {\n              children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                children: getSeverityIcon(threat.severity)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 378,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                primary: /*#__PURE__*/_jsxDEV(Box, {\n                  display: \"flex\",\n                  alignItems: \"center\",\n                  gap: 1,\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"subtitle2\",\n                    children: threat.name || `Threat ${index + 1}`\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 384,\n                    columnNumber: 29\n                  }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                    label: threat.severity || 'Unknown',\n                    color: getSeverityColor(threat.severity),\n                    size: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 387,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 383,\n                  columnNumber: 27\n                }, this),\n                secondary: /*#__PURE__*/_jsxDEV(Box, {\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    color: \"text.secondary\",\n                    children: threat.description || 'No description available'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 396,\n                    columnNumber: 29\n                  }, this), threat.recommendation && /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    color: \"primary\",\n                    children: [\"Recommendation: \", threat.recommendation]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 400,\n                    columnNumber: 31\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 395,\n                  columnNumber: 27\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 381,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 377,\n              columnNumber: 21\n            }, this), index < scanResult.threats.length - 1 && /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 408,\n              columnNumber: 63\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 376,\n            columnNumber: 19\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 374,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 373,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 364,\n      columnNumber: 11\n    }, this), scanResult.vulnerabilities && scanResult.vulnerabilities.length > 0 && /*#__PURE__*/_jsxDEV(Accordion, {\n      expanded: expanded === 'vulnerabilities',\n      onChange: handleAccordionChange('vulnerabilities'),\n      children: [/*#__PURE__*/_jsxDEV(AccordionSummary, {\n        expandIcon: /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 422,\n          columnNumber: 43\n        }, this),\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          children: [\"Vulnerabilities (\", scanResult.vulnerabilities.length, \")\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 423,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 422,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(AccordionDetails, {\n        children: /*#__PURE__*/_jsxDEV(List, {\n          children: scanResult.vulnerabilities.map((vuln, index) => /*#__PURE__*/_jsxDEV(React.Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(ListItem, {\n              children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                children: getSeverityIcon(vuln.severity)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 432,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                primary: /*#__PURE__*/_jsxDEV(Box, {\n                  display: \"flex\",\n                  alignItems: \"center\",\n                  gap: 1,\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"subtitle2\",\n                    children: vuln.name || `Vulnerability ${index + 1}`\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 438,\n                    columnNumber: 29\n                  }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                    label: vuln.severity || 'Unknown',\n                    color: getSeverityColor(vuln.severity),\n                    size: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 441,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 437,\n                  columnNumber: 27\n                }, this),\n                secondary: /*#__PURE__*/_jsxDEV(Box, {\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    color: \"text.secondary\",\n                    children: vuln.description || 'No description available'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 450,\n                    columnNumber: 29\n                  }, this), vuln.cve && /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    color: \"error\",\n                    children: [\"CVE: \", vuln.cve]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 454,\n                    columnNumber: 31\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 449,\n                  columnNumber: 27\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 435,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 431,\n              columnNumber: 21\n            }, this), index < scanResult.vulnerabilities.length - 1 && /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 462,\n              columnNumber: 71\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 430,\n            columnNumber: 19\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 428,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 427,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 418,\n      columnNumber: 11\n    }, this), scanResult.details && scanResult.details.length > 0 && /*#__PURE__*/_jsxDEV(Accordion, {\n      expanded: expanded === 'details',\n      onChange: handleAccordionChange('details'),\n      children: [/*#__PURE__*/_jsxDEV(AccordionSummary, {\n        expandIcon: /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 476,\n          columnNumber: 43\n        }, this),\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          children: \"Technical Details\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 477,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 476,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(AccordionDetails, {\n        children: /*#__PURE__*/_jsxDEV(List, {\n          children: scanResult.details.map((detail, index) => /*#__PURE__*/_jsxDEV(React.Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(ListItem, {\n              children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                children: /*#__PURE__*/_jsxDEV(VisibilityIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 485,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 484,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                primary: detail.title || `Detail ${index + 1}`,\n                secondary: detail.description || detail.value || 'No details available'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 487,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 483,\n              columnNumber: 21\n            }, this), index < scanResult.details.length - 1 && /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 492,\n              columnNumber: 63\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 482,\n            columnNumber: 19\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 480,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 479,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 472,\n      columnNumber: 11\n    }, this), scanResult.recommendations && scanResult.recommendations.length > 0 && /*#__PURE__*/_jsxDEV(Accordion, {\n      expanded: expanded === 'recommendations',\n      onChange: handleAccordionChange('recommendations'),\n      children: [/*#__PURE__*/_jsxDEV(AccordionSummary, {\n        expandIcon: /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 506,\n          columnNumber: 43\n        }, this),\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          children: \"Recommendations\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 507,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 506,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(AccordionDetails, {\n        children: /*#__PURE__*/_jsxDEV(List, {\n          children: scanResult.recommendations.map((rec, index) => /*#__PURE__*/_jsxDEV(React.Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(ListItem, {\n              children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                children: /*#__PURE__*/_jsxDEV(CheckCircleIcon, {\n                  color: \"primary\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 515,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 514,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                primary: rec.title || rec,\n                secondary: rec.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 517,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 513,\n              columnNumber: 21\n            }, this), index < scanResult.recommendations.length - 1 && /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 522,\n              columnNumber: 71\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 512,\n            columnNumber: 19\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 510,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 509,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 502,\n      columnNumber: 11\n    }, this), /*#__PURE__*/_jsxDEV(Menu, {\n      anchorEl: downloadAnchor,\n      open: Boolean(downloadAnchor),\n      onClose: handleDownloadClose,\n      anchorOrigin: {\n        vertical: 'bottom',\n        horizontal: 'right'\n      },\n      transformOrigin: {\n        vertical: 'top',\n        horizontal: 'right'\n      },\n      PaperProps: {\n        sx: {\n          mt: 1,\n          borderRadius: 2,\n          minWidth: 200\n        }\n      },\n      children: /*#__PURE__*/_jsxDEV(MenuItem, {\n        onClick: handleDownloadPDF,\n        sx: {\n          gap: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(PdfIcon, {\n          color: \"error\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 552,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            children: \"PDF Report\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 554,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            color: \"text.secondary\",\n            children: \"Detailed analysis report\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 555,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 553,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 551,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 531,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Menu, {\n      anchorEl: shareAnchor,\n      open: Boolean(shareAnchor),\n      onClose: handleShareClose,\n      anchorOrigin: {\n        vertical: 'bottom',\n        horizontal: 'right'\n      },\n      transformOrigin: {\n        vertical: 'top',\n        horizontal: 'right'\n      },\n      PaperProps: {\n        sx: {\n          mt: 1,\n          borderRadius: 2,\n          minWidth: 220\n        }\n      },\n      children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n        onClick: handleShareLink,\n        sx: {\n          gap: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(LinkIcon, {\n          color: \"primary\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 584,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            children: \"Generate Link\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 586,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            color: \"text.secondary\",\n            children: \"Create shareable link\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 587,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 585,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 583,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n        onClick: handleShareEmail,\n        sx: {\n          gap: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(EmailIcon, {\n          color: \"info\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 593,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            children: \"Email Report\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 595,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            color: \"text.secondary\",\n            children: \"Send via email client\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 596,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 594,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 592,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n        onClick: handleCopyReport,\n        sx: {\n          gap: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(CopyIcon, {\n          color: \"success\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 602,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            children: \"Copy to Clipboard\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 604,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            color: \"text.secondary\",\n            children: \"Copy report summary\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 605,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 603,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 601,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 563,\n      columnNumber: 7\n    }, this), notification && /*#__PURE__*/_jsxDEV(Snackbar, {\n      open: true,\n      autoHideDuration: 4000,\n      onClose: () => setNotification(null),\n      anchorOrigin: {\n        vertical: 'bottom',\n        horizontal: 'center'\n      },\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        onClose: () => setNotification(null),\n        severity: notification.type,\n        variant: \"filled\",\n        sx: {\n          borderRadius: 2\n        },\n        children: notification.message\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 620,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 614,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 229,\n    columnNumber: 5\n  }, this);\n}, \"r4Q/2k03BlVHogNSBrMqLoReE1M=\", false, function () {\n  return [useScan];\n})), \"r4Q/2k03BlVHogNSBrMqLoReE1M=\", false, function () {\n  return [useScan];\n});\n_c2 = ResultView;\nResultView.displayName = 'ResultView';\nexport default ResultView;\nvar _c, _c2;\n$RefreshReg$(_c, \"ResultView$React.memo\");\n$RefreshReg$(_c2, \"ResultView\");", "map": {"version": 3, "names": ["React", "useState", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Typography", "Box", "Accordion", "AccordionSummary", "AccordionDetails", "Chip", "List", "ListItem", "ListItemIcon", "ListItemText", "<PERSON><PERSON>", "Divider", "<PERSON><PERSON>", "IconButton", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "MenuItem", "CircularProgress", "Snackbar", "ExpandMore", "ExpandMoreIcon", "Security", "SecurityIcon", "Warning", "WarningIcon", "Error", "ErrorIcon", "CheckCircle", "CheckCircleIcon", "Info", "InfoIcon", "Download", "DownloadIcon", "Share", "ShareIcon", "Visibility", "VisibilityIcon", "Email", "EmailIcon", "Link", "LinkIcon", "ContentCopy", "CopyIcon", "PictureAsPdf", "PdfIcon", "useScan", "ThreatMeter", "ReportService", "jsxDEV", "_jsxDEV", "ResultView", "_s", "memo", "_c", "scanResult", "onDownloadReport", "onShareResult", "expanded", "setExpanded", "downloadAnchor", "setDownloadAnchor", "shareAnchor", "setShareAnchor", "isDownloading", "setIsDownloading", "isSharing", "setIsSharing", "notification", "setNotification", "addNotification", "sx", "background", "theme", "palette", "mode", "<PERSON><PERSON>ilter", "borderRadius", "border", "divider", "p", "textAlign", "children", "width", "height", "display", "alignItems", "justifyContent", "mx", "mb", "fontSize", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "gutterBottom", "fontWeight", "handleAccordionChange", "panel", "event", "isExpanded", "handleDownloadClick", "currentTarget", "handleShareClick", "handleDownloadClose", "handleShareClose", "handleDownloadPDF", "result", "downloadReport", "success", "type", "message", "filename", "error", "handleShareLink", "shareReport", "navigator", "clipboard", "writeText", "link", "expiresIn", "handleShareEmail", "handleCopyReport", "getSeverityIcon", "severity", "toLowerCase", "getSeverityColor", "position", "overflow", "content", "top", "left", "right", "isSafe", "gap", "component", "startIcon", "size", "onClick", "disabled", "threatData", "onChange", "expandIcon", "summary", "flexWrap", "label", "target", "duration", "timestamp", "Date", "toLocaleString", "threats", "length", "map", "threat", "index", "Fragment", "primary", "name", "secondary", "description", "recommendation", "vulnerabilities", "vuln", "cve", "details", "detail", "title", "value", "recommendations", "rec", "anchorEl", "open", "Boolean", "onClose", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "transform<PERSON><PERSON>in", "PaperProps", "mt", "min<PERSON><PERSON><PERSON>", "autoHideDuration", "_c2", "displayName", "$RefreshReg$"], "sources": ["E:/PythonProjects/React.js/Smart AI Assisstant/src/components/ResultView.jsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  Card,\n  Card<PERSON>ontent,\n  Typography,\n  Box,\n  Accordion,\n  AccordionSummary,\n  AccordionDetails,\n  Chip,\n  List,\n  ListItem,\n  ListItemIcon,\n  ListItemText,\n  Button,\n  Divider,\n  Alert,\n  IconButton,\n  Tooltip,\n  Menu,\n  MenuItem,\n  CircularProgress,\n  Snackbar,\n} from '@mui/material';\nimport {\n  ExpandMore as ExpandMoreIcon,\n  Security as SecurityIcon,\n  Warning as WarningIcon,\n  Error as ErrorIcon,\n  CheckCircle as CheckCircleIcon,\n  Info as InfoIcon,\n  Download as DownloadIcon,\n  Share as ShareIcon,\n  Visibility as VisibilityIcon,\n  Email as EmailIcon,\n  Link as LinkIcon,\n  ContentCopy as CopyIcon,\n  PictureAsPdf as PdfIcon,\n} from '@mui/icons-material';\nimport { useScan } from '../contexts/ScanContext';\nimport ThreatMeter from './ThreatMeter';\nimport { ReportService } from '../services/reportService';\n\nconst ResultView = React.memo(({ scanResult, onDownloadReport, onShareResult }) => {\n  const [expanded, setExpanded] = useState('summary');\n  const [downloadAnchor, setDownloadAnchor] = useState(null);\n  const [shareAnchor, setShareAnchor] = useState(null);\n  const [isDownloading, setIsDownloading] = useState(false);\n  const [isSharing, setIsSharing] = useState(false);\n  const [notification, setNotification] = useState(null);\n  const { addNotification } = useScan();\n\n  if (!scanResult) {\n    return (\n      <Box\n        sx={{\n          background: (theme) => theme.palette.mode === 'dark'\n            ? 'linear-gradient(135deg, rgba(30, 30, 30, 0.95) 0%, rgba(50, 50, 50, 0.95) 100%)'\n            : 'linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%)',\n          backdropFilter: 'blur(20px)',\n          borderRadius: 4,\n          border: (theme) => `1px solid ${theme.palette.divider}`,\n          p: 6,\n          textAlign: 'center',\n        }}\n      >\n        <Box\n          sx={{\n            background: 'linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%)',\n            borderRadius: '50%',\n            width: 80,\n            height: 80,\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            mx: 'auto',\n            mb: 3,\n          }}\n        >\n          <SecurityIcon sx={{ fontSize: 40, color: 'primary.main' }} />\n        </Box>\n        <Typography variant=\"h5\" gutterBottom fontWeight=\"600\">\n          No scan results available\n        </Typography>\n        <Typography variant=\"body1\" color=\"text.secondary\">\n          Start a scan to see detailed security analysis results here.\n        </Typography>\n      </Box>\n    );\n  }\n\n  const handleAccordionChange = (panel) => (event, isExpanded) => {\n    setExpanded(isExpanded ? panel : false);\n  };\n\n  const handleDownloadClick = (event) => {\n    setDownloadAnchor(event.currentTarget);\n  };\n\n  const handleShareClick = (event) => {\n    setShareAnchor(event.currentTarget);\n  };\n\n  const handleDownloadClose = () => {\n    setDownloadAnchor(null);\n  };\n\n  const handleShareClose = () => {\n    setShareAnchor(null);\n  };\n\n  const handleDownloadPDF = async () => {\n    setIsDownloading(true);\n    handleDownloadClose();\n\n    try {\n      const result = await ReportService.downloadReport(scanResult);\n      if (result.success) {\n        setNotification({\n          type: 'success',\n          message: `Report downloaded successfully: ${result.filename}`,\n        });\n      } else {\n        throw new Error(result.error);\n      }\n    } catch (error) {\n      setNotification({\n        type: 'error',\n        message: `Failed to download report: ${error.message}`,\n      });\n    } finally {\n      setIsDownloading(false);\n    }\n  };\n\n  const handleShareLink = async () => {\n    setIsSharing(true);\n    handleShareClose();\n\n    try {\n      const result = await ReportService.shareReport(scanResult, 'link');\n      if (result.success) {\n        await navigator.clipboard.writeText(result.link);\n        setNotification({\n          type: 'success',\n          message: `Shareable link copied to clipboard! Expires in ${result.expiresIn}`,\n        });\n      } else {\n        throw new Error(result.error);\n      }\n    } catch (error) {\n      setNotification({\n        type: 'error',\n        message: `Failed to generate share link: ${error.message}`,\n      });\n    } finally {\n      setIsSharing(false);\n    }\n  };\n\n  const handleShareEmail = async () => {\n    handleShareClose();\n\n    try {\n      const result = await ReportService.shareReport(scanResult, 'email');\n      if (result.success) {\n        setNotification({\n          type: 'success',\n          message: 'Email client opened with report details',\n        });\n      }\n    } catch (error) {\n      setNotification({\n        type: 'error',\n        message: `Failed to share via email: ${error.message}`,\n      });\n    }\n  };\n\n  const handleCopyReport = async () => {\n    handleShareClose();\n\n    try {\n      const result = await ReportService.shareReport(scanResult, 'copy');\n      if (result.success) {\n        setNotification({\n          type: 'success',\n          message: 'Report details copied to clipboard',\n        });\n      }\n    } catch (error) {\n      setNotification({\n        type: 'error',\n        message: `Failed to copy report: ${error.message}`,\n      });\n    }\n  };\n\n  const getSeverityIcon = (severity) => {\n    switch (severity?.toLowerCase()) {\n      case 'critical':\n      case 'high':\n        return <ErrorIcon color=\"error\" />;\n      case 'medium':\n        return <WarningIcon color=\"warning\" />;\n      case 'low':\n        return <InfoIcon color=\"info\" />;\n      default:\n        return <CheckCircleIcon color=\"success\" />;\n    }\n  };\n\n  const getSeverityColor = (severity) => {\n    switch (severity?.toLowerCase()) {\n      case 'critical':\n        return 'error';\n      case 'high':\n        return 'error';\n      case 'medium':\n        return 'warning';\n      case 'low':\n        return 'info';\n      default:\n        return 'success';\n    }\n  };\n\n  return (\n    <Box\n      sx={{\n        background: (theme) => theme.palette.mode === 'dark'\n          ? 'linear-gradient(135deg, rgba(30, 30, 30, 0.95) 0%, rgba(50, 50, 50, 0.95) 100%)'\n          : 'linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%)',\n        backdropFilter: 'blur(20px)',\n        borderRadius: 4,\n        border: (theme) => `1px solid ${theme.palette.divider}`,\n        p: 4,\n        position: 'relative',\n        overflow: 'hidden',\n        '&::before': {\n          content: '\"\"',\n          position: 'absolute',\n          top: 0,\n          left: 0,\n          right: 0,\n          height: '4px',\n          background: scanResult.isSafe\n            ? 'linear-gradient(90deg, #4caf50 0%, #8bc34a 100%)'\n            : 'linear-gradient(90deg, #f44336 0%, #ff9800 100%)',\n        },\n      }}\n    >\n      {/* Header */}\n      <Box display=\"flex\" justifyContent=\"space-between\" alignItems=\"center\" mb={4}>\n        <Box display=\"flex\" alignItems=\"center\" gap={2}>\n          <Box\n            sx={{\n              background: scanResult.isSafe\n                ? 'linear-gradient(135deg, rgba(76, 175, 80, 0.1) 0%, rgba(139, 195, 74, 0.1) 100%)'\n                : 'linear-gradient(135deg, rgba(244, 67, 54, 0.1) 0%, rgba(255, 152, 0, 0.1) 100%)',\n              borderRadius: '50%',\n              width: 50,\n              height: 50,\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n            }}\n          >\n            {scanResult.isSafe ? (\n              <CheckCircleIcon sx={{ color: 'success.main', fontSize: 28 }} />\n            ) : (\n              <WarningIcon sx={{ color: 'error.main', fontSize: 28 }} />\n            )}\n          </Box>\n          <Box>\n            <Typography variant=\"h4\" component=\"h2\" fontWeight=\"bold\" gutterBottom>\n              Security Analysis Results\n            </Typography>\n            <Typography variant=\"body1\" color=\"text.secondary\">\n              Comprehensive threat assessment and recommendations\n            </Typography>\n          </Box>\n        </Box>\n\n        <Box display=\"flex\" gap={2}>\n          <Button\n            variant=\"contained\"\n            startIcon={isDownloading ? <CircularProgress size={16} color=\"inherit\" /> : <DownloadIcon />}\n            onClick={handleDownloadClick}\n            disabled={isDownloading}\n            sx={{\n              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n              '&:hover': {\n                background: 'linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)',\n              },\n            }}\n          >\n            {isDownloading ? 'Generating...' : 'Download'}\n          </Button>\n          <Button\n            variant=\"outlined\"\n            startIcon={isSharing ? <CircularProgress size={16} /> : <ShareIcon />}\n            onClick={handleShareClick}\n            disabled={isSharing}\n          >\n            Share\n          </Button>\n        </Box>\n      </Box>\n\n      {/* Threat Meter */}\n      <Box mb={4}>\n        <ThreatMeter threatData={scanResult} />\n      </Box>\n\n        {/* Summary Accordion */}\n        <Accordion\n          expanded={expanded === 'summary'}\n          onChange={handleAccordionChange('summary')}\n        >\n          <AccordionSummary expandIcon={<ExpandMoreIcon />}>\n            <Typography variant=\"h6\">Summary</Typography>\n          </AccordionSummary>\n          <AccordionDetails>\n            <Box>\n              <Alert\n                severity={scanResult.isSafe ? 'success' : 'warning'}\n                sx={{ mb: 2 }}\n              >\n                <Typography variant=\"subtitle1\" fontWeight=\"bold\">\n                  {scanResult.isSafe ? 'No threats detected' : 'Security issues found'}\n                </Typography>\n                <Typography variant=\"body2\">\n                  {scanResult.summary || 'Scan completed successfully'}\n                </Typography>\n              </Alert>\n\n              <Box display=\"flex\" gap={1} flexWrap=\"wrap\" mb={2}>\n                <Chip\n                  label={`Target: ${scanResult.target || 'Unknown'}`}\n                  variant=\"outlined\"\n                />\n                <Chip\n                  label={`Type: ${scanResult.type || 'Unknown'}`}\n                  variant=\"outlined\"\n                />\n                <Chip\n                  label={`Duration: ${scanResult.duration || 'N/A'}`}\n                  variant=\"outlined\"\n                />\n                {scanResult.timestamp && (\n                  <Chip\n                    label={`Scanned: ${new Date(scanResult.timestamp).toLocaleString()}`}\n                    variant=\"outlined\"\n                  />\n                )}\n              </Box>\n            </Box>\n          </AccordionDetails>\n        </Accordion>\n\n        {/* Threats Accordion */}\n        {scanResult.threats && scanResult.threats.length > 0 && (\n          <Accordion\n            expanded={expanded === 'threats'}\n            onChange={handleAccordionChange('threats')}\n          >\n            <AccordionSummary expandIcon={<ExpandMoreIcon />}>\n              <Typography variant=\"h6\">\n                Threats Detected ({scanResult.threats.length})\n              </Typography>\n            </AccordionSummary>\n            <AccordionDetails>\n              <List>\n                {scanResult.threats.map((threat, index) => (\n                  <React.Fragment key={index}>\n                    <ListItem>\n                      <ListItemIcon>\n                        {getSeverityIcon(threat.severity)}\n                      </ListItemIcon>\n                      <ListItemText\n                        primary={\n                          <Box display=\"flex\" alignItems=\"center\" gap={1}>\n                            <Typography variant=\"subtitle2\">\n                              {threat.name || `Threat ${index + 1}`}\n                            </Typography>\n                            <Chip\n                              label={threat.severity || 'Unknown'}\n                              color={getSeverityColor(threat.severity)}\n                              size=\"small\"\n                            />\n                          </Box>\n                        }\n                        secondary={\n                          <Box>\n                            <Typography variant=\"body2\" color=\"text.secondary\">\n                              {threat.description || 'No description available'}\n                            </Typography>\n                            {threat.recommendation && (\n                              <Typography variant=\"caption\" color=\"primary\">\n                                Recommendation: {threat.recommendation}\n                              </Typography>\n                            )}\n                          </Box>\n                        }\n                      />\n                    </ListItem>\n                    {index < scanResult.threats.length - 1 && <Divider />}\n                  </React.Fragment>\n                ))}\n              </List>\n            </AccordionDetails>\n          </Accordion>\n        )}\n\n        {/* Vulnerabilities Accordion */}\n        {scanResult.vulnerabilities && scanResult.vulnerabilities.length > 0 && (\n          <Accordion\n            expanded={expanded === 'vulnerabilities'}\n            onChange={handleAccordionChange('vulnerabilities')}\n          >\n            <AccordionSummary expandIcon={<ExpandMoreIcon />}>\n              <Typography variant=\"h6\">\n                Vulnerabilities ({scanResult.vulnerabilities.length})\n              </Typography>\n            </AccordionSummary>\n            <AccordionDetails>\n              <List>\n                {scanResult.vulnerabilities.map((vuln, index) => (\n                  <React.Fragment key={index}>\n                    <ListItem>\n                      <ListItemIcon>\n                        {getSeverityIcon(vuln.severity)}\n                      </ListItemIcon>\n                      <ListItemText\n                        primary={\n                          <Box display=\"flex\" alignItems=\"center\" gap={1}>\n                            <Typography variant=\"subtitle2\">\n                              {vuln.name || `Vulnerability ${index + 1}`}\n                            </Typography>\n                            <Chip\n                              label={vuln.severity || 'Unknown'}\n                              color={getSeverityColor(vuln.severity)}\n                              size=\"small\"\n                            />\n                          </Box>\n                        }\n                        secondary={\n                          <Box>\n                            <Typography variant=\"body2\" color=\"text.secondary\">\n                              {vuln.description || 'No description available'}\n                            </Typography>\n                            {vuln.cve && (\n                              <Typography variant=\"caption\" color=\"error\">\n                                CVE: {vuln.cve}\n                              </Typography>\n                            )}\n                          </Box>\n                        }\n                      />\n                    </ListItem>\n                    {index < scanResult.vulnerabilities.length - 1 && <Divider />}\n                  </React.Fragment>\n                ))}\n              </List>\n            </AccordionDetails>\n          </Accordion>\n        )}\n\n        {/* Details Accordion */}\n        {scanResult.details && scanResult.details.length > 0 && (\n          <Accordion\n            expanded={expanded === 'details'}\n            onChange={handleAccordionChange('details')}\n          >\n            <AccordionSummary expandIcon={<ExpandMoreIcon />}>\n              <Typography variant=\"h6\">Technical Details</Typography>\n            </AccordionSummary>\n            <AccordionDetails>\n              <List>\n                {scanResult.details.map((detail, index) => (\n                  <React.Fragment key={index}>\n                    <ListItem>\n                      <ListItemIcon>\n                        <VisibilityIcon />\n                      </ListItemIcon>\n                      <ListItemText\n                        primary={detail.title || `Detail ${index + 1}`}\n                        secondary={detail.description || detail.value || 'No details available'}\n                      />\n                    </ListItem>\n                    {index < scanResult.details.length - 1 && <Divider />}\n                  </React.Fragment>\n                ))}\n              </List>\n            </AccordionDetails>\n          </Accordion>\n        )}\n\n        {/* Recommendations Accordion */}\n        {scanResult.recommendations && scanResult.recommendations.length > 0 && (\n          <Accordion\n            expanded={expanded === 'recommendations'}\n            onChange={handleAccordionChange('recommendations')}\n          >\n            <AccordionSummary expandIcon={<ExpandMoreIcon />}>\n              <Typography variant=\"h6\">Recommendations</Typography>\n            </AccordionSummary>\n            <AccordionDetails>\n              <List>\n                {scanResult.recommendations.map((rec, index) => (\n                  <React.Fragment key={index}>\n                    <ListItem>\n                      <ListItemIcon>\n                        <CheckCircleIcon color=\"primary\" />\n                      </ListItemIcon>\n                      <ListItemText\n                        primary={rec.title || rec}\n                        secondary={rec.description}\n                      />\n                    </ListItem>\n                    {index < scanResult.recommendations.length - 1 && <Divider />}\n                  </React.Fragment>\n                ))}\n              </List>\n            </AccordionDetails>\n          </Accordion>\n        )}\n\n      {/* Download Menu */}\n      <Menu\n        anchorEl={downloadAnchor}\n        open={Boolean(downloadAnchor)}\n        onClose={handleDownloadClose}\n        anchorOrigin={{\n          vertical: 'bottom',\n          horizontal: 'right',\n        }}\n        transformOrigin={{\n          vertical: 'top',\n          horizontal: 'right',\n        }}\n        PaperProps={{\n          sx: {\n            mt: 1,\n            borderRadius: 2,\n            minWidth: 200,\n          },\n        }}\n      >\n        <MenuItem onClick={handleDownloadPDF} sx={{ gap: 2 }}>\n          <PdfIcon color=\"error\" />\n          <Box>\n            <Typography variant=\"subtitle2\">PDF Report</Typography>\n            <Typography variant=\"caption\" color=\"text.secondary\">\n              Detailed analysis report\n            </Typography>\n          </Box>\n        </MenuItem>\n      </Menu>\n\n      {/* Share Menu */}\n      <Menu\n        anchorEl={shareAnchor}\n        open={Boolean(shareAnchor)}\n        onClose={handleShareClose}\n        anchorOrigin={{\n          vertical: 'bottom',\n          horizontal: 'right',\n        }}\n        transformOrigin={{\n          vertical: 'top',\n          horizontal: 'right',\n        }}\n        PaperProps={{\n          sx: {\n            mt: 1,\n            borderRadius: 2,\n            minWidth: 220,\n          },\n        }}\n      >\n        <MenuItem onClick={handleShareLink} sx={{ gap: 2 }}>\n          <LinkIcon color=\"primary\" />\n          <Box>\n            <Typography variant=\"subtitle2\">Generate Link</Typography>\n            <Typography variant=\"caption\" color=\"text.secondary\">\n              Create shareable link\n            </Typography>\n          </Box>\n        </MenuItem>\n        <MenuItem onClick={handleShareEmail} sx={{ gap: 2 }}>\n          <EmailIcon color=\"info\" />\n          <Box>\n            <Typography variant=\"subtitle2\">Email Report</Typography>\n            <Typography variant=\"caption\" color=\"text.secondary\">\n              Send via email client\n            </Typography>\n          </Box>\n        </MenuItem>\n        <MenuItem onClick={handleCopyReport} sx={{ gap: 2 }}>\n          <CopyIcon color=\"success\" />\n          <Box>\n            <Typography variant=\"subtitle2\">Copy to Clipboard</Typography>\n            <Typography variant=\"caption\" color=\"text.secondary\">\n              Copy report summary\n            </Typography>\n          </Box>\n        </MenuItem>\n      </Menu>\n\n      {/* Notification Snackbar */}\n      {notification && (\n        <Snackbar\n          open={true}\n          autoHideDuration={4000}\n          onClose={() => setNotification(null)}\n          anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}\n        >\n          <Alert\n            onClose={() => setNotification(null)}\n            severity={notification.type}\n            variant=\"filled\"\n            sx={{ borderRadius: 2 }}\n          >\n            {notification.message}\n          </Alert>\n        </Snackbar>\n      )}\n    </Box>\n  );\n});\n\nResultView.displayName = 'ResultView';\n\nexport default ResultView;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,GAAG,EACHC,SAAS,EACTC,gBAAgB,EAChBC,gBAAgB,EAChBC,IAAI,EACJC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,YAAY,EACZC,MAAM,EACNC,OAAO,EACPC,KAAK,EACLC,UAAU,EACVC,OAAO,EACPC,IAAI,EACJC,QAAQ,EACRC,gBAAgB,EAChBC,QAAQ,QACH,eAAe;AACtB,SACEC,UAAU,IAAIC,cAAc,EAC5BC,QAAQ,IAAIC,YAAY,EACxBC,OAAO,IAAIC,WAAW,EACtBC,KAAK,IAAIC,SAAS,EAClBC,WAAW,IAAIC,eAAe,EAC9BC,IAAI,IAAIC,QAAQ,EAChBC,QAAQ,IAAIC,YAAY,EACxBC,KAAK,IAAIC,SAAS,EAClBC,UAAU,IAAIC,cAAc,EAC5BC,KAAK,IAAIC,SAAS,EAClBC,IAAI,IAAIC,QAAQ,EAChBC,WAAW,IAAIC,QAAQ,EACvBC,YAAY,IAAIC,OAAO,QAClB,qBAAqB;AAC5B,SAASC,OAAO,QAAQ,yBAAyB;AACjD,OAAOC,WAAW,MAAM,eAAe;AACvC,SAASC,aAAa,QAAQ,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1D,MAAMC,UAAU,gBAAAC,EAAA,cAAGvD,KAAK,CAACwD,IAAI,CAAAC,EAAA,GAAAF,EAAA,CAAC,CAAC;EAAEG,UAAU;EAAEC,gBAAgB;EAAEC;AAAc,CAAC,KAAK;EAAAL,EAAA;EACjF,MAAM,CAACM,QAAQ,EAAEC,WAAW,CAAC,GAAG7D,QAAQ,CAAC,SAAS,CAAC;EACnD,MAAM,CAAC8D,cAAc,EAAEC,iBAAiB,CAAC,GAAG/D,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACgE,WAAW,EAAEC,cAAc,CAAC,GAAGjE,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACkE,aAAa,EAAEC,gBAAgB,CAAC,GAAGnE,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACoE,SAAS,EAAEC,YAAY,CAAC,GAAGrE,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACsE,YAAY,EAAEC,eAAe,CAAC,GAAGvE,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM;IAAEwE;EAAgB,CAAC,GAAGxB,OAAO,CAAC,CAAC;EAErC,IAAI,CAACS,UAAU,EAAE;IACf,oBACEL,OAAA,CAAChD,GAAG;MACFqE,EAAE,EAAE;QACFC,UAAU,EAAGC,KAAK,IAAKA,KAAK,CAACC,OAAO,CAACC,IAAI,KAAK,MAAM,GAChD,iFAAiF,GACjF,uFAAuF;QAC3FC,cAAc,EAAE,YAAY;QAC5BC,YAAY,EAAE,CAAC;QACfC,MAAM,EAAGL,KAAK,IAAK,aAAaA,KAAK,CAACC,OAAO,CAACK,OAAO,EAAE;QACvDC,CAAC,EAAE,CAAC;QACJC,SAAS,EAAE;MACb,CAAE;MAAAC,QAAA,gBAEFhC,OAAA,CAAChD,GAAG;QACFqE,EAAE,EAAE;UACFC,UAAU,EAAE,oFAAoF;UAChGK,YAAY,EAAE,KAAK;UACnBM,KAAK,EAAE,EAAE;UACTC,MAAM,EAAE,EAAE;UACVC,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBC,cAAc,EAAE,QAAQ;UACxBC,EAAE,EAAE,MAAM;UACVC,EAAE,EAAE;QACN,CAAE;QAAAP,QAAA,eAEFhC,OAAA,CAAC3B,YAAY;UAACgD,EAAE,EAAE;YAAEmB,QAAQ,EAAE,EAAE;YAAEC,KAAK,EAAE;UAAe;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1D,CAAC,eACN7C,OAAA,CAACjD,UAAU;QAAC+F,OAAO,EAAC,IAAI;QAACC,YAAY;QAACC,UAAU,EAAC,KAAK;QAAAhB,QAAA,EAAC;MAEvD;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACb7C,OAAA,CAACjD,UAAU;QAAC+F,OAAO,EAAC,OAAO;QAACL,KAAK,EAAC,gBAAgB;QAAAT,QAAA,EAAC;MAEnD;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAEV;EAEA,MAAMI,qBAAqB,GAAIC,KAAK,IAAK,CAACC,KAAK,EAAEC,UAAU,KAAK;IAC9D3C,WAAW,CAAC2C,UAAU,GAAGF,KAAK,GAAG,KAAK,CAAC;EACzC,CAAC;EAED,MAAMG,mBAAmB,GAAIF,KAAK,IAAK;IACrCxC,iBAAiB,CAACwC,KAAK,CAACG,aAAa,CAAC;EACxC,CAAC;EAED,MAAMC,gBAAgB,GAAIJ,KAAK,IAAK;IAClCtC,cAAc,CAACsC,KAAK,CAACG,aAAa,CAAC;EACrC,CAAC;EAED,MAAME,mBAAmB,GAAGA,CAAA,KAAM;IAChC7C,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;EAED,MAAM8C,gBAAgB,GAAGA,CAAA,KAAM;IAC7B5C,cAAc,CAAC,IAAI,CAAC;EACtB,CAAC;EAED,MAAM6C,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC3C,gBAAgB,CAAC,IAAI,CAAC;IACtByC,mBAAmB,CAAC,CAAC;IAErB,IAAI;MACF,MAAMG,MAAM,GAAG,MAAM7D,aAAa,CAAC8D,cAAc,CAACvD,UAAU,CAAC;MAC7D,IAAIsD,MAAM,CAACE,OAAO,EAAE;QAClB1C,eAAe,CAAC;UACd2C,IAAI,EAAE,SAAS;UACfC,OAAO,EAAE,mCAAmCJ,MAAM,CAACK,QAAQ;QAC7D,CAAC,CAAC;MACJ,CAAC,MAAM;QACL,MAAM,IAAIxF,KAAK,CAACmF,MAAM,CAACM,KAAK,CAAC;MAC/B;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACd9C,eAAe,CAAC;QACd2C,IAAI,EAAE,OAAO;QACbC,OAAO,EAAE,8BAA8BE,KAAK,CAACF,OAAO;MACtD,CAAC,CAAC;IACJ,CAAC,SAAS;MACRhD,gBAAgB,CAAC,KAAK,CAAC;IACzB;EACF,CAAC;EAED,MAAMmD,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClCjD,YAAY,CAAC,IAAI,CAAC;IAClBwC,gBAAgB,CAAC,CAAC;IAElB,IAAI;MACF,MAAME,MAAM,GAAG,MAAM7D,aAAa,CAACqE,WAAW,CAAC9D,UAAU,EAAE,MAAM,CAAC;MAClE,IAAIsD,MAAM,CAACE,OAAO,EAAE;QAClB,MAAMO,SAAS,CAACC,SAAS,CAACC,SAAS,CAACX,MAAM,CAACY,IAAI,CAAC;QAChDpD,eAAe,CAAC;UACd2C,IAAI,EAAE,SAAS;UACfC,OAAO,EAAE,kDAAkDJ,MAAM,CAACa,SAAS;QAC7E,CAAC,CAAC;MACJ,CAAC,MAAM;QACL,MAAM,IAAIhG,KAAK,CAACmF,MAAM,CAACM,KAAK,CAAC;MAC/B;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACd9C,eAAe,CAAC;QACd2C,IAAI,EAAE,OAAO;QACbC,OAAO,EAAE,kCAAkCE,KAAK,CAACF,OAAO;MAC1D,CAAC,CAAC;IACJ,CAAC,SAAS;MACR9C,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAMwD,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnChB,gBAAgB,CAAC,CAAC;IAElB,IAAI;MACF,MAAME,MAAM,GAAG,MAAM7D,aAAa,CAACqE,WAAW,CAAC9D,UAAU,EAAE,OAAO,CAAC;MACnE,IAAIsD,MAAM,CAACE,OAAO,EAAE;QAClB1C,eAAe,CAAC;UACd2C,IAAI,EAAE,SAAS;UACfC,OAAO,EAAE;QACX,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,OAAOE,KAAK,EAAE;MACd9C,eAAe,CAAC;QACd2C,IAAI,EAAE,OAAO;QACbC,OAAO,EAAE,8BAA8BE,KAAK,CAACF,OAAO;MACtD,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAMW,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnCjB,gBAAgB,CAAC,CAAC;IAElB,IAAI;MACF,MAAME,MAAM,GAAG,MAAM7D,aAAa,CAACqE,WAAW,CAAC9D,UAAU,EAAE,MAAM,CAAC;MAClE,IAAIsD,MAAM,CAACE,OAAO,EAAE;QAClB1C,eAAe,CAAC;UACd2C,IAAI,EAAE,SAAS;UACfC,OAAO,EAAE;QACX,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,OAAOE,KAAK,EAAE;MACd9C,eAAe,CAAC;QACd2C,IAAI,EAAE,OAAO;QACbC,OAAO,EAAE,0BAA0BE,KAAK,CAACF,OAAO;MAClD,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAMY,eAAe,GAAIC,QAAQ,IAAK;IACpC,QAAQA,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEC,WAAW,CAAC,CAAC;MAC7B,KAAK,UAAU;MACf,KAAK,MAAM;QACT,oBAAO7E,OAAA,CAACvB,SAAS;UAACgE,KAAK,EAAC;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACpC,KAAK,QAAQ;QACX,oBAAO7C,OAAA,CAACzB,WAAW;UAACkE,KAAK,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACxC,KAAK,KAAK;QACR,oBAAO7C,OAAA,CAACnB,QAAQ;UAAC4D,KAAK,EAAC;QAAM;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAClC;QACE,oBAAO7C,OAAA,CAACrB,eAAe;UAAC8D,KAAK,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;IAC9C;EACF,CAAC;EAED,MAAMiC,gBAAgB,GAAIF,QAAQ,IAAK;IACrC,QAAQA,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEC,WAAW,CAAC,CAAC;MAC7B,KAAK,UAAU;QACb,OAAO,OAAO;MAChB,KAAK,MAAM;QACT,OAAO,OAAO;MAChB,KAAK,QAAQ;QACX,OAAO,SAAS;MAClB,KAAK,KAAK;QACR,OAAO,MAAM;MACf;QACE,OAAO,SAAS;IACpB;EACF,CAAC;EAED,oBACE7E,OAAA,CAAChD,GAAG;IACFqE,EAAE,EAAE;MACFC,UAAU,EAAGC,KAAK,IAAKA,KAAK,CAACC,OAAO,CAACC,IAAI,KAAK,MAAM,GAChD,iFAAiF,GACjF,uFAAuF;MAC3FC,cAAc,EAAE,YAAY;MAC5BC,YAAY,EAAE,CAAC;MACfC,MAAM,EAAGL,KAAK,IAAK,aAAaA,KAAK,CAACC,OAAO,CAACK,OAAO,EAAE;MACvDC,CAAC,EAAE,CAAC;MACJiD,QAAQ,EAAE,UAAU;MACpBC,QAAQ,EAAE,QAAQ;MAClB,WAAW,EAAE;QACXC,OAAO,EAAE,IAAI;QACbF,QAAQ,EAAE,UAAU;QACpBG,GAAG,EAAE,CAAC;QACNC,IAAI,EAAE,CAAC;QACPC,KAAK,EAAE,CAAC;QACRlD,MAAM,EAAE,KAAK;QACbZ,UAAU,EAAEjB,UAAU,CAACgF,MAAM,GACzB,kDAAkD,GAClD;MACN;IACF,CAAE;IAAArD,QAAA,gBAGFhC,OAAA,CAAChD,GAAG;MAACmF,OAAO,EAAC,MAAM;MAACE,cAAc,EAAC,eAAe;MAACD,UAAU,EAAC,QAAQ;MAACG,EAAE,EAAE,CAAE;MAAAP,QAAA,gBAC3EhC,OAAA,CAAChD,GAAG;QAACmF,OAAO,EAAC,MAAM;QAACC,UAAU,EAAC,QAAQ;QAACkD,GAAG,EAAE,CAAE;QAAAtD,QAAA,gBAC7ChC,OAAA,CAAChD,GAAG;UACFqE,EAAE,EAAE;YACFC,UAAU,EAAEjB,UAAU,CAACgF,MAAM,GACzB,kFAAkF,GAClF,iFAAiF;YACrF1D,YAAY,EAAE,KAAK;YACnBM,KAAK,EAAE,EAAE;YACTC,MAAM,EAAE,EAAE;YACVC,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpBC,cAAc,EAAE;UAClB,CAAE;UAAAL,QAAA,EAED3B,UAAU,CAACgF,MAAM,gBAChBrF,OAAA,CAACrB,eAAe;YAAC0C,EAAE,EAAE;cAAEoB,KAAK,EAAE,cAAc;cAAED,QAAQ,EAAE;YAAG;UAAE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAEhE7C,OAAA,CAACzB,WAAW;YAAC8C,EAAE,EAAE;cAAEoB,KAAK,EAAE,YAAY;cAAED,QAAQ,EAAE;YAAG;UAAE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAC1D;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eACN7C,OAAA,CAAChD,GAAG;UAAAgF,QAAA,gBACFhC,OAAA,CAACjD,UAAU;YAAC+F,OAAO,EAAC,IAAI;YAACyC,SAAS,EAAC,IAAI;YAACvC,UAAU,EAAC,MAAM;YAACD,YAAY;YAAAf,QAAA,EAAC;UAEvE;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb7C,OAAA,CAACjD,UAAU;YAAC+F,OAAO,EAAC,OAAO;YAACL,KAAK,EAAC,gBAAgB;YAAAT,QAAA,EAAC;UAEnD;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN7C,OAAA,CAAChD,GAAG;QAACmF,OAAO,EAAC,MAAM;QAACmD,GAAG,EAAE,CAAE;QAAAtD,QAAA,gBACzBhC,OAAA,CAACvC,MAAM;UACLqF,OAAO,EAAC,WAAW;UACnB0C,SAAS,EAAE1E,aAAa,gBAAGd,OAAA,CAAChC,gBAAgB;YAACyH,IAAI,EAAE,EAAG;YAAChD,KAAK,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAAG7C,OAAA,CAACjB,YAAY;YAAA2D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC7F6C,OAAO,EAAErC,mBAAoB;UAC7BsC,QAAQ,EAAE7E,aAAc;UACxBO,EAAE,EAAE;YACFC,UAAU,EAAE,mDAAmD;YAC/D,SAAS,EAAE;cACTA,UAAU,EAAE;YACd;UACF,CAAE;UAAAU,QAAA,EAEDlB,aAAa,GAAG,eAAe,GAAG;QAAU;UAAA4B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CAAC,eACT7C,OAAA,CAACvC,MAAM;UACLqF,OAAO,EAAC,UAAU;UAClB0C,SAAS,EAAExE,SAAS,gBAAGhB,OAAA,CAAChC,gBAAgB;YAACyH,IAAI,EAAE;UAAG;YAAA/C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAAG7C,OAAA,CAACf,SAAS;YAAAyD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACtE6C,OAAO,EAAEnC,gBAAiB;UAC1BoC,QAAQ,EAAE3E,SAAU;UAAAgB,QAAA,EACrB;QAED;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN7C,OAAA,CAAChD,GAAG;MAACuF,EAAE,EAAE,CAAE;MAAAP,QAAA,eACThC,OAAA,CAACH,WAAW;QAAC+F,UAAU,EAAEvF;MAAW;QAAAqC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpC,CAAC,eAGJ7C,OAAA,CAAC/C,SAAS;MACRuD,QAAQ,EAAEA,QAAQ,KAAK,SAAU;MACjCqF,QAAQ,EAAE5C,qBAAqB,CAAC,SAAS,CAAE;MAAAjB,QAAA,gBAE3ChC,OAAA,CAAC9C,gBAAgB;QAAC4I,UAAU,eAAE9F,OAAA,CAAC7B,cAAc;UAAAuE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAAAb,QAAA,eAC/ChC,OAAA,CAACjD,UAAU;UAAC+F,OAAO,EAAC,IAAI;UAAAd,QAAA,EAAC;QAAO;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7B,CAAC,eACnB7C,OAAA,CAAC7C,gBAAgB;QAAA6E,QAAA,eACfhC,OAAA,CAAChD,GAAG;UAAAgF,QAAA,gBACFhC,OAAA,CAACrC,KAAK;YACJiH,QAAQ,EAAEvE,UAAU,CAACgF,MAAM,GAAG,SAAS,GAAG,SAAU;YACpDhE,EAAE,EAAE;cAAEkB,EAAE,EAAE;YAAE,CAAE;YAAAP,QAAA,gBAEdhC,OAAA,CAACjD,UAAU;cAAC+F,OAAO,EAAC,WAAW;cAACE,UAAU,EAAC,MAAM;cAAAhB,QAAA,EAC9C3B,UAAU,CAACgF,MAAM,GAAG,qBAAqB,GAAG;YAAuB;cAAA3C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1D,CAAC,eACb7C,OAAA,CAACjD,UAAU;cAAC+F,OAAO,EAAC,OAAO;cAAAd,QAAA,EACxB3B,UAAU,CAAC0F,OAAO,IAAI;YAA6B;cAAArD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,eAER7C,OAAA,CAAChD,GAAG;YAACmF,OAAO,EAAC,MAAM;YAACmD,GAAG,EAAE,CAAE;YAACU,QAAQ,EAAC,MAAM;YAACzD,EAAE,EAAE,CAAE;YAAAP,QAAA,gBAChDhC,OAAA,CAAC5C,IAAI;cACH6I,KAAK,EAAE,WAAW5F,UAAU,CAAC6F,MAAM,IAAI,SAAS,EAAG;cACnDpD,OAAO,EAAC;YAAU;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CAAC,eACF7C,OAAA,CAAC5C,IAAI;cACH6I,KAAK,EAAE,SAAS5F,UAAU,CAACyD,IAAI,IAAI,SAAS,EAAG;cAC/ChB,OAAO,EAAC;YAAU;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CAAC,eACF7C,OAAA,CAAC5C,IAAI;cACH6I,KAAK,EAAE,aAAa5F,UAAU,CAAC8F,QAAQ,IAAI,KAAK,EAAG;cACnDrD,OAAO,EAAC;YAAU;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CAAC,EACDxC,UAAU,CAAC+F,SAAS,iBACnBpG,OAAA,CAAC5C,IAAI;cACH6I,KAAK,EAAE,YAAY,IAAII,IAAI,CAAChG,UAAU,CAAC+F,SAAS,CAAC,CAACE,cAAc,CAAC,CAAC,EAAG;cACrExD,OAAO,EAAC;YAAU;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CACF;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACU,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,EAGXxC,UAAU,CAACkG,OAAO,IAAIlG,UAAU,CAACkG,OAAO,CAACC,MAAM,GAAG,CAAC,iBAClDxG,OAAA,CAAC/C,SAAS;MACRuD,QAAQ,EAAEA,QAAQ,KAAK,SAAU;MACjCqF,QAAQ,EAAE5C,qBAAqB,CAAC,SAAS,CAAE;MAAAjB,QAAA,gBAE3ChC,OAAA,CAAC9C,gBAAgB;QAAC4I,UAAU,eAAE9F,OAAA,CAAC7B,cAAc;UAAAuE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAAAb,QAAA,eAC/ChC,OAAA,CAACjD,UAAU;UAAC+F,OAAO,EAAC,IAAI;UAAAd,QAAA,GAAC,oBACL,EAAC3B,UAAU,CAACkG,OAAO,CAACC,MAAM,EAAC,GAC/C;QAAA;UAAA9D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eACnB7C,OAAA,CAAC7C,gBAAgB;QAAA6E,QAAA,eACfhC,OAAA,CAAC3C,IAAI;UAAA2E,QAAA,EACF3B,UAAU,CAACkG,OAAO,CAACE,GAAG,CAAC,CAACC,MAAM,EAAEC,KAAK,kBACpC3G,OAAA,CAACrD,KAAK,CAACiK,QAAQ;YAAA5E,QAAA,gBACbhC,OAAA,CAAC1C,QAAQ;cAAA0E,QAAA,gBACPhC,OAAA,CAACzC,YAAY;gBAAAyE,QAAA,EACV2C,eAAe,CAAC+B,MAAM,CAAC9B,QAAQ;cAAC;gBAAAlC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrB,CAAC,eACf7C,OAAA,CAACxC,YAAY;gBACXqJ,OAAO,eACL7G,OAAA,CAAChD,GAAG;kBAACmF,OAAO,EAAC,MAAM;kBAACC,UAAU,EAAC,QAAQ;kBAACkD,GAAG,EAAE,CAAE;kBAAAtD,QAAA,gBAC7ChC,OAAA,CAACjD,UAAU;oBAAC+F,OAAO,EAAC,WAAW;oBAAAd,QAAA,EAC5B0E,MAAM,CAACI,IAAI,IAAI,UAAUH,KAAK,GAAG,CAAC;kBAAE;oBAAAjE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3B,CAAC,eACb7C,OAAA,CAAC5C,IAAI;oBACH6I,KAAK,EAAES,MAAM,CAAC9B,QAAQ,IAAI,SAAU;oBACpCnC,KAAK,EAAEqC,gBAAgB,CAAC4B,MAAM,CAAC9B,QAAQ,CAAE;oBACzCa,IAAI,EAAC;kBAAO;oBAAA/C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACb,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CACN;gBACDkE,SAAS,eACP/G,OAAA,CAAChD,GAAG;kBAAAgF,QAAA,gBACFhC,OAAA,CAACjD,UAAU;oBAAC+F,OAAO,EAAC,OAAO;oBAACL,KAAK,EAAC,gBAAgB;oBAAAT,QAAA,EAC/C0E,MAAM,CAACM,WAAW,IAAI;kBAA0B;oBAAAtE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvC,CAAC,EACZ6D,MAAM,CAACO,cAAc,iBACpBjH,OAAA,CAACjD,UAAU;oBAAC+F,OAAO,EAAC,SAAS;oBAACL,KAAK,EAAC,SAAS;oBAAAT,QAAA,GAAC,kBAC5B,EAAC0E,MAAM,CAACO,cAAc;kBAAA;oBAAAvE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5B,CACb;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE;cACN;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CAAC,EACV8D,KAAK,GAAGtG,UAAU,CAACkG,OAAO,CAACC,MAAM,GAAG,CAAC,iBAAIxG,OAAA,CAACtC,OAAO;cAAAgF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA,GAhClC8D,KAAK;YAAAjE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAiCV,CACjB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CACZ,EAGAxC,UAAU,CAAC6G,eAAe,IAAI7G,UAAU,CAAC6G,eAAe,CAACV,MAAM,GAAG,CAAC,iBAClExG,OAAA,CAAC/C,SAAS;MACRuD,QAAQ,EAAEA,QAAQ,KAAK,iBAAkB;MACzCqF,QAAQ,EAAE5C,qBAAqB,CAAC,iBAAiB,CAAE;MAAAjB,QAAA,gBAEnDhC,OAAA,CAAC9C,gBAAgB;QAAC4I,UAAU,eAAE9F,OAAA,CAAC7B,cAAc;UAAAuE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAAAb,QAAA,eAC/ChC,OAAA,CAACjD,UAAU;UAAC+F,OAAO,EAAC,IAAI;UAAAd,QAAA,GAAC,mBACN,EAAC3B,UAAU,CAAC6G,eAAe,CAACV,MAAM,EAAC,GACtD;QAAA;UAAA9D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eACnB7C,OAAA,CAAC7C,gBAAgB;QAAA6E,QAAA,eACfhC,OAAA,CAAC3C,IAAI;UAAA2E,QAAA,EACF3B,UAAU,CAAC6G,eAAe,CAACT,GAAG,CAAC,CAACU,IAAI,EAAER,KAAK,kBAC1C3G,OAAA,CAACrD,KAAK,CAACiK,QAAQ;YAAA5E,QAAA,gBACbhC,OAAA,CAAC1C,QAAQ;cAAA0E,QAAA,gBACPhC,OAAA,CAACzC,YAAY;gBAAAyE,QAAA,EACV2C,eAAe,CAACwC,IAAI,CAACvC,QAAQ;cAAC;gBAAAlC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB,CAAC,eACf7C,OAAA,CAACxC,YAAY;gBACXqJ,OAAO,eACL7G,OAAA,CAAChD,GAAG;kBAACmF,OAAO,EAAC,MAAM;kBAACC,UAAU,EAAC,QAAQ;kBAACkD,GAAG,EAAE,CAAE;kBAAAtD,QAAA,gBAC7ChC,OAAA,CAACjD,UAAU;oBAAC+F,OAAO,EAAC,WAAW;oBAAAd,QAAA,EAC5BmF,IAAI,CAACL,IAAI,IAAI,iBAAiBH,KAAK,GAAG,CAAC;kBAAE;oBAAAjE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChC,CAAC,eACb7C,OAAA,CAAC5C,IAAI;oBACH6I,KAAK,EAAEkB,IAAI,CAACvC,QAAQ,IAAI,SAAU;oBAClCnC,KAAK,EAAEqC,gBAAgB,CAACqC,IAAI,CAACvC,QAAQ,CAAE;oBACvCa,IAAI,EAAC;kBAAO;oBAAA/C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACb,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CACN;gBACDkE,SAAS,eACP/G,OAAA,CAAChD,GAAG;kBAAAgF,QAAA,gBACFhC,OAAA,CAACjD,UAAU;oBAAC+F,OAAO,EAAC,OAAO;oBAACL,KAAK,EAAC,gBAAgB;oBAAAT,QAAA,EAC/CmF,IAAI,CAACH,WAAW,IAAI;kBAA0B;oBAAAtE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrC,CAAC,EACZsE,IAAI,CAACC,GAAG,iBACPpH,OAAA,CAACjD,UAAU;oBAAC+F,OAAO,EAAC,SAAS;oBAACL,KAAK,EAAC,OAAO;oBAAAT,QAAA,GAAC,OACrC,EAACmF,IAAI,CAACC,GAAG;kBAAA;oBAAA1E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CACb;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE;cACN;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CAAC,EACV8D,KAAK,GAAGtG,UAAU,CAAC6G,eAAe,CAACV,MAAM,GAAG,CAAC,iBAAIxG,OAAA,CAACtC,OAAO;cAAAgF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA,GAhC1C8D,KAAK;YAAAjE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAiCV,CACjB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CACZ,EAGAxC,UAAU,CAACgH,OAAO,IAAIhH,UAAU,CAACgH,OAAO,CAACb,MAAM,GAAG,CAAC,iBAClDxG,OAAA,CAAC/C,SAAS;MACRuD,QAAQ,EAAEA,QAAQ,KAAK,SAAU;MACjCqF,QAAQ,EAAE5C,qBAAqB,CAAC,SAAS,CAAE;MAAAjB,QAAA,gBAE3ChC,OAAA,CAAC9C,gBAAgB;QAAC4I,UAAU,eAAE9F,OAAA,CAAC7B,cAAc;UAAAuE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAAAb,QAAA,eAC/ChC,OAAA,CAACjD,UAAU;UAAC+F,OAAO,EAAC,IAAI;UAAAd,QAAA,EAAC;QAAiB;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvC,CAAC,eACnB7C,OAAA,CAAC7C,gBAAgB;QAAA6E,QAAA,eACfhC,OAAA,CAAC3C,IAAI;UAAA2E,QAAA,EACF3B,UAAU,CAACgH,OAAO,CAACZ,GAAG,CAAC,CAACa,MAAM,EAAEX,KAAK,kBACpC3G,OAAA,CAACrD,KAAK,CAACiK,QAAQ;YAAA5E,QAAA,gBACbhC,OAAA,CAAC1C,QAAQ;cAAA0E,QAAA,gBACPhC,OAAA,CAACzC,YAAY;gBAAAyE,QAAA,eACXhC,OAAA,CAACb,cAAc;kBAAAuD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eACf7C,OAAA,CAACxC,YAAY;gBACXqJ,OAAO,EAAES,MAAM,CAACC,KAAK,IAAI,UAAUZ,KAAK,GAAG,CAAC,EAAG;gBAC/CI,SAAS,EAAEO,MAAM,CAACN,WAAW,IAAIM,MAAM,CAACE,KAAK,IAAI;cAAuB;gBAAA9E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CAAC,EACV8D,KAAK,GAAGtG,UAAU,CAACgH,OAAO,CAACb,MAAM,GAAG,CAAC,iBAAIxG,OAAA,CAACtC,OAAO;cAAAgF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA,GAVlC8D,KAAK;YAAAjE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAWV,CACjB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CACZ,EAGAxC,UAAU,CAACoH,eAAe,IAAIpH,UAAU,CAACoH,eAAe,CAACjB,MAAM,GAAG,CAAC,iBAClExG,OAAA,CAAC/C,SAAS;MACRuD,QAAQ,EAAEA,QAAQ,KAAK,iBAAkB;MACzCqF,QAAQ,EAAE5C,qBAAqB,CAAC,iBAAiB,CAAE;MAAAjB,QAAA,gBAEnDhC,OAAA,CAAC9C,gBAAgB;QAAC4I,UAAU,eAAE9F,OAAA,CAAC7B,cAAc;UAAAuE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAAAb,QAAA,eAC/ChC,OAAA,CAACjD,UAAU;UAAC+F,OAAO,EAAC,IAAI;UAAAd,QAAA,EAAC;QAAe;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrC,CAAC,eACnB7C,OAAA,CAAC7C,gBAAgB;QAAA6E,QAAA,eACfhC,OAAA,CAAC3C,IAAI;UAAA2E,QAAA,EACF3B,UAAU,CAACoH,eAAe,CAAChB,GAAG,CAAC,CAACiB,GAAG,EAAEf,KAAK,kBACzC3G,OAAA,CAACrD,KAAK,CAACiK,QAAQ;YAAA5E,QAAA,gBACbhC,OAAA,CAAC1C,QAAQ;cAAA0E,QAAA,gBACPhC,OAAA,CAACzC,YAAY;gBAAAyE,QAAA,eACXhC,OAAA,CAACrB,eAAe;kBAAC8D,KAAK,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB,CAAC,eACf7C,OAAA,CAACxC,YAAY;gBACXqJ,OAAO,EAAEa,GAAG,CAACH,KAAK,IAAIG,GAAI;gBAC1BX,SAAS,EAAEW,GAAG,CAACV;cAAY;gBAAAtE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CAAC,EACV8D,KAAK,GAAGtG,UAAU,CAACoH,eAAe,CAACjB,MAAM,GAAG,CAAC,iBAAIxG,OAAA,CAACtC,OAAO;cAAAgF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA,GAV1C8D,KAAK;YAAAjE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAWV,CACjB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CACZ,eAGH7C,OAAA,CAAClC,IAAI;MACH6J,QAAQ,EAAEjH,cAAe;MACzBkH,IAAI,EAAEC,OAAO,CAACnH,cAAc,CAAE;MAC9BoH,OAAO,EAAEtE,mBAAoB;MAC7BuE,YAAY,EAAE;QACZC,QAAQ,EAAE,QAAQ;QAClBC,UAAU,EAAE;MACd,CAAE;MACFC,eAAe,EAAE;QACfF,QAAQ,EAAE,KAAK;QACfC,UAAU,EAAE;MACd,CAAE;MACFE,UAAU,EAAE;QACV9G,EAAE,EAAE;UACF+G,EAAE,EAAE,CAAC;UACLzG,YAAY,EAAE,CAAC;UACf0G,QAAQ,EAAE;QACZ;MACF,CAAE;MAAArG,QAAA,eAEFhC,OAAA,CAACjC,QAAQ;QAAC2H,OAAO,EAAEhC,iBAAkB;QAACrC,EAAE,EAAE;UAAEiE,GAAG,EAAE;QAAE,CAAE;QAAAtD,QAAA,gBACnDhC,OAAA,CAACL,OAAO;UAAC8C,KAAK,EAAC;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACzB7C,OAAA,CAAChD,GAAG;UAAAgF,QAAA,gBACFhC,OAAA,CAACjD,UAAU;YAAC+F,OAAO,EAAC,WAAW;YAAAd,QAAA,EAAC;UAAU;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACvD7C,OAAA,CAACjD,UAAU;YAAC+F,OAAO,EAAC,SAAS;YAACL,KAAK,EAAC,gBAAgB;YAAAT,QAAA,EAAC;UAErD;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CAAC,eAGP7C,OAAA,CAAClC,IAAI;MACH6J,QAAQ,EAAE/G,WAAY;MACtBgH,IAAI,EAAEC,OAAO,CAACjH,WAAW,CAAE;MAC3BkH,OAAO,EAAErE,gBAAiB;MAC1BsE,YAAY,EAAE;QACZC,QAAQ,EAAE,QAAQ;QAClBC,UAAU,EAAE;MACd,CAAE;MACFC,eAAe,EAAE;QACfF,QAAQ,EAAE,KAAK;QACfC,UAAU,EAAE;MACd,CAAE;MACFE,UAAU,EAAE;QACV9G,EAAE,EAAE;UACF+G,EAAE,EAAE,CAAC;UACLzG,YAAY,EAAE,CAAC;UACf0G,QAAQ,EAAE;QACZ;MACF,CAAE;MAAArG,QAAA,gBAEFhC,OAAA,CAACjC,QAAQ;QAAC2H,OAAO,EAAExB,eAAgB;QAAC7C,EAAE,EAAE;UAAEiE,GAAG,EAAE;QAAE,CAAE;QAAAtD,QAAA,gBACjDhC,OAAA,CAACT,QAAQ;UAACkD,KAAK,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC5B7C,OAAA,CAAChD,GAAG;UAAAgF,QAAA,gBACFhC,OAAA,CAACjD,UAAU;YAAC+F,OAAO,EAAC,WAAW;YAAAd,QAAA,EAAC;UAAa;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC1D7C,OAAA,CAACjD,UAAU;YAAC+F,OAAO,EAAC,SAAS;YAACL,KAAK,EAAC,gBAAgB;YAAAT,QAAA,EAAC;UAErD;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACX7C,OAAA,CAACjC,QAAQ;QAAC2H,OAAO,EAAEjB,gBAAiB;QAACpD,EAAE,EAAE;UAAEiE,GAAG,EAAE;QAAE,CAAE;QAAAtD,QAAA,gBAClDhC,OAAA,CAACX,SAAS;UAACoD,KAAK,EAAC;QAAM;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC1B7C,OAAA,CAAChD,GAAG;UAAAgF,QAAA,gBACFhC,OAAA,CAACjD,UAAU;YAAC+F,OAAO,EAAC,WAAW;YAAAd,QAAA,EAAC;UAAY;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACzD7C,OAAA,CAACjD,UAAU;YAAC+F,OAAO,EAAC,SAAS;YAACL,KAAK,EAAC,gBAAgB;YAAAT,QAAA,EAAC;UAErD;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACX7C,OAAA,CAACjC,QAAQ;QAAC2H,OAAO,EAAEhB,gBAAiB;QAACrD,EAAE,EAAE;UAAEiE,GAAG,EAAE;QAAE,CAAE;QAAAtD,QAAA,gBAClDhC,OAAA,CAACP,QAAQ;UAACgD,KAAK,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC5B7C,OAAA,CAAChD,GAAG;UAAAgF,QAAA,gBACFhC,OAAA,CAACjD,UAAU;YAAC+F,OAAO,EAAC,WAAW;YAAAd,QAAA,EAAC;UAAiB;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC9D7C,OAAA,CAACjD,UAAU;YAAC+F,OAAO,EAAC,SAAS;YAACL,KAAK,EAAC,gBAAgB;YAAAT,QAAA,EAAC;UAErD;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CAAC,EAGN3B,YAAY,iBACXlB,OAAA,CAAC/B,QAAQ;MACP2J,IAAI,EAAE,IAAK;MACXU,gBAAgB,EAAE,IAAK;MACvBR,OAAO,EAAEA,CAAA,KAAM3G,eAAe,CAAC,IAAI,CAAE;MACrC4G,YAAY,EAAE;QAAEC,QAAQ,EAAE,QAAQ;QAAEC,UAAU,EAAE;MAAS,CAAE;MAAAjG,QAAA,eAE3DhC,OAAA,CAACrC,KAAK;QACJmK,OAAO,EAAEA,CAAA,KAAM3G,eAAe,CAAC,IAAI,CAAE;QACrCyD,QAAQ,EAAE1D,YAAY,CAAC4C,IAAK;QAC5BhB,OAAO,EAAC,QAAQ;QAChBzB,EAAE,EAAE;UAAEM,YAAY,EAAE;QAAE,CAAE;QAAAK,QAAA,EAEvBd,YAAY,CAAC6C;MAAO;QAAArB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CACX;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;EAAA,QArkB6BjD,OAAO;AAAA,EAqkBpC,CAAC;EAAA,QArkB4BA,OAAO;AAAA,EAqkBnC;AAAC2I,GAAA,GA5kBGtI,UAAU;AA8kBhBA,UAAU,CAACuI,WAAW,GAAG,YAAY;AAErC,eAAevI,UAAU;AAAC,IAAAG,EAAA,EAAAmI,GAAA;AAAAE,YAAA,CAAArI,EAAA;AAAAqI,YAAA,CAAAF,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}