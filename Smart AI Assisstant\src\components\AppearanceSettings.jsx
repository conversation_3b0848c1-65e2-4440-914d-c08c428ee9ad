import React, { useState } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  ButtonGroup,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Slider,
  Switch,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondaryAction,
  Alert,
  Chip,
} from '@mui/material';
import {
  Palette as PaletteIcon,
  Language as LanguageIcon,
  TextFields as TextFieldsIcon,
  Brightness4 as DarkModeIcon,
  Brightness7 as LightModeIcon,
  BrightnessAuto as AutoModeIcon,
  Zoom as ZoomIcon,
  Animation as AnimationIcon,
} from '@mui/icons-material';
import { useScan } from '../contexts/ScanContext';

const AppearanceSettings = ({ showLanguageOnly = false }) => {
  const { darkMode, language, toggleDarkMode, setLanguage } = useScan();
  const [alert, setAlert] = useState({ show: false, message: '', severity: 'success' });
  
  // Local appearance settings
  const [appearanceSettings, setAppearanceSettings] = useState({
    fontSize: 16,
    animations: true,
    compactMode: false,
    highContrast: false,
    reducedMotion: false,
  });

  const showAlert = (message, severity = 'success') => {
    setAlert({ show: true, message, severity });
    setTimeout(() => setAlert({ show: false, message: '', severity: 'success' }), 3000);
  };

  const languages = [
    { code: 'en', label: '🇺🇸 English', name: 'English' },
    { code: 'ar', label: '🇸🇦 العربية', name: 'Arabic' },
    { code: 'fr', label: '🇫🇷 Français', name: 'French' },
    { code: 'es', label: '🇪🇸 Español', name: 'Spanish' },
    { code: 'de', label: '🇩🇪 Deutsch', name: 'German' },
    { code: 'zh', label: '🇨🇳 中文', name: 'Chinese' },
    { code: 'ja', label: '🇯🇵 日本語', name: 'Japanese' },
    { code: 'ko', label: '🇰🇷 한국어', name: 'Korean' },
  ];

  const fontSizes = [
    { value: 12, label: 'Small' },
    { value: 14, label: 'Medium' },
    { value: 16, label: 'Large' },
    { value: 18, label: 'Extra Large' },
    { value: 20, label: 'Huge' },
  ];

  const handleLanguageChange = (langCode) => {
    setLanguage(langCode);
    const langName = languages.find(l => l.code === langCode)?.name || 'Unknown';
    showAlert(`Language changed to ${langName}`, 'success');
  };

  const handleThemeChange = () => {
    toggleDarkMode();
    showAlert(`Switched to ${!darkMode ? 'dark' : 'light'} mode`, 'success');
  };

  const handleAppearanceSettingChange = (setting, value) => {
    setAppearanceSettings(prev => ({
      ...prev,
      [setting]: value,
    }));
    
    const settingName = setting.replace(/([A-Z])/g, ' $1').toLowerCase();
    showAlert(`${settingName} ${typeof value === 'boolean' ? (value ? 'enabled' : 'disabled') : 'updated'}`, 'info');
  };

  if (showLanguageOnly) {
    return (
      <Box>
        {alert.show && (
          <Alert severity={alert.severity} sx={{ mb: 3 }}>
            {alert.message}
          </Alert>
        )}

        <Typography variant="h4" gutterBottom fontWeight="bold">
          Language Settings
        </Typography>
        <Typography variant="body1" color="text.secondary" sx={{ mb: 4 }}>
          Choose your preferred language for the interface
        </Typography>

        <Card>
          <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
              <LanguageIcon sx={{ mr: 2, color: 'primary.main' }} />
              <Typography variant="h6" fontWeight="600">
                Interface Language
              </Typography>
            </Box>
            
            <Grid container spacing={2}>
              {languages.map((lang) => (
                <Grid item xs={12} sm={6} md={4} key={lang.code}>
                  <Button
                    variant={language === lang.code ? 'contained' : 'outlined'}
                    onClick={() => handleLanguageChange(lang.code)}
                    fullWidth
                    sx={{
                      borderRadius: 3,
                      py: 1.5,
                      justifyContent: 'flex-start',
                      background: language === lang.code
                        ? 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
                        : 'transparent',
                      '&:hover': {
                        background: language === lang.code
                          ? 'linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)'
                          : 'rgba(102, 126, 234, 0.1)',
                      },
                    }}
                  >
                    {lang.label}
                  </Button>
                </Grid>
              ))}
            </Grid>
          </CardContent>
        </Card>
      </Box>
    );
  }

  return (
    <Box>
      {/* Alert */}
      {alert.show && (
        <Alert severity={alert.severity} sx={{ mb: 3 }}>
          {alert.message}
        </Alert>
      )}

      <Typography variant="h4" gutterBottom fontWeight="bold">
        Appearance Settings
      </Typography>
      <Typography variant="body1" color="text.secondary" sx={{ mb: 4 }}>
        Customize the look and feel of your interface
      </Typography>

      {/* Theme Settings */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
            <PaletteIcon sx={{ mr: 2, color: 'primary.main' }} />
            <Typography variant="h6" fontWeight="600">
              Theme
            </Typography>
          </Box>
          
          <ButtonGroup variant="outlined" fullWidth sx={{ mb: 2 }}>
            <Button
              variant={!darkMode ? 'contained' : 'outlined'}
              startIcon={<LightModeIcon />}
              onClick={!darkMode ? undefined : handleThemeChange}
              sx={{
                background: !darkMode ? 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)' : 'transparent',
                '&:hover': {
                  background: !darkMode ? 'linear-gradient(135deg, #e081e8 0%, #e34759 100%)' : 'rgba(240, 147, 251, 0.1)',
                },
              }}
            >
              Light Mode
            </Button>
            <Button
              variant={darkMode ? 'contained' : 'outlined'}
              startIcon={<DarkModeIcon />}
              onClick={darkMode ? undefined : handleThemeChange}
              sx={{
                background: darkMode ? 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' : 'transparent',
                '&:hover': {
                  background: darkMode ? 'linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)' : 'rgba(102, 126, 234, 0.1)',
                },
              }}
            >
              Dark Mode
            </Button>
          </ButtonGroup>
          
          <Typography variant="body2" color="text.secondary">
            Current theme: {darkMode ? 'Dark Mode' : 'Light Mode'}
          </Typography>
        </CardContent>
      </Card>

      {/* Language Settings */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
            <LanguageIcon sx={{ mr: 2, color: 'primary.main' }} />
            <Typography variant="h6" fontWeight="600">
              Language
            </Typography>
          </Box>
          
          <FormControl fullWidth sx={{ mb: 2 }}>
            <InputLabel>Interface Language</InputLabel>
            <Select
              value={language}
              label="Interface Language"
              onChange={(e) => handleLanguageChange(e.target.value)}
            >
              {languages.map((lang) => (
                <MenuItem key={lang.code} value={lang.code}>
                  {lang.label}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
          
          <Typography variant="body2" color="text.secondary">
            Current language: {languages.find(l => l.code === language)?.name || 'English'}
          </Typography>
        </CardContent>
      </Card>

      {/* Typography Settings */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
            <TextFieldsIcon sx={{ mr: 2, color: 'primary.main' }} />
            <Typography variant="h6" fontWeight="600">
              Typography
            </Typography>
          </Box>
          
          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
            Font Size: {appearanceSettings.fontSize}px
          </Typography>
          
          <Slider
            value={appearanceSettings.fontSize}
            onChange={(e, value) => handleAppearanceSettingChange('fontSize', value)}
            min={12}
            max={24}
            step={2}
            marks={fontSizes}
            valueLabelDisplay="auto"
            sx={{ mb: 2 }}
          />
          
          <Typography 
            variant="body1" 
            sx={{ 
              fontSize: `${appearanceSettings.fontSize}px`,
              p: 2,
              border: '1px solid',
              borderColor: 'divider',
              borderRadius: 2,
              background: 'rgba(102, 126, 234, 0.05)',
            }}
          >
            This is a preview of how text will appear with your selected font size.
          </Typography>
        </CardContent>
      </Card>

      {/* Accessibility Settings */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" fontWeight="600" sx={{ mb: 2 }}>
            Accessibility
          </Typography>
          
          <List>
            <ListItem>
              <ListItemText
                primary="High Contrast"
                secondary="Increase contrast for better visibility"
              />
              <ListItemSecondaryAction>
                <Switch
                  checked={appearanceSettings.highContrast}
                  onChange={(e) => handleAppearanceSettingChange('highContrast', e.target.checked)}
                />
              </ListItemSecondaryAction>
            </ListItem>
            
            <ListItem>
              <ListItemText
                primary="Reduced Motion"
                secondary="Minimize animations and transitions"
              />
              <ListItemSecondaryAction>
                <Switch
                  checked={appearanceSettings.reducedMotion}
                  onChange={(e) => handleAppearanceSettingChange('reducedMotion', e.target.checked)}
                />
              </ListItemSecondaryAction>
            </ListItem>
            
            <ListItem>
              <ListItemText
                primary="Compact Mode"
                secondary="Reduce spacing for more content"
              />
              <ListItemSecondaryAction>
                <Switch
                  checked={appearanceSettings.compactMode}
                  onChange={(e) => handleAppearanceSettingChange('compactMode', e.target.checked)}
                />
              </ListItemSecondaryAction>
            </ListItem>
          </List>
        </CardContent>
      </Card>

      {/* Animation Settings */}
      <Card>
        <CardContent>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <AnimationIcon sx={{ mr: 2, color: 'primary.main' }} />
            <Typography variant="h6" fontWeight="600">
              Animations
            </Typography>
          </Box>
          
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <Box>
              <Typography variant="body1" fontWeight="500">
                Enable Animations
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Smooth transitions and visual effects
              </Typography>
            </Box>
            <Switch
              checked={appearanceSettings.animations}
              onChange={(e) => handleAppearanceSettingChange('animations', e.target.checked)}
            />
          </Box>
        </CardContent>
      </Card>
    </Box>
  );
};

export default AppearanceSettings;
