{"ast": null, "code": "var _jsxFileName = \"E:\\\\PythonProjects\\\\React.js\\\\Smart AI Assisstant\\\\src\\\\components\\\\Header.jsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { AppBar, Toolbar, Typography, IconButton, Switch, FormControlLabel, Box, Button, Menu, MenuItem, Container, Tooltip, Avatar, Badge, Divider } from '@mui/material';\nimport { Security as SecurityIcon, DarkMode as DarkModeIcon, LightMode as LightModeIcon, Language as LanguageIcon, History as HistoryIcon, Home as HomeIcon, Info as InfoIcon, Notifications as NotificationsIcon, Settings as SettingsIcon, Star as StarIcon } from '@mui/icons-material';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport { useScan } from '../contexts/ScanContext';\nimport EnhancedSettingsDialog from './EnhancedSettingsDialog';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Header = /*#__PURE__*/_s(/*#__PURE__*/React.memo(_c = _s(() => {\n  _s();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const {\n    darkMode,\n    language,\n    setLanguage,\n    notifications\n  } = useScan();\n  const [languageAnchor, setLanguageAnchor] = React.useState(null);\n  const [settingsAnchor, setSettingsAnchor] = React.useState(null);\n  const [notificationAnchor, setNotificationAnchor] = React.useState(null);\n  const [settingsDialogOpen, setSettingsDialogOpen] = React.useState(false);\n\n  // Sample notification history\n  const [notificationHistory, setNotificationHistory] = React.useState([{\n    id: 1,\n    title: 'Welcome to AI Security Guard',\n    message: 'You successfully logged in to the Platform. Have a nice tour!',\n    timestamp: new Date().toISOString(),\n    type: 'success',\n    read: false\n  }, {\n    id: 2,\n    title: 'Security Scan Completed',\n    message: 'Your recent URL scan has been completed successfully.',\n    timestamp: new Date(Date.now() - 3600000).toISOString(),\n    type: 'info',\n    read: true\n  }, {\n    id: 3,\n    title: 'Threat Detected',\n    message: 'A potential threat was detected and blocked automatically.',\n    timestamp: new Date(Date.now() - 7200000).toISOString(),\n    type: 'warning',\n    read: true\n  }]);\n  const handleLanguageClick = event => {\n    setLanguageAnchor(event.currentTarget);\n  };\n  const handleLanguageClose = () => {\n    setLanguageAnchor(null);\n  };\n  const handleLanguageSelect = lang => {\n    setLanguage(lang);\n    handleLanguageClose();\n  };\n  const handleSettingsClick = event => {\n    setSettingsAnchor(event.currentTarget);\n  };\n  const handleSettingsClose = () => {\n    setSettingsAnchor(null);\n  };\n  const handleOpenSettingsDialog = () => {\n    setSettingsDialogOpen(true);\n    handleSettingsClose();\n  };\n  const handleNotificationClick = event => {\n    setNotificationAnchor(event.currentTarget);\n  };\n  const handleNotificationClose = () => {\n    setNotificationAnchor(null);\n  };\n  const markNotificationAsRead = notificationId => {\n    setNotificationHistory(prev => prev.map(notif => notif.id === notificationId ? {\n      ...notif,\n      read: true\n    } : notif));\n  };\n  const unreadCount = notificationHistory.filter(notif => !notif.read).length;\n  const isHomePage = location.pathname === '/';\n  const isSmartFeaturesPage = location.pathname === '/smart-features';\n  const isHistoryPage = location.pathname === '/history';\n  const isAboutPage = location.pathname === '/about';\n  const isPlansPage = location.pathname === '/plans';\n  const navigationItems = [{\n    label: 'Home',\n    path: '/',\n    icon: /*#__PURE__*/_jsxDEV(HomeIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 123,\n      columnNumber: 39\n    }, this),\n    active: isHomePage\n  }, {\n    label: 'Smart Features',\n    path: '/smart-features',\n    icon: /*#__PURE__*/_jsxDEV(SecurityIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 124,\n      columnNumber: 63\n    }, this),\n    active: isSmartFeaturesPage\n  }, {\n    label: 'History',\n    path: '/history',\n    icon: /*#__PURE__*/_jsxDEV(HistoryIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 125,\n      columnNumber: 49\n    }, this),\n    active: isHistoryPage\n  }, {\n    label: 'Plans',\n    path: '/plans',\n    icon: /*#__PURE__*/_jsxDEV(StarIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 126,\n      columnNumber: 45\n    }, this),\n    active: isPlansPage\n  }, {\n    label: 'About',\n    path: '/about',\n    icon: /*#__PURE__*/_jsxDEV(InfoIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 127,\n      columnNumber: 45\n    }, this),\n    active: isAboutPage\n  }];\n  return /*#__PURE__*/_jsxDEV(AppBar, {\n    position: \"sticky\",\n    elevation: 0,\n    sx: {\n      background: theme => theme.palette.mode === 'dark' ? 'linear-gradient(135deg, rgba(12, 12, 12, 0.98) 0%, rgba(26, 26, 26, 0.98) 50%, rgba(45, 45, 45, 0.98) 100%)' : 'linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%)',\n      backdropFilter: 'blur(25px) saturate(180%)',\n      borderBottom: theme => theme.palette.mode === 'dark' ? '1px solid rgba(255, 255, 255, 0.1)' : '1px solid rgba(255, 255, 255, 0.2)',\n      boxShadow: theme => theme.palette.mode === 'dark' ? '0 4px 20px rgba(102, 126, 234, 0.1)' : '0 4px 20px rgba(102, 126, 234, 0.3)',\n      position: 'relative',\n      color: 'white',\n      '&::after': {\n        content: '\"\"',\n        position: 'absolute',\n        bottom: 0,\n        left: 0,\n        right: 0,\n        height: '2px',\n        background: theme => theme.palette.mode === 'dark' ? 'linear-gradient(90deg, transparent 0%, #667eea 50%, transparent 100%)' : 'linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.8) 50%, transparent 100%)',\n        opacity: 0.6\n      }\n    },\n    children: [/*#__PURE__*/_jsxDEV(Container, {\n      maxWidth: \"xl\",\n      children: /*#__PURE__*/_jsxDEV(Toolbar, {\n        sx: {\n          justifyContent: 'space-between',\n          py: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          display: \"flex\",\n          alignItems: \"center\",\n          gap: 2,\n          children: [/*#__PURE__*/_jsxDEV(IconButton, {\n            edge: \"start\",\n            color: \"inherit\",\n            \"aria-label\": \"security logo\",\n            onClick: () => navigate('/'),\n            sx: {\n              background: 'rgba(255,255,255,0.1)',\n              backdropFilter: 'blur(10px)',\n              '&:hover': {\n                background: 'rgba(255,255,255,0.2)',\n                transform: 'scale(1.05)'\n              },\n              transition: 'all 0.2s ease'\n            },\n            children: /*#__PURE__*/_jsxDEV(SecurityIcon, {\n              fontSize: \"large\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 180,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 165,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h4\",\n              component: \"h1\",\n              sx: {\n                fontWeight: 800,\n                cursor: 'pointer',\n                display: {\n                  xs: 'none',\n                  md: 'block'\n                },\n                color: 'white',\n                textShadow: '0 2px 8px rgba(0,0,0,0.3)',\n                '&:hover': {\n                  textShadow: '0 2px 12px rgba(255,255,255,0.3)'\n                },\n                transition: 'all 0.3s ease'\n              },\n              onClick: () => navigate('/'),\n              children: \"AI Security Guard\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 183,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              component: \"h1\",\n              sx: {\n                fontWeight: 700,\n                cursor: 'pointer',\n                display: {\n                  xs: 'block',\n                  md: 'none'\n                },\n                color: 'white'\n              },\n              onClick: () => navigate('/'),\n              children: \"AI Security\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 201,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              sx: {\n                display: {\n                  xs: 'none',\n                  md: 'block'\n                },\n                background: 'linear-gradient(90deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%)',\n                backgroundSize: '200% 200%',\n                backgroundClip: 'text',\n                WebkitBackgroundClip: 'text',\n                WebkitTextFillColor: 'transparent',\n                animation: 'movingText 4s ease infinite',\n                fontSize: '0.75rem',\n                fontWeight: 600,\n                letterSpacing: '0.5px',\n                textTransform: 'uppercase',\n                '@keyframes movingText': {\n                  '0%': {\n                    backgroundPosition: '0% 50%'\n                  },\n                  '50%': {\n                    backgroundPosition: '100% 50%'\n                  },\n                  '100%': {\n                    backgroundPosition: '0% 50%'\n                  }\n                }\n              },\n              children: \"Advanced Threat Detection Platform\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 214,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 182,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 164,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          display: \"flex\",\n          alignItems: \"center\",\n          gap: 2,\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            display: {\n              xs: 'none',\n              md: 'flex'\n            },\n            gap: 1,\n            children: navigationItems.map(item => /*#__PURE__*/_jsxDEV(Button, {\n              color: \"inherit\",\n              startIcon: item.icon,\n              onClick: () => navigate(item.path),\n              variant: item.active ? 'contained' : 'text',\n              sx: {\n                borderRadius: 3,\n                px: 3,\n                py: 1,\n                fontWeight: 600,\n                textTransform: 'none',\n                background: item.active ? 'rgba(255,255,255,0.2)' : 'transparent',\n                backdropFilter: item.active ? 'blur(10px)' : 'none',\n                border: item.active ? '1px solid rgba(255,255,255,0.3)' : 'none',\n                '&:hover': {\n                  background: 'rgba(255,255,255,0.15)',\n                  transform: 'translateY(-1px)'\n                },\n                transition: 'all 0.2s ease'\n              },\n              children: item.label\n            }, item.path, false, {\n              fileName: _jsxFileName,\n              lineNumber: 245,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 243,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            display: {\n              xs: 'flex',\n              md: 'none'\n            },\n            gap: 0.5,\n            children: navigationItems.map(item => /*#__PURE__*/_jsxDEV(Tooltip, {\n              title: item.label,\n              children: /*#__PURE__*/_jsxDEV(IconButton, {\n                color: \"inherit\",\n                onClick: () => navigate(item.path),\n                sx: {\n                  background: item.active ? 'rgba(255,255,255,0.2)' : 'transparent',\n                  backdropFilter: item.active ? 'blur(10px)' : 'none',\n                  border: item.active ? '1px solid rgba(255,255,255,0.3)' : 'none',\n                  '&:hover': {\n                    background: 'rgba(255,255,255,0.15)'\n                  }\n                },\n                children: item.icon\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 278,\n                columnNumber: 19\n              }, this)\n            }, item.path, false, {\n              fileName: _jsxFileName,\n              lineNumber: 277,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 275,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n            title: \"Notifications\",\n            children: /*#__PURE__*/_jsxDEV(IconButton, {\n              color: \"inherit\",\n              onClick: handleNotificationClick,\n              sx: {\n                background: 'rgba(255,255,255,0.1)',\n                '&:hover': {\n                  background: 'rgba(255,255,255,0.2)'\n                }\n              },\n              children: /*#__PURE__*/_jsxDEV(Badge, {\n                badgeContent: unreadCount,\n                color: \"error\",\n                children: /*#__PURE__*/_jsxDEV(NotificationsIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 311,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 310,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 300,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 299,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n            title: \"Settings\",\n            children: /*#__PURE__*/_jsxDEV(IconButton, {\n              color: \"inherit\",\n              onClick: handleSettingsClick,\n              sx: {\n                background: 'rgba(255,255,255,0.1)',\n                '&:hover': {\n                  background: 'rgba(255,255,255,0.2)'\n                }\n              },\n              children: /*#__PURE__*/_jsxDEV(SettingsIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 328,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 318,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 317,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Menu, {\n            anchorEl: settingsAnchor,\n            open: Boolean(settingsAnchor),\n            onClose: handleSettingsClose,\n            anchorOrigin: {\n              vertical: 'bottom',\n              horizontal: 'right'\n            },\n            transformOrigin: {\n              vertical: 'top',\n              horizontal: 'right'\n            },\n            PaperProps: {\n              sx: {\n                mt: 1,\n                borderRadius: 2,\n                minWidth: 200\n              }\n            },\n            children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: handleOpenSettingsDialog,\n              sx: {\n                gap: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(SettingsIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 354,\n                columnNumber: 17\n              }, this), \"Account & Settings\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 353,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: handleLanguageClick,\n              sx: {\n                gap: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(LanguageIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 360,\n                columnNumber: 17\n              }, this), \"Language\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 359,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => setSettingsDialogOpen(true),\n              sx: {\n                gap: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(SettingsIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 366,\n                columnNumber: 17\n              }, this), \"Settings\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 365,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 332,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Menu, {\n            anchorEl: languageAnchor,\n            open: Boolean(languageAnchor),\n            onClose: handleLanguageClose,\n            anchorOrigin: {\n              vertical: 'bottom',\n              horizontal: 'right'\n            },\n            transformOrigin: {\n              vertical: 'top',\n              horizontal: 'right'\n            },\n            PaperProps: {\n              sx: {\n                mt: 1,\n                borderRadius: 2\n              }\n            },\n            children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => handleLanguageSelect('en'),\n              selected: language === 'en',\n              children: \"\\uD83C\\uDDFA\\uD83C\\uDDF8 English\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 391,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => handleLanguageSelect('ar'),\n              selected: language === 'ar',\n              children: \"\\uD83C\\uDDF8\\uD83C\\uDDE6 \\u0627\\u0644\\u0639\\u0631\\u0628\\u064A\\u0629\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 397,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 372,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Menu, {\n            anchorEl: notificationAnchor,\n            open: Boolean(notificationAnchor),\n            onClose: handleNotificationClose,\n            anchorOrigin: {\n              vertical: 'bottom',\n              horizontal: 'right'\n            },\n            transformOrigin: {\n              vertical: 'top',\n              horizontal: 'right'\n            },\n            PaperProps: {\n              sx: {\n                mt: 1,\n                borderRadius: 3,\n                minWidth: 350,\n                maxWidth: 400,\n                maxHeight: 500,\n                background: theme => theme.palette.mode === 'dark' ? 'linear-gradient(135deg, rgba(12, 12, 12, 0.98) 0%, rgba(26, 26, 26, 0.98) 100%)' : 'linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(248, 250, 252, 0.98) 100%)',\n                backdropFilter: 'blur(25px)',\n                border: '1px solid rgba(102, 126, 234, 0.2)',\n                boxShadow: '0 20px 60px rgba(0, 0, 0, 0.3)'\n              }\n            },\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                p: 2,\n                borderBottom: '1px solid rgba(102, 126, 234, 0.2)'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                fontWeight: \"bold\",\n                color: \"primary.main\",\n                children: \"Notifications\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 435,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                children: unreadCount > 0 ? `${unreadCount} unread notifications` : 'All caught up!'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 438,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 434,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                maxHeight: 350,\n                overflow: 'auto'\n              },\n              children: notificationHistory.length === 0 ? /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  p: 3,\n                  textAlign: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(NotificationsIcon, {\n                  sx: {\n                    fontSize: 48,\n                    color: 'text.secondary',\n                    mb: 2\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 445,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: \"No notifications yet\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 446,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 444,\n                columnNumber: 19\n              }, this) : notificationHistory.map((notification, index) => /*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                  onClick: () => markNotificationAsRead(notification.id),\n                  sx: {\n                    py: 2,\n                    px: 3,\n                    alignItems: 'flex-start',\n                    background: !notification.read ? 'rgba(102, 126, 234, 0.05)' : 'transparent',\n                    '&:hover': {\n                      background: 'rgba(102, 126, 234, 0.1)'\n                    }\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      mr: 2,\n                      mt: 0.5\n                    },\n                    children: /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        width: 8,\n                        height: 8,\n                        borderRadius: '50%',\n                        background: notification.type === 'success' ? '#4caf50' : notification.type === 'warning' ? '#ff9800' : notification.type === 'error' ? '#f44336' : '#2196f3',\n                        opacity: notification.read ? 0.3 : 1\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 468,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 467,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      flex: 1\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"subtitle2\",\n                      fontWeight: notification.read ? 400 : 600,\n                      sx: {\n                        mb: 0.5\n                      },\n                      children: notification.title\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 481,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      color: \"text.secondary\",\n                      sx: {\n                        mb: 1,\n                        lineHeight: 1.4\n                      },\n                      children: notification.message\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 488,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"caption\",\n                      color: \"text.secondary\",\n                      children: new Date(notification.timestamp).toLocaleString()\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 495,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 480,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 453,\n                  columnNumber: 23\n                }, this), index < notificationHistory.length - 1 && /*#__PURE__*/_jsxDEV(Divider, {\n                  sx: {\n                    mx: 2\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 501,\n                  columnNumber: 25\n                }, this)]\n              }, notification.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 452,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 442,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 406,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 241,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 162,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 161,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(EnhancedSettingsDialog, {\n      open: settingsDialogOpen,\n      onClose: () => setSettingsDialogOpen(false)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 514,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 131,\n    columnNumber: 5\n  }, this);\n}, \"8YjtmiSIV6ObLR3u427Hv2xewt0=\", false, function () {\n  return [useNavigate, useLocation, useScan];\n})), \"8YjtmiSIV6ObLR3u427Hv2xewt0=\", false, function () {\n  return [useNavigate, useLocation, useScan];\n});\n_c2 = Header;\nHeader.displayName = 'Header';\nexport default Header;\nvar _c, _c2;\n$RefreshReg$(_c, \"Header$React.memo\");\n$RefreshReg$(_c2, \"Header\");", "map": {"version": 3, "names": ["React", "AppBar", "<PERSON><PERSON><PERSON>", "Typography", "IconButton", "Switch", "FormControlLabel", "Box", "<PERSON><PERSON>", "<PERSON><PERSON>", "MenuItem", "Container", "<PERSON><PERSON><PERSON>", "Avatar", "Badge", "Divider", "Security", "SecurityIcon", "DarkMode", "DarkModeIcon", "LightMode", "LightModeIcon", "Language", "LanguageIcon", "History", "HistoryIcon", "Home", "HomeIcon", "Info", "InfoIcon", "Notifications", "NotificationsIcon", "Settings", "SettingsIcon", "Star", "StarIcon", "useNavigate", "useLocation", "useScan", "EnhancedSettingsDialog", "jsxDEV", "_jsxDEV", "Header", "_s", "memo", "_c", "navigate", "location", "darkMode", "language", "setLanguage", "notifications", "languageAnchor", "setLanguageAnchor", "useState", "settingsAnchor", "setSettingsAnchor", "notificationAnchor", "setNotificationAnchor", "settingsDialogOpen", "setSettingsDialogOpen", "notificationHistory", "setNotificationHistory", "id", "title", "message", "timestamp", "Date", "toISOString", "type", "read", "now", "handleLanguageClick", "event", "currentTarget", "handleLanguageClose", "handleLanguageSelect", "lang", "handleSettingsClick", "handleSettingsClose", "handleOpenSettingsDialog", "handleNotificationClick", "handleNotificationClose", "markNotificationAsRead", "notificationId", "prev", "map", "notif", "unreadCount", "filter", "length", "isHomePage", "pathname", "isSmartFeaturesPage", "isHistoryPage", "isAboutPage", "isPlansPage", "navigationItems", "label", "path", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "active", "position", "elevation", "sx", "background", "theme", "palette", "mode", "<PERSON><PERSON>ilter", "borderBottom", "boxShadow", "color", "content", "bottom", "left", "right", "height", "opacity", "children", "max<PERSON><PERSON><PERSON>", "justifyContent", "py", "display", "alignItems", "gap", "edge", "onClick", "transform", "transition", "fontSize", "variant", "component", "fontWeight", "cursor", "xs", "md", "textShadow", "backgroundSize", "backgroundClip", "WebkitBackgroundClip", "WebkitTextFillColor", "animation", "letterSpacing", "textTransform", "backgroundPosition", "item", "startIcon", "borderRadius", "px", "border", "badgeContent", "anchorEl", "open", "Boolean", "onClose", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "transform<PERSON><PERSON>in", "PaperProps", "mt", "min<PERSON><PERSON><PERSON>", "selected", "maxHeight", "p", "overflow", "textAlign", "mb", "notification", "index", "mr", "width", "flex", "lineHeight", "toLocaleString", "mx", "_c2", "displayName", "$RefreshReg$"], "sources": ["E:/PythonProjects/React.js/Smart AI Assisstant/src/components/Header.jsx"], "sourcesContent": ["import React from 'react';\nimport {\n  <PERSON><PERSON><PERSON><PERSON>,\n  <PERSON><PERSON><PERSON>,\n  <PERSON><PERSON><PERSON>,\n  IconButton,\n  Switch,\n  FormControlLabel,\n  Box,\n  Button,\n  Menu,\n  MenuItem,\n  Container,\n  Tooltip,\n  Avatar,\n  Badge,\n  Divider,\n} from '@mui/material';\nimport {\n  Security as SecurityIcon,\n  DarkMode as DarkModeIcon,\n  LightMode as LightModeIcon,\n  Language as LanguageIcon,\n  History as HistoryIcon,\n  Home as HomeIcon,\n  Info as InfoIcon,\n  Notifications as NotificationsIcon,\n  Settings as SettingsIcon,\n  Star as StarIcon,\n} from '@mui/icons-material';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport { useScan } from '../contexts/ScanContext';\nimport EnhancedSettingsDialog from './EnhancedSettingsDialog';\n\nconst Header = React.memo(() => {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const { darkMode, language, setLanguage, notifications } = useScan();\n  const [languageAnchor, setLanguageAnchor] = React.useState(null);\n  const [settingsAnchor, setSettingsAnchor] = React.useState(null);\n  const [notificationAnchor, setNotificationAnchor] = React.useState(null);\n  const [settingsDialogOpen, setSettingsDialogOpen] = React.useState(false);\n\n  // Sample notification history\n  const [notificationHistory, setNotificationHistory] = React.useState([\n    {\n      id: 1,\n      title: 'Welcome to AI Security Guard',\n      message: 'You successfully logged in to the Platform. Have a nice tour!',\n      timestamp: new Date().toISOString(),\n      type: 'success',\n      read: false,\n    },\n    {\n      id: 2,\n      title: 'Security Scan Completed',\n      message: 'Your recent URL scan has been completed successfully.',\n      timestamp: new Date(Date.now() - 3600000).toISOString(),\n      type: 'info',\n      read: true,\n    },\n    {\n      id: 3,\n      title: 'Threat Detected',\n      message: 'A potential threat was detected and blocked automatically.',\n      timestamp: new Date(Date.now() - 7200000).toISOString(),\n      type: 'warning',\n      read: true,\n    },\n  ]);\n\n  const handleLanguageClick = (event) => {\n    setLanguageAnchor(event.currentTarget);\n  };\n\n  const handleLanguageClose = () => {\n    setLanguageAnchor(null);\n  };\n\n  const handleLanguageSelect = (lang) => {\n    setLanguage(lang);\n    handleLanguageClose();\n  };\n\n  const handleSettingsClick = (event) => {\n    setSettingsAnchor(event.currentTarget);\n  };\n\n  const handleSettingsClose = () => {\n    setSettingsAnchor(null);\n  };\n\n  const handleOpenSettingsDialog = () => {\n    setSettingsDialogOpen(true);\n    handleSettingsClose();\n  };\n\n  const handleNotificationClick = (event) => {\n    setNotificationAnchor(event.currentTarget);\n  };\n\n  const handleNotificationClose = () => {\n    setNotificationAnchor(null);\n  };\n\n  const markNotificationAsRead = (notificationId) => {\n    setNotificationHistory(prev =>\n      prev.map(notif =>\n        notif.id === notificationId ? { ...notif, read: true } : notif\n      )\n    );\n  };\n\n  const unreadCount = notificationHistory.filter(notif => !notif.read).length;\n\n  const isHomePage = location.pathname === '/';\n  const isSmartFeaturesPage = location.pathname === '/smart-features';\n  const isHistoryPage = location.pathname === '/history';\n  const isAboutPage = location.pathname === '/about';\n  const isPlansPage = location.pathname === '/plans';\n\n  const navigationItems = [\n    { label: 'Home', path: '/', icon: <HomeIcon />, active: isHomePage },\n    { label: 'Smart Features', path: '/smart-features', icon: <SecurityIcon />, active: isSmartFeaturesPage },\n    { label: 'History', path: '/history', icon: <HistoryIcon />, active: isHistoryPage },\n    { label: 'Plans', path: '/plans', icon: <StarIcon />, active: isPlansPage },\n    { label: 'About', path: '/about', icon: <InfoIcon />, active: isAboutPage },\n  ];\n\n  return (\n    <AppBar\n      position=\"sticky\"\n      elevation={0}\n      sx={{\n        background: (theme) => theme.palette.mode === 'dark'\n          ? 'linear-gradient(135deg, rgba(12, 12, 12, 0.98) 0%, rgba(26, 26, 26, 0.98) 50%, rgba(45, 45, 45, 0.98) 100%)'\n          : 'linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%)',\n        backdropFilter: 'blur(25px) saturate(180%)',\n        borderBottom: (theme) => theme.palette.mode === 'dark'\n          ? '1px solid rgba(255, 255, 255, 0.1)'\n          : '1px solid rgba(255, 255, 255, 0.2)',\n        boxShadow: (theme) => theme.palette.mode === 'dark'\n          ? '0 4px 20px rgba(102, 126, 234, 0.1)'\n          : '0 4px 20px rgba(102, 126, 234, 0.3)',\n        position: 'relative',\n        color: 'white',\n        '&::after': {\n          content: '\"\"',\n          position: 'absolute',\n          bottom: 0,\n          left: 0,\n          right: 0,\n          height: '2px',\n          background: (theme) => theme.palette.mode === 'dark'\n            ? 'linear-gradient(90deg, transparent 0%, #667eea 50%, transparent 100%)'\n            : 'linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.8) 50%, transparent 100%)',\n          opacity: 0.6,\n        },\n      }}\n    >\n      <Container maxWidth=\"xl\">\n        <Toolbar sx={{ justifyContent: 'space-between', py: 1 }}>\n          {/* Logo and Title */}\n          <Box display=\"flex\" alignItems=\"center\" gap={2}>\n            <IconButton\n              edge=\"start\"\n              color=\"inherit\"\n              aria-label=\"security logo\"\n              onClick={() => navigate('/')}\n              sx={{\n                background: 'rgba(255,255,255,0.1)',\n                backdropFilter: 'blur(10px)',\n                '&:hover': {\n                  background: 'rgba(255,255,255,0.2)',\n                  transform: 'scale(1.05)',\n                },\n                transition: 'all 0.2s ease',\n              }}\n            >\n              <SecurityIcon fontSize=\"large\" />\n            </IconButton>\n            <Box>\n              <Typography\n                variant=\"h4\"\n                component=\"h1\"\n                sx={{\n                  fontWeight: 800,\n                  cursor: 'pointer',\n                  display: { xs: 'none', md: 'block' },\n                  color: 'white',\n                  textShadow: '0 2px 8px rgba(0,0,0,0.3)',\n                  '&:hover': {\n                    textShadow: '0 2px 12px rgba(255,255,255,0.3)',\n                  },\n                  transition: 'all 0.3s ease',\n                }}\n                onClick={() => navigate('/')}\n              >\n                AI Security Guard\n              </Typography>\n              <Typography\n                variant=\"h6\"\n                component=\"h1\"\n                sx={{\n                  fontWeight: 700,\n                  cursor: 'pointer',\n                  display: { xs: 'block', md: 'none' },\n                  color: 'white',\n                }}\n                onClick={() => navigate('/')}\n              >\n                AI Security\n              </Typography>\n              <Typography\n                variant=\"caption\"\n                sx={{\n                  display: { xs: 'none', md: 'block' },\n                  background: 'linear-gradient(90deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%)',\n                  backgroundSize: '200% 200%',\n                  backgroundClip: 'text',\n                  WebkitBackgroundClip: 'text',\n                  WebkitTextFillColor: 'transparent',\n                  animation: 'movingText 4s ease infinite',\n                  fontSize: '0.75rem',\n                  fontWeight: 600,\n                  letterSpacing: '0.5px',\n                  textTransform: 'uppercase',\n                  '@keyframes movingText': {\n                    '0%': { backgroundPosition: '0% 50%' },\n                    '50%': { backgroundPosition: '100% 50%' },\n                    '100%': { backgroundPosition: '0% 50%' },\n                  },\n                }}\n              >\n                Advanced Threat Detection Platform\n              </Typography>\n            </Box>\n          </Box>\n\n          {/* Navigation and Controls */}\n          <Box display=\"flex\" alignItems=\"center\" gap={2}>\n            {/* Desktop Navigation */}\n            <Box display={{ xs: 'none', md: 'flex' }} gap={1}>\n              {navigationItems.map((item) => (\n                <Button\n                  key={item.path}\n                  color=\"inherit\"\n                  startIcon={item.icon}\n                  onClick={() => navigate(item.path)}\n                  variant={item.active ? 'contained' : 'text'}\n                  sx={{\n                    borderRadius: 3,\n                    px: 3,\n                    py: 1,\n                    fontWeight: 600,\n                    textTransform: 'none',\n                    background: item.active\n                      ? 'rgba(255,255,255,0.2)'\n                      : 'transparent',\n                    backdropFilter: item.active ? 'blur(10px)' : 'none',\n                    border: item.active ? '1px solid rgba(255,255,255,0.3)' : 'none',\n                    '&:hover': {\n                      background: 'rgba(255,255,255,0.15)',\n                      transform: 'translateY(-1px)',\n                    },\n                    transition: 'all 0.2s ease',\n                  }}\n                >\n                  {item.label}\n                </Button>\n              ))}\n            </Box>\n\n            {/* Mobile Navigation */}\n            <Box display={{ xs: 'flex', md: 'none' }} gap={0.5}>\n              {navigationItems.map((item) => (\n                <Tooltip key={item.path} title={item.label}>\n                  <IconButton\n                    color=\"inherit\"\n                    onClick={() => navigate(item.path)}\n                    sx={{\n                      background: item.active\n                        ? 'rgba(255,255,255,0.2)'\n                        : 'transparent',\n                      backdropFilter: item.active ? 'blur(10px)' : 'none',\n                      border: item.active ? '1px solid rgba(255,255,255,0.3)' : 'none',\n                      '&:hover': {\n                        background: 'rgba(255,255,255,0.15)',\n                      },\n                    }}\n                  >\n                    {item.icon}\n                  </IconButton>\n                </Tooltip>\n              ))}\n            </Box>\n\n            {/* Notifications */}\n            <Tooltip title=\"Notifications\">\n              <IconButton\n                color=\"inherit\"\n                onClick={handleNotificationClick}\n                sx={{\n                  background: 'rgba(255,255,255,0.1)',\n                  '&:hover': {\n                    background: 'rgba(255,255,255,0.2)',\n                  },\n                }}\n              >\n                <Badge badgeContent={unreadCount} color=\"error\">\n                  <NotificationsIcon />\n                </Badge>\n              </IconButton>\n            </Tooltip>\n\n            {/* Settings Menu */}\n            <Tooltip title=\"Settings\">\n              <IconButton\n                color=\"inherit\"\n                onClick={handleSettingsClick}\n                sx={{\n                  background: 'rgba(255,255,255,0.1)',\n                  '&:hover': {\n                    background: 'rgba(255,255,255,0.2)',\n                  },\n                }}\n              >\n                <SettingsIcon />\n              </IconButton>\n            </Tooltip>\n\n            <Menu\n              anchorEl={settingsAnchor}\n              open={Boolean(settingsAnchor)}\n              onClose={handleSettingsClose}\n              anchorOrigin={{\n                vertical: 'bottom',\n                horizontal: 'right',\n              }}\n              transformOrigin={{\n                vertical: 'top',\n                horizontal: 'right',\n              }}\n              PaperProps={{\n                sx: {\n                  mt: 1,\n                  borderRadius: 2,\n                  minWidth: 200,\n                },\n              }}\n            >\n              {/* Settings Options */}\n              <MenuItem onClick={handleOpenSettingsDialog} sx={{ gap: 2 }}>\n                <SettingsIcon />\n                Account & Settings\n              </MenuItem>\n\n              {/* Language Selector */}\n              <MenuItem onClick={handleLanguageClick} sx={{ gap: 2 }}>\n                <LanguageIcon />\n                Language\n              </MenuItem>\n\n              {/* Settings */}\n              <MenuItem onClick={() => setSettingsDialogOpen(true)} sx={{ gap: 2 }}>\n                <SettingsIcon />\n                Settings\n              </MenuItem>\n            </Menu>\n\n            {/* Language Menu */}\n            <Menu\n              anchorEl={languageAnchor}\n              open={Boolean(languageAnchor)}\n              onClose={handleLanguageClose}\n              anchorOrigin={{\n                vertical: 'bottom',\n                horizontal: 'right',\n              }}\n              transformOrigin={{\n                vertical: 'top',\n                horizontal: 'right',\n              }}\n              PaperProps={{\n                sx: {\n                  mt: 1,\n                  borderRadius: 2,\n                },\n              }}\n            >\n              <MenuItem\n                onClick={() => handleLanguageSelect('en')}\n                selected={language === 'en'}\n              >\n                🇺🇸 English\n              </MenuItem>\n              <MenuItem\n                onClick={() => handleLanguageSelect('ar')}\n                selected={language === 'ar'}\n              >\n                🇸🇦 العربية\n              </MenuItem>\n            </Menu>\n\n            {/* Notification Menu */}\n            <Menu\n              anchorEl={notificationAnchor}\n              open={Boolean(notificationAnchor)}\n              onClose={handleNotificationClose}\n              anchorOrigin={{\n                vertical: 'bottom',\n                horizontal: 'right',\n              }}\n              transformOrigin={{\n                vertical: 'top',\n                horizontal: 'right',\n              }}\n              PaperProps={{\n                sx: {\n                  mt: 1,\n                  borderRadius: 3,\n                  minWidth: 350,\n                  maxWidth: 400,\n                  maxHeight: 500,\n                  background: (theme) => theme.palette.mode === 'dark'\n                    ? 'linear-gradient(135deg, rgba(12, 12, 12, 0.98) 0%, rgba(26, 26, 26, 0.98) 100%)'\n                    : 'linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(248, 250, 252, 0.98) 100%)',\n                  backdropFilter: 'blur(25px)',\n                  border: '1px solid rgba(102, 126, 234, 0.2)',\n                  boxShadow: '0 20px 60px rgba(0, 0, 0, 0.3)',\n                },\n              }}\n            >\n              <Box sx={{ p: 2, borderBottom: '1px solid rgba(102, 126, 234, 0.2)' }}>\n                <Typography variant=\"h6\" fontWeight=\"bold\" color=\"primary.main\">\n                  Notifications\n                </Typography>\n                <Typography variant=\"body2\" color=\"text.secondary\">\n                  {unreadCount > 0 ? `${unreadCount} unread notifications` : 'All caught up!'}\n                </Typography>\n              </Box>\n              <Box sx={{ maxHeight: 350, overflow: 'auto' }}>\n                {notificationHistory.length === 0 ? (\n                  <Box sx={{ p: 3, textAlign: 'center' }}>\n                    <NotificationsIcon sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />\n                    <Typography variant=\"body2\" color=\"text.secondary\">\n                      No notifications yet\n                    </Typography>\n                  </Box>\n                ) : (\n                  notificationHistory.map((notification, index) => (\n                    <Box key={notification.id}>\n                      <MenuItem\n                        onClick={() => markNotificationAsRead(notification.id)}\n                        sx={{\n                          py: 2,\n                          px: 3,\n                          alignItems: 'flex-start',\n                          background: !notification.read\n                            ? 'rgba(102, 126, 234, 0.05)'\n                            : 'transparent',\n                          '&:hover': {\n                            background: 'rgba(102, 126, 234, 0.1)',\n                          },\n                        }}\n                      >\n                        <Box sx={{ mr: 2, mt: 0.5 }}>\n                          <Box\n                            sx={{\n                              width: 8,\n                              height: 8,\n                              borderRadius: '50%',\n                              background: notification.type === 'success' ? '#4caf50' :\n                                         notification.type === 'warning' ? '#ff9800' :\n                                         notification.type === 'error' ? '#f44336' : '#2196f3',\n                              opacity: notification.read ? 0.3 : 1,\n                            }}\n                          />\n                        </Box>\n                        <Box sx={{ flex: 1 }}>\n                          <Typography\n                            variant=\"subtitle2\"\n                            fontWeight={notification.read ? 400 : 600}\n                            sx={{ mb: 0.5 }}\n                          >\n                            {notification.title}\n                          </Typography>\n                          <Typography\n                            variant=\"body2\"\n                            color=\"text.secondary\"\n                            sx={{ mb: 1, lineHeight: 1.4 }}\n                          >\n                            {notification.message}\n                          </Typography>\n                          <Typography variant=\"caption\" color=\"text.secondary\">\n                            {new Date(notification.timestamp).toLocaleString()}\n                          </Typography>\n                        </Box>\n                      </MenuItem>\n                      {index < notificationHistory.length - 1 && (\n                        <Divider sx={{ mx: 2 }} />\n                      )}\n                    </Box>\n                  ))\n                )}\n              </Box>\n            </Menu>\n\n          </Box>\n        </Toolbar>\n      </Container>\n\n      {/* Settings Dialog */}\n      <EnhancedSettingsDialog\n        open={settingsDialogOpen}\n        onClose={() => setSettingsDialogOpen(false)}\n      />\n    </AppBar>\n  );\n});\n\nHeader.displayName = 'Header';\n\nexport default Header;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SACEC,MAAM,EACNC,OAAO,EACPC,UAAU,EACVC,UAAU,EACVC,MAAM,EACNC,gBAAgB,EAChBC,GAAG,EACHC,MAAM,EACNC,IAAI,EACJC,QAAQ,EACRC,SAAS,EACTC,OAAO,EACPC,MAAM,EACNC,KAAK,EACLC,OAAO,QACF,eAAe;AACtB,SACEC,QAAQ,IAAIC,YAAY,EACxBC,QAAQ,IAAIC,YAAY,EACxBC,SAAS,IAAIC,aAAa,EAC1BC,QAAQ,IAAIC,YAAY,EACxBC,OAAO,IAAIC,WAAW,EACtBC,IAAI,IAAIC,QAAQ,EAChBC,IAAI,IAAIC,QAAQ,EAChBC,aAAa,IAAIC,iBAAiB,EAClCC,QAAQ,IAAIC,YAAY,EACxBC,IAAI,IAAIC,QAAQ,QACX,qBAAqB;AAC5B,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,SAASC,OAAO,QAAQ,yBAAyB;AACjD,OAAOC,sBAAsB,MAAM,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9D,MAAMC,MAAM,gBAAAC,EAAA,cAAG3C,KAAK,CAAC4C,IAAI,CAAAC,EAAA,GAAAF,EAAA,CAAC,MAAM;EAAAA,EAAA;EAC9B,MAAMG,QAAQ,GAAGV,WAAW,CAAC,CAAC;EAC9B,MAAMW,QAAQ,GAAGV,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEW,QAAQ;IAAEC,QAAQ;IAAEC,WAAW;IAAEC;EAAc,CAAC,GAAGb,OAAO,CAAC,CAAC;EACpE,MAAM,CAACc,cAAc,EAAEC,iBAAiB,CAAC,GAAGrD,KAAK,CAACsD,QAAQ,CAAC,IAAI,CAAC;EAChE,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGxD,KAAK,CAACsD,QAAQ,CAAC,IAAI,CAAC;EAChE,MAAM,CAACG,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG1D,KAAK,CAACsD,QAAQ,CAAC,IAAI,CAAC;EACxE,MAAM,CAACK,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG5D,KAAK,CAACsD,QAAQ,CAAC,KAAK,CAAC;;EAEzE;EACA,MAAM,CAACO,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG9D,KAAK,CAACsD,QAAQ,CAAC,CACnE;IACES,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,8BAA8B;IACrCC,OAAO,EAAE,+DAA+D;IACxEC,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;IACnCC,IAAI,EAAE,SAAS;IACfC,IAAI,EAAE;EACR,CAAC,EACD;IACEP,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,yBAAyB;IAChCC,OAAO,EAAE,uDAAuD;IAChEC,SAAS,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACI,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC,CAACH,WAAW,CAAC,CAAC;IACvDC,IAAI,EAAE,MAAM;IACZC,IAAI,EAAE;EACR,CAAC,EACD;IACEP,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,iBAAiB;IACxBC,OAAO,EAAE,4DAA4D;IACrEC,SAAS,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACI,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC,CAACH,WAAW,CAAC,CAAC;IACvDC,IAAI,EAAE,SAAS;IACfC,IAAI,EAAE;EACR,CAAC,CACF,CAAC;EAEF,MAAME,mBAAmB,GAAIC,KAAK,IAAK;IACrCpB,iBAAiB,CAACoB,KAAK,CAACC,aAAa,CAAC;EACxC,CAAC;EAED,MAAMC,mBAAmB,GAAGA,CAAA,KAAM;IAChCtB,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;EAED,MAAMuB,oBAAoB,GAAIC,IAAI,IAAK;IACrC3B,WAAW,CAAC2B,IAAI,CAAC;IACjBF,mBAAmB,CAAC,CAAC;EACvB,CAAC;EAED,MAAMG,mBAAmB,GAAIL,KAAK,IAAK;IACrCjB,iBAAiB,CAACiB,KAAK,CAACC,aAAa,CAAC;EACxC,CAAC;EAED,MAAMK,mBAAmB,GAAGA,CAAA,KAAM;IAChCvB,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;EAED,MAAMwB,wBAAwB,GAAGA,CAAA,KAAM;IACrCpB,qBAAqB,CAAC,IAAI,CAAC;IAC3BmB,mBAAmB,CAAC,CAAC;EACvB,CAAC;EAED,MAAME,uBAAuB,GAAIR,KAAK,IAAK;IACzCf,qBAAqB,CAACe,KAAK,CAACC,aAAa,CAAC;EAC5C,CAAC;EAED,MAAMQ,uBAAuB,GAAGA,CAAA,KAAM;IACpCxB,qBAAqB,CAAC,IAAI,CAAC;EAC7B,CAAC;EAED,MAAMyB,sBAAsB,GAAIC,cAAc,IAAK;IACjDtB,sBAAsB,CAACuB,IAAI,IACzBA,IAAI,CAACC,GAAG,CAACC,KAAK,IACZA,KAAK,CAACxB,EAAE,KAAKqB,cAAc,GAAG;MAAE,GAAGG,KAAK;MAAEjB,IAAI,EAAE;IAAK,CAAC,GAAGiB,KAC3D,CACF,CAAC;EACH,CAAC;EAED,MAAMC,WAAW,GAAG3B,mBAAmB,CAAC4B,MAAM,CAACF,KAAK,IAAI,CAACA,KAAK,CAACjB,IAAI,CAAC,CAACoB,MAAM;EAE3E,MAAMC,UAAU,GAAG5C,QAAQ,CAAC6C,QAAQ,KAAK,GAAG;EAC5C,MAAMC,mBAAmB,GAAG9C,QAAQ,CAAC6C,QAAQ,KAAK,iBAAiB;EACnE,MAAME,aAAa,GAAG/C,QAAQ,CAAC6C,QAAQ,KAAK,UAAU;EACtD,MAAMG,WAAW,GAAGhD,QAAQ,CAAC6C,QAAQ,KAAK,QAAQ;EAClD,MAAMI,WAAW,GAAGjD,QAAQ,CAAC6C,QAAQ,KAAK,QAAQ;EAElD,MAAMK,eAAe,GAAG,CACtB;IAAEC,KAAK,EAAE,MAAM;IAAEC,IAAI,EAAE,GAAG;IAAEC,IAAI,eAAE3D,OAAA,CAACd,QAAQ;MAAA0E,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,MAAM,EAAEd;EAAW,CAAC,EACpE;IAAEO,KAAK,EAAE,gBAAgB;IAAEC,IAAI,EAAE,iBAAiB;IAAEC,IAAI,eAAE3D,OAAA,CAACxB,YAAY;MAAAoF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,MAAM,EAAEZ;EAAoB,CAAC,EACzG;IAAEK,KAAK,EAAE,SAAS;IAAEC,IAAI,EAAE,UAAU;IAAEC,IAAI,eAAE3D,OAAA,CAAChB,WAAW;MAAA4E,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,MAAM,EAAEX;EAAc,CAAC,EACpF;IAAEI,KAAK,EAAE,OAAO;IAAEC,IAAI,EAAE,QAAQ;IAAEC,IAAI,eAAE3D,OAAA,CAACN,QAAQ;MAAAkE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,MAAM,EAAET;EAAY,CAAC,EAC3E;IAAEE,KAAK,EAAE,OAAO;IAAEC,IAAI,EAAE,QAAQ;IAAEC,IAAI,eAAE3D,OAAA,CAACZ,QAAQ;MAAAwE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,MAAM,EAAEV;EAAY,CAAC,CAC5E;EAED,oBACEtD,OAAA,CAACxC,MAAM;IACLyG,QAAQ,EAAC,QAAQ;IACjBC,SAAS,EAAE,CAAE;IACbC,EAAE,EAAE;MACFC,UAAU,EAAGC,KAAK,IAAKA,KAAK,CAACC,OAAO,CAACC,IAAI,KAAK,MAAM,GAChD,6GAA6G,GAC7G,gEAAgE;MACpEC,cAAc,EAAE,2BAA2B;MAC3CC,YAAY,EAAGJ,KAAK,IAAKA,KAAK,CAACC,OAAO,CAACC,IAAI,KAAK,MAAM,GAClD,oCAAoC,GACpC,oCAAoC;MACxCG,SAAS,EAAGL,KAAK,IAAKA,KAAK,CAACC,OAAO,CAACC,IAAI,KAAK,MAAM,GAC/C,qCAAqC,GACrC,qCAAqC;MACzCN,QAAQ,EAAE,UAAU;MACpBU,KAAK,EAAE,OAAO;MACd,UAAU,EAAE;QACVC,OAAO,EAAE,IAAI;QACbX,QAAQ,EAAE,UAAU;QACpBY,MAAM,EAAE,CAAC;QACTC,IAAI,EAAE,CAAC;QACPC,KAAK,EAAE,CAAC;QACRC,MAAM,EAAE,KAAK;QACbZ,UAAU,EAAGC,KAAK,IAAKA,KAAK,CAACC,OAAO,CAACC,IAAI,KAAK,MAAM,GAChD,uEAAuE,GACvE,wFAAwF;QAC5FU,OAAO,EAAE;MACX;IACF,CAAE;IAAAC,QAAA,gBAEFlF,OAAA,CAAC9B,SAAS;MAACiH,QAAQ,EAAC,IAAI;MAAAD,QAAA,eACtBlF,OAAA,CAACvC,OAAO;QAAC0G,EAAE,EAAE;UAAEiB,cAAc,EAAE,eAAe;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAH,QAAA,gBAEtDlF,OAAA,CAAClC,GAAG;UAACwH,OAAO,EAAC,MAAM;UAACC,UAAU,EAAC,QAAQ;UAACC,GAAG,EAAE,CAAE;UAAAN,QAAA,gBAC7ClF,OAAA,CAACrC,UAAU;YACT8H,IAAI,EAAC,OAAO;YACZd,KAAK,EAAC,SAAS;YACf,cAAW,eAAe;YAC1Be,OAAO,EAAEA,CAAA,KAAMrF,QAAQ,CAAC,GAAG,CAAE;YAC7B8D,EAAE,EAAE;cACFC,UAAU,EAAE,uBAAuB;cACnCI,cAAc,EAAE,YAAY;cAC5B,SAAS,EAAE;gBACTJ,UAAU,EAAE,uBAAuB;gBACnCuB,SAAS,EAAE;cACb,CAAC;cACDC,UAAU,EAAE;YACd,CAAE;YAAAV,QAAA,eAEFlF,OAAA,CAACxB,YAAY;cAACqH,QAAQ,EAAC;YAAO;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvB,CAAC,eACb/D,OAAA,CAAClC,GAAG;YAAAoH,QAAA,gBACFlF,OAAA,CAACtC,UAAU;cACToI,OAAO,EAAC,IAAI;cACZC,SAAS,EAAC,IAAI;cACd5B,EAAE,EAAE;gBACF6B,UAAU,EAAE,GAAG;gBACfC,MAAM,EAAE,SAAS;gBACjBX,OAAO,EAAE;kBAAEY,EAAE,EAAE,MAAM;kBAAEC,EAAE,EAAE;gBAAQ,CAAC;gBACpCxB,KAAK,EAAE,OAAO;gBACdyB,UAAU,EAAE,2BAA2B;gBACvC,SAAS,EAAE;kBACTA,UAAU,EAAE;gBACd,CAAC;gBACDR,UAAU,EAAE;cACd,CAAE;cACFF,OAAO,EAAEA,CAAA,KAAMrF,QAAQ,CAAC,GAAG,CAAE;cAAA6E,QAAA,EAC9B;YAED;cAAAtB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb/D,OAAA,CAACtC,UAAU;cACToI,OAAO,EAAC,IAAI;cACZC,SAAS,EAAC,IAAI;cACd5B,EAAE,EAAE;gBACF6B,UAAU,EAAE,GAAG;gBACfC,MAAM,EAAE,SAAS;gBACjBX,OAAO,EAAE;kBAAEY,EAAE,EAAE,OAAO;kBAAEC,EAAE,EAAE;gBAAO,CAAC;gBACpCxB,KAAK,EAAE;cACT,CAAE;cACFe,OAAO,EAAEA,CAAA,KAAMrF,QAAQ,CAAC,GAAG,CAAE;cAAA6E,QAAA,EAC9B;YAED;cAAAtB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb/D,OAAA,CAACtC,UAAU;cACToI,OAAO,EAAC,SAAS;cACjB3B,EAAE,EAAE;gBACFmB,OAAO,EAAE;kBAAEY,EAAE,EAAE,MAAM;kBAAEC,EAAE,EAAE;gBAAQ,CAAC;gBACpC/B,UAAU,EAAE,yFAAyF;gBACrGiC,cAAc,EAAE,WAAW;gBAC3BC,cAAc,EAAE,MAAM;gBACtBC,oBAAoB,EAAE,MAAM;gBAC5BC,mBAAmB,EAAE,aAAa;gBAClCC,SAAS,EAAE,6BAA6B;gBACxCZ,QAAQ,EAAE,SAAS;gBACnBG,UAAU,EAAE,GAAG;gBACfU,aAAa,EAAE,OAAO;gBACtBC,aAAa,EAAE,WAAW;gBAC1B,uBAAuB,EAAE;kBACvB,IAAI,EAAE;oBAAEC,kBAAkB,EAAE;kBAAS,CAAC;kBACtC,KAAK,EAAE;oBAAEA,kBAAkB,EAAE;kBAAW,CAAC;kBACzC,MAAM,EAAE;oBAAEA,kBAAkB,EAAE;kBAAS;gBACzC;cACF,CAAE;cAAA1B,QAAA,EACH;YAED;cAAAtB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN/D,OAAA,CAAClC,GAAG;UAACwH,OAAO,EAAC,MAAM;UAACC,UAAU,EAAC,QAAQ;UAACC,GAAG,EAAE,CAAE;UAAAN,QAAA,gBAE7ClF,OAAA,CAAClC,GAAG;YAACwH,OAAO,EAAE;cAAEY,EAAE,EAAE,MAAM;cAAEC,EAAE,EAAE;YAAO,CAAE;YAACX,GAAG,EAAE,CAAE;YAAAN,QAAA,EAC9C1B,eAAe,CAACX,GAAG,CAAEgE,IAAI,iBACxB7G,OAAA,CAACjC,MAAM;cAEL4G,KAAK,EAAC,SAAS;cACfmC,SAAS,EAAED,IAAI,CAAClD,IAAK;cACrB+B,OAAO,EAAEA,CAAA,KAAMrF,QAAQ,CAACwG,IAAI,CAACnD,IAAI,CAAE;cACnCoC,OAAO,EAAEe,IAAI,CAAC7C,MAAM,GAAG,WAAW,GAAG,MAAO;cAC5CG,EAAE,EAAE;gBACF4C,YAAY,EAAE,CAAC;gBACfC,EAAE,EAAE,CAAC;gBACL3B,EAAE,EAAE,CAAC;gBACLW,UAAU,EAAE,GAAG;gBACfW,aAAa,EAAE,MAAM;gBACrBvC,UAAU,EAAEyC,IAAI,CAAC7C,MAAM,GACnB,uBAAuB,GACvB,aAAa;gBACjBQ,cAAc,EAAEqC,IAAI,CAAC7C,MAAM,GAAG,YAAY,GAAG,MAAM;gBACnDiD,MAAM,EAAEJ,IAAI,CAAC7C,MAAM,GAAG,iCAAiC,GAAG,MAAM;gBAChE,SAAS,EAAE;kBACTI,UAAU,EAAE,wBAAwB;kBACpCuB,SAAS,EAAE;gBACb,CAAC;gBACDC,UAAU,EAAE;cACd,CAAE;cAAAV,QAAA,EAED2B,IAAI,CAACpD;YAAK,GAvBNoD,IAAI,CAACnD,IAAI;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAwBR,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGN/D,OAAA,CAAClC,GAAG;YAACwH,OAAO,EAAE;cAAEY,EAAE,EAAE,MAAM;cAAEC,EAAE,EAAE;YAAO,CAAE;YAACX,GAAG,EAAE,GAAI;YAAAN,QAAA,EAChD1B,eAAe,CAACX,GAAG,CAAEgE,IAAI,iBACxB7G,OAAA,CAAC7B,OAAO;cAAiBoD,KAAK,EAAEsF,IAAI,CAACpD,KAAM;cAAAyB,QAAA,eACzClF,OAAA,CAACrC,UAAU;gBACTgH,KAAK,EAAC,SAAS;gBACfe,OAAO,EAAEA,CAAA,KAAMrF,QAAQ,CAACwG,IAAI,CAACnD,IAAI,CAAE;gBACnCS,EAAE,EAAE;kBACFC,UAAU,EAAEyC,IAAI,CAAC7C,MAAM,GACnB,uBAAuB,GACvB,aAAa;kBACjBQ,cAAc,EAAEqC,IAAI,CAAC7C,MAAM,GAAG,YAAY,GAAG,MAAM;kBACnDiD,MAAM,EAAEJ,IAAI,CAAC7C,MAAM,GAAG,iCAAiC,GAAG,MAAM;kBAChE,SAAS,EAAE;oBACTI,UAAU,EAAE;kBACd;gBACF,CAAE;gBAAAc,QAAA,EAED2B,IAAI,CAAClD;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA;YAAC,GAhBD8C,IAAI,CAACnD,IAAI;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAiBd,CACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGN/D,OAAA,CAAC7B,OAAO;YAACoD,KAAK,EAAC,eAAe;YAAA2D,QAAA,eAC5BlF,OAAA,CAACrC,UAAU;cACTgH,KAAK,EAAC,SAAS;cACfe,OAAO,EAAElD,uBAAwB;cACjC2B,EAAE,EAAE;gBACFC,UAAU,EAAE,uBAAuB;gBACnC,SAAS,EAAE;kBACTA,UAAU,EAAE;gBACd;cACF,CAAE;cAAAc,QAAA,eAEFlF,OAAA,CAAC3B,KAAK;gBAAC6I,YAAY,EAAEnE,WAAY;gBAAC4B,KAAK,EAAC,OAAO;gBAAAO,QAAA,eAC7ClF,OAAA,CAACV,iBAAiB;kBAAAsE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAGV/D,OAAA,CAAC7B,OAAO;YAACoD,KAAK,EAAC,UAAU;YAAA2D,QAAA,eACvBlF,OAAA,CAACrC,UAAU;cACTgH,KAAK,EAAC,SAAS;cACfe,OAAO,EAAErD,mBAAoB;cAC7B8B,EAAE,EAAE;gBACFC,UAAU,EAAE,uBAAuB;gBACnC,SAAS,EAAE;kBACTA,UAAU,EAAE;gBACd;cACF,CAAE;cAAAc,QAAA,eAEFlF,OAAA,CAACR,YAAY;gBAAAoE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAEV/D,OAAA,CAAChC,IAAI;YACHmJ,QAAQ,EAAErG,cAAe;YACzBsG,IAAI,EAAEC,OAAO,CAACvG,cAAc,CAAE;YAC9BwG,OAAO,EAAEhF,mBAAoB;YAC7BiF,YAAY,EAAE;cACZC,QAAQ,EAAE,QAAQ;cAClBC,UAAU,EAAE;YACd,CAAE;YACFC,eAAe,EAAE;cACfF,QAAQ,EAAE,KAAK;cACfC,UAAU,EAAE;YACd,CAAE;YACFE,UAAU,EAAE;cACVxD,EAAE,EAAE;gBACFyD,EAAE,EAAE,CAAC;gBACLb,YAAY,EAAE,CAAC;gBACfc,QAAQ,EAAE;cACZ;YACF,CAAE;YAAA3C,QAAA,gBAGFlF,OAAA,CAAC/B,QAAQ;cAACyH,OAAO,EAAEnD,wBAAyB;cAAC4B,EAAE,EAAE;gBAAEqB,GAAG,EAAE;cAAE,CAAE;cAAAN,QAAA,gBAC1DlF,OAAA,CAACR,YAAY;gBAAAoE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,sBAElB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eAGX/D,OAAA,CAAC/B,QAAQ;cAACyH,OAAO,EAAE3D,mBAAoB;cAACoC,EAAE,EAAE;gBAAEqB,GAAG,EAAE;cAAE,CAAE;cAAAN,QAAA,gBACrDlF,OAAA,CAAClB,YAAY;gBAAA8E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,YAElB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eAGX/D,OAAA,CAAC/B,QAAQ;cAACyH,OAAO,EAAEA,CAAA,KAAMvE,qBAAqB,CAAC,IAAI,CAAE;cAACgD,EAAE,EAAE;gBAAEqB,GAAG,EAAE;cAAE,CAAE;cAAAN,QAAA,gBACnElF,OAAA,CAACR,YAAY;gBAAAoE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,YAElB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP,CAAC,eAGP/D,OAAA,CAAChC,IAAI;YACHmJ,QAAQ,EAAExG,cAAe;YACzByG,IAAI,EAAEC,OAAO,CAAC1G,cAAc,CAAE;YAC9B2G,OAAO,EAAEpF,mBAAoB;YAC7BqF,YAAY,EAAE;cACZC,QAAQ,EAAE,QAAQ;cAClBC,UAAU,EAAE;YACd,CAAE;YACFC,eAAe,EAAE;cACfF,QAAQ,EAAE,KAAK;cACfC,UAAU,EAAE;YACd,CAAE;YACFE,UAAU,EAAE;cACVxD,EAAE,EAAE;gBACFyD,EAAE,EAAE,CAAC;gBACLb,YAAY,EAAE;cAChB;YACF,CAAE;YAAA7B,QAAA,gBAEFlF,OAAA,CAAC/B,QAAQ;cACPyH,OAAO,EAAEA,CAAA,KAAMvD,oBAAoB,CAAC,IAAI,CAAE;cAC1C2F,QAAQ,EAAEtH,QAAQ,KAAK,IAAK;cAAA0E,QAAA,EAC7B;YAED;cAAAtB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACX/D,OAAA,CAAC/B,QAAQ;cACPyH,OAAO,EAAEA,CAAA,KAAMvD,oBAAoB,CAAC,IAAI,CAAE;cAC1C2F,QAAQ,EAAEtH,QAAQ,KAAK,IAAK;cAAA0E,QAAA,EAC7B;YAED;cAAAtB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP,CAAC,eAGP/D,OAAA,CAAChC,IAAI;YACHmJ,QAAQ,EAAEnG,kBAAmB;YAC7BoG,IAAI,EAAEC,OAAO,CAACrG,kBAAkB,CAAE;YAClCsG,OAAO,EAAE7E,uBAAwB;YACjC8E,YAAY,EAAE;cACZC,QAAQ,EAAE,QAAQ;cAClBC,UAAU,EAAE;YACd,CAAE;YACFC,eAAe,EAAE;cACfF,QAAQ,EAAE,KAAK;cACfC,UAAU,EAAE;YACd,CAAE;YACFE,UAAU,EAAE;cACVxD,EAAE,EAAE;gBACFyD,EAAE,EAAE,CAAC;gBACLb,YAAY,EAAE,CAAC;gBACfc,QAAQ,EAAE,GAAG;gBACb1C,QAAQ,EAAE,GAAG;gBACb4C,SAAS,EAAE,GAAG;gBACd3D,UAAU,EAAGC,KAAK,IAAKA,KAAK,CAACC,OAAO,CAACC,IAAI,KAAK,MAAM,GAChD,iFAAiF,GACjF,uFAAuF;gBAC3FC,cAAc,EAAE,YAAY;gBAC5ByC,MAAM,EAAE,oCAAoC;gBAC5CvC,SAAS,EAAE;cACb;YACF,CAAE;YAAAQ,QAAA,gBAEFlF,OAAA,CAAClC,GAAG;cAACqG,EAAE,EAAE;gBAAE6D,CAAC,EAAE,CAAC;gBAAEvD,YAAY,EAAE;cAAqC,CAAE;cAAAS,QAAA,gBACpElF,OAAA,CAACtC,UAAU;gBAACoI,OAAO,EAAC,IAAI;gBAACE,UAAU,EAAC,MAAM;gBAACrB,KAAK,EAAC,cAAc;gBAAAO,QAAA,EAAC;cAEhE;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACb/D,OAAA,CAACtC,UAAU;gBAACoI,OAAO,EAAC,OAAO;gBAACnB,KAAK,EAAC,gBAAgB;gBAAAO,QAAA,EAC/CnC,WAAW,GAAG,CAAC,GAAG,GAAGA,WAAW,uBAAuB,GAAG;cAAgB;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACN/D,OAAA,CAAClC,GAAG;cAACqG,EAAE,EAAE;gBAAE4D,SAAS,EAAE,GAAG;gBAAEE,QAAQ,EAAE;cAAO,CAAE;cAAA/C,QAAA,EAC3C9D,mBAAmB,CAAC6B,MAAM,KAAK,CAAC,gBAC/BjD,OAAA,CAAClC,GAAG;gBAACqG,EAAE,EAAE;kBAAE6D,CAAC,EAAE,CAAC;kBAAEE,SAAS,EAAE;gBAAS,CAAE;gBAAAhD,QAAA,gBACrClF,OAAA,CAACV,iBAAiB;kBAAC6E,EAAE,EAAE;oBAAE0B,QAAQ,EAAE,EAAE;oBAAElB,KAAK,EAAE,gBAAgB;oBAAEwD,EAAE,EAAE;kBAAE;gBAAE;kBAAAvE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC3E/D,OAAA,CAACtC,UAAU;kBAACoI,OAAO,EAAC,OAAO;kBAACnB,KAAK,EAAC,gBAAgB;kBAAAO,QAAA,EAAC;gBAEnD;kBAAAtB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,GAEN3C,mBAAmB,CAACyB,GAAG,CAAC,CAACuF,YAAY,EAAEC,KAAK,kBAC1CrI,OAAA,CAAClC,GAAG;gBAAAoH,QAAA,gBACFlF,OAAA,CAAC/B,QAAQ;kBACPyH,OAAO,EAAEA,CAAA,KAAMhD,sBAAsB,CAAC0F,YAAY,CAAC9G,EAAE,CAAE;kBACvD6C,EAAE,EAAE;oBACFkB,EAAE,EAAE,CAAC;oBACL2B,EAAE,EAAE,CAAC;oBACLzB,UAAU,EAAE,YAAY;oBACxBnB,UAAU,EAAE,CAACgE,YAAY,CAACvG,IAAI,GAC1B,2BAA2B,GAC3B,aAAa;oBACjB,SAAS,EAAE;sBACTuC,UAAU,EAAE;oBACd;kBACF,CAAE;kBAAAc,QAAA,gBAEFlF,OAAA,CAAClC,GAAG;oBAACqG,EAAE,EAAE;sBAAEmE,EAAE,EAAE,CAAC;sBAAEV,EAAE,EAAE;oBAAI,CAAE;oBAAA1C,QAAA,eAC1BlF,OAAA,CAAClC,GAAG;sBACFqG,EAAE,EAAE;wBACFoE,KAAK,EAAE,CAAC;wBACRvD,MAAM,EAAE,CAAC;wBACT+B,YAAY,EAAE,KAAK;wBACnB3C,UAAU,EAAEgE,YAAY,CAACxG,IAAI,KAAK,SAAS,GAAG,SAAS,GAC5CwG,YAAY,CAACxG,IAAI,KAAK,SAAS,GAAG,SAAS,GAC3CwG,YAAY,CAACxG,IAAI,KAAK,OAAO,GAAG,SAAS,GAAG,SAAS;wBAChEqD,OAAO,EAAEmD,YAAY,CAACvG,IAAI,GAAG,GAAG,GAAG;sBACrC;oBAAE;sBAAA+B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,eACN/D,OAAA,CAAClC,GAAG;oBAACqG,EAAE,EAAE;sBAAEqE,IAAI,EAAE;oBAAE,CAAE;oBAAAtD,QAAA,gBACnBlF,OAAA,CAACtC,UAAU;sBACToI,OAAO,EAAC,WAAW;sBACnBE,UAAU,EAAEoC,YAAY,CAACvG,IAAI,GAAG,GAAG,GAAG,GAAI;sBAC1CsC,EAAE,EAAE;wBAAEgE,EAAE,EAAE;sBAAI,CAAE;sBAAAjD,QAAA,EAEfkD,YAAY,CAAC7G;oBAAK;sBAAAqC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACT,CAAC,eACb/D,OAAA,CAACtC,UAAU;sBACToI,OAAO,EAAC,OAAO;sBACfnB,KAAK,EAAC,gBAAgB;sBACtBR,EAAE,EAAE;wBAAEgE,EAAE,EAAE,CAAC;wBAAEM,UAAU,EAAE;sBAAI,CAAE;sBAAAvD,QAAA,EAE9BkD,YAAY,CAAC5G;oBAAO;sBAAAoC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACX,CAAC,eACb/D,OAAA,CAACtC,UAAU;sBAACoI,OAAO,EAAC,SAAS;sBAACnB,KAAK,EAAC,gBAAgB;sBAAAO,QAAA,EACjD,IAAIxD,IAAI,CAAC0G,YAAY,CAAC3G,SAAS,CAAC,CAACiH,cAAc,CAAC;oBAAC;sBAAA9E,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,EACVsE,KAAK,GAAGjH,mBAAmB,CAAC6B,MAAM,GAAG,CAAC,iBACrCjD,OAAA,CAAC1B,OAAO;kBAAC6F,EAAE,EAAE;oBAAEwE,EAAE,EAAE;kBAAE;gBAAE;kBAAA/E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAC1B;cAAA,GAlDOqE,YAAY,CAAC9G,EAAE;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAmDpB,CACN;YACF;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGZ/D,OAAA,CAACF,sBAAsB;MACrBsH,IAAI,EAAElG,kBAAmB;MACzBoG,OAAO,EAAEA,CAAA,KAAMnG,qBAAqB,CAAC,KAAK;IAAE;MAAAyC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7C,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACI,CAAC;AAEb,CAAC;EAAA,QApekBpE,WAAW,EACXC,WAAW,EAC+BC,OAAO;AAAA,EAkenE,CAAC;EAAA,QApeiBF,WAAW,EACXC,WAAW,EAC+BC,OAAO;AAAA,EAkelE;AAAC+I,GAAA,GAreG3I,MAAM;AAueZA,MAAM,CAAC4I,WAAW,GAAG,QAAQ;AAE7B,eAAe5I,MAAM;AAAC,IAAAG,EAAA,EAAAwI,GAAA;AAAAE,YAAA,CAAA1I,EAAA;AAAA0I,YAAA,CAAAF,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}