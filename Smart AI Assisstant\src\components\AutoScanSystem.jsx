import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Typography,
  Switch,
  FormControlLabel,
  Grid,
  Chip,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Avatar,
  IconButton,
  Tooltip,
  LinearProgress,
  Alert,
  Slider,
  TextField,
  Button,
  Divider,
} from '@mui/material';
import {
  AutoMode as AutoIcon,
  Schedule as ScheduleIcon,
  Speed as SpeedIcon,
  Security as SecurityIcon,
  Notifications as NotificationsIcon,
  Settings as SettingsIcon,
  PlayArrow as PlayIcon,
  Pause as PauseIcon,
  Stop as StopIcon,
  Refresh as RefreshIcon,
  Psychology as AIIcon,
  Shield as ShieldIcon,
} from '@mui/icons-material';

const AutoScanSystem = React.memo(() => {
  const [autoScanEnabled, setAutoScanEnabled] = useState(true);
  const [scanInterval, setScanInterval] = useState(30); // minutes
  const [scanIntensity, setScanIntensity] = useState(2); // 1-3 scale
  const [smartScheduling, setSmartScheduling] = useState(true);
  const [adaptiveScan, setAdaptiveScan] = useState(true);

  const [currentScan, setCurrentScan] = useState({
    isRunning: false,
    progress: 0,
    currentTarget: '',
    threatsFound: 0,
    estimatedTime: 0,
  });

  const [scanQueue, setScanQueue] = useState([
    { type: 'URL', target: 'https://suspicious-site.com', priority: 'high', status: 'pending' },
    { type: 'File', target: 'document.pdf', priority: 'medium', status: 'pending' },
    { type: 'URL', target: 'https://phishing-attempt.net', priority: 'high', status: 'pending' },
    { type: 'File', target: 'software.exe', priority: 'low', status: 'pending' },
  ]);

  const [scanHistory, setScanHistory] = useState([
    {
      timestamp: new Date(Date.now() - 1000 * 60 * 15),
      type: 'Auto Scan',
      targets: 12,
      threats: 2,
      duration: '3m 45s',
      status: 'completed',
    },
    {
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2),
      type: 'Scheduled Scan',
      targets: 8,
      threats: 0,
      duration: '2m 12s',
      status: 'completed',
    },
    {
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 6),
      type: 'Priority Scan',
      targets: 3,
      threats: 1,
      duration: '1m 33s',
      status: 'completed',
    },
  ]);

  const [aiRecommendations, setAiRecommendations] = useState([
    {
      type: 'schedule',
      message: 'Increase scan frequency during peak hours (9-17)',
      confidence: 89,
    },
    {
      type: 'priority',
      message: 'Focus on .exe files - 67% higher threat probability',
      confidence: 94,
    },
    {
      type: 'optimization',
      message: 'Reduce scan intensity for trusted domains',
      confidence: 82,
    },
  ]);

  // Simulate auto-scan progress
  useEffect(() => {
    let interval;
    if (currentScan.isRunning) {
      interval = setInterval(() => {
        setCurrentScan(prev => {
          const newProgress = Math.min(100, prev.progress + Math.random() * 10);
          if (newProgress >= 100) {
            return {
              ...prev,
              progress: 100,
              isRunning: false,
              threatsFound: prev.threatsFound + Math.floor(Math.random() * 3),
            };
          }
          return {
            ...prev,
            progress: newProgress,
            estimatedTime: Math.max(0, prev.estimatedTime - 5),
          };
        });
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [currentScan.isRunning]);

  const startAutoScan = () => {
    setCurrentScan({
      isRunning: true,
      progress: 0,
      currentTarget: scanQueue[0]?.target || 'Multiple targets',
      threatsFound: 0,
      estimatedTime: 180, // 3 minutes
    });
  };

  const pauseAutoScan = () => {
    setCurrentScan(prev => ({ ...prev, isRunning: false }));
  };

  const stopAutoScan = () => {
    setCurrentScan({
      isRunning: false,
      progress: 0,
      currentTarget: '',
      threatsFound: 0,
      estimatedTime: 0,
    });
  };

  const getIntensityLabel = (value) => {
    switch (value) {
      case 1: return 'Light';
      case 2: return 'Balanced';
      case 3: return 'Intensive';
      default: return 'Balanced';
    }
  };

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'high': return 'error';
      case 'medium': return 'warning';
      case 'low': return 'success';
      default: return 'default';
    }
  };

  return (
    <Box data-testid="auto-scan" sx={{ p: 3 }}>
      {/* Auto-Scan Controls - Data Flow */}
      <Card
        sx={{
          mb: 4,
          background: (theme) => theme.palette.mode === 'dark'
            ? 'linear-gradient(135deg, rgba(12, 12, 12, 0.95) 0%, rgba(26, 26, 26, 0.95) 50%, rgba(45, 45, 45, 0.95) 100%)'
            : 'linear-gradient(135deg, rgba(248, 250, 252, 0.95) 0%, rgba(241, 245, 249, 0.95) 50%, rgba(226, 232, 240, 0.95) 100%)',
          backdropFilter: 'blur(25px) saturate(180%)',
          border: '1px solid rgba(139, 92, 246, 0.2)',
          borderRadius: 4,
          position: 'relative',
          overflow: 'hidden',
          boxShadow: '0 8px 32px rgba(139, 92, 246, 0.2)',
          transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
          '&:hover': {
            transform: 'translateY(-2px)',
            boxShadow: '0 16px 48px rgba(139, 92, 246, 0.3)',
          },
          '&::before': {
            content: '""',
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            height: '4px',
            background: 'linear-gradient(90deg, #8b5cf6 0%, #a855f7 50%, #c084fc 100%)',
            backgroundSize: '200% 200%',
            animation: 'dataFlow 5s ease infinite',
          },
          '@keyframes dataFlow': {
            '0%': { backgroundPosition: '0% 50%' },
            '50%': { backgroundPosition: '100% 50%' },
            '100%': { backgroundPosition: '0% 50%' },
          },
        }}
      >
        <CardContent>
          <Box display="flex" alignItems="center" gap={2} mb={3}>
            <Avatar
              sx={{
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                width: 56,
                height: 56,
              }}
            >
              <AutoIcon sx={{ fontSize: 28 }} />
            </Avatar>
            <Box flex={1}>
              <Typography variant="h4" fontWeight="bold" gutterBottom>
                Intelligent Auto-Scan System
              </Typography>
              <Typography variant="body1" color="text.secondary">
                AI-powered automated security scanning with adaptive scheduling
              </Typography>
            </Box>
            <Box display="flex" gap={1}>
              <Tooltip title="Start Auto Scan">
                <IconButton
                  onClick={startAutoScan}
                  disabled={currentScan.isRunning}
                  sx={{
                    background: 'linear-gradient(135deg, #4caf50 0%, #8bc34a 100%)',
                    color: 'white',
                    '&:hover': {
                      background: 'linear-gradient(135deg, #45a049 0%, #7cb342 100%)',
                    },
                  }}
                >
                  <PlayIcon />
                </IconButton>
              </Tooltip>
              <Tooltip title="Pause">
                <IconButton
                  onClick={pauseAutoScan}
                  disabled={!currentScan.isRunning}
                  sx={{
                    background: 'linear-gradient(135deg, #ff9800 0%, #ffc107 100%)',
                    color: 'white',
                    '&:hover': {
                      background: 'linear-gradient(135deg, #f57c00 0%, #ffb300 100%)',
                    },
                  }}
                >
                  <PauseIcon />
                </IconButton>
              </Tooltip>
              <Tooltip title="Stop">
                <IconButton
                  onClick={stopAutoScan}
                  sx={{
                    background: 'linear-gradient(135deg, #f44336 0%, #ff5722 100%)',
                    color: 'white',
                    '&:hover': {
                      background: 'linear-gradient(135deg, #d32f2f 0%, #f4511e 100%)',
                    },
                  }}
                >
                  <StopIcon />
                </IconButton>
              </Tooltip>
            </Box>
          </Box>

          {/* Current Scan Status */}
          {(currentScan.isRunning || currentScan.progress > 0) && (
            <Alert
              severity={currentScan.isRunning ? 'info' : 'success'}
              sx={{ mb: 3 }}
              icon={currentScan.isRunning ? <RefreshIcon /> : <ShieldIcon />}
            >
              <Box>
                <Typography variant="subtitle2" fontWeight="600">
                  {currentScan.isRunning ? 'Scanning in Progress' : 'Scan Completed'}
                </Typography>
                <Typography variant="body2" sx={{ mb: 1 }}>
                  Target: {currentScan.currentTarget}
                </Typography>
                <LinearProgress
                  variant="determinate"
                  value={currentScan.progress}
                  sx={{ mb: 1, height: 6, borderRadius: 3 }}
                />
                <Box display="flex" justifyContent="space-between" alignItems="center">
                  <Typography variant="caption">
                    Progress: {Math.round(currentScan.progress)}%
                  </Typography>
                  <Typography variant="caption">
                    Threats Found: {currentScan.threatsFound}
                  </Typography>
                  {currentScan.isRunning && (
                    <Typography variant="caption">
                      ETA: {Math.floor(currentScan.estimatedTime / 60)}m {currentScan.estimatedTime % 60}s
                    </Typography>
                  )}
                </Box>
              </Box>
            </Alert>
          )}

          {/* Auto-Scan Settings */}
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Typography variant="h6" gutterBottom fontWeight="600">
                Auto-Scan Configuration
              </Typography>
              
              <FormControlLabel
                control={
                  <Switch
                    checked={autoScanEnabled}
                    onChange={(e) => setAutoScanEnabled(e.target.checked)}
                    color="primary"
                  />
                }
                label="Enable Auto-Scan"
                sx={{ mb: 2 }}
              />

              <FormControlLabel
                control={
                  <Switch
                    checked={smartScheduling}
                    onChange={(e) => setSmartScheduling(e.target.checked)}
                    color="primary"
                  />
                }
                label="AI Smart Scheduling"
                sx={{ mb: 2 }}
              />

              <FormControlLabel
                control={
                  <Switch
                    checked={adaptiveScan}
                    onChange={(e) => setAdaptiveScan(e.target.checked)}
                    color="primary"
                  />
                }
                label="Adaptive Scan Intensity"
                sx={{ mb: 3 }}
              />

              <Box mb={3}>
                <Typography variant="body2" gutterBottom>
                  Scan Interval: {scanInterval} minutes
                </Typography>
                <Slider
                  value={scanInterval}
                  onChange={(e, value) => setScanInterval(value)}
                  min={5}
                  max={120}
                  step={5}
                  marks={[
                    { value: 5, label: '5m' },
                    { value: 30, label: '30m' },
                    { value: 60, label: '1h' },
                    { value: 120, label: '2h' },
                  ]}
                  color="primary"
                />
              </Box>

              <Box mb={3}>
                <Typography variant="body2" gutterBottom>
                  Scan Intensity: {getIntensityLabel(scanIntensity)}
                </Typography>
                <Slider
                  value={scanIntensity}
                  onChange={(e, value) => setScanIntensity(value)}
                  min={1}
                  max={3}
                  step={1}
                  marks={[
                    { value: 1, label: 'Light' },
                    { value: 2, label: 'Balanced' },
                    { value: 3, label: 'Intensive' },
                  ]}
                  color="primary"
                />
              </Box>
            </Grid>

            <Grid item xs={12} md={6}>
              <Typography variant="h6" gutterBottom fontWeight="600">
                AI Recommendations
              </Typography>
              
              <List>
                {aiRecommendations.map((rec, index) => (
                  <ListItem
                    key={index}
                    sx={{
                      background: (theme) => theme.palette.mode === 'dark'
                        ? 'rgba(255, 255, 255, 0.05)'
                        : 'rgba(0, 0, 0, 0.02)',
                      borderRadius: 2,
                      mb: 1,
                    }}
                  >
                    <ListItemIcon>
                      <AIIcon color="secondary" />
                    </ListItemIcon>
                    <ListItemText
                      primary={rec.message}
                      secondary={
                        <Chip
                          label={`${rec.confidence}% confidence`}
                          size="small"
                          color={rec.confidence > 90 ? 'success' : 'warning'}
                          variant="outlined"
                        />
                      }
                    />
                  </ListItem>
                ))}
              </List>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      <Grid container spacing={3}>
        {/* Scan Queue */}
        <Grid item xs={12} md={6}>
          <Card
            sx={{
              background: (theme) => theme.palette.mode === 'dark'
                ? 'linear-gradient(135deg, rgba(30, 30, 30, 0.95) 0%, rgba(50, 50, 50, 0.95) 100%)'
                : 'linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%)',
              backdropFilter: 'blur(20px)',
              border: (theme) => `1px solid ${theme.palette.divider}`,
            }}
          >
            <CardContent>
              <Box display="flex" alignItems="center" gap={2} mb={3}>
                <Avatar
                  sx={{
                    background: 'linear-gradient(135deg, #ff9800 0%, #ffc107 100%)',
                    width: 48,
                    height: 48,
                  }}
                >
                  <ScheduleIcon />
                </Avatar>
                <Typography variant="h6" fontWeight="bold">
                  Scan Queue ({scanQueue.length})
                </Typography>
              </Box>

              <List>
                {scanQueue.map((item, index) => (
                  <ListItem
                    key={index}
                    sx={{
                      background: (theme) => theme.palette.mode === 'dark'
                        ? 'rgba(255, 255, 255, 0.05)'
                        : 'rgba(0, 0, 0, 0.02)',
                      borderRadius: 2,
                      mb: 1,
                    }}
                  >
                    <ListItemIcon>
                      <SecurityIcon color="primary" />
                    </ListItemIcon>
                    <ListItemText
                      primary={
                        <Box display="flex" alignItems="center" gap={2}>
                          <Typography variant="body2" fontWeight="600">
                            {item.target}
                          </Typography>
                          <Chip
                            label={item.type}
                            size="small"
                            variant="outlined"
                          />
                          <Chip
                            label={item.priority}
                            size="small"
                            color={getPriorityColor(item.priority)}
                          />
                        </Box>
                      }
                      secondary={`Status: ${item.status}`}
                    />
                  </ListItem>
                ))}
              </List>
            </CardContent>
          </Card>
        </Grid>

        {/* Recent Scans */}
        <Grid item xs={12} md={6}>
          <Card
            sx={{
              background: (theme) => theme.palette.mode === 'dark'
                ? 'linear-gradient(135deg, rgba(30, 30, 30, 0.95) 0%, rgba(50, 50, 50, 0.95) 100%)'
                : 'linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%)',
              backdropFilter: 'blur(20px)',
              border: (theme) => `1px solid ${theme.palette.divider}`,
            }}
          >
            <CardContent>
              <Box display="flex" alignItems="center" gap={2} mb={3}>
                <Avatar
                  sx={{
                    background: 'linear-gradient(135deg, #4caf50 0%, #8bc34a 100%)',
                    width: 48,
                    height: 48,
                  }}
                >
                  <SpeedIcon />
                </Avatar>
                <Typography variant="h6" fontWeight="bold">
                  Recent Auto-Scans
                </Typography>
              </Box>

              <List>
                {scanHistory.map((scan, index) => (
                  <ListItem
                    key={index}
                    sx={{
                      background: (theme) => theme.palette.mode === 'dark'
                        ? 'rgba(255, 255, 255, 0.05)'
                        : 'rgba(0, 0, 0, 0.02)',
                      borderRadius: 2,
                      mb: 1,
                    }}
                  >
                    <ListItemText
                      primary={
                        <Box display="flex" alignItems="center" justifyContent="space-between">
                          <Typography variant="body2" fontWeight="600">
                            {scan.type}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {scan.timestamp.toLocaleTimeString()}
                          </Typography>
                        </Box>
                      }
                      secondary={
                        <Box display="flex" alignItems="center" gap={2} mt={1}>
                          <Typography variant="caption">
                            {scan.targets} targets
                          </Typography>
                          <Typography variant="caption" color={scan.threats > 0 ? 'error.main' : 'success.main'}>
                            {scan.threats} threats
                          </Typography>
                          <Typography variant="caption">
                            {scan.duration}
                          </Typography>
                        </Box>
                      }
                    />
                  </ListItem>
                ))}
              </List>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
});

AutoScanSystem.displayName = 'AutoScanSystem';

export default AutoScanSystem;
