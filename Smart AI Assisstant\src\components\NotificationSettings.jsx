import React, { useState } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondaryAction,
  Switch,
  Divider,
  Chip,
  Alert,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
} from '@mui/material';
import {
  Notifications as NotificationsIcon,
  Email as EmailIcon,
  Sms as SmsIcon,
  Security as SecurityIcon,
  Assessment as AssessmentIcon,
  Update as UpdateIcon,
  Campaign as MarketingIcon,
} from '@mui/icons-material';

const NotificationSettings = () => {
  const [alert, setAlert] = useState({ show: false, message: '', severity: 'success' });
  const [notifications, setNotifications] = useState({
    // Security notifications
    loginAlerts: true,
    passwordChanges: true,
    suspiciousActivity: true,
    
    // Scan notifications
    scanComplete: true,
    threatDetected: true,
    scanScheduled: false,
    
    // System notifications
    systemUpdates: true,
    maintenanceAlerts: true,
    
    // Marketing
    newsletter: false,
    productUpdates: false,
    tips: true,
  });

  const [notificationMethods, setNotificationMethods] = useState({
    email: true,
    push: true,
    sms: false,
  });

  const [frequency, setFrequency] = useState('immediate');

  const showAlert = (message, severity = 'success') => {
    setAlert({ show: true, message, severity });
    setTimeout(() => setAlert({ show: false, message: '', severity: 'success' }), 3000);
  };

  const handleNotificationChange = (key) => {
    setNotifications(prev => ({
      ...prev,
      [key]: !prev[key],
    }));
    
    const action = notifications[key] ? 'disabled' : 'enabled';
    showAlert(`${key.replace(/([A-Z])/g, ' $1').toLowerCase()} notifications ${action}`, 'info');
  };

  const handleMethodChange = (method) => {
    setNotificationMethods(prev => ({
      ...prev,
      [method]: !prev[method],
    }));
    
    const action = notificationMethods[method] ? 'disabled' : 'enabled';
    showAlert(`${method.toUpperCase()} notifications ${action}`, 'info');
  };

  const notificationCategories = [
    {
      title: 'Security Notifications',
      icon: <SecurityIcon />,
      items: [
        {
          key: 'loginAlerts',
          primary: 'Login Alerts',
          secondary: 'Get notified when someone logs into your account',
          important: true,
        },
        {
          key: 'passwordChanges',
          primary: 'Password Changes',
          secondary: 'Alerts when your password is changed',
          important: true,
        },
        {
          key: 'suspiciousActivity',
          primary: 'Suspicious Activity',
          secondary: 'Notifications about unusual account activity',
          important: true,
        },
      ],
    },
    {
      title: 'Scan Notifications',
      icon: <AssessmentIcon />,
      items: [
        {
          key: 'scanComplete',
          primary: 'Scan Complete',
          secondary: 'When your security scans finish',
        },
        {
          key: 'threatDetected',
          primary: 'Threat Detected',
          secondary: 'Immediate alerts for detected threats',
          important: true,
        },
        {
          key: 'scanScheduled',
          primary: 'Scheduled Scans',
          secondary: 'Reminders about upcoming scheduled scans',
        },
      ],
    },
    {
      title: 'System Notifications',
      icon: <UpdateIcon />,
      items: [
        {
          key: 'systemUpdates',
          primary: 'System Updates',
          secondary: 'Important updates and new features',
        },
        {
          key: 'maintenanceAlerts',
          primary: 'Maintenance Alerts',
          secondary: 'Scheduled maintenance notifications',
        },
      ],
    },
    {
      title: 'Marketing & Tips',
      icon: <MarketingIcon />,
      items: [
        {
          key: 'newsletter',
          primary: 'Newsletter',
          secondary: 'Monthly security tips and updates',
        },
        {
          key: 'productUpdates',
          primary: 'Product Updates',
          secondary: 'New features and product announcements',
        },
        {
          key: 'tips',
          primary: 'Security Tips',
          secondary: 'Helpful security tips and best practices',
        },
      ],
    },
  ];

  return (
    <Box>
      {/* Alert */}
      {alert.show && (
        <Alert severity={alert.severity} sx={{ mb: 3 }}>
          {alert.message}
        </Alert>
      )}

      <Typography variant="h4" gutterBottom fontWeight="bold">
        Notification Settings
      </Typography>
      <Typography variant="body1" color="text.secondary" sx={{ mb: 4 }}>
        Customize how and when you receive notifications
      </Typography>

      {/* Notification Methods */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" fontWeight="600" sx={{ mb: 2 }}>
            Notification Methods
          </Typography>
          
          <List>
            <ListItem>
              <ListItemIcon>
                <EmailIcon color="primary" />
              </ListItemIcon>
              <ListItemText
                primary="Email Notifications"
                secondary="Receive notifications via email"
              />
              <ListItemSecondaryAction>
                <Switch
                  checked={notificationMethods.email}
                  onChange={() => handleMethodChange('email')}
                />
              </ListItemSecondaryAction>
            </ListItem>
            
            <Divider />
            
            <ListItem>
              <ListItemIcon>
                <NotificationsIcon color="primary" />
              </ListItemIcon>
              <ListItemText
                primary="Push Notifications"
                secondary="Browser push notifications"
              />
              <ListItemSecondaryAction>
                <Switch
                  checked={notificationMethods.push}
                  onChange={() => handleMethodChange('push')}
                />
              </ListItemSecondaryAction>
            </ListItem>
            
            <Divider />
            
            <ListItem>
              <ListItemIcon>
                <SmsIcon color="primary" />
              </ListItemIcon>
              <ListItemText
                primary="SMS Notifications"
                secondary="Text message alerts for critical events"
              />
              <ListItemSecondaryAction>
                <Switch
                  checked={notificationMethods.sms}
                  onChange={() => handleMethodChange('sms')}
                />
              </ListItemSecondaryAction>
            </ListItem>
          </List>
        </CardContent>
      </Card>

      {/* Notification Frequency */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" fontWeight="600" sx={{ mb: 2 }}>
            Notification Frequency
          </Typography>
          
          <FormControl fullWidth sx={{ maxWidth: 300 }}>
            <InputLabel>Frequency</InputLabel>
            <Select
              value={frequency}
              label="Frequency"
              onChange={(e) => setFrequency(e.target.value)}
            >
              <MenuItem value="immediate">Immediate</MenuItem>
              <MenuItem value="hourly">Hourly Digest</MenuItem>
              <MenuItem value="daily">Daily Digest</MenuItem>
              <MenuItem value="weekly">Weekly Summary</MenuItem>
            </Select>
          </FormControl>
        </CardContent>
      </Card>

      {/* Notification Categories */}
      {notificationCategories.map((category, index) => (
        <Card key={category.title} sx={{ mb: 3 }}>
          <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              {React.cloneElement(category.icon, { sx: { mr: 2, color: 'primary.main' } })}
              <Typography variant="h6" fontWeight="600">
                {category.title}
              </Typography>
            </Box>
            
            <List>
              {category.items.map((item, itemIndex) => (
                <React.Fragment key={item.key}>
                  <ListItem>
                    <ListItemText
                      primary={
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          {item.primary}
                          {item.important && (
                            <Chip
                              label="Important"
                              size="small"
                              color="warning"
                              variant="outlined"
                            />
                          )}
                        </Box>
                      }
                      secondary={item.secondary}
                    />
                    <ListItemSecondaryAction>
                      <Switch
                        checked={notifications[item.key]}
                        onChange={() => handleNotificationChange(item.key)}
                      />
                    </ListItemSecondaryAction>
                  </ListItem>
                  {itemIndex < category.items.length - 1 && <Divider />}
                </React.Fragment>
              ))}
            </List>
          </CardContent>
        </Card>
      ))}

      {/* Quick Actions */}
      <Card>
        <CardContent>
          <Typography variant="h6" fontWeight="600" sx={{ mb: 2 }}>
            Quick Actions
          </Typography>
          
          <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
            <Button
              variant="outlined"
              onClick={() => {
                const allEnabled = Object.fromEntries(
                  Object.keys(notifications).map(key => [key, true])
                );
                setNotifications(allEnabled);
                showAlert('All notifications enabled', 'success');
              }}
              sx={{ borderRadius: 3 }}
            >
              Enable All
            </Button>
            
            <Button
              variant="outlined"
              onClick={() => {
                const essentialOnly = {
                  ...Object.fromEntries(Object.keys(notifications).map(key => [key, false])),
                  loginAlerts: true,
                  passwordChanges: true,
                  suspiciousActivity: true,
                  threatDetected: true,
                };
                setNotifications(essentialOnly);
                showAlert('Only essential notifications enabled', 'info');
              }}
              sx={{ borderRadius: 3 }}
            >
              Essential Only
            </Button>
            
            <Button
              variant="outlined"
              color="error"
              onClick={() => {
                const allDisabled = Object.fromEntries(
                  Object.keys(notifications).map(key => [key, false])
                );
                setNotifications(allDisabled);
                showAlert('All notifications disabled', 'warning');
              }}
              sx={{ borderRadius: 3 }}
            >
              Disable All
            </Button>
          </Box>
        </CardContent>
      </Card>
    </Box>
  );
};

export default NotificationSettings;
