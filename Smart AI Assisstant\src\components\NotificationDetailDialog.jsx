import React from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  Chip,
  IconButton,
  Divider,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Card,
  CardContent,
} from '@mui/material';
import {
  Close as CloseIcon,
  Security as SecurityIcon,
  Warning as WarningIcon,
  Info as InfoIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Schedule as ScheduleIcon,
  Person as PersonIcon,
  Computer as ComputerIcon,
  LocationOn as LocationIcon,
} from '@mui/icons-material';

const NotificationDetailDialog = ({ open, onClose, notification }) => {
  if (!notification) return null;

  const getTypeIcon = (type) => {
    switch (type) {
      case 'success':
        return <CheckCircleIcon sx={{ color: '#4caf50' }} />;
      case 'warning':
        return <WarningIcon sx={{ color: '#ff9800' }} />;
      case 'error':
        return <ErrorIcon sx={{ color: '#f44336' }} />;
      case 'security':
        return <SecurityIcon sx={{ color: '#2196f3' }} />;
      default:
        return <InfoIcon sx={{ color: '#2196f3' }} />;
    }
  };

  const getTypeColor = (type) => {
    switch (type) {
      case 'success':
        return '#4caf50';
      case 'warning':
        return '#ff9800';
      case 'error':
        return '#f44336';
      case 'security':
        return '#2196f3';
      default:
        return '#2196f3';
    }
  };

  const mockDetailedInfo = {
    timestamp: notification.timestamp,
    source: 'AI Security Guard System',
    severity: notification.type === 'error' ? 'High' : notification.type === 'warning' ? 'Medium' : 'Low',
    category: notification.type === 'security' ? 'Security Alert' : 'System Notification',
    affectedSystems: ['Web Scanner', 'Threat Detection Engine'],
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    ipAddress: '*************',
    location: 'New York, USA',
    actions: [
      'View detailed report',
      'Mark as resolved',
      'Configure alerts',
      'Contact support'
    ]
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 4,
          background: (theme) => theme.palette.mode === 'dark'
            ? 'linear-gradient(135deg, rgba(12, 12, 12, 0.98) 0%, rgba(26, 26, 26, 0.98) 100%)'
            : 'linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(248, 250, 252, 0.98) 100%)',
          backdropFilter: 'blur(25px)',
          border: '1px solid rgba(102, 126, 234, 0.2)',
          boxShadow: '0 25px 80px rgba(0, 0, 0, 0.3)',
        },
      }}
    >
      <DialogTitle sx={{ p: 0 }}>
        <Box
          display="flex"
          alignItems="center"
          justifyContent="space-between"
          sx={{
            p: 3,
            background: `linear-gradient(135deg, ${getTypeColor(notification.type)}20 0%, ${getTypeColor(notification.type)}10 100%)`,
            borderBottom: '1px solid',
            borderColor: 'divider',
          }}
        >
          <Box display="flex" alignItems="center" gap={2}>
            {getTypeIcon(notification.type)}
            <Box>
              <Typography variant="h5" fontWeight="bold">
                Notification Details
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {mockDetailedInfo.category}
              </Typography>
            </Box>
          </Box>
          
          <IconButton
            onClick={onClose}
            sx={{
              background: 'rgba(255, 255, 255, 0.1)',
              backdropFilter: 'blur(10px)',
              '&:hover': {
                background: 'rgba(255, 255, 255, 0.2)',
              },
            }}
          >
            <CloseIcon />
          </IconButton>
        </Box>
      </DialogTitle>

      <DialogContent sx={{ p: 0 }}>
        <Box sx={{ p: 3 }}>
          {/* Main Message */}
          <Card sx={{ mb: 3, border: `1px solid ${getTypeColor(notification.type)}40` }}>
            <CardContent>
              <Box display="flex" alignItems="center" gap={2} mb={2}>
                <Typography variant="h6" fontWeight="bold">
                  {notification.title}
                </Typography>
                <Chip
                  label={notification.type.toUpperCase()}
                  size="small"
                  sx={{
                    background: getTypeColor(notification.type),
                    color: 'white',
                    fontWeight: 'bold',
                  }}
                />
                <Chip
                  label={mockDetailedInfo.severity}
                  size="small"
                  color={mockDetailedInfo.severity === 'High' ? 'error' : mockDetailedInfo.severity === 'Medium' ? 'warning' : 'success'}
                  variant="outlined"
                />
              </Box>
              
              <Typography variant="body1" sx={{ mb: 2, lineHeight: 1.6 }}>
                {notification.message}
              </Typography>
              
              <Typography variant="body2" color="text.secondary">
                This notification was generated by our AI-powered security monitoring system 
                to alert you about important events and potential security concerns.
              </Typography>
            </CardContent>
          </Card>

          {/* Detailed Information */}
          <Typography variant="h6" fontWeight="bold" sx={{ mb: 2 }}>
            Detailed Information
          </Typography>
          
          <List sx={{ mb: 3 }}>
            <ListItem>
              <ListItemIcon>
                <ScheduleIcon color="primary" />
              </ListItemIcon>
              <ListItemText
                primary="Timestamp"
                secondary={new Date(notification.timestamp).toLocaleString()}
              />
            </ListItem>
            
            <ListItem>
              <ListItemIcon>
                <ComputerIcon color="primary" />
              </ListItemIcon>
              <ListItemText
                primary="Source"
                secondary={mockDetailedInfo.source}
              />
            </ListItem>
            
            <ListItem>
              <ListItemIcon>
                <PersonIcon color="primary" />
              </ListItemIcon>
              <ListItemText
                primary="User Agent"
                secondary={mockDetailedInfo.userAgent}
              />
            </ListItem>
            
            <ListItem>
              <ListItemIcon>
                <LocationIcon color="primary" />
              </ListItemIcon>
              <ListItemText
                primary="Location"
                secondary={`${mockDetailedInfo.ipAddress} - ${mockDetailedInfo.location}`}
              />
            </ListItem>
          </List>

          <Divider sx={{ my: 3 }} />

          {/* Affected Systems */}
          <Typography variant="h6" fontWeight="bold" sx={{ mb: 2 }}>
            Affected Systems
          </Typography>
          
          <Box display="flex" gap={1} flexWrap="wrap" mb={3}>
            {mockDetailedInfo.affectedSystems.map((system, index) => (
              <Chip
                key={index}
                label={system}
                variant="outlined"
                color="primary"
                size="small"
              />
            ))}
          </Box>

          <Divider sx={{ my: 3 }} />

          {/* Recommended Actions */}
          <Typography variant="h6" fontWeight="bold" sx={{ mb: 2 }}>
            Recommended Actions
          </Typography>
          
          <Box display="flex" gap={2} flexWrap="wrap">
            {mockDetailedInfo.actions.map((action, index) => (
              <Button
                key={index}
                variant="outlined"
                size="small"
                sx={{
                  borderRadius: 3,
                  borderColor: getTypeColor(notification.type),
                  color: getTypeColor(notification.type),
                  '&:hover': {
                    background: `${getTypeColor(notification.type)}10`,
                    borderColor: getTypeColor(notification.type),
                  },
                }}
              >
                {action}
              </Button>
            ))}
          </Box>
        </Box>
      </DialogContent>

      <DialogActions sx={{ p: 3, borderTop: '1px solid', borderColor: 'divider' }}>
        <Button
          onClick={onClose}
          variant="outlined"
          sx={{ borderRadius: 3 }}
        >
          Close
        </Button>
        <Button
          variant="contained"
          sx={{
            borderRadius: 3,
            background: `linear-gradient(135deg, ${getTypeColor(notification.type)} 0%, ${getTypeColor(notification.type)}CC 100%)`,
            '&:hover': {
              background: `linear-gradient(135deg, ${getTypeColor(notification.type)}DD 0%, ${getTypeColor(notification.type)}AA 100%)`,
            },
          }}
        >
          Mark as Resolved
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default NotificationDetailDialog;
