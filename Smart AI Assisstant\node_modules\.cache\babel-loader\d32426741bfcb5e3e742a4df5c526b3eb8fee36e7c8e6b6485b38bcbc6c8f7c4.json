{"ast": null, "code": "var _jsxFileName = \"E:\\\\PythonProjects\\\\React.js\\\\Smart AI Assisstant\\\\src\\\\pages\\\\PlansPage.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Container, Box, Typography, Card, CardContent, Button, Grid, Chip, List, ListItem, ListItemIcon, ListItemText, Switch, Avatar } from '@mui/material';\nimport { Check as CheckIcon, Star as StarIcon, Security as SecurityIcon, Speed as SpeedIcon, Cloud as CloudIcon, Support as SupportIcon, Analytics as AnalyticsIcon, Shield as ShieldIcon } from '@mui/icons-material';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst PlansPage = /*#__PURE__*/_s(/*#__PURE__*/React.memo(_c = _s(() => {\n  _s();\n  const [isAnnual, setIsAnnual] = useState(false);\n  const plans = [{\n    name: 'Starter',\n    description: 'Perfect for individuals and small projects',\n    price: {\n      monthly: 0,\n      annual: 0\n    },\n    gradient: 'linear-gradient(135deg, #4caf50 0%, #8bc34a 100%)',\n    glowColor: 'rgba(76, 175, 80, 0.3)',\n    icon: /*#__PURE__*/_jsxDEV(SecurityIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 39,\n      columnNumber: 13\n    }, this),\n    features: ['100 scans per month', 'Basic threat detection', 'Email support', 'Standard reports', 'Community access'],\n    popular: false\n  }, {\n    name: 'Professional',\n    description: 'Advanced security for growing businesses',\n    price: {\n      monthly: 29,\n      annual: 290\n    },\n    gradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n    glowColor: 'rgba(102, 126, 234, 0.4)',\n    icon: /*#__PURE__*/_jsxDEV(ShieldIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 55,\n      columnNumber: 13\n    }, this),\n    features: ['Unlimited scans', 'AI-powered threat prediction', 'Real-time monitoring', 'Advanced analytics', 'Priority support', 'Custom reports', 'API access'],\n    popular: true\n  }, {\n    name: 'Enterprise',\n    description: 'Complete security solution for large organizations',\n    price: {\n      monthly: 99,\n      annual: 990\n    },\n    gradient: 'linear-gradient(135deg, #9c27b0 0%, #e91e63 100%)',\n    glowColor: 'rgba(156, 39, 176, 0.4)',\n    icon: /*#__PURE__*/_jsxDEV(CloudIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 73,\n      columnNumber: 13\n    }, this),\n    features: ['Everything in Professional', 'White-label solution', 'Custom integrations', 'Dedicated support', 'SLA guarantees', 'Advanced compliance', 'Multi-tenant architecture', 'Custom training'],\n    popular: false\n  }];\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      minHeight: '100vh',\n      background: 'linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 50%, #121212 100%)',\n      position: 'relative',\n      py: 8,\n      '&::before': {\n        content: '\"\"',\n        position: 'absolute',\n        top: 0,\n        left: 0,\n        right: 0,\n        bottom: 0,\n        backgroundImage: `\n            radial-gradient(circle at 20% 20%, rgba(102, 126, 234, 0.08) 0%, transparent 50%),\n            radial-gradient(circle at 80% 80%, rgba(156, 39, 176, 0.08) 0%, transparent 50%),\n            radial-gradient(circle at 50% 10%, rgba(76, 175, 80, 0.06) 0%, transparent 40%)\n          `,\n        zIndex: -1\n      }\n    },\n    children: /*#__PURE__*/_jsxDEV(Container, {\n      maxWidth: \"lg\",\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        textAlign: \"center\",\n        mb: 8,\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h1\",\n          component: \"h1\",\n          gutterBottom: true,\n          fontWeight: \"bold\",\n          sx: {\n            background: 'linear-gradient(45deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%)',\n            backgroundSize: '400% 400%',\n            backgroundClip: 'text',\n            WebkitBackgroundClip: 'text',\n            WebkitTextFillColor: 'transparent',\n            animation: 'gradientText 8s ease infinite',\n            fontSize: {\n              xs: '2.5rem',\n              md: '4rem'\n            },\n            '@keyframes gradientText': {\n              '0%': {\n                backgroundPosition: '0% 50%'\n              },\n              '50%': {\n                backgroundPosition: '100% 50%'\n              },\n              '100%': {\n                backgroundPosition: '0% 50%'\n              }\n            }\n          },\n          children: \"Choose Your Plan\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 114,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h4\",\n          color: \"rgba(255, 255, 255, 0.8)\",\n          maxWidth: \"800px\",\n          mx: \"auto\",\n          mb: 6,\n          sx: {\n            fontSize: {\n              xs: '1.2rem',\n              md: '1.5rem'\n            }\n          },\n          children: \"Advanced cybersecurity solutions for every need\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 137,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          display: \"flex\",\n          alignItems: \"center\",\n          justifyContent: \"center\",\n          gap: 2,\n          mb: 6,\n          sx: {\n            background: 'rgba(255, 255, 255, 0.05)',\n            backdropFilter: 'blur(10px)',\n            border: '1px solid rgba(255, 255, 255, 0.1)',\n            borderRadius: 3,\n            p: 2,\n            maxWidth: 300,\n            mx: 'auto'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            color: \"white\",\n            fontWeight: !isAnnual ? 'bold' : 'normal',\n            children: \"Monthly\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 165,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Switch, {\n            checked: isAnnual,\n            onChange: e => setIsAnnual(e.target.checked),\n            sx: {\n              '& .MuiSwitch-thumb': {\n                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'\n              }\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 168,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            color: \"white\",\n            fontWeight: isAnnual ? 'bold' : 'normal',\n            children: \"Annual\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 177,\n            columnNumber: 13\n          }, this), isAnnual && /*#__PURE__*/_jsxDEV(Chip, {\n            label: \"Save 20%\",\n            size: \"small\",\n            sx: {\n              background: 'linear-gradient(135deg, #4caf50 0%, #8bc34a 100%)',\n              color: 'white',\n              fontWeight: 'bold'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 181,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 149,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 113,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 3,\n        justifyContent: \"center\",\n        alignItems: \"stretch\",\n        sx: {\n          maxWidth: '1200px',\n          margin: '0 auto',\n          '@media (min-width: 900px)': {\n            flexWrap: 'nowrap',\n            '& .MuiGrid-item': {\n              flexBasis: '33.333333%',\n              maxWidth: '33.333333%',\n              flexShrink: 0\n            }\n          }\n        },\n        children: plans.map((plan, index) => /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 12,\n          md: 4,\n          sx: {\n            display: 'flex',\n            justifyContent: 'center',\n            '@media (min-width: 900px)': {\n              flexBasis: '33.333333%',\n              maxWidth: '33.333333%',\n              flexShrink: 0\n            }\n          },\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            sx: {\n              height: '100%',\n              minHeight: '500px',\n              maxWidth: '320px',\n              width: '100%',\n              margin: '0 auto',\n              background: 'linear-gradient(135deg, rgba(12, 12, 12, 0.95) 0%, rgba(26, 26, 26, 0.95) 50%, rgba(45, 45, 45, 0.95) 100%)',\n              backdropFilter: 'blur(25px) saturate(180%)',\n              border: plan.popular ? '2px solid rgba(102, 126, 234, 0.6)' : '1px solid rgba(255, 255, 255, 0.1)',\n              borderRadius: 4,\n              position: 'relative',\n              overflow: 'hidden',\n              boxShadow: plan.popular ? `0 20px 60px ${plan.glowColor}` : '0 8px 32px rgba(0, 0, 0, 0.3)',\n              transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',\n              transform: plan.popular ? 'scale(1.05)' : 'scale(1)',\n              '&:hover': {\n                transform: plan.popular ? 'scale(1.08)' : 'scale(1.03)',\n                boxShadow: `0 30px 80px ${plan.glowColor}`\n              },\n              '&::before': {\n                content: '\"\"',\n                position: 'absolute',\n                top: 0,\n                left: 0,\n                right: 0,\n                height: '4px',\n                background: plan.gradient,\n                backgroundSize: '200% 200%',\n                animation: plan.popular ? 'gradientShift 3s ease infinite' : 'none'\n              },\n              '@keyframes gradientShift': {\n                '0%': {\n                  backgroundPosition: '0% 50%'\n                },\n                '50%': {\n                  backgroundPosition: '100% 50%'\n                },\n                '100%': {\n                  backgroundPosition: '0% 50%'\n                }\n              }\n            },\n            children: [plan.popular && /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                position: 'absolute',\n                top: 20,\n                right: 20,\n                background: 'linear-gradient(135deg, #ff006e 0%, #8338ec 100%)',\n                color: 'white',\n                px: 2,\n                py: 0.5,\n                borderRadius: 2,\n                fontSize: '0.875rem',\n                fontWeight: 'bold',\n                zIndex: 2\n              },\n              children: \"MOST POPULAR\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 273,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(CardContent, {\n              sx: {\n                p: 4,\n                height: '100%',\n                display: 'flex',\n                flexDirection: 'column'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                textAlign: \"center\",\n                mb: 4,\n                children: [/*#__PURE__*/_jsxDEV(Avatar, {\n                  sx: {\n                    background: plan.gradient,\n                    width: 64,\n                    height: 64,\n                    mx: 'auto',\n                    mb: 2,\n                    boxShadow: `0 8px 25px ${plan.glowColor}`\n                  },\n                  children: /*#__PURE__*/React.cloneElement(plan.icon, {\n                    sx: {\n                      fontSize: 32\n                    }\n                  })\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 295,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h4\",\n                  fontWeight: \"bold\",\n                  color: \"white\",\n                  gutterBottom: true,\n                  children: plan.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 308,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body1\",\n                  color: \"rgba(255, 255, 255, 0.7)\",\n                  mb: 3,\n                  children: plan.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 312,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"h2\",\n                    fontWeight: \"bold\",\n                    color: \"white\",\n                    sx: {\n                      background: plan.gradient,\n                      backgroundClip: 'text',\n                      WebkitBackgroundClip: 'text',\n                      WebkitTextFillColor: 'transparent'\n                    },\n                    children: [\"$\", isAnnual ? plan.price.annual : plan.price.monthly, plan.price.monthly > 0 && /*#__PURE__*/_jsxDEV(Typography, {\n                      component: \"span\",\n                      variant: \"h6\",\n                      color: \"rgba(255, 255, 255, 0.6)\",\n                      children: [\"/\", isAnnual ? 'year' : 'month']\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 331,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 318,\n                    columnNumber: 23\n                  }, this), plan.price.monthly === 0 && /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"h6\",\n                    color: \"rgba(255, 255, 255, 0.8)\",\n                    children: \"Free Forever\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 337,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 317,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 294,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(List, {\n                sx: {\n                  flex: 1\n                },\n                children: plan.features.map((feature, featureIndex) => /*#__PURE__*/_jsxDEV(ListItem, {\n                  sx: {\n                    px: 0,\n                    py: 0.5\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                    sx: {\n                      minWidth: 32\n                    },\n                    children: /*#__PURE__*/_jsxDEV(CheckIcon, {\n                      sx: {\n                        color: '#4caf50',\n                        fontSize: 20\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 349,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 348,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                    primary: feature,\n                    sx: {\n                      '& .MuiListItemText-primary': {\n                        color: 'rgba(255, 255, 255, 0.9)',\n                        fontSize: '0.95rem'\n                      }\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 351,\n                    columnNumber: 25\n                  }, this)]\n                }, featureIndex, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 347,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 345,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"contained\",\n                fullWidth: true,\n                size: \"large\",\n                sx: {\n                  mt: 4,\n                  py: 2,\n                  background: plan.gradient,\n                  boxShadow: `0 8px 25px ${plan.glowColor}`,\n                  fontWeight: 'bold',\n                  fontSize: '1.1rem',\n                  '&:hover': {\n                    background: plan.gradient,\n                    filter: 'brightness(1.1)',\n                    transform: 'translateY(-2px)',\n                    boxShadow: `0 12px 35px ${plan.glowColor}`\n                  }\n                },\n                children: plan.price.monthly === 0 ? 'Get Started Free' : 'Choose Plan'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 365,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 292,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 230,\n            columnNumber: 15\n          }, this)\n        }, plan.name, false, {\n          fileName: _jsxFileName,\n          lineNumber: 214,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 195,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 111,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 89,\n    columnNumber: 5\n  }, this);\n}, \"32C5zVy2ApD6gwvYQFfUM/bysm8=\")), \"32C5zVy2ApD6gwvYQFfUM/bysm8=\");\n_c2 = PlansPage;\nPlansPage.displayName = 'PlansPage';\nexport default PlansPage;\nvar _c, _c2;\n$RefreshReg$(_c, \"PlansPage$React.memo\");\n$RefreshReg$(_c2, \"PlansPage\");", "map": {"version": 3, "names": ["React", "useState", "Container", "Box", "Typography", "Card", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Grid", "Chip", "List", "ListItem", "ListItemIcon", "ListItemText", "Switch", "Avatar", "Check", "CheckIcon", "Star", "StarIcon", "Security", "SecurityIcon", "Speed", "SpeedIcon", "Cloud", "CloudIcon", "Support", "SupportIcon", "Analytics", "AnalyticsIcon", "Shield", "ShieldIcon", "jsxDEV", "_jsxDEV", "PlansPage", "_s", "memo", "_c", "isAnnual", "setIsAnnual", "plans", "name", "description", "price", "monthly", "annual", "gradient", "glowColor", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "features", "popular", "sx", "minHeight", "background", "position", "py", "content", "top", "left", "right", "bottom", "backgroundImage", "zIndex", "children", "max<PERSON><PERSON><PERSON>", "textAlign", "mb", "variant", "component", "gutterBottom", "fontWeight", "backgroundSize", "backgroundClip", "WebkitBackgroundClip", "WebkitTextFillColor", "animation", "fontSize", "xs", "md", "backgroundPosition", "color", "mx", "display", "alignItems", "justifyContent", "gap", "<PERSON><PERSON>ilter", "border", "borderRadius", "p", "checked", "onChange", "e", "target", "label", "size", "container", "spacing", "margin", "flexWrap", "flexBasis", "flexShrink", "map", "plan", "index", "item", "sm", "height", "width", "overflow", "boxShadow", "transition", "transform", "px", "flexDirection", "cloneElement", "flex", "feature", "featureIndex", "min<PERSON><PERSON><PERSON>", "primary", "fullWidth", "mt", "filter", "_c2", "displayName", "$RefreshReg$"], "sources": ["E:/PythonProjects/React.js/Smart AI Assisstant/src/pages/PlansPage.jsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  Container,\n  Box,\n  Typography,\n  Card,\n  CardContent,\n  Button,\n  Grid,\n  Chip,\n  List,\n  ListItem,\n  ListItemIcon,\n  ListItemText,\n  Switch,\n  Avatar,\n} from '@mui/material';\nimport {\n  Check as CheckIcon,\n  Star as StarIcon,\n  Security as SecurityIcon,\n  Speed as SpeedIcon,\n  Cloud as CloudIcon,\n  Support as SupportIcon,\n  Analytics as AnalyticsIcon,\n  Shield as ShieldIcon,\n} from '@mui/icons-material';\n\nconst PlansPage = React.memo(() => {\n  const [isAnnual, setIsAnnual] = useState(false);\n\n  const plans = [\n    {\n      name: 'Starter',\n      description: 'Perfect for individuals and small projects',\n      price: { monthly: 0, annual: 0 },\n      gradient: 'linear-gradient(135deg, #4caf50 0%, #8bc34a 100%)',\n      glowColor: 'rgba(76, 175, 80, 0.3)',\n      icon: <SecurityIcon />,\n      features: [\n        '100 scans per month',\n        'Basic threat detection',\n        'Email support',\n        'Standard reports',\n        'Community access',\n      ],\n      popular: false,\n    },\n    {\n      name: 'Professional',\n      description: 'Advanced security for growing businesses',\n      price: { monthly: 29, annual: 290 },\n      gradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n      glowColor: 'rgba(102, 126, 234, 0.4)',\n      icon: <ShieldIcon />,\n      features: [\n        'Unlimited scans',\n        'AI-powered threat prediction',\n        'Real-time monitoring',\n        'Advanced analytics',\n        'Priority support',\n        'Custom reports',\n        'API access',\n      ],\n      popular: true,\n    },\n    {\n      name: 'Enterprise',\n      description: 'Complete security solution for large organizations',\n      price: { monthly: 99, annual: 990 },\n      gradient: 'linear-gradient(135deg, #9c27b0 0%, #e91e63 100%)',\n      glowColor: 'rgba(156, 39, 176, 0.4)',\n      icon: <CloudIcon />,\n      features: [\n        'Everything in Professional',\n        'White-label solution',\n        'Custom integrations',\n        'Dedicated support',\n        'SLA guarantees',\n        'Advanced compliance',\n        'Multi-tenant architecture',\n        'Custom training',\n      ],\n      popular: false,\n    },\n  ];\n\n  return (\n    <Box\n      sx={{\n        minHeight: '100vh',\n        background: 'linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 50%, #121212 100%)',\n        position: 'relative',\n        py: 8,\n        '&::before': {\n          content: '\"\"',\n          position: 'absolute',\n          top: 0,\n          left: 0,\n          right: 0,\n          bottom: 0,\n          backgroundImage: `\n            radial-gradient(circle at 20% 20%, rgba(102, 126, 234, 0.08) 0%, transparent 50%),\n            radial-gradient(circle at 80% 80%, rgba(156, 39, 176, 0.08) 0%, transparent 50%),\n            radial-gradient(circle at 50% 10%, rgba(76, 175, 80, 0.06) 0%, transparent 40%)\n          `,\n          zIndex: -1,\n        },\n      }}\n    >\n      <Container maxWidth=\"lg\">\n        {/* Hero Section */}\n        <Box textAlign=\"center\" mb={8}>\n          <Typography \n            variant=\"h1\" \n            component=\"h1\" \n            gutterBottom \n            fontWeight=\"bold\"\n            sx={{\n              background: 'linear-gradient(45deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%)',\n              backgroundSize: '400% 400%',\n              backgroundClip: 'text',\n              WebkitBackgroundClip: 'text',\n              WebkitTextFillColor: 'transparent',\n              animation: 'gradientText 8s ease infinite',\n              fontSize: { xs: '2.5rem', md: '4rem' },\n              '@keyframes gradientText': {\n                '0%': { backgroundPosition: '0% 50%' },\n                '50%': { backgroundPosition: '100% 50%' },\n                '100%': { backgroundPosition: '0% 50%' },\n              },\n            }}\n          >\n            Choose Your Plan\n          </Typography>\n          \n          <Typography \n            variant=\"h4\" \n            color=\"rgba(255, 255, 255, 0.8)\" \n            maxWidth=\"800px\" \n            mx=\"auto\"\n            mb={6}\n            sx={{ fontSize: { xs: '1.2rem', md: '1.5rem' } }}\n          >\n            Advanced cybersecurity solutions for every need\n          </Typography>\n\n          {/* Billing Toggle */}\n          <Box \n            display=\"flex\" \n            alignItems=\"center\" \n            justifyContent=\"center\" \n            gap={2}\n            mb={6}\n            sx={{\n              background: 'rgba(255, 255, 255, 0.05)',\n              backdropFilter: 'blur(10px)',\n              border: '1px solid rgba(255, 255, 255, 0.1)',\n              borderRadius: 3,\n              p: 2,\n              maxWidth: 300,\n              mx: 'auto',\n            }}\n          >\n            <Typography color=\"white\" fontWeight={!isAnnual ? 'bold' : 'normal'}>\n              Monthly\n            </Typography>\n            <Switch\n              checked={isAnnual}\n              onChange={(e) => setIsAnnual(e.target.checked)}\n              sx={{\n                '& .MuiSwitch-thumb': {\n                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                },\n              }}\n            />\n            <Typography color=\"white\" fontWeight={isAnnual ? 'bold' : 'normal'}>\n              Annual\n            </Typography>\n            {isAnnual && (\n              <Chip \n                label=\"Save 20%\" \n                size=\"small\"\n                sx={{\n                  background: 'linear-gradient(135deg, #4caf50 0%, #8bc34a 100%)',\n                  color: 'white',\n                  fontWeight: 'bold',\n                }}\n              />\n            )}\n          </Box>\n        </Box>\n\n        {/* Plans Grid */}\n        <Grid\n          container\n          spacing={3}\n          justifyContent=\"center\"\n          alignItems=\"stretch\"\n          sx={{\n            maxWidth: '1200px',\n            margin: '0 auto',\n            '@media (min-width: 900px)': {\n              flexWrap: 'nowrap',\n              '& .MuiGrid-item': {\n                flexBasis: '33.333333%',\n                maxWidth: '33.333333%',\n                flexShrink: 0,\n              }\n            }\n          }}\n        >\n          {plans.map((plan, index) => (\n            <Grid\n              item\n              xs={12}\n              sm={12}\n              md={4}\n              key={plan.name}\n              sx={{\n                display: 'flex',\n                justifyContent: 'center',\n                '@media (min-width: 900px)': {\n                  flexBasis: '33.333333%',\n                  maxWidth: '33.333333%',\n                  flexShrink: 0,\n                }\n              }}\n            >\n              <Card\n                sx={{\n                  height: '100%',\n                  minHeight: '500px',\n                  maxWidth: '320px',\n                  width: '100%',\n                  margin: '0 auto',\n                  background: 'linear-gradient(135deg, rgba(12, 12, 12, 0.95) 0%, rgba(26, 26, 26, 0.95) 50%, rgba(45, 45, 45, 0.95) 100%)',\n                  backdropFilter: 'blur(25px) saturate(180%)',\n                  border: plan.popular\n                    ? '2px solid rgba(102, 126, 234, 0.6)'\n                    : '1px solid rgba(255, 255, 255, 0.1)',\n                  borderRadius: 4,\n                  position: 'relative',\n                  overflow: 'hidden',\n                  boxShadow: plan.popular\n                    ? `0 20px 60px ${plan.glowColor}`\n                    : '0 8px 32px rgba(0, 0, 0, 0.3)',\n                  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',\n                  transform: plan.popular ? 'scale(1.05)' : 'scale(1)',\n                  '&:hover': {\n                    transform: plan.popular ? 'scale(1.08)' : 'scale(1.03)',\n                    boxShadow: `0 30px 80px ${plan.glowColor}`,\n                  },\n                  '&::before': {\n                    content: '\"\"',\n                    position: 'absolute',\n                    top: 0,\n                    left: 0,\n                    right: 0,\n                    height: '4px',\n                    background: plan.gradient,\n                    backgroundSize: '200% 200%',\n                    animation: plan.popular ? 'gradientShift 3s ease infinite' : 'none',\n                  },\n                  '@keyframes gradientShift': {\n                    '0%': { backgroundPosition: '0% 50%' },\n                    '50%': { backgroundPosition: '100% 50%' },\n                    '100%': { backgroundPosition: '0% 50%' },\n                  },\n                }}\n              >\n                {plan.popular && (\n                  <Box\n                    sx={{\n                      position: 'absolute',\n                      top: 20,\n                      right: 20,\n                      background: 'linear-gradient(135deg, #ff006e 0%, #8338ec 100%)',\n                      color: 'white',\n                      px: 2,\n                      py: 0.5,\n                      borderRadius: 2,\n                      fontSize: '0.875rem',\n                      fontWeight: 'bold',\n                      zIndex: 2,\n                    }}\n                  >\n                    MOST POPULAR\n                  </Box>\n                )}\n\n                <CardContent sx={{ p: 4, height: '100%', display: 'flex', flexDirection: 'column' }}>\n                  {/* Plan Header */}\n                  <Box textAlign=\"center\" mb={4}>\n                    <Avatar\n                      sx={{\n                        background: plan.gradient,\n                        width: 64,\n                        height: 64,\n                        mx: 'auto',\n                        mb: 2,\n                        boxShadow: `0 8px 25px ${plan.glowColor}`,\n                      }}\n                    >\n                      {React.cloneElement(plan.icon, { sx: { fontSize: 32 } })}\n                    </Avatar>\n                    \n                    <Typography variant=\"h4\" fontWeight=\"bold\" color=\"white\" gutterBottom>\n                      {plan.name}\n                    </Typography>\n                    \n                    <Typography variant=\"body1\" color=\"rgba(255, 255, 255, 0.7)\" mb={3}>\n                      {plan.description}\n                    </Typography>\n\n                    {/* Price */}\n                    <Box>\n                      <Typography \n                        variant=\"h2\" \n                        fontWeight=\"bold\" \n                        color=\"white\"\n                        sx={{ \n                          background: plan.gradient,\n                          backgroundClip: 'text',\n                          WebkitBackgroundClip: 'text',\n                          WebkitTextFillColor: 'transparent',\n                        }}\n                      >\n                        ${isAnnual ? plan.price.annual : plan.price.monthly}\n                        {plan.price.monthly > 0 && (\n                          <Typography component=\"span\" variant=\"h6\" color=\"rgba(255, 255, 255, 0.6)\">\n                            /{isAnnual ? 'year' : 'month'}\n                          </Typography>\n                        )}\n                      </Typography>\n                      {plan.price.monthly === 0 && (\n                        <Typography variant=\"h6\" color=\"rgba(255, 255, 255, 0.8)\">\n                          Free Forever\n                        </Typography>\n                      )}\n                    </Box>\n                  </Box>\n\n                  {/* Features */}\n                  <List sx={{ flex: 1 }}>\n                    {plan.features.map((feature, featureIndex) => (\n                      <ListItem key={featureIndex} sx={{ px: 0, py: 0.5 }}>\n                        <ListItemIcon sx={{ minWidth: 32 }}>\n                          <CheckIcon sx={{ color: '#4caf50', fontSize: 20 }} />\n                        </ListItemIcon>\n                        <ListItemText \n                          primary={feature}\n                          sx={{ \n                            '& .MuiListItemText-primary': { \n                              color: 'rgba(255, 255, 255, 0.9)',\n                              fontSize: '0.95rem',\n                            }\n                          }}\n                        />\n                      </ListItem>\n                    ))}\n                  </List>\n\n                  {/* CTA Button */}\n                  <Button\n                    variant=\"contained\"\n                    fullWidth\n                    size=\"large\"\n                    sx={{\n                      mt: 4,\n                      py: 2,\n                      background: plan.gradient,\n                      boxShadow: `0 8px 25px ${plan.glowColor}`,\n                      fontWeight: 'bold',\n                      fontSize: '1.1rem',\n                      '&:hover': {\n                        background: plan.gradient,\n                        filter: 'brightness(1.1)',\n                        transform: 'translateY(-2px)',\n                        boxShadow: `0 12px 35px ${plan.glowColor}`,\n                      },\n                    }}\n                  >\n                    {plan.price.monthly === 0 ? 'Get Started Free' : 'Choose Plan'}\n                  </Button>\n                </CardContent>\n              </Card>\n            </Grid>\n          ))}\n        </Grid>\n      </Container>\n    </Box>\n  );\n});\n\nPlansPage.displayName = 'PlansPage';\n\nexport default PlansPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,SAAS,EACTC,GAAG,EACHC,UAAU,EACVC,IAAI,EACJC,WAAW,EACXC,MAAM,EACNC,IAAI,EACJC,IAAI,EACJC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,YAAY,EACZC,MAAM,EACNC,MAAM,QACD,eAAe;AACtB,SACEC,KAAK,IAAIC,SAAS,EAClBC,IAAI,IAAIC,QAAQ,EAChBC,QAAQ,IAAIC,YAAY,EACxBC,KAAK,IAAIC,SAAS,EAClBC,KAAK,IAAIC,SAAS,EAClBC,OAAO,IAAIC,WAAW,EACtBC,SAAS,IAAIC,aAAa,EAC1BC,MAAM,IAAIC,UAAU,QACf,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7B,MAAMC,SAAS,gBAAAC,EAAA,cAAGnC,KAAK,CAACoC,IAAI,CAAAC,EAAA,GAAAF,EAAA,CAAC,MAAM;EAAAA,EAAA;EACjC,MAAM,CAACG,QAAQ,EAAEC,WAAW,CAAC,GAAGtC,QAAQ,CAAC,KAAK,CAAC;EAE/C,MAAMuC,KAAK,GAAG,CACZ;IACEC,IAAI,EAAE,SAAS;IACfC,WAAW,EAAE,4CAA4C;IACzDC,KAAK,EAAE;MAAEC,OAAO,EAAE,CAAC;MAAEC,MAAM,EAAE;IAAE,CAAC;IAChCC,QAAQ,EAAE,mDAAmD;IAC7DC,SAAS,EAAE,wBAAwB;IACnCC,IAAI,eAAEf,OAAA,CAACZ,YAAY;MAAA4B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBC,QAAQ,EAAE,CACR,qBAAqB,EACrB,wBAAwB,EACxB,eAAe,EACf,kBAAkB,EAClB,kBAAkB,CACnB;IACDC,OAAO,EAAE;EACX,CAAC,EACD;IACEb,IAAI,EAAE,cAAc;IACpBC,WAAW,EAAE,0CAA0C;IACvDC,KAAK,EAAE;MAAEC,OAAO,EAAE,EAAE;MAAEC,MAAM,EAAE;IAAI,CAAC;IACnCC,QAAQ,EAAE,mDAAmD;IAC7DC,SAAS,EAAE,0BAA0B;IACrCC,IAAI,eAAEf,OAAA,CAACF,UAAU;MAAAkB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACpBC,QAAQ,EAAE,CACR,iBAAiB,EACjB,8BAA8B,EAC9B,sBAAsB,EACtB,oBAAoB,EACpB,kBAAkB,EAClB,gBAAgB,EAChB,YAAY,CACb;IACDC,OAAO,EAAE;EACX,CAAC,EACD;IACEb,IAAI,EAAE,YAAY;IAClBC,WAAW,EAAE,oDAAoD;IACjEC,KAAK,EAAE;MAAEC,OAAO,EAAE,EAAE;MAAEC,MAAM,EAAE;IAAI,CAAC;IACnCC,QAAQ,EAAE,mDAAmD;IAC7DC,SAAS,EAAE,yBAAyB;IACpCC,IAAI,eAAEf,OAAA,CAACR,SAAS;MAAAwB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACnBC,QAAQ,EAAE,CACR,4BAA4B,EAC5B,sBAAsB,EACtB,qBAAqB,EACrB,mBAAmB,EACnB,gBAAgB,EAChB,qBAAqB,EACrB,2BAA2B,EAC3B,iBAAiB,CAClB;IACDC,OAAO,EAAE;EACX,CAAC,CACF;EAED,oBACErB,OAAA,CAAC9B,GAAG;IACFoD,EAAE,EAAE;MACFC,SAAS,EAAE,OAAO;MAClBC,UAAU,EAAE,gEAAgE;MAC5EC,QAAQ,EAAE,UAAU;MACpBC,EAAE,EAAE,CAAC;MACL,WAAW,EAAE;QACXC,OAAO,EAAE,IAAI;QACbF,QAAQ,EAAE,UAAU;QACpBG,GAAG,EAAE,CAAC;QACNC,IAAI,EAAE,CAAC;QACPC,KAAK,EAAE,CAAC;QACRC,MAAM,EAAE,CAAC;QACTC,eAAe,EAAE;AAC3B;AACA;AACA;AACA,WAAW;QACDC,MAAM,EAAE,CAAC;MACX;IACF,CAAE;IAAAC,QAAA,eAEFlC,OAAA,CAAC/B,SAAS;MAACkE,QAAQ,EAAC,IAAI;MAAAD,QAAA,gBAEtBlC,OAAA,CAAC9B,GAAG;QAACkE,SAAS,EAAC,QAAQ;QAACC,EAAE,EAAE,CAAE;QAAAH,QAAA,gBAC5BlC,OAAA,CAAC7B,UAAU;UACTmE,OAAO,EAAC,IAAI;UACZC,SAAS,EAAC,IAAI;UACdC,YAAY;UACZC,UAAU,EAAC,MAAM;UACjBnB,EAAE,EAAE;YACFE,UAAU,EAAE,yFAAyF;YACrGkB,cAAc,EAAE,WAAW;YAC3BC,cAAc,EAAE,MAAM;YACtBC,oBAAoB,EAAE,MAAM;YAC5BC,mBAAmB,EAAE,aAAa;YAClCC,SAAS,EAAE,+BAA+B;YAC1CC,QAAQ,EAAE;cAAEC,EAAE,EAAE,QAAQ;cAAEC,EAAE,EAAE;YAAO,CAAC;YACtC,yBAAyB,EAAE;cACzB,IAAI,EAAE;gBAAEC,kBAAkB,EAAE;cAAS,CAAC;cACtC,KAAK,EAAE;gBAAEA,kBAAkB,EAAE;cAAW,CAAC;cACzC,MAAM,EAAE;gBAAEA,kBAAkB,EAAE;cAAS;YACzC;UACF,CAAE;UAAAhB,QAAA,EACH;QAED;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAEbnB,OAAA,CAAC7B,UAAU;UACTmE,OAAO,EAAC,IAAI;UACZa,KAAK,EAAC,0BAA0B;UAChChB,QAAQ,EAAC,OAAO;UAChBiB,EAAE,EAAC,MAAM;UACTf,EAAE,EAAE,CAAE;UACNf,EAAE,EAAE;YAAEyB,QAAQ,EAAE;cAAEC,EAAE,EAAE,QAAQ;cAAEC,EAAE,EAAE;YAAS;UAAE,CAAE;UAAAf,QAAA,EAClD;QAED;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAGbnB,OAAA,CAAC9B,GAAG;UACFmF,OAAO,EAAC,MAAM;UACdC,UAAU,EAAC,QAAQ;UACnBC,cAAc,EAAC,QAAQ;UACvBC,GAAG,EAAE,CAAE;UACPnB,EAAE,EAAE,CAAE;UACNf,EAAE,EAAE;YACFE,UAAU,EAAE,2BAA2B;YACvCiC,cAAc,EAAE,YAAY;YAC5BC,MAAM,EAAE,oCAAoC;YAC5CC,YAAY,EAAE,CAAC;YACfC,CAAC,EAAE,CAAC;YACJzB,QAAQ,EAAE,GAAG;YACbiB,EAAE,EAAE;UACN,CAAE;UAAAlB,QAAA,gBAEFlC,OAAA,CAAC7B,UAAU;YAACgF,KAAK,EAAC,OAAO;YAACV,UAAU,EAAE,CAACpC,QAAQ,GAAG,MAAM,GAAG,QAAS;YAAA6B,QAAA,EAAC;UAErE;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbnB,OAAA,CAACnB,MAAM;YACLgF,OAAO,EAAExD,QAAS;YAClByD,QAAQ,EAAGC,CAAC,IAAKzD,WAAW,CAACyD,CAAC,CAACC,MAAM,CAACH,OAAO,CAAE;YAC/CvC,EAAE,EAAE;cACF,oBAAoB,EAAE;gBACpBE,UAAU,EAAE;cACd;YACF;UAAE;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACFnB,OAAA,CAAC7B,UAAU;YAACgF,KAAK,EAAC,OAAO;YAACV,UAAU,EAAEpC,QAAQ,GAAG,MAAM,GAAG,QAAS;YAAA6B,QAAA,EAAC;UAEpE;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,EACZd,QAAQ,iBACPL,OAAA,CAACxB,IAAI;YACHyF,KAAK,EAAC,UAAU;YAChBC,IAAI,EAAC,OAAO;YACZ5C,EAAE,EAAE;cACFE,UAAU,EAAE,mDAAmD;cAC/D2B,KAAK,EAAE,OAAO;cACdV,UAAU,EAAE;YACd;UAAE;YAAAzB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACF;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNnB,OAAA,CAACzB,IAAI;QACH4F,SAAS;QACTC,OAAO,EAAE,CAAE;QACXb,cAAc,EAAC,QAAQ;QACvBD,UAAU,EAAC,SAAS;QACpBhC,EAAE,EAAE;UACFa,QAAQ,EAAE,QAAQ;UAClBkC,MAAM,EAAE,QAAQ;UAChB,2BAA2B,EAAE;YAC3BC,QAAQ,EAAE,QAAQ;YAClB,iBAAiB,EAAE;cACjBC,SAAS,EAAE,YAAY;cACvBpC,QAAQ,EAAE,YAAY;cACtBqC,UAAU,EAAE;YACd;UACF;QACF,CAAE;QAAAtC,QAAA,EAED3B,KAAK,CAACkE,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBACrB3E,OAAA,CAACzB,IAAI;UACHqG,IAAI;UACJ5B,EAAE,EAAE,EAAG;UACP6B,EAAE,EAAE,EAAG;UACP5B,EAAE,EAAE,CAAE;UAEN3B,EAAE,EAAE;YACF+B,OAAO,EAAE,MAAM;YACfE,cAAc,EAAE,QAAQ;YACxB,2BAA2B,EAAE;cAC3BgB,SAAS,EAAE,YAAY;cACvBpC,QAAQ,EAAE,YAAY;cACtBqC,UAAU,EAAE;YACd;UACF,CAAE;UAAAtC,QAAA,eAEFlC,OAAA,CAAC5B,IAAI;YACHkD,EAAE,EAAE;cACFwD,MAAM,EAAE,MAAM;cACdvD,SAAS,EAAE,OAAO;cAClBY,QAAQ,EAAE,OAAO;cACjB4C,KAAK,EAAE,MAAM;cACbV,MAAM,EAAE,QAAQ;cAChB7C,UAAU,EAAE,6GAA6G;cACzHiC,cAAc,EAAE,2BAA2B;cAC3CC,MAAM,EAAEgB,IAAI,CAACrD,OAAO,GAChB,oCAAoC,GACpC,oCAAoC;cACxCsC,YAAY,EAAE,CAAC;cACflC,QAAQ,EAAE,UAAU;cACpBuD,QAAQ,EAAE,QAAQ;cAClBC,SAAS,EAAEP,IAAI,CAACrD,OAAO,GACnB,eAAeqD,IAAI,CAAC5D,SAAS,EAAE,GAC/B,+BAA+B;cACnCoE,UAAU,EAAE,uCAAuC;cACnDC,SAAS,EAAET,IAAI,CAACrD,OAAO,GAAG,aAAa,GAAG,UAAU;cACpD,SAAS,EAAE;gBACT8D,SAAS,EAAET,IAAI,CAACrD,OAAO,GAAG,aAAa,GAAG,aAAa;gBACvD4D,SAAS,EAAE,eAAeP,IAAI,CAAC5D,SAAS;cAC1C,CAAC;cACD,WAAW,EAAE;gBACXa,OAAO,EAAE,IAAI;gBACbF,QAAQ,EAAE,UAAU;gBACpBG,GAAG,EAAE,CAAC;gBACNC,IAAI,EAAE,CAAC;gBACPC,KAAK,EAAE,CAAC;gBACRgD,MAAM,EAAE,KAAK;gBACbtD,UAAU,EAAEkD,IAAI,CAAC7D,QAAQ;gBACzB6B,cAAc,EAAE,WAAW;gBAC3BI,SAAS,EAAE4B,IAAI,CAACrD,OAAO,GAAG,gCAAgC,GAAG;cAC/D,CAAC;cACD,0BAA0B,EAAE;gBAC1B,IAAI,EAAE;kBAAE6B,kBAAkB,EAAE;gBAAS,CAAC;gBACtC,KAAK,EAAE;kBAAEA,kBAAkB,EAAE;gBAAW,CAAC;gBACzC,MAAM,EAAE;kBAAEA,kBAAkB,EAAE;gBAAS;cACzC;YACF,CAAE;YAAAhB,QAAA,GAEDwC,IAAI,CAACrD,OAAO,iBACXrB,OAAA,CAAC9B,GAAG;cACFoD,EAAE,EAAE;gBACFG,QAAQ,EAAE,UAAU;gBACpBG,GAAG,EAAE,EAAE;gBACPE,KAAK,EAAE,EAAE;gBACTN,UAAU,EAAE,mDAAmD;gBAC/D2B,KAAK,EAAE,OAAO;gBACdiC,EAAE,EAAE,CAAC;gBACL1D,EAAE,EAAE,GAAG;gBACPiC,YAAY,EAAE,CAAC;gBACfZ,QAAQ,EAAE,UAAU;gBACpBN,UAAU,EAAE,MAAM;gBAClBR,MAAM,EAAE;cACV,CAAE;cAAAC,QAAA,EACH;YAED;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CACN,eAEDnB,OAAA,CAAC3B,WAAW;cAACiD,EAAE,EAAE;gBAAEsC,CAAC,EAAE,CAAC;gBAAEkB,MAAM,EAAE,MAAM;gBAAEzB,OAAO,EAAE,MAAM;gBAAEgC,aAAa,EAAE;cAAS,CAAE;cAAAnD,QAAA,gBAElFlC,OAAA,CAAC9B,GAAG;gBAACkE,SAAS,EAAC,QAAQ;gBAACC,EAAE,EAAE,CAAE;gBAAAH,QAAA,gBAC5BlC,OAAA,CAAClB,MAAM;kBACLwC,EAAE,EAAE;oBACFE,UAAU,EAAEkD,IAAI,CAAC7D,QAAQ;oBACzBkE,KAAK,EAAE,EAAE;oBACTD,MAAM,EAAE,EAAE;oBACV1B,EAAE,EAAE,MAAM;oBACVf,EAAE,EAAE,CAAC;oBACL4C,SAAS,EAAE,cAAcP,IAAI,CAAC5D,SAAS;kBACzC,CAAE;kBAAAoB,QAAA,eAEDnE,KAAK,CAACuH,YAAY,CAACZ,IAAI,CAAC3D,IAAI,EAAE;oBAAEO,EAAE,EAAE;sBAAEyB,QAAQ,EAAE;oBAAG;kBAAE,CAAC;gBAAC;kBAAA/B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClD,CAAC,eAETnB,OAAA,CAAC7B,UAAU;kBAACmE,OAAO,EAAC,IAAI;kBAACG,UAAU,EAAC,MAAM;kBAACU,KAAK,EAAC,OAAO;kBAACX,YAAY;kBAAAN,QAAA,EAClEwC,IAAI,CAAClE;gBAAI;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC,eAEbnB,OAAA,CAAC7B,UAAU;kBAACmE,OAAO,EAAC,OAAO;kBAACa,KAAK,EAAC,0BAA0B;kBAACd,EAAE,EAAE,CAAE;kBAAAH,QAAA,EAChEwC,IAAI,CAACjE;gBAAW;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP,CAAC,eAGbnB,OAAA,CAAC9B,GAAG;kBAAAgE,QAAA,gBACFlC,OAAA,CAAC7B,UAAU;oBACTmE,OAAO,EAAC,IAAI;oBACZG,UAAU,EAAC,MAAM;oBACjBU,KAAK,EAAC,OAAO;oBACb7B,EAAE,EAAE;sBACFE,UAAU,EAAEkD,IAAI,CAAC7D,QAAQ;sBACzB8B,cAAc,EAAE,MAAM;sBACtBC,oBAAoB,EAAE,MAAM;sBAC5BC,mBAAmB,EAAE;oBACvB,CAAE;oBAAAX,QAAA,GACH,GACE,EAAC7B,QAAQ,GAAGqE,IAAI,CAAChE,KAAK,CAACE,MAAM,GAAG8D,IAAI,CAAChE,KAAK,CAACC,OAAO,EAClD+D,IAAI,CAAChE,KAAK,CAACC,OAAO,GAAG,CAAC,iBACrBX,OAAA,CAAC7B,UAAU;sBAACoE,SAAS,EAAC,MAAM;sBAACD,OAAO,EAAC,IAAI;sBAACa,KAAK,EAAC,0BAA0B;sBAAAjB,QAAA,GAAC,GACxE,EAAC7B,QAAQ,GAAG,MAAM,GAAG,OAAO;oBAAA;sBAAAW,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnB,CACb;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACS,CAAC,EACZuD,IAAI,CAAChE,KAAK,CAACC,OAAO,KAAK,CAAC,iBACvBX,OAAA,CAAC7B,UAAU;oBAACmE,OAAO,EAAC,IAAI;oBAACa,KAAK,EAAC,0BAA0B;oBAAAjB,QAAA,EAAC;kBAE1D;oBAAAlB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CACb;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNnB,OAAA,CAACvB,IAAI;gBAAC6C,EAAE,EAAE;kBAAEiE,IAAI,EAAE;gBAAE,CAAE;gBAAArD,QAAA,EACnBwC,IAAI,CAACtD,QAAQ,CAACqD,GAAG,CAAC,CAACe,OAAO,EAAEC,YAAY,kBACvCzF,OAAA,CAACtB,QAAQ;kBAAoB4C,EAAE,EAAE;oBAAE8D,EAAE,EAAE,CAAC;oBAAE1D,EAAE,EAAE;kBAAI,CAAE;kBAAAQ,QAAA,gBAClDlC,OAAA,CAACrB,YAAY;oBAAC2C,EAAE,EAAE;sBAAEoE,QAAQ,EAAE;oBAAG,CAAE;oBAAAxD,QAAA,eACjClC,OAAA,CAAChB,SAAS;sBAACsC,EAAE,EAAE;wBAAE6B,KAAK,EAAE,SAAS;wBAAEJ,QAAQ,EAAE;sBAAG;oBAAE;sBAAA/B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzC,CAAC,eACfnB,OAAA,CAACpB,YAAY;oBACX+G,OAAO,EAAEH,OAAQ;oBACjBlE,EAAE,EAAE;sBACF,4BAA4B,EAAE;wBAC5B6B,KAAK,EAAE,0BAA0B;wBACjCJ,QAAQ,EAAE;sBACZ;oBACF;kBAAE;oBAAA/B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA,GAZWsE,YAAY;kBAAAzE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAajB,CACX;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAGPnB,OAAA,CAAC1B,MAAM;gBACLgE,OAAO,EAAC,WAAW;gBACnBsD,SAAS;gBACT1B,IAAI,EAAC,OAAO;gBACZ5C,EAAE,EAAE;kBACFuE,EAAE,EAAE,CAAC;kBACLnE,EAAE,EAAE,CAAC;kBACLF,UAAU,EAAEkD,IAAI,CAAC7D,QAAQ;kBACzBoE,SAAS,EAAE,cAAcP,IAAI,CAAC5D,SAAS,EAAE;kBACzC2B,UAAU,EAAE,MAAM;kBAClBM,QAAQ,EAAE,QAAQ;kBAClB,SAAS,EAAE;oBACTvB,UAAU,EAAEkD,IAAI,CAAC7D,QAAQ;oBACzBiF,MAAM,EAAE,iBAAiB;oBACzBX,SAAS,EAAE,kBAAkB;oBAC7BF,SAAS,EAAE,eAAeP,IAAI,CAAC5D,SAAS;kBAC1C;gBACF,CAAE;gBAAAoB,QAAA,EAEDwC,IAAI,CAAChE,KAAK,CAACC,OAAO,KAAK,CAAC,GAAG,kBAAkB,GAAG;cAAa;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC,GAxKFuD,IAAI,CAAClE,IAAI;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAyKV,CACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACT,CAAC;AAEV,CAAC,kCAAC;AAAC4E,GAAA,GA7WG9F,SAAS;AA+WfA,SAAS,CAAC+F,WAAW,GAAG,WAAW;AAEnC,eAAe/F,SAAS;AAAC,IAAAG,EAAA,EAAA2F,GAAA;AAAAE,YAAA,CAAA7F,EAAA;AAAA6F,YAAA,CAAAF,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}