{"ast": null, "code": "var _jsxFileName = \"E:\\\\PythonProjects\\\\React.js\\\\Smart AI Assisstant\\\\src\\\\pages\\\\AboutPage.jsx\";\nimport React from 'react';\nimport { Container, Box, Typography, Card, CardContent, Avatar } from '@mui/material';\nimport { Security as SecurityIcon } from '@mui/icons-material';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AboutPage = /*#__PURE__*/React.memo(_c = () => {\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      minHeight: '100vh',\n      display: 'flex',\n      flexDirection: 'column',\n      justifyContent: 'center',\n      alignItems: 'center',\n      background: 'linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 50%, #121212 100%)',\n      position: 'relative',\n      '&::before': {\n        content: '\"\"',\n        position: 'absolute',\n        top: 0,\n        left: 0,\n        right: 0,\n        bottom: 0,\n        backgroundImage: `\n            radial-gradient(circle at 30% 40%, rgba(102, 126, 234, 0.08) 0%, transparent 50%),\n            radial-gradient(circle at 70% 60%, rgba(118, 75, 162, 0.08) 0%, transparent 50%)\n          `,\n        zIndex: -1\n      }\n    },\n    children: /*#__PURE__*/_jsxDEV(Container, {\n      maxWidth: \"lg\",\n      sx: {\n        py: 8\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        display: \"flex\",\n        flexDirection: \"column\",\n        justifyContent: \"center\",\n        alignItems: \"center\",\n        minHeight: \"80vh\",\n        textAlign: \"center\",\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          mb: 8,\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h1\",\n            component: \"h1\",\n            gutterBottom: true,\n            fontWeight: \"bold\",\n            sx: {\n              background: 'linear-gradient(45deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%)',\n              backgroundSize: '400% 400%',\n              backgroundClip: 'text',\n              WebkitBackgroundClip: 'text',\n              WebkitTextFillColor: 'transparent',\n              animation: 'gradientText 8s ease infinite',\n              fontSize: {\n                xs: '2.5rem',\n                md: '4rem'\n              },\n              '@keyframes gradientText': {\n                '0%': {\n                  backgroundPosition: '0% 50%'\n                },\n                '50%': {\n                  backgroundPosition: '100% 50%'\n                },\n                '100%': {\n                  backgroundPosition: '0% 50%'\n                }\n              }\n            },\n            children: \"About AI Security Guard\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 51,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h4\",\n            color: \"rgba(255, 255, 255, 0.8)\",\n            maxWidth: \"900px\",\n            mx: \"auto\",\n            sx: {\n              lineHeight: 1.6,\n              fontSize: {\n                xs: '1.2rem',\n                md: '1.5rem'\n              },\n              mb: 6\n            },\n            children: \"Advanced cybersecurity platform powered by artificial intelligence\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 73,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 50,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Card, {\n          sx: {\n            maxWidth: 800,\n            background: 'linear-gradient(135deg, rgba(12, 12, 12, 0.95) 0%, rgba(26, 26, 26, 0.95) 50%, rgba(45, 45, 45, 0.95) 100%)',\n            backdropFilter: 'blur(25px) saturate(180%)',\n            border: '1px solid rgba(102, 126, 234, 0.2)',\n            borderRadius: 4,\n            position: 'relative',\n            overflow: 'hidden',\n            boxShadow: '0 20px 60px rgba(102, 126, 234, 0.2)',\n            transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',\n            '&:hover': {\n              transform: 'translateY(-8px) scale(1.02)',\n              boxShadow: '0 30px 80px rgba(102, 126, 234, 0.3)'\n            },\n            '&::before': {\n              content: '\"\"',\n              position: 'absolute',\n              top: 0,\n              left: 0,\n              right: 0,\n              height: '4px',\n              background: 'linear-gradient(90deg, #667eea 0%, #764ba2 50%, #f093fb 100%)',\n              backgroundSize: '200% 200%',\n              animation: 'gradientShift 3s ease infinite'\n            },\n            '@keyframes gradientShift': {\n              '0%': {\n                backgroundPosition: '0% 50%'\n              },\n              '50%': {\n                backgroundPosition: '100% 50%'\n              },\n              '100%': {\n                backgroundPosition: '0% 50%'\n              }\n            }\n          },\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            sx: {\n              p: 6\n            },\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              display: \"flex\",\n              alignItems: \"center\",\n              justifyContent: \"center\",\n              mb: 4,\n              children: [/*#__PURE__*/_jsxDEV(Avatar, {\n                sx: {\n                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                  width: 80,\n                  height: 80,\n                  mr: 3,\n                  boxShadow: '0 8px 25px rgba(102, 126, 234, 0.4)'\n                },\n                children: /*#__PURE__*/_jsxDEV(SecurityIcon, {\n                  sx: {\n                    fontSize: 40\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 133,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 124,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h3\",\n                fontWeight: \"bold\",\n                color: \"white\",\n                sx: {\n                  fontSize: {\n                    xs: '1.8rem',\n                    md: '2.5rem'\n                  }\n                },\n                children: \"Security & Compliance\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 135,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 123,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h5\",\n              color: \"rgba(255, 255, 255, 0.9)\",\n              textAlign: \"center\",\n              sx: {\n                lineHeight: 1.8,\n                fontSize: {\n                  xs: '1.1rem',\n                  md: '1.3rem'\n                },\n                fontWeight: 400\n              },\n              children: \"We maintain the highest security standards and compliance certifications to protect your data and ensure regulatory compliance.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 145,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 122,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 89,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 41,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 40,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 16,\n    columnNumber: 5\n  }, this);\n});\n_c2 = AboutPage;\nAboutPage.displayName = 'AboutPage';\nexport default AboutPage;\nvar _c, _c2;\n$RefreshReg$(_c, \"AboutPage$React.memo\");\n$RefreshReg$(_c2, \"AboutPage\");", "map": {"version": 3, "names": ["React", "Container", "Box", "Typography", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Avatar", "Security", "SecurityIcon", "jsxDEV", "_jsxDEV", "AboutPage", "memo", "_c", "sx", "minHeight", "display", "flexDirection", "justifyContent", "alignItems", "background", "position", "content", "top", "left", "right", "bottom", "backgroundImage", "zIndex", "children", "max<PERSON><PERSON><PERSON>", "py", "textAlign", "mb", "variant", "component", "gutterBottom", "fontWeight", "backgroundSize", "backgroundClip", "WebkitBackgroundClip", "WebkitTextFillColor", "animation", "fontSize", "xs", "md", "backgroundPosition", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "color", "mx", "lineHeight", "<PERSON><PERSON>ilter", "border", "borderRadius", "overflow", "boxShadow", "transition", "transform", "height", "p", "width", "mr", "_c2", "displayName", "$RefreshReg$"], "sources": ["E:/PythonProjects/React.js/Smart AI Assisstant/src/pages/AboutPage.jsx"], "sourcesContent": ["import React from 'react';\nimport {\n  Container,\n  Box,\n  Typography,\n  Card,\n  CardContent,\n  Avatar,\n} from '@mui/material';\nimport {\n  Security as SecurityIcon,\n} from '@mui/icons-material';\n\nconst AboutPage = React.memo(() => {\n  return (\n    <Box\n      sx={{\n        minHeight: '100vh',\n        display: 'flex',\n        flexDirection: 'column',\n        justifyContent: 'center',\n        alignItems: 'center',\n        background: 'linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 50%, #121212 100%)',\n        position: 'relative',\n        '&::before': {\n          content: '\"\"',\n          position: 'absolute',\n          top: 0,\n          left: 0,\n          right: 0,\n          bottom: 0,\n          backgroundImage: `\n            radial-gradient(circle at 30% 40%, rgba(102, 126, 234, 0.08) 0%, transparent 50%),\n            radial-gradient(circle at 70% 60%, rgba(118, 75, 162, 0.08) 0%, transparent 50%)\n          `,\n          zIndex: -1,\n        },\n      }}\n    >\n      <Container maxWidth=\"lg\" sx={{ py: 8 }}>\n        <Box \n          display=\"flex\" \n          flexDirection=\"column\" \n          justifyContent=\"center\" \n          alignItems=\"center\" \n          minHeight=\"80vh\"\n          textAlign=\"center\"\n        >\n          {/* Hero Section */}\n          <Box mb={8}>\n            <Typography \n              variant=\"h1\" \n              component=\"h1\" \n              gutterBottom \n              fontWeight=\"bold\"\n              sx={{\n                background: 'linear-gradient(45deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%)',\n                backgroundSize: '400% 400%',\n                backgroundClip: 'text',\n                WebkitBackgroundClip: 'text',\n                WebkitTextFillColor: 'transparent',\n                animation: 'gradientText 8s ease infinite',\n                fontSize: { xs: '2.5rem', md: '4rem' },\n                '@keyframes gradientText': {\n                  '0%': { backgroundPosition: '0% 50%' },\n                  '50%': { backgroundPosition: '100% 50%' },\n                  '100%': { backgroundPosition: '0% 50%' },\n                },\n              }}\n            >\n              About AI Security Guard\n            </Typography>\n            <Typography \n              variant=\"h4\" \n              color=\"rgba(255, 255, 255, 0.8)\" \n              maxWidth=\"900px\" \n              mx=\"auto\"\n              sx={{ \n                lineHeight: 1.6,\n                fontSize: { xs: '1.2rem', md: '1.5rem' },\n                mb: 6,\n              }}\n            >\n              Advanced cybersecurity platform powered by artificial intelligence\n            </Typography>\n          </Box>\n\n          {/* Security & Compliance Card */}\n          <Card\n            sx={{\n              maxWidth: 800,\n              background: 'linear-gradient(135deg, rgba(12, 12, 12, 0.95) 0%, rgba(26, 26, 26, 0.95) 50%, rgba(45, 45, 45, 0.95) 100%)',\n              backdropFilter: 'blur(25px) saturate(180%)',\n              border: '1px solid rgba(102, 126, 234, 0.2)',\n              borderRadius: 4,\n              position: 'relative',\n              overflow: 'hidden',\n              boxShadow: '0 20px 60px rgba(102, 126, 234, 0.2)',\n              transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',\n              '&:hover': {\n                transform: 'translateY(-8px) scale(1.02)',\n                boxShadow: '0 30px 80px rgba(102, 126, 234, 0.3)',\n              },\n              '&::before': {\n                content: '\"\"',\n                position: 'absolute',\n                top: 0,\n                left: 0,\n                right: 0,\n                height: '4px',\n                background: 'linear-gradient(90deg, #667eea 0%, #764ba2 50%, #f093fb 100%)',\n                backgroundSize: '200% 200%',\n                animation: 'gradientShift 3s ease infinite',\n              },\n              '@keyframes gradientShift': {\n                '0%': { backgroundPosition: '0% 50%' },\n                '50%': { backgroundPosition: '100% 50%' },\n                '100%': { backgroundPosition: '0% 50%' },\n              },\n            }}\n          >\n            <CardContent sx={{ p: 6 }}>\n              <Box display=\"flex\" alignItems=\"center\" justifyContent=\"center\" mb={4}>\n                <Avatar\n                  sx={{\n                    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                    width: 80,\n                    height: 80,\n                    mr: 3,\n                    boxShadow: '0 8px 25px rgba(102, 126, 234, 0.4)',\n                  }}\n                >\n                  <SecurityIcon sx={{ fontSize: 40 }} />\n                </Avatar>\n                <Typography \n                  variant=\"h3\" \n                  fontWeight=\"bold\" \n                  color=\"white\"\n                  sx={{ fontSize: { xs: '1.8rem', md: '2.5rem' } }}\n                >\n                  Security & Compliance\n                </Typography>\n              </Box>\n              \n              <Typography \n                variant=\"h5\" \n                color=\"rgba(255, 255, 255, 0.9)\" \n                textAlign=\"center\"\n                sx={{ \n                  lineHeight: 1.8,\n                  fontSize: { xs: '1.1rem', md: '1.3rem' },\n                  fontWeight: 400,\n                }}\n              >\n                We maintain the highest security standards and compliance certifications \n                to protect your data and ensure regulatory compliance.\n              </Typography>\n            </CardContent>\n          </Card>\n        </Box>\n      </Container>\n    </Box>\n  );\n});\n\nAboutPage.displayName = 'AboutPage';\n\nexport default AboutPage;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SACEC,SAAS,EACTC,GAAG,EACHC,UAAU,EACVC,IAAI,EACJC,WAAW,EACXC,MAAM,QACD,eAAe;AACtB,SACEC,QAAQ,IAAIC,YAAY,QACnB,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7B,MAAMC,SAAS,gBAAGX,KAAK,CAACY,IAAI,CAAAC,EAAA,GAACA,CAAA,KAAM;EACjC,oBACEH,OAAA,CAACR,GAAG;IACFY,EAAE,EAAE;MACFC,SAAS,EAAE,OAAO;MAClBC,OAAO,EAAE,MAAM;MACfC,aAAa,EAAE,QAAQ;MACvBC,cAAc,EAAE,QAAQ;MACxBC,UAAU,EAAE,QAAQ;MACpBC,UAAU,EAAE,gEAAgE;MAC5EC,QAAQ,EAAE,UAAU;MACpB,WAAW,EAAE;QACXC,OAAO,EAAE,IAAI;QACbD,QAAQ,EAAE,UAAU;QACpBE,GAAG,EAAE,CAAC;QACNC,IAAI,EAAE,CAAC;QACPC,KAAK,EAAE,CAAC;QACRC,MAAM,EAAE,CAAC;QACTC,eAAe,EAAE;AAC3B;AACA;AACA,WAAW;QACDC,MAAM,EAAE,CAAC;MACX;IACF,CAAE;IAAAC,QAAA,eAEFnB,OAAA,CAACT,SAAS;MAAC6B,QAAQ,EAAC,IAAI;MAAChB,EAAE,EAAE;QAAEiB,EAAE,EAAE;MAAE,CAAE;MAAAF,QAAA,eACrCnB,OAAA,CAACR,GAAG;QACFc,OAAO,EAAC,MAAM;QACdC,aAAa,EAAC,QAAQ;QACtBC,cAAc,EAAC,QAAQ;QACvBC,UAAU,EAAC,QAAQ;QACnBJ,SAAS,EAAC,MAAM;QAChBiB,SAAS,EAAC,QAAQ;QAAAH,QAAA,gBAGlBnB,OAAA,CAACR,GAAG;UAAC+B,EAAE,EAAE,CAAE;UAAAJ,QAAA,gBACTnB,OAAA,CAACP,UAAU;YACT+B,OAAO,EAAC,IAAI;YACZC,SAAS,EAAC,IAAI;YACdC,YAAY;YACZC,UAAU,EAAC,MAAM;YACjBvB,EAAE,EAAE;cACFM,UAAU,EAAE,yFAAyF;cACrGkB,cAAc,EAAE,WAAW;cAC3BC,cAAc,EAAE,MAAM;cACtBC,oBAAoB,EAAE,MAAM;cAC5BC,mBAAmB,EAAE,aAAa;cAClCC,SAAS,EAAE,+BAA+B;cAC1CC,QAAQ,EAAE;gBAAEC,EAAE,EAAE,QAAQ;gBAAEC,EAAE,EAAE;cAAO,CAAC;cACtC,yBAAyB,EAAE;gBACzB,IAAI,EAAE;kBAAEC,kBAAkB,EAAE;gBAAS,CAAC;gBACtC,KAAK,EAAE;kBAAEA,kBAAkB,EAAE;gBAAW,CAAC;gBACzC,MAAM,EAAE;kBAAEA,kBAAkB,EAAE;gBAAS;cACzC;YACF,CAAE;YAAAjB,QAAA,EACH;UAED;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbxC,OAAA,CAACP,UAAU;YACT+B,OAAO,EAAC,IAAI;YACZiB,KAAK,EAAC,0BAA0B;YAChCrB,QAAQ,EAAC,OAAO;YAChBsB,EAAE,EAAC,MAAM;YACTtC,EAAE,EAAE;cACFuC,UAAU,EAAE,GAAG;cACfV,QAAQ,EAAE;gBAAEC,EAAE,EAAE,QAAQ;gBAAEC,EAAE,EAAE;cAAS,CAAC;cACxCZ,EAAE,EAAE;YACN,CAAE;YAAAJ,QAAA,EACH;UAED;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAGNxC,OAAA,CAACN,IAAI;UACHU,EAAE,EAAE;YACFgB,QAAQ,EAAE,GAAG;YACbV,UAAU,EAAE,6GAA6G;YACzHkC,cAAc,EAAE,2BAA2B;YAC3CC,MAAM,EAAE,oCAAoC;YAC5CC,YAAY,EAAE,CAAC;YACfnC,QAAQ,EAAE,UAAU;YACpBoC,QAAQ,EAAE,QAAQ;YAClBC,SAAS,EAAE,sCAAsC;YACjDC,UAAU,EAAE,uCAAuC;YACnD,SAAS,EAAE;cACTC,SAAS,EAAE,8BAA8B;cACzCF,SAAS,EAAE;YACb,CAAC;YACD,WAAW,EAAE;cACXpC,OAAO,EAAE,IAAI;cACbD,QAAQ,EAAE,UAAU;cACpBE,GAAG,EAAE,CAAC;cACNC,IAAI,EAAE,CAAC;cACPC,KAAK,EAAE,CAAC;cACRoC,MAAM,EAAE,KAAK;cACbzC,UAAU,EAAE,+DAA+D;cAC3EkB,cAAc,EAAE,WAAW;cAC3BI,SAAS,EAAE;YACb,CAAC;YACD,0BAA0B,EAAE;cAC1B,IAAI,EAAE;gBAAEI,kBAAkB,EAAE;cAAS,CAAC;cACtC,KAAK,EAAE;gBAAEA,kBAAkB,EAAE;cAAW,CAAC;cACzC,MAAM,EAAE;gBAAEA,kBAAkB,EAAE;cAAS;YACzC;UACF,CAAE;UAAAjB,QAAA,eAEFnB,OAAA,CAACL,WAAW;YAACS,EAAE,EAAE;cAAEgD,CAAC,EAAE;YAAE,CAAE;YAAAjC,QAAA,gBACxBnB,OAAA,CAACR,GAAG;cAACc,OAAO,EAAC,MAAM;cAACG,UAAU,EAAC,QAAQ;cAACD,cAAc,EAAC,QAAQ;cAACe,EAAE,EAAE,CAAE;cAAAJ,QAAA,gBACpEnB,OAAA,CAACJ,MAAM;gBACLQ,EAAE,EAAE;kBACFM,UAAU,EAAE,mDAAmD;kBAC/D2C,KAAK,EAAE,EAAE;kBACTF,MAAM,EAAE,EAAE;kBACVG,EAAE,EAAE,CAAC;kBACLN,SAAS,EAAE;gBACb,CAAE;gBAAA7B,QAAA,eAEFnB,OAAA,CAACF,YAAY;kBAACM,EAAE,EAAE;oBAAE6B,QAAQ,EAAE;kBAAG;gBAAE;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC,CAAC,eACTxC,OAAA,CAACP,UAAU;gBACT+B,OAAO,EAAC,IAAI;gBACZG,UAAU,EAAC,MAAM;gBACjBc,KAAK,EAAC,OAAO;gBACbrC,EAAE,EAAE;kBAAE6B,QAAQ,EAAE;oBAAEC,EAAE,EAAE,QAAQ;oBAAEC,EAAE,EAAE;kBAAS;gBAAE,CAAE;gBAAAhB,QAAA,EAClD;cAED;gBAAAkB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAENxC,OAAA,CAACP,UAAU;cACT+B,OAAO,EAAC,IAAI;cACZiB,KAAK,EAAC,0BAA0B;cAChCnB,SAAS,EAAC,QAAQ;cAClBlB,EAAE,EAAE;gBACFuC,UAAU,EAAE,GAAG;gBACfV,QAAQ,EAAE;kBAAEC,EAAE,EAAE,QAAQ;kBAAEC,EAAE,EAAE;gBAAS,CAAC;gBACxCR,UAAU,EAAE;cACd,CAAE;cAAAR,QAAA,EACH;YAGD;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACT,CAAC;AAEV,CAAC,CAAC;AAACe,GAAA,GAtJGtD,SAAS;AAwJfA,SAAS,CAACuD,WAAW,GAAG,WAAW;AAEnC,eAAevD,SAAS;AAAC,IAAAE,EAAA,EAAAoD,GAAA;AAAAE,YAAA,CAAAtD,EAAA;AAAAsD,YAAA,CAAAF,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}