import React, { useState } from 'react';
import {
  <PERSON>,
  Card,
  Card<PERSON>ontent,
  <PERSON><PERSON><PERSON>,
  Button,
  TextField,
  IconButton,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondaryAction,
  Switch,
  Chip,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  CircularProgress,
  Divider,
} from '@mui/material';
import {
  Security as SecurityIcon,
  Lock as LockIcon,
  Key as KeyIcon,
  Shield as ShieldIcon,
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon,
  Warning as WarningIcon,
  CheckCircle as CheckCircleIcon,
} from '@mui/icons-material';
import { useAuth } from '../contexts/AuthContext';
import authService from '../services/auth';

const SecuritySettings = () => {
  const { user, logout } = useAuth();
  const [changePasswordOpen, setChangePasswordOpen] = useState(false);
  const [passwordForm, setPasswordForm] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: '',
  });
  const [showPasswords, setShowPasswords] = useState({
    current: false,
    new: false,
    confirm: false,
  });
  const [isLoading, setIsLoading] = useState(false);
  const [alert, setAlert] = useState({ show: false, message: '', severity: 'success' });
  const [formErrors, setFormErrors] = useState({});

  // Security settings state
  const [securitySettings, setSecuritySettings] = useState({
    twoFactorAuth: false,
    loginNotifications: true,
    sessionTimeout: true,
    deviceTracking: true,
  });

  const showAlert = (message, severity = 'success') => {
    setAlert({ show: true, message, severity });
    setTimeout(() => setAlert({ show: false, message: '', severity: 'success' }), 5000);
  };

  const handlePasswordChange = async () => {
    // Validate form
    const errors = {};
    
    if (!passwordForm.currentPassword) {
      errors.currentPassword = 'Current password is required';
    }
    
    if (!passwordForm.newPassword) {
      errors.newPassword = 'New password is required';
    } else {
      const validation = authService.validatePassword(passwordForm.newPassword);
      if (!validation.isValid) {
        errors.newPassword = validation.errors[0];
      }
    }
    
    if (!passwordForm.confirmPassword) {
      errors.confirmPassword = 'Please confirm your new password';
    } else if (passwordForm.newPassword !== passwordForm.confirmPassword) {
      errors.confirmPassword = 'Passwords do not match';
    }
    
    if (passwordForm.currentPassword === passwordForm.newPassword) {
      errors.newPassword = 'New password must be different from current password';
    }
    
    setFormErrors(errors);
    
    if (Object.keys(errors).length > 0) {
      return;
    }

    setIsLoading(true);
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      showAlert('Password changed successfully!', 'success');
      setChangePasswordOpen(false);
      setPasswordForm({
        currentPassword: '',
        newPassword: '',
        confirmPassword: '',
      });
      setFormErrors({});
    } catch (error) {
      showAlert('Failed to change password. Please try again.', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSecuritySettingChange = (setting) => {
    setSecuritySettings(prev => ({
      ...prev,
      [setting]: !prev[setting],
    }));
    
    showAlert(`${setting.replace(/([A-Z])/g, ' $1').toLowerCase()} ${securitySettings[setting] ? 'disabled' : 'enabled'}`, 'info');
  };

  const getPasswordStrength = (password) => {
    if (!password) return { strength: 0, label: 'No password', color: 'default' };
    
    let strength = 0;
    if (password.length >= 8) strength++;
    if (/[A-Z]/.test(password)) strength++;
    if (/[a-z]/.test(password)) strength++;
    if (/\d/.test(password)) strength++;
    if (/[@$!%*?&]/.test(password)) strength++;
    
    const levels = [
      { strength: 0, label: 'Very Weak', color: 'error' },
      { strength: 1, label: 'Weak', color: 'error' },
      { strength: 2, label: 'Fair', color: 'warning' },
      { strength: 3, label: 'Good', color: 'info' },
      { strength: 4, label: 'Strong', color: 'success' },
      { strength: 5, label: 'Very Strong', color: 'success' },
    ];
    
    return levels[strength];
  };

  const passwordStrength = getPasswordStrength(passwordForm.newPassword);

  return (
    <Box>
      {/* Alert */}
      {alert.show && (
        <Alert severity={alert.severity} sx={{ mb: 3 }}>
          {alert.message}
        </Alert>
      )}

      <Typography variant="h4" gutterBottom fontWeight="bold">
        Security Settings
      </Typography>
      <Typography variant="body1" color="text.secondary" sx={{ mb: 4 }}>
        Manage your account security and privacy settings
      </Typography>

      {/* Password Security */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <LockIcon sx={{ mr: 2, color: 'primary.main' }} />
            <Typography variant="h6" fontWeight="600">
              Password Security
            </Typography>
          </Box>
          
          <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
            Keep your account secure with a strong password
          </Typography>
          
          <Button
            variant="outlined"
            startIcon={<KeyIcon />}
            onClick={() => setChangePasswordOpen(true)}
            sx={{ borderRadius: 3 }}
          >
            Change Password
          </Button>
        </CardContent>
      </Card>

      {/* Two-Factor Authentication */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <ShieldIcon sx={{ mr: 2, color: 'primary.main' }} />
            <Typography variant="h6" fontWeight="600">
              Two-Factor Authentication
            </Typography>
          </Box>
          
          <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
            Add an extra layer of security to your account
          </Typography>
          
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <Box>
              <Typography variant="body1" fontWeight="500">
                SMS Authentication
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Receive codes via text message
              </Typography>
            </Box>
            <Switch
              checked={securitySettings.twoFactorAuth}
              onChange={() => handleSecuritySettingChange('twoFactorAuth')}
            />
          </Box>
        </CardContent>
      </Card>

      {/* Security Preferences */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <SecurityIcon sx={{ mr: 2, color: 'primary.main' }} />
            <Typography variant="h6" fontWeight="600">
              Security Preferences
            </Typography>
          </Box>
          
          <List>
            <ListItem>
              <ListItemText
                primary="Login Notifications"
                secondary="Get notified when someone logs into your account"
              />
              <ListItemSecondaryAction>
                <Switch
                  checked={securitySettings.loginNotifications}
                  onChange={() => handleSecuritySettingChange('loginNotifications')}
                />
              </ListItemSecondaryAction>
            </ListItem>
            
            <Divider />
            
            <ListItem>
              <ListItemText
                primary="Session Timeout"
                secondary="Automatically log out after period of inactivity"
              />
              <ListItemSecondaryAction>
                <Switch
                  checked={securitySettings.sessionTimeout}
                  onChange={() => handleSecuritySettingChange('sessionTimeout')}
                />
              </ListItemSecondaryAction>
            </ListItem>
            
            <Divider />
            
            <ListItem>
              <ListItemText
                primary="Device Tracking"
                secondary="Keep track of devices that access your account"
              />
              <ListItemSecondaryAction>
                <Switch
                  checked={securitySettings.deviceTracking}
                  onChange={() => handleSecuritySettingChange('deviceTracking')}
                />
              </ListItemSecondaryAction>
            </ListItem>
          </List>
        </CardContent>
      </Card>

      {/* Account Actions */}
      <Card>
        <CardContent>
          <Typography variant="h6" fontWeight="600" sx={{ mb: 2 }}>
            Account Actions
          </Typography>
          
          <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
            <Button
              variant="outlined"
              color="error"
              onClick={logout}
              sx={{ borderRadius: 3 }}
            >
              Sign Out
            </Button>
            
            <Button
              variant="outlined"
              color="warning"
              sx={{ borderRadius: 3 }}
            >
              Deactivate Account
            </Button>
          </Box>
        </CardContent>
      </Card>

      {/* Change Password Dialog */}
      <Dialog
        open={changePasswordOpen}
        onClose={() => setChangePasswordOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          <Typography variant="h6" fontWeight="bold">
            Change Password
          </Typography>
        </DialogTitle>
        
        <DialogContent>
          <TextField
            fullWidth
            label="Current Password"
            type={showPasswords.current ? 'text' : 'password'}
            value={passwordForm.currentPassword}
            onChange={(e) => {
              setPasswordForm({ ...passwordForm, currentPassword: e.target.value });
              if (formErrors.currentPassword) {
                setFormErrors({ ...formErrors, currentPassword: '' });
              }
            }}
            margin="normal"
            error={!!formErrors.currentPassword}
            helperText={formErrors.currentPassword}
            InputProps={{
              endAdornment: (
                <IconButton
                  onClick={() => setShowPasswords({ ...showPasswords, current: !showPasswords.current })}
                >
                  {showPasswords.current ? <VisibilityOffIcon /> : <VisibilityIcon />}
                </IconButton>
              ),
            }}
          />
          
          <TextField
            fullWidth
            label="New Password"
            type={showPasswords.new ? 'text' : 'password'}
            value={passwordForm.newPassword}
            onChange={(e) => {
              setPasswordForm({ ...passwordForm, newPassword: e.target.value });
              if (formErrors.newPassword) {
                setFormErrors({ ...formErrors, newPassword: '' });
              }
            }}
            margin="normal"
            error={!!formErrors.newPassword}
            helperText={formErrors.newPassword}
            InputProps={{
              endAdornment: (
                <IconButton
                  onClick={() => setShowPasswords({ ...showPasswords, new: !showPasswords.new })}
                >
                  {showPasswords.new ? <VisibilityOffIcon /> : <VisibilityIcon />}
                </IconButton>
              ),
            }}
          />
          
          {passwordForm.newPassword && (
            <Box sx={{ mt: 1, mb: 2 }}>
              <Chip
                label={`Password Strength: ${passwordStrength.label}`}
                color={passwordStrength.color}
                size="small"
                icon={passwordStrength.strength >= 4 ? <CheckCircleIcon /> : <WarningIcon />}
              />
            </Box>
          )}
          
          <TextField
            fullWidth
            label="Confirm New Password"
            type={showPasswords.confirm ? 'text' : 'password'}
            value={passwordForm.confirmPassword}
            onChange={(e) => {
              setPasswordForm({ ...passwordForm, confirmPassword: e.target.value });
              if (formErrors.confirmPassword) {
                setFormErrors({ ...formErrors, confirmPassword: '' });
              }
            }}
            margin="normal"
            error={!!formErrors.confirmPassword}
            helperText={formErrors.confirmPassword}
            InputProps={{
              endAdornment: (
                <IconButton
                  onClick={() => setShowPasswords({ ...showPasswords, confirm: !showPasswords.confirm })}
                >
                  {showPasswords.confirm ? <VisibilityOffIcon /> : <VisibilityIcon />}
                </IconButton>
              ),
            }}
          />
        </DialogContent>
        
        <DialogActions sx={{ p: 3 }}>
          <Button
            onClick={() => setChangePasswordOpen(false)}
            disabled={isLoading}
          >
            Cancel
          </Button>
          <Button
            variant="contained"
            onClick={handlePasswordChange}
            disabled={isLoading}
            startIcon={isLoading ? <CircularProgress size={20} /> : <LockIcon />}
          >
            {isLoading ? 'Changing...' : 'Change Password'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default SecuritySettings;
