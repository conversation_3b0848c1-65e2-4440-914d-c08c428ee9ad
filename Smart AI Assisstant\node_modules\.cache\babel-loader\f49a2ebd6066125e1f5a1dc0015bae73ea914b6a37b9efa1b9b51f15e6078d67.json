{"ast": null, "code": "var _jsxFileName = \"E:\\\\PythonProjects\\\\React.js\\\\Smart AI Assisstant\\\\src\\\\contexts\\\\ScanContext.jsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useReducer, useCallback } from 'react';\n\n// Initial state\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst initialState = {\n  scanHistory: [],\n  currentScan: null,\n  isScanning: false,\n  threatLevel: 'low',\n  darkMode: true,\n  // Force dark mode only\n  language: 'en',\n  notifications: []\n};\n\n// Action types\nconst actionTypes = {\n  START_SCAN: 'START_SCAN',\n  COMPLETE_SCAN: 'COMPLETE_SCAN',\n  ADD_TO_HISTORY: 'ADD_TO_HISTORY',\n  CLEAR_HISTORY: 'CLEAR_HISTORY',\n  SET_THREAT_LEVEL: 'SET_THREAT_LEVEL',\n  TOGGLE_DARK_MODE: 'TOGGLE_DARK_MODE',\n  SET_LANGUAGE: 'SET_LANGUAGE',\n  ADD_NOTIFICATION: 'ADD_NOTIFICATION',\n  REMOVE_NOTIFICATION: 'REMOVE_NOTIFICATION',\n  SET_CURRENT_SCAN: 'SET_CURRENT_SCAN'\n};\n\n// Reducer function\nconst scanReducer = (state, action) => {\n  switch (action.type) {\n    case actionTypes.START_SCAN:\n      return {\n        ...state,\n        isScanning: true,\n        currentScan: {\n          id: Date.now(),\n          type: action.payload.type,\n          target: action.payload.target,\n          startTime: new Date().toISOString(),\n          status: 'scanning'\n        }\n      };\n    case actionTypes.COMPLETE_SCAN:\n      return {\n        ...state,\n        isScanning: false,\n        currentScan: {\n          ...state.currentScan,\n          ...action.payload,\n          endTime: new Date().toISOString(),\n          status: 'completed'\n        }\n      };\n    case actionTypes.ADD_TO_HISTORY:\n      return {\n        ...state,\n        scanHistory: [action.payload, ...state.scanHistory].slice(0, 100) // Keep last 100 scans\n      };\n    case actionTypes.CLEAR_HISTORY:\n      return {\n        ...state,\n        scanHistory: []\n      };\n    case actionTypes.SET_THREAT_LEVEL:\n      return {\n        ...state,\n        threatLevel: action.payload\n      };\n    case actionTypes.TOGGLE_DARK_MODE:\n      return {\n        ...state,\n        darkMode: true // Always keep dark mode\n      };\n    case actionTypes.SET_LANGUAGE:\n      return {\n        ...state,\n        language: action.payload\n      };\n    case actionTypes.ADD_NOTIFICATION:\n      return {\n        ...state,\n        notifications: [...state.notifications, {\n          id: Date.now(),\n          ...action.payload,\n          timestamp: new Date().toISOString()\n        }]\n      };\n    case actionTypes.REMOVE_NOTIFICATION:\n      return {\n        ...state,\n        notifications: state.notifications.filter(notification => notification.id !== action.payload)\n      };\n    case actionTypes.SET_CURRENT_SCAN:\n      return {\n        ...state,\n        currentScan: action.payload\n      };\n    default:\n      return state;\n  }\n};\n\n// Create context\nconst ScanContext = /*#__PURE__*/createContext();\n\n// Provider component\nexport const ScanProvider = ({\n  children\n}) => {\n  _s();\n  const [state, dispatch] = useReducer(scanReducer, initialState);\n\n  // Action creators\n  const startScan = useCallback((type, target) => {\n    dispatch({\n      type: actionTypes.START_SCAN,\n      payload: {\n        type,\n        target\n      }\n    });\n  }, []);\n  const completeScan = useCallback(results => {\n    dispatch({\n      type: actionTypes.COMPLETE_SCAN,\n      payload: results\n    });\n  }, []);\n  const addToHistory = useCallback(scanResult => {\n    dispatch({\n      type: actionTypes.ADD_TO_HISTORY,\n      payload: scanResult\n    });\n  }, []);\n  const clearHistory = useCallback(() => {\n    dispatch({\n      type: actionTypes.CLEAR_HISTORY\n    });\n  }, []);\n  const setThreatLevel = useCallback(level => {\n    dispatch({\n      type: actionTypes.SET_THREAT_LEVEL,\n      payload: level\n    });\n  }, []);\n  const toggleDarkMode = useCallback(() => {\n    dispatch({\n      type: actionTypes.TOGGLE_DARK_MODE\n    });\n  }, []);\n  const setLanguage = useCallback(language => {\n    dispatch({\n      type: actionTypes.SET_LANGUAGE,\n      payload: language\n    });\n  }, []);\n  const addNotification = useCallback(notification => {\n    dispatch({\n      type: actionTypes.ADD_NOTIFICATION,\n      payload: notification\n    });\n  }, []);\n  const removeNotification = useCallback(id => {\n    dispatch({\n      type: actionTypes.REMOVE_NOTIFICATION,\n      payload: id\n    });\n  }, []);\n  const setCurrentScan = useCallback(scan => {\n    dispatch({\n      type: actionTypes.SET_CURRENT_SCAN,\n      payload: scan\n    });\n  }, []);\n  const value = {\n    ...state,\n    startScan,\n    completeScan,\n    addToHistory,\n    clearHistory,\n    setThreatLevel,\n    toggleDarkMode,\n    setLanguage,\n    addNotification,\n    removeNotification,\n    setCurrentScan\n  };\n  return /*#__PURE__*/_jsxDEV(ScanContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 204,\n    columnNumber: 10\n  }, this);\n};\n\n// Custom hook to use the scan context\n_s(ScanProvider, \"ElmYA0Xb++NCr4kbzHzuWoFP+9Y=\");\n_c = ScanProvider;\nexport const useScan = () => {\n  _s2();\n  const context = useContext(ScanContext);\n  if (!context) {\n    throw new Error('useScan must be used within a ScanProvider');\n  }\n  return context;\n};\n_s2(useScan, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"ScanProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useReducer", "useCallback", "jsxDEV", "_jsxDEV", "initialState", "scanHistory", "currentScan", "isScanning", "threatLevel", "darkMode", "language", "notifications", "actionTypes", "START_SCAN", "COMPLETE_SCAN", "ADD_TO_HISTORY", "CLEAR_HISTORY", "SET_THREAT_LEVEL", "TOGGLE_DARK_MODE", "SET_LANGUAGE", "ADD_NOTIFICATION", "REMOVE_NOTIFICATION", "SET_CURRENT_SCAN", "scanReducer", "state", "action", "type", "id", "Date", "now", "payload", "target", "startTime", "toISOString", "status", "endTime", "slice", "timestamp", "filter", "notification", "ScanContext", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "children", "_s", "dispatch", "startScan", "completeScan", "results", "addToHistory", "scanResult", "clearHistory", "setThreatLevel", "level", "toggleDarkMode", "setLanguage", "addNotification", "removeNotification", "setCurrentScan", "scan", "value", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "useScan", "_s2", "context", "Error", "$RefreshReg$"], "sources": ["E:/PythonProjects/React.js/Smart AI Assisstant/src/contexts/ScanContext.jsx"], "sourcesContent": ["import React, { createContext, useContext, useReducer, useCallback } from 'react';\n\n// Initial state\nconst initialState = {\n  scanHistory: [],\n  currentScan: null,\n  isScanning: false,\n  threatLevel: 'low',\n  darkMode: true, // Force dark mode only\n  language: 'en',\n  notifications: [],\n};\n\n// Action types\nconst actionTypes = {\n  START_SCAN: 'START_SCAN',\n  COMPLETE_SCAN: 'COMPLETE_SCAN',\n  ADD_TO_HISTORY: 'ADD_TO_HISTORY',\n  CLEAR_HISTORY: 'CLEAR_HISTORY',\n  SET_THREAT_LEVEL: 'SET_THREAT_LEVEL',\n  TOGGLE_DARK_MODE: 'TOGGLE_DARK_MODE',\n  SET_LANGUAGE: 'SET_LANGUAGE',\n  ADD_NOTIFICATION: 'ADD_NOTIFICATION',\n  REMOVE_NOTIFICATION: 'REMOVE_NOTIFICATION',\n  SET_CURRENT_SCAN: 'SET_CURRENT_SCAN',\n};\n\n// Reducer function\nconst scanReducer = (state, action) => {\n  switch (action.type) {\n    case actionTypes.START_SCAN:\n      return {\n        ...state,\n        isScanning: true,\n        currentScan: {\n          id: Date.now(),\n          type: action.payload.type,\n          target: action.payload.target,\n          startTime: new Date().toISOString(),\n          status: 'scanning',\n        },\n      };\n\n    case actionTypes.COMPLETE_SCAN:\n      return {\n        ...state,\n        isScanning: false,\n        currentScan: {\n          ...state.currentScan,\n          ...action.payload,\n          endTime: new Date().toISOString(),\n          status: 'completed',\n        },\n      };\n\n    case actionTypes.ADD_TO_HISTORY:\n      return {\n        ...state,\n        scanHistory: [action.payload, ...state.scanHistory].slice(0, 100), // Keep last 100 scans\n      };\n\n    case actionTypes.CLEAR_HISTORY:\n      return {\n        ...state,\n        scanHistory: [],\n      };\n\n    case actionTypes.SET_THREAT_LEVEL:\n      return {\n        ...state,\n        threatLevel: action.payload,\n      };\n\n    case actionTypes.TOGGLE_DARK_MODE:\n      return {\n        ...state,\n        darkMode: true, // Always keep dark mode\n      };\n\n    case actionTypes.SET_LANGUAGE:\n      return {\n        ...state,\n        language: action.payload,\n      };\n\n    case actionTypes.ADD_NOTIFICATION:\n      return {\n        ...state,\n        notifications: [\n          ...state.notifications,\n          {\n            id: Date.now(),\n            ...action.payload,\n            timestamp: new Date().toISOString(),\n          },\n        ],\n      };\n\n    case actionTypes.REMOVE_NOTIFICATION:\n      return {\n        ...state,\n        notifications: state.notifications.filter(\n          (notification) => notification.id !== action.payload\n        ),\n      };\n\n    case actionTypes.SET_CURRENT_SCAN:\n      return {\n        ...state,\n        currentScan: action.payload,\n      };\n\n    default:\n      return state;\n  }\n};\n\n// Create context\nconst ScanContext = createContext();\n\n// Provider component\nexport const ScanProvider = ({ children }) => {\n  const [state, dispatch] = useReducer(scanReducer, initialState);\n\n  // Action creators\n  const startScan = useCallback((type, target) => {\n    dispatch({\n      type: actionTypes.START_SCAN,\n      payload: { type, target },\n    });\n  }, []);\n\n  const completeScan = useCallback((results) => {\n    dispatch({\n      type: actionTypes.COMPLETE_SCAN,\n      payload: results,\n    });\n  }, []);\n\n  const addToHistory = useCallback((scanResult) => {\n    dispatch({\n      type: actionTypes.ADD_TO_HISTORY,\n      payload: scanResult,\n    });\n  }, []);\n\n  const clearHistory = useCallback(() => {\n    dispatch({ type: actionTypes.CLEAR_HISTORY });\n  }, []);\n\n  const setThreatLevel = useCallback((level) => {\n    dispatch({\n      type: actionTypes.SET_THREAT_LEVEL,\n      payload: level,\n    });\n  }, []);\n\n  const toggleDarkMode = useCallback(() => {\n    dispatch({ type: actionTypes.TOGGLE_DARK_MODE });\n  }, []);\n\n  const setLanguage = useCallback((language) => {\n    dispatch({\n      type: actionTypes.SET_LANGUAGE,\n      payload: language,\n    });\n  }, []);\n\n  const addNotification = useCallback((notification) => {\n    dispatch({\n      type: actionTypes.ADD_NOTIFICATION,\n      payload: notification,\n    });\n  }, []);\n\n  const removeNotification = useCallback((id) => {\n    dispatch({\n      type: actionTypes.REMOVE_NOTIFICATION,\n      payload: id,\n    });\n  }, []);\n\n  const setCurrentScan = useCallback((scan) => {\n    dispatch({\n      type: actionTypes.SET_CURRENT_SCAN,\n      payload: scan,\n    });\n  }, []);\n\n  const value = {\n    ...state,\n    startScan,\n    completeScan,\n    addToHistory,\n    clearHistory,\n    setThreatLevel,\n    toggleDarkMode,\n    setLanguage,\n    addNotification,\n    removeNotification,\n    setCurrentScan,\n  };\n\n  return <ScanContext.Provider value={value}>{children}</ScanContext.Provider>;\n};\n\n// Custom hook to use the scan context\nexport const useScan = () => {\n  const context = useContext(ScanContext);\n  if (!context) {\n    throw new Error('useScan must be used within a ScanProvider');\n  }\n  return context;\n};\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,UAAU,EAAEC,WAAW,QAAQ,OAAO;;AAEjF;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,YAAY,GAAG;EACnBC,WAAW,EAAE,EAAE;EACfC,WAAW,EAAE,IAAI;EACjBC,UAAU,EAAE,KAAK;EACjBC,WAAW,EAAE,KAAK;EAClBC,QAAQ,EAAE,IAAI;EAAE;EAChBC,QAAQ,EAAE,IAAI;EACdC,aAAa,EAAE;AACjB,CAAC;;AAED;AACA,MAAMC,WAAW,GAAG;EAClBC,UAAU,EAAE,YAAY;EACxBC,aAAa,EAAE,eAAe;EAC9BC,cAAc,EAAE,gBAAgB;EAChCC,aAAa,EAAE,eAAe;EAC9BC,gBAAgB,EAAE,kBAAkB;EACpCC,gBAAgB,EAAE,kBAAkB;EACpCC,YAAY,EAAE,cAAc;EAC5BC,gBAAgB,EAAE,kBAAkB;EACpCC,mBAAmB,EAAE,qBAAqB;EAC1CC,gBAAgB,EAAE;AACpB,CAAC;;AAED;AACA,MAAMC,WAAW,GAAGA,CAACC,KAAK,EAAEC,MAAM,KAAK;EACrC,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAKd,WAAW,CAACC,UAAU;MACzB,OAAO;QACL,GAAGW,KAAK;QACRjB,UAAU,EAAE,IAAI;QAChBD,WAAW,EAAE;UACXqB,EAAE,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC;UACdH,IAAI,EAAED,MAAM,CAACK,OAAO,CAACJ,IAAI;UACzBK,MAAM,EAAEN,MAAM,CAACK,OAAO,CAACC,MAAM;UAC7BC,SAAS,EAAE,IAAIJ,IAAI,CAAC,CAAC,CAACK,WAAW,CAAC,CAAC;UACnCC,MAAM,EAAE;QACV;MACF,CAAC;IAEH,KAAKtB,WAAW,CAACE,aAAa;MAC5B,OAAO;QACL,GAAGU,KAAK;QACRjB,UAAU,EAAE,KAAK;QACjBD,WAAW,EAAE;UACX,GAAGkB,KAAK,CAAClB,WAAW;UACpB,GAAGmB,MAAM,CAACK,OAAO;UACjBK,OAAO,EAAE,IAAIP,IAAI,CAAC,CAAC,CAACK,WAAW,CAAC,CAAC;UACjCC,MAAM,EAAE;QACV;MACF,CAAC;IAEH,KAAKtB,WAAW,CAACG,cAAc;MAC7B,OAAO;QACL,GAAGS,KAAK;QACRnB,WAAW,EAAE,CAACoB,MAAM,CAACK,OAAO,EAAE,GAAGN,KAAK,CAACnB,WAAW,CAAC,CAAC+B,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,CAAE;MACrE,CAAC;IAEH,KAAKxB,WAAW,CAACI,aAAa;MAC5B,OAAO;QACL,GAAGQ,KAAK;QACRnB,WAAW,EAAE;MACf,CAAC;IAEH,KAAKO,WAAW,CAACK,gBAAgB;MAC/B,OAAO;QACL,GAAGO,KAAK;QACRhB,WAAW,EAAEiB,MAAM,CAACK;MACtB,CAAC;IAEH,KAAKlB,WAAW,CAACM,gBAAgB;MAC/B,OAAO;QACL,GAAGM,KAAK;QACRf,QAAQ,EAAE,IAAI,CAAE;MAClB,CAAC;IAEH,KAAKG,WAAW,CAACO,YAAY;MAC3B,OAAO;QACL,GAAGK,KAAK;QACRd,QAAQ,EAAEe,MAAM,CAACK;MACnB,CAAC;IAEH,KAAKlB,WAAW,CAACQ,gBAAgB;MAC/B,OAAO;QACL,GAAGI,KAAK;QACRb,aAAa,EAAE,CACb,GAAGa,KAAK,CAACb,aAAa,EACtB;UACEgB,EAAE,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC;UACd,GAAGJ,MAAM,CAACK,OAAO;UACjBO,SAAS,EAAE,IAAIT,IAAI,CAAC,CAAC,CAACK,WAAW,CAAC;QACpC,CAAC;MAEL,CAAC;IAEH,KAAKrB,WAAW,CAACS,mBAAmB;MAClC,OAAO;QACL,GAAGG,KAAK;QACRb,aAAa,EAAEa,KAAK,CAACb,aAAa,CAAC2B,MAAM,CACtCC,YAAY,IAAKA,YAAY,CAACZ,EAAE,KAAKF,MAAM,CAACK,OAC/C;MACF,CAAC;IAEH,KAAKlB,WAAW,CAACU,gBAAgB;MAC/B,OAAO;QACL,GAAGE,KAAK;QACRlB,WAAW,EAAEmB,MAAM,CAACK;MACtB,CAAC;IAEH;MACE,OAAON,KAAK;EAChB;AACF,CAAC;;AAED;AACA,MAAMgB,WAAW,gBAAG1C,aAAa,CAAC,CAAC;;AAEnC;AACA,OAAO,MAAM2C,YAAY,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAC5C,MAAM,CAACnB,KAAK,EAAEoB,QAAQ,CAAC,GAAG5C,UAAU,CAACuB,WAAW,EAAEnB,YAAY,CAAC;;EAE/D;EACA,MAAMyC,SAAS,GAAG5C,WAAW,CAAC,CAACyB,IAAI,EAAEK,MAAM,KAAK;IAC9Ca,QAAQ,CAAC;MACPlB,IAAI,EAAEd,WAAW,CAACC,UAAU;MAC5BiB,OAAO,EAAE;QAAEJ,IAAI;QAAEK;MAAO;IAC1B,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMe,YAAY,GAAG7C,WAAW,CAAE8C,OAAO,IAAK;IAC5CH,QAAQ,CAAC;MACPlB,IAAI,EAAEd,WAAW,CAACE,aAAa;MAC/BgB,OAAO,EAAEiB;IACX,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,YAAY,GAAG/C,WAAW,CAAEgD,UAAU,IAAK;IAC/CL,QAAQ,CAAC;MACPlB,IAAI,EAAEd,WAAW,CAACG,cAAc;MAChCe,OAAO,EAAEmB;IACX,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,YAAY,GAAGjD,WAAW,CAAC,MAAM;IACrC2C,QAAQ,CAAC;MAAElB,IAAI,EAAEd,WAAW,CAACI;IAAc,CAAC,CAAC;EAC/C,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMmC,cAAc,GAAGlD,WAAW,CAAEmD,KAAK,IAAK;IAC5CR,QAAQ,CAAC;MACPlB,IAAI,EAAEd,WAAW,CAACK,gBAAgB;MAClCa,OAAO,EAAEsB;IACX,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,cAAc,GAAGpD,WAAW,CAAC,MAAM;IACvC2C,QAAQ,CAAC;MAAElB,IAAI,EAAEd,WAAW,CAACM;IAAiB,CAAC,CAAC;EAClD,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMoC,WAAW,GAAGrD,WAAW,CAAES,QAAQ,IAAK;IAC5CkC,QAAQ,CAAC;MACPlB,IAAI,EAAEd,WAAW,CAACO,YAAY;MAC9BW,OAAO,EAAEpB;IACX,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;EAEN,MAAM6C,eAAe,GAAGtD,WAAW,CAAEsC,YAAY,IAAK;IACpDK,QAAQ,CAAC;MACPlB,IAAI,EAAEd,WAAW,CAACQ,gBAAgB;MAClCU,OAAO,EAAES;IACX,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMiB,kBAAkB,GAAGvD,WAAW,CAAE0B,EAAE,IAAK;IAC7CiB,QAAQ,CAAC;MACPlB,IAAI,EAAEd,WAAW,CAACS,mBAAmB;MACrCS,OAAO,EAAEH;IACX,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;EAEN,MAAM8B,cAAc,GAAGxD,WAAW,CAAEyD,IAAI,IAAK;IAC3Cd,QAAQ,CAAC;MACPlB,IAAI,EAAEd,WAAW,CAACU,gBAAgB;MAClCQ,OAAO,EAAE4B;IACX,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,KAAK,GAAG;IACZ,GAAGnC,KAAK;IACRqB,SAAS;IACTC,YAAY;IACZE,YAAY;IACZE,YAAY;IACZC,cAAc;IACdE,cAAc;IACdC,WAAW;IACXC,eAAe;IACfC,kBAAkB;IAClBC;EACF,CAAC;EAED,oBAAOtD,OAAA,CAACqC,WAAW,CAACoB,QAAQ;IAACD,KAAK,EAAEA,KAAM;IAAAjB,QAAA,EAAEA;EAAQ;IAAAmB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAuB,CAAC;AAC9E,CAAC;;AAED;AAAArB,EAAA,CArFaF,YAAY;AAAAwB,EAAA,GAAZxB,YAAY;AAsFzB,OAAO,MAAMyB,OAAO,GAAGA,CAAA,KAAM;EAAAC,GAAA;EAC3B,MAAMC,OAAO,GAAGrE,UAAU,CAACyC,WAAW,CAAC;EACvC,IAAI,CAAC4B,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,4CAA4C,CAAC;EAC/D;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,GAAA,CANWD,OAAO;AAAA,IAAAD,EAAA;AAAAK,YAAA,CAAAL,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}