{"ast": null, "code": "var _jsxFileName = \"E:\\\\PythonProjects\\\\React.js\\\\Smart AI Assisstant\\\\src\\\\components\\\\EnhancedSettingsDialog.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Dialog, DialogTitle, DialogContent, Box, Typography, IconButton, List, ListItem, ListItemIcon, ListItemText, ListItemButton, Paper, Divider, Fade, useTheme, useMediaQuery, Drawer } from '@mui/material';\nimport { Close as CloseIcon, Person as PersonIcon, Security as SecurityIcon, Notifications as NotificationsIcon, Palette as PaletteIcon, Language as LanguageIcon, Info as InfoIcon, Menu as MenuIcon } from '@mui/icons-material';\nimport { useAuth } from '../contexts/AuthContext';\nimport { useScan } from '../contexts/ScanContext';\nimport UserProfile from './UserProfile';\nimport SecuritySettings from './SecuritySettings';\nimport NotificationSettings from './NotificationSettings';\nimport AppearanceSettings from './AppearanceSettings';\nimport AboutSettings from './AboutSettings';\nimport AuthenticationForms from './AuthenticationForms';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst EnhancedSettingsDialog = ({\n  open,\n  onClose\n}) => {\n  _s();\n  var _filteredMenuItems$fi;\n  const [activeSection, setActiveSection] = useState('profile');\n  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);\n  const {\n    isAuthenticated\n  } = useAuth();\n  const theme = useTheme();\n  const isMobile = useMediaQuery(theme.breakpoints.down('md'));\n  const menuItems = [{\n    id: 'profile',\n    label: 'Profile',\n    icon: /*#__PURE__*/_jsxDEV(PersonIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 51,\n      columnNumber: 13\n    }, this),\n    requiresAuth: true\n  }, {\n    id: 'security',\n    label: 'Security',\n    icon: /*#__PURE__*/_jsxDEV(SecurityIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 57,\n      columnNumber: 13\n    }, this),\n    requiresAuth: true\n  }, {\n    id: 'notifications',\n    label: 'Notifications',\n    icon: /*#__PURE__*/_jsxDEV(NotificationsIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 63,\n      columnNumber: 13\n    }, this),\n    requiresAuth: true\n  }, {\n    id: 'appearance',\n    label: 'Appearance',\n    icon: /*#__PURE__*/_jsxDEV(PaletteIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 69,\n      columnNumber: 13\n    }, this),\n    requiresAuth: false\n  }, {\n    id: 'language',\n    label: 'Language',\n    icon: /*#__PURE__*/_jsxDEV(LanguageIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 75,\n      columnNumber: 13\n    }, this),\n    requiresAuth: false\n  }, {\n    id: 'about',\n    label: 'About',\n    icon: /*#__PURE__*/_jsxDEV(InfoIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 81,\n      columnNumber: 13\n    }, this),\n    requiresAuth: false\n  }];\n  const filteredMenuItems = menuItems.filter(item => !item.requiresAuth || isAuthenticated);\n  const handleSectionChange = sectionId => {\n    setActiveSection(sectionId);\n    if (isMobile) {\n      setMobileMenuOpen(false);\n    }\n  };\n  const renderContent = () => {\n    if (!isAuthenticated && ['profile', 'security', 'notifications'].includes(activeSection)) {\n      return /*#__PURE__*/_jsxDEV(AuthenticationForms, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 99,\n        columnNumber: 14\n      }, this);\n    }\n    switch (activeSection) {\n      case 'profile':\n        return /*#__PURE__*/_jsxDEV(UserProfile, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 104,\n          columnNumber: 16\n        }, this);\n      case 'security':\n        return /*#__PURE__*/_jsxDEV(SecuritySettings, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 106,\n          columnNumber: 16\n        }, this);\n      case 'notifications':\n        return /*#__PURE__*/_jsxDEV(NotificationSettings, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 108,\n          columnNumber: 16\n        }, this);\n      case 'appearance':\n        return /*#__PURE__*/_jsxDEV(AppearanceSettings, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 110,\n          columnNumber: 16\n        }, this);\n      case 'language':\n        return /*#__PURE__*/_jsxDEV(AppearanceSettings, {\n          showLanguageOnly: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 112,\n          columnNumber: 16\n        }, this);\n      case 'about':\n        return /*#__PURE__*/_jsxDEV(AboutSettings, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 114,\n          columnNumber: 16\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(UserProfile, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 116,\n          columnNumber: 16\n        }, this);\n    }\n  };\n  const sidebarContent = /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      width: isMobile ? 280 : 300,\n      height: '100%'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        p: 3,\n        borderBottom: '1px solid',\n        borderColor: 'divider'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        fontWeight: \"bold\",\n        children: \"Settings\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 123,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body2\",\n        color: \"text.secondary\",\n        children: \"Manage your account and preferences\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 126,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 122,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(List, {\n      sx: {\n        p: 0\n      },\n      children: filteredMenuItems.map((item, index) => /*#__PURE__*/_jsxDEV(ListItem, {\n        disablePadding: true,\n        children: /*#__PURE__*/_jsxDEV(ListItemButton, {\n          selected: activeSection === item.id,\n          onClick: () => handleSectionChange(item.id),\n          sx: {\n            py: 2,\n            px: 3,\n            '&.Mui-selected': {\n              background: theme => theme.palette.mode === 'dark' ? 'linear-gradient(135deg, rgba(102, 126, 234, 0.2) 0%, rgba(118, 75, 162, 0.2) 100%)' : 'linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%)',\n              borderRight: '3px solid',\n              borderRightColor: 'primary.main',\n              '&:hover': {\n                background: theme => theme.palette.mode === 'dark' ? 'linear-gradient(135deg, rgba(102, 126, 234, 0.25) 0%, rgba(118, 75, 162, 0.25) 100%)' : 'linear-gradient(135deg, rgba(102, 126, 234, 0.15) 0%, rgba(118, 75, 162, 0.15) 100%)'\n              }\n            },\n            '&:hover': {\n              background: theme => theme.palette.mode === 'dark' ? 'rgba(102, 126, 234, 0.1)' : 'rgba(102, 126, 234, 0.05)'\n            },\n            transition: 'all 0.2s ease'\n          },\n          children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n            sx: {\n              color: activeSection === item.id ? 'primary.main' : 'text.secondary',\n              minWidth: 40\n            },\n            children: item.icon\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 160,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n            primary: item.label,\n            primaryTypographyProps: {\n              fontWeight: activeSection === item.id ? 600 : 400,\n              color: activeSection === item.id ? 'primary.main' : 'text.primary'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 168,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 134,\n          columnNumber: 13\n        }, this)\n      }, item.id, false, {\n        fileName: _jsxFileName,\n        lineNumber: 133,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 131,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 121,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(Dialog, {\n    open: open,\n    onClose: onClose,\n    maxWidth: \"lg\",\n    fullWidth: true,\n    fullScreen: isMobile,\n    PaperProps: {\n      sx: {\n        borderRadius: isMobile ? 0 : 4,\n        minHeight: isMobile ? '100vh' : '80vh',\n        maxHeight: isMobile ? '100vh' : '90vh',\n        background: theme => theme.palette.mode === 'dark' ? 'linear-gradient(135deg, rgba(12, 12, 12, 0.98) 0%, rgba(26, 26, 26, 0.98) 50%, rgba(45, 45, 45, 0.98) 100%)' : 'linear-gradient(135deg, rgba(248, 250, 252, 0.98) 0%, rgba(241, 245, 249, 0.98) 50%, rgba(226, 232, 240, 0.98) 100%)',\n        backdropFilter: 'blur(25px) saturate(180%)',\n        border: theme => theme.palette.mode === 'dark' ? '1px solid rgba(102, 126, 234, 0.2)' : '1px solid rgba(102, 126, 234, 0.1)',\n        boxShadow: '0 25px 80px rgba(0, 0, 0, 0.3)',\n        overflow: 'hidden'\n      }\n    },\n    children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n      sx: {\n        p: 0\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        display: \"flex\",\n        alignItems: \"center\",\n        justifyContent: \"space-between\",\n        sx: {\n          p: 3,\n          background: theme => theme.palette.mode === 'dark' ? 'linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%)' : 'linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%)',\n          borderBottom: '1px solid',\n          borderColor: 'divider'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          display: \"flex\",\n          alignItems: \"center\",\n          gap: 2,\n          children: [isMobile && /*#__PURE__*/_jsxDEV(IconButton, {\n            onClick: () => setMobileMenuOpen(true),\n            sx: {\n              mr: 1\n            },\n            children: /*#__PURE__*/_jsxDEV(MenuIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 227,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 223,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h5\",\n              fontWeight: \"bold\",\n              children: \"Settings & Account\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 232,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: ((_filteredMenuItems$fi = filteredMenuItems.find(item => item.id === activeSection)) === null || _filteredMenuItems$fi === void 0 ? void 0 : _filteredMenuItems$fi.label) || 'Settings'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 235,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 231,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 221,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: onClose,\n          sx: {\n            background: 'rgba(255, 255, 255, 0.1)',\n            backdropFilter: 'blur(10px)',\n            '&:hover': {\n              background: 'rgba(255, 255, 255, 0.2)',\n              transform: 'scale(1.05)'\n            },\n            transition: 'all 0.2s ease'\n          },\n          children: /*#__PURE__*/_jsxDEV(CloseIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 253,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 241,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 208,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 207,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n      sx: {\n        p: 0,\n        display: 'flex',\n        height: '100%'\n      },\n      children: [isMobile && /*#__PURE__*/_jsxDEV(Drawer, {\n        anchor: \"left\",\n        open: mobileMenuOpen,\n        onClose: () => setMobileMenuOpen(false),\n        PaperProps: {\n          sx: {\n            background: theme => theme.palette.mode === 'dark' ? 'rgba(26, 26, 26, 0.98)' : 'rgba(248, 250, 252, 0.98)',\n            backdropFilter: 'blur(25px)'\n          }\n        },\n        children: sidebarContent\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 261,\n        columnNumber: 11\n      }, this), !isMobile && /*#__PURE__*/_jsxDEV(Paper, {\n        elevation: 0,\n        sx: {\n          background: theme => theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.02)' : 'rgba(0, 0, 0, 0.02)',\n          borderRight: '1px solid',\n          borderColor: 'divider',\n          borderRadius: 0\n        },\n        children: sidebarContent\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 280,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          flex: 1,\n          p: 4,\n          overflow: 'auto',\n          background: theme => theme.palette.mode === 'dark' ? 'rgba(0, 0, 0, 0.02)' : 'rgba(255, 255, 255, 0.02)'\n        },\n        children: /*#__PURE__*/_jsxDEV(Fade, {\n          in: true,\n          timeout: 300,\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            children: renderContent()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 307,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 306,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 296,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 258,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 183,\n    columnNumber: 5\n  }, this);\n};\n_s(EnhancedSettingsDialog, \"3yg59cIa7QTmvVjT2l7QeF/vdF4=\", false, function () {\n  return [useAuth, useTheme, useMediaQuery];\n});\n_c = EnhancedSettingsDialog;\nexport default EnhancedSettingsDialog;\nvar _c;\n$RefreshReg$(_c, \"EnhancedSettingsDialog\");", "map": {"version": 3, "names": ["React", "useState", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Box", "Typography", "IconButton", "List", "ListItem", "ListItemIcon", "ListItemText", "ListItemButton", "Paper", "Divider", "Fade", "useTheme", "useMediaQuery", "Drawer", "Close", "CloseIcon", "Person", "PersonIcon", "Security", "SecurityIcon", "Notifications", "NotificationsIcon", "Palette", "PaletteIcon", "Language", "LanguageIcon", "Info", "InfoIcon", "<PERSON><PERSON>", "MenuIcon", "useAuth", "useScan", "UserProfile", "SecuritySettings", "NotificationSettings", "AppearanceSettings", "AboutSettings", "AuthenticationForms", "jsxDEV", "_jsxDEV", "EnhancedSettingsDialog", "open", "onClose", "_s", "_filteredMenuItems$fi", "activeSection", "setActiveSection", "mobileMenuOpen", "setMobileMenuOpen", "isAuthenticated", "theme", "isMobile", "breakpoints", "down", "menuItems", "id", "label", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "requiresAuth", "filteredMenuItems", "filter", "item", "handleSectionChange", "sectionId", "renderContent", "includes", "showLanguageOnly", "sidebarContent", "sx", "width", "height", "children", "p", "borderBottom", "borderColor", "variant", "fontWeight", "color", "map", "index", "disablePadding", "selected", "onClick", "py", "px", "background", "palette", "mode", "borderRight", "borderRightColor", "transition", "min<PERSON><PERSON><PERSON>", "primary", "primaryTypographyProps", "max<PERSON><PERSON><PERSON>", "fullWidth", "fullScreen", "PaperProps", "borderRadius", "minHeight", "maxHeight", "<PERSON><PERSON>ilter", "border", "boxShadow", "overflow", "display", "alignItems", "justifyContent", "gap", "mr", "find", "transform", "anchor", "elevation", "flex", "in", "timeout", "_c", "$RefreshReg$"], "sources": ["E:/PythonProjects/React.js/Smart AI Assisstant/src/components/EnhancedSettingsDialog.jsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  Box,\n  Typography,\n  IconButton,\n  List,\n  ListItem,\n  ListItemIcon,\n  ListItemText,\n  ListItemButton,\n  Paper,\n  Divider,\n  Fade,\n  useTheme,\n  useMediaQuery,\n  Drawer,\n} from '@mui/material';\nimport {\n  Close as CloseIcon,\n  Person as PersonIcon,\n  Security as SecurityIcon,\n  Notifications as NotificationsIcon,\n  Palette as PaletteIcon,\n  Language as LanguageIcon,\n  Info as InfoIcon,\n  Menu as MenuIcon,\n} from '@mui/icons-material';\nimport { useAuth } from '../contexts/AuthContext';\nimport { useScan } from '../contexts/ScanContext';\nimport UserProfile from './UserProfile';\nimport SecuritySettings from './SecuritySettings';\nimport NotificationSettings from './NotificationSettings';\nimport AppearanceSettings from './AppearanceSettings';\nimport AboutSettings from './AboutSettings';\nimport AuthenticationForms from './AuthenticationForms';\n\nconst EnhancedSettingsDialog = ({ open, onClose }) => {\n  const [activeSection, setActiveSection] = useState('profile');\n  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);\n  const { isAuthenticated } = useAuth();\n  const theme = useTheme();\n  const isMobile = useMediaQuery(theme.breakpoints.down('md'));\n\n  const menuItems = [\n    {\n      id: 'profile',\n      label: 'Profile',\n      icon: <PersonIcon />,\n      requiresAuth: true,\n    },\n    {\n      id: 'security',\n      label: 'Security',\n      icon: <SecurityIcon />,\n      requiresAuth: true,\n    },\n    {\n      id: 'notifications',\n      label: 'Notifications',\n      icon: <NotificationsIcon />,\n      requiresAuth: true,\n    },\n    {\n      id: 'appearance',\n      label: 'Appearance',\n      icon: <PaletteIcon />,\n      requiresAuth: false,\n    },\n    {\n      id: 'language',\n      label: 'Language',\n      icon: <LanguageIcon />,\n      requiresAuth: false,\n    },\n    {\n      id: 'about',\n      label: 'About',\n      icon: <InfoIcon />,\n      requiresAuth: false,\n    },\n  ];\n\n  const filteredMenuItems = menuItems.filter(item => \n    !item.requiresAuth || isAuthenticated\n  );\n\n  const handleSectionChange = (sectionId) => {\n    setActiveSection(sectionId);\n    if (isMobile) {\n      setMobileMenuOpen(false);\n    }\n  };\n\n  const renderContent = () => {\n    if (!isAuthenticated && ['profile', 'security', 'notifications'].includes(activeSection)) {\n      return <AuthenticationForms />;\n    }\n\n    switch (activeSection) {\n      case 'profile':\n        return <UserProfile />;\n      case 'security':\n        return <SecuritySettings />;\n      case 'notifications':\n        return <NotificationSettings />;\n      case 'appearance':\n        return <AppearanceSettings />;\n      case 'language':\n        return <AppearanceSettings showLanguageOnly />;\n      case 'about':\n        return <AboutSettings />;\n      default:\n        return <UserProfile />;\n    }\n  };\n\n  const sidebarContent = (\n    <Box sx={{ width: isMobile ? 280 : 300, height: '100%' }}>\n      <Box sx={{ p: 3, borderBottom: '1px solid', borderColor: 'divider' }}>\n        <Typography variant=\"h6\" fontWeight=\"bold\">\n          Settings\n        </Typography>\n        <Typography variant=\"body2\" color=\"text.secondary\">\n          Manage your account and preferences\n        </Typography>\n      </Box>\n      \n      <List sx={{ p: 0 }}>\n        {filteredMenuItems.map((item, index) => (\n          <ListItem key={item.id} disablePadding>\n            <ListItemButton\n              selected={activeSection === item.id}\n              onClick={() => handleSectionChange(item.id)}\n              sx={{\n                py: 2,\n                px: 3,\n                '&.Mui-selected': {\n                  background: (theme) => theme.palette.mode === 'dark'\n                    ? 'linear-gradient(135deg, rgba(102, 126, 234, 0.2) 0%, rgba(118, 75, 162, 0.2) 100%)'\n                    : 'linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%)',\n                  borderRight: '3px solid',\n                  borderRightColor: 'primary.main',\n                  '&:hover': {\n                    background: (theme) => theme.palette.mode === 'dark'\n                      ? 'linear-gradient(135deg, rgba(102, 126, 234, 0.25) 0%, rgba(118, 75, 162, 0.25) 100%)'\n                      : 'linear-gradient(135deg, rgba(102, 126, 234, 0.15) 0%, rgba(118, 75, 162, 0.15) 100%)',\n                  },\n                },\n                '&:hover': {\n                  background: (theme) => theme.palette.mode === 'dark'\n                    ? 'rgba(102, 126, 234, 0.1)'\n                    : 'rgba(102, 126, 234, 0.05)',\n                },\n                transition: 'all 0.2s ease',\n              }}\n            >\n              <ListItemIcon\n                sx={{\n                  color: activeSection === item.id ? 'primary.main' : 'text.secondary',\n                  minWidth: 40,\n                }}\n              >\n                {item.icon}\n              </ListItemIcon>\n              <ListItemText\n                primary={item.label}\n                primaryTypographyProps={{\n                  fontWeight: activeSection === item.id ? 600 : 400,\n                  color: activeSection === item.id ? 'primary.main' : 'text.primary',\n                }}\n              />\n            </ListItemButton>\n          </ListItem>\n        ))}\n      </List>\n    </Box>\n  );\n\n  return (\n    <Dialog\n      open={open}\n      onClose={onClose}\n      maxWidth=\"lg\"\n      fullWidth\n      fullScreen={isMobile}\n      PaperProps={{\n        sx: {\n          borderRadius: isMobile ? 0 : 4,\n          minHeight: isMobile ? '100vh' : '80vh',\n          maxHeight: isMobile ? '100vh' : '90vh',\n          background: (theme) => theme.palette.mode === 'dark'\n            ? 'linear-gradient(135deg, rgba(12, 12, 12, 0.98) 0%, rgba(26, 26, 26, 0.98) 50%, rgba(45, 45, 45, 0.98) 100%)'\n            : 'linear-gradient(135deg, rgba(248, 250, 252, 0.98) 0%, rgba(241, 245, 249, 0.98) 50%, rgba(226, 232, 240, 0.98) 100%)',\n          backdropFilter: 'blur(25px) saturate(180%)',\n          border: (theme) => theme.palette.mode === 'dark'\n            ? '1px solid rgba(102, 126, 234, 0.2)'\n            : '1px solid rgba(102, 126, 234, 0.1)',\n          boxShadow: '0 25px 80px rgba(0, 0, 0, 0.3)',\n          overflow: 'hidden',\n        },\n      }}\n    >\n      {/* Header */}\n      <DialogTitle sx={{ p: 0 }}>\n        <Box\n          display=\"flex\"\n          alignItems=\"center\"\n          justifyContent=\"space-between\"\n          sx={{\n            p: 3,\n            background: (theme) => theme.palette.mode === 'dark'\n              ? 'linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%)'\n              : 'linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%)',\n            borderBottom: '1px solid',\n            borderColor: 'divider',\n          }}\n        >\n          <Box display=\"flex\" alignItems=\"center\" gap={2}>\n            {isMobile && (\n              <IconButton\n                onClick={() => setMobileMenuOpen(true)}\n                sx={{ mr: 1 }}\n              >\n                <MenuIcon />\n              </IconButton>\n            )}\n            \n            <Box>\n              <Typography variant=\"h5\" fontWeight=\"bold\">\n                Settings & Account\n              </Typography>\n              <Typography variant=\"body2\" color=\"text.secondary\">\n                {filteredMenuItems.find(item => item.id === activeSection)?.label || 'Settings'}\n              </Typography>\n            </Box>\n          </Box>\n          \n          <IconButton\n            onClick={onClose}\n            sx={{\n              background: 'rgba(255, 255, 255, 0.1)',\n              backdropFilter: 'blur(10px)',\n              '&:hover': {\n                background: 'rgba(255, 255, 255, 0.2)',\n                transform: 'scale(1.05)',\n              },\n              transition: 'all 0.2s ease',\n            }}\n          >\n            <CloseIcon />\n          </IconButton>\n        </Box>\n      </DialogTitle>\n\n      <DialogContent sx={{ p: 0, display: 'flex', height: '100%' }}>\n        {/* Mobile Drawer */}\n        {isMobile && (\n          <Drawer\n            anchor=\"left\"\n            open={mobileMenuOpen}\n            onClose={() => setMobileMenuOpen(false)}\n            PaperProps={{\n              sx: {\n                background: (theme) => theme.palette.mode === 'dark'\n                  ? 'rgba(26, 26, 26, 0.98)'\n                  : 'rgba(248, 250, 252, 0.98)',\n                backdropFilter: 'blur(25px)',\n              },\n            }}\n          >\n            {sidebarContent}\n          </Drawer>\n        )}\n\n        {/* Desktop Sidebar */}\n        {!isMobile && (\n          <Paper\n            elevation={0}\n            sx={{\n              background: (theme) => theme.palette.mode === 'dark'\n                ? 'rgba(255, 255, 255, 0.02)'\n                : 'rgba(0, 0, 0, 0.02)',\n              borderRight: '1px solid',\n              borderColor: 'divider',\n              borderRadius: 0,\n            }}\n          >\n            {sidebarContent}\n          </Paper>\n        )}\n\n        {/* Main Content */}\n        <Box\n          sx={{\n            flex: 1,\n            p: 4,\n            overflow: 'auto',\n            background: (theme) => theme.palette.mode === 'dark'\n              ? 'rgba(0, 0, 0, 0.02)'\n              : 'rgba(255, 255, 255, 0.02)',\n          }}\n        >\n          <Fade in={true} timeout={300}>\n            <Box>\n              {renderContent()}\n            </Box>\n          </Fade>\n        </Box>\n      </DialogContent>\n    </Dialog>\n  );\n};\n\nexport default EnhancedSettingsDialog;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,GAAG,EACHC,UAAU,EACVC,UAAU,EACVC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,YAAY,EACZC,cAAc,EACdC,KAAK,EACLC,OAAO,EACPC,IAAI,EACJC,QAAQ,EACRC,aAAa,EACbC,MAAM,QACD,eAAe;AACtB,SACEC,KAAK,IAAIC,SAAS,EAClBC,MAAM,IAAIC,UAAU,EACpBC,QAAQ,IAAIC,YAAY,EACxBC,aAAa,IAAIC,iBAAiB,EAClCC,OAAO,IAAIC,WAAW,EACtBC,QAAQ,IAAIC,YAAY,EACxBC,IAAI,IAAIC,QAAQ,EAChBC,IAAI,IAAIC,QAAQ,QACX,qBAAqB;AAC5B,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SAASC,OAAO,QAAQ,yBAAyB;AACjD,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,OAAOC,oBAAoB,MAAM,wBAAwB;AACzD,OAAOC,kBAAkB,MAAM,sBAAsB;AACrD,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,mBAAmB,MAAM,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExD,MAAMC,sBAAsB,GAAGA,CAAC;EAAEC,IAAI;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,qBAAA;EACpD,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGlD,QAAQ,CAAC,SAAS,CAAC;EAC7D,MAAM,CAACmD,cAAc,EAAEC,iBAAiB,CAAC,GAAGpD,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM;IAAEqD;EAAgB,CAAC,GAAGnB,OAAO,CAAC,CAAC;EACrC,MAAMoB,KAAK,GAAGvC,QAAQ,CAAC,CAAC;EACxB,MAAMwC,QAAQ,GAAGvC,aAAa,CAACsC,KAAK,CAACE,WAAW,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;EAE5D,MAAMC,SAAS,GAAG,CAChB;IACEC,EAAE,EAAE,SAAS;IACbC,KAAK,EAAE,SAAS;IAChBC,IAAI,eAAElB,OAAA,CAACtB,UAAU;MAAAyC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACpBC,YAAY,EAAE;EAChB,CAAC,EACD;IACEP,EAAE,EAAE,UAAU;IACdC,KAAK,EAAE,UAAU;IACjBC,IAAI,eAAElB,OAAA,CAACpB,YAAY;MAAAuC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBC,YAAY,EAAE;EAChB,CAAC,EACD;IACEP,EAAE,EAAE,eAAe;IACnBC,KAAK,EAAE,eAAe;IACtBC,IAAI,eAAElB,OAAA,CAAClB,iBAAiB;MAAAqC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC3BC,YAAY,EAAE;EAChB,CAAC,EACD;IACEP,EAAE,EAAE,YAAY;IAChBC,KAAK,EAAE,YAAY;IACnBC,IAAI,eAAElB,OAAA,CAAChB,WAAW;MAAAmC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACrBC,YAAY,EAAE;EAChB,CAAC,EACD;IACEP,EAAE,EAAE,UAAU;IACdC,KAAK,EAAE,UAAU;IACjBC,IAAI,eAAElB,OAAA,CAACd,YAAY;MAAAiC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBC,YAAY,EAAE;EAChB,CAAC,EACD;IACEP,EAAE,EAAE,OAAO;IACXC,KAAK,EAAE,OAAO;IACdC,IAAI,eAAElB,OAAA,CAACZ,QAAQ;MAAA+B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAClBC,YAAY,EAAE;EAChB,CAAC,CACF;EAED,MAAMC,iBAAiB,GAAGT,SAAS,CAACU,MAAM,CAACC,IAAI,IAC7C,CAACA,IAAI,CAACH,YAAY,IAAIb,eACxB,CAAC;EAED,MAAMiB,mBAAmB,GAAIC,SAAS,IAAK;IACzCrB,gBAAgB,CAACqB,SAAS,CAAC;IAC3B,IAAIhB,QAAQ,EAAE;MACZH,iBAAiB,CAAC,KAAK,CAAC;IAC1B;EACF,CAAC;EAED,MAAMoB,aAAa,GAAGA,CAAA,KAAM;IAC1B,IAAI,CAACnB,eAAe,IAAI,CAAC,SAAS,EAAE,UAAU,EAAE,eAAe,CAAC,CAACoB,QAAQ,CAACxB,aAAa,CAAC,EAAE;MACxF,oBAAON,OAAA,CAACF,mBAAmB;QAAAqB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAChC;IAEA,QAAQhB,aAAa;MACnB,KAAK,SAAS;QACZ,oBAAON,OAAA,CAACP,WAAW;UAAA0B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACxB,KAAK,UAAU;QACb,oBAAOtB,OAAA,CAACN,gBAAgB;UAAAyB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC7B,KAAK,eAAe;QAClB,oBAAOtB,OAAA,CAACL,oBAAoB;UAAAwB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACjC,KAAK,YAAY;QACf,oBAAOtB,OAAA,CAACJ,kBAAkB;UAAAuB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC/B,KAAK,UAAU;QACb,oBAAOtB,OAAA,CAACJ,kBAAkB;UAACmC,gBAAgB;QAAA;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAChD,KAAK,OAAO;QACV,oBAAOtB,OAAA,CAACH,aAAa;UAAAsB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC1B;QACE,oBAAOtB,OAAA,CAACP,WAAW;UAAA0B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;IAC1B;EACF,CAAC;EAED,MAAMU,cAAc,gBAClBhC,OAAA,CAACvC,GAAG;IAACwE,EAAE,EAAE;MAAEC,KAAK,EAAEtB,QAAQ,GAAG,GAAG,GAAG,GAAG;MAAEuB,MAAM,EAAE;IAAO,CAAE;IAAAC,QAAA,gBACvDpC,OAAA,CAACvC,GAAG;MAACwE,EAAE,EAAE;QAAEI,CAAC,EAAE,CAAC;QAAEC,YAAY,EAAE,WAAW;QAAEC,WAAW,EAAE;MAAU,CAAE;MAAAH,QAAA,gBACnEpC,OAAA,CAACtC,UAAU;QAAC8E,OAAO,EAAC,IAAI;QAACC,UAAU,EAAC,MAAM;QAAAL,QAAA,EAAC;MAE3C;QAAAjB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbtB,OAAA,CAACtC,UAAU;QAAC8E,OAAO,EAAC,OAAO;QAACE,KAAK,EAAC,gBAAgB;QAAAN,QAAA,EAAC;MAEnD;QAAAjB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAENtB,OAAA,CAACpC,IAAI;MAACqE,EAAE,EAAE;QAAEI,CAAC,EAAE;MAAE,CAAE;MAAAD,QAAA,EAChBZ,iBAAiB,CAACmB,GAAG,CAAC,CAACjB,IAAI,EAAEkB,KAAK,kBACjC5C,OAAA,CAACnC,QAAQ;QAAegF,cAAc;QAAAT,QAAA,eACpCpC,OAAA,CAAChC,cAAc;UACb8E,QAAQ,EAAExC,aAAa,KAAKoB,IAAI,CAACV,EAAG;UACpC+B,OAAO,EAAEA,CAAA,KAAMpB,mBAAmB,CAACD,IAAI,CAACV,EAAE,CAAE;UAC5CiB,EAAE,EAAE;YACFe,EAAE,EAAE,CAAC;YACLC,EAAE,EAAE,CAAC;YACL,gBAAgB,EAAE;cAChBC,UAAU,EAAGvC,KAAK,IAAKA,KAAK,CAACwC,OAAO,CAACC,IAAI,KAAK,MAAM,GAChD,oFAAoF,GACpF,oFAAoF;cACxFC,WAAW,EAAE,WAAW;cACxBC,gBAAgB,EAAE,cAAc;cAChC,SAAS,EAAE;gBACTJ,UAAU,EAAGvC,KAAK,IAAKA,KAAK,CAACwC,OAAO,CAACC,IAAI,KAAK,MAAM,GAChD,sFAAsF,GACtF;cACN;YACF,CAAC;YACD,SAAS,EAAE;cACTF,UAAU,EAAGvC,KAAK,IAAKA,KAAK,CAACwC,OAAO,CAACC,IAAI,KAAK,MAAM,GAChD,0BAA0B,GAC1B;YACN,CAAC;YACDG,UAAU,EAAE;UACd,CAAE;UAAAnB,QAAA,gBAEFpC,OAAA,CAAClC,YAAY;YACXmE,EAAE,EAAE;cACFS,KAAK,EAAEpC,aAAa,KAAKoB,IAAI,CAACV,EAAE,GAAG,cAAc,GAAG,gBAAgB;cACpEwC,QAAQ,EAAE;YACZ,CAAE;YAAApB,QAAA,EAEDV,IAAI,CAACR;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACftB,OAAA,CAACjC,YAAY;YACX0F,OAAO,EAAE/B,IAAI,CAACT,KAAM;YACpByC,sBAAsB,EAAE;cACtBjB,UAAU,EAAEnC,aAAa,KAAKoB,IAAI,CAACV,EAAE,GAAG,GAAG,GAAG,GAAG;cACjD0B,KAAK,EAAEpC,aAAa,KAAKoB,IAAI,CAACV,EAAE,GAAG,cAAc,GAAG;YACtD;UAAE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACY;MAAC,GA1CJI,IAAI,CAACV,EAAE;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OA2CZ,CACX;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CACN;EAED,oBACEtB,OAAA,CAAC1C,MAAM;IACL4C,IAAI,EAAEA,IAAK;IACXC,OAAO,EAAEA,OAAQ;IACjBwD,QAAQ,EAAC,IAAI;IACbC,SAAS;IACTC,UAAU,EAAEjD,QAAS;IACrBkD,UAAU,EAAE;MACV7B,EAAE,EAAE;QACF8B,YAAY,EAAEnD,QAAQ,GAAG,CAAC,GAAG,CAAC;QAC9BoD,SAAS,EAAEpD,QAAQ,GAAG,OAAO,GAAG,MAAM;QACtCqD,SAAS,EAAErD,QAAQ,GAAG,OAAO,GAAG,MAAM;QACtCsC,UAAU,EAAGvC,KAAK,IAAKA,KAAK,CAACwC,OAAO,CAACC,IAAI,KAAK,MAAM,GAChD,6GAA6G,GAC7G,sHAAsH;QAC1Hc,cAAc,EAAE,2BAA2B;QAC3CC,MAAM,EAAGxD,KAAK,IAAKA,KAAK,CAACwC,OAAO,CAACC,IAAI,KAAK,MAAM,GAC5C,oCAAoC,GACpC,oCAAoC;QACxCgB,SAAS,EAAE,gCAAgC;QAC3CC,QAAQ,EAAE;MACZ;IACF,CAAE;IAAAjC,QAAA,gBAGFpC,OAAA,CAACzC,WAAW;MAAC0E,EAAE,EAAE;QAAEI,CAAC,EAAE;MAAE,CAAE;MAAAD,QAAA,eACxBpC,OAAA,CAACvC,GAAG;QACF6G,OAAO,EAAC,MAAM;QACdC,UAAU,EAAC,QAAQ;QACnBC,cAAc,EAAC,eAAe;QAC9BvC,EAAE,EAAE;UACFI,CAAC,EAAE,CAAC;UACJa,UAAU,EAAGvC,KAAK,IAAKA,KAAK,CAACwC,OAAO,CAACC,IAAI,KAAK,MAAM,GAChD,oFAAoF,GACpF,sFAAsF;UAC1Fd,YAAY,EAAE,WAAW;UACzBC,WAAW,EAAE;QACf,CAAE;QAAAH,QAAA,gBAEFpC,OAAA,CAACvC,GAAG;UAAC6G,OAAO,EAAC,MAAM;UAACC,UAAU,EAAC,QAAQ;UAACE,GAAG,EAAE,CAAE;UAAArC,QAAA,GAC5CxB,QAAQ,iBACPZ,OAAA,CAACrC,UAAU;YACToF,OAAO,EAAEA,CAAA,KAAMtC,iBAAiB,CAAC,IAAI,CAAE;YACvCwB,EAAE,EAAE;cAAEyC,EAAE,EAAE;YAAE,CAAE;YAAAtC,QAAA,eAEdpC,OAAA,CAACV,QAAQ;cAAA6B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CACb,eAEDtB,OAAA,CAACvC,GAAG;YAAA2E,QAAA,gBACFpC,OAAA,CAACtC,UAAU;cAAC8E,OAAO,EAAC,IAAI;cAACC,UAAU,EAAC,MAAM;cAAAL,QAAA,EAAC;YAE3C;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbtB,OAAA,CAACtC,UAAU;cAAC8E,OAAO,EAAC,OAAO;cAACE,KAAK,EAAC,gBAAgB;cAAAN,QAAA,EAC/C,EAAA/B,qBAAA,GAAAmB,iBAAiB,CAACmD,IAAI,CAACjD,IAAI,IAAIA,IAAI,CAACV,EAAE,KAAKV,aAAa,CAAC,cAAAD,qBAAA,uBAAzDA,qBAAA,CAA2DY,KAAK,KAAI;YAAU;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENtB,OAAA,CAACrC,UAAU;UACToF,OAAO,EAAE5C,OAAQ;UACjB8B,EAAE,EAAE;YACFiB,UAAU,EAAE,0BAA0B;YACtCgB,cAAc,EAAE,YAAY;YAC5B,SAAS,EAAE;cACThB,UAAU,EAAE,0BAA0B;cACtC0B,SAAS,EAAE;YACb,CAAC;YACDrB,UAAU,EAAE;UACd,CAAE;UAAAnB,QAAA,eAEFpC,OAAA,CAACxB,SAAS;YAAA2C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CAAC,eAEdtB,OAAA,CAACxC,aAAa;MAACyE,EAAE,EAAE;QAAEI,CAAC,EAAE,CAAC;QAAEiC,OAAO,EAAE,MAAM;QAAEnC,MAAM,EAAE;MAAO,CAAE;MAAAC,QAAA,GAE1DxB,QAAQ,iBACPZ,OAAA,CAAC1B,MAAM;QACLuG,MAAM,EAAC,MAAM;QACb3E,IAAI,EAAEM,cAAe;QACrBL,OAAO,EAAEA,CAAA,KAAMM,iBAAiB,CAAC,KAAK,CAAE;QACxCqD,UAAU,EAAE;UACV7B,EAAE,EAAE;YACFiB,UAAU,EAAGvC,KAAK,IAAKA,KAAK,CAACwC,OAAO,CAACC,IAAI,KAAK,MAAM,GAChD,wBAAwB,GACxB,2BAA2B;YAC/Bc,cAAc,EAAE;UAClB;QACF,CAAE;QAAA9B,QAAA,EAEDJ;MAAc;QAAAb,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CACT,EAGA,CAACV,QAAQ,iBACRZ,OAAA,CAAC/B,KAAK;QACJ6G,SAAS,EAAE,CAAE;QACb7C,EAAE,EAAE;UACFiB,UAAU,EAAGvC,KAAK,IAAKA,KAAK,CAACwC,OAAO,CAACC,IAAI,KAAK,MAAM,GAChD,2BAA2B,GAC3B,qBAAqB;UACzBC,WAAW,EAAE,WAAW;UACxBd,WAAW,EAAE,SAAS;UACtBwB,YAAY,EAAE;QAChB,CAAE;QAAA3B,QAAA,EAEDJ;MAAc;QAAAb,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CACR,eAGDtB,OAAA,CAACvC,GAAG;QACFwE,EAAE,EAAE;UACF8C,IAAI,EAAE,CAAC;UACP1C,CAAC,EAAE,CAAC;UACJgC,QAAQ,EAAE,MAAM;UAChBnB,UAAU,EAAGvC,KAAK,IAAKA,KAAK,CAACwC,OAAO,CAACC,IAAI,KAAK,MAAM,GAChD,qBAAqB,GACrB;QACN,CAAE;QAAAhB,QAAA,eAEFpC,OAAA,CAAC7B,IAAI;UAAC6G,EAAE,EAAE,IAAK;UAACC,OAAO,EAAE,GAAI;UAAA7C,QAAA,eAC3BpC,OAAA,CAACvC,GAAG;YAAA2E,QAAA,EACDP,aAAa,CAAC;UAAC;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACO,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEb,CAAC;AAAClB,EAAA,CAnRIH,sBAAsB;EAAA,QAGEV,OAAO,EACrBnB,QAAQ,EACLC,aAAa;AAAA;AAAA6G,EAAA,GAL1BjF,sBAAsB;AAqR5B,eAAeA,sBAAsB;AAAC,IAAAiF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}