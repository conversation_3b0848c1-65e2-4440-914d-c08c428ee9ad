{"ast": null, "code": "var _jsxFileName = \"E:\\\\PythonProjects\\\\React.js\\\\Smart AI Assisstant\\\\src\\\\components\\\\Header.jsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { AppBar, Toolbar, Typography, IconButton, Switch, FormControlLabel, Box, Button, Menu, MenuItem, Container, Tooltip, Avatar, Badge } from '@mui/material';\nimport { Security as SecurityIcon, DarkMode as DarkModeIcon, LightMode as LightModeIcon, Language as LanguageIcon, History as HistoryIcon, Home as HomeIcon, Info as InfoIcon, Notifications as NotificationsIcon, Settings as SettingsIcon, Star as StarIcon } from '@mui/icons-material';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport { useScan } from '../contexts/ScanContext';\nimport SettingsDialog from './SettingsDialog';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Header = /*#__PURE__*/_s(/*#__PURE__*/React.memo(_c = _s(() => {\n  _s();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const {\n    darkMode,\n    language,\n    setLanguage,\n    notifications\n  } = useScan();\n  const [languageAnchor, setLanguageAnchor] = React.useState(null);\n  const [settingsAnchor, setSettingsAnchor] = React.useState(null);\n  const [settingsDialogOpen, setSettingsDialogOpen] = React.useState(false);\n  const handleLanguageClick = event => {\n    setLanguageAnchor(event.currentTarget);\n  };\n  const handleLanguageClose = () => {\n    setLanguageAnchor(null);\n  };\n  const handleLanguageSelect = lang => {\n    setLanguage(lang);\n    handleLanguageClose();\n  };\n  const handleSettingsClick = event => {\n    setSettingsAnchor(event.currentTarget);\n  };\n  const handleSettingsClose = () => {\n    setSettingsAnchor(null);\n  };\n  const handleOpenSettingsDialog = () => {\n    setSettingsDialogOpen(true);\n    handleSettingsClose();\n  };\n  const isHomePage = location.pathname === '/';\n  const isSmartFeaturesPage = location.pathname === '/smart-features';\n  const isHistoryPage = location.pathname === '/history';\n  const isAboutPage = location.pathname === '/about';\n  const isPlansPage = location.pathname === '/plans';\n  const navigationItems = [{\n    label: 'Home',\n    path: '/',\n    icon: /*#__PURE__*/_jsxDEV(HomeIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 75,\n      columnNumber: 39\n    }, this),\n    active: isHomePage\n  }, {\n    label: 'Smart Features',\n    path: '/smart-features',\n    icon: /*#__PURE__*/_jsxDEV(SecurityIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 76,\n      columnNumber: 63\n    }, this),\n    active: isSmartFeaturesPage\n  }, {\n    label: 'History',\n    path: '/history',\n    icon: /*#__PURE__*/_jsxDEV(HistoryIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 77,\n      columnNumber: 49\n    }, this),\n    active: isHistoryPage\n  }, {\n    label: 'Plans',\n    path: '/plans',\n    icon: /*#__PURE__*/_jsxDEV(StarIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 78,\n      columnNumber: 45\n    }, this),\n    active: isPlansPage\n  }, {\n    label: 'About',\n    path: '/about',\n    icon: /*#__PURE__*/_jsxDEV(InfoIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 79,\n      columnNumber: 45\n    }, this),\n    active: isAboutPage\n  }];\n  return /*#__PURE__*/_jsxDEV(AppBar, {\n    position: \"sticky\",\n    elevation: 0,\n    sx: {\n      background: theme => theme.palette.mode === 'dark' ? 'linear-gradient(135deg, rgba(12, 12, 12, 0.98) 0%, rgba(26, 26, 26, 0.98) 50%, rgba(45, 45, 45, 0.98) 100%)' : 'linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%)',\n      backdropFilter: 'blur(25px) saturate(180%)',\n      borderBottom: theme => theme.palette.mode === 'dark' ? '1px solid rgba(255, 255, 255, 0.1)' : '1px solid rgba(255, 255, 255, 0.2)',\n      boxShadow: theme => theme.palette.mode === 'dark' ? '0 4px 20px rgba(102, 126, 234, 0.1)' : '0 4px 20px rgba(102, 126, 234, 0.3)',\n      position: 'relative',\n      color: 'white',\n      '&::after': {\n        content: '\"\"',\n        position: 'absolute',\n        bottom: 0,\n        left: 0,\n        right: 0,\n        height: '2px',\n        background: theme => theme.palette.mode === 'dark' ? 'linear-gradient(90deg, transparent 0%, #667eea 50%, transparent 100%)' : 'linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.8) 50%, transparent 100%)',\n        opacity: 0.6\n      }\n    },\n    children: [/*#__PURE__*/_jsxDEV(Container, {\n      maxWidth: \"xl\",\n      children: /*#__PURE__*/_jsxDEV(Toolbar, {\n        sx: {\n          justifyContent: 'space-between',\n          py: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          display: \"flex\",\n          alignItems: \"center\",\n          gap: 2,\n          children: [/*#__PURE__*/_jsxDEV(IconButton, {\n            edge: \"start\",\n            color: \"inherit\",\n            \"aria-label\": \"security logo\",\n            onClick: () => navigate('/'),\n            sx: {\n              background: 'rgba(255,255,255,0.1)',\n              backdropFilter: 'blur(10px)',\n              '&:hover': {\n                background: 'rgba(255,255,255,0.2)',\n                transform: 'scale(1.05)'\n              },\n              transition: 'all 0.2s ease'\n            },\n            children: /*#__PURE__*/_jsxDEV(SecurityIcon, {\n              fontSize: \"large\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 132,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 117,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h4\",\n              component: \"h1\",\n              sx: {\n                fontWeight: 800,\n                cursor: 'pointer',\n                display: {\n                  xs: 'none',\n                  md: 'block'\n                },\n                color: 'white',\n                textShadow: '0 2px 8px rgba(0,0,0,0.3)',\n                '&:hover': {\n                  textShadow: '0 2px 12px rgba(255,255,255,0.3)'\n                },\n                transition: 'all 0.3s ease'\n              },\n              onClick: () => navigate('/'),\n              children: \"AI Security Guard\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 135,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              component: \"h1\",\n              sx: {\n                fontWeight: 700,\n                cursor: 'pointer',\n                display: {\n                  xs: 'block',\n                  md: 'none'\n                },\n                color: 'white'\n              },\n              onClick: () => navigate('/'),\n              children: \"AI Security\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 153,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              sx: {\n                display: {\n                  xs: 'none',\n                  md: 'block'\n                },\n                background: 'linear-gradient(90deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%)',\n                backgroundSize: '200% 200%',\n                backgroundClip: 'text',\n                WebkitBackgroundClip: 'text',\n                WebkitTextFillColor: 'transparent',\n                animation: 'movingText 4s ease infinite',\n                fontSize: '0.75rem',\n                fontWeight: 600,\n                letterSpacing: '0.5px',\n                textTransform: 'uppercase',\n                '@keyframes movingText': {\n                  '0%': {\n                    backgroundPosition: '0% 50%'\n                  },\n                  '50%': {\n                    backgroundPosition: '100% 50%'\n                  },\n                  '100%': {\n                    backgroundPosition: '0% 50%'\n                  }\n                }\n              },\n              children: \"Advanced Threat Detection Platform\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 166,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 134,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 116,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          display: \"flex\",\n          alignItems: \"center\",\n          gap: 2,\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            display: {\n              xs: 'none',\n              md: 'flex'\n            },\n            gap: 1,\n            children: navigationItems.map(item => /*#__PURE__*/_jsxDEV(Button, {\n              color: \"inherit\",\n              startIcon: item.icon,\n              onClick: () => navigate(item.path),\n              variant: item.active ? 'contained' : 'text',\n              sx: {\n                borderRadius: 3,\n                px: 3,\n                py: 1,\n                fontWeight: 600,\n                textTransform: 'none',\n                background: item.active ? 'rgba(255,255,255,0.2)' : 'transparent',\n                backdropFilter: item.active ? 'blur(10px)' : 'none',\n                border: item.active ? '1px solid rgba(255,255,255,0.3)' : 'none',\n                '&:hover': {\n                  background: 'rgba(255,255,255,0.15)',\n                  transform: 'translateY(-1px)'\n                },\n                transition: 'all 0.2s ease'\n              },\n              children: item.label\n            }, item.path, false, {\n              fileName: _jsxFileName,\n              lineNumber: 197,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            display: {\n              xs: 'flex',\n              md: 'none'\n            },\n            gap: 0.5,\n            children: navigationItems.map(item => /*#__PURE__*/_jsxDEV(Tooltip, {\n              title: item.label,\n              children: /*#__PURE__*/_jsxDEV(IconButton, {\n                color: \"inherit\",\n                onClick: () => navigate(item.path),\n                sx: {\n                  background: item.active ? 'rgba(255,255,255,0.2)' : 'transparent',\n                  backdropFilter: item.active ? 'blur(10px)' : 'none',\n                  border: item.active ? '1px solid rgba(255,255,255,0.3)' : 'none',\n                  '&:hover': {\n                    background: 'rgba(255,255,255,0.15)'\n                  }\n                },\n                children: item.icon\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 230,\n                columnNumber: 19\n              }, this)\n            }, item.path, false, {\n              fileName: _jsxFileName,\n              lineNumber: 229,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 227,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n            title: \"Notifications\",\n            children: /*#__PURE__*/_jsxDEV(IconButton, {\n              color: \"inherit\",\n              children: /*#__PURE__*/_jsxDEV(Badge, {\n                badgeContent: notifications.length,\n                color: \"error\",\n                children: /*#__PURE__*/_jsxDEV(NotificationsIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 254,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 253,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 252,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 251,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n            title: \"Settings\",\n            children: /*#__PURE__*/_jsxDEV(IconButton, {\n              color: \"inherit\",\n              onClick: handleSettingsClick,\n              sx: {\n                background: 'rgba(255,255,255,0.1)',\n                '&:hover': {\n                  background: 'rgba(255,255,255,0.2)'\n                }\n              },\n              children: /*#__PURE__*/_jsxDEV(SettingsIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 271,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 261,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 260,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Menu, {\n            anchorEl: settingsAnchor,\n            open: Boolean(settingsAnchor),\n            onClose: handleSettingsClose,\n            anchorOrigin: {\n              vertical: 'bottom',\n              horizontal: 'right'\n            },\n            transformOrigin: {\n              vertical: 'top',\n              horizontal: 'right'\n            },\n            PaperProps: {\n              sx: {\n                mt: 1,\n                borderRadius: 2,\n                minWidth: 200\n              }\n            },\n            children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: handleOpenSettingsDialog,\n              sx: {\n                gap: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(SettingsIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 297,\n                columnNumber: 17\n              }, this), \"Account & Settings\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 296,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: handleLanguageClick,\n              sx: {\n                gap: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(LanguageIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 303,\n                columnNumber: 17\n              }, this), \"Language\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 302,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => setSettingsDialogOpen(true),\n              sx: {\n                gap: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(SettingsIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 309,\n                columnNumber: 17\n              }, this), \"Settings\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 308,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 275,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Menu, {\n            anchorEl: languageAnchor,\n            open: Boolean(languageAnchor),\n            onClose: handleLanguageClose,\n            anchorOrigin: {\n              vertical: 'bottom',\n              horizontal: 'right'\n            },\n            transformOrigin: {\n              vertical: 'top',\n              horizontal: 'right'\n            },\n            PaperProps: {\n              sx: {\n                mt: 1,\n                borderRadius: 2\n              }\n            },\n            children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => handleLanguageSelect('en'),\n              selected: language === 'en',\n              children: \"\\uD83C\\uDDFA\\uD83C\\uDDF8 English\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 334,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => handleLanguageSelect('ar'),\n              selected: language === 'ar',\n              children: \"\\uD83C\\uDDF8\\uD83C\\uDDE6 \\u0627\\u0644\\u0639\\u0631\\u0628\\u064A\\u0629\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 340,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 315,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Avatar, {\n            sx: {\n              width: 40,\n              height: 40,\n              bgcolor: 'rgba(255,255,255,0.2)',\n              border: '2px solid rgba(255,255,255,0.3)',\n              cursor: 'pointer',\n              '&:hover': {\n                transform: 'scale(1.05)'\n              },\n              transition: 'transform 0.2s ease'\n            },\n            children: /*#__PURE__*/_jsxDEV(SecurityIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 362,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 349,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 193,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 114,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 113,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(SettingsDialog, {\n      open: settingsDialogOpen,\n      onClose: () => setSettingsDialogOpen(false)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 369,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 83,\n    columnNumber: 5\n  }, this);\n}, \"G8PopQu9wwgQkotCAfwE803doNE=\", false, function () {\n  return [useNavigate, useLocation, useScan];\n})), \"G8PopQu9wwgQkotCAfwE803doNE=\", false, function () {\n  return [useNavigate, useLocation, useScan];\n});\n_c2 = Header;\nHeader.displayName = 'Header';\nexport default Header;\nvar _c, _c2;\n$RefreshReg$(_c, \"Header$React.memo\");\n$RefreshReg$(_c2, \"Header\");", "map": {"version": 3, "names": ["React", "AppBar", "<PERSON><PERSON><PERSON>", "Typography", "IconButton", "Switch", "FormControlLabel", "Box", "<PERSON><PERSON>", "<PERSON><PERSON>", "MenuItem", "Container", "<PERSON><PERSON><PERSON>", "Avatar", "Badge", "Security", "SecurityIcon", "DarkMode", "DarkModeIcon", "LightMode", "LightModeIcon", "Language", "LanguageIcon", "History", "HistoryIcon", "Home", "HomeIcon", "Info", "InfoIcon", "Notifications", "NotificationsIcon", "Settings", "SettingsIcon", "Star", "StarIcon", "useNavigate", "useLocation", "useScan", "SettingsDialog", "jsxDEV", "_jsxDEV", "Header", "_s", "memo", "_c", "navigate", "location", "darkMode", "language", "setLanguage", "notifications", "languageAnchor", "setLanguageAnchor", "useState", "settingsAnchor", "setSettingsAnchor", "settingsDialogOpen", "setSettingsDialogOpen", "handleLanguageClick", "event", "currentTarget", "handleLanguageClose", "handleLanguageSelect", "lang", "handleSettingsClick", "handleSettingsClose", "handleOpenSettingsDialog", "isHomePage", "pathname", "isSmartFeaturesPage", "isHistoryPage", "isAboutPage", "isPlansPage", "navigationItems", "label", "path", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "active", "position", "elevation", "sx", "background", "theme", "palette", "mode", "<PERSON><PERSON>ilter", "borderBottom", "boxShadow", "color", "content", "bottom", "left", "right", "height", "opacity", "children", "max<PERSON><PERSON><PERSON>", "justifyContent", "py", "display", "alignItems", "gap", "edge", "onClick", "transform", "transition", "fontSize", "variant", "component", "fontWeight", "cursor", "xs", "md", "textShadow", "backgroundSize", "backgroundClip", "WebkitBackgroundClip", "WebkitTextFillColor", "animation", "letterSpacing", "textTransform", "backgroundPosition", "map", "item", "startIcon", "borderRadius", "px", "border", "title", "badgeContent", "length", "anchorEl", "open", "Boolean", "onClose", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "transform<PERSON><PERSON>in", "PaperProps", "mt", "min<PERSON><PERSON><PERSON>", "selected", "width", "bgcolor", "_c2", "displayName", "$RefreshReg$"], "sources": ["E:/PythonProjects/React.js/Smart AI Assisstant/src/components/Header.jsx"], "sourcesContent": ["import React from 'react';\nimport {\n  A<PERSON><PERSON><PERSON>,\n  <PERSON><PERSON><PERSON>,\n  <PERSON><PERSON><PERSON>,\n  IconButton,\n  Switch,\n  FormControlLabel,\n  Box,\n  Button,\n  Menu,\n  MenuItem,\n  Container,\n  Tooltip,\n  Avatar,\n  Badge,\n} from '@mui/material';\nimport {\n  Security as SecurityIcon,\n  DarkMode as DarkModeIcon,\n  LightMode as LightModeIcon,\n  Language as LanguageIcon,\n  History as HistoryIcon,\n  Home as HomeIcon,\n  Info as InfoIcon,\n  Notifications as NotificationsIcon,\n  Settings as SettingsIcon,\n  Star as StarIcon,\n} from '@mui/icons-material';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport { useScan } from '../contexts/ScanContext';\nimport SettingsDialog from './SettingsDialog';\n\nconst Header = React.memo(() => {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const { darkMode, language, setLanguage, notifications } = useScan();\n  const [languageAnchor, setLanguageAnchor] = React.useState(null);\n  const [settingsAnchor, setSettingsAnchor] = React.useState(null);\n  const [settingsDialogOpen, setSettingsDialogOpen] = React.useState(false);\n\n  const handleLanguageClick = (event) => {\n    setLanguageAnchor(event.currentTarget);\n  };\n\n  const handleLanguageClose = () => {\n    setLanguageAnchor(null);\n  };\n\n  const handleLanguageSelect = (lang) => {\n    setLanguage(lang);\n    handleLanguageClose();\n  };\n\n  const handleSettingsClick = (event) => {\n    setSettingsAnchor(event.currentTarget);\n  };\n\n  const handleSettingsClose = () => {\n    setSettingsAnchor(null);\n  };\n\n  const handleOpenSettingsDialog = () => {\n    setSettingsDialogOpen(true);\n    handleSettingsClose();\n  };\n\n  const isHomePage = location.pathname === '/';\n  const isSmartFeaturesPage = location.pathname === '/smart-features';\n  const isHistoryPage = location.pathname === '/history';\n  const isAboutPage = location.pathname === '/about';\n  const isPlansPage = location.pathname === '/plans';\n\n  const navigationItems = [\n    { label: 'Home', path: '/', icon: <HomeIcon />, active: isHomePage },\n    { label: 'Smart Features', path: '/smart-features', icon: <SecurityIcon />, active: isSmartFeaturesPage },\n    { label: 'History', path: '/history', icon: <HistoryIcon />, active: isHistoryPage },\n    { label: 'Plans', path: '/plans', icon: <StarIcon />, active: isPlansPage },\n    { label: 'About', path: '/about', icon: <InfoIcon />, active: isAboutPage },\n  ];\n\n  return (\n    <AppBar\n      position=\"sticky\"\n      elevation={0}\n      sx={{\n        background: (theme) => theme.palette.mode === 'dark'\n          ? 'linear-gradient(135deg, rgba(12, 12, 12, 0.98) 0%, rgba(26, 26, 26, 0.98) 50%, rgba(45, 45, 45, 0.98) 100%)'\n          : 'linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%)',\n        backdropFilter: 'blur(25px) saturate(180%)',\n        borderBottom: (theme) => theme.palette.mode === 'dark'\n          ? '1px solid rgba(255, 255, 255, 0.1)'\n          : '1px solid rgba(255, 255, 255, 0.2)',\n        boxShadow: (theme) => theme.palette.mode === 'dark'\n          ? '0 4px 20px rgba(102, 126, 234, 0.1)'\n          : '0 4px 20px rgba(102, 126, 234, 0.3)',\n        position: 'relative',\n        color: 'white',\n        '&::after': {\n          content: '\"\"',\n          position: 'absolute',\n          bottom: 0,\n          left: 0,\n          right: 0,\n          height: '2px',\n          background: (theme) => theme.palette.mode === 'dark'\n            ? 'linear-gradient(90deg, transparent 0%, #667eea 50%, transparent 100%)'\n            : 'linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.8) 50%, transparent 100%)',\n          opacity: 0.6,\n        },\n      }}\n    >\n      <Container maxWidth=\"xl\">\n        <Toolbar sx={{ justifyContent: 'space-between', py: 1 }}>\n          {/* Logo and Title */}\n          <Box display=\"flex\" alignItems=\"center\" gap={2}>\n            <IconButton\n              edge=\"start\"\n              color=\"inherit\"\n              aria-label=\"security logo\"\n              onClick={() => navigate('/')}\n              sx={{\n                background: 'rgba(255,255,255,0.1)',\n                backdropFilter: 'blur(10px)',\n                '&:hover': {\n                  background: 'rgba(255,255,255,0.2)',\n                  transform: 'scale(1.05)',\n                },\n                transition: 'all 0.2s ease',\n              }}\n            >\n              <SecurityIcon fontSize=\"large\" />\n            </IconButton>\n            <Box>\n              <Typography\n                variant=\"h4\"\n                component=\"h1\"\n                sx={{\n                  fontWeight: 800,\n                  cursor: 'pointer',\n                  display: { xs: 'none', md: 'block' },\n                  color: 'white',\n                  textShadow: '0 2px 8px rgba(0,0,0,0.3)',\n                  '&:hover': {\n                    textShadow: '0 2px 12px rgba(255,255,255,0.3)',\n                  },\n                  transition: 'all 0.3s ease',\n                }}\n                onClick={() => navigate('/')}\n              >\n                AI Security Guard\n              </Typography>\n              <Typography\n                variant=\"h6\"\n                component=\"h1\"\n                sx={{\n                  fontWeight: 700,\n                  cursor: 'pointer',\n                  display: { xs: 'block', md: 'none' },\n                  color: 'white',\n                }}\n                onClick={() => navigate('/')}\n              >\n                AI Security\n              </Typography>\n              <Typography\n                variant=\"caption\"\n                sx={{\n                  display: { xs: 'none', md: 'block' },\n                  background: 'linear-gradient(90deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%)',\n                  backgroundSize: '200% 200%',\n                  backgroundClip: 'text',\n                  WebkitBackgroundClip: 'text',\n                  WebkitTextFillColor: 'transparent',\n                  animation: 'movingText 4s ease infinite',\n                  fontSize: '0.75rem',\n                  fontWeight: 600,\n                  letterSpacing: '0.5px',\n                  textTransform: 'uppercase',\n                  '@keyframes movingText': {\n                    '0%': { backgroundPosition: '0% 50%' },\n                    '50%': { backgroundPosition: '100% 50%' },\n                    '100%': { backgroundPosition: '0% 50%' },\n                  },\n                }}\n              >\n                Advanced Threat Detection Platform\n              </Typography>\n            </Box>\n          </Box>\n\n          {/* Navigation and Controls */}\n          <Box display=\"flex\" alignItems=\"center\" gap={2}>\n            {/* Desktop Navigation */}\n            <Box display={{ xs: 'none', md: 'flex' }} gap={1}>\n              {navigationItems.map((item) => (\n                <Button\n                  key={item.path}\n                  color=\"inherit\"\n                  startIcon={item.icon}\n                  onClick={() => navigate(item.path)}\n                  variant={item.active ? 'contained' : 'text'}\n                  sx={{\n                    borderRadius: 3,\n                    px: 3,\n                    py: 1,\n                    fontWeight: 600,\n                    textTransform: 'none',\n                    background: item.active\n                      ? 'rgba(255,255,255,0.2)'\n                      : 'transparent',\n                    backdropFilter: item.active ? 'blur(10px)' : 'none',\n                    border: item.active ? '1px solid rgba(255,255,255,0.3)' : 'none',\n                    '&:hover': {\n                      background: 'rgba(255,255,255,0.15)',\n                      transform: 'translateY(-1px)',\n                    },\n                    transition: 'all 0.2s ease',\n                  }}\n                >\n                  {item.label}\n                </Button>\n              ))}\n            </Box>\n\n            {/* Mobile Navigation */}\n            <Box display={{ xs: 'flex', md: 'none' }} gap={0.5}>\n              {navigationItems.map((item) => (\n                <Tooltip key={item.path} title={item.label}>\n                  <IconButton\n                    color=\"inherit\"\n                    onClick={() => navigate(item.path)}\n                    sx={{\n                      background: item.active\n                        ? 'rgba(255,255,255,0.2)'\n                        : 'transparent',\n                      backdropFilter: item.active ? 'blur(10px)' : 'none',\n                      border: item.active ? '1px solid rgba(255,255,255,0.3)' : 'none',\n                      '&:hover': {\n                        background: 'rgba(255,255,255,0.15)',\n                      },\n                    }}\n                  >\n                    {item.icon}\n                  </IconButton>\n                </Tooltip>\n              ))}\n            </Box>\n\n            {/* Notifications */}\n            <Tooltip title=\"Notifications\">\n              <IconButton color=\"inherit\">\n                <Badge badgeContent={notifications.length} color=\"error\">\n                  <NotificationsIcon />\n                </Badge>\n              </IconButton>\n            </Tooltip>\n\n            {/* Settings Menu */}\n            <Tooltip title=\"Settings\">\n              <IconButton\n                color=\"inherit\"\n                onClick={handleSettingsClick}\n                sx={{\n                  background: 'rgba(255,255,255,0.1)',\n                  '&:hover': {\n                    background: 'rgba(255,255,255,0.2)',\n                  },\n                }}\n              >\n                <SettingsIcon />\n              </IconButton>\n            </Tooltip>\n\n            <Menu\n              anchorEl={settingsAnchor}\n              open={Boolean(settingsAnchor)}\n              onClose={handleSettingsClose}\n              anchorOrigin={{\n                vertical: 'bottom',\n                horizontal: 'right',\n              }}\n              transformOrigin={{\n                vertical: 'top',\n                horizontal: 'right',\n              }}\n              PaperProps={{\n                sx: {\n                  mt: 1,\n                  borderRadius: 2,\n                  minWidth: 200,\n                },\n              }}\n            >\n              {/* Settings Options */}\n              <MenuItem onClick={handleOpenSettingsDialog} sx={{ gap: 2 }}>\n                <SettingsIcon />\n                Account & Settings\n              </MenuItem>\n\n              {/* Language Selector */}\n              <MenuItem onClick={handleLanguageClick} sx={{ gap: 2 }}>\n                <LanguageIcon />\n                Language\n              </MenuItem>\n\n              {/* Settings */}\n              <MenuItem onClick={() => setSettingsDialogOpen(true)} sx={{ gap: 2 }}>\n                <SettingsIcon />\n                Settings\n              </MenuItem>\n            </Menu>\n\n            {/* Language Menu */}\n            <Menu\n              anchorEl={languageAnchor}\n              open={Boolean(languageAnchor)}\n              onClose={handleLanguageClose}\n              anchorOrigin={{\n                vertical: 'bottom',\n                horizontal: 'right',\n              }}\n              transformOrigin={{\n                vertical: 'top',\n                horizontal: 'right',\n              }}\n              PaperProps={{\n                sx: {\n                  mt: 1,\n                  borderRadius: 2,\n                },\n              }}\n            >\n              <MenuItem\n                onClick={() => handleLanguageSelect('en')}\n                selected={language === 'en'}\n              >\n                🇺🇸 English\n              </MenuItem>\n              <MenuItem\n                onClick={() => handleLanguageSelect('ar')}\n                selected={language === 'ar'}\n              >\n                🇸🇦 العربية\n              </MenuItem>\n            </Menu>\n\n            {/* User Avatar */}\n            <Avatar\n              sx={{\n                width: 40,\n                height: 40,\n                bgcolor: 'rgba(255,255,255,0.2)',\n                border: '2px solid rgba(255,255,255,0.3)',\n                cursor: 'pointer',\n                '&:hover': {\n                  transform: 'scale(1.05)',\n                },\n                transition: 'transform 0.2s ease',\n              }}\n            >\n              <SecurityIcon />\n            </Avatar>\n          </Box>\n        </Toolbar>\n      </Container>\n\n      {/* Settings Dialog */}\n      <SettingsDialog\n        open={settingsDialogOpen}\n        onClose={() => setSettingsDialogOpen(false)}\n      />\n    </AppBar>\n  );\n});\n\nHeader.displayName = 'Header';\n\nexport default Header;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SACEC,MAAM,EACNC,OAAO,EACPC,UAAU,EACVC,UAAU,EACVC,MAAM,EACNC,gBAAgB,EAChBC,GAAG,EACHC,MAAM,EACNC,IAAI,EACJC,QAAQ,EACRC,SAAS,EACTC,OAAO,EACPC,MAAM,EACNC,KAAK,QACA,eAAe;AACtB,SACEC,QAAQ,IAAIC,YAAY,EACxBC,QAAQ,IAAIC,YAAY,EACxBC,SAAS,IAAIC,aAAa,EAC1BC,QAAQ,IAAIC,YAAY,EACxBC,OAAO,IAAIC,WAAW,EACtBC,IAAI,IAAIC,QAAQ,EAChBC,IAAI,IAAIC,QAAQ,EAChBC,aAAa,IAAIC,iBAAiB,EAClCC,QAAQ,IAAIC,YAAY,EACxBC,IAAI,IAAIC,QAAQ,QACX,qBAAqB;AAC5B,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,SAASC,OAAO,QAAQ,yBAAyB;AACjD,OAAOC,cAAc,MAAM,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9C,MAAMC,MAAM,gBAAAC,EAAA,cAAG1C,KAAK,CAAC2C,IAAI,CAAAC,EAAA,GAAAF,EAAA,CAAC,MAAM;EAAAA,EAAA;EAC9B,MAAMG,QAAQ,GAAGV,WAAW,CAAC,CAAC;EAC9B,MAAMW,QAAQ,GAAGV,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEW,QAAQ;IAAEC,QAAQ;IAAEC,WAAW;IAAEC;EAAc,CAAC,GAAGb,OAAO,CAAC,CAAC;EACpE,MAAM,CAACc,cAAc,EAAEC,iBAAiB,CAAC,GAAGpD,KAAK,CAACqD,QAAQ,CAAC,IAAI,CAAC;EAChE,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGvD,KAAK,CAACqD,QAAQ,CAAC,IAAI,CAAC;EAChE,MAAM,CAACG,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGzD,KAAK,CAACqD,QAAQ,CAAC,KAAK,CAAC;EAEzE,MAAMK,mBAAmB,GAAIC,KAAK,IAAK;IACrCP,iBAAiB,CAACO,KAAK,CAACC,aAAa,CAAC;EACxC,CAAC;EAED,MAAMC,mBAAmB,GAAGA,CAAA,KAAM;IAChCT,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;EAED,MAAMU,oBAAoB,GAAIC,IAAI,IAAK;IACrCd,WAAW,CAACc,IAAI,CAAC;IACjBF,mBAAmB,CAAC,CAAC;EACvB,CAAC;EAED,MAAMG,mBAAmB,GAAIL,KAAK,IAAK;IACrCJ,iBAAiB,CAACI,KAAK,CAACC,aAAa,CAAC;EACxC,CAAC;EAED,MAAMK,mBAAmB,GAAGA,CAAA,KAAM;IAChCV,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;EAED,MAAMW,wBAAwB,GAAGA,CAAA,KAAM;IACrCT,qBAAqB,CAAC,IAAI,CAAC;IAC3BQ,mBAAmB,CAAC,CAAC;EACvB,CAAC;EAED,MAAME,UAAU,GAAGrB,QAAQ,CAACsB,QAAQ,KAAK,GAAG;EAC5C,MAAMC,mBAAmB,GAAGvB,QAAQ,CAACsB,QAAQ,KAAK,iBAAiB;EACnE,MAAME,aAAa,GAAGxB,QAAQ,CAACsB,QAAQ,KAAK,UAAU;EACtD,MAAMG,WAAW,GAAGzB,QAAQ,CAACsB,QAAQ,KAAK,QAAQ;EAClD,MAAMI,WAAW,GAAG1B,QAAQ,CAACsB,QAAQ,KAAK,QAAQ;EAElD,MAAMK,eAAe,GAAG,CACtB;IAAEC,KAAK,EAAE,MAAM;IAAEC,IAAI,EAAE,GAAG;IAAEC,IAAI,eAAEpC,OAAA,CAACd,QAAQ;MAAAmD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,MAAM,EAAEd;EAAW,CAAC,EACpE;IAAEO,KAAK,EAAE,gBAAgB;IAAEC,IAAI,EAAE,iBAAiB;IAAEC,IAAI,eAAEpC,OAAA,CAACxB,YAAY;MAAA6D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,MAAM,EAAEZ;EAAoB,CAAC,EACzG;IAAEK,KAAK,EAAE,SAAS;IAAEC,IAAI,EAAE,UAAU;IAAEC,IAAI,eAAEpC,OAAA,CAAChB,WAAW;MAAAqD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,MAAM,EAAEX;EAAc,CAAC,EACpF;IAAEI,KAAK,EAAE,OAAO;IAAEC,IAAI,EAAE,QAAQ;IAAEC,IAAI,eAAEpC,OAAA,CAACN,QAAQ;MAAA2C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,MAAM,EAAET;EAAY,CAAC,EAC3E;IAAEE,KAAK,EAAE,OAAO;IAAEC,IAAI,EAAE,QAAQ;IAAEC,IAAI,eAAEpC,OAAA,CAACZ,QAAQ;MAAAiD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,MAAM,EAAEV;EAAY,CAAC,CAC5E;EAED,oBACE/B,OAAA,CAACvC,MAAM;IACLiF,QAAQ,EAAC,QAAQ;IACjBC,SAAS,EAAE,CAAE;IACbC,EAAE,EAAE;MACFC,UAAU,EAAGC,KAAK,IAAKA,KAAK,CAACC,OAAO,CAACC,IAAI,KAAK,MAAM,GAChD,6GAA6G,GAC7G,gEAAgE;MACpEC,cAAc,EAAE,2BAA2B;MAC3CC,YAAY,EAAGJ,KAAK,IAAKA,KAAK,CAACC,OAAO,CAACC,IAAI,KAAK,MAAM,GAClD,oCAAoC,GACpC,oCAAoC;MACxCG,SAAS,EAAGL,KAAK,IAAKA,KAAK,CAACC,OAAO,CAACC,IAAI,KAAK,MAAM,GAC/C,qCAAqC,GACrC,qCAAqC;MACzCN,QAAQ,EAAE,UAAU;MACpBU,KAAK,EAAE,OAAO;MACd,UAAU,EAAE;QACVC,OAAO,EAAE,IAAI;QACbX,QAAQ,EAAE,UAAU;QACpBY,MAAM,EAAE,CAAC;QACTC,IAAI,EAAE,CAAC;QACPC,KAAK,EAAE,CAAC;QACRC,MAAM,EAAE,KAAK;QACbZ,UAAU,EAAGC,KAAK,IAAKA,KAAK,CAACC,OAAO,CAACC,IAAI,KAAK,MAAM,GAChD,uEAAuE,GACvE,wFAAwF;QAC5FU,OAAO,EAAE;MACX;IACF,CAAE;IAAAC,QAAA,gBAEF3D,OAAA,CAAC7B,SAAS;MAACyF,QAAQ,EAAC,IAAI;MAAAD,QAAA,eACtB3D,OAAA,CAACtC,OAAO;QAACkF,EAAE,EAAE;UAAEiB,cAAc,EAAE,eAAe;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAH,QAAA,gBAEtD3D,OAAA,CAACjC,GAAG;UAACgG,OAAO,EAAC,MAAM;UAACC,UAAU,EAAC,QAAQ;UAACC,GAAG,EAAE,CAAE;UAAAN,QAAA,gBAC7C3D,OAAA,CAACpC,UAAU;YACTsG,IAAI,EAAC,OAAO;YACZd,KAAK,EAAC,SAAS;YACf,cAAW,eAAe;YAC1Be,OAAO,EAAEA,CAAA,KAAM9D,QAAQ,CAAC,GAAG,CAAE;YAC7BuC,EAAE,EAAE;cACFC,UAAU,EAAE,uBAAuB;cACnCI,cAAc,EAAE,YAAY;cAC5B,SAAS,EAAE;gBACTJ,UAAU,EAAE,uBAAuB;gBACnCuB,SAAS,EAAE;cACb,CAAC;cACDC,UAAU,EAAE;YACd,CAAE;YAAAV,QAAA,eAEF3D,OAAA,CAACxB,YAAY;cAAC8F,QAAQ,EAAC;YAAO;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvB,CAAC,eACbxC,OAAA,CAACjC,GAAG;YAAA4F,QAAA,gBACF3D,OAAA,CAACrC,UAAU;cACT4G,OAAO,EAAC,IAAI;cACZC,SAAS,EAAC,IAAI;cACd5B,EAAE,EAAE;gBACF6B,UAAU,EAAE,GAAG;gBACfC,MAAM,EAAE,SAAS;gBACjBX,OAAO,EAAE;kBAAEY,EAAE,EAAE,MAAM;kBAAEC,EAAE,EAAE;gBAAQ,CAAC;gBACpCxB,KAAK,EAAE,OAAO;gBACdyB,UAAU,EAAE,2BAA2B;gBACvC,SAAS,EAAE;kBACTA,UAAU,EAAE;gBACd,CAAC;gBACDR,UAAU,EAAE;cACd,CAAE;cACFF,OAAO,EAAEA,CAAA,KAAM9D,QAAQ,CAAC,GAAG,CAAE;cAAAsD,QAAA,EAC9B;YAED;cAAAtB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbxC,OAAA,CAACrC,UAAU;cACT4G,OAAO,EAAC,IAAI;cACZC,SAAS,EAAC,IAAI;cACd5B,EAAE,EAAE;gBACF6B,UAAU,EAAE,GAAG;gBACfC,MAAM,EAAE,SAAS;gBACjBX,OAAO,EAAE;kBAAEY,EAAE,EAAE,OAAO;kBAAEC,EAAE,EAAE;gBAAO,CAAC;gBACpCxB,KAAK,EAAE;cACT,CAAE;cACFe,OAAO,EAAEA,CAAA,KAAM9D,QAAQ,CAAC,GAAG,CAAE;cAAAsD,QAAA,EAC9B;YAED;cAAAtB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbxC,OAAA,CAACrC,UAAU;cACT4G,OAAO,EAAC,SAAS;cACjB3B,EAAE,EAAE;gBACFmB,OAAO,EAAE;kBAAEY,EAAE,EAAE,MAAM;kBAAEC,EAAE,EAAE;gBAAQ,CAAC;gBACpC/B,UAAU,EAAE,yFAAyF;gBACrGiC,cAAc,EAAE,WAAW;gBAC3BC,cAAc,EAAE,MAAM;gBACtBC,oBAAoB,EAAE,MAAM;gBAC5BC,mBAAmB,EAAE,aAAa;gBAClCC,SAAS,EAAE,6BAA6B;gBACxCZ,QAAQ,EAAE,SAAS;gBACnBG,UAAU,EAAE,GAAG;gBACfU,aAAa,EAAE,OAAO;gBACtBC,aAAa,EAAE,WAAW;gBAC1B,uBAAuB,EAAE;kBACvB,IAAI,EAAE;oBAAEC,kBAAkB,EAAE;kBAAS,CAAC;kBACtC,KAAK,EAAE;oBAAEA,kBAAkB,EAAE;kBAAW,CAAC;kBACzC,MAAM,EAAE;oBAAEA,kBAAkB,EAAE;kBAAS;gBACzC;cACF,CAAE;cAAA1B,QAAA,EACH;YAED;cAAAtB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNxC,OAAA,CAACjC,GAAG;UAACgG,OAAO,EAAC,MAAM;UAACC,UAAU,EAAC,QAAQ;UAACC,GAAG,EAAE,CAAE;UAAAN,QAAA,gBAE7C3D,OAAA,CAACjC,GAAG;YAACgG,OAAO,EAAE;cAAEY,EAAE,EAAE,MAAM;cAAEC,EAAE,EAAE;YAAO,CAAE;YAACX,GAAG,EAAE,CAAE;YAAAN,QAAA,EAC9C1B,eAAe,CAACqD,GAAG,CAAEC,IAAI,iBACxBvF,OAAA,CAAChC,MAAM;cAELoF,KAAK,EAAC,SAAS;cACfoC,SAAS,EAAED,IAAI,CAACnD,IAAK;cACrB+B,OAAO,EAAEA,CAAA,KAAM9D,QAAQ,CAACkF,IAAI,CAACpD,IAAI,CAAE;cACnCoC,OAAO,EAAEgB,IAAI,CAAC9C,MAAM,GAAG,WAAW,GAAG,MAAO;cAC5CG,EAAE,EAAE;gBACF6C,YAAY,EAAE,CAAC;gBACfC,EAAE,EAAE,CAAC;gBACL5B,EAAE,EAAE,CAAC;gBACLW,UAAU,EAAE,GAAG;gBACfW,aAAa,EAAE,MAAM;gBACrBvC,UAAU,EAAE0C,IAAI,CAAC9C,MAAM,GACnB,uBAAuB,GACvB,aAAa;gBACjBQ,cAAc,EAAEsC,IAAI,CAAC9C,MAAM,GAAG,YAAY,GAAG,MAAM;gBACnDkD,MAAM,EAAEJ,IAAI,CAAC9C,MAAM,GAAG,iCAAiC,GAAG,MAAM;gBAChE,SAAS,EAAE;kBACTI,UAAU,EAAE,wBAAwB;kBACpCuB,SAAS,EAAE;gBACb,CAAC;gBACDC,UAAU,EAAE;cACd,CAAE;cAAAV,QAAA,EAED4B,IAAI,CAACrD;YAAK,GAvBNqD,IAAI,CAACpD,IAAI;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAwBR,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGNxC,OAAA,CAACjC,GAAG;YAACgG,OAAO,EAAE;cAAEY,EAAE,EAAE,MAAM;cAAEC,EAAE,EAAE;YAAO,CAAE;YAACX,GAAG,EAAE,GAAI;YAAAN,QAAA,EAChD1B,eAAe,CAACqD,GAAG,CAAEC,IAAI,iBACxBvF,OAAA,CAAC5B,OAAO;cAAiBwH,KAAK,EAAEL,IAAI,CAACrD,KAAM;cAAAyB,QAAA,eACzC3D,OAAA,CAACpC,UAAU;gBACTwF,KAAK,EAAC,SAAS;gBACfe,OAAO,EAAEA,CAAA,KAAM9D,QAAQ,CAACkF,IAAI,CAACpD,IAAI,CAAE;gBACnCS,EAAE,EAAE;kBACFC,UAAU,EAAE0C,IAAI,CAAC9C,MAAM,GACnB,uBAAuB,GACvB,aAAa;kBACjBQ,cAAc,EAAEsC,IAAI,CAAC9C,MAAM,GAAG,YAAY,GAAG,MAAM;kBACnDkD,MAAM,EAAEJ,IAAI,CAAC9C,MAAM,GAAG,iCAAiC,GAAG,MAAM;kBAChE,SAAS,EAAE;oBACTI,UAAU,EAAE;kBACd;gBACF,CAAE;gBAAAc,QAAA,EAED4B,IAAI,CAACnD;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA;YAAC,GAhBD+C,IAAI,CAACpD,IAAI;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAiBd,CACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGNxC,OAAA,CAAC5B,OAAO;YAACwH,KAAK,EAAC,eAAe;YAAAjC,QAAA,eAC5B3D,OAAA,CAACpC,UAAU;cAACwF,KAAK,EAAC,SAAS;cAAAO,QAAA,eACzB3D,OAAA,CAAC1B,KAAK;gBAACuH,YAAY,EAAEnF,aAAa,CAACoF,MAAO;gBAAC1C,KAAK,EAAC,OAAO;gBAAAO,QAAA,eACtD3D,OAAA,CAACV,iBAAiB;kBAAA+C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAGVxC,OAAA,CAAC5B,OAAO;YAACwH,KAAK,EAAC,UAAU;YAAAjC,QAAA,eACvB3D,OAAA,CAACpC,UAAU;cACTwF,KAAK,EAAC,SAAS;cACfe,OAAO,EAAE3C,mBAAoB;cAC7BoB,EAAE,EAAE;gBACFC,UAAU,EAAE,uBAAuB;gBACnC,SAAS,EAAE;kBACTA,UAAU,EAAE;gBACd;cACF,CAAE;cAAAc,QAAA,eAEF3D,OAAA,CAACR,YAAY;gBAAA6C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAEVxC,OAAA,CAAC/B,IAAI;YACH8H,QAAQ,EAAEjF,cAAe;YACzBkF,IAAI,EAAEC,OAAO,CAACnF,cAAc,CAAE;YAC9BoF,OAAO,EAAEzE,mBAAoB;YAC7B0E,YAAY,EAAE;cACZC,QAAQ,EAAE,QAAQ;cAClBC,UAAU,EAAE;YACd,CAAE;YACFC,eAAe,EAAE;cACfF,QAAQ,EAAE,KAAK;cACfC,UAAU,EAAE;YACd,CAAE;YACFE,UAAU,EAAE;cACV3D,EAAE,EAAE;gBACF4D,EAAE,EAAE,CAAC;gBACLf,YAAY,EAAE,CAAC;gBACfgB,QAAQ,EAAE;cACZ;YACF,CAAE;YAAA9C,QAAA,gBAGF3D,OAAA,CAAC9B,QAAQ;cAACiG,OAAO,EAAEzC,wBAAyB;cAACkB,EAAE,EAAE;gBAAEqB,GAAG,EAAE;cAAE,CAAE;cAAAN,QAAA,gBAC1D3D,OAAA,CAACR,YAAY;gBAAA6C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,sBAElB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eAGXxC,OAAA,CAAC9B,QAAQ;cAACiG,OAAO,EAAEjD,mBAAoB;cAAC0B,EAAE,EAAE;gBAAEqB,GAAG,EAAE;cAAE,CAAE;cAAAN,QAAA,gBACrD3D,OAAA,CAAClB,YAAY;gBAAAuD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,YAElB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eAGXxC,OAAA,CAAC9B,QAAQ;cAACiG,OAAO,EAAEA,CAAA,KAAMlD,qBAAqB,CAAC,IAAI,CAAE;cAAC2B,EAAE,EAAE;gBAAEqB,GAAG,EAAE;cAAE,CAAE;cAAAN,QAAA,gBACnE3D,OAAA,CAACR,YAAY;gBAAA6C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,YAElB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP,CAAC,eAGPxC,OAAA,CAAC/B,IAAI;YACH8H,QAAQ,EAAEpF,cAAe;YACzBqF,IAAI,EAAEC,OAAO,CAACtF,cAAc,CAAE;YAC9BuF,OAAO,EAAE7E,mBAAoB;YAC7B8E,YAAY,EAAE;cACZC,QAAQ,EAAE,QAAQ;cAClBC,UAAU,EAAE;YACd,CAAE;YACFC,eAAe,EAAE;cACfF,QAAQ,EAAE,KAAK;cACfC,UAAU,EAAE;YACd,CAAE;YACFE,UAAU,EAAE;cACV3D,EAAE,EAAE;gBACF4D,EAAE,EAAE,CAAC;gBACLf,YAAY,EAAE;cAChB;YACF,CAAE;YAAA9B,QAAA,gBAEF3D,OAAA,CAAC9B,QAAQ;cACPiG,OAAO,EAAEA,CAAA,KAAM7C,oBAAoB,CAAC,IAAI,CAAE;cAC1CoF,QAAQ,EAAElG,QAAQ,KAAK,IAAK;cAAAmD,QAAA,EAC7B;YAED;cAAAtB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACXxC,OAAA,CAAC9B,QAAQ;cACPiG,OAAO,EAAEA,CAAA,KAAM7C,oBAAoB,CAAC,IAAI,CAAE;cAC1CoF,QAAQ,EAAElG,QAAQ,KAAK,IAAK;cAAAmD,QAAA,EAC7B;YAED;cAAAtB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP,CAAC,eAGPxC,OAAA,CAAC3B,MAAM;YACLuE,EAAE,EAAE;cACF+D,KAAK,EAAE,EAAE;cACTlD,MAAM,EAAE,EAAE;cACVmD,OAAO,EAAE,uBAAuB;cAChCjB,MAAM,EAAE,iCAAiC;cACzCjB,MAAM,EAAE,SAAS;cACjB,SAAS,EAAE;gBACTN,SAAS,EAAE;cACb,CAAC;cACDC,UAAU,EAAE;YACd,CAAE;YAAAV,QAAA,eAEF3D,OAAA,CAACxB,YAAY;cAAA6D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGZxC,OAAA,CAACF,cAAc;MACbkG,IAAI,EAAEhF,kBAAmB;MACzBkF,OAAO,EAAEA,CAAA,KAAMjF,qBAAqB,CAAC,KAAK;IAAE;MAAAoB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7C,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACI,CAAC;AAEb,CAAC;EAAA,QApVkB7C,WAAW,EACXC,WAAW,EAC+BC,OAAO;AAAA,EAkVnE,CAAC;EAAA,QApViBF,WAAW,EACXC,WAAW,EAC+BC,OAAO;AAAA,EAkVlE;AAACgH,GAAA,GArVG5G,MAAM;AAuVZA,MAAM,CAAC6G,WAAW,GAAG,QAAQ;AAE7B,eAAe7G,MAAM;AAAC,IAAAG,EAAA,EAAAyG,GAAA;AAAAE,YAAA,CAAA3G,EAAA;AAAA2G,YAAA,CAAAF,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}