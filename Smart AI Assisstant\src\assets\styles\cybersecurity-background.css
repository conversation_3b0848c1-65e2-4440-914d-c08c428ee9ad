/* Professional Cybersecurity Background Patterns */

/* High-Quality Digital Shield Pattern */
.cyber-shield-pattern {
  background-image: 
    /* Primary shield pattern */
    radial-gradient(circle at 25% 25%, rgba(102, 126, 234, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(118, 75, 162, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 50% 10%, rgba(240, 147, 251, 0.08) 0%, transparent 40%),
    radial-gradient(circle at 10% 90%, rgba(79, 172, 254, 0.08) 0%, transparent 40%),
    
    /* Circuit board pattern */
    linear-gradient(90deg, transparent 24%, rgba(102, 126, 234, 0.03) 25%, rgba(102, 126, 234, 0.03) 26%, transparent 27%, transparent 74%, rgba(102, 126, 234, 0.03) 75%, rgba(102, 126, 234, 0.03) 76%, transparent 77%),
    linear-gradient(0deg, transparent 24%, rgba(118, 75, 162, 0.03) 25%, rgba(118, 75, 162, 0.03) 26%, transparent 27%, transparent 74%, rgba(118, 75, 162, 0.03) 75%, rgba(118, 75, 162, 0.03) 76%, transparent 77%),
    
    /* Hexagonal security pattern */
    url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cdefs%3E%3Cpattern id='hexagon' x='0' y='0' width='50' height='43.4' patternUnits='userSpaceOnUse'%3E%3Cpath d='M25 0L43.4 12.5v25L25 50L6.6 37.5v-25z' fill='none' stroke='%23667eea' stroke-width='0.5' stroke-opacity='0.1'/%3E%3C/pattern%3E%3C/defs%3E%3Crect width='100' height='100' fill='url(%23hexagon)'/%3E%3C/svg%3E"),
    
    /* Network nodes pattern */
    url("data:image/svg+xml,%3Csvg width='80' height='80' viewBox='0 0 80 80' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23764ba2' fill-opacity='0.05'%3E%3Ccircle cx='40' cy='40' r='2'/%3E%3Cpath d='M40 0v80M0 40h80' stroke='%23764ba2' stroke-opacity='0.02' stroke-width='0.5'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
  
  background-size: 
    400px 400px,
    300px 300px, 
    200px 200px,
    150px 150px,
    100px 100px,
    100px 100px,
    100px 100px,
    80px 80px;
    
  background-position: 
    0% 0%,
    100% 100%,
    50% 0%,
    0% 100%,
    0% 0%,
    100% 0%,
    50% 50%,
    25% 75%;
    
  animation: cyberPatternShift 30s ease-in-out infinite;
}

@keyframes cyberPatternShift {
  0%, 100% { 
    background-position: 
      0% 0%, 100% 100%, 50% 0%, 0% 100%,
      0% 0%, 100% 0%, 50% 50%, 25% 75%;
  }
  25% { 
    background-position: 
      10% 10%, 90% 90%, 60% 10%, 10% 90%,
      10% 10%, 90% 10%, 40% 60%, 35% 65%;
  }
  50% { 
    background-position: 
      20% 20%, 80% 80%, 70% 20%, 20% 80%,
      20% 20%, 80% 20%, 30% 70%, 45% 55%;
  }
  75% { 
    background-position: 
      10% 30%, 90% 70%, 40% 30%, 30% 70%,
      30% 10%, 70% 30%, 60% 40%, 15% 85%;
  }
}

/* Professional Security Network Background */
.security-network-bg {
  background-image:
    /* Main cybersecurity image */
    url("https://images.unsplash.com/photo-1563206767-5b18f218e8de?ixlib=rb-4.0.3&auto=format&fit=crop&w=2400&q=80"),
    
    /* Professional overlay gradients */
    linear-gradient(135deg, rgba(15, 15, 15, 0.75) 0%, rgba(26, 26, 26, 0.80) 50%, rgba(18, 18, 18, 0.85) 100%),
    
    /* Additional security patterns */
    radial-gradient(circle at 30% 20%, rgba(102, 126, 234, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 70% 80%, rgba(118, 75, 162, 0.1) 0%, transparent 50%);
    
  background-size: cover, cover, 400px 400px, 300px 300px;
  background-position: center center, center center, 0% 0%, 100% 100%;
  background-repeat: no-repeat, no-repeat, repeat, repeat;
  background-attachment: fixed, fixed, scroll, scroll;
}

/* Digital Lock Background */
.digital-lock-bg {
  background-image:
    /* Digital security/lock image */
    url("https://images.unsplash.com/photo-1614064641938-3bbee52942c7?ixlib=rb-4.0.3&auto=format&fit=crop&w=2400&q=80"),
    
    /* Professional dark overlay */
    linear-gradient(135deg, rgba(15, 15, 15, 0.70) 0%, rgba(26, 26, 26, 0.75) 50%, rgba(18, 18, 18, 0.80) 100%),
    
    /* Quantum security grid */
    url("data:image/svg+xml,%3Csvg width='120' height='120' viewBox='0 0 120 120' xmlns='http://www.w3.org/2000/svg'%3E%3Cdefs%3E%3Cpattern id='grid' width='40' height='40' patternUnits='userSpaceOnUse'%3E%3Cpath d='M 40 0 L 0 0 0 40' fill='none' stroke='%23667eea' stroke-width='0.5' stroke-opacity='0.1'/%3E%3C/pattern%3E%3C/defs%3E%3Crect width='120' height='120' fill='url(%23grid)'/%3E%3C/svg%3E");
    
  background-size: cover, cover, 120px 120px;
  background-position: center center, center center, 0% 0%;
  background-repeat: no-repeat, no-repeat, repeat;
  background-attachment: fixed, fixed, scroll;
}

/* Cyber Defense Background */
.cyber-defense-bg {
  background-image:
    /* High-quality cybersecurity background */
    url("https://images.unsplash.com/photo-1518709268805-4e9042af2176?ixlib=rb-4.0.3&auto=format&fit=crop&w=2400&q=80"),

    /* Professional transparency overlay - 60% opacity as requested */
    linear-gradient(135deg, rgba(15, 15, 15, 0.60) 0%, rgba(26, 26, 26, 0.60) 100%),

    /* Additional security patterns for depth */
    radial-gradient(circle at 25% 25%, rgba(102, 126, 234, 0.12) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(118, 75, 162, 0.12) 0%, transparent 50%),
    radial-gradient(circle at 50% 10%, rgba(240, 147, 251, 0.08) 0%, transparent 40%),

    /* Professional grid pattern */
    url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cdefs%3E%3Cpattern id='cyber-grid' width='25' height='25' patternUnits='userSpaceOnUse'%3E%3Cpath d='M 25 0 L 0 0 0 25' fill='none' stroke='%23667eea' stroke-width='0.5' stroke-opacity='0.1'/%3E%3Ccircle cx='12.5' cy='12.5' r='1' fill='%23764ba2' fill-opacity='0.05'/%3E%3C/pattern%3E%3C/defs%3E%3Crect width='100' height='100' fill='url(%23cyber-grid)'/%3E%3C/svg%3E");

  background-size:
    cover,                    /* Main image */
    cover,                    /* Overlay gradient */
    400px 400px,             /* Radial gradient 1 */
    350px 350px,             /* Radial gradient 2 */
    300px 300px,             /* Radial gradient 3 */
    100px 100px;             /* Grid pattern */

  background-position:
    center center,           /* Main image - professional center positioning */
    center center,           /* Overlay */
    0% 0%,                   /* Radial 1 */
    100% 100%,               /* Radial 2 */
    50% 0%,                  /* Radial 3 */
    0% 0%;                   /* Grid */

  background-repeat:
    no-repeat,               /* Main image */
    no-repeat,               /* Overlay */
    no-repeat,               /* Radials */
    no-repeat,
    no-repeat,
    repeat;                  /* Grid pattern */

  background-attachment: fixed, fixed, scroll, scroll, scroll, scroll;
}

/* Performance Optimizations for Smooth Scrolling */
* {
  scroll-behavior: smooth;
}

/* Optimized Background for Better Performance */
.cyber-defense-bg-enhanced {
  will-change: transform;
  transform: translateZ(0); /* Force GPU acceleration */
}

/* Responsive Background Adjustments */
@media (max-width: 768px) {
  .cyber-shield-pattern,
  .security-network-bg,
  .digital-lock-bg,
  .cyber-defense-bg,
  .cyber-defense-bg-enhanced {
    background-attachment: scroll !important; /* Better performance on mobile */
    will-change: auto; /* Reduce GPU usage on mobile */
  }

  .cyber-shield-pattern {
    background-size:
      300px 300px, 200px 200px, 150px 150px, 100px 100px,
      80px 80px, 80px 80px, 80px 80px, 60px 60px;
  }
}

/* High Performance Mode */
@media (prefers-reduced-motion: reduce) {
  .cyber-shield-pattern {
    animation: none;
  }
  
  @keyframes cyberPatternShift,
  @keyframes particleFloat {
    animation: none;
  }
}

/* Professional Image Fallbacks */
.bg-fallback-pattern {
  background-image:
    /* Professional fallback pattern if images don't load */
    linear-gradient(135deg, rgba(15, 15, 15, 0.9) 0%, rgba(26, 26, 26, 0.9) 50%, rgba(18, 18, 18, 0.9) 100%),

    /* Digital shield pattern */
    url("data:image/svg+xml,%3Csvg width='200' height='200' viewBox='0 0 200 200' xmlns='http://www.w3.org/2000/svg'%3E%3Cdefs%3E%3Cpattern id='shield-pattern' width='60' height='60' patternUnits='userSpaceOnUse'%3E%3Cpath d='M30 5L50 20v25L30 55L10 45V20z' fill='none' stroke='%23667eea' stroke-width='0.8' stroke-opacity='0.15'/%3E%3Ccircle cx='30' cy='30' r='3' fill='%23764ba2' fill-opacity='0.08'/%3E%3Cpath d='M30 15v30M15 30h30' stroke='%23f093fb' stroke-width='0.5' stroke-opacity='0.1'/%3E%3C/pattern%3E%3C/defs%3E%3Crect width='200' height='200' fill='url(%23shield-pattern)'/%3E%3C/svg%3E"),

    /* Network security nodes */
    radial-gradient(circle at 20% 20%, rgba(102, 126, 234, 0.1) 0%, transparent 30%),
    radial-gradient(circle at 80% 80%, rgba(118, 75, 162, 0.1) 0%, transparent 30%),
    radial-gradient(circle at 50% 50%, rgba(240, 147, 251, 0.06) 0%, transparent 25%);

  background-size: cover, 120px 120px, 300px 300px, 250px 250px, 200px 200px;
  background-position: center center, 0% 0%, 0% 0%, 100% 100%, 50% 50%;
  background-repeat: no-repeat, repeat, no-repeat, no-repeat, no-repeat;
}

/* Enhanced Cyber Defense with Multiple Image Options */
.cyber-defense-bg-enhanced {
  /* Base gradient background for immediate loading */
  background: linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 50%, #121212 100%);

  background-image:
    /* Primary high-quality cybersecurity image */
    url("https://images.unsplash.com/photo-1518709268805-4e9042af2176?ixlib=rb-4.0.3&auto=format&fit=crop&w=2400&q=80"),

    /* Professional transparency overlay - exactly 60% as requested */
    linear-gradient(135deg, rgba(15, 15, 15, 0.60) 0%, rgba(26, 26, 26, 0.60) 100%),

    /* Additional depth layers */
    radial-gradient(ellipse at 30% 20%, rgba(102, 126, 234, 0.15) 0%, transparent 60%),
    radial-gradient(ellipse at 70% 80%, rgba(118, 75, 162, 0.15) 0%, transparent 60%),

    /* Professional pattern fallback */
    url("data:image/svg+xml,%3Csvg width='150' height='150' viewBox='0 0 150 150' xmlns='http://www.w3.org/2000/svg'%3E%3Cdefs%3E%3Cpattern id='cyber-security' width='50' height='50' patternUnits='userSpaceOnUse'%3E%3Cpath d='M25 2L45 15v20L25 48L5 35V15z' fill='none' stroke='%23667eea' stroke-width='1' stroke-opacity='0.12'/%3E%3Ccircle cx='25' cy='25' r='2.5' fill='%23764ba2' fill-opacity='0.08'/%3E%3Cpath d='M25 10v30M10 25h30' stroke='%23f093fb' stroke-width='0.8' stroke-opacity='0.08'/%3E%3C/pattern%3E%3C/defs%3E%3Crect width='150' height='150' fill='url(%23cyber-security)'/%3E%3C/svg%3E");

  background-size:
    cover,                    /* Primary image */
    cover,                    /* Transparency overlay */
    500px 500px,             /* Radial gradient 1 */
    400px 400px,             /* Radial gradient 2 */
    150px 150px;             /* Pattern */

  background-position:
    center center,           /* Primary image - professional center positioning */
    center center,           /* Overlay */
    20% 20%,                 /* Radial 1 */
    80% 80%,                 /* Radial 2 */
    0% 0%;                   /* Pattern */

  background-repeat:
    no-repeat,               /* Primary image */
    no-repeat,               /* Overlay */
    no-repeat,               /* Radials */
    no-repeat,
    repeat;                  /* Pattern */

  background-attachment: fixed, fixed, scroll, scroll, scroll;
}
