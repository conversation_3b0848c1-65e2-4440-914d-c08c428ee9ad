import React, { useState } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Avatar,
  Typography,
  Button,
  TextField,
  IconButton,
  Chip,
  Divider,
  Grid,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  CircularProgress,
  Fade,
  Tooltip,
} from '@mui/material';
import {
  Edit as EditIcon,
  Save as SaveIcon,
  Cancel as CancelIcon,
  PhotoCamera as PhotoCameraIcon,
  Verified as VerifiedIcon,
  Email as EmailIcon,
  Person as PersonIcon,
  CalendarToday as CalendarIcon,
} from '@mui/icons-material';
import { useAuth } from '../contexts/AuthContext';
import authService from '../services/auth';

const UserProfile = () => {
  const { user, updateUser } = useAuth();
  const [isEditing, setIsEditing] = useState(false);
  const [editForm, setEditForm] = useState({
    username: user?.username || '',
    email: user?.email || '',
    fullName: user?.fullName || '',
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  const handleEditToggle = () => {
    if (isEditing) {
      // Cancel editing
      setEditForm({
        username: user?.username || '',
        email: user?.email || '',
        fullName: user?.fullName || '',
      });
      setError('');
    }
    setIsEditing(!isEditing);
  };

  const handleSave = async () => {
    setIsLoading(true);
    setError('');
    setSuccess('');

    try {
      // Validate inputs
      if (!editForm.username.trim()) {
        setError('Username is required');
        return;
      }

      if (!editForm.email.trim()) {
        setError('Email is required');
        return;
      }

      if (!authService.validateEmail(editForm.email)) {
        setError('Please enter a valid email address');
        return;
      }

      // Make API call to update profile
      const response = await fetch(`${process.env.REACT_APP_API_BASE}/auth/profile`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authService.getToken()}`,
        },
        body: JSON.stringify({
          username: editForm.username,
          email: editForm.email,
          fullName: editForm.fullName,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to update profile');
      }

      if (data.success) {
        // Update local user data
        updateUser(data.data.user);
        setSuccess('Profile updated successfully!');
        setIsEditing(false);

        // Clear success message after 3 seconds
        setTimeout(() => setSuccess(''), 3000);
      } else {
        setError(data.error || 'Failed to update profile');
      }
    } catch (error) {
      console.error('Profile update error:', error);
      setError(error.message || 'Failed to update profile. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const getInitials = (name) => {
    if (!name) return user?.username?.charAt(0).toUpperCase() || 'U';
    return name.split(' ').map(n => n.charAt(0)).join('').toUpperCase().slice(0, 2);
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'Unknown';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  if (!user) {
    return (
      <Card sx={{ maxWidth: 600, mx: 'auto' }}>
        <CardContent sx={{ textAlign: 'center', py: 4 }}>
          <Typography variant="h6" color="text.secondary">
            Please log in to view your profile
          </Typography>
        </CardContent>
      </Card>
    );
  }

  return (
    <Box>
      {/* Success/Error Messages */}
      <Fade in={!!success}>
        <Alert severity="success" sx={{ mb: 2 }}>
          {success}
        </Alert>
      </Fade>
      
      <Fade in={!!error}>
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      </Fade>

      <Card 
        sx={{ 
          maxWidth: 600, 
          mx: 'auto',
          background: (theme) => theme.palette.mode === 'dark'
            ? 'linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%)'
            : 'linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%)',
          border: '1px solid rgba(102, 126, 234, 0.2)',
          borderRadius: 4,
          overflow: 'hidden',
        }}
      >
        {/* Header with gradient */}
        <Box
          sx={{
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            height: 120,
            position: 'relative',
          }}
        >
          <Box
            sx={{
              position: 'absolute',
              top: 16,
              right: 16,
            }}
          >
            <Tooltip title={isEditing ? 'Cancel editing' : 'Edit profile'}>
              <IconButton
                onClick={handleEditToggle}
                disabled={isLoading}
                sx={{
                  background: 'rgba(255, 255, 255, 0.2)',
                  color: 'white',
                  '&:hover': {
                    background: 'rgba(255, 255, 255, 0.3)',
                  },
                }}
              >
                {isEditing ? <CancelIcon /> : <EditIcon />}
              </IconButton>
            </Tooltip>
          </Box>
        </Box>

        <CardContent sx={{ pt: 0, pb: 4 }}>
          {/* Avatar Section */}
          <Box sx={{ display: 'flex', justifyContent: 'center', mb: 3, mt: -6 }}>
            <Box sx={{ position: 'relative' }}>
              <Avatar
                sx={{
                  width: 120,
                  height: 120,
                  fontSize: '2.5rem',
                  fontWeight: 'bold',
                  background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
                  border: '4px solid white',
                  boxShadow: '0 8px 25px rgba(0, 0, 0, 0.15)',
                }}
              >
                {getInitials(editForm.fullName || user.fullName)}
              </Avatar>
              
              {isEditing && (
                <IconButton
                  sx={{
                    position: 'absolute',
                    bottom: 0,
                    right: 0,
                    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                    color: 'white',
                    width: 40,
                    height: 40,
                    '&:hover': {
                      background: 'linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)',
                    },
                  }}
                >
                  <PhotoCameraIcon fontSize="small" />
                </IconButton>
              )}
            </Box>
          </Box>

          {/* User Information */}
          <Box sx={{ textAlign: 'center', mb: 4 }}>
            {isEditing ? (
              <Box sx={{ maxWidth: 400, mx: 'auto' }}>
                <TextField
                  fullWidth
                  label="Full Name"
                  value={editForm.fullName}
                  onChange={(e) => setEditForm({ ...editForm, fullName: e.target.value })}
                  margin="normal"
                  variant="outlined"
                  sx={{ mb: 2 }}
                />
                <TextField
                  fullWidth
                  label="Username"
                  value={editForm.username}
                  onChange={(e) => setEditForm({ ...editForm, username: e.target.value })}
                  margin="normal"
                  variant="outlined"
                  sx={{ mb: 2 }}
                />
                <TextField
                  fullWidth
                  label="Email"
                  type="email"
                  value={editForm.email}
                  onChange={(e) => setEditForm({ ...editForm, email: e.target.value })}
                  margin="normal"
                  variant="outlined"
                  sx={{ mb: 3 }}
                />
                
                <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center' }}>
                  <Button
                    variant="contained"
                    startIcon={isLoading ? <CircularProgress size={20} /> : <SaveIcon />}
                    onClick={handleSave}
                    disabled={isLoading}
                    sx={{
                      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                      '&:hover': {
                        background: 'linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)',
                      },
                    }}
                  >
                    {isLoading ? 'Saving...' : 'Save Changes'}
                  </Button>
                  <Button
                    variant="outlined"
                    onClick={handleEditToggle}
                    disabled={isLoading}
                  >
                    Cancel
                  </Button>
                </Box>
              </Box>
            ) : (
              <>
                <Typography variant="h4" fontWeight="bold" gutterBottom>
                  {user.fullName || user.username}
                </Typography>
                
                <Typography variant="h6" color="text.secondary" gutterBottom>
                  @{user.username}
                </Typography>

                <Box sx={{ display: 'flex', justifyContent: 'center', gap: 1, mb: 3 }}>
                  <Chip
                    icon={<EmailIcon />}
                    label={user.emailVerified ? 'Verified' : 'Unverified'}
                    color={user.emailVerified ? 'success' : 'warning'}
                    size="small"
                  />
                </Box>

                <Divider sx={{ my: 3 }} />

                {/* Profile Details */}
                <Grid container spacing={3} sx={{ textAlign: 'left' }}>
                  <Grid item xs={12} sm={6}>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                      <EmailIcon sx={{ mr: 2, color: 'text.secondary' }} />
                      <Box>
                        <Typography variant="body2" color="text.secondary">
                          Email
                        </Typography>
                        <Typography variant="body1" fontWeight="500">
                          {user.email}
                        </Typography>
                      </Box>
                    </Box>
                  </Grid>
                  
                  <Grid item xs={12} sm={6}>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                      <PersonIcon sx={{ mr: 2, color: 'text.secondary' }} />
                      <Box>
                        <Typography variant="body2" color="text.secondary">
                          Username
                        </Typography>
                        <Typography variant="body1" fontWeight="500">
                          {user.username}
                        </Typography>
                      </Box>
                    </Box>
                  </Grid>
                  
                  <Grid item xs={12} sm={6}>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                      <CalendarIcon sx={{ mr: 2, color: 'text.secondary' }} />
                      <Box>
                        <Typography variant="body2" color="text.secondary">
                          Member Since
                        </Typography>
                        <Typography variant="body1" fontWeight="500">
                          {formatDate(user.createdAt)}
                        </Typography>
                      </Box>
                    </Box>
                  </Grid>
                </Grid>
              </>
            )}
          </Box>
        </CardContent>
      </Card>
    </Box>
  );
};

export default UserProfile;
