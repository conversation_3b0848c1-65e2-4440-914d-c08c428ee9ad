{"ast": null, "code": "var _jsxFileName = \"E:\\\\PythonProjects\\\\React.js\\\\Smart AI Assisstant\\\\src\\\\components\\\\SmartDashboard.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Grid, Card, CardContent, Typography, LinearProgress, Chip, Avatar, List, ListItem, ListItemIcon, ListItemText, IconButton, Tooltip, Badge, Divider } from '@mui/material';\nimport { Security as SecurityIcon, TrendingUp as TrendingUpIcon, Warning as WarningIcon, Shield as ShieldIcon, Speed as SpeedIcon, Notifications as NotificationsIcon, Timeline as TimelineIcon, Psychology as AIIcon, AutoFixHigh as AutoFixIcon, Insights as InsightsIcon } from '@mui/icons-material';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SmartDashboard = /*#__PURE__*/_s(/*#__PURE__*/React.memo(_c = _s(() => {\n  _s();\n  const [realTimeData, setRealTimeData] = useState({\n    threatLevel: 15,\n    scansToday: 247,\n    threatsBlocked: 12,\n    systemHealth: 98,\n    aiConfidence: 94,\n    activeScans: 3\n  });\n  const [smartInsights, setSmartInsights] = useState([{\n    type: 'warning',\n    title: 'Unusual Activity Detected',\n    description: 'Increased malware attempts from Eastern Europe region',\n    confidence: 87,\n    timestamp: new Date(Date.now() - 1000 * 60 * 15)\n  }, {\n    type: 'success',\n    title: 'AI Model Updated',\n    description: 'Threat detection accuracy improved by 12%',\n    confidence: 95,\n    timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2)\n  }, {\n    type: 'info',\n    title: 'Security Trend Analysis',\n    description: 'Phishing attempts decreased by 23% this week',\n    confidence: 91,\n    timestamp: new Date(Date.now() - 1000 * 60 * 60 * 4)\n  }]);\n  const [threatMap, setThreatMap] = useState([{\n    region: 'North America',\n    threats: 45,\n    trend: 'down'\n  }, {\n    region: 'Europe',\n    threats: 67,\n    trend: 'up'\n  }, {\n    region: 'Asia Pacific',\n    threats: 89,\n    trend: 'stable'\n  }, {\n    region: 'South America',\n    threats: 23,\n    trend: 'down'\n  }]);\n\n  // Simulate real-time updates\n  useEffect(() => {\n    const interval = setInterval(() => {\n      setRealTimeData(prev => ({\n        ...prev,\n        scansToday: prev.scansToday + Math.floor(Math.random() * 3),\n        threatLevel: Math.max(5, Math.min(100, prev.threatLevel + (Math.random() - 0.5) * 10)),\n        systemHealth: Math.max(85, Math.min(100, prev.systemHealth + (Math.random() - 0.5) * 2)),\n        aiConfidence: Math.max(80, Math.min(100, prev.aiConfidence + (Math.random() - 0.5) * 3))\n      }));\n    }, 5000);\n    return () => clearInterval(interval);\n  }, []);\n  const getThreatLevelColor = level => {\n    if (level < 30) return 'success';\n    if (level < 70) return 'warning';\n    return 'error';\n  };\n  const getInsightIcon = type => {\n    switch (type) {\n      case 'warning':\n        return /*#__PURE__*/_jsxDEV(WarningIcon, {\n          color: \"warning\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 97,\n          columnNumber: 30\n        }, this);\n      case 'success':\n        return /*#__PURE__*/_jsxDEV(ShieldIcon, {\n          color: \"success\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 98,\n          columnNumber: 30\n        }, this);\n      case 'info':\n        return /*#__PURE__*/_jsxDEV(InsightsIcon, {\n          color: \"info\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 99,\n          columnNumber: 27\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(SecurityIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 100,\n          columnNumber: 23\n        }, this);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      p: 3\n    },\n    children: [/*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      mb: 4,\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          sx: {\n            background: theme => theme.palette.mode === 'dark' ? 'linear-gradient(135deg, rgba(12, 12, 12, 0.95) 0%, rgba(26, 26, 26, 0.95) 50%, rgba(45, 45, 45, 0.95) 100%)' : 'linear-gradient(135deg, rgba(248, 250, 252, 0.95) 0%, rgba(241, 245, 249, 0.95) 50%, rgba(226, 232, 240, 0.95) 100%)',\n            backdropFilter: 'blur(25px) saturate(180%)',\n            border: '1px solid rgba(102, 126, 234, 0.2)',\n            borderRadius: 3,\n            position: 'relative',\n            overflow: 'hidden',\n            boxShadow: '0 8px 32px rgba(102, 126, 234, 0.15)',\n            transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',\n            '&:hover': {\n              transform: 'translateY(-4px) scale(1.02)',\n              boxShadow: '0 16px 48px rgba(102, 126, 234, 0.25)'\n            },\n            '&::before': {\n              content: '\"\"',\n              position: 'absolute',\n              top: 0,\n              left: 0,\n              right: 0,\n              height: '4px',\n              background: 'linear-gradient(90deg, #667eea 0%, #764ba2 100%)'\n            }\n          },\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              display: \"flex\",\n              alignItems: \"center\",\n              justifyContent: \"space-between\",\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h3\",\n                  fontWeight: \"bold\",\n                  color: \"primary\",\n                  children: realTimeData.scansToday\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 139,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: \"Scans Today\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 142,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 138,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Avatar, {\n                sx: {\n                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                  width: 56,\n                  height: 56\n                },\n                children: /*#__PURE__*/_jsxDEV(SpeedIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 153,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 146,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 137,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 136,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 109,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 108,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          sx: {\n            background: theme => theme.palette.mode === 'dark' ? 'linear-gradient(135deg, rgba(26, 26, 46, 0.95) 0%, rgba(22, 33, 62, 0.95) 50%, rgba(15, 52, 96, 0.95) 100%)' : 'linear-gradient(135deg, rgba(248, 250, 252, 0.95) 0%, rgba(241, 245, 249, 0.95) 50%, rgba(226, 232, 240, 0.95) 100%)',\n            backdropFilter: 'blur(25px) saturate(180%)',\n            border: '1px solid rgba(233, 69, 96, 0.2)',\n            borderRadius: 3,\n            position: 'relative',\n            overflow: 'hidden',\n            boxShadow: '0 8px 32px rgba(233, 69, 96, 0.15)',\n            transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',\n            '&:hover': {\n              transform: 'translateY(-4px) scale(1.02)',\n              boxShadow: '0 16px 48px rgba(233, 69, 96, 0.25)'\n            },\n            '&::before': {\n              content: '\"\"',\n              position: 'absolute',\n              top: 0,\n              left: 0,\n              right: 0,\n              height: '4px',\n              background: 'linear-gradient(90deg, #e94560 0%, #f27121 100%)'\n            }\n          },\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              display: \"flex\",\n              alignItems: \"center\",\n              justifyContent: \"space-between\",\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h3\",\n                  fontWeight: \"bold\",\n                  color: \"error\",\n                  children: realTimeData.threatsBlocked\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 191,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: \"Threats Blocked\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 194,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 190,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Avatar, {\n                sx: {\n                  background: 'linear-gradient(135deg, #f44336 0%, #ff5722 100%)',\n                  width: 56,\n                  height: 56\n                },\n                children: /*#__PURE__*/_jsxDEV(ShieldIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 205,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 198,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 189,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 188,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 161,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 160,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          sx: {\n            background: 'linear-gradient(135deg, rgba(76, 175, 80, 0.1) 0%, rgba(139, 195, 74, 0.1) 100%)',\n            border: '1px solid rgba(76, 175, 80, 0.2)',\n            position: 'relative',\n            overflow: 'hidden',\n            '&::before': {\n              content: '\"\"',\n              position: 'absolute',\n              top: 0,\n              left: 0,\n              right: 0,\n              height: '3px',\n              background: 'linear-gradient(90deg, #4caf50 0%, #8bc34a 100%)'\n            }\n          },\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              display: \"flex\",\n              alignItems: \"center\",\n              justifyContent: \"space-between\",\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h3\",\n                  fontWeight: \"bold\",\n                  color: \"success\",\n                  children: [realTimeData.systemHealth, \"%\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 233,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: \"System Health\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 236,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 232,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Avatar, {\n                sx: {\n                  background: 'linear-gradient(135deg, #4caf50 0%, #8bc34a 100%)',\n                  width: 56,\n                  height: 56\n                },\n                children: /*#__PURE__*/_jsxDEV(TrendingUpIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 247,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 240,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 231,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(LinearProgress, {\n              variant: \"determinate\",\n              value: realTimeData.systemHealth,\n              sx: {\n                mt: 1,\n                height: 6,\n                borderRadius: 3\n              },\n              color: \"success\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 250,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 230,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 213,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 212,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          sx: {\n            background: 'linear-gradient(135deg, rgba(156, 39, 176, 0.1) 0%, rgba(233, 30, 99, 0.1) 100%)',\n            border: '1px solid rgba(156, 39, 176, 0.2)',\n            position: 'relative',\n            overflow: 'hidden',\n            '&::before': {\n              content: '\"\"',\n              position: 'absolute',\n              top: 0,\n              left: 0,\n              right: 0,\n              height: '3px',\n              background: 'linear-gradient(90deg, #9c27b0 0%, #e91e63 100%)'\n            }\n          },\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              display: \"flex\",\n              alignItems: \"center\",\n              justifyContent: \"space-between\",\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h3\",\n                  fontWeight: \"bold\",\n                  color: \"secondary\",\n                  children: [realTimeData.aiConfidence, \"%\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 281,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: \"AI Confidence\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 284,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 280,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Avatar, {\n                sx: {\n                  background: 'linear-gradient(135deg, #9c27b0 0%, #e91e63 100%)',\n                  width: 56,\n                  height: 56\n                },\n                children: /*#__PURE__*/_jsxDEV(AIIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 295,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 288,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 279,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(LinearProgress, {\n              variant: \"determinate\",\n              value: realTimeData.aiConfidence,\n              sx: {\n                mt: 1,\n                height: 6,\n                borderRadius: 3\n              },\n              color: \"secondary\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 298,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 278,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 261,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 260,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 107,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 8,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          sx: {\n            background: theme => theme.palette.mode === 'dark' ? 'linear-gradient(135deg, rgba(30, 30, 30, 0.95) 0%, rgba(50, 50, 50, 0.95) 100%)' : 'linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%)',\n            backdropFilter: 'blur(20px)',\n            border: theme => `1px solid ${theme.palette.divider}`,\n            height: '100%'\n          },\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              display: \"flex\",\n              alignItems: \"center\",\n              gap: 2,\n              mb: 3,\n              children: [/*#__PURE__*/_jsxDEV(Avatar, {\n                sx: {\n                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                  width: 40,\n                  height: 40\n                },\n                children: /*#__PURE__*/_jsxDEV(InsightsIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 331,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 324,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h5\",\n                fontWeight: \"bold\",\n                children: \"Smart Security Insights\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 333,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Badge, {\n                badgeContent: smartInsights.length,\n                color: \"primary\",\n                children: /*#__PURE__*/_jsxDEV(NotificationsIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 337,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 336,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 323,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(List, {\n              children: smartInsights.map((insight, index) => /*#__PURE__*/_jsxDEV(React.Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(ListItem, {\n                  sx: {\n                    borderRadius: 2,\n                    mb: 1,\n                    background: theme => theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.02)',\n                    '&:hover': {\n                      background: theme => theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.08)' : 'rgba(0, 0, 0, 0.04)'\n                    }\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                    children: getInsightIcon(insight.type)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 358,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                    primary: /*#__PURE__*/_jsxDEV(Box, {\n                      display: \"flex\",\n                      alignItems: \"center\",\n                      gap: 2,\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"subtitle1\",\n                        fontWeight: \"600\",\n                        children: insight.title\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 364,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                        label: `${insight.confidence}% confidence`,\n                        size: \"small\",\n                        color: insight.confidence > 90 ? 'success' : 'warning',\n                        variant: \"outlined\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 367,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 363,\n                      columnNumber: 27\n                    }, this),\n                    secondary: /*#__PURE__*/_jsxDEV(Box, {\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"body2\",\n                        color: \"text.secondary\",\n                        sx: {\n                          mb: 1\n                        },\n                        children: insight.description\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 377,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        color: \"text.secondary\",\n                        children: insight.timestamp.toLocaleString()\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 380,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 376,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 361,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                    title: \"Auto-resolve\",\n                    children: /*#__PURE__*/_jsxDEV(IconButton, {\n                      size: \"small\",\n                      children: /*#__PURE__*/_jsxDEV(AutoFixIcon, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 388,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 387,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 386,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 344,\n                  columnNumber: 21\n                }, this), index < smartInsights.length - 1 && /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 392,\n                  columnNumber: 58\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 343,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 341,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 322,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 312,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 311,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 4,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          sx: {\n            background: theme => theme.palette.mode === 'dark' ? 'linear-gradient(135deg, rgba(30, 30, 30, 0.95) 0%, rgba(50, 50, 50, 0.95) 100%)' : 'linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%)',\n            backdropFilter: 'blur(20px)',\n            border: theme => `1px solid ${theme.palette.divider}`,\n            height: '100%'\n          },\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              display: \"flex\",\n              alignItems: \"center\",\n              gap: 2,\n              mb: 3,\n              children: [/*#__PURE__*/_jsxDEV(Avatar, {\n                sx: {\n                  background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',\n                  width: 40,\n                  height: 40\n                },\n                children: /*#__PURE__*/_jsxDEV(TimelineIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 420,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 413,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                fontWeight: \"bold\",\n                children: \"Global Threat Map\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 422,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 412,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(List, {\n              dense: true,\n              children: threatMap.map((region, index) => /*#__PURE__*/_jsxDEV(ListItem, {\n                sx: {\n                  px: 0\n                },\n                children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: /*#__PURE__*/_jsxDEV(Box, {\n                    display: \"flex\",\n                    alignItems: \"center\",\n                    justifyContent: \"space-between\",\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      fontWeight: \"600\",\n                      children: region.region\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 433,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Box, {\n                      display: \"flex\",\n                      alignItems: \"center\",\n                      gap: 1,\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"body2\",\n                        color: \"text.secondary\",\n                        children: region.threats\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 437,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                        label: region.trend,\n                        size: \"small\",\n                        color: region.trend === 'up' ? 'error' : region.trend === 'down' ? 'success' : 'default',\n                        variant: \"outlined\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 440,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 436,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 432,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 430,\n                  columnNumber: 21\n                }, this)\n              }, index, false, {\n                fileName: _jsxFileName,\n                lineNumber: 429,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 427,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 411,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 401,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 400,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 310,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 105,\n    columnNumber: 5\n  }, this);\n}, \"ZP1fjcwMW3PjPomMalqrrp6Az0k=\")), \"ZP1fjcwMW3PjPomMalqrrp6Az0k=\");\n_c2 = SmartDashboard;\nSmartDashboard.displayName = 'SmartDashboard';\nexport default SmartDashboard;\nvar _c, _c2;\n$RefreshReg$(_c, \"SmartDashboard$React.memo\");\n$RefreshReg$(_c2, \"SmartDashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Typography", "LinearProgress", "Chip", "Avatar", "List", "ListItem", "ListItemIcon", "ListItemText", "IconButton", "<PERSON><PERSON><PERSON>", "Badge", "Divider", "Security", "SecurityIcon", "TrendingUp", "TrendingUpIcon", "Warning", "WarningIcon", "Shield", "ShieldIcon", "Speed", "SpeedIcon", "Notifications", "NotificationsIcon", "Timeline", "TimelineIcon", "Psychology", "AIIcon", "AutoFixHigh", "AutoFixIcon", "Insights", "InsightsIcon", "jsxDEV", "_jsxDEV", "SmartDashboard", "_s", "memo", "_c", "realTimeData", "setRealTimeData", "threatLevel", "scansToday", "threatsBlocked", "systemHealth", "aiConfidence", "activeScans", "smartInsights", "setSmartInsights", "type", "title", "description", "confidence", "timestamp", "Date", "now", "threatMap", "setThreatMap", "region", "threats", "trend", "interval", "setInterval", "prev", "Math", "floor", "random", "max", "min", "clearInterval", "getThreatLevelColor", "level", "getInsightIcon", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "sx", "p", "children", "container", "spacing", "mb", "item", "xs", "sm", "md", "background", "theme", "palette", "mode", "<PERSON><PERSON>ilter", "border", "borderRadius", "position", "overflow", "boxShadow", "transition", "transform", "content", "top", "left", "right", "height", "display", "alignItems", "justifyContent", "variant", "fontWeight", "width", "value", "mt", "divider", "gap", "badgeContent", "length", "map", "insight", "index", "Fragment", "primary", "label", "size", "secondary", "toLocaleString", "dense", "px", "_c2", "displayName", "$RefreshReg$"], "sources": ["E:/PythonProjects/React.js/Smart AI Assisstant/src/components/SmartDashboard.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Grid,\n  Card,\n  CardContent,\n  Typography,\n  LinearProgress,\n  Chip,\n  Avatar,\n  List,\n  ListItem,\n  ListItemIcon,\n  ListItemText,\n  IconButton,\n  Tooltip,\n  Badge,\n  Divider,\n} from '@mui/material';\nimport {\n  Security as SecurityIcon,\n  TrendingUp as TrendingUpIcon,\n  Warning as WarningIcon,\n  Shield as ShieldIcon,\n  Speed as SpeedIcon,\n  Notifications as NotificationsIcon,\n  Timeline as TimelineIcon,\n  Psychology as AIIcon,\n  AutoFixHigh as AutoFixIcon,\n  Insights as InsightsIcon,\n} from '@mui/icons-material';\n\nconst SmartDashboard = React.memo(() => {\n  const [realTimeData, setRealTimeData] = useState({\n    threatLevel: 15,\n    scansToday: 247,\n    threatsBlocked: 12,\n    systemHealth: 98,\n    aiConfidence: 94,\n    activeScans: 3,\n  });\n\n  const [smartInsights, setSmartInsights] = useState([\n    {\n      type: 'warning',\n      title: 'Unusual Activity Detected',\n      description: 'Increased malware attempts from Eastern Europe region',\n      confidence: 87,\n      timestamp: new Date(Date.now() - 1000 * 60 * 15),\n    },\n    {\n      type: 'success',\n      title: 'AI Model Updated',\n      description: 'Threat detection accuracy improved by 12%',\n      confidence: 95,\n      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2),\n    },\n    {\n      type: 'info',\n      title: 'Security Trend Analysis',\n      description: 'Phishing attempts decreased by 23% this week',\n      confidence: 91,\n      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 4),\n    },\n  ]);\n\n  const [threatMap, setThreatMap] = useState([\n    { region: 'North America', threats: 45, trend: 'down' },\n    { region: 'Europe', threats: 67, trend: 'up' },\n    { region: 'Asia Pacific', threats: 89, trend: 'stable' },\n    { region: 'South America', threats: 23, trend: 'down' },\n  ]);\n\n  // Simulate real-time updates\n  useEffect(() => {\n    const interval = setInterval(() => {\n      setRealTimeData(prev => ({\n        ...prev,\n        scansToday: prev.scansToday + Math.floor(Math.random() * 3),\n        threatLevel: Math.max(5, Math.min(100, prev.threatLevel + (Math.random() - 0.5) * 10)),\n        systemHealth: Math.max(85, Math.min(100, prev.systemHealth + (Math.random() - 0.5) * 2)),\n        aiConfidence: Math.max(80, Math.min(100, prev.aiConfidence + (Math.random() - 0.5) * 3)),\n      }));\n    }, 5000);\n\n    return () => clearInterval(interval);\n  }, []);\n\n  const getThreatLevelColor = (level) => {\n    if (level < 30) return 'success';\n    if (level < 70) return 'warning';\n    return 'error';\n  };\n\n  const getInsightIcon = (type) => {\n    switch (type) {\n      case 'warning': return <WarningIcon color=\"warning\" />;\n      case 'success': return <ShieldIcon color=\"success\" />;\n      case 'info': return <InsightsIcon color=\"info\" />;\n      default: return <SecurityIcon />;\n    }\n  };\n\n  return (\n    <Box sx={{ p: 3 }}>\n      {/* Real-time Metrics */}\n      <Grid container spacing={3} mb={4}>\n        <Grid item xs={12} sm={6} md={3}>\n          <Card\n            sx={{\n              background: (theme) => theme.palette.mode === 'dark'\n                ? 'linear-gradient(135deg, rgba(12, 12, 12, 0.95) 0%, rgba(26, 26, 26, 0.95) 50%, rgba(45, 45, 45, 0.95) 100%)'\n                : 'linear-gradient(135deg, rgba(248, 250, 252, 0.95) 0%, rgba(241, 245, 249, 0.95) 50%, rgba(226, 232, 240, 0.95) 100%)',\n              backdropFilter: 'blur(25px) saturate(180%)',\n              border: '1px solid rgba(102, 126, 234, 0.2)',\n              borderRadius: 3,\n              position: 'relative',\n              overflow: 'hidden',\n              boxShadow: '0 8px 32px rgba(102, 126, 234, 0.15)',\n              transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',\n              '&:hover': {\n                transform: 'translateY(-4px) scale(1.02)',\n                boxShadow: '0 16px 48px rgba(102, 126, 234, 0.25)',\n              },\n              '&::before': {\n                content: '\"\"',\n                position: 'absolute',\n                top: 0,\n                left: 0,\n                right: 0,\n                height: '4px',\n                background: 'linear-gradient(90deg, #667eea 0%, #764ba2 100%)',\n              },\n            }}\n          >\n            <CardContent>\n              <Box display=\"flex\" alignItems=\"center\" justifyContent=\"space-between\">\n                <Box>\n                  <Typography variant=\"h3\" fontWeight=\"bold\" color=\"primary\">\n                    {realTimeData.scansToday}\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    Scans Today\n                  </Typography>\n                </Box>\n                <Avatar\n                  sx={{\n                    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                    width: 56,\n                    height: 56,\n                  }}\n                >\n                  <SpeedIcon />\n                </Avatar>\n              </Box>\n            </CardContent>\n          </Card>\n        </Grid>\n\n        <Grid item xs={12} sm={6} md={3}>\n          <Card\n            sx={{\n              background: (theme) => theme.palette.mode === 'dark'\n                ? 'linear-gradient(135deg, rgba(26, 26, 46, 0.95) 0%, rgba(22, 33, 62, 0.95) 50%, rgba(15, 52, 96, 0.95) 100%)'\n                : 'linear-gradient(135deg, rgba(248, 250, 252, 0.95) 0%, rgba(241, 245, 249, 0.95) 50%, rgba(226, 232, 240, 0.95) 100%)',\n              backdropFilter: 'blur(25px) saturate(180%)',\n              border: '1px solid rgba(233, 69, 96, 0.2)',\n              borderRadius: 3,\n              position: 'relative',\n              overflow: 'hidden',\n              boxShadow: '0 8px 32px rgba(233, 69, 96, 0.15)',\n              transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',\n              '&:hover': {\n                transform: 'translateY(-4px) scale(1.02)',\n                boxShadow: '0 16px 48px rgba(233, 69, 96, 0.25)',\n              },\n              '&::before': {\n                content: '\"\"',\n                position: 'absolute',\n                top: 0,\n                left: 0,\n                right: 0,\n                height: '4px',\n                background: 'linear-gradient(90deg, #e94560 0%, #f27121 100%)',\n              },\n            }}\n          >\n            <CardContent>\n              <Box display=\"flex\" alignItems=\"center\" justifyContent=\"space-between\">\n                <Box>\n                  <Typography variant=\"h3\" fontWeight=\"bold\" color=\"error\">\n                    {realTimeData.threatsBlocked}\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    Threats Blocked\n                  </Typography>\n                </Box>\n                <Avatar\n                  sx={{\n                    background: 'linear-gradient(135deg, #f44336 0%, #ff5722 100%)',\n                    width: 56,\n                    height: 56,\n                  }}\n                >\n                  <ShieldIcon />\n                </Avatar>\n              </Box>\n            </CardContent>\n          </Card>\n        </Grid>\n\n        <Grid item xs={12} sm={6} md={3}>\n          <Card\n            sx={{\n              background: 'linear-gradient(135deg, rgba(76, 175, 80, 0.1) 0%, rgba(139, 195, 74, 0.1) 100%)',\n              border: '1px solid rgba(76, 175, 80, 0.2)',\n              position: 'relative',\n              overflow: 'hidden',\n              '&::before': {\n                content: '\"\"',\n                position: 'absolute',\n                top: 0,\n                left: 0,\n                right: 0,\n                height: '3px',\n                background: 'linear-gradient(90deg, #4caf50 0%, #8bc34a 100%)',\n              },\n            }}\n          >\n            <CardContent>\n              <Box display=\"flex\" alignItems=\"center\" justifyContent=\"space-between\">\n                <Box>\n                  <Typography variant=\"h3\" fontWeight=\"bold\" color=\"success\">\n                    {realTimeData.systemHealth}%\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    System Health\n                  </Typography>\n                </Box>\n                <Avatar\n                  sx={{\n                    background: 'linear-gradient(135deg, #4caf50 0%, #8bc34a 100%)',\n                    width: 56,\n                    height: 56,\n                  }}\n                >\n                  <TrendingUpIcon />\n                </Avatar>\n              </Box>\n              <LinearProgress\n                variant=\"determinate\"\n                value={realTimeData.systemHealth}\n                sx={{ mt: 1, height: 6, borderRadius: 3 }}\n                color=\"success\"\n              />\n            </CardContent>\n          </Card>\n        </Grid>\n\n        <Grid item xs={12} sm={6} md={3}>\n          <Card\n            sx={{\n              background: 'linear-gradient(135deg, rgba(156, 39, 176, 0.1) 0%, rgba(233, 30, 99, 0.1) 100%)',\n              border: '1px solid rgba(156, 39, 176, 0.2)',\n              position: 'relative',\n              overflow: 'hidden',\n              '&::before': {\n                content: '\"\"',\n                position: 'absolute',\n                top: 0,\n                left: 0,\n                right: 0,\n                height: '3px',\n                background: 'linear-gradient(90deg, #9c27b0 0%, #e91e63 100%)',\n              },\n            }}\n          >\n            <CardContent>\n              <Box display=\"flex\" alignItems=\"center\" justifyContent=\"space-between\">\n                <Box>\n                  <Typography variant=\"h3\" fontWeight=\"bold\" color=\"secondary\">\n                    {realTimeData.aiConfidence}%\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    AI Confidence\n                  </Typography>\n                </Box>\n                <Avatar\n                  sx={{\n                    background: 'linear-gradient(135deg, #9c27b0 0%, #e91e63 100%)',\n                    width: 56,\n                    height: 56,\n                  }}\n                >\n                  <AIIcon />\n                </Avatar>\n              </Box>\n              <LinearProgress\n                variant=\"determinate\"\n                value={realTimeData.aiConfidence}\n                sx={{ mt: 1, height: 6, borderRadius: 3 }}\n                color=\"secondary\"\n              />\n            </CardContent>\n          </Card>\n        </Grid>\n      </Grid>\n\n      {/* Smart Insights and Threat Map */}\n      <Grid container spacing={3}>\n        <Grid item xs={12} md={8}>\n          <Card\n            sx={{\n              background: (theme) => theme.palette.mode === 'dark'\n                ? 'linear-gradient(135deg, rgba(30, 30, 30, 0.95) 0%, rgba(50, 50, 50, 0.95) 100%)'\n                : 'linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%)',\n              backdropFilter: 'blur(20px)',\n              border: (theme) => `1px solid ${theme.palette.divider}`,\n              height: '100%',\n            }}\n          >\n            <CardContent>\n              <Box display=\"flex\" alignItems=\"center\" gap={2} mb={3}>\n                <Avatar\n                  sx={{\n                    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                    width: 40,\n                    height: 40,\n                  }}\n                >\n                  <InsightsIcon />\n                </Avatar>\n                <Typography variant=\"h5\" fontWeight=\"bold\">\n                  Smart Security Insights\n                </Typography>\n                <Badge badgeContent={smartInsights.length} color=\"primary\">\n                  <NotificationsIcon />\n                </Badge>\n              </Box>\n\n              <List>\n                {smartInsights.map((insight, index) => (\n                  <React.Fragment key={index}>\n                    <ListItem\n                      sx={{\n                        borderRadius: 2,\n                        mb: 1,\n                        background: (theme) => theme.palette.mode === 'dark'\n                          ? 'rgba(255, 255, 255, 0.05)'\n                          : 'rgba(0, 0, 0, 0.02)',\n                        '&:hover': {\n                          background: (theme) => theme.palette.mode === 'dark'\n                            ? 'rgba(255, 255, 255, 0.08)'\n                            : 'rgba(0, 0, 0, 0.04)',\n                        },\n                      }}\n                    >\n                      <ListItemIcon>\n                        {getInsightIcon(insight.type)}\n                      </ListItemIcon>\n                      <ListItemText\n                        primary={\n                          <Box display=\"flex\" alignItems=\"center\" gap={2}>\n                            <Typography variant=\"subtitle1\" fontWeight=\"600\">\n                              {insight.title}\n                            </Typography>\n                            <Chip\n                              label={`${insight.confidence}% confidence`}\n                              size=\"small\"\n                              color={insight.confidence > 90 ? 'success' : 'warning'}\n                              variant=\"outlined\"\n                            />\n                          </Box>\n                        }\n                        secondary={\n                          <Box>\n                            <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 1 }}>\n                              {insight.description}\n                            </Typography>\n                            <Typography variant=\"caption\" color=\"text.secondary\">\n                              {insight.timestamp.toLocaleString()}\n                            </Typography>\n                          </Box>\n                        }\n                      />\n                      <Tooltip title=\"Auto-resolve\">\n                        <IconButton size=\"small\">\n                          <AutoFixIcon />\n                        </IconButton>\n                      </Tooltip>\n                    </ListItem>\n                    {index < smartInsights.length - 1 && <Divider />}\n                  </React.Fragment>\n                ))}\n              </List>\n            </CardContent>\n          </Card>\n        </Grid>\n\n        <Grid item xs={12} md={4}>\n          <Card\n            sx={{\n              background: (theme) => theme.palette.mode === 'dark'\n                ? 'linear-gradient(135deg, rgba(30, 30, 30, 0.95) 0%, rgba(50, 50, 50, 0.95) 100%)'\n                : 'linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%)',\n              backdropFilter: 'blur(20px)',\n              border: (theme) => `1px solid ${theme.palette.divider}`,\n              height: '100%',\n            }}\n          >\n            <CardContent>\n              <Box display=\"flex\" alignItems=\"center\" gap={2} mb={3}>\n                <Avatar\n                  sx={{\n                    background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',\n                    width: 40,\n                    height: 40,\n                  }}\n                >\n                  <TimelineIcon />\n                </Avatar>\n                <Typography variant=\"h6\" fontWeight=\"bold\">\n                  Global Threat Map\n                </Typography>\n              </Box>\n\n              <List dense>\n                {threatMap.map((region, index) => (\n                  <ListItem key={index} sx={{ px: 0 }}>\n                    <ListItemText\n                      primary={\n                        <Box display=\"flex\" alignItems=\"center\" justifyContent=\"space-between\">\n                          <Typography variant=\"body2\" fontWeight=\"600\">\n                            {region.region}\n                          </Typography>\n                          <Box display=\"flex\" alignItems=\"center\" gap={1}>\n                            <Typography variant=\"body2\" color=\"text.secondary\">\n                              {region.threats}\n                            </Typography>\n                            <Chip\n                              label={region.trend}\n                              size=\"small\"\n                              color={\n                                region.trend === 'up' ? 'error' :\n                                region.trend === 'down' ? 'success' : 'default'\n                              }\n                              variant=\"outlined\"\n                            />\n                          </Box>\n                        </Box>\n                      }\n                    />\n                  </ListItem>\n                ))}\n              </List>\n            </CardContent>\n          </Card>\n        </Grid>\n      </Grid>\n    </Box>\n  );\n});\n\nSmartDashboard.displayName = 'SmartDashboard';\n\nexport default SmartDashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,cAAc,EACdC,IAAI,EACJC,MAAM,EACNC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,YAAY,EACZC,UAAU,EACVC,OAAO,EACPC,KAAK,EACLC,OAAO,QACF,eAAe;AACtB,SACEC,QAAQ,IAAIC,YAAY,EACxBC,UAAU,IAAIC,cAAc,EAC5BC,OAAO,IAAIC,WAAW,EACtBC,MAAM,IAAIC,UAAU,EACpBC,KAAK,IAAIC,SAAS,EAClBC,aAAa,IAAIC,iBAAiB,EAClCC,QAAQ,IAAIC,YAAY,EACxBC,UAAU,IAAIC,MAAM,EACpBC,WAAW,IAAIC,WAAW,EAC1BC,QAAQ,IAAIC,YAAY,QACnB,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7B,MAAMC,cAAc,gBAAAC,EAAA,cAAG1C,KAAK,CAAC2C,IAAI,CAAAC,EAAA,GAAAF,EAAA,CAAC,MAAM;EAAAA,EAAA;EACtC,MAAM,CAACG,YAAY,EAAEC,eAAe,CAAC,GAAG7C,QAAQ,CAAC;IAC/C8C,WAAW,EAAE,EAAE;IACfC,UAAU,EAAE,GAAG;IACfC,cAAc,EAAE,EAAE;IAClBC,YAAY,EAAE,EAAE;IAChBC,YAAY,EAAE,EAAE;IAChBC,WAAW,EAAE;EACf,CAAC,CAAC;EAEF,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGrD,QAAQ,CAAC,CACjD;IACEsD,IAAI,EAAE,SAAS;IACfC,KAAK,EAAE,2BAA2B;IAClCC,WAAW,EAAE,uDAAuD;IACpEC,UAAU,EAAE,EAAE;IACdC,SAAS,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE;EACjD,CAAC,EACD;IACEN,IAAI,EAAE,SAAS;IACfC,KAAK,EAAE,kBAAkB;IACzBC,WAAW,EAAE,2CAA2C;IACxDC,UAAU,EAAE,EAAE;IACdC,SAAS,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACrD,CAAC,EACD;IACEN,IAAI,EAAE,MAAM;IACZC,KAAK,EAAE,yBAAyB;IAChCC,WAAW,EAAE,8CAA8C;IAC3DC,UAAU,EAAE,EAAE;IACdC,SAAS,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACrD,CAAC,CACF,CAAC;EAEF,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAG9D,QAAQ,CAAC,CACzC;IAAE+D,MAAM,EAAE,eAAe;IAAEC,OAAO,EAAE,EAAE;IAAEC,KAAK,EAAE;EAAO,CAAC,EACvD;IAAEF,MAAM,EAAE,QAAQ;IAAEC,OAAO,EAAE,EAAE;IAAEC,KAAK,EAAE;EAAK,CAAC,EAC9C;IAAEF,MAAM,EAAE,cAAc;IAAEC,OAAO,EAAE,EAAE;IAAEC,KAAK,EAAE;EAAS,CAAC,EACxD;IAAEF,MAAM,EAAE,eAAe;IAAEC,OAAO,EAAE,EAAE;IAAEC,KAAK,EAAE;EAAO,CAAC,CACxD,CAAC;;EAEF;EACAhE,SAAS,CAAC,MAAM;IACd,MAAMiE,QAAQ,GAAGC,WAAW,CAAC,MAAM;MACjCtB,eAAe,CAACuB,IAAI,KAAK;QACvB,GAAGA,IAAI;QACPrB,UAAU,EAAEqB,IAAI,CAACrB,UAAU,GAAGsB,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC;QAC3DzB,WAAW,EAAEuB,IAAI,CAACG,GAAG,CAAC,CAAC,EAAEH,IAAI,CAACI,GAAG,CAAC,GAAG,EAAEL,IAAI,CAACtB,WAAW,GAAG,CAACuB,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI,EAAE,CAAC,CAAC;QACtFtB,YAAY,EAAEoB,IAAI,CAACG,GAAG,CAAC,EAAE,EAAEH,IAAI,CAACI,GAAG,CAAC,GAAG,EAAEL,IAAI,CAACnB,YAAY,GAAG,CAACoB,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC;QACxFrB,YAAY,EAAEmB,IAAI,CAACG,GAAG,CAAC,EAAE,EAAEH,IAAI,CAACI,GAAG,CAAC,GAAG,EAAEL,IAAI,CAAClB,YAAY,GAAG,CAACmB,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC,CAAC;MACzF,CAAC,CAAC,CAAC;IACL,CAAC,EAAE,IAAI,CAAC;IAER,OAAO,MAAMG,aAAa,CAACR,QAAQ,CAAC;EACtC,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMS,mBAAmB,GAAIC,KAAK,IAAK;IACrC,IAAIA,KAAK,GAAG,EAAE,EAAE,OAAO,SAAS;IAChC,IAAIA,KAAK,GAAG,EAAE,EAAE,OAAO,SAAS;IAChC,OAAO,OAAO;EAChB,CAAC;EAED,MAAMC,cAAc,GAAIvB,IAAI,IAAK;IAC/B,QAAQA,IAAI;MACV,KAAK,SAAS;QAAE,oBAAOf,OAAA,CAAChB,WAAW;UAACuD,KAAK,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACtD,KAAK,SAAS;QAAE,oBAAO3C,OAAA,CAACd,UAAU;UAACqD,KAAK,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACrD,KAAK,MAAM;QAAE,oBAAO3C,OAAA,CAACF,YAAY;UAACyC,KAAK,EAAC;QAAM;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACjD;QAAS,oBAAO3C,OAAA,CAACpB,YAAY;UAAA4D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;IAClC;EACF,CAAC;EAED,oBACE3C,OAAA,CAACrC,GAAG;IAACiF,EAAE,EAAE;MAAEC,CAAC,EAAE;IAAE,CAAE;IAAAC,QAAA,gBAEhB9C,OAAA,CAACpC,IAAI;MAACmF,SAAS;MAACC,OAAO,EAAE,CAAE;MAACC,EAAE,EAAE,CAAE;MAAAH,QAAA,gBAChC9C,OAAA,CAACpC,IAAI;QAACsF,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAAP,QAAA,eAC9B9C,OAAA,CAACnC,IAAI;UACH+E,EAAE,EAAE;YACFU,UAAU,EAAGC,KAAK,IAAKA,KAAK,CAACC,OAAO,CAACC,IAAI,KAAK,MAAM,GAChD,6GAA6G,GAC7G,sHAAsH;YAC1HC,cAAc,EAAE,2BAA2B;YAC3CC,MAAM,EAAE,oCAAoC;YAC5CC,YAAY,EAAE,CAAC;YACfC,QAAQ,EAAE,UAAU;YACpBC,QAAQ,EAAE,QAAQ;YAClBC,SAAS,EAAE,sCAAsC;YACjDC,UAAU,EAAE,uCAAuC;YACnD,SAAS,EAAE;cACTC,SAAS,EAAE,8BAA8B;cACzCF,SAAS,EAAE;YACb,CAAC;YACD,WAAW,EAAE;cACXG,OAAO,EAAE,IAAI;cACbL,QAAQ,EAAE,UAAU;cACpBM,GAAG,EAAE,CAAC;cACNC,IAAI,EAAE,CAAC;cACPC,KAAK,EAAE,CAAC;cACRC,MAAM,EAAE,KAAK;cACbhB,UAAU,EAAE;YACd;UACF,CAAE;UAAAR,QAAA,eAEF9C,OAAA,CAAClC,WAAW;YAAAgF,QAAA,eACV9C,OAAA,CAACrC,GAAG;cAAC4G,OAAO,EAAC,MAAM;cAACC,UAAU,EAAC,QAAQ;cAACC,cAAc,EAAC,eAAe;cAAA3B,QAAA,gBACpE9C,OAAA,CAACrC,GAAG;gBAAAmF,QAAA,gBACF9C,OAAA,CAACjC,UAAU;kBAAC2G,OAAO,EAAC,IAAI;kBAACC,UAAU,EAAC,MAAM;kBAACpC,KAAK,EAAC,SAAS;kBAAAO,QAAA,EACvDzC,YAAY,CAACG;gBAAU;kBAAAgC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd,CAAC,eACb3C,OAAA,CAACjC,UAAU;kBAAC2G,OAAO,EAAC,OAAO;kBAACnC,KAAK,EAAC,gBAAgB;kBAAAO,QAAA,EAAC;gBAEnD;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACN3C,OAAA,CAAC9B,MAAM;gBACL0E,EAAE,EAAE;kBACFU,UAAU,EAAE,mDAAmD;kBAC/DsB,KAAK,EAAE,EAAE;kBACTN,MAAM,EAAE;gBACV,CAAE;gBAAAxB,QAAA,eAEF9C,OAAA,CAACZ,SAAS;kBAAAoD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEP3C,OAAA,CAACpC,IAAI;QAACsF,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAAP,QAAA,eAC9B9C,OAAA,CAACnC,IAAI;UACH+E,EAAE,EAAE;YACFU,UAAU,EAAGC,KAAK,IAAKA,KAAK,CAACC,OAAO,CAACC,IAAI,KAAK,MAAM,GAChD,6GAA6G,GAC7G,sHAAsH;YAC1HC,cAAc,EAAE,2BAA2B;YAC3CC,MAAM,EAAE,kCAAkC;YAC1CC,YAAY,EAAE,CAAC;YACfC,QAAQ,EAAE,UAAU;YACpBC,QAAQ,EAAE,QAAQ;YAClBC,SAAS,EAAE,oCAAoC;YAC/CC,UAAU,EAAE,uCAAuC;YACnD,SAAS,EAAE;cACTC,SAAS,EAAE,8BAA8B;cACzCF,SAAS,EAAE;YACb,CAAC;YACD,WAAW,EAAE;cACXG,OAAO,EAAE,IAAI;cACbL,QAAQ,EAAE,UAAU;cACpBM,GAAG,EAAE,CAAC;cACNC,IAAI,EAAE,CAAC;cACPC,KAAK,EAAE,CAAC;cACRC,MAAM,EAAE,KAAK;cACbhB,UAAU,EAAE;YACd;UACF,CAAE;UAAAR,QAAA,eAEF9C,OAAA,CAAClC,WAAW;YAAAgF,QAAA,eACV9C,OAAA,CAACrC,GAAG;cAAC4G,OAAO,EAAC,MAAM;cAACC,UAAU,EAAC,QAAQ;cAACC,cAAc,EAAC,eAAe;cAAA3B,QAAA,gBACpE9C,OAAA,CAACrC,GAAG;gBAAAmF,QAAA,gBACF9C,OAAA,CAACjC,UAAU;kBAAC2G,OAAO,EAAC,IAAI;kBAACC,UAAU,EAAC,MAAM;kBAACpC,KAAK,EAAC,OAAO;kBAAAO,QAAA,EACrDzC,YAAY,CAACI;gBAAc;kBAAA+B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClB,CAAC,eACb3C,OAAA,CAACjC,UAAU;kBAAC2G,OAAO,EAAC,OAAO;kBAACnC,KAAK,EAAC,gBAAgB;kBAAAO,QAAA,EAAC;gBAEnD;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACN3C,OAAA,CAAC9B,MAAM;gBACL0E,EAAE,EAAE;kBACFU,UAAU,EAAE,mDAAmD;kBAC/DsB,KAAK,EAAE,EAAE;kBACTN,MAAM,EAAE;gBACV,CAAE;gBAAAxB,QAAA,eAEF9C,OAAA,CAACd,UAAU;kBAAAsD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEP3C,OAAA,CAACpC,IAAI;QAACsF,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAAP,QAAA,eAC9B9C,OAAA,CAACnC,IAAI;UACH+E,EAAE,EAAE;YACFU,UAAU,EAAE,kFAAkF;YAC9FK,MAAM,EAAE,kCAAkC;YAC1CE,QAAQ,EAAE,UAAU;YACpBC,QAAQ,EAAE,QAAQ;YAClB,WAAW,EAAE;cACXI,OAAO,EAAE,IAAI;cACbL,QAAQ,EAAE,UAAU;cACpBM,GAAG,EAAE,CAAC;cACNC,IAAI,EAAE,CAAC;cACPC,KAAK,EAAE,CAAC;cACRC,MAAM,EAAE,KAAK;cACbhB,UAAU,EAAE;YACd;UACF,CAAE;UAAAR,QAAA,eAEF9C,OAAA,CAAClC,WAAW;YAAAgF,QAAA,gBACV9C,OAAA,CAACrC,GAAG;cAAC4G,OAAO,EAAC,MAAM;cAACC,UAAU,EAAC,QAAQ;cAACC,cAAc,EAAC,eAAe;cAAA3B,QAAA,gBACpE9C,OAAA,CAACrC,GAAG;gBAAAmF,QAAA,gBACF9C,OAAA,CAACjC,UAAU;kBAAC2G,OAAO,EAAC,IAAI;kBAACC,UAAU,EAAC,MAAM;kBAACpC,KAAK,EAAC,SAAS;kBAAAO,QAAA,GACvDzC,YAAY,CAACK,YAAY,EAAC,GAC7B;gBAAA;kBAAA8B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACb3C,OAAA,CAACjC,UAAU;kBAAC2G,OAAO,EAAC,OAAO;kBAACnC,KAAK,EAAC,gBAAgB;kBAAAO,QAAA,EAAC;gBAEnD;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACN3C,OAAA,CAAC9B,MAAM;gBACL0E,EAAE,EAAE;kBACFU,UAAU,EAAE,mDAAmD;kBAC/DsB,KAAK,EAAE,EAAE;kBACTN,MAAM,EAAE;gBACV,CAAE;gBAAAxB,QAAA,eAEF9C,OAAA,CAAClB,cAAc;kBAAA0D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACN3C,OAAA,CAAChC,cAAc;cACb0G,OAAO,EAAC,aAAa;cACrBG,KAAK,EAAExE,YAAY,CAACK,YAAa;cACjCkC,EAAE,EAAE;gBAAEkC,EAAE,EAAE,CAAC;gBAAER,MAAM,EAAE,CAAC;gBAAEV,YAAY,EAAE;cAAE,CAAE;cAC1CrB,KAAK,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACS;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEP3C,OAAA,CAACpC,IAAI;QAACsF,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAAP,QAAA,eAC9B9C,OAAA,CAACnC,IAAI;UACH+E,EAAE,EAAE;YACFU,UAAU,EAAE,kFAAkF;YAC9FK,MAAM,EAAE,mCAAmC;YAC3CE,QAAQ,EAAE,UAAU;YACpBC,QAAQ,EAAE,QAAQ;YAClB,WAAW,EAAE;cACXI,OAAO,EAAE,IAAI;cACbL,QAAQ,EAAE,UAAU;cACpBM,GAAG,EAAE,CAAC;cACNC,IAAI,EAAE,CAAC;cACPC,KAAK,EAAE,CAAC;cACRC,MAAM,EAAE,KAAK;cACbhB,UAAU,EAAE;YACd;UACF,CAAE;UAAAR,QAAA,eAEF9C,OAAA,CAAClC,WAAW;YAAAgF,QAAA,gBACV9C,OAAA,CAACrC,GAAG;cAAC4G,OAAO,EAAC,MAAM;cAACC,UAAU,EAAC,QAAQ;cAACC,cAAc,EAAC,eAAe;cAAA3B,QAAA,gBACpE9C,OAAA,CAACrC,GAAG;gBAAAmF,QAAA,gBACF9C,OAAA,CAACjC,UAAU;kBAAC2G,OAAO,EAAC,IAAI;kBAACC,UAAU,EAAC,MAAM;kBAACpC,KAAK,EAAC,WAAW;kBAAAO,QAAA,GACzDzC,YAAY,CAACM,YAAY,EAAC,GAC7B;gBAAA;kBAAA6B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACb3C,OAAA,CAACjC,UAAU;kBAAC2G,OAAO,EAAC,OAAO;kBAACnC,KAAK,EAAC,gBAAgB;kBAAAO,QAAA,EAAC;gBAEnD;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACN3C,OAAA,CAAC9B,MAAM;gBACL0E,EAAE,EAAE;kBACFU,UAAU,EAAE,mDAAmD;kBAC/DsB,KAAK,EAAE,EAAE;kBACTN,MAAM,EAAE;gBACV,CAAE;gBAAAxB,QAAA,eAEF9C,OAAA,CAACN,MAAM;kBAAA8C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACN3C,OAAA,CAAChC,cAAc;cACb0G,OAAO,EAAC,aAAa;cACrBG,KAAK,EAAExE,YAAY,CAACM,YAAa;cACjCiC,EAAE,EAAE;gBAAEkC,EAAE,EAAE,CAAC;gBAAER,MAAM,EAAE,CAAC;gBAAEV,YAAY,EAAE;cAAE,CAAE;cAC1CrB,KAAK,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACS;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGP3C,OAAA,CAACpC,IAAI;MAACmF,SAAS;MAACC,OAAO,EAAE,CAAE;MAAAF,QAAA,gBACzB9C,OAAA,CAACpC,IAAI;QAACsF,IAAI;QAACC,EAAE,EAAE,EAAG;QAACE,EAAE,EAAE,CAAE;QAAAP,QAAA,eACvB9C,OAAA,CAACnC,IAAI;UACH+E,EAAE,EAAE;YACFU,UAAU,EAAGC,KAAK,IAAKA,KAAK,CAACC,OAAO,CAACC,IAAI,KAAK,MAAM,GAChD,iFAAiF,GACjF,uFAAuF;YAC3FC,cAAc,EAAE,YAAY;YAC5BC,MAAM,EAAGJ,KAAK,IAAK,aAAaA,KAAK,CAACC,OAAO,CAACuB,OAAO,EAAE;YACvDT,MAAM,EAAE;UACV,CAAE;UAAAxB,QAAA,eAEF9C,OAAA,CAAClC,WAAW;YAAAgF,QAAA,gBACV9C,OAAA,CAACrC,GAAG;cAAC4G,OAAO,EAAC,MAAM;cAACC,UAAU,EAAC,QAAQ;cAACQ,GAAG,EAAE,CAAE;cAAC/B,EAAE,EAAE,CAAE;cAAAH,QAAA,gBACpD9C,OAAA,CAAC9B,MAAM;gBACL0E,EAAE,EAAE;kBACFU,UAAU,EAAE,mDAAmD;kBAC/DsB,KAAK,EAAE,EAAE;kBACTN,MAAM,EAAE;gBACV,CAAE;gBAAAxB,QAAA,eAEF9C,OAAA,CAACF,YAAY;kBAAA0C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACT3C,OAAA,CAACjC,UAAU;gBAAC2G,OAAO,EAAC,IAAI;gBAACC,UAAU,EAAC,MAAM;gBAAA7B,QAAA,EAAC;cAE3C;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACb3C,OAAA,CAACvB,KAAK;gBAACwG,YAAY,EAAEpE,aAAa,CAACqE,MAAO;gBAAC3C,KAAK,EAAC,SAAS;gBAAAO,QAAA,eACxD9C,OAAA,CAACV,iBAAiB;kBAAAkD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eAEN3C,OAAA,CAAC7B,IAAI;cAAA2E,QAAA,EACFjC,aAAa,CAACsE,GAAG,CAAC,CAACC,OAAO,EAAEC,KAAK,kBAChCrF,OAAA,CAACxC,KAAK,CAAC8H,QAAQ;gBAAAxC,QAAA,gBACb9C,OAAA,CAAC5B,QAAQ;kBACPwE,EAAE,EAAE;oBACFgB,YAAY,EAAE,CAAC;oBACfX,EAAE,EAAE,CAAC;oBACLK,UAAU,EAAGC,KAAK,IAAKA,KAAK,CAACC,OAAO,CAACC,IAAI,KAAK,MAAM,GAChD,2BAA2B,GAC3B,qBAAqB;oBACzB,SAAS,EAAE;sBACTH,UAAU,EAAGC,KAAK,IAAKA,KAAK,CAACC,OAAO,CAACC,IAAI,KAAK,MAAM,GAChD,2BAA2B,GAC3B;oBACN;kBACF,CAAE;kBAAAX,QAAA,gBAEF9C,OAAA,CAAC3B,YAAY;oBAAAyE,QAAA,EACVR,cAAc,CAAC8C,OAAO,CAACrE,IAAI;kBAAC;oBAAAyB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjB,CAAC,eACf3C,OAAA,CAAC1B,YAAY;oBACXiH,OAAO,eACLvF,OAAA,CAACrC,GAAG;sBAAC4G,OAAO,EAAC,MAAM;sBAACC,UAAU,EAAC,QAAQ;sBAACQ,GAAG,EAAE,CAAE;sBAAAlC,QAAA,gBAC7C9C,OAAA,CAACjC,UAAU;wBAAC2G,OAAO,EAAC,WAAW;wBAACC,UAAU,EAAC,KAAK;wBAAA7B,QAAA,EAC7CsC,OAAO,CAACpE;sBAAK;wBAAAwB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACJ,CAAC,eACb3C,OAAA,CAAC/B,IAAI;wBACHuH,KAAK,EAAE,GAAGJ,OAAO,CAAClE,UAAU,cAAe;wBAC3CuE,IAAI,EAAC,OAAO;wBACZlD,KAAK,EAAE6C,OAAO,CAAClE,UAAU,GAAG,EAAE,GAAG,SAAS,GAAG,SAAU;wBACvDwD,OAAO,EAAC;sBAAU;wBAAAlC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnB,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CACN;oBACD+C,SAAS,eACP1F,OAAA,CAACrC,GAAG;sBAAAmF,QAAA,gBACF9C,OAAA,CAACjC,UAAU;wBAAC2G,OAAO,EAAC,OAAO;wBAACnC,KAAK,EAAC,gBAAgB;wBAACK,EAAE,EAAE;0BAAEK,EAAE,EAAE;wBAAE,CAAE;wBAAAH,QAAA,EAC9DsC,OAAO,CAACnE;sBAAW;wBAAAuB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACV,CAAC,eACb3C,OAAA,CAACjC,UAAU;wBAAC2G,OAAO,EAAC,SAAS;wBAACnC,KAAK,EAAC,gBAAgB;wBAAAO,QAAA,EACjDsC,OAAO,CAACjE,SAAS,CAACwE,cAAc,CAAC;sBAAC;wBAAAnD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACzB,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV;kBACN;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eACF3C,OAAA,CAACxB,OAAO;oBAACwC,KAAK,EAAC,cAAc;oBAAA8B,QAAA,eAC3B9C,OAAA,CAACzB,UAAU;sBAACkH,IAAI,EAAC,OAAO;sBAAA3C,QAAA,eACtB9C,OAAA,CAACJ,WAAW;wBAAA4C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC,EACV0C,KAAK,GAAGxE,aAAa,CAACqE,MAAM,GAAG,CAAC,iBAAIlF,OAAA,CAACtB,OAAO;kBAAA8D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA,GAjD7B0C,KAAK;gBAAA7C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAkDV,CACjB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEP3C,OAAA,CAACpC,IAAI;QAACsF,IAAI;QAACC,EAAE,EAAE,EAAG;QAACE,EAAE,EAAE,CAAE;QAAAP,QAAA,eACvB9C,OAAA,CAACnC,IAAI;UACH+E,EAAE,EAAE;YACFU,UAAU,EAAGC,KAAK,IAAKA,KAAK,CAACC,OAAO,CAACC,IAAI,KAAK,MAAM,GAChD,iFAAiF,GACjF,uFAAuF;YAC3FC,cAAc,EAAE,YAAY;YAC5BC,MAAM,EAAGJ,KAAK,IAAK,aAAaA,KAAK,CAACC,OAAO,CAACuB,OAAO,EAAE;YACvDT,MAAM,EAAE;UACV,CAAE;UAAAxB,QAAA,eAEF9C,OAAA,CAAClC,WAAW;YAAAgF,QAAA,gBACV9C,OAAA,CAACrC,GAAG;cAAC4G,OAAO,EAAC,MAAM;cAACC,UAAU,EAAC,QAAQ;cAACQ,GAAG,EAAE,CAAE;cAAC/B,EAAE,EAAE,CAAE;cAAAH,QAAA,gBACpD9C,OAAA,CAAC9B,MAAM;gBACL0E,EAAE,EAAE;kBACFU,UAAU,EAAE,mDAAmD;kBAC/DsB,KAAK,EAAE,EAAE;kBACTN,MAAM,EAAE;gBACV,CAAE;gBAAAxB,QAAA,eAEF9C,OAAA,CAACR,YAAY;kBAAAgD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACT3C,OAAA,CAACjC,UAAU;gBAAC2G,OAAO,EAAC,IAAI;gBAACC,UAAU,EAAC,MAAM;gBAAA7B,QAAA,EAAC;cAE3C;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAEN3C,OAAA,CAAC7B,IAAI;cAACyH,KAAK;cAAA9C,QAAA,EACRxB,SAAS,CAAC6D,GAAG,CAAC,CAAC3D,MAAM,EAAE6D,KAAK,kBAC3BrF,OAAA,CAAC5B,QAAQ;gBAAawE,EAAE,EAAE;kBAAEiD,EAAE,EAAE;gBAAE,CAAE;gBAAA/C,QAAA,eAClC9C,OAAA,CAAC1B,YAAY;kBACXiH,OAAO,eACLvF,OAAA,CAACrC,GAAG;oBAAC4G,OAAO,EAAC,MAAM;oBAACC,UAAU,EAAC,QAAQ;oBAACC,cAAc,EAAC,eAAe;oBAAA3B,QAAA,gBACpE9C,OAAA,CAACjC,UAAU;sBAAC2G,OAAO,EAAC,OAAO;sBAACC,UAAU,EAAC,KAAK;sBAAA7B,QAAA,EACzCtB,MAAM,CAACA;oBAAM;sBAAAgB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC,eACb3C,OAAA,CAACrC,GAAG;sBAAC4G,OAAO,EAAC,MAAM;sBAACC,UAAU,EAAC,QAAQ;sBAACQ,GAAG,EAAE,CAAE;sBAAAlC,QAAA,gBAC7C9C,OAAA,CAACjC,UAAU;wBAAC2G,OAAO,EAAC,OAAO;wBAACnC,KAAK,EAAC,gBAAgB;wBAAAO,QAAA,EAC/CtB,MAAM,CAACC;sBAAO;wBAAAe,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACL,CAAC,eACb3C,OAAA,CAAC/B,IAAI;wBACHuH,KAAK,EAAEhE,MAAM,CAACE,KAAM;wBACpB+D,IAAI,EAAC,OAAO;wBACZlD,KAAK,EACHf,MAAM,CAACE,KAAK,KAAK,IAAI,GAAG,OAAO,GAC/BF,MAAM,CAACE,KAAK,KAAK,MAAM,GAAG,SAAS,GAAG,SACvC;wBACDgD,OAAO,EAAC;sBAAU;wBAAAlC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnB,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBACN;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF;cAAC,GAvBW0C,KAAK;gBAAA7C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAwBV,CACX;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC,kCAAC;AAACmD,GAAA,GA7aG7F,cAAc;AA+apBA,cAAc,CAAC8F,WAAW,GAAG,gBAAgB;AAE7C,eAAe9F,cAAc;AAAC,IAAAG,EAAA,EAAA0F,GAAA;AAAAE,YAAA,CAAA5F,EAAA;AAAA4F,YAAA,CAAAF,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}