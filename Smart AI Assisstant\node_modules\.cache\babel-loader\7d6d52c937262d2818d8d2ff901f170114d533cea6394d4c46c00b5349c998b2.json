{"ast": null, "code": "var _jsxFileName = \"E:\\\\PythonProjects\\\\React.js\\\\Smart AI Assisstant\\\\src\\\\contexts\\\\AuthContext.jsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useReducer, useEffect, useCallback } from 'react';\nimport authService from '../services/auth';\n\n// Initial state\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst initialState = {\n  user: null,\n  token: null,\n  isAuthenticated: false,\n  isLoading: true,\n  error: null\n};\n\n// Action types\nconst actionTypes = {\n  SET_LOADING: 'SET_LOADING',\n  LOGIN_SUCCESS: 'LOGIN_SUCCESS',\n  LOGOUT: 'LOGOUT',\n  SET_ERROR: 'SET_ERROR',\n  CLEAR_ERROR: 'CLEAR_ERROR',\n  UPDATE_USER: 'UPDATE_USER'\n};\n\n// Reducer\nconst authReducer = (state, action) => {\n  switch (action.type) {\n    case actionTypes.SET_LOADING:\n      return {\n        ...state,\n        isLoading: action.payload\n      };\n    case actionTypes.LOGIN_SUCCESS:\n      return {\n        ...state,\n        user: action.payload.user,\n        token: action.payload.token,\n        isAuthenticated: true,\n        isLoading: false,\n        error: null\n      };\n    case actionTypes.LOGOUT:\n      return {\n        ...state,\n        user: null,\n        token: null,\n        isAuthenticated: false,\n        isLoading: false,\n        error: null\n      };\n    case actionTypes.SET_ERROR:\n      return {\n        ...state,\n        error: action.payload,\n        isLoading: false\n      };\n    case actionTypes.CLEAR_ERROR:\n      return {\n        ...state,\n        error: null\n      };\n    case actionTypes.UPDATE_USER:\n      return {\n        ...state,\n        user: action.payload\n      };\n    default:\n      return state;\n  }\n};\n\n// Create context\nconst AuthContext = /*#__PURE__*/createContext();\n\n// Auth provider component\nexport const AuthProvider = ({\n  children\n}) => {\n  _s();\n  const [state, dispatch] = useReducer(authReducer, initialState);\n\n  // Initialize auth state on app load\n  useEffect(() => {\n    const initializeAuth = async () => {\n      try {\n        dispatch({\n          type: actionTypes.SET_LOADING,\n          payload: true\n        });\n        const token = authService.getToken();\n        const user = authService.getCurrentUser();\n        if (token && user) {\n          // Verify token is still valid by getting fresh profile\n          const result = await authService.getProfile();\n          if (result.success) {\n            dispatch({\n              type: actionTypes.LOGIN_SUCCESS,\n              payload: {\n                user: result.user,\n                token\n              }\n            });\n          } else {\n            // Token is invalid, clear auth data\n            authService.clearAuthData();\n            dispatch({\n              type: actionTypes.LOGOUT\n            });\n          }\n        } else {\n          dispatch({\n            type: actionTypes.LOGOUT\n          });\n        }\n      } catch (error) {\n        console.error('Auth initialization error:', error);\n        authService.clearAuthData();\n        dispatch({\n          type: actionTypes.LOGOUT\n        });\n      } finally {\n        dispatch({\n          type: actionTypes.SET_LOADING,\n          payload: false\n        });\n      }\n    };\n    initializeAuth();\n  }, []);\n\n  // Login function\n  const login = useCallback(async credentials => {\n    try {\n      dispatch({\n        type: actionTypes.SET_LOADING,\n        payload: true\n      });\n      dispatch({\n        type: actionTypes.CLEAR_ERROR\n      });\n      const result = await authService.login(credentials);\n      if (result.success) {\n        dispatch({\n          type: actionTypes.LOGIN_SUCCESS,\n          payload: {\n            user: result.user,\n            token: result.token\n          }\n        });\n        return {\n          success: true\n        };\n      } else {\n        dispatch({\n          type: actionTypes.SET_ERROR,\n          payload: result.error\n        });\n        return {\n          success: false,\n          error: result.error\n        };\n      }\n    } catch (error) {\n      const errorMessage = 'Login failed. Please try again.';\n      dispatch({\n        type: actionTypes.SET_ERROR,\n        payload: errorMessage\n      });\n      return {\n        success: false,\n        error: errorMessage\n      };\n    }\n  }, []);\n\n  // Signup function\n  const signup = useCallback(async userData => {\n    try {\n      dispatch({\n        type: actionTypes.SET_LOADING,\n        payload: true\n      });\n      dispatch({\n        type: actionTypes.CLEAR_ERROR\n      });\n      const result = await authService.signup(userData);\n      if (result.success) {\n        dispatch({\n          type: actionTypes.LOGIN_SUCCESS,\n          payload: {\n            user: result.user,\n            token: result.token\n          }\n        });\n        return {\n          success: true\n        };\n      } else {\n        dispatch({\n          type: actionTypes.SET_ERROR,\n          payload: result.error\n        });\n        return {\n          success: false,\n          error: result.error,\n          details: result.details\n        };\n      }\n    } catch (error) {\n      const errorMessage = 'Registration failed. Please try again.';\n      dispatch({\n        type: actionTypes.SET_ERROR,\n        payload: errorMessage\n      });\n      return {\n        success: false,\n        error: errorMessage\n      };\n    }\n  }, []);\n\n  // Logout function\n  const logout = useCallback(async () => {\n    try {\n      dispatch({\n        type: actionTypes.SET_LOADING,\n        payload: true\n      });\n      await authService.logout();\n      dispatch({\n        type: actionTypes.LOGOUT\n      });\n    } catch (error) {\n      console.error('Logout error:', error);\n      // Still logout locally even if server request fails\n      dispatch({\n        type: actionTypes.LOGOUT\n      });\n    }\n  }, []);\n\n  // Clear error function\n  const clearError = useCallback(() => {\n    dispatch({\n      type: actionTypes.CLEAR_ERROR\n    });\n  }, []);\n\n  // Update user function\n  const updateUser = useCallback(userData => {\n    dispatch({\n      type: actionTypes.UPDATE_USER,\n      payload: userData\n    });\n  }, []);\n  const value = {\n    ...state,\n    login,\n    signup,\n    logout,\n    clearError,\n    updateUser\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 203,\n    columnNumber: 10\n  }, this);\n};\n\n// Custom hook to use auth context\n_s(AuthProvider, \"XW5mgz3U7PXUc0jl0Ic13svkB0o=\");\n_c = AuthProvider;\nexport const useAuth = () => {\n  _s2();\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n_s2(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useReducer", "useEffect", "useCallback", "authService", "jsxDEV", "_jsxDEV", "initialState", "user", "token", "isAuthenticated", "isLoading", "error", "actionTypes", "SET_LOADING", "LOGIN_SUCCESS", "LOGOUT", "SET_ERROR", "CLEAR_ERROR", "UPDATE_USER", "authReducer", "state", "action", "type", "payload", "AuthContext", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s", "dispatch", "initializeAuth", "getToken", "getCurrentUser", "result", "getProfile", "success", "clearAuthData", "console", "login", "credentials", "errorMessage", "signup", "userData", "details", "logout", "clearError", "updateUser", "value", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "useAuth", "_s2", "context", "Error", "$RefreshReg$"], "sources": ["E:/PythonProjects/React.js/Smart AI Assisstant/src/contexts/AuthContext.jsx"], "sourcesContent": ["import React, { createContext, useContext, useReducer, useEffect, useCallback } from 'react';\nimport authService from '../services/auth';\n\n// Initial state\nconst initialState = {\n  user: null,\n  token: null,\n  isAuthenticated: false,\n  isLoading: true,\n  error: null,\n};\n\n// Action types\nconst actionTypes = {\n  SET_LOADING: 'SET_LOADING',\n  LOGIN_SUCCESS: 'LOGIN_SUCCESS',\n  LOGOUT: 'LOGOUT',\n  SET_ERROR: 'SET_ERROR',\n  CLEAR_ERROR: 'CLEAR_ERROR',\n  UPDATE_USER: 'UPDATE_USER',\n};\n\n// Reducer\nconst authReducer = (state, action) => {\n  switch (action.type) {\n    case actionTypes.SET_LOADING:\n      return {\n        ...state,\n        isLoading: action.payload,\n      };\n\n    case actionTypes.LOGIN_SUCCESS:\n      return {\n        ...state,\n        user: action.payload.user,\n        token: action.payload.token,\n        isAuthenticated: true,\n        isLoading: false,\n        error: null,\n      };\n\n    case actionTypes.LOGOUT:\n      return {\n        ...state,\n        user: null,\n        token: null,\n        isAuthenticated: false,\n        isLoading: false,\n        error: null,\n      };\n\n    case actionTypes.SET_ERROR:\n      return {\n        ...state,\n        error: action.payload,\n        isLoading: false,\n      };\n\n    case actionTypes.CLEAR_ERROR:\n      return {\n        ...state,\n        error: null,\n      };\n\n    case actionTypes.UPDATE_USER:\n      return {\n        ...state,\n        user: action.payload,\n      };\n\n    default:\n      return state;\n  }\n};\n\n// Create context\nconst AuthContext = createContext();\n\n// Auth provider component\nexport const AuthProvider = ({ children }) => {\n  const [state, dispatch] = useReducer(authReducer, initialState);\n\n  // Initialize auth state on app load\n  useEffect(() => {\n    const initializeAuth = async () => {\n      try {\n        dispatch({ type: actionTypes.SET_LOADING, payload: true });\n\n        const token = authService.getToken();\n        const user = authService.getCurrentUser();\n\n        if (token && user) {\n          // Verify token is still valid by getting fresh profile\n          const result = await authService.getProfile();\n          \n          if (result.success) {\n            dispatch({\n              type: actionTypes.LOGIN_SUCCESS,\n              payload: { user: result.user, token },\n            });\n          } else {\n            // Token is invalid, clear auth data\n            authService.clearAuthData();\n            dispatch({ type: actionTypes.LOGOUT });\n          }\n        } else {\n          dispatch({ type: actionTypes.LOGOUT });\n        }\n      } catch (error) {\n        console.error('Auth initialization error:', error);\n        authService.clearAuthData();\n        dispatch({ type: actionTypes.LOGOUT });\n      } finally {\n        dispatch({ type: actionTypes.SET_LOADING, payload: false });\n      }\n    };\n\n    initializeAuth();\n  }, []);\n\n  // Login function\n  const login = useCallback(async (credentials) => {\n    try {\n      dispatch({ type: actionTypes.SET_LOADING, payload: true });\n      dispatch({ type: actionTypes.CLEAR_ERROR });\n\n      const result = await authService.login(credentials);\n\n      if (result.success) {\n        dispatch({\n          type: actionTypes.LOGIN_SUCCESS,\n          payload: { user: result.user, token: result.token },\n        });\n        return { success: true };\n      } else {\n        dispatch({ type: actionTypes.SET_ERROR, payload: result.error });\n        return { success: false, error: result.error };\n      }\n    } catch (error) {\n      const errorMessage = 'Login failed. Please try again.';\n      dispatch({ type: actionTypes.SET_ERROR, payload: errorMessage });\n      return { success: false, error: errorMessage };\n    }\n  }, []);\n\n  // Signup function\n  const signup = useCallback(async (userData) => {\n    try {\n      dispatch({ type: actionTypes.SET_LOADING, payload: true });\n      dispatch({ type: actionTypes.CLEAR_ERROR });\n\n      const result = await authService.signup(userData);\n\n      if (result.success) {\n        dispatch({\n          type: actionTypes.LOGIN_SUCCESS,\n          payload: { user: result.user, token: result.token },\n        });\n        return { success: true };\n      } else {\n        dispatch({ type: actionTypes.SET_ERROR, payload: result.error });\n        return { success: false, error: result.error, details: result.details };\n      }\n    } catch (error) {\n      const errorMessage = 'Registration failed. Please try again.';\n      dispatch({ type: actionTypes.SET_ERROR, payload: errorMessage });\n      return { success: false, error: errorMessage };\n    }\n  }, []);\n\n  // Logout function\n  const logout = useCallback(async () => {\n    try {\n      dispatch({ type: actionTypes.SET_LOADING, payload: true });\n      await authService.logout();\n      dispatch({ type: actionTypes.LOGOUT });\n    } catch (error) {\n      console.error('Logout error:', error);\n      // Still logout locally even if server request fails\n      dispatch({ type: actionTypes.LOGOUT });\n    }\n  }, []);\n\n  // Clear error function\n  const clearError = useCallback(() => {\n    dispatch({ type: actionTypes.CLEAR_ERROR });\n  }, []);\n\n  // Update user function\n  const updateUser = useCallback((userData) => {\n    dispatch({ type: actionTypes.UPDATE_USER, payload: userData });\n  }, []);\n\n  const value = {\n    ...state,\n    login,\n    signup,\n    logout,\n    clearError,\n    updateUser,\n  };\n\n  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;\n};\n\n// Custom hook to use auth context\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,UAAU,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC5F,OAAOC,WAAW,MAAM,kBAAkB;;AAE1C;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,YAAY,GAAG;EACnBC,IAAI,EAAE,IAAI;EACVC,KAAK,EAAE,IAAI;EACXC,eAAe,EAAE,KAAK;EACtBC,SAAS,EAAE,IAAI;EACfC,KAAK,EAAE;AACT,CAAC;;AAED;AACA,MAAMC,WAAW,GAAG;EAClBC,WAAW,EAAE,aAAa;EAC1BC,aAAa,EAAE,eAAe;EAC9BC,MAAM,EAAE,QAAQ;EAChBC,SAAS,EAAE,WAAW;EACtBC,WAAW,EAAE,aAAa;EAC1BC,WAAW,EAAE;AACf,CAAC;;AAED;AACA,MAAMC,WAAW,GAAGA,CAACC,KAAK,EAAEC,MAAM,KAAK;EACrC,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAKV,WAAW,CAACC,WAAW;MAC1B,OAAO;QACL,GAAGO,KAAK;QACRV,SAAS,EAAEW,MAAM,CAACE;MACpB,CAAC;IAEH,KAAKX,WAAW,CAACE,aAAa;MAC5B,OAAO;QACL,GAAGM,KAAK;QACRb,IAAI,EAAEc,MAAM,CAACE,OAAO,CAAChB,IAAI;QACzBC,KAAK,EAAEa,MAAM,CAACE,OAAO,CAACf,KAAK;QAC3BC,eAAe,EAAE,IAAI;QACrBC,SAAS,EAAE,KAAK;QAChBC,KAAK,EAAE;MACT,CAAC;IAEH,KAAKC,WAAW,CAACG,MAAM;MACrB,OAAO;QACL,GAAGK,KAAK;QACRb,IAAI,EAAE,IAAI;QACVC,KAAK,EAAE,IAAI;QACXC,eAAe,EAAE,KAAK;QACtBC,SAAS,EAAE,KAAK;QAChBC,KAAK,EAAE;MACT,CAAC;IAEH,KAAKC,WAAW,CAACI,SAAS;MACxB,OAAO;QACL,GAAGI,KAAK;QACRT,KAAK,EAAEU,MAAM,CAACE,OAAO;QACrBb,SAAS,EAAE;MACb,CAAC;IAEH,KAAKE,WAAW,CAACK,WAAW;MAC1B,OAAO;QACL,GAAGG,KAAK;QACRT,KAAK,EAAE;MACT,CAAC;IAEH,KAAKC,WAAW,CAACM,WAAW;MAC1B,OAAO;QACL,GAAGE,KAAK;QACRb,IAAI,EAAEc,MAAM,CAACE;MACf,CAAC;IAEH;MACE,OAAOH,KAAK;EAChB;AACF,CAAC;;AAED;AACA,MAAMI,WAAW,gBAAG1B,aAAa,CAAC,CAAC;;AAEnC;AACA,OAAO,MAAM2B,YAAY,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAC5C,MAAM,CAACP,KAAK,EAAEQ,QAAQ,CAAC,GAAG5B,UAAU,CAACmB,WAAW,EAAEb,YAAY,CAAC;;EAE/D;EACAL,SAAS,CAAC,MAAM;IACd,MAAM4B,cAAc,GAAG,MAAAA,CAAA,KAAY;MACjC,IAAI;QACFD,QAAQ,CAAC;UAAEN,IAAI,EAAEV,WAAW,CAACC,WAAW;UAAEU,OAAO,EAAE;QAAK,CAAC,CAAC;QAE1D,MAAMf,KAAK,GAAGL,WAAW,CAAC2B,QAAQ,CAAC,CAAC;QACpC,MAAMvB,IAAI,GAAGJ,WAAW,CAAC4B,cAAc,CAAC,CAAC;QAEzC,IAAIvB,KAAK,IAAID,IAAI,EAAE;UACjB;UACA,MAAMyB,MAAM,GAAG,MAAM7B,WAAW,CAAC8B,UAAU,CAAC,CAAC;UAE7C,IAAID,MAAM,CAACE,OAAO,EAAE;YAClBN,QAAQ,CAAC;cACPN,IAAI,EAAEV,WAAW,CAACE,aAAa;cAC/BS,OAAO,EAAE;gBAAEhB,IAAI,EAAEyB,MAAM,CAACzB,IAAI;gBAAEC;cAAM;YACtC,CAAC,CAAC;UACJ,CAAC,MAAM;YACL;YACAL,WAAW,CAACgC,aAAa,CAAC,CAAC;YAC3BP,QAAQ,CAAC;cAAEN,IAAI,EAAEV,WAAW,CAACG;YAAO,CAAC,CAAC;UACxC;QACF,CAAC,MAAM;UACLa,QAAQ,CAAC;YAAEN,IAAI,EAAEV,WAAW,CAACG;UAAO,CAAC,CAAC;QACxC;MACF,CAAC,CAAC,OAAOJ,KAAK,EAAE;QACdyB,OAAO,CAACzB,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;QAClDR,WAAW,CAACgC,aAAa,CAAC,CAAC;QAC3BP,QAAQ,CAAC;UAAEN,IAAI,EAAEV,WAAW,CAACG;QAAO,CAAC,CAAC;MACxC,CAAC,SAAS;QACRa,QAAQ,CAAC;UAAEN,IAAI,EAAEV,WAAW,CAACC,WAAW;UAAEU,OAAO,EAAE;QAAM,CAAC,CAAC;MAC7D;IACF,CAAC;IAEDM,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMQ,KAAK,GAAGnC,WAAW,CAAC,MAAOoC,WAAW,IAAK;IAC/C,IAAI;MACFV,QAAQ,CAAC;QAAEN,IAAI,EAAEV,WAAW,CAACC,WAAW;QAAEU,OAAO,EAAE;MAAK,CAAC,CAAC;MAC1DK,QAAQ,CAAC;QAAEN,IAAI,EAAEV,WAAW,CAACK;MAAY,CAAC,CAAC;MAE3C,MAAMe,MAAM,GAAG,MAAM7B,WAAW,CAACkC,KAAK,CAACC,WAAW,CAAC;MAEnD,IAAIN,MAAM,CAACE,OAAO,EAAE;QAClBN,QAAQ,CAAC;UACPN,IAAI,EAAEV,WAAW,CAACE,aAAa;UAC/BS,OAAO,EAAE;YAAEhB,IAAI,EAAEyB,MAAM,CAACzB,IAAI;YAAEC,KAAK,EAAEwB,MAAM,CAACxB;UAAM;QACpD,CAAC,CAAC;QACF,OAAO;UAAE0B,OAAO,EAAE;QAAK,CAAC;MAC1B,CAAC,MAAM;QACLN,QAAQ,CAAC;UAAEN,IAAI,EAAEV,WAAW,CAACI,SAAS;UAAEO,OAAO,EAAES,MAAM,CAACrB;QAAM,CAAC,CAAC;QAChE,OAAO;UAAEuB,OAAO,EAAE,KAAK;UAAEvB,KAAK,EAAEqB,MAAM,CAACrB;QAAM,CAAC;MAChD;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACd,MAAM4B,YAAY,GAAG,iCAAiC;MACtDX,QAAQ,CAAC;QAAEN,IAAI,EAAEV,WAAW,CAACI,SAAS;QAAEO,OAAO,EAAEgB;MAAa,CAAC,CAAC;MAChE,OAAO;QAAEL,OAAO,EAAE,KAAK;QAAEvB,KAAK,EAAE4B;MAAa,CAAC;IAChD;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMC,MAAM,GAAGtC,WAAW,CAAC,MAAOuC,QAAQ,IAAK;IAC7C,IAAI;MACFb,QAAQ,CAAC;QAAEN,IAAI,EAAEV,WAAW,CAACC,WAAW;QAAEU,OAAO,EAAE;MAAK,CAAC,CAAC;MAC1DK,QAAQ,CAAC;QAAEN,IAAI,EAAEV,WAAW,CAACK;MAAY,CAAC,CAAC;MAE3C,MAAMe,MAAM,GAAG,MAAM7B,WAAW,CAACqC,MAAM,CAACC,QAAQ,CAAC;MAEjD,IAAIT,MAAM,CAACE,OAAO,EAAE;QAClBN,QAAQ,CAAC;UACPN,IAAI,EAAEV,WAAW,CAACE,aAAa;UAC/BS,OAAO,EAAE;YAAEhB,IAAI,EAAEyB,MAAM,CAACzB,IAAI;YAAEC,KAAK,EAAEwB,MAAM,CAACxB;UAAM;QACpD,CAAC,CAAC;QACF,OAAO;UAAE0B,OAAO,EAAE;QAAK,CAAC;MAC1B,CAAC,MAAM;QACLN,QAAQ,CAAC;UAAEN,IAAI,EAAEV,WAAW,CAACI,SAAS;UAAEO,OAAO,EAAES,MAAM,CAACrB;QAAM,CAAC,CAAC;QAChE,OAAO;UAAEuB,OAAO,EAAE,KAAK;UAAEvB,KAAK,EAAEqB,MAAM,CAACrB,KAAK;UAAE+B,OAAO,EAAEV,MAAM,CAACU;QAAQ,CAAC;MACzE;IACF,CAAC,CAAC,OAAO/B,KAAK,EAAE;MACd,MAAM4B,YAAY,GAAG,wCAAwC;MAC7DX,QAAQ,CAAC;QAAEN,IAAI,EAAEV,WAAW,CAACI,SAAS;QAAEO,OAAO,EAAEgB;MAAa,CAAC,CAAC;MAChE,OAAO;QAAEL,OAAO,EAAE,KAAK;QAAEvB,KAAK,EAAE4B;MAAa,CAAC;IAChD;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMI,MAAM,GAAGzC,WAAW,CAAC,YAAY;IACrC,IAAI;MACF0B,QAAQ,CAAC;QAAEN,IAAI,EAAEV,WAAW,CAACC,WAAW;QAAEU,OAAO,EAAE;MAAK,CAAC,CAAC;MAC1D,MAAMpB,WAAW,CAACwC,MAAM,CAAC,CAAC;MAC1Bf,QAAQ,CAAC;QAAEN,IAAI,EAAEV,WAAW,CAACG;MAAO,CAAC,CAAC;IACxC,CAAC,CAAC,OAAOJ,KAAK,EAAE;MACdyB,OAAO,CAACzB,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;MACrC;MACAiB,QAAQ,CAAC;QAAEN,IAAI,EAAEV,WAAW,CAACG;MAAO,CAAC,CAAC;IACxC;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAM6B,UAAU,GAAG1C,WAAW,CAAC,MAAM;IACnC0B,QAAQ,CAAC;MAAEN,IAAI,EAAEV,WAAW,CAACK;IAAY,CAAC,CAAC;EAC7C,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAM4B,UAAU,GAAG3C,WAAW,CAAEuC,QAAQ,IAAK;IAC3Cb,QAAQ,CAAC;MAAEN,IAAI,EAAEV,WAAW,CAACM,WAAW;MAAEK,OAAO,EAAEkB;IAAS,CAAC,CAAC;EAChE,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMK,KAAK,GAAG;IACZ,GAAG1B,KAAK;IACRiB,KAAK;IACLG,MAAM;IACNG,MAAM;IACNC,UAAU;IACVC;EACF,CAAC;EAED,oBAAOxC,OAAA,CAACmB,WAAW,CAACuB,QAAQ;IAACD,KAAK,EAAEA,KAAM;IAAApB,QAAA,EAAEA;EAAQ;IAAAsB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAuB,CAAC;AAC9E,CAAC;;AAED;AAAAxB,EAAA,CA9HaF,YAAY;AAAA2B,EAAA,GAAZ3B,YAAY;AA+HzB,OAAO,MAAM4B,OAAO,GAAGA,CAAA,KAAM;EAAAC,GAAA;EAC3B,MAAMC,OAAO,GAAGxD,UAAU,CAACyB,WAAW,CAAC;EACvC,IAAI,CAAC+B,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,6CAA6C,CAAC;EAChE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,GAAA,CANWD,OAAO;AAAA,IAAAD,EAAA;AAAAK,YAAA,CAAAL,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}