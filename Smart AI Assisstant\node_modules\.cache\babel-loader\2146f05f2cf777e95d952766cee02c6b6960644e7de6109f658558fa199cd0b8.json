{"ast": null, "code": "var _jsxFileName = \"E:\\\\PythonProjects\\\\React.js\\\\Smart AI Assisstant\\\\src\\\\components\\\\UserProfile.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Box, Card, CardContent, Avatar, Typography, Button, TextField, IconButton, Chip, Divider, Grid, Dialog, DialogTitle, DialogContent, DialogActions, Alert, CircularProgress, Fade, Tooltip } from '@mui/material';\nimport { Edit as EditIcon, Save as SaveIcon, Cancel as CancelIcon, PhotoCamera as PhotoCameraIcon, Verified as VerifiedIcon, Email as EmailIcon, Person as PersonIcon, CalendarToday as CalendarIcon } from '@mui/icons-material';\nimport { useAuth } from '../contexts/AuthContext';\nimport authService from '../services/auth';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst UserProfile = () => {\n  _s();\n  const {\n    user,\n    updateUser\n  } = useAuth();\n  const [isEditing, setIsEditing] = useState(false);\n  const [editForm, setEditForm] = useState({\n    username: (user === null || user === void 0 ? void 0 : user.username) || '',\n    email: (user === null || user === void 0 ? void 0 : user.email) || '',\n    fullName: (user === null || user === void 0 ? void 0 : user.fullName) || ''\n  });\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n  const handleEditToggle = () => {\n    if (isEditing) {\n      // Cancel editing\n      setEditForm({\n        username: (user === null || user === void 0 ? void 0 : user.username) || '',\n        email: (user === null || user === void 0 ? void 0 : user.email) || '',\n        fullName: (user === null || user === void 0 ? void 0 : user.fullName) || ''\n      });\n      setError('');\n    }\n    setIsEditing(!isEditing);\n  };\n  const handleSave = async () => {\n    setIsLoading(true);\n    setError('');\n    setSuccess('');\n    try {\n      // Validate inputs\n      if (!editForm.username.trim()) {\n        setError('Username is required');\n        return;\n      }\n      if (!editForm.email.trim()) {\n        setError('Email is required');\n        return;\n      }\n      if (!authService.validateEmail(editForm.email)) {\n        setError('Please enter a valid email address');\n        return;\n      }\n\n      // Make API call to update profile\n      const response = await fetch(`${process.env.REACT_APP_API_BASE}/auth/profile`, {\n        method: 'PUT',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${authService.getToken()}`\n        },\n        body: JSON.stringify({\n          username: editForm.username,\n          email: editForm.email,\n          fullName: editForm.fullName\n        })\n      });\n      const data = await response.json();\n      if (!response.ok) {\n        throw new Error(data.error || 'Failed to update profile');\n      }\n      if (data.success) {\n        // Update local user data\n        updateUser(data.data.user);\n        setSuccess('Profile updated successfully!');\n        setIsEditing(false);\n\n        // Clear success message after 3 seconds\n        setTimeout(() => setSuccess(''), 3000);\n      } else {\n        setError(data.error || 'Failed to update profile');\n      }\n    } catch (error) {\n      console.error('Profile update error:', error);\n      setError(error.message || 'Failed to update profile. Please try again.');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  const getInitials = name => {\n    var _user$username;\n    if (!name) return (user === null || user === void 0 ? void 0 : (_user$username = user.username) === null || _user$username === void 0 ? void 0 : _user$username.charAt(0).toUpperCase()) || 'U';\n    return name.split(' ').map(n => n.charAt(0)).join('').toUpperCase().slice(0, 2);\n  };\n  const formatDate = dateString => {\n    if (!dateString) return 'Unknown';\n    return new Date(dateString).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  };\n  if (!user) {\n    return /*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        maxWidth: 600,\n        mx: 'auto'\n      },\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        sx: {\n          textAlign: 'center',\n          py: 4\n        },\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          color: \"text.secondary\",\n          children: \"Please log in to view your profile\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 139,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 138,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Fade, {\n      in: !!success,\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"success\",\n        sx: {\n          mb: 2\n        },\n        children: success\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 152,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 151,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Fade, {\n      in: !!error,\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"error\",\n        sx: {\n          mb: 2\n        },\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 158,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 157,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        maxWidth: 600,\n        mx: 'auto',\n        background: theme => theme.palette.mode === 'dark' ? 'linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%)' : 'linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%)',\n        border: '1px solid rgba(102, 126, 234, 0.2)',\n        borderRadius: 4,\n        overflow: 'hidden'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n          height: 120,\n          position: 'relative'\n        },\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            position: 'absolute',\n            top: 16,\n            right: 16\n          },\n          children: /*#__PURE__*/_jsxDEV(Tooltip, {\n            title: isEditing ? 'Cancel editing' : 'Edit profile',\n            children: /*#__PURE__*/_jsxDEV(IconButton, {\n              onClick: handleEditToggle,\n              disabled: isLoading,\n              sx: {\n                background: 'rgba(255, 255, 255, 0.2)',\n                color: 'white',\n                '&:hover': {\n                  background: 'rgba(255, 255, 255, 0.3)'\n                }\n              },\n              children: isEditing ? /*#__PURE__*/_jsxDEV(CancelIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 202,\n                columnNumber: 30\n              }, this) : /*#__PURE__*/_jsxDEV(EditIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 202,\n                columnNumber: 47\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 191,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 190,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 176,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(CardContent, {\n        sx: {\n          pt: 0,\n          pb: 4\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'center',\n            mb: 3,\n            mt: -6\n          },\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              position: 'relative'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Avatar, {\n              sx: {\n                width: 120,\n                height: 120,\n                fontSize: '2.5rem',\n                fontWeight: 'bold',\n                background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',\n                border: '4px solid white',\n                boxShadow: '0 8px 25px rgba(0, 0, 0, 0.15)'\n              },\n              children: getInitials(editForm.fullName || user.fullName)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 212,\n              columnNumber: 15\n            }, this), isEditing && /*#__PURE__*/_jsxDEV(IconButton, {\n              sx: {\n                position: 'absolute',\n                bottom: 0,\n                right: 0,\n                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                color: 'white',\n                width: 40,\n                height: 40,\n                '&:hover': {\n                  background: 'linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)'\n                }\n              },\n              children: /*#__PURE__*/_jsxDEV(PhotoCameraIcon, {\n                fontSize: \"small\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 241,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 227,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 211,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 210,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            textAlign: 'center',\n            mb: 4\n          },\n          children: isEditing ? /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              maxWidth: 400,\n              mx: 'auto'\n            },\n            children: [/*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Full Name\",\n              value: editForm.fullName,\n              onChange: e => setEditForm({\n                ...editForm,\n                fullName: e.target.value\n              }),\n              margin: \"normal\",\n              variant: \"outlined\",\n              sx: {\n                mb: 2\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 251,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Username\",\n              value: editForm.username,\n              onChange: e => setEditForm({\n                ...editForm,\n                username: e.target.value\n              }),\n              margin: \"normal\",\n              variant: \"outlined\",\n              sx: {\n                mb: 2\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 260,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Email\",\n              type: \"email\",\n              value: editForm.email,\n              onChange: e => setEditForm({\n                ...editForm,\n                email: e.target.value\n              }),\n              margin: \"normal\",\n              variant: \"outlined\",\n              sx: {\n                mb: 3\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 269,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                gap: 2,\n                justifyContent: 'center'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                variant: \"contained\",\n                startIcon: isLoading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n                  size: 20\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 283,\n                  columnNumber: 44\n                }, this) : /*#__PURE__*/_jsxDEV(SaveIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 283,\n                  columnNumber: 77\n                }, this),\n                onClick: handleSave,\n                disabled: isLoading,\n                sx: {\n                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                  '&:hover': {\n                    background: 'linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)'\n                  }\n                },\n                children: isLoading ? 'Saving...' : 'Save Changes'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 281,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"outlined\",\n                onClick: handleEditToggle,\n                disabled: isLoading,\n                children: \"Cancel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 295,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 280,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 250,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h4\",\n              fontWeight: \"bold\",\n              gutterBottom: true,\n              children: user.fullName || user.username\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 306,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              color: \"text.secondary\",\n              gutterBottom: true,\n              children: [\"@\", user.username]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 310,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                justifyContent: 'center',\n                gap: 1,\n                mb: 3\n              },\n              children: /*#__PURE__*/_jsxDEV(Chip, {\n                icon: /*#__PURE__*/_jsxDEV(EmailIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 316,\n                  columnNumber: 27\n                }, this),\n                label: user.emailVerified ? 'Verified' : 'Unverified',\n                color: user.emailVerified ? 'success' : 'warning',\n                size: \"small\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 315,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 314,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Divider, {\n              sx: {\n                my: 3\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 323,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              container: true,\n              spacing: 3,\n              sx: {\n                textAlign: 'left'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                sm: 6,\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    mb: 2\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(EmailIcon, {\n                    sx: {\n                      mr: 2,\n                      color: 'text.secondary'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 329,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      color: \"text.secondary\",\n                      children: \"Email\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 331,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body1\",\n                      fontWeight: \"500\",\n                      children: user.email\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 334,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 330,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 328,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 327,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                sm: 6,\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    mb: 2\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(PersonIcon, {\n                    sx: {\n                      mr: 2,\n                      color: 'text.secondary'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 343,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      color: \"text.secondary\",\n                      children: \"Username\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 345,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body1\",\n                      fontWeight: \"500\",\n                      children: user.username\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 348,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 344,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 342,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 341,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                sm: 6,\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    mb: 2\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(CalendarIcon, {\n                    sx: {\n                      mr: 2,\n                      color: 'text.secondary'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 357,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      color: \"text.secondary\",\n                      children: \"Member Since\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 359,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body1\",\n                      fontWeight: \"500\",\n                      children: formatDate(user.createdAt)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 362,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 358,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 356,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 355,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 326,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 248,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 208,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 163,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 149,\n    columnNumber: 5\n  }, this);\n};\n_s(UserProfile, \"MnRr3BCSTvaRfc1y42CZM5/+VX4=\", false, function () {\n  return [useAuth];\n});\n_c = UserProfile;\nexport default UserProfile;\nvar _c;\n$RefreshReg$(_c, \"UserProfile\");", "map": {"version": 3, "names": ["React", "useState", "Box", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Avatar", "Typography", "<PERSON><PERSON>", "TextField", "IconButton", "Chip", "Divider", "Grid", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "<PERSON><PERSON>", "CircularProgress", "Fade", "<PERSON><PERSON><PERSON>", "Edit", "EditIcon", "Save", "SaveIcon", "Cancel", "CancelIcon", "PhotoCamera", "PhotoCameraIcon", "Verified", "VerifiedIcon", "Email", "EmailIcon", "Person", "PersonIcon", "CalendarToday", "CalendarIcon", "useAuth", "authService", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "UserProfile", "_s", "user", "updateUser", "isEditing", "setIsEditing", "editForm", "setEditForm", "username", "email", "fullName", "isLoading", "setIsLoading", "error", "setError", "success", "setSuccess", "handleEditToggle", "handleSave", "trim", "validateEmail", "response", "fetch", "process", "env", "REACT_APP_API_BASE", "method", "headers", "getToken", "body", "JSON", "stringify", "data", "json", "ok", "Error", "setTimeout", "console", "message", "getInitials", "name", "_user$username", "char<PERSON>t", "toUpperCase", "split", "map", "n", "join", "slice", "formatDate", "dateString", "Date", "toLocaleDateString", "year", "month", "day", "sx", "max<PERSON><PERSON><PERSON>", "mx", "children", "textAlign", "py", "variant", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "in", "severity", "mb", "background", "theme", "palette", "mode", "border", "borderRadius", "overflow", "height", "position", "top", "right", "title", "onClick", "disabled", "pt", "pb", "display", "justifyContent", "mt", "width", "fontSize", "fontWeight", "boxShadow", "bottom", "fullWidth", "label", "value", "onChange", "e", "target", "margin", "type", "gap", "startIcon", "size", "gutterBottom", "icon", "emailVerified", "my", "container", "spacing", "item", "xs", "sm", "alignItems", "mr", "createdAt", "_c", "$RefreshReg$"], "sources": ["E:/PythonProjects/React.js/Smart AI Assisstant/src/components/UserProfile.jsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  <PERSON>,\n  Card,\n  CardContent,\n  Avatar,\n  Typography,\n  Button,\n  TextField,\n  IconButton,\n  Chip,\n  Divider,\n  Grid,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  Alert,\n  CircularProgress,\n  Fade,\n  Tooltip,\n} from '@mui/material';\nimport {\n  Edit as EditIcon,\n  Save as SaveIcon,\n  Cancel as CancelIcon,\n  PhotoCamera as PhotoCameraIcon,\n  Verified as VerifiedIcon,\n  Email as EmailIcon,\n  Person as PersonIcon,\n  CalendarToday as CalendarIcon,\n} from '@mui/icons-material';\nimport { useAuth } from '../contexts/AuthContext';\nimport authService from '../services/auth';\n\nconst UserProfile = () => {\n  const { user, updateUser } = useAuth();\n  const [isEditing, setIsEditing] = useState(false);\n  const [editForm, setEditForm] = useState({\n    username: user?.username || '',\n    email: user?.email || '',\n    fullName: user?.fullName || '',\n  });\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n\n  const handleEditToggle = () => {\n    if (isEditing) {\n      // Cancel editing\n      setEditForm({\n        username: user?.username || '',\n        email: user?.email || '',\n        fullName: user?.fullName || '',\n      });\n      setError('');\n    }\n    setIsEditing(!isEditing);\n  };\n\n  const handleSave = async () => {\n    setIsLoading(true);\n    setError('');\n    setSuccess('');\n\n    try {\n      // Validate inputs\n      if (!editForm.username.trim()) {\n        setError('Username is required');\n        return;\n      }\n\n      if (!editForm.email.trim()) {\n        setError('Email is required');\n        return;\n      }\n\n      if (!authService.validateEmail(editForm.email)) {\n        setError('Please enter a valid email address');\n        return;\n      }\n\n      // Make API call to update profile\n      const response = await fetch(`${process.env.REACT_APP_API_BASE}/auth/profile`, {\n        method: 'PUT',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${authService.getToken()}`,\n        },\n        body: JSON.stringify({\n          username: editForm.username,\n          email: editForm.email,\n          fullName: editForm.fullName,\n        }),\n      });\n\n      const data = await response.json();\n\n      if (!response.ok) {\n        throw new Error(data.error || 'Failed to update profile');\n      }\n\n      if (data.success) {\n        // Update local user data\n        updateUser(data.data.user);\n        setSuccess('Profile updated successfully!');\n        setIsEditing(false);\n\n        // Clear success message after 3 seconds\n        setTimeout(() => setSuccess(''), 3000);\n      } else {\n        setError(data.error || 'Failed to update profile');\n      }\n    } catch (error) {\n      console.error('Profile update error:', error);\n      setError(error.message || 'Failed to update profile. Please try again.');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const getInitials = (name) => {\n    if (!name) return user?.username?.charAt(0).toUpperCase() || 'U';\n    return name.split(' ').map(n => n.charAt(0)).join('').toUpperCase().slice(0, 2);\n  };\n\n  const formatDate = (dateString) => {\n    if (!dateString) return 'Unknown';\n    return new Date(dateString).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  };\n\n  if (!user) {\n    return (\n      <Card sx={{ maxWidth: 600, mx: 'auto' }}>\n        <CardContent sx={{ textAlign: 'center', py: 4 }}>\n          <Typography variant=\"h6\" color=\"text.secondary\">\n            Please log in to view your profile\n          </Typography>\n        </CardContent>\n      </Card>\n    );\n  }\n\n  return (\n    <Box>\n      {/* Success/Error Messages */}\n      <Fade in={!!success}>\n        <Alert severity=\"success\" sx={{ mb: 2 }}>\n          {success}\n        </Alert>\n      </Fade>\n      \n      <Fade in={!!error}>\n        <Alert severity=\"error\" sx={{ mb: 2 }}>\n          {error}\n        </Alert>\n      </Fade>\n\n      <Card \n        sx={{ \n          maxWidth: 600, \n          mx: 'auto',\n          background: (theme) => theme.palette.mode === 'dark'\n            ? 'linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%)'\n            : 'linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%)',\n          border: '1px solid rgba(102, 126, 234, 0.2)',\n          borderRadius: 4,\n          overflow: 'hidden',\n        }}\n      >\n        {/* Header with gradient */}\n        <Box\n          sx={{\n            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n            height: 120,\n            position: 'relative',\n          }}\n        >\n          <Box\n            sx={{\n              position: 'absolute',\n              top: 16,\n              right: 16,\n            }}\n          >\n            <Tooltip title={isEditing ? 'Cancel editing' : 'Edit profile'}>\n              <IconButton\n                onClick={handleEditToggle}\n                disabled={isLoading}\n                sx={{\n                  background: 'rgba(255, 255, 255, 0.2)',\n                  color: 'white',\n                  '&:hover': {\n                    background: 'rgba(255, 255, 255, 0.3)',\n                  },\n                }}\n              >\n                {isEditing ? <CancelIcon /> : <EditIcon />}\n              </IconButton>\n            </Tooltip>\n          </Box>\n        </Box>\n\n        <CardContent sx={{ pt: 0, pb: 4 }}>\n          {/* Avatar Section */}\n          <Box sx={{ display: 'flex', justifyContent: 'center', mb: 3, mt: -6 }}>\n            <Box sx={{ position: 'relative' }}>\n              <Avatar\n                sx={{\n                  width: 120,\n                  height: 120,\n                  fontSize: '2.5rem',\n                  fontWeight: 'bold',\n                  background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',\n                  border: '4px solid white',\n                  boxShadow: '0 8px 25px rgba(0, 0, 0, 0.15)',\n                }}\n              >\n                {getInitials(editForm.fullName || user.fullName)}\n              </Avatar>\n              \n              {isEditing && (\n                <IconButton\n                  sx={{\n                    position: 'absolute',\n                    bottom: 0,\n                    right: 0,\n                    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                    color: 'white',\n                    width: 40,\n                    height: 40,\n                    '&:hover': {\n                      background: 'linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)',\n                    },\n                  }}\n                >\n                  <PhotoCameraIcon fontSize=\"small\" />\n                </IconButton>\n              )}\n            </Box>\n          </Box>\n\n          {/* User Information */}\n          <Box sx={{ textAlign: 'center', mb: 4 }}>\n            {isEditing ? (\n              <Box sx={{ maxWidth: 400, mx: 'auto' }}>\n                <TextField\n                  fullWidth\n                  label=\"Full Name\"\n                  value={editForm.fullName}\n                  onChange={(e) => setEditForm({ ...editForm, fullName: e.target.value })}\n                  margin=\"normal\"\n                  variant=\"outlined\"\n                  sx={{ mb: 2 }}\n                />\n                <TextField\n                  fullWidth\n                  label=\"Username\"\n                  value={editForm.username}\n                  onChange={(e) => setEditForm({ ...editForm, username: e.target.value })}\n                  margin=\"normal\"\n                  variant=\"outlined\"\n                  sx={{ mb: 2 }}\n                />\n                <TextField\n                  fullWidth\n                  label=\"Email\"\n                  type=\"email\"\n                  value={editForm.email}\n                  onChange={(e) => setEditForm({ ...editForm, email: e.target.value })}\n                  margin=\"normal\"\n                  variant=\"outlined\"\n                  sx={{ mb: 3 }}\n                />\n                \n                <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center' }}>\n                  <Button\n                    variant=\"contained\"\n                    startIcon={isLoading ? <CircularProgress size={20} /> : <SaveIcon />}\n                    onClick={handleSave}\n                    disabled={isLoading}\n                    sx={{\n                      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                      '&:hover': {\n                        background: 'linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)',\n                      },\n                    }}\n                  >\n                    {isLoading ? 'Saving...' : 'Save Changes'}\n                  </Button>\n                  <Button\n                    variant=\"outlined\"\n                    onClick={handleEditToggle}\n                    disabled={isLoading}\n                  >\n                    Cancel\n                  </Button>\n                </Box>\n              </Box>\n            ) : (\n              <>\n                <Typography variant=\"h4\" fontWeight=\"bold\" gutterBottom>\n                  {user.fullName || user.username}\n                </Typography>\n                \n                <Typography variant=\"h6\" color=\"text.secondary\" gutterBottom>\n                  @{user.username}\n                </Typography>\n\n                <Box sx={{ display: 'flex', justifyContent: 'center', gap: 1, mb: 3 }}>\n                  <Chip\n                    icon={<EmailIcon />}\n                    label={user.emailVerified ? 'Verified' : 'Unverified'}\n                    color={user.emailVerified ? 'success' : 'warning'}\n                    size=\"small\"\n                  />\n                </Box>\n\n                <Divider sx={{ my: 3 }} />\n\n                {/* Profile Details */}\n                <Grid container spacing={3} sx={{ textAlign: 'left' }}>\n                  <Grid item xs={12} sm={6}>\n                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>\n                      <EmailIcon sx={{ mr: 2, color: 'text.secondary' }} />\n                      <Box>\n                        <Typography variant=\"body2\" color=\"text.secondary\">\n                          Email\n                        </Typography>\n                        <Typography variant=\"body1\" fontWeight=\"500\">\n                          {user.email}\n                        </Typography>\n                      </Box>\n                    </Box>\n                  </Grid>\n                  \n                  <Grid item xs={12} sm={6}>\n                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>\n                      <PersonIcon sx={{ mr: 2, color: 'text.secondary' }} />\n                      <Box>\n                        <Typography variant=\"body2\" color=\"text.secondary\">\n                          Username\n                        </Typography>\n                        <Typography variant=\"body1\" fontWeight=\"500\">\n                          {user.username}\n                        </Typography>\n                      </Box>\n                    </Box>\n                  </Grid>\n                  \n                  <Grid item xs={12} sm={6}>\n                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>\n                      <CalendarIcon sx={{ mr: 2, color: 'text.secondary' }} />\n                      <Box>\n                        <Typography variant=\"body2\" color=\"text.secondary\">\n                          Member Since\n                        </Typography>\n                        <Typography variant=\"body1\" fontWeight=\"500\">\n                          {formatDate(user.createdAt)}\n                        </Typography>\n                      </Box>\n                    </Box>\n                  </Grid>\n                </Grid>\n              </>\n            )}\n          </Box>\n        </CardContent>\n      </Card>\n    </Box>\n  );\n};\n\nexport default UserProfile;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,GAAG,EACHC,IAAI,EACJC,WAAW,EACXC,MAAM,EACNC,UAAU,EACVC,MAAM,EACNC,SAAS,EACTC,UAAU,EACVC,IAAI,EACJC,OAAO,EACPC,IAAI,EACJC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,KAAK,EACLC,gBAAgB,EAChBC,IAAI,EACJC,OAAO,QACF,eAAe;AACtB,SACEC,IAAI,IAAIC,QAAQ,EAChBC,IAAI,IAAIC,QAAQ,EAChBC,MAAM,IAAIC,UAAU,EACpBC,WAAW,IAAIC,eAAe,EAC9BC,QAAQ,IAAIC,YAAY,EACxBC,KAAK,IAAIC,SAAS,EAClBC,MAAM,IAAIC,UAAU,EACpBC,aAAa,IAAIC,YAAY,QACxB,qBAAqB;AAC5B,SAASC,OAAO,QAAQ,yBAAyB;AACjD,OAAOC,WAAW,MAAM,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE3C,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxB,MAAM;IAAEC,IAAI;IAAEC;EAAW,CAAC,GAAGT,OAAO,CAAC,CAAC;EACtC,MAAM,CAACU,SAAS,EAAEC,YAAY,CAAC,GAAG/C,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACgD,QAAQ,EAAEC,WAAW,CAAC,GAAGjD,QAAQ,CAAC;IACvCkD,QAAQ,EAAE,CAAAN,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEM,QAAQ,KAAI,EAAE;IAC9BC,KAAK,EAAE,CAAAP,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEO,KAAK,KAAI,EAAE;IACxBC,QAAQ,EAAE,CAAAR,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEQ,QAAQ,KAAI;EAC9B,CAAC,CAAC;EACF,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGtD,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACuD,KAAK,EAAEC,QAAQ,CAAC,GAAGxD,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACyD,OAAO,EAAEC,UAAU,CAAC,GAAG1D,QAAQ,CAAC,EAAE,CAAC;EAE1C,MAAM2D,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAIb,SAAS,EAAE;MACb;MACAG,WAAW,CAAC;QACVC,QAAQ,EAAE,CAAAN,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEM,QAAQ,KAAI,EAAE;QAC9BC,KAAK,EAAE,CAAAP,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEO,KAAK,KAAI,EAAE;QACxBC,QAAQ,EAAE,CAAAR,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEQ,QAAQ,KAAI;MAC9B,CAAC,CAAC;MACFI,QAAQ,CAAC,EAAE,CAAC;IACd;IACAT,YAAY,CAAC,CAACD,SAAS,CAAC;EAC1B,CAAC;EAED,MAAMc,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7BN,YAAY,CAAC,IAAI,CAAC;IAClBE,QAAQ,CAAC,EAAE,CAAC;IACZE,UAAU,CAAC,EAAE,CAAC;IAEd,IAAI;MACF;MACA,IAAI,CAACV,QAAQ,CAACE,QAAQ,CAACW,IAAI,CAAC,CAAC,EAAE;QAC7BL,QAAQ,CAAC,sBAAsB,CAAC;QAChC;MACF;MAEA,IAAI,CAACR,QAAQ,CAACG,KAAK,CAACU,IAAI,CAAC,CAAC,EAAE;QAC1BL,QAAQ,CAAC,mBAAmB,CAAC;QAC7B;MACF;MAEA,IAAI,CAACnB,WAAW,CAACyB,aAAa,CAACd,QAAQ,CAACG,KAAK,CAAC,EAAE;QAC9CK,QAAQ,CAAC,oCAAoC,CAAC;QAC9C;MACF;;MAEA;MACA,MAAMO,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGC,OAAO,CAACC,GAAG,CAACC,kBAAkB,eAAe,EAAE;QAC7EC,MAAM,EAAE,KAAK;QACbC,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB;UAClC,eAAe,EAAE,UAAUhC,WAAW,CAACiC,QAAQ,CAAC,CAAC;QACnD,CAAC;QACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UACnBvB,QAAQ,EAAEF,QAAQ,CAACE,QAAQ;UAC3BC,KAAK,EAAEH,QAAQ,CAACG,KAAK;UACrBC,QAAQ,EAAEJ,QAAQ,CAACI;QACrB,CAAC;MACH,CAAC,CAAC;MAEF,MAAMsB,IAAI,GAAG,MAAMX,QAAQ,CAACY,IAAI,CAAC,CAAC;MAElC,IAAI,CAACZ,QAAQ,CAACa,EAAE,EAAE;QAChB,MAAM,IAAIC,KAAK,CAACH,IAAI,CAACnB,KAAK,IAAI,0BAA0B,CAAC;MAC3D;MAEA,IAAImB,IAAI,CAACjB,OAAO,EAAE;QAChB;QACAZ,UAAU,CAAC6B,IAAI,CAACA,IAAI,CAAC9B,IAAI,CAAC;QAC1Bc,UAAU,CAAC,+BAA+B,CAAC;QAC3CX,YAAY,CAAC,KAAK,CAAC;;QAEnB;QACA+B,UAAU,CAAC,MAAMpB,UAAU,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;MACxC,CAAC,MAAM;QACLF,QAAQ,CAACkB,IAAI,CAACnB,KAAK,IAAI,0BAA0B,CAAC;MACpD;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdwB,OAAO,CAACxB,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7CC,QAAQ,CAACD,KAAK,CAACyB,OAAO,IAAI,6CAA6C,CAAC;IAC1E,CAAC,SAAS;MACR1B,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAM2B,WAAW,GAAIC,IAAI,IAAK;IAAA,IAAAC,cAAA;IAC5B,IAAI,CAACD,IAAI,EAAE,OAAO,CAAAtC,IAAI,aAAJA,IAAI,wBAAAuC,cAAA,GAAJvC,IAAI,CAAEM,QAAQ,cAAAiC,cAAA,uBAAdA,cAAA,CAAgBC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,KAAI,GAAG;IAChE,OAAOH,IAAI,CAACI,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACJ,MAAM,CAAC,CAAC,CAAC,CAAC,CAACK,IAAI,CAAC,EAAE,CAAC,CAACJ,WAAW,CAAC,CAAC,CAACK,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;EACjF,CAAC;EAED,MAAMC,UAAU,GAAIC,UAAU,IAAK;IACjC,IAAI,CAACA,UAAU,EAAE,OAAO,SAAS;IACjC,OAAO,IAAIC,IAAI,CAACD,UAAU,CAAC,CAACE,kBAAkB,CAAC,OAAO,EAAE;MACtDC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,MAAM;MACbC,GAAG,EAAE;IACP,CAAC,CAAC;EACJ,CAAC;EAED,IAAI,CAACrD,IAAI,EAAE;IACT,oBACEL,OAAA,CAACrC,IAAI;MAACgG,EAAE,EAAE;QAAEC,QAAQ,EAAE,GAAG;QAAEC,EAAE,EAAE;MAAO,CAAE;MAAAC,QAAA,eACtC9D,OAAA,CAACpC,WAAW;QAAC+F,EAAE,EAAE;UAAEI,SAAS,EAAE,QAAQ;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAF,QAAA,eAC9C9D,OAAA,CAAClC,UAAU;UAACmG,OAAO,EAAC,IAAI;UAACC,KAAK,EAAC,gBAAgB;UAAAJ,QAAA,EAAC;QAEhD;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAEX;EAEA,oBACEtE,OAAA,CAACtC,GAAG;IAAAoG,QAAA,gBAEF9D,OAAA,CAACrB,IAAI;MAAC4F,EAAE,EAAE,CAAC,CAACrD,OAAQ;MAAA4C,QAAA,eAClB9D,OAAA,CAACvB,KAAK;QAAC+F,QAAQ,EAAC,SAAS;QAACb,EAAE,EAAE;UAAEc,EAAE,EAAE;QAAE,CAAE;QAAAX,QAAA,EACrC5C;MAAO;QAAAiD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAEPtE,OAAA,CAACrB,IAAI;MAAC4F,EAAE,EAAE,CAAC,CAACvD,KAAM;MAAA8C,QAAA,eAChB9D,OAAA,CAACvB,KAAK;QAAC+F,QAAQ,EAAC,OAAO;QAACb,EAAE,EAAE;UAAEc,EAAE,EAAE;QAAE,CAAE;QAAAX,QAAA,EACnC9C;MAAK;QAAAmD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAEPtE,OAAA,CAACrC,IAAI;MACHgG,EAAE,EAAE;QACFC,QAAQ,EAAE,GAAG;QACbC,EAAE,EAAE,MAAM;QACVa,UAAU,EAAGC,KAAK,IAAKA,KAAK,CAACC,OAAO,CAACC,IAAI,KAAK,MAAM,GAChD,oFAAoF,GACpF,sFAAsF;QAC1FC,MAAM,EAAE,oCAAoC;QAC5CC,YAAY,EAAE,CAAC;QACfC,QAAQ,EAAE;MACZ,CAAE;MAAAlB,QAAA,gBAGF9D,OAAA,CAACtC,GAAG;QACFiG,EAAE,EAAE;UACFe,UAAU,EAAE,mDAAmD;UAC/DO,MAAM,EAAE,GAAG;UACXC,QAAQ,EAAE;QACZ,CAAE;QAAApB,QAAA,eAEF9D,OAAA,CAACtC,GAAG;UACFiG,EAAE,EAAE;YACFuB,QAAQ,EAAE,UAAU;YACpBC,GAAG,EAAE,EAAE;YACPC,KAAK,EAAE;UACT,CAAE;UAAAtB,QAAA,eAEF9D,OAAA,CAACpB,OAAO;YAACyG,KAAK,EAAE9E,SAAS,GAAG,gBAAgB,GAAG,cAAe;YAAAuD,QAAA,eAC5D9D,OAAA,CAAC/B,UAAU;cACTqH,OAAO,EAAElE,gBAAiB;cAC1BmE,QAAQ,EAAEzE,SAAU;cACpB6C,EAAE,EAAE;gBACFe,UAAU,EAAE,0BAA0B;gBACtCR,KAAK,EAAE,OAAO;gBACd,SAAS,EAAE;kBACTQ,UAAU,EAAE;gBACd;cACF,CAAE;cAAAZ,QAAA,EAEDvD,SAAS,gBAAGP,OAAA,CAACd,UAAU;gBAAAiF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAAGtE,OAAA,CAAClB,QAAQ;gBAAAqF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENtE,OAAA,CAACpC,WAAW;QAAC+F,EAAE,EAAE;UAAE6B,EAAE,EAAE,CAAC;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAA3B,QAAA,gBAEhC9D,OAAA,CAACtC,GAAG;UAACiG,EAAE,EAAE;YAAE+B,OAAO,EAAE,MAAM;YAAEC,cAAc,EAAE,QAAQ;YAAElB,EAAE,EAAE,CAAC;YAAEmB,EAAE,EAAE,CAAC;UAAE,CAAE;UAAA9B,QAAA,eACpE9D,OAAA,CAACtC,GAAG;YAACiG,EAAE,EAAE;cAAEuB,QAAQ,EAAE;YAAW,CAAE;YAAApB,QAAA,gBAChC9D,OAAA,CAACnC,MAAM;cACL8F,EAAE,EAAE;gBACFkC,KAAK,EAAE,GAAG;gBACVZ,MAAM,EAAE,GAAG;gBACXa,QAAQ,EAAE,QAAQ;gBAClBC,UAAU,EAAE,MAAM;gBAClBrB,UAAU,EAAE,mDAAmD;gBAC/DI,MAAM,EAAE,iBAAiB;gBACzBkB,SAAS,EAAE;cACb,CAAE;cAAAlC,QAAA,EAEDpB,WAAW,CAACjC,QAAQ,CAACI,QAAQ,IAAIR,IAAI,CAACQ,QAAQ;YAAC;cAAAsD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C,CAAC,EAER/D,SAAS,iBACRP,OAAA,CAAC/B,UAAU;cACT0F,EAAE,EAAE;gBACFuB,QAAQ,EAAE,UAAU;gBACpBe,MAAM,EAAE,CAAC;gBACTb,KAAK,EAAE,CAAC;gBACRV,UAAU,EAAE,mDAAmD;gBAC/DR,KAAK,EAAE,OAAO;gBACd2B,KAAK,EAAE,EAAE;gBACTZ,MAAM,EAAE,EAAE;gBACV,SAAS,EAAE;kBACTP,UAAU,EAAE;gBACd;cACF,CAAE;cAAAZ,QAAA,eAEF9D,OAAA,CAACZ,eAAe;gBAAC0G,QAAQ,EAAC;cAAO;gBAAA3B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1B,CACb;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNtE,OAAA,CAACtC,GAAG;UAACiG,EAAE,EAAE;YAAEI,SAAS,EAAE,QAAQ;YAAEU,EAAE,EAAE;UAAE,CAAE;UAAAX,QAAA,EACrCvD,SAAS,gBACRP,OAAA,CAACtC,GAAG;YAACiG,EAAE,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEC,EAAE,EAAE;YAAO,CAAE;YAAAC,QAAA,gBACrC9D,OAAA,CAAChC,SAAS;cACRkI,SAAS;cACTC,KAAK,EAAC,WAAW;cACjBC,KAAK,EAAE3F,QAAQ,CAACI,QAAS;cACzBwF,QAAQ,EAAGC,CAAC,IAAK5F,WAAW,CAAC;gBAAE,GAAGD,QAAQ;gBAAEI,QAAQ,EAAEyF,CAAC,CAACC,MAAM,CAACH;cAAM,CAAC,CAAE;cACxEI,MAAM,EAAC,QAAQ;cACfvC,OAAO,EAAC,UAAU;cAClBN,EAAE,EAAE;gBAAEc,EAAE,EAAE;cAAE;YAAE;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC,eACFtE,OAAA,CAAChC,SAAS;cACRkI,SAAS;cACTC,KAAK,EAAC,UAAU;cAChBC,KAAK,EAAE3F,QAAQ,CAACE,QAAS;cACzB0F,QAAQ,EAAGC,CAAC,IAAK5F,WAAW,CAAC;gBAAE,GAAGD,QAAQ;gBAAEE,QAAQ,EAAE2F,CAAC,CAACC,MAAM,CAACH;cAAM,CAAC,CAAE;cACxEI,MAAM,EAAC,QAAQ;cACfvC,OAAO,EAAC,UAAU;cAClBN,EAAE,EAAE;gBAAEc,EAAE,EAAE;cAAE;YAAE;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC,eACFtE,OAAA,CAAChC,SAAS;cACRkI,SAAS;cACTC,KAAK,EAAC,OAAO;cACbM,IAAI,EAAC,OAAO;cACZL,KAAK,EAAE3F,QAAQ,CAACG,KAAM;cACtByF,QAAQ,EAAGC,CAAC,IAAK5F,WAAW,CAAC;gBAAE,GAAGD,QAAQ;gBAAEG,KAAK,EAAE0F,CAAC,CAACC,MAAM,CAACH;cAAM,CAAC,CAAE;cACrEI,MAAM,EAAC,QAAQ;cACfvC,OAAO,EAAC,UAAU;cAClBN,EAAE,EAAE;gBAAEc,EAAE,EAAE;cAAE;YAAE;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC,eAEFtE,OAAA,CAACtC,GAAG;cAACiG,EAAE,EAAE;gBAAE+B,OAAO,EAAE,MAAM;gBAAEgB,GAAG,EAAE,CAAC;gBAAEf,cAAc,EAAE;cAAS,CAAE;cAAA7B,QAAA,gBAC7D9D,OAAA,CAACjC,MAAM;gBACLkG,OAAO,EAAC,WAAW;gBACnB0C,SAAS,EAAE7F,SAAS,gBAAGd,OAAA,CAACtB,gBAAgB;kBAACkI,IAAI,EAAE;gBAAG;kBAAAzC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAAGtE,OAAA,CAAChB,QAAQ;kBAAAmF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACrEgB,OAAO,EAAEjE,UAAW;gBACpBkE,QAAQ,EAAEzE,SAAU;gBACpB6C,EAAE,EAAE;kBACFe,UAAU,EAAE,mDAAmD;kBAC/D,SAAS,EAAE;oBACTA,UAAU,EAAE;kBACd;gBACF,CAAE;gBAAAZ,QAAA,EAEDhD,SAAS,GAAG,WAAW,GAAG;cAAc;gBAAAqD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC,CAAC,eACTtE,OAAA,CAACjC,MAAM;gBACLkG,OAAO,EAAC,UAAU;gBAClBqB,OAAO,EAAElE,gBAAiB;gBAC1BmE,QAAQ,EAAEzE,SAAU;gBAAAgD,QAAA,EACrB;cAED;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,gBAENtE,OAAA,CAAAE,SAAA;YAAA4D,QAAA,gBACE9D,OAAA,CAAClC,UAAU;cAACmG,OAAO,EAAC,IAAI;cAAC8B,UAAU,EAAC,MAAM;cAACc,YAAY;cAAA/C,QAAA,EACpDzD,IAAI,CAACQ,QAAQ,IAAIR,IAAI,CAACM;YAAQ;cAAAwD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB,CAAC,eAEbtE,OAAA,CAAClC,UAAU;cAACmG,OAAO,EAAC,IAAI;cAACC,KAAK,EAAC,gBAAgB;cAAC2C,YAAY;cAAA/C,QAAA,GAAC,GAC1D,EAACzD,IAAI,CAACM,QAAQ;YAAA;cAAAwD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eAEbtE,OAAA,CAACtC,GAAG;cAACiG,EAAE,EAAE;gBAAE+B,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE,QAAQ;gBAAEe,GAAG,EAAE,CAAC;gBAAEjC,EAAE,EAAE;cAAE,CAAE;cAAAX,QAAA,eACpE9D,OAAA,CAAC9B,IAAI;gBACH4I,IAAI,eAAE9G,OAAA,CAACR,SAAS;kBAAA2E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACpB6B,KAAK,EAAE9F,IAAI,CAAC0G,aAAa,GAAG,UAAU,GAAG,YAAa;gBACtD7C,KAAK,EAAE7D,IAAI,CAAC0G,aAAa,GAAG,SAAS,GAAG,SAAU;gBAClDH,IAAI,EAAC;cAAO;gBAAAzC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENtE,OAAA,CAAC7B,OAAO;cAACwF,EAAE,EAAE;gBAAEqD,EAAE,EAAE;cAAE;YAAE;cAAA7C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAG1BtE,OAAA,CAAC5B,IAAI;cAAC6I,SAAS;cAACC,OAAO,EAAE,CAAE;cAACvD,EAAE,EAAE;gBAAEI,SAAS,EAAE;cAAO,CAAE;cAAAD,QAAA,gBACpD9D,OAAA,CAAC5B,IAAI;gBAAC+I,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAACC,EAAE,EAAE,CAAE;gBAAAvD,QAAA,eACvB9D,OAAA,CAACtC,GAAG;kBAACiG,EAAE,EAAE;oBAAE+B,OAAO,EAAE,MAAM;oBAAE4B,UAAU,EAAE,QAAQ;oBAAE7C,EAAE,EAAE;kBAAE,CAAE;kBAAAX,QAAA,gBACxD9D,OAAA,CAACR,SAAS;oBAACmE,EAAE,EAAE;sBAAE4D,EAAE,EAAE,CAAC;sBAAErD,KAAK,EAAE;oBAAiB;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACrDtE,OAAA,CAACtC,GAAG;oBAAAoG,QAAA,gBACF9D,OAAA,CAAClC,UAAU;sBAACmG,OAAO,EAAC,OAAO;sBAACC,KAAK,EAAC,gBAAgB;sBAAAJ,QAAA,EAAC;oBAEnD;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACbtE,OAAA,CAAClC,UAAU;sBAACmG,OAAO,EAAC,OAAO;sBAAC8B,UAAU,EAAC,KAAK;sBAAAjC,QAAA,EACzCzD,IAAI,CAACO;oBAAK;sBAAAuD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eAEPtE,OAAA,CAAC5B,IAAI;gBAAC+I,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAACC,EAAE,EAAE,CAAE;gBAAAvD,QAAA,eACvB9D,OAAA,CAACtC,GAAG;kBAACiG,EAAE,EAAE;oBAAE+B,OAAO,EAAE,MAAM;oBAAE4B,UAAU,EAAE,QAAQ;oBAAE7C,EAAE,EAAE;kBAAE,CAAE;kBAAAX,QAAA,gBACxD9D,OAAA,CAACN,UAAU;oBAACiE,EAAE,EAAE;sBAAE4D,EAAE,EAAE,CAAC;sBAAErD,KAAK,EAAE;oBAAiB;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACtDtE,OAAA,CAACtC,GAAG;oBAAAoG,QAAA,gBACF9D,OAAA,CAAClC,UAAU;sBAACmG,OAAO,EAAC,OAAO;sBAACC,KAAK,EAAC,gBAAgB;sBAAAJ,QAAA,EAAC;oBAEnD;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACbtE,OAAA,CAAClC,UAAU;sBAACmG,OAAO,EAAC,OAAO;sBAAC8B,UAAU,EAAC,KAAK;sBAAAjC,QAAA,EACzCzD,IAAI,CAACM;oBAAQ;sBAAAwD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eAEPtE,OAAA,CAAC5B,IAAI;gBAAC+I,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAACC,EAAE,EAAE,CAAE;gBAAAvD,QAAA,eACvB9D,OAAA,CAACtC,GAAG;kBAACiG,EAAE,EAAE;oBAAE+B,OAAO,EAAE,MAAM;oBAAE4B,UAAU,EAAE,QAAQ;oBAAE7C,EAAE,EAAE;kBAAE,CAAE;kBAAAX,QAAA,gBACxD9D,OAAA,CAACJ,YAAY;oBAAC+D,EAAE,EAAE;sBAAE4D,EAAE,EAAE,CAAC;sBAAErD,KAAK,EAAE;oBAAiB;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACxDtE,OAAA,CAACtC,GAAG;oBAAAoG,QAAA,gBACF9D,OAAA,CAAClC,UAAU;sBAACmG,OAAO,EAAC,OAAO;sBAACC,KAAK,EAAC,gBAAgB;sBAAAJ,QAAA,EAAC;oBAEnD;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACbtE,OAAA,CAAClC,UAAU;sBAACmG,OAAO,EAAC,OAAO;sBAAC8B,UAAU,EAAC,KAAK;sBAAAjC,QAAA,EACzCV,UAAU,CAAC/C,IAAI,CAACmH,SAAS;oBAAC;sBAAArD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjB,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA,eACP;QACH;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAAClE,EAAA,CApVID,WAAW;EAAA,QACcN,OAAO;AAAA;AAAA4H,EAAA,GADhCtH,WAAW;AAsVjB,eAAeA,WAAW;AAAC,IAAAsH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}