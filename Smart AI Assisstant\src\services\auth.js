import axios from 'axios';

// Create axios instance for auth API
const authAPI = axios.create({
  baseURL: process.env.REACT_APP_API_BASE || 'http://localhost:5000/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Token management
const TOKEN_KEY = 'ai_security_guard_token';
const USER_KEY = 'ai_security_guard_user';

/**
 * Authentication Service
 * Handles all authentication-related operations
 */
class AuthService {
  constructor() {
    this.token = localStorage.getItem(TOKEN_KEY);
    this.user = this.getStoredUser();
    this.setupInterceptors();
  }

  /**
   * Setup axios interceptors for automatic token attachment
   */
  setupInterceptors() {
    // Request interceptor to add token
    authAPI.interceptors.request.use(
      (config) => {
        const token = this.getToken();
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => Promise.reject(error)
    );

    // Response interceptor to handle token expiration
    authAPI.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.response?.status === 401 && this.isAuthenticated()) {
          console.log('Token expired, clearing auth data');
          this.clearAuthData();
          // Don't redirect automatically, let the app handle it
        }
        return Promise.reject(error);
      }
    );
  }

  /**
   * Get stored user from localStorage
   */
  getStoredUser() {
    try {
      const userStr = localStorage.getItem(USER_KEY);
      return userStr ? JSON.parse(userStr) : null;
    } catch (error) {
      console.error('Error parsing stored user:', error);
      localStorage.removeItem(USER_KEY);
      return null;
    }
  }

  /**
   * Store token and user data
   */
  storeAuthData(token, user) {
    localStorage.setItem(TOKEN_KEY, token);
    localStorage.setItem(USER_KEY, JSON.stringify(user));
    this.token = token;
    this.user = user;
  }

  /**
   * Clear stored auth data
   */
  clearAuthData() {
    localStorage.removeItem(TOKEN_KEY);
    localStorage.removeItem(USER_KEY);
    this.token = null;
    this.user = null;
  }

  /**
   * Get current token
   */
  getToken() {
    return this.token || localStorage.getItem(TOKEN_KEY);
  }

  /**
   * Get current user
   */
  getCurrentUser() {
    return this.user || this.getStoredUser();
  }

  /**
   * Check if user is authenticated
   */
  isAuthenticated() {
    return !!this.getToken() && !!this.getCurrentUser();
  }

  /**
   * Register new user
   */
  async signup(userData) {
    try {
      const response = await authAPI.post('/auth/signup', userData);
      
      if (response.data.success) {
        const { user, token } = response.data.data;
        this.storeAuthData(token, user);
        return { success: true, user, token };
      }
      
      return { success: false, error: response.data.error };
    } catch (error) {
      console.error('Signup error:', error);
      return {
        success: false,
        error: error.response?.data?.error || 'Registration failed. Please try again.',
        details: error.response?.data?.details,
      };
    }
  }

  /**
   * Login user
   */
  async login(credentials) {
    try {
      const response = await authAPI.post('/auth/login', credentials);
      
      if (response.data.success) {
        const { user, token } = response.data.data;
        this.storeAuthData(token, user);
        return { success: true, user, token };
      }
      
      return { success: false, error: response.data.error };
    } catch (error) {
      console.error('Login error:', error);
      return {
        success: false,
        error: error.response?.data?.error || 'Login failed. Please try again.',
      };
    }
  }

  /**
   * Logout user
   */
  async logout() {
    const wasAuthenticated = this.isAuthenticated();

    // Clear local data first
    this.clearAuthData();

    // Then try to notify server (optional)
    if (wasAuthenticated) {
      try {
        await authAPI.post('/auth/logout');
      } catch (error) {
        console.error('Server logout error (non-critical):', error);
        // Don't throw error since local logout already succeeded
      }
    }
  }

  /**
   * Get current user profile from server
   */
  async getProfile() {
    try {
      const response = await authAPI.get('/auth/me');
      
      if (response.data.success) {
        const user = response.data.data.user;
        // Update stored user data
        localStorage.setItem(USER_KEY, JSON.stringify(user));
        this.user = user;
        return { success: true, user };
      }
      
      return { success: false, error: response.data.error };
    } catch (error) {
      console.error('Get profile error:', error);
      return {
        success: false,
        error: error.response?.data?.error || 'Failed to get profile.',
      };
    }
  }

  /**
   * Validate password strength
   */
  validatePassword(password) {
    const minLength = 8;
    const hasUpperCase = /[A-Z]/.test(password);
    const hasLowerCase = /[a-z]/.test(password);
    const hasNumbers = /\d/.test(password);
    const hasSpecialChar = /[@$!%*?&]/.test(password);

    const errors = [];
    
    if (password.length < minLength) {
      errors.push(`Password must be at least ${minLength} characters long`);
    }
    if (!hasUpperCase) {
      errors.push('Password must contain at least one uppercase letter');
    }
    if (!hasLowerCase) {
      errors.push('Password must contain at least one lowercase letter');
    }
    if (!hasNumbers) {
      errors.push('Password must contain at least one number');
    }
    if (!hasSpecialChar) {
      errors.push('Password must contain at least one special character (@$!%*?&)');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  /**
   * Validate email format
   */
  validateEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }
}

// Create and export singleton instance
const authService = new AuthService();
export default authService;
