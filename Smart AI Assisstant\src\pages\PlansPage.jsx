import React, { useState } from 'react';
import {
  Container,
  Box,
  Typography,
  Card,
  CardContent,
  Button,
  Grid,
  Chip,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Switch,
  Avatar,
} from '@mui/material';
import {
  Check as CheckIcon,
  Star as StarIcon,
  Security as SecurityIcon,
  Speed as SpeedIcon,
  Cloud as CloudIcon,
  Support as SupportIcon,
  Analytics as AnalyticsIcon,
  Shield as ShieldIcon,
} from '@mui/icons-material';

const PlansPage = React.memo(() => {
  const [isAnnual, setIsAnnual] = useState(false);

  const plans = [
    {
      name: 'Starter',
      description: 'Perfect for individuals and small projects',
      price: { monthly: 0, annual: 0 },
      gradient: 'linear-gradient(135deg, #4caf50 0%, #8bc34a 100%)',
      glowColor: 'rgba(76, 175, 80, 0.3)',
      icon: <SecurityIcon />,
      features: [
        '100 scans per month',
        'Basic threat detection',
        'Email support',
        'Standard reports',
        'Community access',
      ],
      popular: false,
    },
    {
      name: 'Professional',
      description: 'Advanced security for growing businesses',
      price: { monthly: 29, annual: 290 },
      gradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      glowColor: 'rgba(102, 126, 234, 0.4)',
      icon: <ShieldIcon />,
      features: [
        'Unlimited scans',
        'AI-powered threat prediction',
        'Real-time monitoring',
        'Advanced analytics',
        'Priority support',
        'Custom reports',
        'API access',
      ],
      popular: true,
    },
    {
      name: 'Enterprise',
      description: 'Complete security solution for large organizations',
      price: { monthly: 99, annual: 990 },
      gradient: 'linear-gradient(135deg, #9c27b0 0%, #e91e63 100%)',
      glowColor: 'rgba(156, 39, 176, 0.4)',
      icon: <CloudIcon />,
      features: [
        'Everything in Professional',
        'White-label solution',
        'Custom integrations',
        'Dedicated support',
        'SLA guarantees',
        'Advanced compliance',
        'Multi-tenant architecture',
        'Custom training',
      ],
      popular: false,
    },
  ];

  return (
    <Box
      sx={{
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 50%, #121212 100%)',
        position: 'relative',
        py: 8,
        '&::before': {
          content: '""',
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundImage: `
            radial-gradient(circle at 20% 20%, rgba(102, 126, 234, 0.08) 0%, transparent 50%),
            radial-gradient(circle at 80% 80%, rgba(156, 39, 176, 0.08) 0%, transparent 50%),
            radial-gradient(circle at 50% 10%, rgba(76, 175, 80, 0.06) 0%, transparent 40%)
          `,
          zIndex: -1,
        },
      }}
    >
      <Container maxWidth="xl" sx={{ px: { xs: 2, sm: 3, md: 4 } }}>
        {/* Hero Section */}
        <Box textAlign="center" mb={8}>
          <Typography 
            variant="h1" 
            component="h1" 
            gutterBottom 
            fontWeight="bold"
            sx={{
              background: 'linear-gradient(45deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%)',
              backgroundSize: '400% 400%',
              backgroundClip: 'text',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              animation: 'gradientText 8s ease infinite',
              fontSize: { xs: '2.5rem', md: '4rem' },
              '@keyframes gradientText': {
                '0%': { backgroundPosition: '0% 50%' },
                '50%': { backgroundPosition: '100% 50%' },
                '100%': { backgroundPosition: '0% 50%' },
              },
            }}
          >
            Choose Your Plan
          </Typography>
          
          <Typography 
            variant="h4" 
            color="rgba(255, 255, 255, 0.8)" 
            maxWidth="800px" 
            mx="auto"
            mb={6}
            sx={{ fontSize: { xs: '1.2rem', md: '1.5rem' } }}
          >
            Advanced cybersecurity solutions for every need
          </Typography>

          {/* Billing Toggle */}
          <Box 
            display="flex" 
            alignItems="center" 
            justifyContent="center" 
            gap={2}
            mb={6}
            sx={{
              background: 'rgba(255, 255, 255, 0.05)',
              backdropFilter: 'blur(10px)',
              border: '1px solid rgba(255, 255, 255, 0.1)',
              borderRadius: 3,
              p: 2,
              maxWidth: 300,
              mx: 'auto',
            }}
          >
            <Typography color="white" fontWeight={!isAnnual ? 'bold' : 'normal'}>
              Monthly
            </Typography>
            <Switch
              checked={isAnnual}
              onChange={(e) => setIsAnnual(e.target.checked)}
              sx={{
                '& .MuiSwitch-thumb': {
                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                },
              }}
            />
            <Typography color="white" fontWeight={isAnnual ? 'bold' : 'normal'}>
              Annual
            </Typography>
            {isAnnual && (
              <Chip 
                label="Save 20%" 
                size="small"
                sx={{
                  background: 'linear-gradient(135deg, #4caf50 0%, #8bc34a 100%)',
                  color: 'white',
                  fontWeight: 'bold',
                }}
              />
            )}
          </Box>
        </Box>

        {/* Plans Grid */}
        <Grid
          container
          spacing={2}
          justifyContent="center"
          alignItems="stretch"
          sx={{
            maxWidth: '100%',
            margin: '0 auto',
            px: { xs: 1, sm: 2, md: 2, lg: 3 },
            '@media (min-width: 900px)': {
              flexWrap: 'nowrap',
              '& .MuiGrid-item': {
                flexBasis: '33.333333%',
                maxWidth: '33.333333%',
                flexShrink: 0,
              }
            },
            '@media (min-width: 1200px)': {
              px: 4,
              '& .MuiGrid-item .MuiCard-root': {
                maxWidth: '320px',
              }
            }
          }}
        >
          {plans.map((plan, index) => (
            <Grid
              item
              xs={12}
              sm={12}
              md={4}
              key={plan.name}
              sx={{
                display: 'flex',
                justifyContent: 'center',
                px: { xs: 1, md: 0.5 },
                '@media (min-width: 900px)': {
                  flexBasis: '33.333333%',
                  maxWidth: '33.333333%',
                  flexShrink: 0,
                }
              }}
            >
              <Card
                sx={{
                  height: '100%',
                  minHeight: '500px',
                  maxWidth: { xs: '350px', md: '280px', lg: '300px' },
                  width: '100%',
                  margin: '0 auto',
                  background: 'linear-gradient(135deg, rgba(12, 12, 12, 0.95) 0%, rgba(26, 26, 26, 0.95) 50%, rgba(45, 45, 45, 0.95) 100%)',
                  backdropFilter: 'blur(25px) saturate(180%)',
                  border: plan.popular
                    ? '2px solid rgba(102, 126, 234, 0.6)'
                    : '1px solid rgba(255, 255, 255, 0.1)',
                  borderRadius: 4,
                  position: 'relative',
                  overflow: 'hidden',
                  boxShadow: plan.popular
                    ? `0 20px 60px ${plan.glowColor}`
                    : '0 8px 32px rgba(0, 0, 0, 0.3)',
                  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                  transform: plan.popular ? 'scale(1.05)' : 'scale(1)',
                  '&:hover': {
                    transform: plan.popular ? 'scale(1.08)' : 'scale(1.03)',
                    boxShadow: `0 30px 80px ${plan.glowColor}`,
                  },
                  '&::before': {
                    content: '""',
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    right: 0,
                    height: '4px',
                    background: plan.gradient,
                    backgroundSize: '200% 200%',
                    animation: plan.popular ? 'gradientShift 3s ease infinite' : 'none',
                  },
                  '@keyframes gradientShift': {
                    '0%': { backgroundPosition: '0% 50%' },
                    '50%': { backgroundPosition: '100% 50%' },
                    '100%': { backgroundPosition: '0% 50%' },
                  },
                }}
              >
                {plan.popular && (
                  <Box
                    sx={{
                      position: 'absolute',
                      top: 20,
                      right: 20,
                      background: 'linear-gradient(135deg, #ff006e 0%, #8338ec 100%)',
                      color: 'white',
                      px: 2,
                      py: 0.5,
                      borderRadius: 2,
                      fontSize: '0.875rem',
                      fontWeight: 'bold',
                      zIndex: 2,
                    }}
                  >
                    MOST POPULAR
                  </Box>
                )}

                <CardContent sx={{ p: 4, height: '100%', display: 'flex', flexDirection: 'column' }}>
                  {/* Plan Header */}
                  <Box textAlign="center" mb={4}>
                    <Avatar
                      sx={{
                        background: plan.gradient,
                        width: 64,
                        height: 64,
                        mx: 'auto',
                        mb: 2,
                        boxShadow: `0 8px 25px ${plan.glowColor}`,
                      }}
                    >
                      {React.cloneElement(plan.icon, { sx: { fontSize: 32 } })}
                    </Avatar>
                    
                    <Typography variant="h4" fontWeight="bold" color="white" gutterBottom>
                      {plan.name}
                    </Typography>
                    
                    <Typography variant="body1" color="rgba(255, 255, 255, 0.7)" mb={3}>
                      {plan.description}
                    </Typography>

                    {/* Price */}
                    <Box>
                      <Typography 
                        variant="h2" 
                        fontWeight="bold" 
                        color="white"
                        sx={{ 
                          background: plan.gradient,
                          backgroundClip: 'text',
                          WebkitBackgroundClip: 'text',
                          WebkitTextFillColor: 'transparent',
                        }}
                      >
                        ${isAnnual ? plan.price.annual : plan.price.monthly}
                        {plan.price.monthly > 0 && (
                          <Typography component="span" variant="h6" color="rgba(255, 255, 255, 0.6)">
                            /{isAnnual ? 'year' : 'month'}
                          </Typography>
                        )}
                      </Typography>
                      {plan.price.monthly === 0 && (
                        <Typography variant="h6" color="rgba(255, 255, 255, 0.8)">
                          Free Forever
                        </Typography>
                      )}
                    </Box>
                  </Box>

                  {/* Features */}
                  <List sx={{ flex: 1 }}>
                    {plan.features.map((feature, featureIndex) => (
                      <ListItem key={featureIndex} sx={{ px: 0, py: 0.5 }}>
                        <ListItemIcon sx={{ minWidth: 32 }}>
                          <CheckIcon sx={{ color: '#4caf50', fontSize: 20 }} />
                        </ListItemIcon>
                        <ListItemText 
                          primary={feature}
                          sx={{ 
                            '& .MuiListItemText-primary': { 
                              color: 'rgba(255, 255, 255, 0.9)',
                              fontSize: '0.95rem',
                            }
                          }}
                        />
                      </ListItem>
                    ))}
                  </List>

                  {/* CTA Button */}
                  <Button
                    variant="contained"
                    fullWidth
                    size="large"
                    sx={{
                      mt: 4,
                      py: 2,
                      background: plan.gradient,
                      boxShadow: `0 8px 25px ${plan.glowColor}`,
                      fontWeight: 'bold',
                      fontSize: '1.1rem',
                      '&:hover': {
                        background: plan.gradient,
                        filter: 'brightness(1.1)',
                        transform: 'translateY(-2px)',
                        boxShadow: `0 12px 35px ${plan.glowColor}`,
                      },
                    }}
                  >
                    {plan.price.monthly === 0 ? 'Get Started Free' : 'Choose Plan'}
                  </Button>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      </Container>
    </Box>
  );
});

PlansPage.displayName = 'PlansPage';

export default PlansPage;
