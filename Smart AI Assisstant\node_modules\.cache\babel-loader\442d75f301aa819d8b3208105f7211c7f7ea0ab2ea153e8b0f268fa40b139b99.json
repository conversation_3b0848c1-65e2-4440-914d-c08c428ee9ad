{"ast": null, "code": "var _jsxFileName = \"E:\\\\PythonProjects\\\\React.js\\\\Smart AI Assisstant\\\\src\\\\components\\\\UserProfile.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef } from 'react';\nimport { Box, Card, CardContent, Avatar, Typography, Button, TextField, IconButton, Chip, Divider, Grid, Dialog, DialogTitle, DialogContent, DialogActions, Alert, CircularProgress, Fade, Tooltip, Badge } from '@mui/material';\nimport { Edit as EditIcon, Save as SaveIcon, Cancel as CancelIcon, PhotoCamera as PhotoCameraIcon, Verified as VerifiedIcon, Email as EmailIcon, Person as PersonIcon, CalendarToday as CalendarIcon, Delete as DeleteIcon } from '@mui/icons-material';\nimport { useAuth } from '../contexts/AuthContext';\nimport authService from '../services/auth';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst UserProfile = () => {\n  _s();\n  const {\n    user,\n    updateUser\n  } = useAuth();\n  const [isEditing, setIsEditing] = useState(false);\n  const [editForm, setEditForm] = useState({\n    username: (user === null || user === void 0 ? void 0 : user.username) || '',\n    email: (user === null || user === void 0 ? void 0 : user.email) || '',\n    fullName: (user === null || user === void 0 ? void 0 : user.fullName) || ''\n  });\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n  const [profileImage, setProfileImage] = useState((user === null || user === void 0 ? void 0 : user.profileImage) || null);\n  const [imagePreview, setImagePreview] = useState(null);\n  const [isUploadingImage, setIsUploadingImage] = useState(false);\n  const fileInputRef = useRef(null);\n  const handleEditToggle = () => {\n    if (isEditing) {\n      // Cancel editing\n      setEditForm({\n        username: (user === null || user === void 0 ? void 0 : user.username) || '',\n        email: (user === null || user === void 0 ? void 0 : user.email) || '',\n        fullName: (user === null || user === void 0 ? void 0 : user.fullName) || ''\n      });\n      setError('');\n    }\n    setIsEditing(!isEditing);\n  };\n  const handleSave = async () => {\n    setIsLoading(true);\n    setError('');\n    setSuccess('');\n    try {\n      // Validate inputs\n      if (!editForm.username.trim()) {\n        setError('Username is required');\n        return;\n      }\n      if (!editForm.email.trim()) {\n        setError('Email is required');\n        return;\n      }\n      if (!authService.validateEmail(editForm.email)) {\n        setError('Please enter a valid email address');\n        return;\n      }\n\n      // Make API call to update profile\n      const response = await fetch(`${process.env.REACT_APP_API_BASE}/auth/profile`, {\n        method: 'PUT',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${authService.getToken()}`\n        },\n        body: JSON.stringify({\n          username: editForm.username,\n          email: editForm.email,\n          fullName: editForm.fullName\n        })\n      });\n      const data = await response.json();\n      if (!response.ok) {\n        throw new Error(data.error || 'Failed to update profile');\n      }\n      if (data.success) {\n        // Update local user data\n        updateUser(data.data.user);\n        setSuccess('Profile updated successfully!');\n        setIsEditing(false);\n\n        // Clear success message after 3 seconds\n        setTimeout(() => setSuccess(''), 3000);\n      } else {\n        setError(data.error || 'Failed to update profile');\n      }\n    } catch (error) {\n      console.error('Profile update error:', error);\n      setError(error.message || 'Failed to update profile. Please try again.');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  const getInitials = name => {\n    var _user$username;\n    if (!name) return (user === null || user === void 0 ? void 0 : (_user$username = user.username) === null || _user$username === void 0 ? void 0 : _user$username.charAt(0).toUpperCase()) || 'U';\n    return name.split(' ').map(n => n.charAt(0)).join('').toUpperCase().slice(0, 2);\n  };\n  const formatDate = dateString => {\n    if (!dateString) return 'Unknown';\n    return new Date(dateString).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  };\n  const handleImageUpload = event => {\n    const file = event.target.files[0];\n    if (!file) return;\n\n    // Validate file type\n    if (!file.type.startsWith('image/')) {\n      setError('Please select a valid image file');\n      return;\n    }\n\n    // Validate file size (max 5MB)\n    if (file.size > 5 * 1024 * 1024) {\n      setError('Image size must be less than 5MB');\n      return;\n    }\n\n    // Create preview\n    const reader = new FileReader();\n    reader.onload = e => {\n      setImagePreview(e.target.result);\n    };\n    reader.readAsDataURL(file);\n\n    // Upload image\n    uploadProfileImage(file);\n  };\n  const uploadProfileImage = async file => {\n    setIsUploadingImage(true);\n    setError('');\n    try {\n      const formData = new FormData();\n      formData.append('profileImage', file);\n      const response = await fetch(`${process.env.REACT_APP_API_BASE}/auth/profile/image`, {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${authService.getToken()}`\n        },\n        body: formData\n      });\n      const data = await response.json();\n      if (!response.ok) {\n        throw new Error(data.error || 'Failed to upload image');\n      }\n      if (data.success) {\n        setProfileImage(data.data.imageUrl);\n        updateUser({\n          ...user,\n          profileImage: data.data.imageUrl\n        });\n        setSuccess('Profile image updated successfully!');\n        setTimeout(() => setSuccess(''), 3000);\n      } else {\n        setError(data.error || 'Failed to upload image');\n      }\n    } catch (error) {\n      console.error('Image upload error:', error);\n      setError(error.message || 'Failed to upload image. Please try again.');\n    } finally {\n      setIsUploadingImage(false);\n    }\n  };\n  const handleRemoveImage = async () => {\n    setIsUploadingImage(true);\n    setError('');\n    try {\n      const response = await fetch(`${process.env.REACT_APP_API_BASE}/auth/profile/image`, {\n        method: 'DELETE',\n        headers: {\n          'Authorization': `Bearer ${authService.getToken()}`\n        }\n      });\n      const data = await response.json();\n      if (!response.ok) {\n        throw new Error(data.error || 'Failed to remove image');\n      }\n      if (data.success) {\n        setProfileImage(null);\n        setImagePreview(null);\n        updateUser({\n          ...user,\n          profileImage: null\n        });\n        setSuccess('Profile image removed successfully!');\n        setTimeout(() => setSuccess(''), 3000);\n      } else {\n        setError(data.error || 'Failed to remove image');\n      }\n    } catch (error) {\n      console.error('Image removal error:', error);\n      setError(error.message || 'Failed to remove image. Please try again.');\n    } finally {\n      setIsUploadingImage(false);\n    }\n  };\n  if (!user) {\n    return /*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        maxWidth: 600,\n        mx: 'auto'\n      },\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        sx: {\n          textAlign: 'center',\n          py: 4\n        },\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          color: \"text.secondary\",\n          children: \"Please log in to view your profile\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 246,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 245,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 244,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Fade, {\n      in: !!success,\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"success\",\n        sx: {\n          mb: 2\n        },\n        children: success\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 258,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 257,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Fade, {\n      in: !!error,\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"error\",\n        sx: {\n          mb: 2\n        },\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 264,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 263,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        maxWidth: 600,\n        mx: 'auto',\n        background: theme => theme.palette.mode === 'dark' ? 'linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%)' : 'linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%)',\n        border: '1px solid rgba(102, 126, 234, 0.2)',\n        borderRadius: 4,\n        overflow: 'hidden'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n          height: 120,\n          position: 'relative'\n        },\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            position: 'absolute',\n            top: 16,\n            right: 16\n          },\n          children: /*#__PURE__*/_jsxDEV(Tooltip, {\n            title: isEditing ? 'Cancel editing' : 'Edit profile',\n            children: /*#__PURE__*/_jsxDEV(IconButton, {\n              onClick: handleEditToggle,\n              disabled: isLoading,\n              sx: {\n                background: 'rgba(255, 255, 255, 0.2)',\n                color: 'white',\n                '&:hover': {\n                  background: 'rgba(255, 255, 255, 0.3)'\n                }\n              },\n              children: isEditing ? /*#__PURE__*/_jsxDEV(CancelIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 308,\n                columnNumber: 30\n              }, this) : /*#__PURE__*/_jsxDEV(EditIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 308,\n                columnNumber: 47\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 297,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 296,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 289,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 282,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(CardContent, {\n        sx: {\n          pt: 0,\n          pb: 4\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'center',\n            mb: 3,\n            mt: -6\n          },\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              position: 'relative'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Badge, {\n              overlap: \"circular\",\n              anchorOrigin: {\n                vertical: 'bottom',\n                horizontal: 'right'\n              },\n              badgeContent: isEditing && /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  flexDirection: 'column',\n                  gap: 1\n                },\n                children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n                  title: \"Upload new image\",\n                  children: /*#__PURE__*/_jsxDEV(IconButton, {\n                    size: \"small\",\n                    onClick: () => {\n                      var _fileInputRef$current;\n                      return (_fileInputRef$current = fileInputRef.current) === null || _fileInputRef$current === void 0 ? void 0 : _fileInputRef$current.click();\n                    },\n                    disabled: isUploadingImage,\n                    sx: {\n                      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                      color: 'white',\n                      width: 40,\n                      height: 40,\n                      '&:hover': {\n                        background: 'linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)'\n                      }\n                    },\n                    children: isUploadingImage ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n                      size: 16,\n                      color: \"inherit\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 340,\n                      columnNumber: 29\n                    }, this) : /*#__PURE__*/_jsxDEV(PhotoCameraIcon, {\n                      fontSize: \"small\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 342,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 325,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 324,\n                  columnNumber: 23\n                }, this), (profileImage || imagePreview) && /*#__PURE__*/_jsxDEV(Tooltip, {\n                  title: \"Remove image\",\n                  children: /*#__PURE__*/_jsxDEV(IconButton, {\n                    size: \"small\",\n                    onClick: handleRemoveImage,\n                    disabled: isUploadingImage,\n                    sx: {\n                      background: 'linear-gradient(135deg, #f44336 0%, #ff5722 100%)',\n                      color: 'white',\n                      width: 40,\n                      height: 40,\n                      mt: 1,\n                      '&:hover': {\n                        background: 'linear-gradient(135deg, #d32f2f 0%, #f57c00 100%)'\n                      }\n                    },\n                    children: /*#__PURE__*/_jsxDEV(DeleteIcon, {\n                      fontSize: \"small\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 363,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 348,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 347,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 323,\n                columnNumber: 21\n              }, this),\n              children: /*#__PURE__*/_jsxDEV(Avatar, {\n                src: imagePreview || profileImage || (user === null || user === void 0 ? void 0 : user.profileImage),\n                sx: {\n                  width: 120,\n                  height: 120,\n                  fontSize: '2.5rem',\n                  fontWeight: 'bold',\n                  background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',\n                  border: '4px solid white',\n                  boxShadow: '0 8px 25px rgba(0, 0, 0, 0.15)'\n                },\n                children: !(imagePreview || profileImage || user !== null && user !== void 0 && user.profileImage) && getInitials(editForm.fullName || user.fullName)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 371,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 318,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              ref: fileInputRef,\n              type: \"file\",\n              accept: \"image/*\",\n              onChange: handleImageUpload,\n              style: {\n                display: 'none'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 390,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 317,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 316,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            textAlign: 'center',\n            mb: 4\n          },\n          children: isEditing ? /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              maxWidth: 400,\n              mx: 'auto'\n            },\n            children: [/*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Full Name\",\n              value: editForm.fullName,\n              onChange: e => setEditForm({\n                ...editForm,\n                fullName: e.target.value\n              }),\n              margin: \"normal\",\n              variant: \"outlined\",\n              sx: {\n                mb: 2\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 404,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Username\",\n              value: editForm.username,\n              onChange: e => setEditForm({\n                ...editForm,\n                username: e.target.value\n              }),\n              margin: \"normal\",\n              variant: \"outlined\",\n              sx: {\n                mb: 2\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 413,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Email\",\n              type: \"email\",\n              value: editForm.email,\n              onChange: e => setEditForm({\n                ...editForm,\n                email: e.target.value\n              }),\n              margin: \"normal\",\n              variant: \"outlined\",\n              sx: {\n                mb: 3\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 422,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                gap: 2,\n                justifyContent: 'center'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                variant: \"contained\",\n                startIcon: isLoading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n                  size: 20\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 436,\n                  columnNumber: 44\n                }, this) : /*#__PURE__*/_jsxDEV(SaveIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 436,\n                  columnNumber: 77\n                }, this),\n                onClick: handleSave,\n                disabled: isLoading,\n                sx: {\n                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                  '&:hover': {\n                    background: 'linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)'\n                  }\n                },\n                children: isLoading ? 'Saving...' : 'Save Changes'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 434,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"outlined\",\n                onClick: handleEditToggle,\n                disabled: isLoading,\n                children: \"Cancel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 448,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 433,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 403,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h4\",\n              fontWeight: \"bold\",\n              gutterBottom: true,\n              children: user.fullName || user.username\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 459,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              color: \"text.secondary\",\n              gutterBottom: true,\n              children: [\"@\", user.username]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 463,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                justifyContent: 'center',\n                gap: 1,\n                mb: 3\n              },\n              children: /*#__PURE__*/_jsxDEV(Chip, {\n                icon: /*#__PURE__*/_jsxDEV(EmailIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 469,\n                  columnNumber: 27\n                }, this),\n                label: user.emailVerified ? 'Verified' : 'Unverified',\n                color: user.emailVerified ? 'success' : 'warning',\n                size: \"small\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 468,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 467,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Divider, {\n              sx: {\n                my: 3\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 476,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              container: true,\n              spacing: 3,\n              sx: {\n                textAlign: 'left'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                sm: 6,\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    mb: 2\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(EmailIcon, {\n                    sx: {\n                      mr: 2,\n                      color: 'text.secondary'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 482,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      color: \"text.secondary\",\n                      children: \"Email\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 484,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body1\",\n                      fontWeight: \"500\",\n                      children: user.email\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 487,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 483,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 481,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 480,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                sm: 6,\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    mb: 2\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(PersonIcon, {\n                    sx: {\n                      mr: 2,\n                      color: 'text.secondary'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 496,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      color: \"text.secondary\",\n                      children: \"Username\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 498,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body1\",\n                      fontWeight: \"500\",\n                      children: user.username\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 501,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 497,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 495,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 494,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                sm: 6,\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    mb: 2\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(CalendarIcon, {\n                    sx: {\n                      mr: 2,\n                      color: 'text.secondary'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 510,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      color: \"text.secondary\",\n                      children: \"Member Since\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 512,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body1\",\n                      fontWeight: \"500\",\n                      children: formatDate(user.createdAt)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 515,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 511,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 509,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 508,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 479,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 401,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 314,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 269,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 255,\n    columnNumber: 5\n  }, this);\n};\n_s(UserProfile, \"GmrP8C6JkydwlwUOvkyfHF17S/A=\", false, function () {\n  return [useAuth];\n});\n_c = UserProfile;\nexport default UserProfile;\nvar _c;\n$RefreshReg$(_c, \"UserProfile\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "Box", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Avatar", "Typography", "<PERSON><PERSON>", "TextField", "IconButton", "Chip", "Divider", "Grid", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "<PERSON><PERSON>", "CircularProgress", "Fade", "<PERSON><PERSON><PERSON>", "Badge", "Edit", "EditIcon", "Save", "SaveIcon", "Cancel", "CancelIcon", "PhotoCamera", "PhotoCameraIcon", "Verified", "VerifiedIcon", "Email", "EmailIcon", "Person", "PersonIcon", "CalendarToday", "CalendarIcon", "Delete", "DeleteIcon", "useAuth", "authService", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "UserProfile", "_s", "user", "updateUser", "isEditing", "setIsEditing", "editForm", "setEditForm", "username", "email", "fullName", "isLoading", "setIsLoading", "error", "setError", "success", "setSuccess", "profileImage", "setProfileImage", "imagePreview", "setImagePreview", "isUploadingImage", "setIsUploadingImage", "fileInputRef", "handleEditToggle", "handleSave", "trim", "validateEmail", "response", "fetch", "process", "env", "REACT_APP_API_BASE", "method", "headers", "getToken", "body", "JSON", "stringify", "data", "json", "ok", "Error", "setTimeout", "console", "message", "getInitials", "name", "_user$username", "char<PERSON>t", "toUpperCase", "split", "map", "n", "join", "slice", "formatDate", "dateString", "Date", "toLocaleDateString", "year", "month", "day", "handleImageUpload", "event", "file", "target", "files", "type", "startsWith", "size", "reader", "FileReader", "onload", "e", "result", "readAsDataURL", "uploadProfileImage", "formData", "FormData", "append", "imageUrl", "handleRemoveImage", "sx", "max<PERSON><PERSON><PERSON>", "mx", "children", "textAlign", "py", "variant", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "in", "severity", "mb", "background", "theme", "palette", "mode", "border", "borderRadius", "overflow", "height", "position", "top", "right", "title", "onClick", "disabled", "pt", "pb", "display", "justifyContent", "mt", "overlap", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "badgeContent", "flexDirection", "gap", "_fileInputRef$current", "current", "click", "width", "fontSize", "src", "fontWeight", "boxShadow", "ref", "accept", "onChange", "style", "fullWidth", "label", "value", "margin", "startIcon", "gutterBottom", "icon", "emailVerified", "my", "container", "spacing", "item", "xs", "sm", "alignItems", "mr", "createdAt", "_c", "$RefreshReg$"], "sources": ["E:/PythonProjects/React.js/Smart AI Assisstant/src/components/UserProfile.jsx"], "sourcesContent": ["import React, { useState, useRef } from 'react';\nimport {\n  <PERSON>,\n  Card,\n  CardContent,\n  Avatar,\n  <PERSON><PERSON><PERSON>,\n  <PERSON>ton,\n  TextField,\n  IconButton,\n  Chip,\n  Divider,\n  Grid,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  Alert,\n  CircularProgress,\n  Fade,\n  Tooltip,\n  Badge,\n} from '@mui/material';\nimport {\n  Edit as EditIcon,\n  Save as SaveIcon,\n  Cancel as CancelIcon,\n  PhotoCamera as PhotoCameraIcon,\n  Verified as VerifiedIcon,\n  Email as EmailIcon,\n  Person as PersonIcon,\n  CalendarToday as CalendarIcon,\n  Delete as DeleteIcon,\n} from '@mui/icons-material';\nimport { useAuth } from '../contexts/AuthContext';\nimport authService from '../services/auth';\n\nconst UserProfile = () => {\n  const { user, updateUser } = useAuth();\n  const [isEditing, setIsEditing] = useState(false);\n  const [editForm, setEditForm] = useState({\n    username: user?.username || '',\n    email: user?.email || '',\n    fullName: user?.fullName || '',\n  });\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n  const [profileImage, setProfileImage] = useState(user?.profileImage || null);\n  const [imagePreview, setImagePreview] = useState(null);\n  const [isUploadingImage, setIsUploadingImage] = useState(false);\n  const fileInputRef = useRef(null);\n\n  const handleEditToggle = () => {\n    if (isEditing) {\n      // Cancel editing\n      setEditForm({\n        username: user?.username || '',\n        email: user?.email || '',\n        fullName: user?.fullName || '',\n      });\n      setError('');\n    }\n    setIsEditing(!isEditing);\n  };\n\n  const handleSave = async () => {\n    setIsLoading(true);\n    setError('');\n    setSuccess('');\n\n    try {\n      // Validate inputs\n      if (!editForm.username.trim()) {\n        setError('Username is required');\n        return;\n      }\n\n      if (!editForm.email.trim()) {\n        setError('Email is required');\n        return;\n      }\n\n      if (!authService.validateEmail(editForm.email)) {\n        setError('Please enter a valid email address');\n        return;\n      }\n\n      // Make API call to update profile\n      const response = await fetch(`${process.env.REACT_APP_API_BASE}/auth/profile`, {\n        method: 'PUT',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${authService.getToken()}`,\n        },\n        body: JSON.stringify({\n          username: editForm.username,\n          email: editForm.email,\n          fullName: editForm.fullName,\n        }),\n      });\n\n      const data = await response.json();\n\n      if (!response.ok) {\n        throw new Error(data.error || 'Failed to update profile');\n      }\n\n      if (data.success) {\n        // Update local user data\n        updateUser(data.data.user);\n        setSuccess('Profile updated successfully!');\n        setIsEditing(false);\n\n        // Clear success message after 3 seconds\n        setTimeout(() => setSuccess(''), 3000);\n      } else {\n        setError(data.error || 'Failed to update profile');\n      }\n    } catch (error) {\n      console.error('Profile update error:', error);\n      setError(error.message || 'Failed to update profile. Please try again.');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const getInitials = (name) => {\n    if (!name) return user?.username?.charAt(0).toUpperCase() || 'U';\n    return name.split(' ').map(n => n.charAt(0)).join('').toUpperCase().slice(0, 2);\n  };\n\n  const formatDate = (dateString) => {\n    if (!dateString) return 'Unknown';\n    return new Date(dateString).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  };\n\n  const handleImageUpload = (event) => {\n    const file = event.target.files[0];\n    if (!file) return;\n\n    // Validate file type\n    if (!file.type.startsWith('image/')) {\n      setError('Please select a valid image file');\n      return;\n    }\n\n    // Validate file size (max 5MB)\n    if (file.size > 5 * 1024 * 1024) {\n      setError('Image size must be less than 5MB');\n      return;\n    }\n\n    // Create preview\n    const reader = new FileReader();\n    reader.onload = (e) => {\n      setImagePreview(e.target.result);\n    };\n    reader.readAsDataURL(file);\n\n    // Upload image\n    uploadProfileImage(file);\n  };\n\n  const uploadProfileImage = async (file) => {\n    setIsUploadingImage(true);\n    setError('');\n\n    try {\n      const formData = new FormData();\n      formData.append('profileImage', file);\n\n      const response = await fetch(`${process.env.REACT_APP_API_BASE}/auth/profile/image`, {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${authService.getToken()}`,\n        },\n        body: formData,\n      });\n\n      const data = await response.json();\n\n      if (!response.ok) {\n        throw new Error(data.error || 'Failed to upload image');\n      }\n\n      if (data.success) {\n        setProfileImage(data.data.imageUrl);\n        updateUser({ ...user, profileImage: data.data.imageUrl });\n        setSuccess('Profile image updated successfully!');\n        setTimeout(() => setSuccess(''), 3000);\n      } else {\n        setError(data.error || 'Failed to upload image');\n      }\n    } catch (error) {\n      console.error('Image upload error:', error);\n      setError(error.message || 'Failed to upload image. Please try again.');\n    } finally {\n      setIsUploadingImage(false);\n    }\n  };\n\n  const handleRemoveImage = async () => {\n    setIsUploadingImage(true);\n    setError('');\n\n    try {\n      const response = await fetch(`${process.env.REACT_APP_API_BASE}/auth/profile/image`, {\n        method: 'DELETE',\n        headers: {\n          'Authorization': `Bearer ${authService.getToken()}`,\n        },\n      });\n\n      const data = await response.json();\n\n      if (!response.ok) {\n        throw new Error(data.error || 'Failed to remove image');\n      }\n\n      if (data.success) {\n        setProfileImage(null);\n        setImagePreview(null);\n        updateUser({ ...user, profileImage: null });\n        setSuccess('Profile image removed successfully!');\n        setTimeout(() => setSuccess(''), 3000);\n      } else {\n        setError(data.error || 'Failed to remove image');\n      }\n    } catch (error) {\n      console.error('Image removal error:', error);\n      setError(error.message || 'Failed to remove image. Please try again.');\n    } finally {\n      setIsUploadingImage(false);\n    }\n  };\n\n  if (!user) {\n    return (\n      <Card sx={{ maxWidth: 600, mx: 'auto' }}>\n        <CardContent sx={{ textAlign: 'center', py: 4 }}>\n          <Typography variant=\"h6\" color=\"text.secondary\">\n            Please log in to view your profile\n          </Typography>\n        </CardContent>\n      </Card>\n    );\n  }\n\n  return (\n    <Box>\n      {/* Success/Error Messages */}\n      <Fade in={!!success}>\n        <Alert severity=\"success\" sx={{ mb: 2 }}>\n          {success}\n        </Alert>\n      </Fade>\n      \n      <Fade in={!!error}>\n        <Alert severity=\"error\" sx={{ mb: 2 }}>\n          {error}\n        </Alert>\n      </Fade>\n\n      <Card \n        sx={{ \n          maxWidth: 600, \n          mx: 'auto',\n          background: (theme) => theme.palette.mode === 'dark'\n            ? 'linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%)'\n            : 'linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%)',\n          border: '1px solid rgba(102, 126, 234, 0.2)',\n          borderRadius: 4,\n          overflow: 'hidden',\n        }}\n      >\n        {/* Header with gradient */}\n        <Box\n          sx={{\n            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n            height: 120,\n            position: 'relative',\n          }}\n        >\n          <Box\n            sx={{\n              position: 'absolute',\n              top: 16,\n              right: 16,\n            }}\n          >\n            <Tooltip title={isEditing ? 'Cancel editing' : 'Edit profile'}>\n              <IconButton\n                onClick={handleEditToggle}\n                disabled={isLoading}\n                sx={{\n                  background: 'rgba(255, 255, 255, 0.2)',\n                  color: 'white',\n                  '&:hover': {\n                    background: 'rgba(255, 255, 255, 0.3)',\n                  },\n                }}\n              >\n                {isEditing ? <CancelIcon /> : <EditIcon />}\n              </IconButton>\n            </Tooltip>\n          </Box>\n        </Box>\n\n        <CardContent sx={{ pt: 0, pb: 4 }}>\n          {/* Avatar Section */}\n          <Box sx={{ display: 'flex', justifyContent: 'center', mb: 3, mt: -6 }}>\n            <Box sx={{ position: 'relative' }}>\n              <Badge\n                overlap=\"circular\"\n                anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}\n                badgeContent={\n                  isEditing && (\n                    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>\n                      <Tooltip title=\"Upload new image\">\n                        <IconButton\n                          size=\"small\"\n                          onClick={() => fileInputRef.current?.click()}\n                          disabled={isUploadingImage}\n                          sx={{\n                            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                            color: 'white',\n                            width: 40,\n                            height: 40,\n                            '&:hover': {\n                              background: 'linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)',\n                            },\n                          }}\n                        >\n                          {isUploadingImage ? (\n                            <CircularProgress size={16} color=\"inherit\" />\n                          ) : (\n                            <PhotoCameraIcon fontSize=\"small\" />\n                          )}\n                        </IconButton>\n                      </Tooltip>\n                      {(profileImage || imagePreview) && (\n                        <Tooltip title=\"Remove image\">\n                          <IconButton\n                            size=\"small\"\n                            onClick={handleRemoveImage}\n                            disabled={isUploadingImage}\n                            sx={{\n                              background: 'linear-gradient(135deg, #f44336 0%, #ff5722 100%)',\n                              color: 'white',\n                              width: 40,\n                              height: 40,\n                              mt: 1,\n                              '&:hover': {\n                                background: 'linear-gradient(135deg, #d32f2f 0%, #f57c00 100%)',\n                              },\n                            }}\n                          >\n                            <DeleteIcon fontSize=\"small\" />\n                          </IconButton>\n                        </Tooltip>\n                      )}\n                    </Box>\n                  )\n                }\n              >\n                <Avatar\n                  src={imagePreview || profileImage || user?.profileImage}\n                  sx={{\n                    width: 120,\n                    height: 120,\n                    fontSize: '2.5rem',\n                    fontWeight: 'bold',\n                    background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',\n                    border: '4px solid white',\n                    boxShadow: '0 8px 25px rgba(0, 0, 0, 0.15)',\n                  }}\n                >\n                  {!(imagePreview || profileImage || user?.profileImage) &&\n                    getInitials(editForm.fullName || user.fullName)\n                  }\n                </Avatar>\n              </Badge>\n\n              {/* Hidden file input */}\n              <input\n                ref={fileInputRef}\n                type=\"file\"\n                accept=\"image/*\"\n                onChange={handleImageUpload}\n                style={{ display: 'none' }}\n              />\n            </Box>\n          </Box>\n\n          {/* User Information */}\n          <Box sx={{ textAlign: 'center', mb: 4 }}>\n            {isEditing ? (\n              <Box sx={{ maxWidth: 400, mx: 'auto' }}>\n                <TextField\n                  fullWidth\n                  label=\"Full Name\"\n                  value={editForm.fullName}\n                  onChange={(e) => setEditForm({ ...editForm, fullName: e.target.value })}\n                  margin=\"normal\"\n                  variant=\"outlined\"\n                  sx={{ mb: 2 }}\n                />\n                <TextField\n                  fullWidth\n                  label=\"Username\"\n                  value={editForm.username}\n                  onChange={(e) => setEditForm({ ...editForm, username: e.target.value })}\n                  margin=\"normal\"\n                  variant=\"outlined\"\n                  sx={{ mb: 2 }}\n                />\n                <TextField\n                  fullWidth\n                  label=\"Email\"\n                  type=\"email\"\n                  value={editForm.email}\n                  onChange={(e) => setEditForm({ ...editForm, email: e.target.value })}\n                  margin=\"normal\"\n                  variant=\"outlined\"\n                  sx={{ mb: 3 }}\n                />\n                \n                <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center' }}>\n                  <Button\n                    variant=\"contained\"\n                    startIcon={isLoading ? <CircularProgress size={20} /> : <SaveIcon />}\n                    onClick={handleSave}\n                    disabled={isLoading}\n                    sx={{\n                      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                      '&:hover': {\n                        background: 'linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)',\n                      },\n                    }}\n                  >\n                    {isLoading ? 'Saving...' : 'Save Changes'}\n                  </Button>\n                  <Button\n                    variant=\"outlined\"\n                    onClick={handleEditToggle}\n                    disabled={isLoading}\n                  >\n                    Cancel\n                  </Button>\n                </Box>\n              </Box>\n            ) : (\n              <>\n                <Typography variant=\"h4\" fontWeight=\"bold\" gutterBottom>\n                  {user.fullName || user.username}\n                </Typography>\n                \n                <Typography variant=\"h6\" color=\"text.secondary\" gutterBottom>\n                  @{user.username}\n                </Typography>\n\n                <Box sx={{ display: 'flex', justifyContent: 'center', gap: 1, mb: 3 }}>\n                  <Chip\n                    icon={<EmailIcon />}\n                    label={user.emailVerified ? 'Verified' : 'Unverified'}\n                    color={user.emailVerified ? 'success' : 'warning'}\n                    size=\"small\"\n                  />\n                </Box>\n\n                <Divider sx={{ my: 3 }} />\n\n                {/* Profile Details */}\n                <Grid container spacing={3} sx={{ textAlign: 'left' }}>\n                  <Grid item xs={12} sm={6}>\n                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>\n                      <EmailIcon sx={{ mr: 2, color: 'text.secondary' }} />\n                      <Box>\n                        <Typography variant=\"body2\" color=\"text.secondary\">\n                          Email\n                        </Typography>\n                        <Typography variant=\"body1\" fontWeight=\"500\">\n                          {user.email}\n                        </Typography>\n                      </Box>\n                    </Box>\n                  </Grid>\n                  \n                  <Grid item xs={12} sm={6}>\n                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>\n                      <PersonIcon sx={{ mr: 2, color: 'text.secondary' }} />\n                      <Box>\n                        <Typography variant=\"body2\" color=\"text.secondary\">\n                          Username\n                        </Typography>\n                        <Typography variant=\"body1\" fontWeight=\"500\">\n                          {user.username}\n                        </Typography>\n                      </Box>\n                    </Box>\n                  </Grid>\n                  \n                  <Grid item xs={12} sm={6}>\n                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>\n                      <CalendarIcon sx={{ mr: 2, color: 'text.secondary' }} />\n                      <Box>\n                        <Typography variant=\"body2\" color=\"text.secondary\">\n                          Member Since\n                        </Typography>\n                        <Typography variant=\"body1\" fontWeight=\"500\">\n                          {formatDate(user.createdAt)}\n                        </Typography>\n                      </Box>\n                    </Box>\n                  </Grid>\n                </Grid>\n              </>\n            )}\n          </Box>\n        </CardContent>\n      </Card>\n    </Box>\n  );\n};\n\nexport default UserProfile;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AAC/C,SACEC,GAAG,EACHC,IAAI,EACJC,WAAW,EACXC,MAAM,EACNC,UAAU,EACVC,MAAM,EACNC,SAAS,EACTC,UAAU,EACVC,IAAI,EACJC,OAAO,EACPC,IAAI,EACJC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,KAAK,EACLC,gBAAgB,EAChBC,IAAI,EACJC,OAAO,EACPC,KAAK,QACA,eAAe;AACtB,SACEC,IAAI,IAAIC,QAAQ,EAChBC,IAAI,IAAIC,QAAQ,EAChBC,MAAM,IAAIC,UAAU,EACpBC,WAAW,IAAIC,eAAe,EAC9BC,QAAQ,IAAIC,YAAY,EACxBC,KAAK,IAAIC,SAAS,EAClBC,MAAM,IAAIC,UAAU,EACpBC,aAAa,IAAIC,YAAY,EAC7BC,MAAM,IAAIC,UAAU,QACf,qBAAqB;AAC5B,SAASC,OAAO,QAAQ,yBAAyB;AACjD,OAAOC,WAAW,MAAM,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE3C,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxB,MAAM;IAAEC,IAAI;IAAEC;EAAW,CAAC,GAAGT,OAAO,CAAC,CAAC;EACtC,MAAM,CAACU,SAAS,EAAEC,YAAY,CAAC,GAAGnD,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACoD,QAAQ,EAAEC,WAAW,CAAC,GAAGrD,QAAQ,CAAC;IACvCsD,QAAQ,EAAE,CAAAN,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEM,QAAQ,KAAI,EAAE;IAC9BC,KAAK,EAAE,CAAAP,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEO,KAAK,KAAI,EAAE;IACxBC,QAAQ,EAAE,CAAAR,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEQ,QAAQ,KAAI;EAC9B,CAAC,CAAC;EACF,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAG1D,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC2D,KAAK,EAAEC,QAAQ,CAAC,GAAG5D,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC6D,OAAO,EAAEC,UAAU,CAAC,GAAG9D,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAAC+D,YAAY,EAAEC,eAAe,CAAC,GAAGhE,QAAQ,CAAC,CAAAgD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEe,YAAY,KAAI,IAAI,CAAC;EAC5E,MAAM,CAACE,YAAY,EAAEC,eAAe,CAAC,GAAGlE,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACmE,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGpE,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAMqE,YAAY,GAAGpE,MAAM,CAAC,IAAI,CAAC;EAEjC,MAAMqE,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAIpB,SAAS,EAAE;MACb;MACAG,WAAW,CAAC;QACVC,QAAQ,EAAE,CAAAN,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEM,QAAQ,KAAI,EAAE;QAC9BC,KAAK,EAAE,CAAAP,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEO,KAAK,KAAI,EAAE;QACxBC,QAAQ,EAAE,CAAAR,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEQ,QAAQ,KAAI;MAC9B,CAAC,CAAC;MACFI,QAAQ,CAAC,EAAE,CAAC;IACd;IACAT,YAAY,CAAC,CAACD,SAAS,CAAC;EAC1B,CAAC;EAED,MAAMqB,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7Bb,YAAY,CAAC,IAAI,CAAC;IAClBE,QAAQ,CAAC,EAAE,CAAC;IACZE,UAAU,CAAC,EAAE,CAAC;IAEd,IAAI;MACF;MACA,IAAI,CAACV,QAAQ,CAACE,QAAQ,CAACkB,IAAI,CAAC,CAAC,EAAE;QAC7BZ,QAAQ,CAAC,sBAAsB,CAAC;QAChC;MACF;MAEA,IAAI,CAACR,QAAQ,CAACG,KAAK,CAACiB,IAAI,CAAC,CAAC,EAAE;QAC1BZ,QAAQ,CAAC,mBAAmB,CAAC;QAC7B;MACF;MAEA,IAAI,CAACnB,WAAW,CAACgC,aAAa,CAACrB,QAAQ,CAACG,KAAK,CAAC,EAAE;QAC9CK,QAAQ,CAAC,oCAAoC,CAAC;QAC9C;MACF;;MAEA;MACA,MAAMc,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGC,OAAO,CAACC,GAAG,CAACC,kBAAkB,eAAe,EAAE;QAC7EC,MAAM,EAAE,KAAK;QACbC,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB;UAClC,eAAe,EAAE,UAAUvC,WAAW,CAACwC,QAAQ,CAAC,CAAC;QACnD,CAAC;QACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UACnB9B,QAAQ,EAAEF,QAAQ,CAACE,QAAQ;UAC3BC,KAAK,EAAEH,QAAQ,CAACG,KAAK;UACrBC,QAAQ,EAAEJ,QAAQ,CAACI;QACrB,CAAC;MACH,CAAC,CAAC;MAEF,MAAM6B,IAAI,GAAG,MAAMX,QAAQ,CAACY,IAAI,CAAC,CAAC;MAElC,IAAI,CAACZ,QAAQ,CAACa,EAAE,EAAE;QAChB,MAAM,IAAIC,KAAK,CAACH,IAAI,CAAC1B,KAAK,IAAI,0BAA0B,CAAC;MAC3D;MAEA,IAAI0B,IAAI,CAACxB,OAAO,EAAE;QAChB;QACAZ,UAAU,CAACoC,IAAI,CAACA,IAAI,CAACrC,IAAI,CAAC;QAC1Bc,UAAU,CAAC,+BAA+B,CAAC;QAC3CX,YAAY,CAAC,KAAK,CAAC;;QAEnB;QACAsC,UAAU,CAAC,MAAM3B,UAAU,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;MACxC,CAAC,MAAM;QACLF,QAAQ,CAACyB,IAAI,CAAC1B,KAAK,IAAI,0BAA0B,CAAC;MACpD;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACd+B,OAAO,CAAC/B,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7CC,QAAQ,CAACD,KAAK,CAACgC,OAAO,IAAI,6CAA6C,CAAC;IAC1E,CAAC,SAAS;MACRjC,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAMkC,WAAW,GAAIC,IAAI,IAAK;IAAA,IAAAC,cAAA;IAC5B,IAAI,CAACD,IAAI,EAAE,OAAO,CAAA7C,IAAI,aAAJA,IAAI,wBAAA8C,cAAA,GAAJ9C,IAAI,CAAEM,QAAQ,cAAAwC,cAAA,uBAAdA,cAAA,CAAgBC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,KAAI,GAAG;IAChE,OAAOH,IAAI,CAACI,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACJ,MAAM,CAAC,CAAC,CAAC,CAAC,CAACK,IAAI,CAAC,EAAE,CAAC,CAACJ,WAAW,CAAC,CAAC,CAACK,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;EACjF,CAAC;EAED,MAAMC,UAAU,GAAIC,UAAU,IAAK;IACjC,IAAI,CAACA,UAAU,EAAE,OAAO,SAAS;IACjC,OAAO,IAAIC,IAAI,CAACD,UAAU,CAAC,CAACE,kBAAkB,CAAC,OAAO,EAAE;MACtDC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,MAAM;MACbC,GAAG,EAAE;IACP,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,iBAAiB,GAAIC,KAAK,IAAK;IACnC,MAAMC,IAAI,GAAGD,KAAK,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAClC,IAAI,CAACF,IAAI,EAAE;;IAEX;IACA,IAAI,CAACA,IAAI,CAACG,IAAI,CAACC,UAAU,CAAC,QAAQ,CAAC,EAAE;MACnCvD,QAAQ,CAAC,kCAAkC,CAAC;MAC5C;IACF;;IAEA;IACA,IAAImD,IAAI,CAACK,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI,EAAE;MAC/BxD,QAAQ,CAAC,kCAAkC,CAAC;MAC5C;IACF;;IAEA;IACA,MAAMyD,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;IAC/BD,MAAM,CAACE,MAAM,GAAIC,CAAC,IAAK;MACrBtD,eAAe,CAACsD,CAAC,CAACR,MAAM,CAACS,MAAM,CAAC;IAClC,CAAC;IACDJ,MAAM,CAACK,aAAa,CAACX,IAAI,CAAC;;IAE1B;IACAY,kBAAkB,CAACZ,IAAI,CAAC;EAC1B,CAAC;EAED,MAAMY,kBAAkB,GAAG,MAAOZ,IAAI,IAAK;IACzC3C,mBAAmB,CAAC,IAAI,CAAC;IACzBR,QAAQ,CAAC,EAAE,CAAC;IAEZ,IAAI;MACF,MAAMgE,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;MAC/BD,QAAQ,CAACE,MAAM,CAAC,cAAc,EAAEf,IAAI,CAAC;MAErC,MAAMrC,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGC,OAAO,CAACC,GAAG,CAACC,kBAAkB,qBAAqB,EAAE;QACnFC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,eAAe,EAAE,UAAUvC,WAAW,CAACwC,QAAQ,CAAC,CAAC;QACnD,CAAC;QACDC,IAAI,EAAE0C;MACR,CAAC,CAAC;MAEF,MAAMvC,IAAI,GAAG,MAAMX,QAAQ,CAACY,IAAI,CAAC,CAAC;MAElC,IAAI,CAACZ,QAAQ,CAACa,EAAE,EAAE;QAChB,MAAM,IAAIC,KAAK,CAACH,IAAI,CAAC1B,KAAK,IAAI,wBAAwB,CAAC;MACzD;MAEA,IAAI0B,IAAI,CAACxB,OAAO,EAAE;QAChBG,eAAe,CAACqB,IAAI,CAACA,IAAI,CAAC0C,QAAQ,CAAC;QACnC9E,UAAU,CAAC;UAAE,GAAGD,IAAI;UAAEe,YAAY,EAAEsB,IAAI,CAACA,IAAI,CAAC0C;QAAS,CAAC,CAAC;QACzDjE,UAAU,CAAC,qCAAqC,CAAC;QACjD2B,UAAU,CAAC,MAAM3B,UAAU,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;MACxC,CAAC,MAAM;QACLF,QAAQ,CAACyB,IAAI,CAAC1B,KAAK,IAAI,wBAAwB,CAAC;MAClD;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACd+B,OAAO,CAAC/B,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAC3CC,QAAQ,CAACD,KAAK,CAACgC,OAAO,IAAI,2CAA2C,CAAC;IACxE,CAAC,SAAS;MACRvB,mBAAmB,CAAC,KAAK,CAAC;IAC5B;EACF,CAAC;EAED,MAAM4D,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC5D,mBAAmB,CAAC,IAAI,CAAC;IACzBR,QAAQ,CAAC,EAAE,CAAC;IAEZ,IAAI;MACF,MAAMc,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGC,OAAO,CAACC,GAAG,CAACC,kBAAkB,qBAAqB,EAAE;QACnFC,MAAM,EAAE,QAAQ;QAChBC,OAAO,EAAE;UACP,eAAe,EAAE,UAAUvC,WAAW,CAACwC,QAAQ,CAAC,CAAC;QACnD;MACF,CAAC,CAAC;MAEF,MAAMI,IAAI,GAAG,MAAMX,QAAQ,CAACY,IAAI,CAAC,CAAC;MAElC,IAAI,CAACZ,QAAQ,CAACa,EAAE,EAAE;QAChB,MAAM,IAAIC,KAAK,CAACH,IAAI,CAAC1B,KAAK,IAAI,wBAAwB,CAAC;MACzD;MAEA,IAAI0B,IAAI,CAACxB,OAAO,EAAE;QAChBG,eAAe,CAAC,IAAI,CAAC;QACrBE,eAAe,CAAC,IAAI,CAAC;QACrBjB,UAAU,CAAC;UAAE,GAAGD,IAAI;UAAEe,YAAY,EAAE;QAAK,CAAC,CAAC;QAC3CD,UAAU,CAAC,qCAAqC,CAAC;QACjD2B,UAAU,CAAC,MAAM3B,UAAU,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;MACxC,CAAC,MAAM;QACLF,QAAQ,CAACyB,IAAI,CAAC1B,KAAK,IAAI,wBAAwB,CAAC;MAClD;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACd+B,OAAO,CAAC/B,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5CC,QAAQ,CAACD,KAAK,CAACgC,OAAO,IAAI,2CAA2C,CAAC;IACxE,CAAC,SAAS;MACRvB,mBAAmB,CAAC,KAAK,CAAC;IAC5B;EACF,CAAC;EAED,IAAI,CAACpB,IAAI,EAAE;IACT,oBACEL,OAAA,CAACxC,IAAI;MAAC8H,EAAE,EAAE;QAAEC,QAAQ,EAAE,GAAG;QAAEC,EAAE,EAAE;MAAO,CAAE;MAAAC,QAAA,eACtCzF,OAAA,CAACvC,WAAW;QAAC6H,EAAE,EAAE;UAAEI,SAAS,EAAE,QAAQ;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAF,QAAA,eAC9CzF,OAAA,CAACrC,UAAU;UAACiI,OAAO,EAAC,IAAI;UAACC,KAAK,EAAC,gBAAgB;UAAAJ,QAAA,EAAC;QAEhD;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAEX;EAEA,oBACEjG,OAAA,CAACzC,GAAG;IAAAkI,QAAA,gBAEFzF,OAAA,CAACxB,IAAI;MAAC0H,EAAE,EAAE,CAAC,CAAChF,OAAQ;MAAAuE,QAAA,eAClBzF,OAAA,CAAC1B,KAAK;QAAC6H,QAAQ,EAAC,SAAS;QAACb,EAAE,EAAE;UAAEc,EAAE,EAAE;QAAE,CAAE;QAAAX,QAAA,EACrCvE;MAAO;QAAA4E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAEPjG,OAAA,CAACxB,IAAI;MAAC0H,EAAE,EAAE,CAAC,CAAClF,KAAM;MAAAyE,QAAA,eAChBzF,OAAA,CAAC1B,KAAK;QAAC6H,QAAQ,EAAC,OAAO;QAACb,EAAE,EAAE;UAAEc,EAAE,EAAE;QAAE,CAAE;QAAAX,QAAA,EACnCzE;MAAK;QAAA8E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAEPjG,OAAA,CAACxC,IAAI;MACH8H,EAAE,EAAE;QACFC,QAAQ,EAAE,GAAG;QACbC,EAAE,EAAE,MAAM;QACVa,UAAU,EAAGC,KAAK,IAAKA,KAAK,CAACC,OAAO,CAACC,IAAI,KAAK,MAAM,GAChD,oFAAoF,GACpF,sFAAsF;QAC1FC,MAAM,EAAE,oCAAoC;QAC5CC,YAAY,EAAE,CAAC;QACfC,QAAQ,EAAE;MACZ,CAAE;MAAAlB,QAAA,gBAGFzF,OAAA,CAACzC,GAAG;QACF+H,EAAE,EAAE;UACFe,UAAU,EAAE,mDAAmD;UAC/DO,MAAM,EAAE,GAAG;UACXC,QAAQ,EAAE;QACZ,CAAE;QAAApB,QAAA,eAEFzF,OAAA,CAACzC,GAAG;UACF+H,EAAE,EAAE;YACFuB,QAAQ,EAAE,UAAU;YACpBC,GAAG,EAAE,EAAE;YACPC,KAAK,EAAE;UACT,CAAE;UAAAtB,QAAA,eAEFzF,OAAA,CAACvB,OAAO;YAACuI,KAAK,EAAEzG,SAAS,GAAG,gBAAgB,GAAG,cAAe;YAAAkF,QAAA,eAC5DzF,OAAA,CAAClC,UAAU;cACTmJ,OAAO,EAAEtF,gBAAiB;cAC1BuF,QAAQ,EAAEpG,SAAU;cACpBwE,EAAE,EAAE;gBACFe,UAAU,EAAE,0BAA0B;gBACtCR,KAAK,EAAE,OAAO;gBACd,SAAS,EAAE;kBACTQ,UAAU,EAAE;gBACd;cACF,CAAE;cAAAZ,QAAA,EAEDlF,SAAS,gBAAGP,OAAA,CAAChB,UAAU;gBAAA8G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAAGjG,OAAA,CAACpB,QAAQ;gBAAAkH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENjG,OAAA,CAACvC,WAAW;QAAC6H,EAAE,EAAE;UAAE6B,EAAE,EAAE,CAAC;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAA3B,QAAA,gBAEhCzF,OAAA,CAACzC,GAAG;UAAC+H,EAAE,EAAE;YAAE+B,OAAO,EAAE,MAAM;YAAEC,cAAc,EAAE,QAAQ;YAAElB,EAAE,EAAE,CAAC;YAAEmB,EAAE,EAAE,CAAC;UAAE,CAAE;UAAA9B,QAAA,eACpEzF,OAAA,CAACzC,GAAG;YAAC+H,EAAE,EAAE;cAAEuB,QAAQ,EAAE;YAAW,CAAE;YAAApB,QAAA,gBAChCzF,OAAA,CAACtB,KAAK;cACJ8I,OAAO,EAAC,UAAU;cAClBC,YAAY,EAAE;gBAAEC,QAAQ,EAAE,QAAQ;gBAAEC,UAAU,EAAE;cAAQ,CAAE;cAC1DC,YAAY,EACVrH,SAAS,iBACPP,OAAA,CAACzC,GAAG;gBAAC+H,EAAE,EAAE;kBAAE+B,OAAO,EAAE,MAAM;kBAAEQ,aAAa,EAAE,QAAQ;kBAAEC,GAAG,EAAE;gBAAE,CAAE;gBAAArC,QAAA,gBAC5DzF,OAAA,CAACvB,OAAO;kBAACuI,KAAK,EAAC,kBAAkB;kBAAAvB,QAAA,eAC/BzF,OAAA,CAAClC,UAAU;oBACT2G,IAAI,EAAC,OAAO;oBACZwC,OAAO,EAAEA,CAAA;sBAAA,IAAAc,qBAAA;sBAAA,QAAAA,qBAAA,GAAMrG,YAAY,CAACsG,OAAO,cAAAD,qBAAA,uBAApBA,qBAAA,CAAsBE,KAAK,CAAC,CAAC;oBAAA,CAAC;oBAC7Cf,QAAQ,EAAE1F,gBAAiB;oBAC3B8D,EAAE,EAAE;sBACFe,UAAU,EAAE,mDAAmD;sBAC/DR,KAAK,EAAE,OAAO;sBACdqC,KAAK,EAAE,EAAE;sBACTtB,MAAM,EAAE,EAAE;sBACV,SAAS,EAAE;wBACTP,UAAU,EAAE;sBACd;oBACF,CAAE;oBAAAZ,QAAA,EAEDjE,gBAAgB,gBACfxB,OAAA,CAACzB,gBAAgB;sBAACkG,IAAI,EAAE,EAAG;sBAACoB,KAAK,EAAC;oBAAS;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,gBAE9CjG,OAAA,CAACd,eAAe;sBAACiJ,QAAQ,EAAC;oBAAO;sBAAArC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBACpC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACS;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,EACT,CAAC7E,YAAY,IAAIE,YAAY,kBAC5BtB,OAAA,CAACvB,OAAO;kBAACuI,KAAK,EAAC,cAAc;kBAAAvB,QAAA,eAC3BzF,OAAA,CAAClC,UAAU;oBACT2G,IAAI,EAAC,OAAO;oBACZwC,OAAO,EAAE5B,iBAAkB;oBAC3B6B,QAAQ,EAAE1F,gBAAiB;oBAC3B8D,EAAE,EAAE;sBACFe,UAAU,EAAE,mDAAmD;sBAC/DR,KAAK,EAAE,OAAO;sBACdqC,KAAK,EAAE,EAAE;sBACTtB,MAAM,EAAE,EAAE;sBACVW,EAAE,EAAE,CAAC;sBACL,SAAS,EAAE;wBACTlB,UAAU,EAAE;sBACd;oBACF,CAAE;oBAAAZ,QAAA,eAEFzF,OAAA,CAACJ,UAAU;sBAACuI,QAAQ,EAAC;oBAAO;sBAAArC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CACV;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAER;cAAAR,QAAA,eAEDzF,OAAA,CAACtC,MAAM;gBACL0K,GAAG,EAAE9G,YAAY,IAAIF,YAAY,KAAIf,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEe,YAAY,CAAC;gBACxDkE,EAAE,EAAE;kBACF4C,KAAK,EAAE,GAAG;kBACVtB,MAAM,EAAE,GAAG;kBACXuB,QAAQ,EAAE,QAAQ;kBAClBE,UAAU,EAAE,MAAM;kBAClBhC,UAAU,EAAE,mDAAmD;kBAC/DI,MAAM,EAAE,iBAAiB;kBACzB6B,SAAS,EAAE;gBACb,CAAE;gBAAA7C,QAAA,EAED,EAAEnE,YAAY,IAAIF,YAAY,IAAIf,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEe,YAAY,CAAC,IACpD6B,WAAW,CAACxC,QAAQ,CAACI,QAAQ,IAAIR,IAAI,CAACQ,QAAQ;cAAC;gBAAAiF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAE3C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eAGRjG,OAAA;cACEuI,GAAG,EAAE7G,YAAa;cAClB6C,IAAI,EAAC,MAAM;cACXiE,MAAM,EAAC,SAAS;cAChBC,QAAQ,EAAEvE,iBAAkB;cAC5BwE,KAAK,EAAE;gBAAErB,OAAO,EAAE;cAAO;YAAE;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNjG,OAAA,CAACzC,GAAG;UAAC+H,EAAE,EAAE;YAAEI,SAAS,EAAE,QAAQ;YAAEU,EAAE,EAAE;UAAE,CAAE;UAAAX,QAAA,EACrClF,SAAS,gBACRP,OAAA,CAACzC,GAAG;YAAC+H,EAAE,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEC,EAAE,EAAE;YAAO,CAAE;YAAAC,QAAA,gBACrCzF,OAAA,CAACnC,SAAS;cACR8K,SAAS;cACTC,KAAK,EAAC,WAAW;cACjBC,KAAK,EAAEpI,QAAQ,CAACI,QAAS;cACzB4H,QAAQ,EAAG5D,CAAC,IAAKnE,WAAW,CAAC;gBAAE,GAAGD,QAAQ;gBAAEI,QAAQ,EAAEgE,CAAC,CAACR,MAAM,CAACwE;cAAM,CAAC,CAAE;cACxEC,MAAM,EAAC,QAAQ;cACflD,OAAO,EAAC,UAAU;cAClBN,EAAE,EAAE;gBAAEc,EAAE,EAAE;cAAE;YAAE;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC,eACFjG,OAAA,CAACnC,SAAS;cACR8K,SAAS;cACTC,KAAK,EAAC,UAAU;cAChBC,KAAK,EAAEpI,QAAQ,CAACE,QAAS;cACzB8H,QAAQ,EAAG5D,CAAC,IAAKnE,WAAW,CAAC;gBAAE,GAAGD,QAAQ;gBAAEE,QAAQ,EAAEkE,CAAC,CAACR,MAAM,CAACwE;cAAM,CAAC,CAAE;cACxEC,MAAM,EAAC,QAAQ;cACflD,OAAO,EAAC,UAAU;cAClBN,EAAE,EAAE;gBAAEc,EAAE,EAAE;cAAE;YAAE;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC,eACFjG,OAAA,CAACnC,SAAS;cACR8K,SAAS;cACTC,KAAK,EAAC,OAAO;cACbrE,IAAI,EAAC,OAAO;cACZsE,KAAK,EAAEpI,QAAQ,CAACG,KAAM;cACtB6H,QAAQ,EAAG5D,CAAC,IAAKnE,WAAW,CAAC;gBAAE,GAAGD,QAAQ;gBAAEG,KAAK,EAAEiE,CAAC,CAACR,MAAM,CAACwE;cAAM,CAAC,CAAE;cACrEC,MAAM,EAAC,QAAQ;cACflD,OAAO,EAAC,UAAU;cAClBN,EAAE,EAAE;gBAAEc,EAAE,EAAE;cAAE;YAAE;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC,eAEFjG,OAAA,CAACzC,GAAG;cAAC+H,EAAE,EAAE;gBAAE+B,OAAO,EAAE,MAAM;gBAAES,GAAG,EAAE,CAAC;gBAAER,cAAc,EAAE;cAAS,CAAE;cAAA7B,QAAA,gBAC7DzF,OAAA,CAACpC,MAAM;gBACLgI,OAAO,EAAC,WAAW;gBACnBmD,SAAS,EAAEjI,SAAS,gBAAGd,OAAA,CAACzB,gBAAgB;kBAACkG,IAAI,EAAE;gBAAG;kBAAAqB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAAGjG,OAAA,CAAClB,QAAQ;kBAAAgH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACrEgB,OAAO,EAAErF,UAAW;gBACpBsF,QAAQ,EAAEpG,SAAU;gBACpBwE,EAAE,EAAE;kBACFe,UAAU,EAAE,mDAAmD;kBAC/D,SAAS,EAAE;oBACTA,UAAU,EAAE;kBACd;gBACF,CAAE;gBAAAZ,QAAA,EAED3E,SAAS,GAAG,WAAW,GAAG;cAAc;gBAAAgF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC,CAAC,eACTjG,OAAA,CAACpC,MAAM;gBACLgI,OAAO,EAAC,UAAU;gBAClBqB,OAAO,EAAEtF,gBAAiB;gBAC1BuF,QAAQ,EAAEpG,SAAU;gBAAA2E,QAAA,EACrB;cAED;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,gBAENjG,OAAA,CAAAE,SAAA;YAAAuF,QAAA,gBACEzF,OAAA,CAACrC,UAAU;cAACiI,OAAO,EAAC,IAAI;cAACyC,UAAU,EAAC,MAAM;cAACW,YAAY;cAAAvD,QAAA,EACpDpF,IAAI,CAACQ,QAAQ,IAAIR,IAAI,CAACM;YAAQ;cAAAmF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB,CAAC,eAEbjG,OAAA,CAACrC,UAAU;cAACiI,OAAO,EAAC,IAAI;cAACC,KAAK,EAAC,gBAAgB;cAACmD,YAAY;cAAAvD,QAAA,GAAC,GAC1D,EAACpF,IAAI,CAACM,QAAQ;YAAA;cAAAmF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eAEbjG,OAAA,CAACzC,GAAG;cAAC+H,EAAE,EAAE;gBAAE+B,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE,QAAQ;gBAAEQ,GAAG,EAAE,CAAC;gBAAE1B,EAAE,EAAE;cAAE,CAAE;cAAAX,QAAA,eACpEzF,OAAA,CAACjC,IAAI;gBACHkL,IAAI,eAAEjJ,OAAA,CAACV,SAAS;kBAAAwG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACpB2C,KAAK,EAAEvI,IAAI,CAAC6I,aAAa,GAAG,UAAU,GAAG,YAAa;gBACtDrD,KAAK,EAAExF,IAAI,CAAC6I,aAAa,GAAG,SAAS,GAAG,SAAU;gBAClDzE,IAAI,EAAC;cAAO;gBAAAqB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENjG,OAAA,CAAChC,OAAO;cAACsH,EAAE,EAAE;gBAAE6D,EAAE,EAAE;cAAE;YAAE;cAAArD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAG1BjG,OAAA,CAAC/B,IAAI;cAACmL,SAAS;cAACC,OAAO,EAAE,CAAE;cAAC/D,EAAE,EAAE;gBAAEI,SAAS,EAAE;cAAO,CAAE;cAAAD,QAAA,gBACpDzF,OAAA,CAAC/B,IAAI;gBAACqL,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAACC,EAAE,EAAE,CAAE;gBAAA/D,QAAA,eACvBzF,OAAA,CAACzC,GAAG;kBAAC+H,EAAE,EAAE;oBAAE+B,OAAO,EAAE,MAAM;oBAAEoC,UAAU,EAAE,QAAQ;oBAAErD,EAAE,EAAE;kBAAE,CAAE;kBAAAX,QAAA,gBACxDzF,OAAA,CAACV,SAAS;oBAACgG,EAAE,EAAE;sBAAEoE,EAAE,EAAE,CAAC;sBAAE7D,KAAK,EAAE;oBAAiB;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACrDjG,OAAA,CAACzC,GAAG;oBAAAkI,QAAA,gBACFzF,OAAA,CAACrC,UAAU;sBAACiI,OAAO,EAAC,OAAO;sBAACC,KAAK,EAAC,gBAAgB;sBAAAJ,QAAA,EAAC;oBAEnD;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACbjG,OAAA,CAACrC,UAAU;sBAACiI,OAAO,EAAC,OAAO;sBAACyC,UAAU,EAAC,KAAK;sBAAA5C,QAAA,EACzCpF,IAAI,CAACO;oBAAK;sBAAAkF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eAEPjG,OAAA,CAAC/B,IAAI;gBAACqL,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAACC,EAAE,EAAE,CAAE;gBAAA/D,QAAA,eACvBzF,OAAA,CAACzC,GAAG;kBAAC+H,EAAE,EAAE;oBAAE+B,OAAO,EAAE,MAAM;oBAAEoC,UAAU,EAAE,QAAQ;oBAAErD,EAAE,EAAE;kBAAE,CAAE;kBAAAX,QAAA,gBACxDzF,OAAA,CAACR,UAAU;oBAAC8F,EAAE,EAAE;sBAAEoE,EAAE,EAAE,CAAC;sBAAE7D,KAAK,EAAE;oBAAiB;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACtDjG,OAAA,CAACzC,GAAG;oBAAAkI,QAAA,gBACFzF,OAAA,CAACrC,UAAU;sBAACiI,OAAO,EAAC,OAAO;sBAACC,KAAK,EAAC,gBAAgB;sBAAAJ,QAAA,EAAC;oBAEnD;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACbjG,OAAA,CAACrC,UAAU;sBAACiI,OAAO,EAAC,OAAO;sBAACyC,UAAU,EAAC,KAAK;sBAAA5C,QAAA,EACzCpF,IAAI,CAACM;oBAAQ;sBAAAmF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eAEPjG,OAAA,CAAC/B,IAAI;gBAACqL,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAACC,EAAE,EAAE,CAAE;gBAAA/D,QAAA,eACvBzF,OAAA,CAACzC,GAAG;kBAAC+H,EAAE,EAAE;oBAAE+B,OAAO,EAAE,MAAM;oBAAEoC,UAAU,EAAE,QAAQ;oBAAErD,EAAE,EAAE;kBAAE,CAAE;kBAAAX,QAAA,gBACxDzF,OAAA,CAACN,YAAY;oBAAC4F,EAAE,EAAE;sBAAEoE,EAAE,EAAE,CAAC;sBAAE7D,KAAK,EAAE;oBAAiB;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACxDjG,OAAA,CAACzC,GAAG;oBAAAkI,QAAA,gBACFzF,OAAA,CAACrC,UAAU;sBAACiI,OAAO,EAAC,OAAO;sBAACC,KAAK,EAAC,gBAAgB;sBAAAJ,QAAA,EAAC;oBAEnD;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACbjG,OAAA,CAACrC,UAAU;sBAACiI,OAAO,EAAC,OAAO;sBAACyC,UAAU,EAAC,KAAK;sBAAA5C,QAAA,EACzC9B,UAAU,CAACtD,IAAI,CAACsJ,SAAS;oBAAC;sBAAA7D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjB,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA,eACP;QACH;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAAC7F,EAAA,CA3eID,WAAW;EAAA,QACcN,OAAO;AAAA;AAAA+J,EAAA,GADhCzJ,WAAW;AA6ejB,eAAeA,WAAW;AAAC,IAAAyJ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}