import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  LinearProgress,
  Chip,
  Avatar,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  IconButton,
  Tooltip,
  Badge,
  Divider,
} from '@mui/material';
import {
  Security as SecurityIcon,
  TrendingUp as TrendingUpIcon,
  Warning as WarningIcon,
  Shield as ShieldIcon,
  Speed as SpeedIcon,
  Notifications as NotificationsIcon,
  Timeline as TimelineIcon,
  Psychology as AIIcon,
  AutoFixHigh as AutoFixIcon,
  Insights as InsightsIcon,
} from '@mui/icons-material';

const SmartDashboard = React.memo(() => {
  const [realTimeData, setRealTimeData] = useState({
    threatLevel: 15,
    scansToday: 247,
    threatsBlocked: 12,
    systemHealth: 98,
    aiConfidence: 94,
    activeScans: 3,
  });

  const [smartInsights, setSmartInsights] = useState([
    {
      type: 'warning',
      title: 'Unusual Activity Detected',
      description: 'Increased malware attempts from Eastern Europe region',
      confidence: 87,
      timestamp: new Date(Date.now() - 1000 * 60 * 15),
    },
    {
      type: 'success',
      title: 'AI Model Updated',
      description: 'Threat detection accuracy improved by 12%',
      confidence: 95,
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2),
    },
    {
      type: 'info',
      title: 'Security Trend Analysis',
      description: 'Phishing attempts decreased by 23% this week',
      confidence: 91,
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 4),
    },
  ]);

  const [threatMap, setThreatMap] = useState([
    { region: 'North America', threats: 45, trend: 'down' },
    { region: 'Europe', threats: 67, trend: 'up' },
    { region: 'Asia Pacific', threats: 89, trend: 'stable' },
    { region: 'South America', threats: 23, trend: 'down' },
  ]);

  // Simulate real-time updates
  useEffect(() => {
    const interval = setInterval(() => {
      setRealTimeData(prev => ({
        ...prev,
        scansToday: prev.scansToday + Math.floor(Math.random() * 3),
        threatLevel: Math.max(5, Math.min(100, prev.threatLevel + (Math.random() - 0.5) * 10)),
        systemHealth: Math.max(85, Math.min(100, prev.systemHealth + (Math.random() - 0.5) * 2)),
        aiConfidence: Math.max(80, Math.min(100, prev.aiConfidence + (Math.random() - 0.5) * 3)),
      }));
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  const getThreatLevelColor = (level) => {
    if (level < 30) return 'success';
    if (level < 70) return 'warning';
    return 'error';
  };

  const getInsightIcon = (type) => {
    switch (type) {
      case 'warning': return <WarningIcon color="warning" />;
      case 'success': return <ShieldIcon color="success" />;
      case 'info': return <InsightsIcon color="info" />;
      default: return <SecurityIcon />;
    }
  };

  return (
    <Box
      sx={{
        p: 4,
        background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.02) 100%)',
        borderRadius: 4,
        backdropFilter: 'blur(20px)',
        border: '1px solid rgba(255, 255, 255, 0.1)',
      }}
    >
      {/* Dashboard Header */}
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          mb: 4,
          pb: 2,
          borderBottom: '1px solid rgba(102, 126, 234, 0.2)',
        }}
      >
        <Box>
          <Typography
            variant="h4"
            fontWeight="bold"
            sx={{
              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
              backgroundClip: 'text',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              mb: 1,
            }}
          >
            Smart Dashboard
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Real-time security monitoring and analytics
          </Typography>
        </Box>
        <Chip
          icon={<AIIcon />}
          label="AI Active"
          color="success"
          variant="outlined"
          sx={{
            background: 'linear-gradient(135deg, rgba(76, 175, 80, 0.1) 0%, rgba(139, 195, 74, 0.1) 100%)',
            border: '1px solid rgba(76, 175, 80, 0.3)',
          }}
        />
      </Box>

      {/* Real-time Metrics Grid */}
      <Box
        sx={{
          display: 'grid',
          gridTemplateColumns: {
            xs: '1fr',
            sm: 'repeat(2, 1fr)',
            md: 'repeat(4, 1fr)',
          },
          gap: 3,
          mb: 4,
        }}
      >
        <Card
          sx={{
            background: (theme) => theme.palette.mode === 'dark'
              ? 'linear-gradient(135deg, rgba(12, 12, 12, 0.95) 0%, rgba(26, 26, 26, 0.95) 50%, rgba(45, 45, 45, 0.95) 100%)'
              : 'linear-gradient(135deg, rgba(248, 250, 252, 0.95) 0%, rgba(241, 245, 249, 0.95) 50%, rgba(226, 232, 240, 0.95) 100%)',
            backdropFilter: 'blur(25px) saturate(180%)',
            border: '1px solid rgba(102, 126, 234, 0.2)',
            borderRadius: 3,
            position: 'relative',
            overflow: 'hidden',
            boxShadow: '0 8px 32px rgba(102, 126, 234, 0.15)',
            transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
            '&:hover': {
              transform: 'translateY(-4px) scale(1.02)',
                boxShadow: '0 16px 48px rgba(102, 126, 234, 0.25)',
            },
            '&::before': {
              content: '""',
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              height: '4px',
              background: 'linear-gradient(90deg, #667eea 0%, #764ba2 100%)',
            },
          }}
        >
          <CardContent>
            <Box display="flex" alignItems="center" justifyContent="space-between">
              <Box>
                <Typography variant="h3" fontWeight="bold" color="primary">
                  {realTimeData.scansToday}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Scans Today
                </Typography>
              </Box>
              <Avatar
                sx={{
                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                  width: 56,
                  height: 56,
                }}
              >
                <SpeedIcon />
              </Avatar>
            </Box>
          </CardContent>
        </Card>

        <Card
          sx={{
            background: (theme) => theme.palette.mode === 'dark'
              ? 'linear-gradient(135deg, rgba(26, 26, 46, 0.95) 0%, rgba(22, 33, 62, 0.95) 50%, rgba(15, 52, 96, 0.95) 100%)'
              : 'linear-gradient(135deg, rgba(248, 250, 252, 0.95) 0%, rgba(241, 245, 249, 0.95) 50%, rgba(226, 232, 240, 0.95) 100%)',
            backdropFilter: 'blur(25px) saturate(180%)',
            border: '1px solid rgba(233, 69, 96, 0.2)',
            borderRadius: 3,
            position: 'relative',
            overflow: 'hidden',
            boxShadow: '0 8px 32px rgba(233, 69, 96, 0.15)',
            transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
            '&:hover': {
              transform: 'translateY(-4px) scale(1.02)',
              boxShadow: '0 16px 48px rgba(233, 69, 96, 0.25)',
            },
            '&::before': {
              content: '""',
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              height: '4px',
              background: 'linear-gradient(90deg, #e94560 0%, #f27121 100%)',
            },
          }}
        >
          <CardContent>
            <Box display="flex" alignItems="center" justifyContent="space-between">
              <Box>
                <Typography variant="h3" fontWeight="bold" color="error">
                    {realTimeData.threatsBlocked}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Threats Blocked
                </Typography>
              </Box>
              <Avatar
                sx={{
                  background: 'linear-gradient(135deg, #f44336 0%, #ff5722 100%)',
                  width: 56,
                  height: 56,
                }}
              >
                <ShieldIcon />
              </Avatar>
            </Box>
          </CardContent>
        </Card>

        <Card
          sx={{
            background: 'linear-gradient(135deg, rgba(76, 175, 80, 0.1) 0%, rgba(139, 195, 74, 0.1) 100%)',
            border: '1px solid rgba(76, 175, 80, 0.2)',
            borderRadius: 3,
            position: 'relative',
            overflow: 'hidden',
            transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
            '&:hover': {
              transform: 'translateY(-4px) scale(1.02)',
              boxShadow: '0 16px 48px rgba(76, 175, 80, 0.25)',
            },
            '&::before': {
              content: '""',
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              height: '4px',
              background: 'linear-gradient(90deg, #4caf50 0%, #8bc34a 100%)',
            },
          }}
        >
          <CardContent>
            <Box display="flex" alignItems="center" justifyContent="space-between">
              <Box>
                <Typography variant="h3" fontWeight="bold" color="success">
                  {realTimeData.systemHealth}%
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  System Health
                </Typography>
              </Box>
              <Avatar
                sx={{
                  background: 'linear-gradient(135deg, #4caf50 0%, #8bc34a 100%)',
                  width: 56,
                  height: 56,
                }}
              >
                <TrendingUpIcon />
              </Avatar>
            </Box>
            <LinearProgress
              variant="determinate"
              value={realTimeData.systemHealth}
              sx={{ mt: 1, height: 6, borderRadius: 3 }}
              color="success"
            />
          </CardContent>
        </Card>

        <Card
          sx={{
            background: 'linear-gradient(135deg, rgba(156, 39, 176, 0.1) 0%, rgba(233, 30, 99, 0.1) 100%)',
            border: '1px solid rgba(156, 39, 176, 0.2)',
            borderRadius: 3,
            position: 'relative',
            overflow: 'hidden',
            transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
            '&:hover': {
              transform: 'translateY(-4px) scale(1.02)',
              boxShadow: '0 16px 48px rgba(156, 39, 176, 0.25)',
            },
            '&::before': {
              content: '""',
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              height: '4px',
              background: 'linear-gradient(90deg, #9c27b0 0%, #e91e63 100%)',
            },
          }}
        >
          <CardContent>
            <Box display="flex" alignItems="center" justifyContent="space-between">
              <Box>
                <Typography variant="h3" fontWeight="bold" color="secondary">
                  {realTimeData.aiConfidence}%
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  AI Confidence
                </Typography>
              </Box>
              <Avatar
                sx={{
                  background: 'linear-gradient(135deg, #9c27b0 0%, #e91e63 100%)',
                  width: 56,
                  height: 56,
                }}
                >
                  <AIIcon />
                </Avatar>
              </Box>
              <LinearProgress
                variant="determinate"
                value={realTimeData.aiConfidence}
                sx={{ mt: 1, height: 6, borderRadius: 3 }}
                color="secondary"
              />
            </CardContent>
          </Card>
      </Box>

      {/* Smart Insights and Threat Map */}
      <Box
        sx={{
          display: 'grid',
          gridTemplateColumns: {
            xs: '1fr',
            md: '2fr 1fr',
          },
          gap: 3,
          mb: 4,
        }}
      >
        <Card
          sx={{
            background: (theme) => theme.palette.mode === 'dark'
              ? 'linear-gradient(135deg, rgba(30, 30, 30, 0.95) 0%, rgba(50, 50, 50, 0.95) 100%)'
              : 'linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%)',
            backdropFilter: 'blur(20px)',
            border: (theme) => `1px solid ${theme.palette.divider}`,
            borderRadius: 3,
            height: 'fit-content',
          }}
        >
            <CardContent>
              <Box display="flex" alignItems="center" gap={2} mb={3}>
                <Avatar
                  sx={{
                    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                    width: 40,
                    height: 40,
                  }}
                >
                  <InsightsIcon />
                </Avatar>
                <Typography variant="h5" fontWeight="bold">
                  Smart Security Insights
                </Typography>
                <Badge badgeContent={smartInsights.length} color="primary">
                  <NotificationsIcon />
                </Badge>
              </Box>

              <List>
                {smartInsights.map((insight, index) => (
                  <React.Fragment key={index}>
                    <ListItem
                      sx={{
                        borderRadius: 2,
                        mb: 1,
                        background: (theme) => theme.palette.mode === 'dark'
                          ? 'rgba(255, 255, 255, 0.05)'
                          : 'rgba(0, 0, 0, 0.02)',
                        '&:hover': {
                          background: (theme) => theme.palette.mode === 'dark'
                            ? 'rgba(255, 255, 255, 0.08)'
                            : 'rgba(0, 0, 0, 0.04)',
                        },
                      }}
                    >
                      <ListItemIcon>
                        {getInsightIcon(insight.type)}
                      </ListItemIcon>
                      <ListItemText
                        primary={
                          <Box display="flex" alignItems="center" gap={2}>
                            <Typography variant="subtitle1" fontWeight="600">
                              {insight.title}
                            </Typography>
                            <Chip
                              label={`${insight.confidence}% confidence`}
                              size="small"
                              color={insight.confidence > 90 ? 'success' : 'warning'}
                              variant="outlined"
                            />
                          </Box>
                        }
                        secondary={
                          <Box>
                            <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                              {insight.description}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              {insight.timestamp.toLocaleString()}
                            </Typography>
                          </Box>
                        }
                      />
                      <Tooltip title="Auto-resolve">
                        <IconButton size="small">
                          <AutoFixIcon />
                        </IconButton>
                      </Tooltip>
                    </ListItem>
                    {index < smartInsights.length - 1 && <Divider />}
                  </React.Fragment>
                ))}
              </List>
          </CardContent>
        </Card>

        <Card
          sx={{
            background: (theme) => theme.palette.mode === 'dark'
              ? 'linear-gradient(135deg, rgba(30, 30, 30, 0.95) 0%, rgba(50, 50, 50, 0.95) 100%)'
              : 'linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%)',
            backdropFilter: 'blur(20px)',
            border: (theme) => `1px solid ${theme.palette.divider}`,
            borderRadius: 3,
            height: 'fit-content',
          }}
        >
          <CardContent>
            <Box display="flex" alignItems="center" gap={2} mb={3}>
              <Avatar
                sx={{
                  background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
                  width: 40,
                  height: 40,
                }}
              >
                <TimelineIcon />
              </Avatar>
              <Typography variant="h6" fontWeight="bold">
                Global Threat Map
              </Typography>
            </Box>

            <List dense>
              {threatMap.map((region, index) => (
                <ListItem key={index} sx={{ px: 0 }}>
                  <ListItemText
                    primary={
                      <Box display="flex" alignItems="center" justifyContent="space-between">
                        <Typography variant="body2" fontWeight="600">
                          {region.region}
                        </Typography>
                        <Box display="flex" alignItems="center" gap={1}>
                          <Typography variant="body2" color="text.secondary">
                            {region.threats}
                          </Typography>
                          <Chip
                            label={region.trend}
                            size="small"
                            color={
                              region.trend === 'up' ? 'error' :
                              region.trend === 'down' ? 'success' : 'default'
                            }
                            variant="outlined"
                          />
                        </Box>
                      </Box>
                    }
                  />
                </ListItem>
              ))}
            </List>
          </CardContent>
        </Card>
      </Box>
    </Box>
  );
});

SmartDashboard.displayName = 'SmartDashboard';

export default SmartDashboard;
