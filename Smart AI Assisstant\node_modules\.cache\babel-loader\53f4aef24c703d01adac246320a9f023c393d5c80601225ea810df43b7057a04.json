{"ast": null, "code": "var _jsxFileName = \"E:\\\\PythonProjects\\\\React.js\\\\Smart AI Assisstant\\\\src\\\\components\\\\ThreatPrediction.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Card, CardContent, Typography, Grid, LinearProgress, Chip, List, ListItem, ListItemIcon, ListItemText, Avatar, IconButton, Tooltip, Alert, Accordion, AccordionSummary, AccordionDetails } from '@mui/material';\nimport { Psychology as AIIcon, TrendingUp as TrendingUpIcon, Warning as WarningIcon, Security as SecurityIcon, Timeline as TimelineIcon, Insights as InsightsIcon, ExpandMore as ExpandMoreIcon, AutoGraph as GraphIcon, Radar as RadarIcon, Shield as ShieldIcon } from '@mui/icons-material';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ThreatPrediction = /*#__PURE__*/_s(/*#__PURE__*/React.memo(_c = _s(() => {\n  _s();\n  const [predictions, setPredictions] = useState([{\n    type: 'Phishing Campaign',\n    probability: 87,\n    timeframe: '24-48 hours',\n    severity: 'high',\n    indicators: ['Increased suspicious email patterns', 'Domain registration spikes in target sectors', 'Social engineering attempts rising 45%'],\n    recommendation: 'Implement enhanced email filtering and user awareness training',\n    confidence: 92\n  }, {\n    type: 'Malware Distribution',\n    probability: 73,\n    timeframe: '3-5 days',\n    severity: 'medium',\n    indicators: ['Unusual file download patterns', 'Suspicious network traffic from known bad IPs', 'Increased vulnerability scanning attempts'],\n    recommendation: 'Update endpoint protection and monitor file execution',\n    confidence: 85\n  }, {\n    type: 'DDoS Attack',\n    probability: 45,\n    timeframe: '1-2 weeks',\n    severity: 'medium',\n    indicators: ['Botnet activity increase in target regions', 'Infrastructure reconnaissance detected', 'Historical attack pattern analysis'],\n    recommendation: 'Prepare DDoS mitigation strategies and backup systems',\n    confidence: 78\n  }]);\n  const [aiAnalysis, setAiAnalysis] = useState({\n    overallRisk: 68,\n    trendDirection: 'increasing',\n    keyFactors: ['Global cyber activity up 23%', 'New vulnerability disclosures', 'Seasonal attack patterns', 'Geopolitical tensions'],\n    modelAccuracy: 94.7,\n    lastUpdated: new Date()\n  });\n  const [threatTrends, setThreatTrends] = useState([{\n    category: 'Ransomware',\n    trend: 15,\n    direction: 'up'\n  }, {\n    category: 'Phishing',\n    trend: 8,\n    direction: 'down'\n  }, {\n    category: 'Malware',\n    trend: 22,\n    direction: 'up'\n  }, {\n    category: 'Data Breach',\n    trend: -5,\n    direction: 'down'\n  }, {\n    category: 'Social Engineering',\n    trend: 18,\n    direction: 'up'\n  }]);\n\n  // Simulate real-time AI updates\n  useEffect(() => {\n    const interval = setInterval(() => {\n      setAiAnalysis(prev => ({\n        ...prev,\n        overallRisk: Math.max(20, Math.min(95, prev.overallRisk + (Math.random() - 0.5) * 5)),\n        modelAccuracy: Math.max(90, Math.min(99, prev.modelAccuracy + (Math.random() - 0.5) * 0.5)),\n        lastUpdated: new Date()\n      }));\n    }, 10000);\n    return () => clearInterval(interval);\n  }, []);\n  const getSeverityColor = severity => {\n    switch (severity) {\n      case 'high':\n        return 'error';\n      case 'medium':\n        return 'warning';\n      case 'low':\n        return 'success';\n      default:\n        return 'default';\n    }\n  };\n  const getTrendColor = direction => {\n    switch (direction) {\n      case 'up':\n        return 'error';\n      case 'down':\n        return 'success';\n      case 'stable':\n        return 'info';\n      default:\n        return 'default';\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      p: 3\n    },\n    children: [/*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        mb: 4,\n        background: 'linear-gradient(135deg, rgba(156, 39, 176, 0.1) 0%, rgba(233, 30, 99, 0.1) 100%)',\n        border: '1px solid rgba(156, 39, 176, 0.2)',\n        position: 'relative',\n        overflow: 'hidden',\n        '&::before': {\n          content: '\"\"',\n          position: 'absolute',\n          top: 0,\n          left: 0,\n          right: 0,\n          height: '4px',\n          background: 'linear-gradient(90deg, #9c27b0 0%, #e91e63 100%)'\n        }\n      },\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          display: \"flex\",\n          alignItems: \"center\",\n          gap: 2,\n          mb: 3,\n          children: [/*#__PURE__*/_jsxDEV(Avatar, {\n            sx: {\n              background: 'linear-gradient(135deg, #9c27b0 0%, #e91e63 100%)',\n              width: 56,\n              height: 56\n            },\n            children: /*#__PURE__*/_jsxDEV(AIIcon, {\n              sx: {\n                fontSize: 28\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 161,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 154,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            flex: 1,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h4\",\n              fontWeight: \"bold\",\n              gutterBottom: true,\n              children: \"AI Threat Prediction Engine\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 164,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              color: \"text.secondary\",\n              children: \"Advanced machine learning analysis of global threat patterns\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 167,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 163,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            textAlign: \"right\",\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h3\",\n              fontWeight: \"bold\",\n              color: \"secondary\",\n              children: [aiAnalysis.overallRisk, \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 172,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: \"Risk Level\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 175,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 171,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 153,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 3,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                gutterBottom: true,\n                children: \"Model Performance\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 184,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                display: \"flex\",\n                alignItems: \"center\",\n                gap: 2,\n                mb: 2,\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  children: \"Accuracy:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 188,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(LinearProgress, {\n                  variant: \"determinate\",\n                  value: aiAnalysis.modelAccuracy,\n                  sx: {\n                    flex: 1,\n                    height: 8,\n                    borderRadius: 4\n                  },\n                  color: \"secondary\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 189,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  fontWeight: \"bold\",\n                  children: [aiAnalysis.modelAccuracy.toFixed(1), \"%\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 195,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 187,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                color: \"text.secondary\",\n                children: [\"Last updated: \", aiAnalysis.lastUpdated.toLocaleTimeString()]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 199,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 183,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 182,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              gutterBottom: true,\n              children: \"Key Risk Factors\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 205,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(List, {\n              dense: true,\n              children: aiAnalysis.keyFactors.map((factor, index) => /*#__PURE__*/_jsxDEV(ListItem, {\n                sx: {\n                  px: 0,\n                  py: 0.5\n                },\n                children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                  sx: {\n                    minWidth: 32\n                  },\n                  children: /*#__PURE__*/_jsxDEV(InsightsIcon, {\n                    fontSize: \"small\",\n                    color: \"secondary\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 212,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 211,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    children: factor\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 216,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 214,\n                  columnNumber: 21\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 210,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 208,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 204,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 181,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 152,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 134,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        lg: 8,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          sx: {\n            background: theme => theme.palette.mode === 'dark' ? 'linear-gradient(135deg, rgba(30, 30, 30, 0.95) 0%, rgba(50, 50, 50, 0.95) 100%)' : 'linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%)',\n            backdropFilter: 'blur(20px)',\n            border: theme => `1px solid ${theme.palette.divider}`\n          },\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              display: \"flex\",\n              alignItems: \"center\",\n              gap: 2,\n              mb: 3,\n              children: [/*#__PURE__*/_jsxDEV(Avatar, {\n                sx: {\n                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                  width: 48,\n                  height: 48\n                },\n                children: /*#__PURE__*/_jsxDEV(RadarIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 250,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 243,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h5\",\n                fontWeight: \"bold\",\n                children: \"Predicted Threats\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 252,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 242,\n              columnNumber: 15\n            }, this), predictions.map((prediction, index) => /*#__PURE__*/_jsxDEV(Accordion, {\n              sx: {\n                mb: 2,\n                background: theme => theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.02)',\n                '&:before': {\n                  display: 'none'\n                }\n              },\n              children: [/*#__PURE__*/_jsxDEV(AccordionSummary, {\n                expandIcon: /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 268,\n                  columnNumber: 49\n                }, this),\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  display: \"flex\",\n                  alignItems: \"center\",\n                  gap: 2,\n                  width: \"100%\",\n                  children: [/*#__PURE__*/_jsxDEV(Avatar, {\n                    sx: {\n                      background: prediction.severity === 'high' ? 'linear-gradient(135deg, #f44336 0%, #ff5722 100%)' : prediction.severity === 'medium' ? 'linear-gradient(135deg, #ff9800 0%, #ffc107 100%)' : 'linear-gradient(135deg, #4caf50 0%, #8bc34a 100%)',\n                      width: 40,\n                      height: 40\n                    },\n                    children: /*#__PURE__*/_jsxDEV(WarningIcon, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 281,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 270,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    flex: 1,\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"h6\",\n                      fontWeight: \"600\",\n                      children: prediction.type\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 284,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Box, {\n                      display: \"flex\",\n                      alignItems: \"center\",\n                      gap: 2,\n                      mt: 1,\n                      children: [/*#__PURE__*/_jsxDEV(Chip, {\n                        label: `${prediction.probability}% probability`,\n                        size: \"small\",\n                        color: prediction.probability > 80 ? 'error' : 'warning',\n                        variant: \"outlined\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 288,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                        label: prediction.timeframe,\n                        size: \"small\",\n                        variant: \"outlined\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 294,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                        label: prediction.severity,\n                        size: \"small\",\n                        color: getSeverityColor(prediction.severity)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 299,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 287,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 283,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"h4\",\n                    fontWeight: \"bold\",\n                    color: \"primary\",\n                    children: [prediction.probability, \"%\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 306,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 269,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 268,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(AccordionDetails, {\n                children: /*#__PURE__*/_jsxDEV(Grid, {\n                  container: true,\n                  spacing: 3,\n                  children: [/*#__PURE__*/_jsxDEV(Grid, {\n                    item: true,\n                    xs: 12,\n                    md: 6,\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"subtitle2\",\n                      gutterBottom: true,\n                      fontWeight: \"600\",\n                      children: \"Threat Indicators\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 314,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(List, {\n                      dense: true,\n                      children: prediction.indicators.map((indicator, idx) => /*#__PURE__*/_jsxDEV(ListItem, {\n                        sx: {\n                          px: 0\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                          sx: {\n                            minWidth: 32\n                          },\n                          children: /*#__PURE__*/_jsxDEV(SecurityIcon, {\n                            fontSize: \"small\",\n                            color: \"warning\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 321,\n                            columnNumber: 33\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 320,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                          primary: /*#__PURE__*/_jsxDEV(Typography, {\n                            variant: \"body2\",\n                            children: indicator\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 325,\n                            columnNumber: 35\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 323,\n                          columnNumber: 31\n                        }, this)]\n                      }, idx, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 319,\n                        columnNumber: 29\n                      }, this))\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 317,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 313,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                    item: true,\n                    xs: 12,\n                    md: 6,\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"subtitle2\",\n                      gutterBottom: true,\n                      fontWeight: \"600\",\n                      children: \"Recommended Actions\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 335,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Alert, {\n                      severity: getSeverityColor(prediction.severity),\n                      sx: {\n                        mb: 2\n                      },\n                      children: prediction.recommendation\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 338,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Box, {\n                      display: \"flex\",\n                      alignItems: \"center\",\n                      gap: 1,\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        color: \"text.secondary\",\n                        children: \"AI Confidence:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 345,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                        label: `${prediction.confidence}%`,\n                        size: \"small\",\n                        color: prediction.confidence > 90 ? 'success' : 'warning',\n                        variant: \"filled\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 348,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 344,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 334,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 312,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 311,\n                columnNumber: 19\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 258,\n              columnNumber: 17\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 241,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 232,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 231,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        lg: 4,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          sx: {\n            background: theme => theme.palette.mode === 'dark' ? 'linear-gradient(135deg, rgba(30, 30, 30, 0.95) 0%, rgba(50, 50, 50, 0.95) 100%)' : 'linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%)',\n            backdropFilter: 'blur(20px)',\n            border: theme => `1px solid ${theme.palette.divider}`,\n            height: 'fit-content'\n          },\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              display: \"flex\",\n              alignItems: \"center\",\n              gap: 2,\n              mb: 3,\n              children: [/*#__PURE__*/_jsxDEV(Avatar, {\n                sx: {\n                  background: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',\n                  width: 48,\n                  height: 48\n                },\n                children: /*#__PURE__*/_jsxDEV(GraphIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 385,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 378,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                fontWeight: \"bold\",\n                children: \"Threat Trends\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 387,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 377,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(List, {\n              children: threatTrends.map((trend, index) => /*#__PURE__*/_jsxDEV(ListItem, {\n                sx: {\n                  px: 0\n                },\n                children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: /*#__PURE__*/_jsxDEV(Box, {\n                    display: \"flex\",\n                    alignItems: \"center\",\n                    justifyContent: \"space-between\",\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      fontWeight: \"600\",\n                      children: trend.category\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 398,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Box, {\n                      display: \"flex\",\n                      alignItems: \"center\",\n                      gap: 1,\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"body2\",\n                        color: getTrendColor(trend.direction),\n                        fontWeight: \"bold\",\n                        children: [trend.trend > 0 ? '+' : '', trend.trend, \"%\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 402,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(TrendingUpIcon, {\n                        fontSize: \"small\",\n                        color: getTrendColor(trend.direction),\n                        sx: {\n                          transform: trend.direction === 'down' ? 'rotate(180deg)' : 'none'\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 409,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 401,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 397,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 395,\n                  columnNumber: 21\n                }, this)\n              }, index, false, {\n                fileName: _jsxFileName,\n                lineNumber: 394,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 392,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 376,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 366,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 365,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 229,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 132,\n    columnNumber: 5\n  }, this);\n}, \"ATLGVI/VIB9R608noi2nBDo5iuE=\")), \"ATLGVI/VIB9R608noi2nBDo5iuE=\");\n_c2 = ThreatPrediction;\nThreatPrediction.displayName = 'ThreatPrediction';\nexport default ThreatPrediction;\nvar _c, _c2;\n$RefreshReg$(_c, \"ThreatPrediction$React.memo\");\n$RefreshReg$(_c2, \"ThreatPrediction\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Typography", "Grid", "LinearProgress", "Chip", "List", "ListItem", "ListItemIcon", "ListItemText", "Avatar", "IconButton", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Accordion", "AccordionSummary", "AccordionDetails", "Psychology", "AIIcon", "TrendingUp", "TrendingUpIcon", "Warning", "WarningIcon", "Security", "SecurityIcon", "Timeline", "TimelineIcon", "Insights", "InsightsIcon", "ExpandMore", "ExpandMoreIcon", "AutoGraph", "GraphIcon", "Radar", "RadarIcon", "Shield", "ShieldIcon", "jsxDEV", "_jsxDEV", "ThreatPrediction", "_s", "memo", "_c", "predictions", "setPredictions", "type", "probability", "timeframe", "severity", "indicators", "recommendation", "confidence", "aiAnalysis", "setAiAnalysis", "overallRisk", "trendDirection", "keyFactors", "modelAccuracy", "lastUpdated", "Date", "threatTrends", "setThreatTrends", "category", "trend", "direction", "interval", "setInterval", "prev", "Math", "max", "min", "random", "clearInterval", "getSeverityColor", "getTrendColor", "sx", "p", "children", "mb", "background", "border", "position", "overflow", "content", "top", "left", "right", "height", "display", "alignItems", "gap", "width", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "flex", "variant", "fontWeight", "gutterBottom", "color", "textAlign", "container", "spacing", "item", "xs", "md", "value", "borderRadius", "toFixed", "toLocaleTimeString", "dense", "map", "factor", "index", "px", "py", "min<PERSON><PERSON><PERSON>", "primary", "lg", "theme", "palette", "mode", "<PERSON><PERSON>ilter", "divider", "prediction", "expandIcon", "mt", "label", "size", "indicator", "idx", "justifyContent", "transform", "_c2", "displayName", "$RefreshReg$"], "sources": ["E:/PythonProjects/React.js/Smart AI Assisstant/src/components/ThreatPrediction.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  <PERSON>,\n  Card,\n  CardContent,\n  Typography,\n  Grid,\n  LinearProgress,\n  Chip,\n  List,\n  ListItem,\n  ListItemIcon,\n  ListItemText,\n  Avatar,\n  IconButton,\n  Tooltip,\n  Alert,\n  Accordion,\n  AccordionSummary,\n  AccordionDetails,\n} from '@mui/material';\nimport {\n  Psychology as AIIcon,\n  TrendingUp as TrendingUpIcon,\n  Warning as WarningIcon,\n  Security as SecurityIcon,\n  Timeline as TimelineIcon,\n  Insights as InsightsIcon,\n  ExpandMore as ExpandMoreIcon,\n  AutoGraph as GraphIcon,\n  Radar as RadarIcon,\n  Shield as ShieldIcon,\n} from '@mui/icons-material';\n\nconst ThreatPrediction = React.memo(() => {\n  const [predictions, setPredictions] = useState([\n    {\n      type: 'Phishing Campaign',\n      probability: 87,\n      timeframe: '24-48 hours',\n      severity: 'high',\n      indicators: [\n        'Increased suspicious email patterns',\n        'Domain registration spikes in target sectors',\n        'Social engineering attempts rising 45%',\n      ],\n      recommendation: 'Implement enhanced email filtering and user awareness training',\n      confidence: 92,\n    },\n    {\n      type: 'Malware Distribution',\n      probability: 73,\n      timeframe: '3-5 days',\n      severity: 'medium',\n      indicators: [\n        'Unusual file download patterns',\n        'Suspicious network traffic from known bad IPs',\n        'Increased vulnerability scanning attempts',\n      ],\n      recommendation: 'Update endpoint protection and monitor file execution',\n      confidence: 85,\n    },\n    {\n      type: 'DDoS Attack',\n      probability: 45,\n      timeframe: '1-2 weeks',\n      severity: 'medium',\n      indicators: [\n        'Botnet activity increase in target regions',\n        'Infrastructure reconnaissance detected',\n        'Historical attack pattern analysis',\n      ],\n      recommendation: 'Prepare DDoS mitigation strategies and backup systems',\n      confidence: 78,\n    },\n  ]);\n\n  const [aiAnalysis, setAiAnalysis] = useState({\n    overallRisk: 68,\n    trendDirection: 'increasing',\n    keyFactors: [\n      'Global cyber activity up 23%',\n      'New vulnerability disclosures',\n      'Seasonal attack patterns',\n      'Geopolitical tensions',\n    ],\n    modelAccuracy: 94.7,\n    lastUpdated: new Date(),\n  });\n\n  const [threatTrends, setThreatTrends] = useState([\n    { category: 'Ransomware', trend: 15, direction: 'up' },\n    { category: 'Phishing', trend: 8, direction: 'down' },\n    { category: 'Malware', trend: 22, direction: 'up' },\n    { category: 'Data Breach', trend: -5, direction: 'down' },\n    { category: 'Social Engineering', trend: 18, direction: 'up' },\n  ]);\n\n  // Simulate real-time AI updates\n  useEffect(() => {\n    const interval = setInterval(() => {\n      setAiAnalysis(prev => ({\n        ...prev,\n        overallRisk: Math.max(20, Math.min(95, prev.overallRisk + (Math.random() - 0.5) * 5)),\n        modelAccuracy: Math.max(90, Math.min(99, prev.modelAccuracy + (Math.random() - 0.5) * 0.5)),\n        lastUpdated: new Date(),\n      }));\n    }, 10000);\n\n    return () => clearInterval(interval);\n  }, []);\n\n  const getSeverityColor = (severity) => {\n    switch (severity) {\n      case 'high': return 'error';\n      case 'medium': return 'warning';\n      case 'low': return 'success';\n      default: return 'default';\n    }\n  };\n\n  const getTrendColor = (direction) => {\n    switch (direction) {\n      case 'up': return 'error';\n      case 'down': return 'success';\n      case 'stable': return 'info';\n      default: return 'default';\n    }\n  };\n\n  return (\n    <Box sx={{ p: 3 }}>\n      {/* AI Analysis Overview */}\n      <Card\n        sx={{\n          mb: 4,\n          background: 'linear-gradient(135deg, rgba(156, 39, 176, 0.1) 0%, rgba(233, 30, 99, 0.1) 100%)',\n          border: '1px solid rgba(156, 39, 176, 0.2)',\n          position: 'relative',\n          overflow: 'hidden',\n          '&::before': {\n            content: '\"\"',\n            position: 'absolute',\n            top: 0,\n            left: 0,\n            right: 0,\n            height: '4px',\n            background: 'linear-gradient(90deg, #9c27b0 0%, #e91e63 100%)',\n          },\n        }}\n      >\n        <CardContent>\n          <Box display=\"flex\" alignItems=\"center\" gap={2} mb={3}>\n            <Avatar\n              sx={{\n                background: 'linear-gradient(135deg, #9c27b0 0%, #e91e63 100%)',\n                width: 56,\n                height: 56,\n              }}\n            >\n              <AIIcon sx={{ fontSize: 28 }} />\n            </Avatar>\n            <Box flex={1}>\n              <Typography variant=\"h4\" fontWeight=\"bold\" gutterBottom>\n                AI Threat Prediction Engine\n              </Typography>\n              <Typography variant=\"body1\" color=\"text.secondary\">\n                Advanced machine learning analysis of global threat patterns\n              </Typography>\n            </Box>\n            <Box textAlign=\"right\">\n              <Typography variant=\"h3\" fontWeight=\"bold\" color=\"secondary\">\n                {aiAnalysis.overallRisk}%\n              </Typography>\n              <Typography variant=\"body2\" color=\"text.secondary\">\n                Risk Level\n              </Typography>\n            </Box>\n          </Box>\n\n          <Grid container spacing={3}>\n            <Grid item xs={12} md={6}>\n              <Box>\n                <Typography variant=\"h6\" gutterBottom>\n                  Model Performance\n                </Typography>\n                <Box display=\"flex\" alignItems=\"center\" gap={2} mb={2}>\n                  <Typography variant=\"body2\">Accuracy:</Typography>\n                  <LinearProgress\n                    variant=\"determinate\"\n                    value={aiAnalysis.modelAccuracy}\n                    sx={{ flex: 1, height: 8, borderRadius: 4 }}\n                    color=\"secondary\"\n                  />\n                  <Typography variant=\"body2\" fontWeight=\"bold\">\n                    {aiAnalysis.modelAccuracy.toFixed(1)}%\n                  </Typography>\n                </Box>\n                <Typography variant=\"caption\" color=\"text.secondary\">\n                  Last updated: {aiAnalysis.lastUpdated.toLocaleTimeString()}\n                </Typography>\n              </Box>\n            </Grid>\n            <Grid item xs={12} md={6}>\n              <Typography variant=\"h6\" gutterBottom>\n                Key Risk Factors\n              </Typography>\n              <List dense>\n                {aiAnalysis.keyFactors.map((factor, index) => (\n                  <ListItem key={index} sx={{ px: 0, py: 0.5 }}>\n                    <ListItemIcon sx={{ minWidth: 32 }}>\n                      <InsightsIcon fontSize=\"small\" color=\"secondary\" />\n                    </ListItemIcon>\n                    <ListItemText\n                      primary={\n                        <Typography variant=\"body2\">\n                          {factor}\n                        </Typography>\n                      }\n                    />\n                  </ListItem>\n                ))}\n              </List>\n            </Grid>\n          </Grid>\n        </CardContent>\n      </Card>\n\n      <Grid container spacing={3}>\n        {/* Threat Predictions */}\n        <Grid item xs={12} lg={8}>\n          <Card\n            sx={{\n              background: (theme) => theme.palette.mode === 'dark'\n                ? 'linear-gradient(135deg, rgba(30, 30, 30, 0.95) 0%, rgba(50, 50, 50, 0.95) 100%)'\n                : 'linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%)',\n              backdropFilter: 'blur(20px)',\n              border: (theme) => `1px solid ${theme.palette.divider}`,\n            }}\n          >\n            <CardContent>\n              <Box display=\"flex\" alignItems=\"center\" gap={2} mb={3}>\n                <Avatar\n                  sx={{\n                    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                    width: 48,\n                    height: 48,\n                  }}\n                >\n                  <RadarIcon />\n                </Avatar>\n                <Typography variant=\"h5\" fontWeight=\"bold\">\n                  Predicted Threats\n                </Typography>\n              </Box>\n\n              {predictions.map((prediction, index) => (\n                <Accordion\n                  key={index}\n                  sx={{\n                    mb: 2,\n                    background: (theme) => theme.palette.mode === 'dark'\n                      ? 'rgba(255, 255, 255, 0.05)'\n                      : 'rgba(0, 0, 0, 0.02)',\n                    '&:before': { display: 'none' },\n                  }}\n                >\n                  <AccordionSummary expandIcon={<ExpandMoreIcon />}>\n                    <Box display=\"flex\" alignItems=\"center\" gap={2} width=\"100%\">\n                      <Avatar\n                        sx={{\n                          background: prediction.severity === 'high' \n                            ? 'linear-gradient(135deg, #f44336 0%, #ff5722 100%)'\n                            : prediction.severity === 'medium'\n                            ? 'linear-gradient(135deg, #ff9800 0%, #ffc107 100%)'\n                            : 'linear-gradient(135deg, #4caf50 0%, #8bc34a 100%)',\n                          width: 40,\n                          height: 40,\n                        }}\n                      >\n                        <WarningIcon />\n                      </Avatar>\n                      <Box flex={1}>\n                        <Typography variant=\"h6\" fontWeight=\"600\">\n                          {prediction.type}\n                        </Typography>\n                        <Box display=\"flex\" alignItems=\"center\" gap={2} mt={1}>\n                          <Chip\n                            label={`${prediction.probability}% probability`}\n                            size=\"small\"\n                            color={prediction.probability > 80 ? 'error' : 'warning'}\n                            variant=\"outlined\"\n                          />\n                          <Chip\n                            label={prediction.timeframe}\n                            size=\"small\"\n                            variant=\"outlined\"\n                          />\n                          <Chip\n                            label={prediction.severity}\n                            size=\"small\"\n                            color={getSeverityColor(prediction.severity)}\n                          />\n                        </Box>\n                      </Box>\n                      <Typography variant=\"h4\" fontWeight=\"bold\" color=\"primary\">\n                        {prediction.probability}%\n                      </Typography>\n                    </Box>\n                  </AccordionSummary>\n                  <AccordionDetails>\n                    <Grid container spacing={3}>\n                      <Grid item xs={12} md={6}>\n                        <Typography variant=\"subtitle2\" gutterBottom fontWeight=\"600\">\n                          Threat Indicators\n                        </Typography>\n                        <List dense>\n                          {prediction.indicators.map((indicator, idx) => (\n                            <ListItem key={idx} sx={{ px: 0 }}>\n                              <ListItemIcon sx={{ minWidth: 32 }}>\n                                <SecurityIcon fontSize=\"small\" color=\"warning\" />\n                              </ListItemIcon>\n                              <ListItemText\n                                primary={\n                                  <Typography variant=\"body2\">\n                                    {indicator}\n                                  </Typography>\n                                }\n                              />\n                            </ListItem>\n                          ))}\n                        </List>\n                      </Grid>\n                      <Grid item xs={12} md={6}>\n                        <Typography variant=\"subtitle2\" gutterBottom fontWeight=\"600\">\n                          Recommended Actions\n                        </Typography>\n                        <Alert\n                          severity={getSeverityColor(prediction.severity)}\n                          sx={{ mb: 2 }}\n                        >\n                          {prediction.recommendation}\n                        </Alert>\n                        <Box display=\"flex\" alignItems=\"center\" gap={1}>\n                          <Typography variant=\"caption\" color=\"text.secondary\">\n                            AI Confidence:\n                          </Typography>\n                          <Chip\n                            label={`${prediction.confidence}%`}\n                            size=\"small\"\n                            color={prediction.confidence > 90 ? 'success' : 'warning'}\n                            variant=\"filled\"\n                          />\n                        </Box>\n                      </Grid>\n                    </Grid>\n                  </AccordionDetails>\n                </Accordion>\n              ))}\n            </CardContent>\n          </Card>\n        </Grid>\n\n        {/* Threat Trends */}\n        <Grid item xs={12} lg={4}>\n          <Card\n            sx={{\n              background: (theme) => theme.palette.mode === 'dark'\n                ? 'linear-gradient(135deg, rgba(30, 30, 30, 0.95) 0%, rgba(50, 50, 50, 0.95) 100%)'\n                : 'linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%)',\n              backdropFilter: 'blur(20px)',\n              border: (theme) => `1px solid ${theme.palette.divider}`,\n              height: 'fit-content',\n            }}\n          >\n            <CardContent>\n              <Box display=\"flex\" alignItems=\"center\" gap={2} mb={3}>\n                <Avatar\n                  sx={{\n                    background: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',\n                    width: 48,\n                    height: 48,\n                  }}\n                >\n                  <GraphIcon />\n                </Avatar>\n                <Typography variant=\"h6\" fontWeight=\"bold\">\n                  Threat Trends\n                </Typography>\n              </Box>\n\n              <List>\n                {threatTrends.map((trend, index) => (\n                  <ListItem key={index} sx={{ px: 0 }}>\n                    <ListItemText\n                      primary={\n                        <Box display=\"flex\" alignItems=\"center\" justifyContent=\"space-between\">\n                          <Typography variant=\"body2\" fontWeight=\"600\">\n                            {trend.category}\n                          </Typography>\n                          <Box display=\"flex\" alignItems=\"center\" gap={1}>\n                            <Typography\n                              variant=\"body2\"\n                              color={getTrendColor(trend.direction)}\n                              fontWeight=\"bold\"\n                            >\n                              {trend.trend > 0 ? '+' : ''}{trend.trend}%\n                            </Typography>\n                            <TrendingUpIcon\n                              fontSize=\"small\"\n                              color={getTrendColor(trend.direction)}\n                              sx={{\n                                transform: trend.direction === 'down' ? 'rotate(180deg)' : 'none',\n                              }}\n                            />\n                          </Box>\n                        </Box>\n                      }\n                    />\n                  </ListItem>\n                ))}\n              </List>\n            </CardContent>\n          </Card>\n        </Grid>\n      </Grid>\n    </Box>\n  );\n});\n\nThreatPrediction.displayName = 'ThreatPrediction';\n\nexport default ThreatPrediction;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,IAAI,EACJC,cAAc,EACdC,IAAI,EACJC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,YAAY,EACZC,MAAM,EACNC,UAAU,EACVC,OAAO,EACPC,KAAK,EACLC,SAAS,EACTC,gBAAgB,EAChBC,gBAAgB,QACX,eAAe;AACtB,SACEC,UAAU,IAAIC,MAAM,EACpBC,UAAU,IAAIC,cAAc,EAC5BC,OAAO,IAAIC,WAAW,EACtBC,QAAQ,IAAIC,YAAY,EACxBC,QAAQ,IAAIC,YAAY,EACxBC,QAAQ,IAAIC,YAAY,EACxBC,UAAU,IAAIC,cAAc,EAC5BC,SAAS,IAAIC,SAAS,EACtBC,KAAK,IAAIC,SAAS,EAClBC,MAAM,IAAIC,UAAU,QACf,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7B,MAAMC,gBAAgB,gBAAAC,EAAA,cAAG5C,KAAK,CAAC6C,IAAI,CAAAC,EAAA,GAAAF,EAAA,CAAC,MAAM;EAAAA,EAAA;EACxC,MAAM,CAACG,WAAW,EAAEC,cAAc,CAAC,GAAG/C,QAAQ,CAAC,CAC7C;IACEgD,IAAI,EAAE,mBAAmB;IACzBC,WAAW,EAAE,EAAE;IACfC,SAAS,EAAE,aAAa;IACxBC,QAAQ,EAAE,MAAM;IAChBC,UAAU,EAAE,CACV,qCAAqC,EACrC,8CAA8C,EAC9C,wCAAwC,CACzC;IACDC,cAAc,EAAE,gEAAgE;IAChFC,UAAU,EAAE;EACd,CAAC,EACD;IACEN,IAAI,EAAE,sBAAsB;IAC5BC,WAAW,EAAE,EAAE;IACfC,SAAS,EAAE,UAAU;IACrBC,QAAQ,EAAE,QAAQ;IAClBC,UAAU,EAAE,CACV,gCAAgC,EAChC,+CAA+C,EAC/C,2CAA2C,CAC5C;IACDC,cAAc,EAAE,uDAAuD;IACvEC,UAAU,EAAE;EACd,CAAC,EACD;IACEN,IAAI,EAAE,aAAa;IACnBC,WAAW,EAAE,EAAE;IACfC,SAAS,EAAE,WAAW;IACtBC,QAAQ,EAAE,QAAQ;IAClBC,UAAU,EAAE,CACV,4CAA4C,EAC5C,wCAAwC,EACxC,oCAAoC,CACrC;IACDC,cAAc,EAAE,uDAAuD;IACvEC,UAAU,EAAE;EACd,CAAC,CACF,CAAC;EAEF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGxD,QAAQ,CAAC;IAC3CyD,WAAW,EAAE,EAAE;IACfC,cAAc,EAAE,YAAY;IAC5BC,UAAU,EAAE,CACV,8BAA8B,EAC9B,+BAA+B,EAC/B,0BAA0B,EAC1B,uBAAuB,CACxB;IACDC,aAAa,EAAE,IAAI;IACnBC,WAAW,EAAE,IAAIC,IAAI,CAAC;EACxB,CAAC,CAAC;EAEF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGhE,QAAQ,CAAC,CAC/C;IAAEiE,QAAQ,EAAE,YAAY;IAAEC,KAAK,EAAE,EAAE;IAAEC,SAAS,EAAE;EAAK,CAAC,EACtD;IAAEF,QAAQ,EAAE,UAAU;IAAEC,KAAK,EAAE,CAAC;IAAEC,SAAS,EAAE;EAAO,CAAC,EACrD;IAAEF,QAAQ,EAAE,SAAS;IAAEC,KAAK,EAAE,EAAE;IAAEC,SAAS,EAAE;EAAK,CAAC,EACnD;IAAEF,QAAQ,EAAE,aAAa;IAAEC,KAAK,EAAE,CAAC,CAAC;IAAEC,SAAS,EAAE;EAAO,CAAC,EACzD;IAAEF,QAAQ,EAAE,oBAAoB;IAAEC,KAAK,EAAE,EAAE;IAAEC,SAAS,EAAE;EAAK,CAAC,CAC/D,CAAC;;EAEF;EACAlE,SAAS,CAAC,MAAM;IACd,MAAMmE,QAAQ,GAAGC,WAAW,CAAC,MAAM;MACjCb,aAAa,CAACc,IAAI,KAAK;QACrB,GAAGA,IAAI;QACPb,WAAW,EAAEc,IAAI,CAACC,GAAG,CAAC,EAAE,EAAED,IAAI,CAACE,GAAG,CAAC,EAAE,EAAEH,IAAI,CAACb,WAAW,GAAG,CAACc,IAAI,CAACG,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC;QACrFd,aAAa,EAAEW,IAAI,CAACC,GAAG,CAAC,EAAE,EAAED,IAAI,CAACE,GAAG,CAAC,EAAE,EAAEH,IAAI,CAACV,aAAa,GAAG,CAACW,IAAI,CAACG,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI,GAAG,CAAC,CAAC;QAC3Fb,WAAW,EAAE,IAAIC,IAAI,CAAC;MACxB,CAAC,CAAC,CAAC;IACL,CAAC,EAAE,KAAK,CAAC;IAET,OAAO,MAAMa,aAAa,CAACP,QAAQ,CAAC;EACtC,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMQ,gBAAgB,GAAIzB,QAAQ,IAAK;IACrC,QAAQA,QAAQ;MACd,KAAK,MAAM;QAAE,OAAO,OAAO;MAC3B,KAAK,QAAQ;QAAE,OAAO,SAAS;MAC/B,KAAK,KAAK;QAAE,OAAO,SAAS;MAC5B;QAAS,OAAO,SAAS;IAC3B;EACF,CAAC;EAED,MAAM0B,aAAa,GAAIV,SAAS,IAAK;IACnC,QAAQA,SAAS;MACf,KAAK,IAAI;QAAE,OAAO,OAAO;MACzB,KAAK,MAAM;QAAE,OAAO,SAAS;MAC7B,KAAK,QAAQ;QAAE,OAAO,MAAM;MAC5B;QAAS,OAAO,SAAS;IAC3B;EACF,CAAC;EAED,oBACE1B,OAAA,CAACvC,GAAG;IAAC4E,EAAE,EAAE;MAAEC,CAAC,EAAE;IAAE,CAAE;IAAAC,QAAA,gBAEhBvC,OAAA,CAACtC,IAAI;MACH2E,EAAE,EAAE;QACFG,EAAE,EAAE,CAAC;QACLC,UAAU,EAAE,kFAAkF;QAC9FC,MAAM,EAAE,mCAAmC;QAC3CC,QAAQ,EAAE,UAAU;QACpBC,QAAQ,EAAE,QAAQ;QAClB,WAAW,EAAE;UACXC,OAAO,EAAE,IAAI;UACbF,QAAQ,EAAE,UAAU;UACpBG,GAAG,EAAE,CAAC;UACNC,IAAI,EAAE,CAAC;UACPC,KAAK,EAAE,CAAC;UACRC,MAAM,EAAE,KAAK;UACbR,UAAU,EAAE;QACd;MACF,CAAE;MAAAF,QAAA,eAEFvC,OAAA,CAACrC,WAAW;QAAA4E,QAAA,gBACVvC,OAAA,CAACvC,GAAG;UAACyF,OAAO,EAAC,MAAM;UAACC,UAAU,EAAC,QAAQ;UAACC,GAAG,EAAE,CAAE;UAACZ,EAAE,EAAE,CAAE;UAAAD,QAAA,gBACpDvC,OAAA,CAAC5B,MAAM;YACLiE,EAAE,EAAE;cACFI,UAAU,EAAE,mDAAmD;cAC/DY,KAAK,EAAE,EAAE;cACTJ,MAAM,EAAE;YACV,CAAE;YAAAV,QAAA,eAEFvC,OAAA,CAACpB,MAAM;cAACyD,EAAE,EAAE;gBAAEiB,QAAQ,EAAE;cAAG;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC,eACT1D,OAAA,CAACvC,GAAG;YAACkG,IAAI,EAAE,CAAE;YAAApB,QAAA,gBACXvC,OAAA,CAACpC,UAAU;cAACgG,OAAO,EAAC,IAAI;cAACC,UAAU,EAAC,MAAM;cAACC,YAAY;cAAAvB,QAAA,EAAC;YAExD;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb1D,OAAA,CAACpC,UAAU;cAACgG,OAAO,EAAC,OAAO;cAACG,KAAK,EAAC,gBAAgB;cAAAxB,QAAA,EAAC;YAEnD;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACN1D,OAAA,CAACvC,GAAG;YAACuG,SAAS,EAAC,OAAO;YAAAzB,QAAA,gBACpBvC,OAAA,CAACpC,UAAU;cAACgG,OAAO,EAAC,IAAI;cAACC,UAAU,EAAC,MAAM;cAACE,KAAK,EAAC,WAAW;cAAAxB,QAAA,GACzDzB,UAAU,CAACE,WAAW,EAAC,GAC1B;YAAA;cAAAuC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb1D,OAAA,CAACpC,UAAU;cAACgG,OAAO,EAAC,OAAO;cAACG,KAAK,EAAC,gBAAgB;cAAAxB,QAAA,EAAC;YAEnD;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN1D,OAAA,CAACnC,IAAI;UAACoG,SAAS;UAACC,OAAO,EAAE,CAAE;UAAA3B,QAAA,gBACzBvC,OAAA,CAACnC,IAAI;YAACsG,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA9B,QAAA,eACvBvC,OAAA,CAACvC,GAAG;cAAA8E,QAAA,gBACFvC,OAAA,CAACpC,UAAU;gBAACgG,OAAO,EAAC,IAAI;gBAACE,YAAY;gBAAAvB,QAAA,EAAC;cAEtC;gBAAAgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACb1D,OAAA,CAACvC,GAAG;gBAACyF,OAAO,EAAC,MAAM;gBAACC,UAAU,EAAC,QAAQ;gBAACC,GAAG,EAAE,CAAE;gBAACZ,EAAE,EAAE,CAAE;gBAAAD,QAAA,gBACpDvC,OAAA,CAACpC,UAAU;kBAACgG,OAAO,EAAC,OAAO;kBAAArB,QAAA,EAAC;gBAAS;kBAAAgB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAClD1D,OAAA,CAAClC,cAAc;kBACb8F,OAAO,EAAC,aAAa;kBACrBU,KAAK,EAAExD,UAAU,CAACK,aAAc;kBAChCkB,EAAE,EAAE;oBAAEsB,IAAI,EAAE,CAAC;oBAAEV,MAAM,EAAE,CAAC;oBAAEsB,YAAY,EAAE;kBAAE,CAAE;kBAC5CR,KAAK,EAAC;gBAAW;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClB,CAAC,eACF1D,OAAA,CAACpC,UAAU;kBAACgG,OAAO,EAAC,OAAO;kBAACC,UAAU,EAAC,MAAM;kBAAAtB,QAAA,GAC1CzB,UAAU,CAACK,aAAa,CAACqD,OAAO,CAAC,CAAC,CAAC,EAAC,GACvC;gBAAA;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACN1D,OAAA,CAACpC,UAAU;gBAACgG,OAAO,EAAC,SAAS;gBAACG,KAAK,EAAC,gBAAgB;gBAAAxB,QAAA,GAAC,gBACrC,EAACzB,UAAU,CAACM,WAAW,CAACqD,kBAAkB,CAAC,CAAC;cAAA;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACP1D,OAAA,CAACnC,IAAI;YAACsG,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA9B,QAAA,gBACvBvC,OAAA,CAACpC,UAAU;cAACgG,OAAO,EAAC,IAAI;cAACE,YAAY;cAAAvB,QAAA,EAAC;YAEtC;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb1D,OAAA,CAAChC,IAAI;cAAC0G,KAAK;cAAAnC,QAAA,EACRzB,UAAU,CAACI,UAAU,CAACyD,GAAG,CAAC,CAACC,MAAM,EAAEC,KAAK,kBACvC7E,OAAA,CAAC/B,QAAQ;gBAAaoE,EAAE,EAAE;kBAAEyC,EAAE,EAAE,CAAC;kBAAEC,EAAE,EAAE;gBAAI,CAAE;gBAAAxC,QAAA,gBAC3CvC,OAAA,CAAC9B,YAAY;kBAACmE,EAAE,EAAE;oBAAE2C,QAAQ,EAAE;kBAAG,CAAE;kBAAAzC,QAAA,eACjCvC,OAAA,CAACV,YAAY;oBAACgE,QAAQ,EAAC,OAAO;oBAACS,KAAK,EAAC;kBAAW;oBAAAR,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvC,CAAC,eACf1D,OAAA,CAAC7B,YAAY;kBACX8G,OAAO,eACLjF,OAAA,CAACpC,UAAU;oBAACgG,OAAO,EAAC,OAAO;oBAAArB,QAAA,EACxBqC;kBAAM;oBAAArB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACG;gBACb;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC;cAAA,GAVWmB,KAAK;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAWV,CACX;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAEP1D,OAAA,CAACnC,IAAI;MAACoG,SAAS;MAACC,OAAO,EAAE,CAAE;MAAA3B,QAAA,gBAEzBvC,OAAA,CAACnC,IAAI;QAACsG,IAAI;QAACC,EAAE,EAAE,EAAG;QAACc,EAAE,EAAE,CAAE;QAAA3C,QAAA,eACvBvC,OAAA,CAACtC,IAAI;UACH2E,EAAE,EAAE;YACFI,UAAU,EAAG0C,KAAK,IAAKA,KAAK,CAACC,OAAO,CAACC,IAAI,KAAK,MAAM,GAChD,iFAAiF,GACjF,uFAAuF;YAC3FC,cAAc,EAAE,YAAY;YAC5B5C,MAAM,EAAGyC,KAAK,IAAK,aAAaA,KAAK,CAACC,OAAO,CAACG,OAAO;UACvD,CAAE;UAAAhD,QAAA,eAEFvC,OAAA,CAACrC,WAAW;YAAA4E,QAAA,gBACVvC,OAAA,CAACvC,GAAG;cAACyF,OAAO,EAAC,MAAM;cAACC,UAAU,EAAC,QAAQ;cAACC,GAAG,EAAE,CAAE;cAACZ,EAAE,EAAE,CAAE;cAAAD,QAAA,gBACpDvC,OAAA,CAAC5B,MAAM;gBACLiE,EAAE,EAAE;kBACFI,UAAU,EAAE,mDAAmD;kBAC/DY,KAAK,EAAE,EAAE;kBACTJ,MAAM,EAAE;gBACV,CAAE;gBAAAV,QAAA,eAEFvC,OAAA,CAACJ,SAAS;kBAAA2D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP,CAAC,eACT1D,OAAA,CAACpC,UAAU;gBAACgG,OAAO,EAAC,IAAI;gBAACC,UAAU,EAAC,MAAM;gBAAAtB,QAAA,EAAC;cAE3C;gBAAAgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,EAELrD,WAAW,CAACsE,GAAG,CAAC,CAACa,UAAU,EAAEX,KAAK,kBACjC7E,OAAA,CAACxB,SAAS;cAER6D,EAAE,EAAE;gBACFG,EAAE,EAAE,CAAC;gBACLC,UAAU,EAAG0C,KAAK,IAAKA,KAAK,CAACC,OAAO,CAACC,IAAI,KAAK,MAAM,GAChD,2BAA2B,GAC3B,qBAAqB;gBACzB,UAAU,EAAE;kBAAEnC,OAAO,EAAE;gBAAO;cAChC,CAAE;cAAAX,QAAA,gBAEFvC,OAAA,CAACvB,gBAAgB;gBAACgH,UAAU,eAAEzF,OAAA,CAACR,cAAc;kBAAA+D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAAAnB,QAAA,eAC/CvC,OAAA,CAACvC,GAAG;kBAACyF,OAAO,EAAC,MAAM;kBAACC,UAAU,EAAC,QAAQ;kBAACC,GAAG,EAAE,CAAE;kBAACC,KAAK,EAAC,MAAM;kBAAAd,QAAA,gBAC1DvC,OAAA,CAAC5B,MAAM;oBACLiE,EAAE,EAAE;sBACFI,UAAU,EAAE+C,UAAU,CAAC9E,QAAQ,KAAK,MAAM,GACtC,mDAAmD,GACnD8E,UAAU,CAAC9E,QAAQ,KAAK,QAAQ,GAChC,mDAAmD,GACnD,mDAAmD;sBACvD2C,KAAK,EAAE,EAAE;sBACTJ,MAAM,EAAE;oBACV,CAAE;oBAAAV,QAAA,eAEFvC,OAAA,CAAChB,WAAW;sBAAAuE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CAAC,eACT1D,OAAA,CAACvC,GAAG;oBAACkG,IAAI,EAAE,CAAE;oBAAApB,QAAA,gBACXvC,OAAA,CAACpC,UAAU;sBAACgG,OAAO,EAAC,IAAI;sBAACC,UAAU,EAAC,KAAK;sBAAAtB,QAAA,EACtCiD,UAAU,CAACjF;oBAAI;sBAAAgD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN,CAAC,eACb1D,OAAA,CAACvC,GAAG;sBAACyF,OAAO,EAAC,MAAM;sBAACC,UAAU,EAAC,QAAQ;sBAACC,GAAG,EAAE,CAAE;sBAACsC,EAAE,EAAE,CAAE;sBAAAnD,QAAA,gBACpDvC,OAAA,CAACjC,IAAI;wBACH4H,KAAK,EAAE,GAAGH,UAAU,CAAChF,WAAW,eAAgB;wBAChDoF,IAAI,EAAC,OAAO;wBACZ7B,KAAK,EAAEyB,UAAU,CAAChF,WAAW,GAAG,EAAE,GAAG,OAAO,GAAG,SAAU;wBACzDoD,OAAO,EAAC;sBAAU;wBAAAL,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnB,CAAC,eACF1D,OAAA,CAACjC,IAAI;wBACH4H,KAAK,EAAEH,UAAU,CAAC/E,SAAU;wBAC5BmF,IAAI,EAAC,OAAO;wBACZhC,OAAO,EAAC;sBAAU;wBAAAL,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnB,CAAC,eACF1D,OAAA,CAACjC,IAAI;wBACH4H,KAAK,EAAEH,UAAU,CAAC9E,QAAS;wBAC3BkF,IAAI,EAAC,OAAO;wBACZ7B,KAAK,EAAE5B,gBAAgB,CAACqD,UAAU,CAAC9E,QAAQ;sBAAE;wBAAA6C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC9C,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACN1D,OAAA,CAACpC,UAAU;oBAACgG,OAAO,EAAC,IAAI;oBAACC,UAAU,EAAC,MAAM;oBAACE,KAAK,EAAC,SAAS;oBAAAxB,QAAA,GACvDiD,UAAU,CAAChF,WAAW,EAAC,GAC1B;kBAAA;oBAAA+C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACU,CAAC,eACnB1D,OAAA,CAACtB,gBAAgB;gBAAA6D,QAAA,eACfvC,OAAA,CAACnC,IAAI;kBAACoG,SAAS;kBAACC,OAAO,EAAE,CAAE;kBAAA3B,QAAA,gBACzBvC,OAAA,CAACnC,IAAI;oBAACsG,IAAI;oBAACC,EAAE,EAAE,EAAG;oBAACC,EAAE,EAAE,CAAE;oBAAA9B,QAAA,gBACvBvC,OAAA,CAACpC,UAAU;sBAACgG,OAAO,EAAC,WAAW;sBAACE,YAAY;sBAACD,UAAU,EAAC,KAAK;sBAAAtB,QAAA,EAAC;oBAE9D;sBAAAgB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACb1D,OAAA,CAAChC,IAAI;sBAAC0G,KAAK;sBAAAnC,QAAA,EACRiD,UAAU,CAAC7E,UAAU,CAACgE,GAAG,CAAC,CAACkB,SAAS,EAAEC,GAAG,kBACxC9F,OAAA,CAAC/B,QAAQ;wBAAWoE,EAAE,EAAE;0BAAEyC,EAAE,EAAE;wBAAE,CAAE;wBAAAvC,QAAA,gBAChCvC,OAAA,CAAC9B,YAAY;0BAACmE,EAAE,EAAE;4BAAE2C,QAAQ,EAAE;0BAAG,CAAE;0BAAAzC,QAAA,eACjCvC,OAAA,CAACd,YAAY;4BAACoE,QAAQ,EAAC,OAAO;4BAACS,KAAK,EAAC;0BAAS;4BAAAR,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACrC,CAAC,eACf1D,OAAA,CAAC7B,YAAY;0BACX8G,OAAO,eACLjF,OAAA,CAACpC,UAAU;4BAACgG,OAAO,EAAC,OAAO;4BAAArB,QAAA,EACxBsD;0BAAS;4BAAAtC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACA;wBACb;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACF,CAAC;sBAAA,GAVWoC,GAAG;wBAAAvC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAWR,CACX;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACP1D,OAAA,CAACnC,IAAI;oBAACsG,IAAI;oBAACC,EAAE,EAAE,EAAG;oBAACC,EAAE,EAAE,CAAE;oBAAA9B,QAAA,gBACvBvC,OAAA,CAACpC,UAAU;sBAACgG,OAAO,EAAC,WAAW;sBAACE,YAAY;sBAACD,UAAU,EAAC,KAAK;sBAAAtB,QAAA,EAAC;oBAE9D;sBAAAgB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACb1D,OAAA,CAACzB,KAAK;sBACJmC,QAAQ,EAAEyB,gBAAgB,CAACqD,UAAU,CAAC9E,QAAQ,CAAE;sBAChD2B,EAAE,EAAE;wBAAEG,EAAE,EAAE;sBAAE,CAAE;sBAAAD,QAAA,EAEbiD,UAAU,CAAC5E;oBAAc;sBAAA2C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrB,CAAC,eACR1D,OAAA,CAACvC,GAAG;sBAACyF,OAAO,EAAC,MAAM;sBAACC,UAAU,EAAC,QAAQ;sBAACC,GAAG,EAAE,CAAE;sBAAAb,QAAA,gBAC7CvC,OAAA,CAACpC,UAAU;wBAACgG,OAAO,EAAC,SAAS;wBAACG,KAAK,EAAC,gBAAgB;wBAAAxB,QAAA,EAAC;sBAErD;wBAAAgB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eACb1D,OAAA,CAACjC,IAAI;wBACH4H,KAAK,EAAE,GAAGH,UAAU,CAAC3E,UAAU,GAAI;wBACnC+E,IAAI,EAAC,OAAO;wBACZ7B,KAAK,EAAEyB,UAAU,CAAC3E,UAAU,GAAG,EAAE,GAAG,SAAS,GAAG,SAAU;wBAC1D+C,OAAO,EAAC;sBAAQ;wBAAAL,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACjB,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACS,CAAC;YAAA,GAlGdmB,KAAK;cAAAtB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAmGD,CACZ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACS;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGP1D,OAAA,CAACnC,IAAI;QAACsG,IAAI;QAACC,EAAE,EAAE,EAAG;QAACc,EAAE,EAAE,CAAE;QAAA3C,QAAA,eACvBvC,OAAA,CAACtC,IAAI;UACH2E,EAAE,EAAE;YACFI,UAAU,EAAG0C,KAAK,IAAKA,KAAK,CAACC,OAAO,CAACC,IAAI,KAAK,MAAM,GAChD,iFAAiF,GACjF,uFAAuF;YAC3FC,cAAc,EAAE,YAAY;YAC5B5C,MAAM,EAAGyC,KAAK,IAAK,aAAaA,KAAK,CAACC,OAAO,CAACG,OAAO,EAAE;YACvDtC,MAAM,EAAE;UACV,CAAE;UAAAV,QAAA,eAEFvC,OAAA,CAACrC,WAAW;YAAA4E,QAAA,gBACVvC,OAAA,CAACvC,GAAG;cAACyF,OAAO,EAAC,MAAM;cAACC,UAAU,EAAC,QAAQ;cAACC,GAAG,EAAE,CAAE;cAACZ,EAAE,EAAE,CAAE;cAAAD,QAAA,gBACpDvC,OAAA,CAAC5B,MAAM;gBACLiE,EAAE,EAAE;kBACFI,UAAU,EAAE,mDAAmD;kBAC/DY,KAAK,EAAE,EAAE;kBACTJ,MAAM,EAAE;gBACV,CAAE;gBAAAV,QAAA,eAEFvC,OAAA,CAACN,SAAS;kBAAA6D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP,CAAC,eACT1D,OAAA,CAACpC,UAAU;gBAACgG,OAAO,EAAC,IAAI;gBAACC,UAAU,EAAC,MAAM;gBAAAtB,QAAA,EAAC;cAE3C;gBAAAgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAEN1D,OAAA,CAAChC,IAAI;cAAAuE,QAAA,EACFjB,YAAY,CAACqD,GAAG,CAAC,CAAClD,KAAK,EAAEoD,KAAK,kBAC7B7E,OAAA,CAAC/B,QAAQ;gBAAaoE,EAAE,EAAE;kBAAEyC,EAAE,EAAE;gBAAE,CAAE;gBAAAvC,QAAA,eAClCvC,OAAA,CAAC7B,YAAY;kBACX8G,OAAO,eACLjF,OAAA,CAACvC,GAAG;oBAACyF,OAAO,EAAC,MAAM;oBAACC,UAAU,EAAC,QAAQ;oBAAC4C,cAAc,EAAC,eAAe;oBAAAxD,QAAA,gBACpEvC,OAAA,CAACpC,UAAU;sBAACgG,OAAO,EAAC,OAAO;sBAACC,UAAU,EAAC,KAAK;sBAAAtB,QAAA,EACzCd,KAAK,CAACD;oBAAQ;sBAAA+B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL,CAAC,eACb1D,OAAA,CAACvC,GAAG;sBAACyF,OAAO,EAAC,MAAM;sBAACC,UAAU,EAAC,QAAQ;sBAACC,GAAG,EAAE,CAAE;sBAAAb,QAAA,gBAC7CvC,OAAA,CAACpC,UAAU;wBACTgG,OAAO,EAAC,OAAO;wBACfG,KAAK,EAAE3B,aAAa,CAACX,KAAK,CAACC,SAAS,CAAE;wBACtCmC,UAAU,EAAC,MAAM;wBAAAtB,QAAA,GAEhBd,KAAK,CAACA,KAAK,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,EAAEA,KAAK,CAACA,KAAK,EAAC,GAC3C;sBAAA;wBAAA8B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eACb1D,OAAA,CAAClB,cAAc;wBACbwE,QAAQ,EAAC,OAAO;wBAChBS,KAAK,EAAE3B,aAAa,CAACX,KAAK,CAACC,SAAS,CAAE;wBACtCW,EAAE,EAAE;0BACF2D,SAAS,EAAEvE,KAAK,CAACC,SAAS,KAAK,MAAM,GAAG,gBAAgB,GAAG;wBAC7D;sBAAE;wBAAA6B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBACN;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF;cAAC,GAzBWmB,KAAK;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA0BV,CACX;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC,kCAAC;AAACuC,GAAA,GA1YGhG,gBAAgB;AA4YtBA,gBAAgB,CAACiG,WAAW,GAAG,kBAAkB;AAEjD,eAAejG,gBAAgB;AAAC,IAAAG,EAAA,EAAA6F,GAAA;AAAAE,YAAA,CAAA/F,EAAA;AAAA+F,YAAA,CAAAF,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}