import e from"@csstools/postcss-progressive-custom-properties";import n from"postcss-value-parser";
/**
 * Simple matrix (and vector) multiplication
 * Warning: No error handling for incompatible dimensions!
 * <AUTHOR> Verou 2020 MIT License
 *
 * @license W3C
 * https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 *
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/multiply-matrices.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 *
 * @see https://github.com/w3c/csswg-drafts/blob/main/css-color-4/multiply-matrices.js
 */
function t(e,n){const t=e.length;let r,u;r=Array.isArray(e[0])?e:[e],Array.isArray(n[0])||(u=n.map((e=>[e])));const o=u[0].length,a=u[0].map(((e,n)=>u.map((e=>e[n]))));let s=r.map((e=>a.map((n=>Array.isArray(e)?e.reduce(((e,t,r)=>e+t*(n[r]||0)),0):n.reduce(((n,t)=>n+t*e),0)))));return 1===t&&(s=s[0]),1===o?s.map((e=>e[0])):s}
/**
 * @license W3C
 * https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 *
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 *
 * @see https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js
 */function r(e){return e.map((function(e){const n=e<0?-1:1,t=Math.abs(e);return t<.04045?e/12.92:n*Math.pow((t+.055)/1.055,2.4)}))}function u(e){return e.map((function(e){const n=e<0?-1:1,t=Math.abs(e);return t>.0031308?n*(1.055*Math.pow(t,1/2.4)-.055):12.92*e}))}function o(e){return t([[.41239079926595934,.357584339383878,.1804807884018343],[.21263900587151027,.715168678767756,.07219231536073371],[.01933081871559182,.11919477979462598,.9505321522496607]],e)}function a(e){return t([[3.2409699419045226,-1.537383177570094,-.4986107602930034],[-.9692436362808796,1.8759675015077202,.04155505740717559],[.05563007969699366,-.20397695888897652,1.0569715142428786]],e)}function s(e){return r(e)}function i(e){return u(e)}function l(e){return t([[.4865709486482162,.26566769316909306,.1982172852343625],[.2289745640697488,.6917385218365064,.079286914093745],[0,.04511338185890264,1.043944368900976]],e)}function c(e){return t([[2.493496911941425,-.9313836179191239,-.40271078445071684],[-.8294889695615747,1.7626640603183463,.023624685841943577],[.03584583024378447,-.07617238926804182,.9568845240076872]],e)}function p(e){const n=t([[.8190224432164319,.3619062562801221,-.12887378261216414],[.0329836671980271,.9292868468965546,.03614466816999844],[.048177199566046255,.26423952494422764,.6335478258136937]],e);return t([[.2104542553,.793617785,-.0040720468],[1.9779984951,-2.428592205,.4505937099],[.0259040371,.7827717662,-.808675766]],n.map((e=>Math.cbrt(e))))}function f(e){const n=t([[.9999999984505198,.39633779217376786,.2158037580607588],[1.0000000088817609,-.10556134232365635,-.06385417477170591],[1.0000000546724108,-.08948418209496575,-1.2914855378640917]],e);return t([[1.2268798733741557,-.5578149965554813,.28139105017721583],[-.04057576262431372,1.1122868293970594,-.07171106666151701],[-.07637294974672142,-.4214933239627914,1.5869240244272418]],n.map((e=>e**3)))}function d(e){const n=180*Math.atan2(e[2],e[1])/Math.PI;return[e[0],Math.sqrt(e[1]**2+e[2]**2),n>=0?n:n+360]}function b(e){return[e[0],e[1]*Math.cos(e[2]*Math.PI/180),e[1]*Math.sin(e[2]*Math.PI/180)]}
/**
 * @license W3C
 * https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 *
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/deltaEOK.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 *
 * @see https://github.com/w3c/csswg-drafts/blob/main/css-color-4/deltaEOK.js
 */function m(e,n){const[t,r,u]=e,[o,a,s]=n,i=t-o,l=r-a,c=u-s;return Math.sqrt(i**2+l**2+c**2)}function v(e,n,t){return function(e,n,t){let r=0,u=e[1];const o=e;for(;u-r>1e-4;){const e=h(n(o));m(b(o),b(t(e)))-.02<1e-4?r=o[1]:u=o[1],o[1]=(u+r)/2}return h(n([...o]))}(e,n,t)}function h(e){return e.map((e=>e<0?0:e>1?1:e))}function y(e){const[n,t,r]=e;return n>=-1e-4&&n<=1.0001&&t>=-1e-4&&t<=1.0001&&r>=-1e-4&&r<=1.0001}function g(e){const[n,t,r]=e;let u=[Math.max(n,0),t,r],o=d(u);return o[0]<1e-6&&(o=[0,0,0]),o[0]>.999999&&(o=[1,0,0]),u=f(u),u=c(u),u=i(u),y(u)?[h(u),!0]:[v(o,(e=>i(e=c(e=f(e=b(e))))),(e=>d(e=p(e=l(e=s(e)))))),!1]}function w(e){const[n,t,r]=e,u=[Math.max(n,0),t,r%360];let o=u;return o[0]<1e-6&&(o=[0,0,0]),o[0]>.999999&&(o=[1,0,0]),o=b(o),o=f(o),o=c(o),o=i(o),y(o)?[h(o),!0]:[v(u,(e=>i(e=c(e=f(e=b(e))))),(e=>d(e=p(e=l(e=s(e)))))),!1]}function x(e){const[n,t,s]=e;let i=[Math.max(n,0),t,s],l=d(i);return l[0]<1e-6&&(l=[0,0,0]),l[0]>.999999&&(l=[1,0,0]),i=f(i),i=a(i),i=u(i),y(i)?h(i).map((e=>Math.round(255*e))):v(l,(e=>u(e=a(e=f(e=b(e))))),(e=>d(e=p(e=o(e=r(e)))))).map((e=>Math.round(255*e)))}function F(e){const[n,t,s]=e,i=[Math.max(n,0),t,s%360];let l=i;return l[0]<1e-6&&(l=[0,0,0]),l[0]>.999999&&(l=[1,0,0]),l=b(l),l=f(l),l=a(l),l=u(l),y(l)?h(l).map((e=>Math.round(255*e))):v(i,(e=>u(e=a(e=f(e=b(e))))),(e=>d(e=p(e=o(e=r(e)))))).map((e=>Math.round(255*e)))}function M(e){const t=e.value.toLowerCase(),r=e.nodes.slice().filter((e=>"comment"!==e.type&&"space"!==e.type));let u=null;if("oklab"===t?u=N(r):"oklch"===t&&(u=I(r)),!u)return;e.value="rgb",function(e,t,r){if(!t||!r)return;if(e.value="rgba",t.value=",",t.before="",!function(e){if(!e||"word"!==e.type)return!1;if(!E(e))return!1;const t=n.unit(e.value);if(!t)return!1;return!!t.number}(r))return;const u=n.unit(r.value);if(!u)return;"%"===u.unit&&(u.number=String(parseFloat(u.number)/100),r.value=String(u.number))}(e,u.slash,u.alpha);const[o,a,s]=O(u),[i,l,c]=A(u),p=("oklab"===t?x:F)([i.number,l.number,c.number].map((e=>parseFloat(e))));e.nodes.splice(e.nodes.indexOf(o)+1,0,{sourceIndex:0,sourceEndIndex:1,value:",",type:"div",before:"",after:""}),e.nodes.splice(e.nodes.indexOf(a)+1,0,{sourceIndex:0,sourceEndIndex:1,value:",",type:"div",before:"",after:""}),B(e.nodes,o,{...o,value:String(p[0])}),B(e.nodes,a,{...a,value:String(p[1])}),B(e.nodes,s,{...s,value:String(p[2])})}function k(e){if(!e||"word"!==e.type)return!1;if(!E(e))return!1;const t=n.unit(e.value);return!!t&&("%"===t.unit||""===t.unit)}function C(e){return e&&"function"===e.type&&"calc"===e.value.toLowerCase()}function P(e){return e&&"function"===e.type&&"var"===e.value.toLowerCase()}function L(e){return e&&"div"===e.type&&"/"===e.value}function I(e){if(!k(e[0]))return null;if(!k(e[1]))return null;if(!function(e){if(!e||"word"!==e.type)return!1;if(!E(e))return!1;const t=n.unit(e.value);if(!t)return!1;const r=t.unit.toLowerCase();return!!t.number&&("deg"===r||"grad"===r||"rad"===r||"turn"===r||""===r)}(e[2]))return null;const t={l:n.unit(e[0].value),lNode:e[0],c:n.unit(e[1].value),cNode:e[1],h:n.unit(e[2].value),hNode:e[2]};return function(e){switch(e.unit.toLowerCase()){case"deg":return void(e.unit="");case"rad":return e.unit="",void(e.number=(180*parseFloat(e.number)/Math.PI).toString());case"grad":return e.unit="",void(e.number=(.9*parseFloat(e.number)).toString());case"turn":e.unit="",e.number=(360*parseFloat(e.number)).toString()}}(t.h),""!==t.h.unit?null:(L(e[3])&&(t.slash=e[3]),(k(e[4])||C(e[4])||P(e[4]))&&(t.alpha=e[4]),!(e.length>3)||t.slash&&t.alpha?("%"===t.l.unit&&(t.l.unit="",t.l.number=(parseFloat(t.l.number)/100).toFixed(10)),"%"===t.c.unit&&(t.c.unit="",t.c.number=(parseFloat(t.c.number)/100*.4).toFixed(10)),t):null)}function N(e){if(!k(e[0]))return null;if(!k(e[1]))return null;if(!k(e[2]))return null;const t={l:n.unit(e[0].value),lNode:e[0],a:n.unit(e[1].value),aNode:e[1],b:n.unit(e[2].value),bNode:e[2]};return L(e[3])&&(t.slash=e[3]),(k(e[4])||C(e[4])||P(e[4]))&&(t.alpha=e[4]),!(e.length>3)||t.slash&&t.alpha?("%"===t.l.unit&&(t.l.unit="",t.l.number=(parseFloat(t.l.number)/100).toFixed(10)),"%"===t.a.unit&&(t.a.unit="",t.a.number=(parseFloat(t.a.number)/100*.4).toFixed(10)),"%"===t.b.unit&&(t.b.unit="",t.b.number=(parseFloat(t.b.number)/100*.4).toFixed(10)),t):null}function S(e){return void 0!==e.a}function O(e){return S(e)?[e.lNode,e.aNode,e.bNode]:[e.lNode,e.cNode,e.hNode]}function A(e){return S(e)?[e.l,e.a,e.b]:[e.l,e.c,e.h]}function B(e,n,t){const r=e.indexOf(n);e[r]=t}function E(e){if(!e||!e.value)return!1;try{return!1!==n.unit(e.value)}catch(e){return!1}}function j(e,t,r,u){let o;try{o=n(e)}catch(n){t.warn(r,`Failed to parse value '${e}' as an oklab or oklch function. Leaving the original value intact.`)}if(void 0===o)return;o.walk((e=>{e.type&&"function"===e.type&&("oklab"!==e.value.toLowerCase()&&"oklch"!==e.value.toLowerCase()||M(e))}));const a=String(o);if(a===e)return;const s=n(e);s.walk((e=>{e.type&&"function"===e.type&&("oklab"!==e.value.toLowerCase()&&"oklch"!==e.value.toLowerCase()||function(e,t,r,u){const o=n.stringify(e),a=e.value.toLowerCase(),s=e.nodes.slice().filter((e=>"comment"!==e.type&&"space"!==e.type));let i=null;if("oklab"===a?i=N(s):"oklch"===a&&(i=I(s)),!i)return;if(s.length>3&&(!i.slash||!i.alpha))return;e.value="color";const[l,c,p]=O(i),[f,d,b]=A(i),m="oklab"===a?g:w,v=[f.number,d.number,b.number].map((e=>parseFloat(e))),[h,y]=m(v);!y&&u&&t.warn(r,`"${o}" is out of gamut for "display-p3". Given "preserve: true" is set, this will lead to unexpected results in some browsers.`),e.nodes.splice(0,0,{sourceIndex:0,sourceEndIndex:10,value:"display-p3",type:"word"}),e.nodes.splice(1,0,{sourceIndex:0,sourceEndIndex:1,value:" ",type:"space"}),B(e.nodes,l,{...l,value:h[0].toFixed(5)}),B(e.nodes,c,{...c,value:h[1].toFixed(5)}),B(e.nodes,p,{...p,value:h[2].toFixed(5)})}(e,t,r,u))}));return{rgb:a,displayP3:String(s)}}const q=e=>({postcssPlugin:"postcss-oklab-function",Declaration:(n,{result:t})=>{if(function(e){const n=e.parent;if(!n)return!1;const t=n.index(e);for(let r=0;r<t;r++){const t=n.nodes[r];if("decl"===t.type&&t.prop.toLowerCase()===e.prop.toLowerCase())return!0}return!1}(n))return;if(function(e){let n=e.parent;for(;n;)if("atrule"===n.type){if("supports"===n.name.toLowerCase()){if(-1!==n.params.toLowerCase().indexOf("oklab("))return!0;if(-1!==n.params.toLowerCase().indexOf("oklch("))return!0}n=n.parent}else n=n.parent;return!1}(n))return;const r=n.value;if(!/(^|[^\w-])(oklab|oklch)\(/i.test(r.toLowerCase()))return;const u=j(r,n,t,e.preserve);void 0!==u&&(e.preserve?(n.cloneBefore({value:u.rgb}),e.subFeatures.displayP3&&n.cloneBefore({value:u.displayP3})):(n.cloneBefore({value:u.rgb}),e.subFeatures.displayP3&&n.cloneBefore({value:u.displayP3}),n.remove()))}});q.postcss=!0;const $=n=>{const t=Object.assign({enableProgressiveCustomProperties:!0,preserve:!1,subFeatures:{displayP3:!0}},n);return t.subFeatures=Object.assign({displayP3:!0},t.subFeatures),t.enableProgressiveCustomProperties&&(t.preserve||t.subFeatures.displayP3)?{postcssPlugin:"postcss-oklab-function",plugins:[e(),q(t)]}:q(t)};$.postcss=!0;export{$ as default};
