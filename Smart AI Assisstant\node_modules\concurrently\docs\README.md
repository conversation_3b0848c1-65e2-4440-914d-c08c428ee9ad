# Concurrently Documentation

## CLI

These articles cover using concurrently through CLI:

- [Prefixing](./cli/prefixing.md)
- [Output Control](./cli/output-control.md)
- [Success Conditions](./cli/success.md)
- [Shortcuts](./cli/shortcuts.md)
- [Terminating Commands](./cli/terminating.md)
- [Restarting Commands](./cli/restarting.md)
- [Input Handling](./cli/input-handling.md)
- [Passthrough Arguments](./cli/passthrough-arguments.md)
- [Configuration](./cli/configuration.md)
