import React from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  <PERSON>po<PERSON>,
  Button,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Chip,
  Divider,
  Grid,
  Link,
} from '@mui/material';
import {
  Security as SecurityIcon,
  Check as CheckIcon,
  Info as InfoIcon,
  Update as UpdateIcon,
  Support as SupportIcon,
  Code as CodeIcon,
  Code as GitHubIcon,
  Email as EmailIcon,
  Description as DocumentationIcon,
} from '@mui/icons-material';

const AboutSettings = () => {
  const features = [
    'Real-time URL threat detection',
    'Advanced file malware scanning',
    'AI-powered threat analysis',
    'Comprehensive security reports',
    'Multi-language support',
    'Dark/Light theme support',
    'User authentication & profiles',
    'Notification management',
  ];

  const techStack = [
    { name: 'React 18', description: 'Modern UI framework' },
    { name: 'Material-UI v5', description: 'Component library' },
    { name: 'Node.js & Express', description: 'Backend server' },
    { name: 'Supabase', description: 'Database & authentication' },
    { name: 'JW<PERSON>', description: 'Secure authentication' },
    { name: 'bcrypt', description: 'Password hashing' },
  ];

  const supportLinks = [
    {
      title: 'Documentation',
      description: 'Complete user guide and API documentation',
      icon: <DocumentationIcon />,
      action: 'View Docs',
    },
    {
      title: 'GitHub Repository',
      description: 'Source code and issue tracking',
      icon: <GitHubIcon />,
      action: 'View on GitHub',
    },
    {
      title: 'Email Support',
      description: 'Get help from our support team',
      icon: <EmailIcon />,
      action: 'Contact Support',
    },
  ];

  return (
    <Box>
      <Typography variant="h4" gutterBottom fontWeight="bold">
        About AI Security Guard
      </Typography>
      <Typography variant="body1" color="text.secondary" sx={{ mb: 4 }}>
        Advanced AI-powered security scanning platform
      </Typography>

      {/* App Info */}
      <Card sx={{ mb: 3 }}>
        <CardContent sx={{ textAlign: 'center' }}>
          <Box
            sx={{
              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
              borderRadius: '50%',
              width: 100,
              height: 100,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              mx: 'auto',
              mb: 3,
              boxShadow: '0 8px 25px rgba(102, 126, 234, 0.3)',
            }}
          >
            <SecurityIcon sx={{ color: 'white', fontSize: 50 }} />
          </Box>

          <Typography variant="h4" gutterBottom fontWeight="bold">
            AI Security Guard
          </Typography>

          <Box sx={{ display: 'flex', justifyContent: 'center', gap: 1, mb: 3 }}>
            <Chip label="Version 1.0.0" color="primary" />
            <Chip label="Stable" color="success" />
            <Chip label="Open Source" color="info" />
          </Box>

          <Typography variant="body1" sx={{ mb: 3, lineHeight: 1.7, maxWidth: 600, mx: 'auto' }}>
            Advanced AI-powered security scanning platform providing comprehensive
            threat detection and analysis for URLs and files. Built with cutting-edge
            technology to protect your digital assets with real-time monitoring and
            intelligent threat assessment.
          </Typography>

          <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center', flexWrap: 'wrap' }}>
            <Button
              variant="contained"
              startIcon={<UpdateIcon />}
              sx={{
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                '&:hover': {
                  background: 'linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)',
                },
              }}
            >
              Check for Updates
            </Button>
            <Button
              variant="outlined"
              startIcon={<InfoIcon />}
            >
              Release Notes
            </Button>
          </Box>
        </CardContent>
      </Card>

      {/* Key Features */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom fontWeight="bold">
            Key Features
          </Typography>
          
          <Grid container spacing={1}>
            {features.map((feature, index) => (
              <Grid item xs={12} sm={6} key={index}>
                <Box sx={{ display: 'flex', alignItems: 'center', py: 0.5 }}>
                  <CheckIcon sx={{ color: 'success.main', mr: 1, fontSize: 20 }} />
                  <Typography variant="body2">
                    {feature}
                  </Typography>
                </Box>
              </Grid>
            ))}
          </Grid>
        </CardContent>
      </Card>

      {/* Technology Stack */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <CodeIcon sx={{ mr: 2, color: 'primary.main' }} />
            <Typography variant="h6" fontWeight="bold">
              Technology Stack
            </Typography>
          </Box>
          
          <Grid container spacing={2}>
            {techStack.map((tech, index) => (
              <Grid item xs={12} sm={6} md={4} key={index}>
                <Card variant="outlined" sx={{ height: '100%' }}>
                  <CardContent sx={{ p: 2 }}>
                    <Typography variant="subtitle1" fontWeight="600" gutterBottom>
                      {tech.name}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {tech.description}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </CardContent>
      </Card>

      {/* Support & Resources */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <SupportIcon sx={{ mr: 2, color: 'primary.main' }} />
            <Typography variant="h6" fontWeight="bold">
              Support & Resources
            </Typography>
          </Box>
          
          <List>
            {supportLinks.map((link, index) => (
              <React.Fragment key={index}>
                <ListItem sx={{ px: 0 }}>
                  <ListItemIcon>
                    {React.cloneElement(link.icon, { color: 'primary' })}
                  </ListItemIcon>
                  <ListItemText
                    primary={link.title}
                    secondary={link.description}
                  />
                  <Button
                    variant="outlined"
                    size="small"
                    sx={{ borderRadius: 3 }}
                  >
                    {link.action}
                  </Button>
                </ListItem>
                {index < supportLinks.length - 1 && <Divider />}
              </React.Fragment>
            ))}
          </List>
        </CardContent>
      </Card>

      {/* System Information */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom fontWeight="bold">
            System Information
          </Typography>
          
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6}>
              <Typography variant="body2" color="text.secondary">
                Version
              </Typography>
              <Typography variant="body1" fontWeight="500">
                1.0.0
              </Typography>
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <Typography variant="body2" color="text.secondary">
                Build Date
              </Typography>
              <Typography variant="body1" fontWeight="500">
                January 2025
              </Typography>
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <Typography variant="body2" color="text.secondary">
                Environment
              </Typography>
              <Typography variant="body1" fontWeight="500">
                {process.env.NODE_ENV || 'Development'}
              </Typography>
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <Typography variant="body2" color="text.secondary">
                Browser
              </Typography>
              <Typography variant="body1" fontWeight="500">
                {navigator.userAgent.split(' ')[0] || 'Unknown'}
              </Typography>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Legal & Credits */}
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom fontWeight="bold">
            Legal & Credits
          </Typography>
          
          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
            © 2025 AI Security Guard. All rights reserved.
          </Typography>
          
          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
            Built with ❤️ for digital security and privacy protection.
          </Typography>
          
          <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
            <Link href="#" variant="body2" underline="hover">
              Privacy Policy
            </Link>
            <Link href="#" variant="body2" underline="hover">
              Terms of Service
            </Link>
            <Link href="#" variant="body2" underline="hover">
              License
            </Link>
            <Link href="#" variant="body2" underline="hover">
              Acknowledgments
            </Link>
          </Box>
        </CardContent>
      </Card>
    </Box>
  );
};

export default AboutSettings;
