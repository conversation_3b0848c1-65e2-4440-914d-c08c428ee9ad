"use client";

import createSvgIcon from "./utils/createSvgIcon.js";
import { jsx as _jsx } from "react/jsx-runtime";
export default createSvgIcon([/*#__PURE__*/_jsx("path", {
  d: "M3 2h8v2H3zm3 9h2V7h3V5H3v2h3zm6.4036 9.1819 7.7782-7.7782 1.4142 1.4142-7.7782 7.7782z"
}, "0"), /*#__PURE__*/_jsx("circle", {
  cx: "14.5",
  cy: "14.5",
  r: "1.5"
}, "1"), /*#__PURE__*/_jsx("circle", {
  cx: "19.5",
  cy: "19.5",
  r: "1.5"
}, "2"), /*#__PURE__*/_jsx("path", {
  d: "M15.5 11c1.38 0 2.5-1.12 2.5-2.5V4h3V2h-4v4.51c-.42-.32-.93-.51-1.5-.51C14.12 6 13 7.12 13 8.5s1.12 2.5 2.5 2.5m-5.76 4.96-1.41 1.41-.71-.71.35-.35c.98-.98.98-2.56 0-3.54-.49-.49-1.13-.73-1.77-.73s-1.28.24-1.77.73c-.98.98-.98 2.56 0 3.54l.35.35-1.06 1.06c-.98.98-.98 2.56 0 3.54.5.5 1.14.74 1.78.74s1.28-.24 1.77-.73l1.06-1.06 1.41 1.41 1.41-1.41-1.41-1.41 1.41-1.41zM5.85 14.2c.12-.12.26-.15.35-.15s.23.03.35.15c.19.2.19.51 0 .71l-.35.35-.35-.36c-.19-.19-.19-.51 0-.7m0 5.65c-.12.12-.26.15-.35.15s-.23-.03-.35-.15c-.19-.19-.19-.51 0-.71l1.06-1.06.71.71z"
}, "3")], 'EmojiSymbolsTwoTone');