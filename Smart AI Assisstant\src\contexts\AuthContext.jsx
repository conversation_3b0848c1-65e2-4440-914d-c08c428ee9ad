import React, { createContext, useContext, useReducer, useEffect, useCallback } from 'react';
import authService from '../services/auth';

// Initial state
const initialState = {
  user: null,
  token: null,
  isAuthenticated: false,
  isLoading: true,
  error: null,
};

// Action types
const actionTypes = {
  SET_LOADING: 'SET_LOADING',
  LOGIN_SUCCESS: 'LOGIN_SUCCESS',
  LOGOUT: 'LOGOUT',
  SET_ERROR: 'SET_ERROR',
  CLEAR_ERROR: 'CLEAR_ERROR',
  UPDATE_USER: 'UPDATE_USER',
};

// Reducer
const authReducer = (state, action) => {
  switch (action.type) {
    case actionTypes.SET_LOADING:
      return {
        ...state,
        isLoading: action.payload,
      };

    case actionTypes.LOGIN_SUCCESS:
      return {
        ...state,
        user: action.payload.user,
        token: action.payload.token,
        isAuthenticated: true,
        isLoading: false,
        error: null,
      };

    case actionTypes.LOGOUT:
      return {
        ...state,
        user: null,
        token: null,
        isAuthenticated: false,
        isLoading: false,
        error: null,
      };

    case actionTypes.SET_ERROR:
      return {
        ...state,
        error: action.payload,
        isLoading: false,
      };

    case actionTypes.CLEAR_ERROR:
      return {
        ...state,
        error: null,
      };

    case actionTypes.UPDATE_USER:
      return {
        ...state,
        user: action.payload,
      };

    default:
      return state;
  }
};

// Create context
const AuthContext = createContext();

// Auth provider component
export const AuthProvider = ({ children }) => {
  const [state, dispatch] = useReducer(authReducer, initialState);

  // Initialize auth state on app load
  useEffect(() => {
    const initializeAuth = async () => {
      try {
        dispatch({ type: actionTypes.SET_LOADING, payload: true });

        const token = authService.getToken();
        const user = authService.getCurrentUser();

        if (token && user) {
          // Verify token is still valid by getting fresh profile
          const result = await authService.getProfile();
          
          if (result.success) {
            dispatch({
              type: actionTypes.LOGIN_SUCCESS,
              payload: { user: result.user, token },
            });
          } else {
            // Token is invalid, clear auth data
            authService.clearAuthData();
            dispatch({ type: actionTypes.LOGOUT });
          }
        } else {
          dispatch({ type: actionTypes.LOGOUT });
        }
      } catch (error) {
        console.error('Auth initialization error:', error);
        authService.clearAuthData();
        dispatch({ type: actionTypes.LOGOUT });
      } finally {
        dispatch({ type: actionTypes.SET_LOADING, payload: false });
      }
    };

    initializeAuth();
  }, []);

  // Login function
  const login = useCallback(async (credentials) => {
    try {
      dispatch({ type: actionTypes.SET_LOADING, payload: true });
      dispatch({ type: actionTypes.CLEAR_ERROR });

      const result = await authService.login(credentials);

      if (result.success) {
        dispatch({
          type: actionTypes.LOGIN_SUCCESS,
          payload: { user: result.user, token: result.token },
        });
        return { success: true };
      } else {
        dispatch({ type: actionTypes.SET_ERROR, payload: result.error });
        return { success: false, error: result.error };
      }
    } catch (error) {
      const errorMessage = 'Login failed. Please try again.';
      dispatch({ type: actionTypes.SET_ERROR, payload: errorMessage });
      return { success: false, error: errorMessage };
    }
  }, []);

  // Signup function
  const signup = useCallback(async (userData) => {
    try {
      dispatch({ type: actionTypes.SET_LOADING, payload: true });
      dispatch({ type: actionTypes.CLEAR_ERROR });

      const result = await authService.signup(userData);

      if (result.success) {
        dispatch({
          type: actionTypes.LOGIN_SUCCESS,
          payload: { user: result.user, token: result.token },
        });
        return { success: true };
      } else {
        dispatch({ type: actionTypes.SET_ERROR, payload: result.error });
        return { success: false, error: result.error, details: result.details };
      }
    } catch (error) {
      const errorMessage = 'Registration failed. Please try again.';
      dispatch({ type: actionTypes.SET_ERROR, payload: errorMessage });
      return { success: false, error: errorMessage };
    }
  }, []);

  // Logout function
  const logout = useCallback(async () => {
    try {
      dispatch({ type: actionTypes.SET_LOADING, payload: true });
      await authService.logout();
    } catch (error) {
      console.error('Logout error:', error);
      // Continue with logout even if server request fails
    } finally {
      // Always clear local state and storage
      authService.clearAuthData();
      dispatch({ type: actionTypes.LOGOUT });
    }
  }, []);

  // Clear error function
  const clearError = useCallback(() => {
    dispatch({ type: actionTypes.CLEAR_ERROR });
  }, []);

  // Update user function
  const updateUser = useCallback((userData) => {
    dispatch({ type: actionTypes.UPDATE_USER, payload: userData });
  }, []);

  const value = {
    ...state,
    login,
    signup,
    logout,
    clearError,
    updateUser,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

// Custom hook to use auth context
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
