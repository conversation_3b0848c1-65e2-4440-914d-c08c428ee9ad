{"ast": null, "code": "var _jsxFileName = \"E:\\\\PythonProjects\\\\React.js\\\\Smart AI Assisstant\\\\src\\\\components\\\\AutoScanSystem.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Card, CardContent, Typography, Switch, FormControlLabel, Grid, Chip, List, ListItem, ListItemIcon, ListItemText, Avatar, IconButton, Tooltip, LinearProgress, Alert, Slider, TextField, Button, Divider } from '@mui/material';\nimport { AutoMode as AutoIcon, Schedule as ScheduleIcon, Speed as SpeedIcon, Security as SecurityIcon, Notifications as NotificationsIcon, Settings as SettingsIcon, PlayArrow as PlayIcon, Pause as PauseIcon, Stop as StopIcon, Refresh as RefreshIcon, Psychology as AIIcon, Shield as ShieldIcon } from '@mui/icons-material';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AutoScanSystem = /*#__PURE__*/_s(/*#__PURE__*/React.memo(_c = _s(() => {\n  _s();\n  const [autoScanEnabled, setAutoScanEnabled] = useState(true);\n  const [scanInterval, setScanInterval] = useState(30); // minutes\n  const [scanIntensity, setScanIntensity] = useState(2); // 1-3 scale\n  const [smartScheduling, setSmartScheduling] = useState(true);\n  const [adaptiveScan, setAdaptiveScan] = useState(true);\n  const [currentScan, setCurrentScan] = useState({\n    isRunning: false,\n    progress: 0,\n    currentTarget: '',\n    threatsFound: 0,\n    estimatedTime: 0\n  });\n  const [scanQueue, setScanQueue] = useState([{\n    type: 'URL',\n    target: 'https://suspicious-site.com',\n    priority: 'high',\n    status: 'pending'\n  }, {\n    type: 'File',\n    target: 'document.pdf',\n    priority: 'medium',\n    status: 'pending'\n  }, {\n    type: 'URL',\n    target: 'https://phishing-attempt.net',\n    priority: 'high',\n    status: 'pending'\n  }, {\n    type: 'File',\n    target: 'software.exe',\n    priority: 'low',\n    status: 'pending'\n  }]);\n  const [scanHistory, setScanHistory] = useState([{\n    timestamp: new Date(Date.now() - 1000 * 60 * 15),\n    type: 'Auto Scan',\n    targets: 12,\n    threats: 2,\n    duration: '3m 45s',\n    status: 'completed'\n  }, {\n    timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2),\n    type: 'Scheduled Scan',\n    targets: 8,\n    threats: 0,\n    duration: '2m 12s',\n    status: 'completed'\n  }, {\n    timestamp: new Date(Date.now() - 1000 * 60 * 60 * 6),\n    type: 'Priority Scan',\n    targets: 3,\n    threats: 1,\n    duration: '1m 33s',\n    status: 'completed'\n  }]);\n  const [aiRecommendations, setAiRecommendations] = useState([{\n    type: 'schedule',\n    message: 'Increase scan frequency during peak hours (9-17)',\n    confidence: 89\n  }, {\n    type: 'priority',\n    message: 'Focus on .exe files - 67% higher threat probability',\n    confidence: 94\n  }, {\n    type: 'optimization',\n    message: 'Reduce scan intensity for trusted domains',\n    confidence: 82\n  }]);\n\n  // Simulate auto-scan progress\n  useEffect(() => {\n    let interval;\n    if (currentScan.isRunning) {\n      interval = setInterval(() => {\n        setCurrentScan(prev => {\n          const newProgress = Math.min(100, prev.progress + Math.random() * 10);\n          if (newProgress >= 100) {\n            return {\n              ...prev,\n              progress: 100,\n              isRunning: false,\n              threatsFound: prev.threatsFound + Math.floor(Math.random() * 3)\n            };\n          }\n          return {\n            ...prev,\n            progress: newProgress,\n            estimatedTime: Math.max(0, prev.estimatedTime - 5)\n          };\n        });\n      }, 1000);\n    }\n    return () => clearInterval(interval);\n  }, [currentScan.isRunning]);\n  const startAutoScan = () => {\n    var _scanQueue$;\n    setCurrentScan({\n      isRunning: true,\n      progress: 0,\n      currentTarget: ((_scanQueue$ = scanQueue[0]) === null || _scanQueue$ === void 0 ? void 0 : _scanQueue$.target) || 'Multiple targets',\n      threatsFound: 0,\n      estimatedTime: 180 // 3 minutes\n    });\n  };\n  const pauseAutoScan = () => {\n    setCurrentScan(prev => ({\n      ...prev,\n      isRunning: false\n    }));\n  };\n  const stopAutoScan = () => {\n    setCurrentScan({\n      isRunning: false,\n      progress: 0,\n      currentTarget: '',\n      threatsFound: 0,\n      estimatedTime: 0\n    });\n  };\n  const getIntensityLabel = value => {\n    switch (value) {\n      case 1:\n        return 'Light';\n      case 2:\n        return 'Balanced';\n      case 3:\n        return 'Intensive';\n      default:\n        return 'Balanced';\n    }\n  };\n  const getPriorityColor = priority => {\n    switch (priority) {\n      case 'high':\n        return 'error';\n      case 'medium':\n        return 'warning';\n      case 'low':\n        return 'success';\n      default:\n        return 'default';\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      p: 3\n    },\n    children: [/*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        mb: 4,\n        background: 'linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%)',\n        border: '1px solid rgba(102, 126, 234, 0.2)',\n        position: 'relative',\n        overflow: 'hidden',\n        '&::before': {\n          content: '\"\"',\n          position: 'absolute',\n          top: 0,\n          left: 0,\n          right: 0,\n          height: '4px',\n          background: 'linear-gradient(90deg, #667eea 0%, #764ba2 100%)'\n        }\n      },\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          display: \"flex\",\n          alignItems: \"center\",\n          gap: 2,\n          mb: 3,\n          children: [/*#__PURE__*/_jsxDEV(Avatar, {\n            sx: {\n              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n              width: 56,\n              height: 56\n            },\n            children: /*#__PURE__*/_jsxDEV(AutoIcon, {\n              sx: {\n                fontSize: 28\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 205,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 198,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            flex: 1,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h4\",\n              fontWeight: \"bold\",\n              gutterBottom: true,\n              children: \"Intelligent Auto-Scan System\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 208,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              color: \"text.secondary\",\n              children: \"AI-powered automated security scanning with adaptive scheduling\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 211,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 207,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            display: \"flex\",\n            gap: 1,\n            children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n              title: \"Start Auto Scan\",\n              children: /*#__PURE__*/_jsxDEV(IconButton, {\n                onClick: startAutoScan,\n                disabled: currentScan.isRunning,\n                sx: {\n                  background: 'linear-gradient(135deg, #4caf50 0%, #8bc34a 100%)',\n                  color: 'white',\n                  '&:hover': {\n                    background: 'linear-gradient(135deg, #45a049 0%, #7cb342 100%)'\n                  }\n                },\n                children: /*#__PURE__*/_jsxDEV(PlayIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 228,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 217,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 216,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n              title: \"Pause\",\n              children: /*#__PURE__*/_jsxDEV(IconButton, {\n                onClick: pauseAutoScan,\n                disabled: !currentScan.isRunning,\n                sx: {\n                  background: 'linear-gradient(135deg, #ff9800 0%, #ffc107 100%)',\n                  color: 'white',\n                  '&:hover': {\n                    background: 'linear-gradient(135deg, #f57c00 0%, #ffb300 100%)'\n                  }\n                },\n                children: /*#__PURE__*/_jsxDEV(PauseIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 243,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 232,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 231,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n              title: \"Stop\",\n              children: /*#__PURE__*/_jsxDEV(IconButton, {\n                onClick: stopAutoScan,\n                sx: {\n                  background: 'linear-gradient(135deg, #f44336 0%, #ff5722 100%)',\n                  color: 'white',\n                  '&:hover': {\n                    background: 'linear-gradient(135deg, #d32f2f 0%, #f4511e 100%)'\n                  }\n                },\n                children: /*#__PURE__*/_jsxDEV(StopIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 257,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 247,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 246,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 215,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 197,\n          columnNumber: 11\n        }, this), (currentScan.isRunning || currentScan.progress > 0) && /*#__PURE__*/_jsxDEV(Alert, {\n          severity: currentScan.isRunning ? 'info' : 'success',\n          sx: {\n            mb: 3\n          },\n          icon: currentScan.isRunning ? /*#__PURE__*/_jsxDEV(RefreshIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 268,\n            columnNumber: 45\n          }, this) : /*#__PURE__*/_jsxDEV(ShieldIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 268,\n            columnNumber: 63\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle2\",\n              fontWeight: \"600\",\n              children: currentScan.isRunning ? 'Scanning in Progress' : 'Scan Completed'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 271,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              sx: {\n                mb: 1\n              },\n              children: [\"Target: \", currentScan.currentTarget]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 274,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(LinearProgress, {\n              variant: \"determinate\",\n              value: currentScan.progress,\n              sx: {\n                mb: 1,\n                height: 6,\n                borderRadius: 3\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 277,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              display: \"flex\",\n              justifyContent: \"space-between\",\n              alignItems: \"center\",\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                children: [\"Progress: \", Math.round(currentScan.progress), \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 283,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                children: [\"Threats Found: \", currentScan.threatsFound]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 286,\n                columnNumber: 19\n              }, this), currentScan.isRunning && /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                children: [\"ETA: \", Math.floor(currentScan.estimatedTime / 60), \"m \", currentScan.estimatedTime % 60, \"s\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 290,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 282,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 270,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 265,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 3,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              gutterBottom: true,\n              fontWeight: \"600\",\n              children: \"Auto-Scan Configuration\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 302,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(FormControlLabel, {\n              control: /*#__PURE__*/_jsxDEV(Switch, {\n                checked: autoScanEnabled,\n                onChange: e => setAutoScanEnabled(e.target.checked),\n                color: \"primary\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 308,\n                columnNumber: 19\n              }, this),\n              label: \"Enable Auto-Scan\",\n              sx: {\n                mb: 2\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 306,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(FormControlLabel, {\n              control: /*#__PURE__*/_jsxDEV(Switch, {\n                checked: smartScheduling,\n                onChange: e => setSmartScheduling(e.target.checked),\n                color: \"primary\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 320,\n                columnNumber: 19\n              }, this),\n              label: \"AI Smart Scheduling\",\n              sx: {\n                mb: 2\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 318,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(FormControlLabel, {\n              control: /*#__PURE__*/_jsxDEV(Switch, {\n                checked: adaptiveScan,\n                onChange: e => setAdaptiveScan(e.target.checked),\n                color: \"primary\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 332,\n                columnNumber: 19\n              }, this),\n              label: \"Adaptive Scan Intensity\",\n              sx: {\n                mb: 3\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 330,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              mb: 3,\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                gutterBottom: true,\n                children: [\"Scan Interval: \", scanInterval, \" minutes\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 343,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Slider, {\n                value: scanInterval,\n                onChange: (e, value) => setScanInterval(value),\n                min: 5,\n                max: 120,\n                step: 5,\n                marks: [{\n                  value: 5,\n                  label: '5m'\n                }, {\n                  value: 30,\n                  label: '30m'\n                }, {\n                  value: 60,\n                  label: '1h'\n                }, {\n                  value: 120,\n                  label: '2h'\n                }],\n                color: \"primary\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 346,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 342,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              mb: 3,\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                gutterBottom: true,\n                children: [\"Scan Intensity: \", getIntensityLabel(scanIntensity)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 363,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Slider, {\n                value: scanIntensity,\n                onChange: (e, value) => setScanIntensity(value),\n                min: 1,\n                max: 3,\n                step: 1,\n                marks: [{\n                  value: 1,\n                  label: 'Light'\n                }, {\n                  value: 2,\n                  label: 'Balanced'\n                }, {\n                  value: 3,\n                  label: 'Intensive'\n                }],\n                color: \"primary\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 366,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 362,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 301,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              gutterBottom: true,\n              fontWeight: \"600\",\n              children: \"AI Recommendations\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 383,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(List, {\n              children: aiRecommendations.map((rec, index) => /*#__PURE__*/_jsxDEV(ListItem, {\n                sx: {\n                  background: theme => theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.02)',\n                  borderRadius: 2,\n                  mb: 1\n                },\n                children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                  children: /*#__PURE__*/_jsxDEV(AIIcon, {\n                    color: \"secondary\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 400,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 399,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: rec.message,\n                  secondary: /*#__PURE__*/_jsxDEV(Chip, {\n                    label: `${rec.confidence}% confidence`,\n                    size: \"small\",\n                    color: rec.confidence > 90 ? 'success' : 'warning',\n                    variant: \"outlined\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 405,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 402,\n                  columnNumber: 21\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 389,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 387,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 382,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 300,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 196,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 178,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          sx: {\n            background: theme => theme.palette.mode === 'dark' ? 'linear-gradient(135deg, rgba(30, 30, 30, 0.95) 0%, rgba(50, 50, 50, 0.95) 100%)' : 'linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%)',\n            backdropFilter: 'blur(20px)',\n            border: theme => `1px solid ${theme.palette.divider}`\n          },\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              display: \"flex\",\n              alignItems: \"center\",\n              gap: 2,\n              mb: 3,\n              children: [/*#__PURE__*/_jsxDEV(Avatar, {\n                sx: {\n                  background: 'linear-gradient(135deg, #ff9800 0%, #ffc107 100%)',\n                  width: 48,\n                  height: 48\n                },\n                children: /*#__PURE__*/_jsxDEV(ScheduleIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 442,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 435,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                fontWeight: \"bold\",\n                children: [\"Scan Queue (\", scanQueue.length, \")\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 444,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 434,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(List, {\n              children: scanQueue.map((item, index) => /*#__PURE__*/_jsxDEV(ListItem, {\n                sx: {\n                  background: theme => theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.02)',\n                  borderRadius: 2,\n                  mb: 1\n                },\n                children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                  children: /*#__PURE__*/_jsxDEV(SecurityIcon, {\n                    color: \"primary\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 462,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 461,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: /*#__PURE__*/_jsxDEV(Box, {\n                    display: \"flex\",\n                    alignItems: \"center\",\n                    gap: 2,\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      fontWeight: \"600\",\n                      children: item.target\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 467,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                      label: item.type,\n                      size: \"small\",\n                      variant: \"outlined\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 470,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                      label: item.priority,\n                      size: \"small\",\n                      color: getPriorityColor(item.priority)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 475,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 466,\n                    columnNumber: 25\n                  }, this),\n                  secondary: `Status: ${item.status}`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 464,\n                  columnNumber: 21\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 451,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 449,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 433,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 424,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 423,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          sx: {\n            background: theme => theme.palette.mode === 'dark' ? 'linear-gradient(135deg, rgba(30, 30, 30, 0.95) 0%, rgba(50, 50, 50, 0.95) 100%)' : 'linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%)',\n            backdropFilter: 'blur(20px)',\n            border: theme => `1px solid ${theme.palette.divider}`\n          },\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              display: \"flex\",\n              alignItems: \"center\",\n              gap: 2,\n              mb: 3,\n              children: [/*#__PURE__*/_jsxDEV(Avatar, {\n                sx: {\n                  background: 'linear-gradient(135deg, #4caf50 0%, #8bc34a 100%)',\n                  width: 48,\n                  height: 48\n                },\n                children: /*#__PURE__*/_jsxDEV(SpeedIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 511,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 504,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                fontWeight: \"bold\",\n                children: \"Recent Auto-Scans\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 513,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 503,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(List, {\n              children: scanHistory.map((scan, index) => /*#__PURE__*/_jsxDEV(ListItem, {\n                sx: {\n                  background: theme => theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.02)',\n                  borderRadius: 2,\n                  mb: 1\n                },\n                children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: /*#__PURE__*/_jsxDEV(Box, {\n                    display: \"flex\",\n                    alignItems: \"center\",\n                    justifyContent: \"space-between\",\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      fontWeight: \"600\",\n                      children: scan.type\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 533,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"caption\",\n                      color: \"text.secondary\",\n                      children: scan.timestamp.toLocaleTimeString()\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 536,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 532,\n                    columnNumber: 25\n                  }, this),\n                  secondary: /*#__PURE__*/_jsxDEV(Box, {\n                    display: \"flex\",\n                    alignItems: \"center\",\n                    gap: 2,\n                    mt: 1,\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"caption\",\n                      children: [scan.targets, \" targets\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 543,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"caption\",\n                      color: scan.threats > 0 ? 'error.main' : 'success.main',\n                      children: [scan.threats, \" threats\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 546,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"caption\",\n                      children: scan.duration\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 549,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 542,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 530,\n                  columnNumber: 21\n                }, this)\n              }, index, false, {\n                fileName: _jsxFileName,\n                lineNumber: 520,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 518,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 502,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 493,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 492,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 421,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 176,\n    columnNumber: 5\n  }, this);\n}, \"3sWh9O3UvN+oPCNLQyCwrzbNBAE=\")), \"3sWh9O3UvN+oPCNLQyCwrzbNBAE=\");\n_c2 = AutoScanSystem;\nAutoScanSystem.displayName = 'AutoScanSystem';\nexport default AutoScanSystem;\nvar _c, _c2;\n$RefreshReg$(_c, \"AutoScanSystem$React.memo\");\n$RefreshReg$(_c2, \"AutoScanSystem\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Typography", "Switch", "FormControlLabel", "Grid", "Chip", "List", "ListItem", "ListItemIcon", "ListItemText", "Avatar", "IconButton", "<PERSON><PERSON><PERSON>", "LinearProgress", "<PERSON><PERSON>", "Slide<PERSON>", "TextField", "<PERSON><PERSON>", "Divider", "AutoMode", "AutoIcon", "Schedule", "ScheduleIcon", "Speed", "SpeedIcon", "Security", "SecurityIcon", "Notifications", "NotificationsIcon", "Settings", "SettingsIcon", "PlayArrow", "PlayIcon", "Pause", "PauseIcon", "Stop", "StopIcon", "Refresh", "RefreshIcon", "Psychology", "AIIcon", "Shield", "ShieldIcon", "jsxDEV", "_jsxDEV", "AutoScanSystem", "_s", "memo", "_c", "autoScanEnabled", "setAutoScanEnabled", "scanInterval", "setScanInterval", "scanIntensity", "setScanIntensity", "smartScheduling", "setSmartScheduling", "adaptiveScan", "setAdaptiveScan", "currentScan", "setCurrentScan", "isRunning", "progress", "currentTarget", "threatsFound", "estimatedTime", "scanQueue", "setScanQueue", "type", "target", "priority", "status", "scanHistory", "setScanHistory", "timestamp", "Date", "now", "targets", "threats", "duration", "aiRecommendations", "setAiRecommendations", "message", "confidence", "interval", "setInterval", "prev", "newProgress", "Math", "min", "random", "floor", "max", "clearInterval", "startAutoScan", "_scanQueue$", "pauseAutoScan", "stopAutoScan", "getIntensityLabel", "value", "getPriorityColor", "sx", "p", "children", "mb", "background", "border", "position", "overflow", "content", "top", "left", "right", "height", "display", "alignItems", "gap", "width", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "flex", "variant", "fontWeight", "gutterBottom", "color", "title", "onClick", "disabled", "severity", "icon", "borderRadius", "justifyContent", "round", "container", "spacing", "item", "xs", "md", "control", "checked", "onChange", "e", "label", "step", "marks", "map", "rec", "index", "theme", "palette", "mode", "primary", "secondary", "size", "<PERSON><PERSON>ilter", "divider", "length", "scan", "toLocaleTimeString", "mt", "_c2", "displayName", "$RefreshReg$"], "sources": ["E:/PythonProjects/React.js/Smart AI Assisstant/src/components/AutoScanSystem.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  <PERSON>,\n  Card,\n  CardContent,\n  Typography,\n  Switch,\n  FormControlLabel,\n  Grid,\n  Chip,\n  List,\n  ListItem,\n  ListItemIcon,\n  ListItemText,\n  Avatar,\n  IconButton,\n  Tooltip,\n  LinearProgress,\n  Alert,\n  Slider,\n  TextField,\n  Button,\n  Divider,\n} from '@mui/material';\nimport {\n  AutoMode as AutoIcon,\n  Schedule as ScheduleIcon,\n  Speed as SpeedIcon,\n  Security as SecurityIcon,\n  Notifications as NotificationsIcon,\n  Settings as SettingsIcon,\n  PlayArrow as PlayIcon,\n  Pause as PauseIcon,\n  Stop as StopIcon,\n  Refresh as RefreshIcon,\n  Psychology as AIIcon,\n  Shield as ShieldIcon,\n} from '@mui/icons-material';\n\nconst AutoScanSystem = React.memo(() => {\n  const [autoScanEnabled, setAutoScanEnabled] = useState(true);\n  const [scanInterval, setScanInterval] = useState(30); // minutes\n  const [scanIntensity, setScanIntensity] = useState(2); // 1-3 scale\n  const [smartScheduling, setSmartScheduling] = useState(true);\n  const [adaptiveScan, setAdaptiveScan] = useState(true);\n\n  const [currentScan, setCurrentScan] = useState({\n    isRunning: false,\n    progress: 0,\n    currentTarget: '',\n    threatsFound: 0,\n    estimatedTime: 0,\n  });\n\n  const [scanQueue, setScanQueue] = useState([\n    { type: 'URL', target: 'https://suspicious-site.com', priority: 'high', status: 'pending' },\n    { type: 'File', target: 'document.pdf', priority: 'medium', status: 'pending' },\n    { type: 'URL', target: 'https://phishing-attempt.net', priority: 'high', status: 'pending' },\n    { type: 'File', target: 'software.exe', priority: 'low', status: 'pending' },\n  ]);\n\n  const [scanHistory, setScanHistory] = useState([\n    {\n      timestamp: new Date(Date.now() - 1000 * 60 * 15),\n      type: 'Auto Scan',\n      targets: 12,\n      threats: 2,\n      duration: '3m 45s',\n      status: 'completed',\n    },\n    {\n      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2),\n      type: 'Scheduled Scan',\n      targets: 8,\n      threats: 0,\n      duration: '2m 12s',\n      status: 'completed',\n    },\n    {\n      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 6),\n      type: 'Priority Scan',\n      targets: 3,\n      threats: 1,\n      duration: '1m 33s',\n      status: 'completed',\n    },\n  ]);\n\n  const [aiRecommendations, setAiRecommendations] = useState([\n    {\n      type: 'schedule',\n      message: 'Increase scan frequency during peak hours (9-17)',\n      confidence: 89,\n    },\n    {\n      type: 'priority',\n      message: 'Focus on .exe files - 67% higher threat probability',\n      confidence: 94,\n    },\n    {\n      type: 'optimization',\n      message: 'Reduce scan intensity for trusted domains',\n      confidence: 82,\n    },\n  ]);\n\n  // Simulate auto-scan progress\n  useEffect(() => {\n    let interval;\n    if (currentScan.isRunning) {\n      interval = setInterval(() => {\n        setCurrentScan(prev => {\n          const newProgress = Math.min(100, prev.progress + Math.random() * 10);\n          if (newProgress >= 100) {\n            return {\n              ...prev,\n              progress: 100,\n              isRunning: false,\n              threatsFound: prev.threatsFound + Math.floor(Math.random() * 3),\n            };\n          }\n          return {\n            ...prev,\n            progress: newProgress,\n            estimatedTime: Math.max(0, prev.estimatedTime - 5),\n          };\n        });\n      }, 1000);\n    }\n    return () => clearInterval(interval);\n  }, [currentScan.isRunning]);\n\n  const startAutoScan = () => {\n    setCurrentScan({\n      isRunning: true,\n      progress: 0,\n      currentTarget: scanQueue[0]?.target || 'Multiple targets',\n      threatsFound: 0,\n      estimatedTime: 180, // 3 minutes\n    });\n  };\n\n  const pauseAutoScan = () => {\n    setCurrentScan(prev => ({ ...prev, isRunning: false }));\n  };\n\n  const stopAutoScan = () => {\n    setCurrentScan({\n      isRunning: false,\n      progress: 0,\n      currentTarget: '',\n      threatsFound: 0,\n      estimatedTime: 0,\n    });\n  };\n\n  const getIntensityLabel = (value) => {\n    switch (value) {\n      case 1: return 'Light';\n      case 2: return 'Balanced';\n      case 3: return 'Intensive';\n      default: return 'Balanced';\n    }\n  };\n\n  const getPriorityColor = (priority) => {\n    switch (priority) {\n      case 'high': return 'error';\n      case 'medium': return 'warning';\n      case 'low': return 'success';\n      default: return 'default';\n    }\n  };\n\n  return (\n    <Box sx={{ p: 3 }}>\n      {/* Auto-Scan Controls */}\n      <Card\n        sx={{\n          mb: 4,\n          background: 'linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%)',\n          border: '1px solid rgba(102, 126, 234, 0.2)',\n          position: 'relative',\n          overflow: 'hidden',\n          '&::before': {\n            content: '\"\"',\n            position: 'absolute',\n            top: 0,\n            left: 0,\n            right: 0,\n            height: '4px',\n            background: 'linear-gradient(90deg, #667eea 0%, #764ba2 100%)',\n          },\n        }}\n      >\n        <CardContent>\n          <Box display=\"flex\" alignItems=\"center\" gap={2} mb={3}>\n            <Avatar\n              sx={{\n                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                width: 56,\n                height: 56,\n              }}\n            >\n              <AutoIcon sx={{ fontSize: 28 }} />\n            </Avatar>\n            <Box flex={1}>\n              <Typography variant=\"h4\" fontWeight=\"bold\" gutterBottom>\n                Intelligent Auto-Scan System\n              </Typography>\n              <Typography variant=\"body1\" color=\"text.secondary\">\n                AI-powered automated security scanning with adaptive scheduling\n              </Typography>\n            </Box>\n            <Box display=\"flex\" gap={1}>\n              <Tooltip title=\"Start Auto Scan\">\n                <IconButton\n                  onClick={startAutoScan}\n                  disabled={currentScan.isRunning}\n                  sx={{\n                    background: 'linear-gradient(135deg, #4caf50 0%, #8bc34a 100%)',\n                    color: 'white',\n                    '&:hover': {\n                      background: 'linear-gradient(135deg, #45a049 0%, #7cb342 100%)',\n                    },\n                  }}\n                >\n                  <PlayIcon />\n                </IconButton>\n              </Tooltip>\n              <Tooltip title=\"Pause\">\n                <IconButton\n                  onClick={pauseAutoScan}\n                  disabled={!currentScan.isRunning}\n                  sx={{\n                    background: 'linear-gradient(135deg, #ff9800 0%, #ffc107 100%)',\n                    color: 'white',\n                    '&:hover': {\n                      background: 'linear-gradient(135deg, #f57c00 0%, #ffb300 100%)',\n                    },\n                  }}\n                >\n                  <PauseIcon />\n                </IconButton>\n              </Tooltip>\n              <Tooltip title=\"Stop\">\n                <IconButton\n                  onClick={stopAutoScan}\n                  sx={{\n                    background: 'linear-gradient(135deg, #f44336 0%, #ff5722 100%)',\n                    color: 'white',\n                    '&:hover': {\n                      background: 'linear-gradient(135deg, #d32f2f 0%, #f4511e 100%)',\n                    },\n                  }}\n                >\n                  <StopIcon />\n                </IconButton>\n              </Tooltip>\n            </Box>\n          </Box>\n\n          {/* Current Scan Status */}\n          {(currentScan.isRunning || currentScan.progress > 0) && (\n            <Alert\n              severity={currentScan.isRunning ? 'info' : 'success'}\n              sx={{ mb: 3 }}\n              icon={currentScan.isRunning ? <RefreshIcon /> : <ShieldIcon />}\n            >\n              <Box>\n                <Typography variant=\"subtitle2\" fontWeight=\"600\">\n                  {currentScan.isRunning ? 'Scanning in Progress' : 'Scan Completed'}\n                </Typography>\n                <Typography variant=\"body2\" sx={{ mb: 1 }}>\n                  Target: {currentScan.currentTarget}\n                </Typography>\n                <LinearProgress\n                  variant=\"determinate\"\n                  value={currentScan.progress}\n                  sx={{ mb: 1, height: 6, borderRadius: 3 }}\n                />\n                <Box display=\"flex\" justifyContent=\"space-between\" alignItems=\"center\">\n                  <Typography variant=\"caption\">\n                    Progress: {Math.round(currentScan.progress)}%\n                  </Typography>\n                  <Typography variant=\"caption\">\n                    Threats Found: {currentScan.threatsFound}\n                  </Typography>\n                  {currentScan.isRunning && (\n                    <Typography variant=\"caption\">\n                      ETA: {Math.floor(currentScan.estimatedTime / 60)}m {currentScan.estimatedTime % 60}s\n                    </Typography>\n                  )}\n                </Box>\n              </Box>\n            </Alert>\n          )}\n\n          {/* Auto-Scan Settings */}\n          <Grid container spacing={3}>\n            <Grid item xs={12} md={6}>\n              <Typography variant=\"h6\" gutterBottom fontWeight=\"600\">\n                Auto-Scan Configuration\n              </Typography>\n              \n              <FormControlLabel\n                control={\n                  <Switch\n                    checked={autoScanEnabled}\n                    onChange={(e) => setAutoScanEnabled(e.target.checked)}\n                    color=\"primary\"\n                  />\n                }\n                label=\"Enable Auto-Scan\"\n                sx={{ mb: 2 }}\n              />\n\n              <FormControlLabel\n                control={\n                  <Switch\n                    checked={smartScheduling}\n                    onChange={(e) => setSmartScheduling(e.target.checked)}\n                    color=\"primary\"\n                  />\n                }\n                label=\"AI Smart Scheduling\"\n                sx={{ mb: 2 }}\n              />\n\n              <FormControlLabel\n                control={\n                  <Switch\n                    checked={adaptiveScan}\n                    onChange={(e) => setAdaptiveScan(e.target.checked)}\n                    color=\"primary\"\n                  />\n                }\n                label=\"Adaptive Scan Intensity\"\n                sx={{ mb: 3 }}\n              />\n\n              <Box mb={3}>\n                <Typography variant=\"body2\" gutterBottom>\n                  Scan Interval: {scanInterval} minutes\n                </Typography>\n                <Slider\n                  value={scanInterval}\n                  onChange={(e, value) => setScanInterval(value)}\n                  min={5}\n                  max={120}\n                  step={5}\n                  marks={[\n                    { value: 5, label: '5m' },\n                    { value: 30, label: '30m' },\n                    { value: 60, label: '1h' },\n                    { value: 120, label: '2h' },\n                  ]}\n                  color=\"primary\"\n                />\n              </Box>\n\n              <Box mb={3}>\n                <Typography variant=\"body2\" gutterBottom>\n                  Scan Intensity: {getIntensityLabel(scanIntensity)}\n                </Typography>\n                <Slider\n                  value={scanIntensity}\n                  onChange={(e, value) => setScanIntensity(value)}\n                  min={1}\n                  max={3}\n                  step={1}\n                  marks={[\n                    { value: 1, label: 'Light' },\n                    { value: 2, label: 'Balanced' },\n                    { value: 3, label: 'Intensive' },\n                  ]}\n                  color=\"primary\"\n                />\n              </Box>\n            </Grid>\n\n            <Grid item xs={12} md={6}>\n              <Typography variant=\"h6\" gutterBottom fontWeight=\"600\">\n                AI Recommendations\n              </Typography>\n              \n              <List>\n                {aiRecommendations.map((rec, index) => (\n                  <ListItem\n                    key={index}\n                    sx={{\n                      background: (theme) => theme.palette.mode === 'dark'\n                        ? 'rgba(255, 255, 255, 0.05)'\n                        : 'rgba(0, 0, 0, 0.02)',\n                      borderRadius: 2,\n                      mb: 1,\n                    }}\n                  >\n                    <ListItemIcon>\n                      <AIIcon color=\"secondary\" />\n                    </ListItemIcon>\n                    <ListItemText\n                      primary={rec.message}\n                      secondary={\n                        <Chip\n                          label={`${rec.confidence}% confidence`}\n                          size=\"small\"\n                          color={rec.confidence > 90 ? 'success' : 'warning'}\n                          variant=\"outlined\"\n                        />\n                      }\n                    />\n                  </ListItem>\n                ))}\n              </List>\n            </Grid>\n          </Grid>\n        </CardContent>\n      </Card>\n\n      <Grid container spacing={3}>\n        {/* Scan Queue */}\n        <Grid item xs={12} md={6}>\n          <Card\n            sx={{\n              background: (theme) => theme.palette.mode === 'dark'\n                ? 'linear-gradient(135deg, rgba(30, 30, 30, 0.95) 0%, rgba(50, 50, 50, 0.95) 100%)'\n                : 'linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%)',\n              backdropFilter: 'blur(20px)',\n              border: (theme) => `1px solid ${theme.palette.divider}`,\n            }}\n          >\n            <CardContent>\n              <Box display=\"flex\" alignItems=\"center\" gap={2} mb={3}>\n                <Avatar\n                  sx={{\n                    background: 'linear-gradient(135deg, #ff9800 0%, #ffc107 100%)',\n                    width: 48,\n                    height: 48,\n                  }}\n                >\n                  <ScheduleIcon />\n                </Avatar>\n                <Typography variant=\"h6\" fontWeight=\"bold\">\n                  Scan Queue ({scanQueue.length})\n                </Typography>\n              </Box>\n\n              <List>\n                {scanQueue.map((item, index) => (\n                  <ListItem\n                    key={index}\n                    sx={{\n                      background: (theme) => theme.palette.mode === 'dark'\n                        ? 'rgba(255, 255, 255, 0.05)'\n                        : 'rgba(0, 0, 0, 0.02)',\n                      borderRadius: 2,\n                      mb: 1,\n                    }}\n                  >\n                    <ListItemIcon>\n                      <SecurityIcon color=\"primary\" />\n                    </ListItemIcon>\n                    <ListItemText\n                      primary={\n                        <Box display=\"flex\" alignItems=\"center\" gap={2}>\n                          <Typography variant=\"body2\" fontWeight=\"600\">\n                            {item.target}\n                          </Typography>\n                          <Chip\n                            label={item.type}\n                            size=\"small\"\n                            variant=\"outlined\"\n                          />\n                          <Chip\n                            label={item.priority}\n                            size=\"small\"\n                            color={getPriorityColor(item.priority)}\n                          />\n                        </Box>\n                      }\n                      secondary={`Status: ${item.status}`}\n                    />\n                  </ListItem>\n                ))}\n              </List>\n            </CardContent>\n          </Card>\n        </Grid>\n\n        {/* Recent Scans */}\n        <Grid item xs={12} md={6}>\n          <Card\n            sx={{\n              background: (theme) => theme.palette.mode === 'dark'\n                ? 'linear-gradient(135deg, rgba(30, 30, 30, 0.95) 0%, rgba(50, 50, 50, 0.95) 100%)'\n                : 'linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%)',\n              backdropFilter: 'blur(20px)',\n              border: (theme) => `1px solid ${theme.palette.divider}`,\n            }}\n          >\n            <CardContent>\n              <Box display=\"flex\" alignItems=\"center\" gap={2} mb={3}>\n                <Avatar\n                  sx={{\n                    background: 'linear-gradient(135deg, #4caf50 0%, #8bc34a 100%)',\n                    width: 48,\n                    height: 48,\n                  }}\n                >\n                  <SpeedIcon />\n                </Avatar>\n                <Typography variant=\"h6\" fontWeight=\"bold\">\n                  Recent Auto-Scans\n                </Typography>\n              </Box>\n\n              <List>\n                {scanHistory.map((scan, index) => (\n                  <ListItem\n                    key={index}\n                    sx={{\n                      background: (theme) => theme.palette.mode === 'dark'\n                        ? 'rgba(255, 255, 255, 0.05)'\n                        : 'rgba(0, 0, 0, 0.02)',\n                      borderRadius: 2,\n                      mb: 1,\n                    }}\n                  >\n                    <ListItemText\n                      primary={\n                        <Box display=\"flex\" alignItems=\"center\" justifyContent=\"space-between\">\n                          <Typography variant=\"body2\" fontWeight=\"600\">\n                            {scan.type}\n                          </Typography>\n                          <Typography variant=\"caption\" color=\"text.secondary\">\n                            {scan.timestamp.toLocaleTimeString()}\n                          </Typography>\n                        </Box>\n                      }\n                      secondary={\n                        <Box display=\"flex\" alignItems=\"center\" gap={2} mt={1}>\n                          <Typography variant=\"caption\">\n                            {scan.targets} targets\n                          </Typography>\n                          <Typography variant=\"caption\" color={scan.threats > 0 ? 'error.main' : 'success.main'}>\n                            {scan.threats} threats\n                          </Typography>\n                          <Typography variant=\"caption\">\n                            {scan.duration}\n                          </Typography>\n                        </Box>\n                      }\n                    />\n                  </ListItem>\n                ))}\n              </List>\n            </CardContent>\n          </Card>\n        </Grid>\n      </Grid>\n    </Box>\n  );\n});\n\nAutoScanSystem.displayName = 'AutoScanSystem';\n\nexport default AutoScanSystem;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,gBAAgB,EAChBC,IAAI,EACJC,IAAI,EACJC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,YAAY,EACZC,MAAM,EACNC,UAAU,EACVC,OAAO,EACPC,cAAc,EACdC,KAAK,EACLC,MAAM,EACNC,SAAS,EACTC,MAAM,EACNC,OAAO,QACF,eAAe;AACtB,SACEC,QAAQ,IAAIC,QAAQ,EACpBC,QAAQ,IAAIC,YAAY,EACxBC,KAAK,IAAIC,SAAS,EAClBC,QAAQ,IAAIC,YAAY,EACxBC,aAAa,IAAIC,iBAAiB,EAClCC,QAAQ,IAAIC,YAAY,EACxBC,SAAS,IAAIC,QAAQ,EACrBC,KAAK,IAAIC,SAAS,EAClBC,IAAI,IAAIC,QAAQ,EAChBC,OAAO,IAAIC,WAAW,EACtBC,UAAU,IAAIC,MAAM,EACpBC,MAAM,IAAIC,UAAU,QACf,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7B,MAAMC,cAAc,gBAAAC,EAAA,cAAGnD,KAAK,CAACoD,IAAI,CAAAC,EAAA,GAAAF,EAAA,CAAC,MAAM;EAAAA,EAAA;EACtC,MAAM,CAACG,eAAe,EAAEC,kBAAkB,CAAC,GAAGtD,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACuD,YAAY,EAAEC,eAAe,CAAC,GAAGxD,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EACtD,MAAM,CAACyD,aAAa,EAAEC,gBAAgB,CAAC,GAAG1D,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;EACvD,MAAM,CAAC2D,eAAe,EAAEC,kBAAkB,CAAC,GAAG5D,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAAC6D,YAAY,EAAEC,eAAe,CAAC,GAAG9D,QAAQ,CAAC,IAAI,CAAC;EAEtD,MAAM,CAAC+D,WAAW,EAAEC,cAAc,CAAC,GAAGhE,QAAQ,CAAC;IAC7CiE,SAAS,EAAE,KAAK;IAChBC,QAAQ,EAAE,CAAC;IACXC,aAAa,EAAE,EAAE;IACjBC,YAAY,EAAE,CAAC;IACfC,aAAa,EAAE;EACjB,CAAC,CAAC;EAEF,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGvE,QAAQ,CAAC,CACzC;IAAEwE,IAAI,EAAE,KAAK;IAAEC,MAAM,EAAE,6BAA6B;IAAEC,QAAQ,EAAE,MAAM;IAAEC,MAAM,EAAE;EAAU,CAAC,EAC3F;IAAEH,IAAI,EAAE,MAAM;IAAEC,MAAM,EAAE,cAAc;IAAEC,QAAQ,EAAE,QAAQ;IAAEC,MAAM,EAAE;EAAU,CAAC,EAC/E;IAAEH,IAAI,EAAE,KAAK;IAAEC,MAAM,EAAE,8BAA8B;IAAEC,QAAQ,EAAE,MAAM;IAAEC,MAAM,EAAE;EAAU,CAAC,EAC5F;IAAEH,IAAI,EAAE,MAAM;IAAEC,MAAM,EAAE,cAAc;IAAEC,QAAQ,EAAE,KAAK;IAAEC,MAAM,EAAE;EAAU,CAAC,CAC7E,CAAC;EAEF,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG7E,QAAQ,CAAC,CAC7C;IACE8E,SAAS,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC;IAChDR,IAAI,EAAE,WAAW;IACjBS,OAAO,EAAE,EAAE;IACXC,OAAO,EAAE,CAAC;IACVC,QAAQ,EAAE,QAAQ;IAClBR,MAAM,EAAE;EACV,CAAC,EACD;IACEG,SAAS,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;IACpDR,IAAI,EAAE,gBAAgB;IACtBS,OAAO,EAAE,CAAC;IACVC,OAAO,EAAE,CAAC;IACVC,QAAQ,EAAE,QAAQ;IAClBR,MAAM,EAAE;EACV,CAAC,EACD;IACEG,SAAS,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;IACpDR,IAAI,EAAE,eAAe;IACrBS,OAAO,EAAE,CAAC;IACVC,OAAO,EAAE,CAAC;IACVC,QAAQ,EAAE,QAAQ;IAClBR,MAAM,EAAE;EACV,CAAC,CACF,CAAC;EAEF,MAAM,CAACS,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGrF,QAAQ,CAAC,CACzD;IACEwE,IAAI,EAAE,UAAU;IAChBc,OAAO,EAAE,kDAAkD;IAC3DC,UAAU,EAAE;EACd,CAAC,EACD;IACEf,IAAI,EAAE,UAAU;IAChBc,OAAO,EAAE,qDAAqD;IAC9DC,UAAU,EAAE;EACd,CAAC,EACD;IACEf,IAAI,EAAE,cAAc;IACpBc,OAAO,EAAE,2CAA2C;IACpDC,UAAU,EAAE;EACd,CAAC,CACF,CAAC;;EAEF;EACAtF,SAAS,CAAC,MAAM;IACd,IAAIuF,QAAQ;IACZ,IAAIzB,WAAW,CAACE,SAAS,EAAE;MACzBuB,QAAQ,GAAGC,WAAW,CAAC,MAAM;QAC3BzB,cAAc,CAAC0B,IAAI,IAAI;UACrB,MAAMC,WAAW,GAAGC,IAAI,CAACC,GAAG,CAAC,GAAG,EAAEH,IAAI,CAACxB,QAAQ,GAAG0B,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC;UACrE,IAAIH,WAAW,IAAI,GAAG,EAAE;YACtB,OAAO;cACL,GAAGD,IAAI;cACPxB,QAAQ,EAAE,GAAG;cACbD,SAAS,EAAE,KAAK;cAChBG,YAAY,EAAEsB,IAAI,CAACtB,YAAY,GAAGwB,IAAI,CAACG,KAAK,CAACH,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,CAAC;YAChE,CAAC;UACH;UACA,OAAO;YACL,GAAGJ,IAAI;YACPxB,QAAQ,EAAEyB,WAAW;YACrBtB,aAAa,EAAEuB,IAAI,CAACI,GAAG,CAAC,CAAC,EAAEN,IAAI,CAACrB,aAAa,GAAG,CAAC;UACnD,CAAC;QACH,CAAC,CAAC;MACJ,CAAC,EAAE,IAAI,CAAC;IACV;IACA,OAAO,MAAM4B,aAAa,CAACT,QAAQ,CAAC;EACtC,CAAC,EAAE,CAACzB,WAAW,CAACE,SAAS,CAAC,CAAC;EAE3B,MAAMiC,aAAa,GAAGA,CAAA,KAAM;IAAA,IAAAC,WAAA;IAC1BnC,cAAc,CAAC;MACbC,SAAS,EAAE,IAAI;MACfC,QAAQ,EAAE,CAAC;MACXC,aAAa,EAAE,EAAAgC,WAAA,GAAA7B,SAAS,CAAC,CAAC,CAAC,cAAA6B,WAAA,uBAAZA,WAAA,CAAc1B,MAAM,KAAI,kBAAkB;MACzDL,YAAY,EAAE,CAAC;MACfC,aAAa,EAAE,GAAG,CAAE;IACtB,CAAC,CAAC;EACJ,CAAC;EAED,MAAM+B,aAAa,GAAGA,CAAA,KAAM;IAC1BpC,cAAc,CAAC0B,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAEzB,SAAS,EAAE;IAAM,CAAC,CAAC,CAAC;EACzD,CAAC;EAED,MAAMoC,YAAY,GAAGA,CAAA,KAAM;IACzBrC,cAAc,CAAC;MACbC,SAAS,EAAE,KAAK;MAChBC,QAAQ,EAAE,CAAC;MACXC,aAAa,EAAE,EAAE;MACjBC,YAAY,EAAE,CAAC;MACfC,aAAa,EAAE;IACjB,CAAC,CAAC;EACJ,CAAC;EAED,MAAMiC,iBAAiB,GAAIC,KAAK,IAAK;IACnC,QAAQA,KAAK;MACX,KAAK,CAAC;QAAE,OAAO,OAAO;MACtB,KAAK,CAAC;QAAE,OAAO,UAAU;MACzB,KAAK,CAAC;QAAE,OAAO,WAAW;MAC1B;QAAS,OAAO,UAAU;IAC5B;EACF,CAAC;EAED,MAAMC,gBAAgB,GAAI9B,QAAQ,IAAK;IACrC,QAAQA,QAAQ;MACd,KAAK,MAAM;QAAE,OAAO,OAAO;MAC3B,KAAK,QAAQ;QAAE,OAAO,SAAS;MAC/B,KAAK,KAAK;QAAE,OAAO,SAAS;MAC5B;QAAS,OAAO,SAAS;IAC3B;EACF,CAAC;EAED,oBACE1B,OAAA,CAAC9C,GAAG;IAACuG,EAAE,EAAE;MAAEC,CAAC,EAAE;IAAE,CAAE;IAAAC,QAAA,gBAEhB3D,OAAA,CAAC7C,IAAI;MACHsG,EAAE,EAAE;QACFG,EAAE,EAAE,CAAC;QACLC,UAAU,EAAE,oFAAoF;QAChGC,MAAM,EAAE,oCAAoC;QAC5CC,QAAQ,EAAE,UAAU;QACpBC,QAAQ,EAAE,QAAQ;QAClB,WAAW,EAAE;UACXC,OAAO,EAAE,IAAI;UACbF,QAAQ,EAAE,UAAU;UACpBG,GAAG,EAAE,CAAC;UACNC,IAAI,EAAE,CAAC;UACPC,KAAK,EAAE,CAAC;UACRC,MAAM,EAAE,KAAK;UACbR,UAAU,EAAE;QACd;MACF,CAAE;MAAAF,QAAA,eAEF3D,OAAA,CAAC5C,WAAW;QAAAuG,QAAA,gBACV3D,OAAA,CAAC9C,GAAG;UAACoH,OAAO,EAAC,MAAM;UAACC,UAAU,EAAC,QAAQ;UAACC,GAAG,EAAE,CAAE;UAACZ,EAAE,EAAE,CAAE;UAAAD,QAAA,gBACpD3D,OAAA,CAAClC,MAAM;YACL2F,EAAE,EAAE;cACFI,UAAU,EAAE,mDAAmD;cAC/DY,KAAK,EAAE,EAAE;cACTJ,MAAM,EAAE;YACV,CAAE;YAAAV,QAAA,eAEF3D,OAAA,CAACxB,QAAQ;cAACiF,EAAE,EAAE;gBAAEiB,QAAQ,EAAE;cAAG;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CAAC,eACT9E,OAAA,CAAC9C,GAAG;YAAC6H,IAAI,EAAE,CAAE;YAAApB,QAAA,gBACX3D,OAAA,CAAC3C,UAAU;cAAC2H,OAAO,EAAC,IAAI;cAACC,UAAU,EAAC,MAAM;cAACC,YAAY;cAAAvB,QAAA,EAAC;YAExD;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb9E,OAAA,CAAC3C,UAAU;cAAC2H,OAAO,EAAC,OAAO;cAACG,KAAK,EAAC,gBAAgB;cAAAxB,QAAA,EAAC;YAEnD;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACN9E,OAAA,CAAC9C,GAAG;YAACoH,OAAO,EAAC,MAAM;YAACE,GAAG,EAAE,CAAE;YAAAb,QAAA,gBACzB3D,OAAA,CAAChC,OAAO;cAACoH,KAAK,EAAC,iBAAiB;cAAAzB,QAAA,eAC9B3D,OAAA,CAACjC,UAAU;gBACTsH,OAAO,EAAEnC,aAAc;gBACvBoC,QAAQ,EAAEvE,WAAW,CAACE,SAAU;gBAChCwC,EAAE,EAAE;kBACFI,UAAU,EAAE,mDAAmD;kBAC/DsB,KAAK,EAAE,OAAO;kBACd,SAAS,EAAE;oBACTtB,UAAU,EAAE;kBACd;gBACF,CAAE;gBAAAF,QAAA,eAEF3D,OAAA,CAACZ,QAAQ;kBAAAuF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACV9E,OAAA,CAAChC,OAAO;cAACoH,KAAK,EAAC,OAAO;cAAAzB,QAAA,eACpB3D,OAAA,CAACjC,UAAU;gBACTsH,OAAO,EAAEjC,aAAc;gBACvBkC,QAAQ,EAAE,CAACvE,WAAW,CAACE,SAAU;gBACjCwC,EAAE,EAAE;kBACFI,UAAU,EAAE,mDAAmD;kBAC/DsB,KAAK,EAAE,OAAO;kBACd,SAAS,EAAE;oBACTtB,UAAU,EAAE;kBACd;gBACF,CAAE;gBAAAF,QAAA,eAEF3D,OAAA,CAACV,SAAS;kBAAAqF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACV9E,OAAA,CAAChC,OAAO;cAACoH,KAAK,EAAC,MAAM;cAAAzB,QAAA,eACnB3D,OAAA,CAACjC,UAAU;gBACTsH,OAAO,EAAEhC,YAAa;gBACtBI,EAAE,EAAE;kBACFI,UAAU,EAAE,mDAAmD;kBAC/DsB,KAAK,EAAE,OAAO;kBACd,SAAS,EAAE;oBACTtB,UAAU,EAAE;kBACd;gBACF,CAAE;gBAAAF,QAAA,eAEF3D,OAAA,CAACR,QAAQ;kBAAAmF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAGL,CAAC/D,WAAW,CAACE,SAAS,IAAIF,WAAW,CAACG,QAAQ,GAAG,CAAC,kBACjDlB,OAAA,CAAC9B,KAAK;UACJqH,QAAQ,EAAExE,WAAW,CAACE,SAAS,GAAG,MAAM,GAAG,SAAU;UACrDwC,EAAE,EAAE;YAAEG,EAAE,EAAE;UAAE,CAAE;UACd4B,IAAI,EAAEzE,WAAW,CAACE,SAAS,gBAAGjB,OAAA,CAACN,WAAW;YAAAiF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAAG9E,OAAA,CAACF,UAAU;YAAA6E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAnB,QAAA,eAE/D3D,OAAA,CAAC9C,GAAG;YAAAyG,QAAA,gBACF3D,OAAA,CAAC3C,UAAU;cAAC2H,OAAO,EAAC,WAAW;cAACC,UAAU,EAAC,KAAK;cAAAtB,QAAA,EAC7C5C,WAAW,CAACE,SAAS,GAAG,sBAAsB,GAAG;YAAgB;cAAA0D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxD,CAAC,eACb9E,OAAA,CAAC3C,UAAU;cAAC2H,OAAO,EAAC,OAAO;cAACvB,EAAE,EAAE;gBAAEG,EAAE,EAAE;cAAE,CAAE;cAAAD,QAAA,GAAC,UACjC,EAAC5C,WAAW,CAACI,aAAa;YAAA;cAAAwD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB,CAAC,eACb9E,OAAA,CAAC/B,cAAc;cACb+G,OAAO,EAAC,aAAa;cACrBzB,KAAK,EAAExC,WAAW,CAACG,QAAS;cAC5BuC,EAAE,EAAE;gBAAEG,EAAE,EAAE,CAAC;gBAAES,MAAM,EAAE,CAAC;gBAAEoB,YAAY,EAAE;cAAE;YAAE;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC,eACF9E,OAAA,CAAC9C,GAAG;cAACoH,OAAO,EAAC,MAAM;cAACoB,cAAc,EAAC,eAAe;cAACnB,UAAU,EAAC,QAAQ;cAAAZ,QAAA,gBACpE3D,OAAA,CAAC3C,UAAU;gBAAC2H,OAAO,EAAC,SAAS;gBAAArB,QAAA,GAAC,YAClB,EAACf,IAAI,CAAC+C,KAAK,CAAC5E,WAAW,CAACG,QAAQ,CAAC,EAAC,GAC9C;cAAA;gBAAAyD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACb9E,OAAA,CAAC3C,UAAU;gBAAC2H,OAAO,EAAC,SAAS;gBAAArB,QAAA,GAAC,iBACb,EAAC5C,WAAW,CAACK,YAAY;cAAA;gBAAAuD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CAAC,EACZ/D,WAAW,CAACE,SAAS,iBACpBjB,OAAA,CAAC3C,UAAU;gBAAC2H,OAAO,EAAC,SAAS;gBAAArB,QAAA,GAAC,OACvB,EAACf,IAAI,CAACG,KAAK,CAAChC,WAAW,CAACM,aAAa,GAAG,EAAE,CAAC,EAAC,IAAE,EAACN,WAAW,CAACM,aAAa,GAAG,EAAE,EAAC,GACrF;cAAA;gBAAAsD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CACb;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CACR,eAGD9E,OAAA,CAACxC,IAAI;UAACoI,SAAS;UAACC,OAAO,EAAE,CAAE;UAAAlC,QAAA,gBACzB3D,OAAA,CAACxC,IAAI;YAACsI,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAArC,QAAA,gBACvB3D,OAAA,CAAC3C,UAAU;cAAC2H,OAAO,EAAC,IAAI;cAACE,YAAY;cAACD,UAAU,EAAC,KAAK;cAAAtB,QAAA,EAAC;YAEvD;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAEb9E,OAAA,CAACzC,gBAAgB;cACf0I,OAAO,eACLjG,OAAA,CAAC1C,MAAM;gBACL4I,OAAO,EAAE7F,eAAgB;gBACzB8F,QAAQ,EAAGC,CAAC,IAAK9F,kBAAkB,CAAC8F,CAAC,CAAC3E,MAAM,CAACyE,OAAO,CAAE;gBACtDf,KAAK,EAAC;cAAS;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChB,CACF;cACDuB,KAAK,EAAC,kBAAkB;cACxB5C,EAAE,EAAE;gBAAEG,EAAE,EAAE;cAAE;YAAE;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC,eAEF9E,OAAA,CAACzC,gBAAgB;cACf0I,OAAO,eACLjG,OAAA,CAAC1C,MAAM;gBACL4I,OAAO,EAAEvF,eAAgB;gBACzBwF,QAAQ,EAAGC,CAAC,IAAKxF,kBAAkB,CAACwF,CAAC,CAAC3E,MAAM,CAACyE,OAAO,CAAE;gBACtDf,KAAK,EAAC;cAAS;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChB,CACF;cACDuB,KAAK,EAAC,qBAAqB;cAC3B5C,EAAE,EAAE;gBAAEG,EAAE,EAAE;cAAE;YAAE;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC,eAEF9E,OAAA,CAACzC,gBAAgB;cACf0I,OAAO,eACLjG,OAAA,CAAC1C,MAAM;gBACL4I,OAAO,EAAErF,YAAa;gBACtBsF,QAAQ,EAAGC,CAAC,IAAKtF,eAAe,CAACsF,CAAC,CAAC3E,MAAM,CAACyE,OAAO,CAAE;gBACnDf,KAAK,EAAC;cAAS;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChB,CACF;cACDuB,KAAK,EAAC,yBAAyB;cAC/B5C,EAAE,EAAE;gBAAEG,EAAE,EAAE;cAAE;YAAE;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC,eAEF9E,OAAA,CAAC9C,GAAG;cAAC0G,EAAE,EAAE,CAAE;cAAAD,QAAA,gBACT3D,OAAA,CAAC3C,UAAU;gBAAC2H,OAAO,EAAC,OAAO;gBAACE,YAAY;gBAAAvB,QAAA,GAAC,iBACxB,EAACpD,YAAY,EAAC,UAC/B;cAAA;gBAAAoE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACb9E,OAAA,CAAC7B,MAAM;gBACLoF,KAAK,EAAEhD,YAAa;gBACpB4F,QAAQ,EAAEA,CAACC,CAAC,EAAE7C,KAAK,KAAK/C,eAAe,CAAC+C,KAAK,CAAE;gBAC/CV,GAAG,EAAE,CAAE;gBACPG,GAAG,EAAE,GAAI;gBACTsD,IAAI,EAAE,CAAE;gBACRC,KAAK,EAAE,CACL;kBAAEhD,KAAK,EAAE,CAAC;kBAAE8C,KAAK,EAAE;gBAAK,CAAC,EACzB;kBAAE9C,KAAK,EAAE,EAAE;kBAAE8C,KAAK,EAAE;gBAAM,CAAC,EAC3B;kBAAE9C,KAAK,EAAE,EAAE;kBAAE8C,KAAK,EAAE;gBAAK,CAAC,EAC1B;kBAAE9C,KAAK,EAAE,GAAG;kBAAE8C,KAAK,EAAE;gBAAK,CAAC,CAC3B;gBACFlB,KAAK,EAAC;cAAS;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAEN9E,OAAA,CAAC9C,GAAG;cAAC0G,EAAE,EAAE,CAAE;cAAAD,QAAA,gBACT3D,OAAA,CAAC3C,UAAU;gBAAC2H,OAAO,EAAC,OAAO;gBAACE,YAAY;gBAAAvB,QAAA,GAAC,kBACvB,EAACL,iBAAiB,CAAC7C,aAAa,CAAC;cAAA;gBAAAkE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvC,CAAC,eACb9E,OAAA,CAAC7B,MAAM;gBACLoF,KAAK,EAAE9C,aAAc;gBACrB0F,QAAQ,EAAEA,CAACC,CAAC,EAAE7C,KAAK,KAAK7C,gBAAgB,CAAC6C,KAAK,CAAE;gBAChDV,GAAG,EAAE,CAAE;gBACPG,GAAG,EAAE,CAAE;gBACPsD,IAAI,EAAE,CAAE;gBACRC,KAAK,EAAE,CACL;kBAAEhD,KAAK,EAAE,CAAC;kBAAE8C,KAAK,EAAE;gBAAQ,CAAC,EAC5B;kBAAE9C,KAAK,EAAE,CAAC;kBAAE8C,KAAK,EAAE;gBAAW,CAAC,EAC/B;kBAAE9C,KAAK,EAAE,CAAC;kBAAE8C,KAAK,EAAE;gBAAY,CAAC,CAChC;gBACFlB,KAAK,EAAC;cAAS;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAEP9E,OAAA,CAACxC,IAAI;YAACsI,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAArC,QAAA,gBACvB3D,OAAA,CAAC3C,UAAU;cAAC2H,OAAO,EAAC,IAAI;cAACE,YAAY;cAACD,UAAU,EAAC,KAAK;cAAAtB,QAAA,EAAC;YAEvD;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAEb9E,OAAA,CAACtC,IAAI;cAAAiG,QAAA,EACFvB,iBAAiB,CAACoE,GAAG,CAAC,CAACC,GAAG,EAAEC,KAAK,kBAChC1G,OAAA,CAACrC,QAAQ;gBAEP8F,EAAE,EAAE;kBACFI,UAAU,EAAG8C,KAAK,IAAKA,KAAK,CAACC,OAAO,CAACC,IAAI,KAAK,MAAM,GAChD,2BAA2B,GAC3B,qBAAqB;kBACzBpB,YAAY,EAAE,CAAC;kBACf7B,EAAE,EAAE;gBACN,CAAE;gBAAAD,QAAA,gBAEF3D,OAAA,CAACpC,YAAY;kBAAA+F,QAAA,eACX3D,OAAA,CAACJ,MAAM;oBAACuF,KAAK,EAAC;kBAAW;oBAAAR,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChB,CAAC,eACf9E,OAAA,CAACnC,YAAY;kBACXiJ,OAAO,EAAEL,GAAG,CAACnE,OAAQ;kBACrByE,SAAS,eACP/G,OAAA,CAACvC,IAAI;oBACH4I,KAAK,EAAE,GAAGI,GAAG,CAAClE,UAAU,cAAe;oBACvCyE,IAAI,EAAC,OAAO;oBACZ7B,KAAK,EAAEsB,GAAG,CAAClE,UAAU,GAAG,EAAE,GAAG,SAAS,GAAG,SAAU;oBACnDyC,OAAO,EAAC;kBAAU;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnB;gBACF;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC;cAAA,GAtBG4B,KAAK;gBAAA/B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAuBF,CACX;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAEP9E,OAAA,CAACxC,IAAI;MAACoI,SAAS;MAACC,OAAO,EAAE,CAAE;MAAAlC,QAAA,gBAEzB3D,OAAA,CAACxC,IAAI;QAACsI,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAArC,QAAA,eACvB3D,OAAA,CAAC7C,IAAI;UACHsG,EAAE,EAAE;YACFI,UAAU,EAAG8C,KAAK,IAAKA,KAAK,CAACC,OAAO,CAACC,IAAI,KAAK,MAAM,GAChD,iFAAiF,GACjF,uFAAuF;YAC3FI,cAAc,EAAE,YAAY;YAC5BnD,MAAM,EAAG6C,KAAK,IAAK,aAAaA,KAAK,CAACC,OAAO,CAACM,OAAO;UACvD,CAAE;UAAAvD,QAAA,eAEF3D,OAAA,CAAC5C,WAAW;YAAAuG,QAAA,gBACV3D,OAAA,CAAC9C,GAAG;cAACoH,OAAO,EAAC,MAAM;cAACC,UAAU,EAAC,QAAQ;cAACC,GAAG,EAAE,CAAE;cAACZ,EAAE,EAAE,CAAE;cAAAD,QAAA,gBACpD3D,OAAA,CAAClC,MAAM;gBACL2F,EAAE,EAAE;kBACFI,UAAU,EAAE,mDAAmD;kBAC/DY,KAAK,EAAE,EAAE;kBACTJ,MAAM,EAAE;gBACV,CAAE;gBAAAV,QAAA,eAEF3D,OAAA,CAACtB,YAAY;kBAAAiG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACT9E,OAAA,CAAC3C,UAAU;gBAAC2H,OAAO,EAAC,IAAI;gBAACC,UAAU,EAAC,MAAM;gBAAAtB,QAAA,GAAC,cAC7B,EAACrC,SAAS,CAAC6F,MAAM,EAAC,GAChC;cAAA;gBAAAxC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAEN9E,OAAA,CAACtC,IAAI;cAAAiG,QAAA,EACFrC,SAAS,CAACkF,GAAG,CAAC,CAACV,IAAI,EAAEY,KAAK,kBACzB1G,OAAA,CAACrC,QAAQ;gBAEP8F,EAAE,EAAE;kBACFI,UAAU,EAAG8C,KAAK,IAAKA,KAAK,CAACC,OAAO,CAACC,IAAI,KAAK,MAAM,GAChD,2BAA2B,GAC3B,qBAAqB;kBACzBpB,YAAY,EAAE,CAAC;kBACf7B,EAAE,EAAE;gBACN,CAAE;gBAAAD,QAAA,gBAEF3D,OAAA,CAACpC,YAAY;kBAAA+F,QAAA,eACX3D,OAAA,CAAClB,YAAY;oBAACqG,KAAK,EAAC;kBAAS;oBAAAR,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpB,CAAC,eACf9E,OAAA,CAACnC,YAAY;kBACXiJ,OAAO,eACL9G,OAAA,CAAC9C,GAAG;oBAACoH,OAAO,EAAC,MAAM;oBAACC,UAAU,EAAC,QAAQ;oBAACC,GAAG,EAAE,CAAE;oBAAAb,QAAA,gBAC7C3D,OAAA,CAAC3C,UAAU;sBAAC2H,OAAO,EAAC,OAAO;sBAACC,UAAU,EAAC,KAAK;sBAAAtB,QAAA,EACzCmC,IAAI,CAACrE;oBAAM;sBAAAkD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF,CAAC,eACb9E,OAAA,CAACvC,IAAI;sBACH4I,KAAK,EAAEP,IAAI,CAACtE,IAAK;sBACjBwF,IAAI,EAAC,OAAO;sBACZhC,OAAO,EAAC;oBAAU;sBAAAL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnB,CAAC,eACF9E,OAAA,CAACvC,IAAI;sBACH4I,KAAK,EAAEP,IAAI,CAACpE,QAAS;sBACrBsF,IAAI,EAAC,OAAO;sBACZ7B,KAAK,EAAE3B,gBAAgB,CAACsC,IAAI,CAACpE,QAAQ;oBAAE;sBAAAiD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CACN;kBACDiC,SAAS,EAAE,WAAWjB,IAAI,CAACnE,MAAM;gBAAG;kBAAAgD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrC,CAAC;cAAA,GA/BG4B,KAAK;gBAAA/B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAgCF,CACX;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGP9E,OAAA,CAACxC,IAAI;QAACsI,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAArC,QAAA,eACvB3D,OAAA,CAAC7C,IAAI;UACHsG,EAAE,EAAE;YACFI,UAAU,EAAG8C,KAAK,IAAKA,KAAK,CAACC,OAAO,CAACC,IAAI,KAAK,MAAM,GAChD,iFAAiF,GACjF,uFAAuF;YAC3FI,cAAc,EAAE,YAAY;YAC5BnD,MAAM,EAAG6C,KAAK,IAAK,aAAaA,KAAK,CAACC,OAAO,CAACM,OAAO;UACvD,CAAE;UAAAvD,QAAA,eAEF3D,OAAA,CAAC5C,WAAW;YAAAuG,QAAA,gBACV3D,OAAA,CAAC9C,GAAG;cAACoH,OAAO,EAAC,MAAM;cAACC,UAAU,EAAC,QAAQ;cAACC,GAAG,EAAE,CAAE;cAACZ,EAAE,EAAE,CAAE;cAAAD,QAAA,gBACpD3D,OAAA,CAAClC,MAAM;gBACL2F,EAAE,EAAE;kBACFI,UAAU,EAAE,mDAAmD;kBAC/DY,KAAK,EAAE,EAAE;kBACTJ,MAAM,EAAE;gBACV,CAAE;gBAAAV,QAAA,eAEF3D,OAAA,CAACpB,SAAS;kBAAA+F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP,CAAC,eACT9E,OAAA,CAAC3C,UAAU;gBAAC2H,OAAO,EAAC,IAAI;gBAACC,UAAU,EAAC,MAAM;gBAAAtB,QAAA,EAAC;cAE3C;gBAAAgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAEN9E,OAAA,CAACtC,IAAI;cAAAiG,QAAA,EACF/B,WAAW,CAAC4E,GAAG,CAAC,CAACY,IAAI,EAAEV,KAAK,kBAC3B1G,OAAA,CAACrC,QAAQ;gBAEP8F,EAAE,EAAE;kBACFI,UAAU,EAAG8C,KAAK,IAAKA,KAAK,CAACC,OAAO,CAACC,IAAI,KAAK,MAAM,GAChD,2BAA2B,GAC3B,qBAAqB;kBACzBpB,YAAY,EAAE,CAAC;kBACf7B,EAAE,EAAE;gBACN,CAAE;gBAAAD,QAAA,eAEF3D,OAAA,CAACnC,YAAY;kBACXiJ,OAAO,eACL9G,OAAA,CAAC9C,GAAG;oBAACoH,OAAO,EAAC,MAAM;oBAACC,UAAU,EAAC,QAAQ;oBAACmB,cAAc,EAAC,eAAe;oBAAA/B,QAAA,gBACpE3D,OAAA,CAAC3C,UAAU;sBAAC2H,OAAO,EAAC,OAAO;sBAACC,UAAU,EAAC,KAAK;sBAAAtB,QAAA,EACzCyD,IAAI,CAAC5F;oBAAI;sBAAAmD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACA,CAAC,eACb9E,OAAA,CAAC3C,UAAU;sBAAC2H,OAAO,EAAC,SAAS;sBAACG,KAAK,EAAC,gBAAgB;sBAAAxB,QAAA,EACjDyD,IAAI,CAACtF,SAAS,CAACuF,kBAAkB,CAAC;oBAAC;sBAAA1C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1B,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CACN;kBACDiC,SAAS,eACP/G,OAAA,CAAC9C,GAAG;oBAACoH,OAAO,EAAC,MAAM;oBAACC,UAAU,EAAC,QAAQ;oBAACC,GAAG,EAAE,CAAE;oBAAC8C,EAAE,EAAE,CAAE;oBAAA3D,QAAA,gBACpD3D,OAAA,CAAC3C,UAAU;sBAAC2H,OAAO,EAAC,SAAS;sBAAArB,QAAA,GAC1ByD,IAAI,CAACnF,OAAO,EAAC,UAChB;oBAAA;sBAAA0C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACb9E,OAAA,CAAC3C,UAAU;sBAAC2H,OAAO,EAAC,SAAS;sBAACG,KAAK,EAAEiC,IAAI,CAAClF,OAAO,GAAG,CAAC,GAAG,YAAY,GAAG,cAAe;sBAAAyB,QAAA,GACnFyD,IAAI,CAAClF,OAAO,EAAC,UAChB;oBAAA;sBAAAyC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACb9E,OAAA,CAAC3C,UAAU;sBAAC2H,OAAO,EAAC,SAAS;sBAAArB,QAAA,EAC1ByD,IAAI,CAACjF;oBAAQ;sBAAAwC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV;gBACN;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF;cAAC,GAjCG4B,KAAK;gBAAA/B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAkCF,CACX;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC,kCAAC;AAACyC,GAAA,GA5gBGtH,cAAc;AA8gBpBA,cAAc,CAACuH,WAAW,GAAG,gBAAgB;AAE7C,eAAevH,cAAc;AAAC,IAAAG,EAAA,EAAAmH,GAAA;AAAAE,YAAA,CAAArH,EAAA;AAAAqH,YAAA,CAAAF,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}